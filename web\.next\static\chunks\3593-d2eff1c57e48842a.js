"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3593],{1243:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("Triangle<PERSON>lert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1482:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},17649:(e,t,a)=>{a.d(t,{UC:()=>q,VY:()=>T,ZD:()=>I,ZL:()=>F,bL:()=>M,hE:()=>L,hJ:()=>V,rc:()=>E});var r=a(12115),n=a(46081),i=a(6101),o=a(15452),l=a(85185),s=a(99708),d=a(95155),c="AlertDialog",[u,y]=(0,n.A)(c,[o.Hs]),p=(0,o.Hs)(),h=e=>{let{__scopeAlertDialog:t,...a}=e,r=p(t);return(0,d.jsx)(o.bL,{...r,...a,modal:!0})};h.displayName=c,r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,n=p(a);return(0,d.jsx)(o.l9,{...n,...r,ref:t})}).displayName="AlertDialogTrigger";var f=e=>{let{__scopeAlertDialog:t,...a}=e,r=p(t);return(0,d.jsx)(o.ZL,{...r,...a})};f.displayName="AlertDialogPortal";var v=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,n=p(a);return(0,d.jsx)(o.hJ,{...n,...r,ref:t})});v.displayName="AlertDialogOverlay";var x="AlertDialogContent",[g,m]=u(x),A=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,children:n,...c}=e,u=p(a),y=r.useRef(null),h=(0,i.s)(t,y),f=r.useRef(null);return(0,d.jsx)(o.G$,{contentName:x,titleName:b,docsSlug:"alert-dialog",children:(0,d.jsx)(g,{scope:a,cancelRef:f,children:(0,d.jsxs)(o.UC,{role:"alertdialog",...u,...c,ref:h,onOpenAutoFocus:(0,l.m)(c.onOpenAutoFocus,e=>{var t;e.preventDefault(),null===(t=f.current)||void 0===t||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,d.jsx)(s.xV,{children:n}),(0,d.jsx)(R,{contentRef:y})]})})})});A.displayName=x;var b="AlertDialogTitle",k=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,n=p(a);return(0,d.jsx)(o.hE,{...n,...r,ref:t})});k.displayName=b;var w="AlertDialogDescription",j=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,n=p(a);return(0,d.jsx)(o.VY,{...n,...r,ref:t})});j.displayName=w;var D=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,n=p(a);return(0,d.jsx)(o.bm,{...n,...r,ref:t})});D.displayName="AlertDialogAction";var C="AlertDialogCancel",N=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,{cancelRef:n}=m(C,a),l=p(a),s=(0,i.s)(t,n);return(0,d.jsx)(o.bm,{...l,...r,ref:s})});N.displayName=C;var R=e=>{let{contentRef:t}=e,a="`".concat(x,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(x,"` by passing a `").concat(w,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(x,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return r.useEffect(()=>{var e;document.getElementById(null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby"))||console.warn(a)},[a,t]),null},M=h,F=f,V=v,q=A,E=D,I=N,L=k,T=j},35169:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40133:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},44020:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},47924:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},60704:(e,t,a)=>{a.d(t,{B8:()=>M,UC:()=>V,bL:()=>R,l9:()=>F});var r=a(12115),n=a(85185),i=a(46081),o=a(89196),l=a(28905),s=a(63655),d=a(94315),c=a(5845),u=a(61285),y=a(95155),p="Tabs",[h,f]=(0,i.A)(p,[o.RG]),v=(0,o.RG)(),[x,g]=h(p),m=r.forwardRef((e,t)=>{let{__scopeTabs:a,value:r,onValueChange:n,defaultValue:i,orientation:o="horizontal",dir:l,activationMode:p="automatic",...h}=e,f=(0,d.jH)(l),[v,g]=(0,c.i)({prop:r,onChange:n,defaultProp:i});return(0,y.jsx)(x,{scope:a,baseId:(0,u.B)(),value:v,onValueChange:g,orientation:o,dir:f,activationMode:p,children:(0,y.jsx)(s.sG.div,{dir:f,"data-orientation":o,...h,ref:t})})});m.displayName=p;var A="TabsList",b=r.forwardRef((e,t)=>{let{__scopeTabs:a,loop:r=!0,...n}=e,i=g(A,a),l=v(a);return(0,y.jsx)(o.bL,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:r,children:(0,y.jsx)(s.sG.div,{role:"tablist","aria-orientation":i.orientation,...n,ref:t})})});b.displayName=A;var k="TabsTrigger",w=r.forwardRef((e,t)=>{let{__scopeTabs:a,value:r,disabled:i=!1,...l}=e,d=g(k,a),c=v(a),u=C(d.baseId,r),p=N(d.baseId,r),h=r===d.value;return(0,y.jsx)(o.q7,{asChild:!0,...c,focusable:!i,active:h,children:(0,y.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":p,"data-state":h?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:u,...l,ref:t,onMouseDown:(0,n.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(r)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(r)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;h||i||!e||d.onValueChange(r)})})})});w.displayName=k;var j="TabsContent",D=r.forwardRef((e,t)=>{let{__scopeTabs:a,value:n,forceMount:i,children:o,...d}=e,c=g(j,a),u=C(c.baseId,n),p=N(c.baseId,n),h=n===c.value,f=r.useRef(h);return r.useEffect(()=>{let e=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,y.jsx)(l.C,{present:i||h,children:a=>{let{present:r}=a;return(0,y.jsx)(s.sG.div,{"data-state":h?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:p,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:f.current?"0s":void 0},children:r&&o})}})});function C(e,t){return"".concat(e,"-trigger-").concat(t)}function N(e,t){return"".concat(e,"-content-").concat(t)}D.displayName=j;var R=m,M=b,F=w,V=D},62525:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},82178:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("Pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]])},85339:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},85690:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},89114:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("FastForward",[["polygon",{points:"13 19 22 12 13 5 13 19",key:"587y9g"}],["polygon",{points:"2 19 11 12 2 5 2 19",key:"3pweh0"}]])},91788:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(19946).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])}}]);