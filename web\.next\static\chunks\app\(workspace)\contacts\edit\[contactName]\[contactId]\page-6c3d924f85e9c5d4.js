(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6478],{6654:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return s}});let n=a(12115);function s(e,t){let a=(0,n.useRef)(null),s=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=a.current;e&&(a.current=null,e());let t=s.current;t&&(s.current=null,t())}else e&&(a.current=l(e,n)),t&&(s.current=l(t,n))},[e,t])}function l(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let a=e(t);return"function"==typeof a?a:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},14636:(e,t,a)=>{"use strict";a.d(t,{AM:()=>o,Wv:()=>i,hl:()=>r});var n=a(95155);a(12115);var s=a(20547),l=a(59434);function o(e){let{...t}=e;return(0,n.jsx)(s.bL,{"data-slot":"popover",...t})}function i(e){let{...t}=e;return(0,n.jsx)(s.l9,{"data-slot":"popover-trigger",...t})}function r(e){let{className:t,align:a="center",sideOffset:o=4,...i}=e;return(0,n.jsx)(s.ZL,{children:(0,n.jsx)(s.UC,{"data-slot":"popover-content",align:a,sideOffset:o,className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",t),...i})})}},16820:(e,t,a)=>{"use strict";a.d(t,{N:()=>x});var n=a(95155),s=a(90350),l=a.n(s),o=a(12115),i=a(14636),r=a(30285),c=a(77740),d=a(47924),u=a(59434);function m(e){let{className:t,...a}=e;return(0,n.jsx)(c.uB,{"data-slot":"command",className:(0,u.cn)("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",t),...a})}function p(e){let{className:t,...a}=e;return(0,n.jsxs)("div",{"data-slot":"command-input-wrapper",className:"flex h-9 items-center gap-2 border-b px-3",children:[(0,n.jsx)(d.A,{className:"size-4 shrink-0 opacity-50"}),(0,n.jsx)(c.uB.Input,{"data-slot":"command-input",className:(0,u.cn)("placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50",t),...a})]})}function g(e){let{...t}=e;return(0,n.jsx)(c.uB.Empty,{"data-slot":"command-empty",className:"py-6 text-center text-sm",...t})}function h(e){let{className:t,...a}=e;return(0,n.jsx)(c.uB.Group,{"data-slot":"command-group",className:(0,u.cn)("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",t),...a})}function v(e){let{className:t,...a}=e;return(0,n.jsx)(c.uB.Item,{"data-slot":"command-item",className:(0,u.cn)("data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...a})}a(54165);let x=e=>{var t;let{value:a,onChange:s}=e,[c,d]=(0,o.useState)(!1),[u,x]=(0,o.useState)(""),f=l().tz.names().reduce((e,t)=>{let a=t.split("/")[0];return e[a]||(e[a]=[]),e[a].push(t),e},{});return(0,n.jsxs)(i.AM,{modal:!0,open:c,onOpenChange:d,children:[(0,n.jsx)(i.Wv,{asChild:!0,children:(0,n.jsx)(r.$,{variant:"outline",role:"combobox","aria-expanded":c,className:"w-full justify-between",children:a?"".concat(null===(t=a.split("/").pop())||void 0===t?void 0:t.replace(/_/g," ")," (").concat(l().tz(a).format("Z"),")"):"Select timezone..."})}),(0,n.jsx)(i.hl,{className:"w-[250px] p-0",align:"start",side:"bottom",sideOffset:5,children:(0,n.jsxs)(m,{className:"max-h-[300px]",children:[(0,n.jsx)("div",{className:"sticky top-0 bg-background z-10 border-b",children:(0,n.jsx)(p,{placeholder:"Search timezone...",onValueChange:x,className:"py-2"})}),(0,n.jsxs)("div",{className:"overflow-y-auto max-h-[250px]",children:[(0,n.jsx)(g,{children:"No timezone found."}),Object.entries(f).map(e=>{let[t,a]=e,o=a.filter(e=>e.toLowerCase().includes(u.toLowerCase()));return 0===o.length?null:(0,n.jsx)(h,{heading:t,children:o.map(e=>{var t;return(0,n.jsxs)(v,{value:e,onSelect:()=>{s(e),d(!1)},className:"cursor-pointer",children:[null===(t=e.split("/").pop())||void 0===t?void 0:t.replace(/_/g," ")," (",l().tz(e).format("Z"),")"]},e)})},t)})]})]})})]})}},21877:(e,t,a)=>{Promise.resolve().then(a.bind(a,73335))},35169:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,t,a)=>{"use strict";var n=a(18999);a.o(n,"useParams")&&a.d(t,{useParams:function(){return n.useParams}}),a.o(n,"usePathname")&&a.d(t,{usePathname:function(){return n.usePathname}}),a.o(n,"useRouter")&&a.d(t,{useRouter:function(){return n.useRouter}})},40968:(e,t,a)=>{"use strict";a.d(t,{b:()=>i});var n=a(12115),s=a(63655),l=a(95155),o=n.forwardRef((e,t)=>(0,l.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null===(a=e.onMouseDown)||void 0===a||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var i=o},59409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>p,gC:()=>m,l6:()=>c,yv:()=>d});var n=a(95155);a(12115);var s=a(31992),l=a(66474),o=a(5196),i=a(47863),r=a(59434);function c(e){let{...t}=e;return(0,n.jsx)(s.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,n.jsx)(s.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,children:a,...o}=e;return(0,n.jsxs)(s.l9,{"data-slot":"select-trigger",className:(0,r.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o,children:[a,(0,n.jsx)(s.In,{asChild:!0,children:(0,n.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:t,children:a,position:l="popper",...o}=e;return(0,n.jsx)(s.ZL,{children:(0,n.jsxs)(s.UC,{"data-slot":"select-content",className:(0,r.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md","popper"===l&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:l,...o,children:[(0,n.jsx)(g,{}),(0,n.jsx)(s.LM,{className:(0,r.cn)("p-1","popper"===l&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,n.jsx)(h,{})]})})}function p(e){let{className:t,children:a,...l}=e;return(0,n.jsxs)(s.q7,{"data-slot":"select-item",className:(0,r.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...l,children:[(0,n.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,n.jsx)(s.VF,{children:(0,n.jsx)(o.A,{className:"size-4"})})}),(0,n.jsx)(s.p4,{children:a})]})}function g(e){let{className:t,...a}=e;return(0,n.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,r.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,n.jsx)(i.A,{className:"size-4"})})}function h(e){let{className:t,...a}=e;return(0,n.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,r.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,n.jsx)(l.A,{className:"size-4"})})}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>r,Wu:()=>c,ZB:()=>i,Zp:()=>l,aR:()=>o,wL:()=>d});var n=a(95155);a(12115);var s=a(59434);function l(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",t),...a})}function o(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("flex flex-col gap-1.5 px-6",t),...a})}function i(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function r(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6",t),...a})}},73335:(e,t,a)=>{"use strict";a.d(t,{default:()=>j});var n=a(95155),s=a(12115),l=a(35695),o=a(30285),i=a(62523),r=a(85057),c=a(66695),d=a(35169),u=a(5196),m=a(6874),p=a.n(m),g=a(54165),h=a(25561),v=a(59409),x=a(59071),f=a(16820);function j(e){var t,a,m,j,b,y,N,P,A,C,w,D,_,I,k,F,z;let{contactName:R,contactId:L}=e,T=(0,l.useParams)();R||null==T||T.contactName;let O=L||(null==T?void 0:T.contactId),S=O?decodeURIComponent(O):"",J=(0,l.useRouter)(),[E,B]=(0,s.useState)(!1),[$,q]=(0,s.useState)(""),[M,U]=(0,s.useState)([]),[Z,W]=(0,s.useState)(!1),[G,V]=(0,s.useState)(""),[H,K]=(0,s.useState)({_id:"",contactName:"",phoneNumber:"",region:"",projectName:"",unitNumber:"",totalPayableAmount:"",pendingPayableAmount:"",dueDate:"",totalInstallments:"",paymentType:"",pendingInstallments:"",lastPaymentDate:"",lastPaymentAmount:"",lastPaymentType:"",collectionBucket:"",unitPrice:"",paidAmtIncluding:"",eventDate:"",eventLocation:"",eventTime:"",nameOfRegistrant:"",campaigns:[]}),Q=async()=>{try{let e=localStorage.getItem("access_token");if(!e){console.error("No access token available"),q("Authentication required");return}let t=await fetch("".concat("http://localhost:4000","/api/contacts/").concat(S),{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error("Failed to fetch contact: ".concat(t.status));let a=await t.json();if(a){let e="";a.eventDate&&(e="object"==typeof a.eventDate&&"$date"in a.eventDate?new Date(a.eventDate.$date).toISOString():a.eventDate.toString()),console.log("Processed eventDate:",e),K({...a,contactName:a.contactName||"",phoneNumber:a.phoneNumber||"",region:a.region||"",campaigns:a.campaigns||[],eventDate:e,eventLocation:a.eventLocation||"",eventTime:a.eventTime||"",nameOfRegistrant:a.nameOfRegistrant||""}),V(a.phoneNumber||"")}else q("Contact not found")}catch(e){console.error("Error fetching contact:",e),q("Failed to fetch contact data")}},X=async()=>{try{let e=await (0,h.to)();console.log("Loaded campaigns:",e),U(e);let t=e.find(e=>"AquaRiseEvent"===e.name);console.log("AquaRiseEvent campaign:",t)}catch(e){console.error("Error fetching campaigns:",e)}},Y=e=>e?new Date(e).toISOString().split("T")[0]:"";(0,s.useEffect)(()=>{S&&(Q(),X())},[S]),(0,s.useEffect)(()=>{console.log("Contact state updated:",{campaigns:H.campaigns,eventDate:H.eventDate,eventLocation:H.eventLocation,eventTime:H.eventTime,nameOfRegistrant:H.nameOfRegistrant})},[H]);let ee=()=>{let e;if(!H.campaigns||0===H.campaigns.length||!M)return null;console.log("Campaign ID for lookup:",e="object"==typeof H.campaigns[0]?"$oid"in H.campaigns[0]?H.campaigns[0].$oid:H.campaigns[0]._id:H.campaigns[0]),console.log("Available campaigns:",M);let t=M.find(t=>t._id===e||"object"==typeof t._id&&"$oid"in t._id&&t._id.$oid===e);if(!t)return console.log("No campaign found with ID:",e),null;let a=t.name.toLowerCase();return a.includes("collections")?"Collections":a.includes("sales")?"Sales":a.includes("aquarise")||a.includes("aqua rise")?"AquaRiseEvent":null};(0,s.useEffect)(()=>{if(H.phoneNumber&&H.phoneNumber!==G){let e=(0,x.s)(H.phoneNumber);e&&K(t=>({...t,region:e}))}},[H.phoneNumber,G]);let et=async()=>{if(!H.contactName||!H.phoneNumber||!H.region||!H.campaigns||0===H.campaigns.length){q("Contact Name, Phone Number, Region, and Campaign are required");return}B(!0),q("");try{let e={...H,totalPayableAmount:"string"==typeof H.totalPayableAmount?parseFloat(H.totalPayableAmount)||0:H.totalPayableAmount,pendingPayableAmount:"string"==typeof H.pendingPayableAmount?parseFloat(H.pendingPayableAmount)||0:H.pendingPayableAmount,lastPaymentAmount:"string"==typeof H.lastPaymentAmount?parseFloat(H.lastPaymentAmount)||0:H.lastPaymentAmount,unitPrice:"string"==typeof H.unitPrice?parseFloat(H.unitPrice)||0:H.unitPrice,paidAmtIncluding:"string"==typeof H.paidAmtIncluding?parseFloat(H.paidAmtIncluding)||0:H.paidAmtIncluding,totalInstallments:"string"==typeof H.totalInstallments?parseInt(H.totalInstallments)||0:H.totalInstallments,pendingInstallments:"string"==typeof H.pendingInstallments?parseInt(H.pendingInstallments)||0:H.pendingInstallments,eventDate:H.eventDate?(()=>{try{if("string"==typeof H.eventDate&&H.eventDate.includes("T"))return H.eventDate;return new Date(H.eventDate).toISOString()}catch(e){return console.error("Error formatting eventDate for submission:",e),H.eventDate}})():void 0};if(!(await fetch("".concat("http://localhost:4000","/api/contacts/").concat(H._id),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify(e)})).ok)throw Error("Failed to update contact");W(!0)}catch(e){q("Failed to update contact"),console.error(e)}finally{B(!1)}};return(0,n.jsxs)("div",{className:"max-w-6xl mx-auto py-2",children:[(0,n.jsx)("div",{className:"mb-6",children:(0,n.jsxs)(p(),{href:"/contacts",className:"text-sm text-muted-foreground hover:text-primary flex items-center gap-2",children:[(0,n.jsx)(d.A,{className:"h-4 w-4"}),"Back to Contacts"]})}),(0,n.jsxs)(c.Zp,{children:[(0,n.jsxs)(c.aR,{children:[(0,n.jsx)(c.ZB,{children:"Edit Contact"}),(0,n.jsx)(c.BT,{children:"Update contact information."})]}),(0,n.jsx)(c.Wu,{children:(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{className:"mb-2",children:"Campaign*"}),(0,n.jsxs)(v.l6,{value:(()=>{let e="";if(H.campaigns&&H.campaigns.length>0){if("object"==typeof H.campaigns[0]){if("$oid"in H.campaigns[0]){let t=H.campaigns[0].$oid,a=M.find(e=>e._id===t||"object"==typeof e._id&&"$oid"in e._id&&e._id.$oid===t);e=(null==a?void 0:a.name)||""}else"_id"in H.campaigns[0]&&(e=H.campaigns[0].name||"")}else{let t=M.find(e=>e._id===(H.campaigns||[])[0]);e=(null==t?void 0:t.name)||""}}return e})(),onValueChange:e=>{let t=M.find(t=>t.name===e);console.log("Found campaign:",t),t&&K(e=>({...e,campaigns:[t._id]}))},children:[(0,n.jsx)(v.bq,{children:(0,n.jsx)(v.yv,{placeholder:"Select a campaign"})}),(0,n.jsx)(v.gC,{children:null==M?void 0:M.map(e=>(0,n.jsx)(v.eb,{value:e.name,children:e.name},e._id))})]})]})}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"contactName",className:"mb-2",children:"Contact Name*"}),(0,n.jsx)(i.p,{id:"contactName",value:null!==(t=H.contactName)&&void 0!==t?t:"",onChange:e=>K(t=>({...t,contactName:e.target.value}))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"phoneNumber",className:"mb-2",children:"Phone Number*"}),(0,n.jsx)(i.p,{id:"phoneNumber",value:null!==(a=H.phoneNumber)&&void 0!==a?a:"",onChange:e=>K(t=>({...t,phoneNumber:e.target.value}))})]})]}),(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"region",className:"mb-2",children:"Region*"}),(0,n.jsx)(f.N,{value:null!==(m=H.region)&&void 0!==m?m:"",onChange:e=>K(t=>({...t,region:e}))})]})})]}),"AquaRiseEvent"===ee()&&(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"eventDate",className:"mb-2",children:"Event Date"}),(0,n.jsx)(i.p,{id:"eventDate",type:"datetime-local",value:(()=>{if(!H.eventDate)return"";try{return console.log("Formatting eventDate for input:",H.eventDate),H.eventDate.slice(0,16)}catch(e){return console.error("Error formatting eventDate:",e),""}})(),onChange:e=>K(t=>({...t,eventDate:e.target.value}))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"eventLocation",className:"mb-2",children:"Event Location"}),(0,n.jsx)(i.p,{id:"eventLocation",value:(console.log("Event location value:",H.eventLocation),null!==(F=H.eventLocation)&&void 0!==F?F:""),onChange:e=>K(t=>({...t,eventLocation:e.target.value}))})]})]}),(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"nameOfRegistrant",className:"mb-2",children:"Name of Registrant"}),(0,n.jsx)(i.p,{id:"nameOfRegistrant",value:(console.log("Name of registrant value:",H.nameOfRegistrant),null!==(z=H.nameOfRegistrant)&&void 0!==z?z:""),onChange:e=>K(t=>({...t,nameOfRegistrant:e.target.value}))})]})})]})}),"Collections"===ee()&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"projectName",className:"mb-2",children:"Project Name"}),(0,n.jsx)(i.p,{id:"projectName",value:null!==(j=H.projectName)&&void 0!==j?j:"",onChange:e=>K(t=>({...t,projectName:e.target.value}))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"unitNumber",className:"mb-2",children:"Unit Number"}),(0,n.jsx)(i.p,{id:"unitNumber",value:null!==(b=H.unitNumber)&&void 0!==b?b:"",onChange:e=>K(t=>({...t,unitNumber:e.target.value}))})]})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"unitPrice",className:"mb-2",children:"Unit Price"}),(0,n.jsx)(i.p,{id:"unitPrice",type:"number",value:null!==(y=H.unitPrice)&&void 0!==y?y:"",onChange:e=>K(t=>({...t,unitPrice:e.target.value}))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"paidAmtIncluding",className:"mb-2",children:"Paid Amount Including"}),(0,n.jsx)(i.p,{id:"paidAmtIncluding",type:"number",value:null!==(N=H.paidAmtIncluding)&&void 0!==N?N:"",onChange:e=>K(t=>({...t,paidAmtIncluding:e.target.value}))})]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"totalPayableAmount",className:"mb-2",children:"Total Payable Amount"}),(0,n.jsx)(i.p,{id:"totalPayableAmount",type:"number",value:null!==(P=H.totalPayableAmount)&&void 0!==P?P:"",onChange:e=>K(t=>({...t,totalPayableAmount:e.target.value}))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"pendingPayableAmount",className:"mb-2",children:"Pending Payable Amount"}),(0,n.jsx)(i.p,{id:"pendingPayableAmount",type:"number",value:null!==(A=H.pendingPayableAmount)&&void 0!==A?A:"",onChange:e=>K(t=>({...t,pendingPayableAmount:e.target.value}))})]})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"dueDate",className:"mb-2",children:"Due Date"}),(0,n.jsx)(i.p,{id:"dueDate",type:"date",value:H.dueDate?Y(H.dueDate):"",onChange:e=>K(t=>({...t,dueDate:e.target.value}))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"paymentType",className:"mb-2",children:"Payment Type"}),(0,n.jsx)(i.p,{id:"paymentType",value:null!==(C=H.paymentType)&&void 0!==C?C:"",onChange:e=>K(t=>({...t,paymentType:e.target.value}))})]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"totalInstallments",className:"mb-2",children:"Total Installments"}),(0,n.jsx)(i.p,{id:"totalInstallments",type:"number",value:null!==(w=H.totalInstallments)&&void 0!==w?w:"",onChange:e=>K(t=>({...t,totalInstallments:e.target.value}))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"pendingInstallments",className:"mb-2",children:"Pending Installments"}),(0,n.jsx)(i.p,{id:"pendingInstallments",type:"number",value:null!==(D=H.pendingInstallments)&&void 0!==D?D:"",onChange:e=>K(t=>({...t,pendingInstallments:e.target.value}))})]})]}),(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"collectionBucket",className:"mb-2",children:"Collection Bucket"}),(0,n.jsx)(i.p,{id:"collectionBucket",value:null!==(_=H.collectionBucket)&&void 0!==_?_:"",onChange:e=>K(t=>({...t,collectionBucket:e.target.value}))})]})})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"lastPaymentDate",className:"mb-2",children:"Last Payment Date"}),(0,n.jsx)(i.p,{id:"lastPaymentDate",type:"date",value:H.lastPaymentDate?Y(H.lastPaymentDate):"",onChange:e=>K(t=>({...t,lastPaymentDate:e.target.value}))})]})}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"lastPaymentAmount",className:"mb-2",children:"Last Payment Amount"}),(0,n.jsx)(i.p,{id:"lastPaymentAmount",type:"number",value:null!==(I=H.lastPaymentAmount)&&void 0!==I?I:"",onChange:e=>K(t=>({...t,lastPaymentAmount:e.target.value}))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(r.J,{htmlFor:"lastPaymentType",className:"mb-2",children:"Last Payment Type"}),(0,n.jsx)(i.p,{id:"lastPaymentType",value:null!==(k=H.lastPaymentType)&&void 0!==k?k:"",onChange:e=>K(t=>({...t,lastPaymentType:e.target.value}))})]})]})]})]}),$&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded-md text-sm",children:$}),(0,n.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,n.jsx)(o.$,{variant:"outline",onClick:()=>J.push("/contacts"),children:"Cancel"}),(0,n.jsx)(o.$,{onClick:et,disabled:E,children:E?"Updating...":"Update Contact"})]})]})})]}),(0,n.jsx)(g.lG,{open:Z,onOpenChange:W,children:(0,n.jsxs)(g.Cf,{children:[(0,n.jsxs)(g.c7,{children:[(0,n.jsx)(g.L3,{children:"Success"}),(0,n.jsx)(g.rr,{children:"Contact updated successfully."})]}),(0,n.jsx)(g.Es,{children:(0,n.jsxs)(o.$,{onClick:()=>J.push("/contacts"),children:[(0,n.jsx)(u.A,{className:"mr-2 h-4 w-4"})," Back to Contacts"]})})]})})]})}},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>o});var n=a(95155);a(12115);var s=a(40968),l=a(59434);function o(e){let{className:t,...a}=e;return(0,n.jsx)(s.b,{"data-slot":"label",className:(0,l.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}}},e=>{var t=t=>e(e.s=t);e.O(0,[7596,586,4201,4341,6403,1071,6544,6874,7394,9621,8441,1684,7358],()=>t(21877)),_N_E=e.O()}]);