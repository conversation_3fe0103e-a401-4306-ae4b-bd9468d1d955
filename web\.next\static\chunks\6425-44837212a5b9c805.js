"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6425],{25657:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},40646:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},80244:(e,t,r)=>{r.d(t,{OK:()=>J,bL:()=>K,VM:()=>T,lr:()=>k,LM:()=>Z});var n=r(12115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function l(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function i(...e){return n.useCallback(l(...e),e)}r(47650);var a=r(95155),s=Symbol("radix.slottable");function u(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}var c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){var i;let e,a;let s=(i=r,(a=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(a=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),u=function(e,t){let r={...t};for(let n in t){let o=e[n],l=t[n];/^on[A-Z]/.test(n)?o&&l?r[n]=(...e)=>{let t=l(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...l}:"className"===n&&(r[n]=[o,l].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(u.ref=t?l(t,s):s),n.cloneElement(r,u)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...l}=e,i=n.Children.toArray(o),s=i.find(u);if(s){let e=s.props.children,o=i.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,a.jsx)(t,{...l,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...l}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?r:t,{...l,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),d=globalThis?.document?n.useLayoutEffect:()=>{},f=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[o,l]=n.useState(),i=n.useRef(null),a=n.useRef(e),s=n.useRef("none"),[u,c]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=p(i.current);s.current="mounted"===u?e:"none"},[u]),d(()=>{let t=i.current,r=a.current;if(r!==e){let n=s.current,o=p(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):r&&n!==o?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),d(()=>{if(o){var e;let t;let r=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,n=e=>{let n=p(i.current).includes(e.animationName);if(e.target===o&&n&&(c("ANIMATION_END"),!a.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},l=e=>{e.target===o&&(s.current=p(i.current))};return o.addEventListener("animationstart",l),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",l),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}c("ANIMATION_END")},[o,c]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:n.useCallback(e=>{i.current=e?getComputedStyle(e):null,l(e)},[])}}(t),l="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),a=i(o.ref,function(e){var t,r;let n=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null===(r=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||o.isPresent?n.cloneElement(l,{ref:a}):null};function p(e){return(null==e?void 0:e.animationName)||"none"}function v(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}f.displayName="Presence";var h=n.createContext(void 0);function m(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}var w="ScrollArea",[g,y]=function(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return o.scopeName=e,[function(t,o){let l=n.createContext(o),i=r.length;r=[...r,o];let s=t=>{let{scope:r,children:o,...s}=t,u=r?.[e]?.[i]||l,c=n.useMemo(()=>s,Object.values(s));return(0,a.jsx)(u.Provider,{value:c,children:o})};return s.displayName=t+"Provider",[s,function(r,a){let s=a?.[e]?.[i]||l,u=n.useContext(s);if(u)return u;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(o,...t)]}(w),[b,S]=g(w),x=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:o="hover",dir:l,scrollHideDelay:s=600,...u}=e,[d,f]=n.useState(null),[p,v]=n.useState(null),[m,w]=n.useState(null),[g,y]=n.useState(null),[S,x]=n.useState(null),[E,C]=n.useState(0),[R,T]=n.useState(0),[N,P]=n.useState(!1),[L,A]=n.useState(!1),_=i(t,e=>f(e)),j=function(e){let t=n.useContext(h);return e||t||"ltr"}(l);return(0,a.jsx)(b,{scope:r,type:o,dir:j,scrollHideDelay:s,scrollArea:d,viewport:p,onViewportChange:v,content:m,onContentChange:w,scrollbarX:g,onScrollbarXChange:y,scrollbarXEnabled:N,onScrollbarXEnabledChange:P,scrollbarY:S,onScrollbarYChange:x,scrollbarYEnabled:L,onScrollbarYEnabledChange:A,onCornerWidthChange:C,onCornerHeightChange:T,children:(0,a.jsx)(c.div,{dir:j,...u,ref:_,style:{position:"relative","--radix-scroll-area-corner-width":E+"px","--radix-scroll-area-corner-height":R+"px",...e.style}})})});x.displayName=w;var E="ScrollAreaViewport",C=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:o,nonce:l,...s}=e,u=S(E,r),d=i(t,n.useRef(null),u.onViewportChange);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,a.jsx)(c.div,{"data-radix-scroll-area-viewport":"",...s,ref:d,style:{overflowX:u.scrollbarXEnabled?"scroll":"hidden",overflowY:u.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,a.jsx)("div",{ref:u.onContentChange,style:{minWidth:"100%",display:"table"},children:o})})]})});C.displayName=E;var R="ScrollAreaScrollbar",T=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=S(R,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:s}=l,u="horizontal"===e.orientation;return n.useEffect(()=>(u?i(!0):s(!0),()=>{u?i(!1):s(!1)}),[u,i,s]),"hover"===l.type?(0,a.jsx)(N,{...o,ref:t,forceMount:r}):"scroll"===l.type?(0,a.jsx)(P,{...o,ref:t,forceMount:r}):"auto"===l.type?(0,a.jsx)(L,{...o,ref:t,forceMount:r}):"always"===l.type?(0,a.jsx)(A,{...o,ref:t}):null});T.displayName=R;var N=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=S(R,e.__scopeScrollArea),[i,s]=n.useState(!1);return n.useEffect(()=>{let e=l.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[l.scrollArea,l.scrollHideDelay]),(0,a.jsx)(f,{present:r||i,children:(0,a.jsx)(L,{"data-state":i?"visible":"hidden",...o,ref:t})})}),P=n.forwardRef((e,t)=>{var r;let{forceMount:o,...l}=e,i=S(R,e.__scopeScrollArea),s="horizontal"===e.orientation,u=q(()=>d("SCROLL_END"),100),[c,d]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},"hidden"));return n.useEffect(()=>{if("idle"===c){let e=window.setTimeout(()=>d("HIDE"),i.scrollHideDelay);return()=>window.clearTimeout(e)}},[c,i.scrollHideDelay,d]),n.useEffect(()=>{let e=i.viewport,t=s?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(d("SCROLL"),u()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[i.viewport,s,d,u]),(0,a.jsx)(f,{present:o||"hidden"!==c,children:(0,a.jsx)(A,{"data-state":"hidden"===c?"hidden":"visible",...l,ref:t,onPointerEnter:m(e.onPointerEnter,()=>d("POINTER_ENTER")),onPointerLeave:m(e.onPointerLeave,()=>d("POINTER_LEAVE"))})})}),L=n.forwardRef((e,t)=>{let r=S(R,e.__scopeScrollArea),{forceMount:o,...l}=e,[i,s]=n.useState(!1),u="horizontal"===e.orientation,c=q(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(u?e:t)}},10);return G(r.viewport,c),G(r.content,c),(0,a.jsx)(f,{present:o||i,children:(0,a.jsx)(A,{"data-state":i?"visible":"hidden",...l,ref:t})})}),A=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,l=S(R,e.__scopeScrollArea),i=n.useRef(null),s=n.useRef(0),[u,c]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=F(u.viewport,u.content),f={...o,sizes:u,onSizesChange:c,hasThumb:!!(d>0&&d<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>s.current=0,onThumbPointerDown:e=>s.current=e};function p(e,t){return function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=X(r),l=t||o/2,i=r.scrollbar.paddingStart+l,a=r.scrollbar.size-r.scrollbar.paddingEnd-(o-l),s=r.content-r.viewport;return $([i,a],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,s.current,u,t)}return"horizontal"===r?(0,a.jsx)(_,{...f,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=Y(l.viewport.scrollLeft,u,l.dir);i.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=p(e,l.dir))}}):"vertical"===r?(0,a.jsx)(j,{...f,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=Y(l.viewport.scrollTop,u);i.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=p(e))}}):null}),_=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...l}=e,s=S(R,e.__scopeScrollArea),[u,c]=n.useState(),d=n.useRef(null),f=i(t,d,s.onScrollbarXChange);return n.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,a.jsx)(M,{"data-orientation":"horizontal",...l,ref:f,sizes:r,style:{bottom:0,left:"rtl"===s.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===s.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":X(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(s.viewport){let n=s.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&s.viewport&&u&&o({content:s.viewport.scrollWidth,viewport:s.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:V(u.paddingLeft),paddingEnd:V(u.paddingRight)}})}})}),j=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...l}=e,s=S(R,e.__scopeScrollArea),[u,c]=n.useState(),d=n.useRef(null),f=i(t,d,s.onScrollbarYChange);return n.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,a.jsx)(M,{"data-orientation":"vertical",...l,ref:f,sizes:r,style:{top:0,right:"ltr"===s.dir?0:void 0,left:"rtl"===s.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":X(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(s.viewport){let n=s.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&s.viewport&&u&&o({content:s.viewport.scrollHeight,viewport:s.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:V(u.paddingTop),paddingEnd:V(u.paddingBottom)}})}})}),[D,O]=g(R),M=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:o,hasThumb:l,onThumbChange:s,onThumbPointerUp:u,onThumbPointerDown:d,onThumbPositionChange:f,onDragScroll:p,onWheelScroll:h,onResize:w,...g}=e,y=S(R,r),[b,x]=n.useState(null),E=i(t,e=>x(e)),C=n.useRef(null),T=n.useRef(""),N=y.viewport,P=o.content-o.viewport,L=v(h),A=v(f),_=q(w,10);function j(e){C.current&&p({x:e.clientX-C.current.left,y:e.clientY-C.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;(null==b?void 0:b.contains(t))&&L(e,P)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[N,b,P,L]),n.useEffect(A,[o,A]),G(b,_),G(y.content,_),(0,a.jsx)(D,{scope:r,scrollbar:b,hasThumb:l,onThumbChange:v(s),onThumbPointerUp:v(u),onThumbPositionChange:A,onThumbPointerDown:v(d),children:(0,a.jsx)(c.div,{...g,ref:E,style:{position:"absolute",...g.style},onPointerDown:m(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),C.current=b.getBoundingClientRect(),T.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",y.viewport&&(y.viewport.style.scrollBehavior="auto"),j(e))}),onPointerMove:m(e.onPointerMove,j),onPointerUp:m(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=T.current,y.viewport&&(y.viewport.style.scrollBehavior=""),C.current=null})})})}),I="ScrollAreaThumb",k=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=O(I,e.__scopeScrollArea);return(0,a.jsx)(f,{present:r||o.hasThumb,children:(0,a.jsx)(W,{ref:t,...n})})}),W=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:o,...l}=e,s=S(I,r),u=O(I,r),{onThumbPositionChange:d}=u,f=i(t,e=>u.onThumbChange(e)),p=n.useRef(void 0),v=q(()=>{p.current&&(p.current(),p.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{v(),p.current||(p.current=B(e,d),d())};return d(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,v,d]),(0,a.jsx)(c.div,{"data-state":u.hasThumb?"visible":"hidden",...l,ref:f,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...o},onPointerDownCapture:m(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;u.onThumbPointerDown({x:r,y:n})}),onPointerUp:m(e.onPointerUp,u.onThumbPointerUp)})});k.displayName=I;var U="ScrollAreaCorner",z=n.forwardRef((e,t)=>{let r=S(U,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,a.jsx)(H,{...e,ref:t}):null});z.displayName=U;var H=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...o}=e,l=S(U,r),[i,s]=n.useState(0),[u,d]=n.useState(0),f=!!(i&&u);return G(l.scrollbarX,()=>{var e;let t=(null===(e=l.scrollbarX)||void 0===e?void 0:e.offsetHeight)||0;l.onCornerHeightChange(t),d(t)}),G(l.scrollbarY,()=>{var e;let t=(null===(e=l.scrollbarY)||void 0===e?void 0:e.offsetWidth)||0;l.onCornerWidthChange(t),s(t)}),f?(0,a.jsx)(c.div,{...o,ref:t,style:{width:i,height:u,position:"absolute",right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:0,...e.style}}):null});function V(e){return e?parseInt(e,10):0}function F(e,t){let r=e/t;return isNaN(r)?0:r}function X(e){let t=F(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function Y(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=X(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,l=t.scrollbar.size-o,i=t.content-t.viewport,a=function(e,[t,r]){return Math.min(r,Math.max(t,e))}(e,"ltr"===r?[0,i]:[-1*i,0]);return $([0,i],[0,l-n])(a)}function $(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var B=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},r={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let l={left:e.scrollLeft,top:e.scrollTop},i=r.left!==l.left,a=r.top!==l.top;(i||a)&&t(),r=l,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function q(e,t){let r=v(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function G(e,t){let r=v(t);d(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var K=x,Z=C,J=z},85339:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},85977:(e,t,r)=>{r.d(t,{H4:()=>S,_V:()=>b,bL:()=>y});var n=r(12115),o=r(46081),l=r(39033),i=r(52712),a=r(63655),s=r(95155),u="Avatar",[c,d]=(0,o.A)(u),[f,p]=c(u),v=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...o}=e,[l,i]=n.useState("idle");return(0,s.jsx)(f,{scope:r,imageLoadingStatus:l,onImageLoadingStatusChange:i,children:(0,s.jsx)(a.sG.span,{...o,ref:t})})});v.displayName=u;var h="AvatarImage",m=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:o,onLoadingStatusChange:u=()=>{},...c}=e,d=p(h,r),f=function(e,t){let[r,o]=n.useState("idle");return(0,i.N)(()=>{if(!e){o("error");return}let r=!0,n=new window.Image,l=e=>()=>{r&&o(e)};return o("loading"),n.onload=l("loaded"),n.onerror=l("error"),n.src=e,t&&(n.referrerPolicy=t),()=>{r=!1}},[e,t]),r}(o,c.referrerPolicy),v=(0,l.c)(e=>{u(e),d.onImageLoadingStatusChange(e)});return(0,i.N)(()=>{"idle"!==f&&v(f)},[f,v]),"loaded"===f?(0,s.jsx)(a.sG.img,{...c,ref:t,src:o}):null});m.displayName=h;var w="AvatarFallback",g=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:o,...l}=e,i=p(w,r),[u,c]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>c(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==i.imageLoadingStatus?(0,s.jsx)(a.sG.span,{...l,ref:t}):null});g.displayName=w;var y=v,b=m,S=g}}]);