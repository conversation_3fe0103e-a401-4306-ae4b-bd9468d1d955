"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9424],{20848:(e,t,r)=>{let n,i,a,o,s,c,l,u;function d(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function h(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function p(e){return(p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function f(e){var t=function(e,t){if("object"!=p(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=p(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==p(t)?t:t+""}function v(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,f(n.key),n)}}function g(e,t,r){return t&&v(e.prototype,t),r&&v(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function m(e,t){if(t&&("object"==p(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function y(e){return(y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function _(e,t){return(_=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function b(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_(e,t)}function S(e,t,r){return(t=f(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function k(e,t,r,n,i,a,o){try{var s=e[a](o),c=s.value}catch(e){return void r(e)}s.done?t(c):Promise.resolve(c).then(n,i)}function w(e){return function(){var t=this,r=arguments;return new Promise(function(n,i){var a=e.apply(t,r);function o(e){k(a,n,i,o,s,"next",e)}function s(e){k(a,n,i,o,s,"throw",e)}o(void 0)})}}function E(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function T(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,o,s=[],c=!0,l=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(s.push(n.value),s.length!==t);c=!0);}catch(e){l=!0,i=e}finally{try{if(!c&&null!=r.return&&(o=r.return(),Object(o)!==o))return}finally{if(l)throw i}}return s}}(e,t)||function(e,t){if(e){if("string"==typeof e)return E(e,t);var r=({}).toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?E(e,t):void 0}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}r.r(t),r.d(t,{DAILY_ACCESS_LEVEL_FULL:()=>no,DAILY_ACCESS_LEVEL_LOBBY:()=>ns,DAILY_ACCESS_LEVEL_NONE:()=>nc,DAILY_ACCESS_UNKNOWN:()=>na,DAILY_CAMERA_ERROR_CAM_AND_MIC_IN_USE:()=>nw,DAILY_CAMERA_ERROR_CAM_IN_USE:()=>nS,DAILY_CAMERA_ERROR_CONSTRAINTS:()=>nM,DAILY_CAMERA_ERROR_MIC_IN_USE:()=>nk,DAILY_CAMERA_ERROR_NOT_FOUND:()=>nC,DAILY_CAMERA_ERROR_PERMISSIONS:()=>nE,DAILY_CAMERA_ERROR_UNDEF_MEDIADEVICES:()=>nT,DAILY_CAMERA_ERROR_UNKNOWN:()=>nA,DAILY_EVENT_ACCESS_STATE_UPDATED:()=>n$,DAILY_EVENT_ACTIVE_SPEAKER_CHANGE:()=>is,DAILY_EVENT_ACTIVE_SPEAKER_MODE_CHANGE:()=>ic,DAILY_EVENT_APP_MSG:()=>n8,DAILY_EVENT_CAMERA_ERROR:()=>nR,DAILY_EVENT_CPU_LOAD_CHANGE:()=>id,DAILY_EVENT_ERROR:()=>iE,DAILY_EVENT_EXIT_FULLSCREEN:()=>iv,DAILY_EVENT_FACE_COUNTS_UPDATED:()=>ih,DAILY_EVENT_FULLSCREEN:()=>ip,DAILY_EVENT_IFRAME_LAUNCH_CONFIG:()=>nI,DAILY_EVENT_IFRAME_READY_FOR_LAUNCH_CONFIG:()=>nO,DAILY_EVENT_INPUT_SETTINGS_UPDATED:()=>ik,DAILY_EVENT_JOINED_MEETING:()=>nF,DAILY_EVENT_JOINING_MEETING:()=>nx,DAILY_EVENT_LANG_UPDATED:()=>ib,DAILY_EVENT_LEFT_MEETING:()=>nV,DAILY_EVENT_LIVE_STREAMING_ERROR:()=>i_,DAILY_EVENT_LIVE_STREAMING_STARTED:()=>ig,DAILY_EVENT_LIVE_STREAMING_STOPPED:()=>iy,DAILY_EVENT_LIVE_STREAMING_UPDATED:()=>im,DAILY_EVENT_LOADED:()=>nN,DAILY_EVENT_LOADING:()=>nL,DAILY_EVENT_LOAD_ATTEMPT_FAILED:()=>nD,DAILY_EVENT_LOCAL_SCREEN_SHARE_CANCELED:()=>io,DAILY_EVENT_LOCAL_SCREEN_SHARE_STARTED:()=>ii,DAILY_EVENT_LOCAL_SCREEN_SHARE_STOPPED:()=>ia,DAILY_EVENT_MEETING_SESSION_DATA_ERROR:()=>nz,DAILY_EVENT_MEETING_SESSION_STATE_UPDATED:()=>nW,DAILY_EVENT_MEETING_SESSION_SUMMARY_UPDATED:()=>nq,DAILY_EVENT_NETWORK_CONNECTION:()=>iu,DAILY_EVENT_NETWORK_QUALITY_CHANGE:()=>il,DAILY_EVENT_NONFATAL_ERROR:()=>iw,DAILY_EVENT_PARTICIPANT_COUNTS_UPDATED:()=>nY,DAILY_EVENT_PARTICIPANT_JOINED:()=>nU,DAILY_EVENT_PARTICIPANT_LEFT:()=>nJ,DAILY_EVENT_PARTICIPANT_UPDATED:()=>nB,DAILY_EVENT_RECEIVE_SETTINGS_UPDATED:()=>iS,DAILY_EVENT_RECORDING_DATA:()=>n9,DAILY_EVENT_RECORDING_ERROR:()=>n5,DAILY_EVENT_RECORDING_STARTED:()=>n2,DAILY_EVENT_RECORDING_STATS:()=>n4,DAILY_EVENT_RECORDING_STOPPED:()=>n3,DAILY_EVENT_RECORDING_UPLOAD_COMPLETED:()=>n6,DAILY_EVENT_REMOTE_MEDIA_PLAYER_STARTED:()=>ie,DAILY_EVENT_REMOTE_MEDIA_PLAYER_STOPPED:()=>ir,DAILY_EVENT_REMOTE_MEDIA_PLAYER_UPDATED:()=>it,DAILY_EVENT_STARTED_CAMERA:()=>nj,DAILY_EVENT_THEME_UPDATED:()=>nP,DAILY_EVENT_TRACK_STARTED:()=>nQ,DAILY_EVENT_TRACK_STOPPED:()=>nX,DAILY_EVENT_TRANSCRIPTION_ERROR:()=>n1,DAILY_EVENT_TRANSCRIPTION_MSG:()=>n7,DAILY_EVENT_TRANSCRIPTION_STARTED:()=>nZ,DAILY_EVENT_TRANSCRIPTION_STOPPED:()=>n0,DAILY_EVENT_WAITING_PARTICIPANT_ADDED:()=>nG,DAILY_EVENT_WAITING_PARTICIPANT_REMOVED:()=>nK,DAILY_EVENT_WAITING_PARTICIPANT_UPDATED:()=>nH,DAILY_FATAL_ERROR_CONNECTION:()=>nb,DAILY_FATAL_ERROR_EJECTED:()=>nd,DAILY_FATAL_ERROR_EOL:()=>ny,DAILY_FATAL_ERROR_EXP_ROOM:()=>nf,DAILY_FATAL_ERROR_EXP_TOKEN:()=>nv,DAILY_FATAL_ERROR_MEETING_FULL:()=>nm,DAILY_FATAL_ERROR_NBF_ROOM:()=>nh,DAILY_FATAL_ERROR_NBF_TOKEN:()=>np,DAILY_FATAL_ERROR_NOT_ALLOWED:()=>n_,DAILY_FATAL_ERROR_NO_ROOM:()=>ng,DAILY_RECEIVE_SETTINGS_ALL_PARTICIPANTS_KEY:()=>nu,DAILY_RECEIVE_SETTINGS_BASE_KEY:()=>nl,DAILY_STATE_ERROR:()=>r8,DAILY_STATE_JOINED:()=>r6,DAILY_STATE_JOINING:()=>r5,DAILY_STATE_LEFT:()=>r9,DAILY_STATE_NEW:()=>r2,DAILY_TRACK_STATE_BLOCKED:()=>r7,DAILY_TRACK_STATE_INTERRUPTED:()=>nn,DAILY_TRACK_STATE_LOADING:()=>nr,DAILY_TRACK_STATE_OFF:()=>ne,DAILY_TRACK_STATE_PLAYABLE:()=>ni,DAILY_TRACK_STATE_SENDABLE:()=>nt,default:()=>a3});var C,M,A={exports:{}},O="object"==typeof Reflect?Reflect:null,I=O&&"function"==typeof O.apply?O.apply:function(e,t,r){return Function.prototype.apply.call(e,t,r)};M=O&&"function"==typeof O.ownKeys?O.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var P=Number.isNaN||function(e){return e!=e};function L(){L.init.call(this)}A.exports=L,A.exports.once=function(e,t){return new Promise(function(r,n){function i(r){e.removeListener(t,a),n(r)}function a(){"function"==typeof e.removeListener&&e.removeListener("error",i),r([].slice.call(arguments))}J(e,t,a,{once:!0}),"error"!==t&&"function"==typeof e.on&&J(e,"error",i,{once:!0})})},L.EventEmitter=L,L.prototype._events=void 0,L.prototype._eventsCount=0,L.prototype._maxListeners=void 0;var D=10;function N(e){if("function"!=typeof e)throw TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function j(e){return void 0===e._maxListeners?L.defaultMaxListeners:e._maxListeners}function R(e,t,r,n){var i,a,o;if(N(r),void 0===(a=e._events)?(a=e._events=Object.create(null),e._eventsCount=0):(void 0!==a.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),a=e._events),o=a[t]),void 0===o)o=a[t]=r,++e._eventsCount;else if("function"==typeof o?o=a[t]=n?[r,o]:[o,r]:n?o.unshift(r):o.push(r),(i=j(e))>0&&o.length>i&&!o.warned){o.warned=!0;var s=Error("Possible EventEmitter memory leak detected. "+o.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");s.name="MaxListenersExceededWarning",s.emitter=e,s.type=t,s.count=o.length,console&&console.warn&&console.warn(s)}return e}function x(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0==arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function F(e,t,r){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},i=x.bind(n);return i.listener=r,n.wrapFn=i,i}function V(e,t,r){var n=e._events;if(void 0===n)return[];var i=n[t];return void 0===i?[]:"function"==typeof i?r?[i.listener||i]:[i]:r?function(e){for(var t=Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(i):B(i,i.length)}function U(e){var t=this._events;if(void 0!==t){var r=t[e];if("function"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function B(e,t){for(var r=Array(t),n=0;n<t;++n)r[n]=e[n];return r}function J(e,t,r,n){if("function"==typeof e.on)n.once?e.once(t,r):e.on(t,r);else{if("function"!=typeof e.addEventListener)throw TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,function i(a){n.once&&e.removeEventListener(t,i),r(a)})}}Object.defineProperty(L,"defaultMaxListeners",{enumerable:!0,get:function(){return D},set:function(e){if("number"!=typeof e||e<0||P(e))throw RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");D=e}}),L.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},L.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||P(e))throw RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},L.prototype.getMaxListeners=function(){return j(this)},L.prototype.emit=function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var n="error"===e,i=this._events;if(void 0!==i)n=n&&void 0===i.error;else if(!n)return!1;if(n){if(t.length>0&&(a=t[0]),a instanceof Error)throw a;var a,o=Error("Unhandled error."+(a?" ("+a.message+")":""));throw o.context=a,o}var s=i[e];if(void 0===s)return!1;if("function"==typeof s)I(s,this,t);else{var c=s.length,l=B(s,c);for(r=0;r<c;++r)I(l[r],this,t)}return!0},L.prototype.addListener=function(e,t){return R(this,e,t,!1)},L.prototype.on=L.prototype.addListener,L.prototype.prependListener=function(e,t){return R(this,e,t,!0)},L.prototype.once=function(e,t){return N(t),this.on(e,F(this,e,t)),this},L.prototype.prependOnceListener=function(e,t){return N(t),this.prependListener(e,F(this,e,t)),this},L.prototype.removeListener=function(e,t){var r,n,i,a,o;if(N(t),void 0===(n=this._events)||void 0===(r=n[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete n[e],n.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(i=-1,a=r.length-1;a>=0;a--)if(r[a]===t||r[a].listener===t){o=r[a].listener,i=a;break}if(i<0)return this;0===i?r.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(r,i),1===r.length&&(n[e]=r[0]),void 0!==n.removeListener&&this.emit("removeListener",e,o||t)}return this},L.prototype.off=L.prototype.removeListener,L.prototype.removeAllListeners=function(e){var t,r,n;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0==arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0==arguments.length){var i,a=Object.keys(r);for(n=0;n<a.length;++n)"removeListener"!==(i=a[n])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(void 0!==t)for(n=t.length-1;n>=0;n--)this.removeListener(e,t[n]);return this},L.prototype.listeners=function(e){return V(this,e,!0)},L.prototype.rawListeners=function(e){return V(this,e,!1)},L.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):U.call(e,t)},L.prototype.listenerCount=U,L.prototype.eventNames=function(){return this._eventsCount>0?M(this._events):[]};var Y=A.exports,$=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(Y),q=Object.prototype.hasOwnProperty;function W(e,t,r){for(r of e.keys())if(z(r,t))return r}function z(e,t){var r,n,i;if(e===t)return!0;if(e&&t&&(r=e.constructor)===t.constructor){if(r===Date)return e.getTime()===t.getTime();if(r===RegExp)return e.toString()===t.toString();if(r===Array){if((n=e.length)===t.length)for(;n--&&z(e[n],t[n]););return -1===n}if(r===Set){if(e.size!==t.size)return!1;for(n of e)if((i=n)&&"object"==typeof i&&!(i=W(t,i))||!t.has(i))return!1;return!0}if(r===Map){if(e.size!==t.size)return!1;for(n of e)if((i=n[0])&&"object"==typeof i&&!(i=W(t,i))||!z(n[1],t.get(i)))return!1;return!0}if(r===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(r===DataView){if((n=e.byteLength)===t.byteLength)for(;n--&&e.getInt8(n)===t.getInt8(n););return -1===n}if(ArrayBuffer.isView(e)){if((n=e.byteLength)===t.byteLength)for(;n--&&e[n]===t[n];);return -1===n}if(!r||"object"==typeof e){for(r in n=0,e)if(q.call(e,r)&&++n&&!q.call(t,r)||!(r in t)||!z(e[r],t[r]))return!1;return Object.keys(t).length===n}}return e!=e&&t!=t}let G={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},H={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},K={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},Q={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},X={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"};class Z{static getFirstMatch(e,t){let r=t.match(e);return r&&r.length>0&&r[1]||""}static getSecondMatch(e,t){let r=t.match(e);return r&&r.length>1&&r[2]||""}static matchAndReturnConst(e,t,r){if(e.test(t))return r}static getWindowsVersionName(e){switch(e){case"NT":return"NT";case"XP":case"NT 5.1":return"XP";case"NT 5.0":return"2000";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}}static getMacOSVersionName(e){let t=e.split(".").splice(0,2).map(e=>parseInt(e,10)||0);if(t.push(0),10===t[0])switch(t[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}}static getAndroidVersionName(e){let t=e.split(".").splice(0,2).map(e=>parseInt(e,10)||0);if(t.push(0),!(1===t[0]&&t[1]<5))return 1===t[0]&&t[1]<6?"Cupcake":1===t[0]&&t[1]>=6?"Donut":2===t[0]&&t[1]<2?"Eclair":2===t[0]&&2===t[1]?"Froyo":2===t[0]&&t[1]>2?"Gingerbread":3===t[0]?"Honeycomb":4===t[0]&&t[1]<1?"Ice Cream Sandwich":4===t[0]&&t[1]<4?"Jelly Bean":4===t[0]&&t[1]>=4?"KitKat":5===t[0]?"Lollipop":6===t[0]?"Marshmallow":7===t[0]?"Nougat":8===t[0]?"Oreo":9===t[0]?"Pie":void 0}static getVersionPrecision(e){return e.split(".").length}static compareVersions(e,t,r=!1){let n=Z.getVersionPrecision(e),i=Z.getVersionPrecision(t),a=Math.max(n,i),o=0,s=Z.map([e,t],e=>{let t=a-Z.getVersionPrecision(e),r=e+Array(t+1).join(".0");return Z.map(r.split("."),e=>Array(20-e.length).join("0")+e).reverse()});for(r&&(o=a-Math.min(n,i)),a-=1;a>=o;){if(s[0][a]>s[1][a])return 1;if(s[0][a]===s[1][a]){if(a===o)return 0;a-=1}else if(s[0][a]<s[1][a])return -1}}static map(e,t){let r;let n=[];if(Array.prototype.map)return Array.prototype.map.call(e,t);for(r=0;r<e.length;r+=1)n.push(t(e[r]));return n}static find(e,t){let r,n;if(Array.prototype.find)return Array.prototype.find.call(e,t);for(r=0,n=e.length;r<n;r+=1){let n=e[r];if(t(n,r))return n}}static assign(e,...t){let r,n;if(Object.assign)return Object.assign(e,...t);for(r=0,n=t.length;r<n;r+=1){let n=t[r];"object"==typeof n&&null!==n&&Object.keys(n).forEach(t=>{e[t]=n[t]})}return e}static getBrowserAlias(e){return G[e]}static getBrowserTypeByAlias(e){return H[e]||""}}let ee=/version\/(\d+(\.?_?\d+)+)/i,et=[{test:[/googlebot/i],describe(e){let t={name:"Googlebot"},r=Z.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/opera/i],describe(e){let t={name:"Opera"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opr\/|opios/i],describe(e){let t={name:"Opera"},r=Z.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/SamsungBrowser/i],describe(e){let t={name:"Samsung Internet for Android"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Whale/i],describe(e){let t={name:"NAVER Whale Browser"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MZBrowser/i],describe(e){let t={name:"MZ Browser"},r=Z.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/focus/i],describe(e){let t={name:"Focus"},r=Z.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/swing/i],describe(e){let t={name:"Swing"},r=Z.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/coast/i],describe(e){let t={name:"Opera Coast"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe(e){let t={name:"Opera Touch"},r=Z.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/yabrowser/i],describe(e){let t={name:"Yandex Browser"},r=Z.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/ucbrowser/i],describe(e){let t={name:"UC Browser"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Maxthon|mxios/i],describe(e){let t={name:"Maxthon"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/epiphany/i],describe(e){let t={name:"Epiphany"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/puffin/i],describe(e){let t={name:"Puffin"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sleipnir/i],describe(e){let t={name:"Sleipnir"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/k-meleon/i],describe(e){let t={name:"K-Meleon"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/micromessenger/i],describe(e){let t={name:"WeChat"},r=Z.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/qqbrowser/i],describe(e){let t={name:/qqbrowserlite/i.test(e)?"QQ Browser Lite":"QQ Browser"},r=Z.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/msie|trident/i],describe(e){let t={name:"Internet Explorer"},r=Z.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/\sedg\//i],describe(e){let t={name:"Microsoft Edge"},r=Z.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/edg([ea]|ios)/i],describe(e){let t={name:"Microsoft Edge"},r=Z.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/vivaldi/i],describe(e){let t={name:"Vivaldi"},r=Z.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/seamonkey/i],describe(e){let t={name:"SeaMonkey"},r=Z.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sailfish/i],describe(e){let t={name:"Sailfish"},r=Z.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,e);return r&&(t.version=r),t}},{test:[/silk/i],describe(e){let t={name:"Amazon Silk"},r=Z.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/phantom/i],describe(e){let t={name:"PhantomJS"},r=Z.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/slimerjs/i],describe(e){let t={name:"SlimerJS"},r=Z.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(e){let t={name:"BlackBerry"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(web|hpw)[o0]s/i],describe(e){let t={name:"WebOS Browser"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/bada/i],describe(e){let t={name:"Bada"},r=Z.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/tizen/i],describe(e){let t={name:"Tizen"},r=Z.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/qupzilla/i],describe(e){let t={name:"QupZilla"},r=Z.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/firefox|iceweasel|fxios/i],describe(e){let t={name:"Firefox"},r=Z.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/electron/i],describe(e){let t={name:"Electron"},r=Z.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MiuiBrowser/i],describe(e){let t={name:"Miui"},r=Z.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/chromium/i],describe(e){let t={name:"Chromium"},r=Z.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/chrome|crios|crmo/i],describe(e){let t={name:"Chrome"},r=Z.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/GSA/i],describe(e){let t={name:"Google Search"},r=Z.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test(e){let t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe(e){let t={name:"Android Browser"},r=Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/playstation 4/i],describe(e){let t={name:"PlayStation 4"},r=Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/safari|applewebkit/i],describe(e){let t={name:"Safari"},r=Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/.*/i],describe(e){let t=-1!==e.search("\\(")?/^(.*)\/(.*)[ \t]\((.*)/:/^(.*)\/(.*) /;return{name:Z.getFirstMatch(t,e),version:Z.getSecondMatch(t,e)}}}];var er=[{test:[/Roku\/DVP/],describe(e){let t=Z.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,e);return{name:Q.Roku,version:t}}},{test:[/windows phone/i],describe(e){let t=Z.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,e);return{name:Q.WindowsPhone,version:t}}},{test:[/windows /i],describe(e){let t=Z.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,e),r=Z.getWindowsVersionName(t);return{name:Q.Windows,version:t,versionName:r}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(e){let t={name:Q.iOS},r=Z.getSecondMatch(/(Version\/)(\d[\d.]+)/,e);return r&&(t.version=r),t}},{test:[/macintosh/i],describe(e){let t=Z.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,e).replace(/[_\s]/g,"."),r=Z.getMacOSVersionName(t),n={name:Q.MacOS,version:t};return r&&(n.versionName=r),n}},{test:[/(ipod|iphone|ipad)/i],describe(e){let t=Z.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,e).replace(/[_\s]/g,".");return{name:Q.iOS,version:t}}},{test(e){let t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe(e){let t=Z.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,e),r=Z.getAndroidVersionName(t),n={name:Q.Android,version:t};return r&&(n.versionName=r),n}},{test:[/(web|hpw)[o0]s/i],describe(e){let t=Z.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,e),r={name:Q.WebOS};return t&&t.length&&(r.version=t),r}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(e){let t=Z.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,e)||Z.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,e)||Z.getFirstMatch(/\bbb(\d+)/i,e);return{name:Q.BlackBerry,version:t}}},{test:[/bada/i],describe(e){let t=Z.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,e);return{name:Q.Bada,version:t}}},{test:[/tizen/i],describe(e){let t=Z.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,e);return{name:Q.Tizen,version:t}}},{test:[/linux/i],describe:()=>({name:Q.Linux})},{test:[/CrOS/],describe:()=>({name:Q.ChromeOS})},{test:[/PlayStation 4/],describe(e){let t=Z.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,e);return{name:Q.PlayStation4,version:t}}}],en=[{test:[/googlebot/i],describe:()=>({type:"bot",vendor:"Google"})},{test:[/huawei/i],describe(e){let t=Z.getFirstMatch(/(can-l01)/i,e)&&"Nova",r={type:K.mobile,vendor:"Huawei"};return t&&(r.model=t),r}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe:()=>({type:K.tablet,vendor:"Nexus"})},{test:[/ipad/i],describe:()=>({type:K.tablet,vendor:"Apple",model:"iPad"})},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:()=>({type:K.tablet,vendor:"Apple",model:"iPad"})},{test:[/kftt build/i],describe:()=>({type:K.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"})},{test:[/silk/i],describe:()=>({type:K.tablet,vendor:"Amazon"})},{test:[/tablet(?! pc)/i],describe:()=>({type:K.tablet})},{test(e){let t=e.test(/ipod|iphone/i),r=e.test(/like (ipod|iphone)/i);return t&&!r},describe(e){let t=Z.getFirstMatch(/(ipod|iphone)/i,e);return{type:K.mobile,vendor:"Apple",model:t}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe:()=>({type:K.mobile,vendor:"Nexus"})},{test:[/[^-]mobi/i],describe:()=>({type:K.mobile})},{test:e=>"blackberry"===e.getBrowserName(!0),describe:()=>({type:K.mobile,vendor:"BlackBerry"})},{test:e=>"bada"===e.getBrowserName(!0),describe:()=>({type:K.mobile})},{test:e=>"windows phone"===e.getBrowserName(),describe:()=>({type:K.mobile,vendor:"Microsoft"})},{test(e){let t=Number(String(e.getOSVersion()).split(".")[0]);return"android"===e.getOSName(!0)&&t>=3},describe:()=>({type:K.tablet})},{test:e=>"android"===e.getOSName(!0),describe:()=>({type:K.mobile})},{test:e=>"macos"===e.getOSName(!0),describe:()=>({type:K.desktop,vendor:"Apple"})},{test:e=>"windows"===e.getOSName(!0),describe:()=>({type:K.desktop})},{test:e=>"linux"===e.getOSName(!0),describe:()=>({type:K.desktop})},{test:e=>"playstation 4"===e.getOSName(!0),describe:()=>({type:K.tv})},{test:e=>"roku"===e.getOSName(!0),describe:()=>({type:K.tv})}],ei=[{test:e=>"microsoft edge"===e.getBrowserName(!0),describe(e){if(/\sedg\//i.test(e))return{name:X.Blink};let t=Z.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,e);return{name:X.EdgeHTML,version:t}}},{test:[/trident/i],describe(e){let t={name:X.Trident},r=Z.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:e=>e.test(/presto/i),describe(e){let t={name:X.Presto},r=Z.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test(e){let t=e.test(/gecko/i),r=e.test(/like gecko/i);return t&&!r},describe(e){let t={name:X.Gecko},r=Z.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(apple)?webkit\/537\.36/i],describe:()=>({name:X.Blink})},{test:[/(apple)?webkit/i],describe(e){let t={name:X.WebKit},r=Z.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}}];class ea{constructor(e,t=!1){if(null==e||""===e)throw Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},!0!==t&&this.parse()}getUA(){return this._ua}test(e){return e.test(this._ua)}parseBrowser(){this.parsedResult.browser={};let e=Z.find(et,e=>{if("function"==typeof e.test)return e.test(this);if(e.test instanceof Array)return e.test.some(e=>this.test(e));throw Error("Browser's test function is not valid")});return e&&(this.parsedResult.browser=e.describe(this.getUA())),this.parsedResult.browser}getBrowser(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()}getBrowserName(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""}getBrowserVersion(){return this.getBrowser().version}getOS(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()}parseOS(){this.parsedResult.os={};let e=Z.find(er,e=>{if("function"==typeof e.test)return e.test(this);if(e.test instanceof Array)return e.test.some(e=>this.test(e));throw Error("Browser's test function is not valid")});return e&&(this.parsedResult.os=e.describe(this.getUA())),this.parsedResult.os}getOSName(e){let{name:t}=this.getOS();return e?String(t).toLowerCase()||"":t||""}getOSVersion(){return this.getOS().version}getPlatform(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()}getPlatformType(e=!1){let{type:t}=this.getPlatform();return e?String(t).toLowerCase()||"":t||""}parsePlatform(){this.parsedResult.platform={};let e=Z.find(en,e=>{if("function"==typeof e.test)return e.test(this);if(e.test instanceof Array)return e.test.some(e=>this.test(e));throw Error("Browser's test function is not valid")});return e&&(this.parsedResult.platform=e.describe(this.getUA())),this.parsedResult.platform}getEngine(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()}getEngineName(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""}parseEngine(){this.parsedResult.engine={};let e=Z.find(ei,e=>{if("function"==typeof e.test)return e.test(this);if(e.test instanceof Array)return e.test.some(e=>this.test(e));throw Error("Browser's test function is not valid")});return e&&(this.parsedResult.engine=e.describe(this.getUA())),this.parsedResult.engine}parse(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this}getResult(){return Z.assign({},this.parsedResult)}satisfies(e){let t={},r=0,n={},i=0;if(Object.keys(e).forEach(a=>{let o=e[a];"string"==typeof o?(n[a]=o,i+=1):"object"==typeof o&&(t[a]=o,r+=1)}),r>0){let e=Object.keys(t),r=Z.find(e,e=>this.isOS(e));if(r){let e=this.satisfies(t[r]);if(void 0!==e)return e}let n=Z.find(e,e=>this.isPlatform(e));if(n){let e=this.satisfies(t[n]);if(void 0!==e)return e}}if(i>0){let e=Object.keys(n),t=Z.find(e,e=>this.isBrowser(e,!0));if(void 0!==t)return this.compareVersion(n[t])}}isBrowser(e,t=!1){let r=this.getBrowserName().toLowerCase(),n=e.toLowerCase(),i=Z.getBrowserTypeByAlias(n);return t&&i&&(n=i.toLowerCase()),n===r}compareVersion(e){let t=[0],r=e,n=!1,i=this.getBrowserVersion();if("string"==typeof i)return">"===e[0]||"<"===e[0]?(r=e.substr(1),"="===e[1]?(n=!0,r=e.substr(2)):t=[],">"===e[0]?t.push(1):t.push(-1)):"="===e[0]?r=e.substr(1):"~"===e[0]&&(n=!0,r=e.substr(1)),t.indexOf(Z.compareVersions(i,r,n))>-1}isOS(e){return this.getOSName(!0)===String(e).toLowerCase()}isPlatform(e){return this.getPlatformType(!0)===String(e).toLowerCase()}isEngine(e){return this.getEngineName(!0)===String(e).toLowerCase()}is(e,t=!1){return this.isBrowser(e,t)||this.isOS(e)||this.isPlatform(e)}some(e=[]){return e.some(e=>this.is(e))}}class eo{static getParser(e,t=!1){if("string"!=typeof e)throw Error("UserAgent should be a string");return new ea(e,t)}static parse(e){return new ea(e).getResult()}static get BROWSER_MAP(){return H}static get ENGINE_MAP(){return X}static get OS_MAP(){return Q}static get PLATFORMS_MAP(){return K}}function es(){return Date.now()+Math.random().toString()}function ec(){throw Error("Method must be implemented in subclass")}function el(e,t){return null!=t&&t.proxyUrl?t.proxyUrl+("/"===t.proxyUrl.slice(-1)?"":"/")+e.substring(8):e}function eu(e){return null!=e&&e.callObjectBundleUrlOverride?e.callObjectBundleUrlOverride:el("https://c.daily.co/call-machine/versioned/".concat("0.79.0","/static/call-machine-object-bundle.js"),e)}function ed(e){try{new URL(e)}catch(e){return!1}return!0}let eh="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__,ep="8.55.0",ef=globalThis;function ev(e,t,r){let n=r||ef,i=n.__SENTRY__=n.__SENTRY__||{},a=i[ep]=i[ep]||{};return a[e]||(a[e]=t())}let eg="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__,em=["debug","info","warn","error","log","assert","trace"],ey={};function e_(e){if(!("console"in ef))return e();let t=ef.console,r={},n=Object.keys(ey);n.forEach(e=>{let n=ey[e];r[e]=t[e],t[e]=n});try{return e()}finally{n.forEach(e=>{t[e]=r[e]})}}let eb=ev("logger",function(){let e=!1,t={enable:()=>{e=!0},disable:()=>{e=!1},isEnabled:()=>e};return eg?em.forEach(r=>{t[r]=(...t)=>{e&&e_(()=>{ef.console[r](`Sentry Logger [${r}]:`,...t)})}}):em.forEach(e=>{t[e]=()=>{}}),t}),eS=/\(error: (.*)\)/,ek=/captureMessage|captureException/;function ew(e){return e[e.length-1]||{}}let eE="<anonymous>";function eT(e){try{return e&&"function"==typeof e&&e.name||eE}catch(e){return eE}}function eC(e){let t=e.exception;if(t){let e=[];try{return t.values.forEach(t=>{t.stacktrace.frames&&e.push(...t.stacktrace.frames)}),e}catch(e){return}}}let eM={},eA={};function eO(e,t){eM[e]=eM[e]||[],eM[e].push(t)}function eI(e,t){if(!eA[e]){eA[e]=!0;try{t()}catch(t){eg&&eb.error(`Error while instrumenting ${e}`,t)}}}function eP(e,t){let r=e&&eM[e];if(r)for(let n of r)try{n(t)}catch(t){eg&&eb.error(`Error while triggering instrumentation handler.
Type: ${e}
Name: ${eT(n)}
Error:`,t)}}let eL=null;function eD(){eL=ef.onerror,ef.onerror=function(e,t,r,n,i){return eP("error",{column:n,error:i,line:r,msg:e,url:t}),!!eL&&eL.apply(this,arguments)},ef.onerror.__SENTRY_INSTRUMENTED__=!0}let eN=null;function ej(){eN=ef.onunhandledrejection,ef.onunhandledrejection=function(e){return eP("unhandledrejection",e),!eN||eN.apply(this,arguments)},ef.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}function eR(){return ex(ef),ef}function ex(e){let t=e.__SENTRY__=e.__SENTRY__||{};return t.version=t.version||ep,t[ep]=t[ep]||{}}let eF=Object.prototype.toString;function eV(e){switch(eF.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return eH(e,Error)}}function eU(e,t){return eF.call(e)===`[object ${t}]`}function eB(e){return eU(e,"ErrorEvent")}function eJ(e){return eU(e,"DOMError")}function eY(e){return eU(e,"String")}function e$(e){return"object"==typeof e&&null!==e&&"__sentry_template_string__"in e&&"__sentry_template_values__"in e}function eq(e){return null===e||e$(e)||"object"!=typeof e&&"function"!=typeof e}function eW(e){return eU(e,"Object")}function ez(e){return"undefined"!=typeof Event&&eH(e,Event)}function eG(e){return!!(e&&e.then&&"function"==typeof e.then)}function eH(e,t){try{return e instanceof t}catch(e){return!1}}function eK(e){return!("object"!=typeof e||null===e||!e.__isVue&&!e._isVue)}function eQ(e,t={}){if(!e)return"<unknown>";try{let r,n=e,i=[],a=0,o=0,s=Array.isArray(t)?t:t.keyAttrs,c=!Array.isArray(t)&&t.maxStringLength||80;for(;n&&a++<5&&(r=function(e,t){let r=[];if(!e||!e.tagName)return"";if(ef.HTMLElement&&e instanceof HTMLElement&&e.dataset){if(e.dataset.sentryComponent)return e.dataset.sentryComponent;if(e.dataset.sentryElement)return e.dataset.sentryElement}r.push(e.tagName.toLowerCase());let n=t&&t.length?t.filter(t=>e.getAttribute(t)).map(t=>[t,e.getAttribute(t)]):null;if(n&&n.length)n.forEach(e=>{r.push(`[${e[0]}="${e[1]}"]`)});else{e.id&&r.push(`#${e.id}`);let t=e.className;if(t&&eY(t))for(let e of t.split(/\s+/))r.push(`.${e}`)}for(let t of["aria-label","type","name","title","alt"]){let n=e.getAttribute(t);n&&r.push(`[${t}="${n}"]`)}return r.join("")}(n,s),!("html"===r||a>1&&o+3*i.length+r.length>=c));)i.push(r),o+=r.length,n=n.parentNode;return i.reverse().join(" > ")}catch(e){return"<unknown>"}}function eX(e,t=0){return"string"!=typeof e||0===t||e.length<=t?e:`${e.slice(0,t)}...`}function eZ(e,t){if(!Array.isArray(e))return"";let r=[];for(let t=0;t<e.length;t++){let n=e[t];try{eK(n)?r.push("[VueViewModel]"):r.push(String(n))}catch(e){r.push("[value cannot be serialized]")}}return r.join(t)}function e0(e,t=[],r=!1){return t.some(t=>(function(e,t,r=!1){return!!eY(e)&&(eU(t,"RegExp")?t.test(e):!!eY(t)&&(r?e===t:e.includes(t)))})(e,t,r))}function e1(e,t,r){if(!(t in e))return;let n=e[t],i=r(n);"function"==typeof i&&e3(i,n);try{e[t]=i}catch(r){eg&&eb.log(`Failed to replace method "${t}" in object`,e)}}function e2(e,t,r){try{Object.defineProperty(e,t,{value:r,writable:!0,configurable:!0})}catch(r){eg&&eb.log(`Failed to add non-enumerable property "${t}" to object`,e)}}function e3(e,t){try{let r=t.prototype||{};e.prototype=t.prototype=r,e2(e,"__sentry_original__",t)}catch(e){}}function e4(e){return e.__sentry_original__}function e5(e){if(eV(e))return{message:e.message,name:e.name,stack:e.stack,...e9(e)};if(ez(e)){let t={type:e.type,target:e6(e.target),currentTarget:e6(e.currentTarget),...e9(e)};return"undefined"!=typeof CustomEvent&&eH(e,CustomEvent)&&(t.detail=e.detail),t}return e}function e6(e){try{return"undefined"!=typeof Element&&eH(e,Element)?eQ(e):Object.prototype.toString.call(e)}catch(e){return"<unknown>"}}function e9(e){if("object"==typeof e&&null!==e){let t={};for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}return{}}function e8(e){return function e(t,r){if(function(e){if(!eW(e))return!1;try{let t=Object.getPrototypeOf(e).constructor.name;return!t||"Object"===t}catch(e){return!0}}(t)){let n=r.get(t);if(void 0!==n)return n;let i={};for(let n of(r.set(t,i),Object.getOwnPropertyNames(t)))void 0!==t[n]&&(i[n]=e(t[n],r));return i}if(Array.isArray(t)){let n=r.get(t);if(void 0!==n)return n;let i=[];return r.set(t,i),t.forEach(t=>{i.push(e(t,r))}),i}return t}(e,new Map)}function e7(){return Date.now()/1e3}let te=function(){let{performance:e}=ef;if(!e||!e.now)return e7;let t=Date.now()-e.now(),r=null==e.timeOrigin?t:e.timeOrigin;return()=>(r+e.now())/1e3}();function tt(){let e=ef.crypto||ef.msCrypto,t=()=>16*Math.random();try{if(e&&e.randomUUID)return e.randomUUID().replace(/-/g,"");e&&e.getRandomValues&&(t=()=>{let t=new Uint8Array(1);return e.getRandomValues(t),t[0]})}catch(e){}return"10000000100040008000100000000000".replace(/[018]/g,e=>(e^(15&t())>>e/4).toString(16))}function tr(e){return e.exception&&e.exception.values?e.exception.values[0]:void 0}function tn(e){let{message:t,event_id:r}=e;if(t)return t;let n=tr(e);return n?n.type&&n.value?`${n.type}: ${n.value}`:n.type||n.value||r||"<unknown>":r||"<unknown>"}function ti(e,t,r){let n=e.exception=e.exception||{},i=n.values=n.values||[],a=i[0]=i[0]||{};a.value||(a.value=t||""),a.type||(a.type=r||"Error")}function ta(e,t){let r=tr(e);if(!r)return;let n=r.mechanism;if(r.mechanism={type:"generic",handled:!0,...n,...t},t&&"data"in t){let e={...n&&n.data,...t.data};r.mechanism.data=e}}function to(e){if(function(e){try{return e.__sentry_captured__}catch(e){}}(e))return!0;try{e2(e,"__sentry_captured__",!0)}catch(e){}return!1}function ts(e){return new tl(t=>{t(e)})}function tc(e){return new tl((t,r)=>{r(e)})}(()=>{let{performance:e}=ef;if(!e||!e.now)return;let t=e.now(),r=Date.now(),n=e.timeOrigin?Math.abs(e.timeOrigin+t-r):36e5,i=e.timing&&e.timing.navigationStart,a="number"==typeof i?Math.abs(i+t-r):36e5;(n<36e5||a<36e5)&&n<=a&&e.timeOrigin})(),function(e){e[e.PENDING=0]="PENDING",e[e.RESOLVED=1]="RESOLVED",e[e.REJECTED=2]="REJECTED"}(C||(C={}));class tl{constructor(e){tl.prototype.__init.call(this),tl.prototype.__init2.call(this),tl.prototype.__init3.call(this),tl.prototype.__init4.call(this),this._state=C.PENDING,this._handlers=[];try{e(this._resolve,this._reject)}catch(e){this._reject(e)}}then(e,t){return new tl((r,n)=>{this._handlers.push([!1,t=>{if(e)try{r(e(t))}catch(e){n(e)}else r(t)},e=>{if(t)try{r(t(e))}catch(e){n(e)}else n(e)}]),this._executeHandlers()})}catch(e){return this.then(e=>e,e)}finally(e){return new tl((t,r)=>{let n,i;return this.then(t=>{i=!1,n=t,e&&e()},t=>{i=!0,n=t,e&&e()}).then(()=>{i?r(n):t(n)})})}__init(){this._resolve=e=>{this._setResult(C.RESOLVED,e)}}__init2(){this._reject=e=>{this._setResult(C.REJECTED,e)}}__init3(){this._setResult=(e,t)=>{this._state===C.PENDING&&(eG(t)?t.then(this._resolve,this._reject):(this._state=e,this._value=t,this._executeHandlers()))}}__init4(){this._executeHandlers=()=>{if(this._state===C.PENDING)return;let e=this._handlers.slice();this._handlers=[],e.forEach(e=>{e[0]||(this._state===C.RESOLVED&&e[1](this._value),this._state===C.REJECTED&&e[2](this._value),e[0]=!0)})}}}function tu(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),e.did||t.did||(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||te(),t.abnormal_mechanism&&(e.abnormal_mechanism=t.abnormal_mechanism),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=32===t.sid.length?t.sid:tt()),void 0!==t.init&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),"number"==typeof t.started&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if("number"==typeof t.duration)e.duration=t.duration;else{let t=e.timestamp-e.started;e.duration=t>=0?t:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),"number"==typeof t.errors&&(e.errors=t.errors),t.status&&(e.status=t.status)}function td(){return tt().substring(16)}function th(e,t,r=2){if(!t||"object"!=typeof t||r<=0)return t;if(e&&t&&0===Object.keys(t).length)return e;let n={...e};for(let e in t)Object.prototype.hasOwnProperty.call(t,e)&&(n[e]=th(n[e],t[e],r-1));return n}let tp="_sentrySpan";function tf(e,t){t?e2(e,tp,t):delete e[tp]}class tv{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:tt(),spanId:td()}}clone(){let e=new tv;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},this._contexts.flags&&(e._contexts.flags={values:[...this._contexts.flags.values]}),e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._requestSession=this._requestSession,e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,tf(e,this[tp]),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&tu(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(e){return this._requestSession=e,this}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,t){return this._tags={...this._tags,[e]:t},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,t){return this._extra={...this._extra,[e]:t},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,t){return null===t?delete this._contexts[e]:this._contexts[e]=t,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;let t="function"==typeof e?e(this):e,[r,n]=t instanceof tg?[t.getScopeData(),t.getRequestSession()]:eW(t)?[e,e.requestSession]:[],{tags:i,extra:a,user:o,contexts:s,level:c,fingerprint:l=[],propagationContext:u}=r||{};return this._tags={...this._tags,...i},this._extra={...this._extra,...a},this._contexts={...this._contexts,...s},o&&Object.keys(o).length&&(this._user=o),c&&(this._level=c),l.length&&(this._fingerprint=l),u&&(this._propagationContext=u),n&&(this._requestSession=n),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._session=void 0,tf(this,void 0),this._attachments=[],this.setPropagationContext({traceId:tt()}),this._notifyScopeListeners(),this}addBreadcrumb(e,t){let r="number"==typeof t?t:100;if(r<=0)return this;let n={timestamp:e7(),...e};return this._breadcrumbs.push(n),this._breadcrumbs.length>r&&(this._breadcrumbs=this._breadcrumbs.slice(-r),this._client&&this._client.recordDroppedEvent("buffer_overflow","log_item")),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:this[tp]}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata=th(this._sdkProcessingMetadata,e,2),this}setPropagationContext(e){return this._propagationContext={spanId:td(),...e},this}getPropagationContext(){return this._propagationContext}captureException(e,t){let r=t&&t.event_id?t.event_id:tt();if(!this._client)return eb.warn("No client configured on scope - will not capture exception!"),r;let n=Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:n,...t,event_id:r},this),r}captureMessage(e,t,r){let n=r&&r.event_id?r.event_id:tt();if(!this._client)return eb.warn("No client configured on scope - will not capture message!"),n;let i=Error(e);return this._client.captureMessage(e,t,{originalException:e,syntheticException:i,...r,event_id:n},this),n}captureEvent(e,t){let r=t&&t.event_id?t.event_id:tt();return this._client?this._client.captureEvent(e,{...t,event_id:r},this):eb.warn("No client configured on scope - will not capture event!"),r}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}let tg=tv;class tm{constructor(e,t){let r,n;r=e||new tg,n=t||new tg,this._stack=[{scope:r}],this._isolationScope=n}withScope(e){let t;let r=this._pushScope();try{t=e(r)}catch(e){throw this._popScope(),e}return eG(t)?t.then(e=>(this._popScope(),e),e=>{throw this._popScope(),e}):(this._popScope(),t)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){let e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function ty(){let e=ex(eR());return e.stack=e.stack||new tm(ev("defaultCurrentScope",()=>new tg),ev("defaultIsolationScope",()=>new tg))}function t_(e){return ty().withScope(e)}function tb(e,t){let r=ty();return r.withScope(()=>(r.getStackTop().scope=e,t(e)))}function tS(e){return ty().withScope(()=>e(ty().getIsolationScope()))}function tk(e){let t=ex(e);return t.acs?t.acs:{withIsolationScope:tS,withScope:t_,withSetScope:tb,withSetIsolationScope:(e,t)=>tS(t),getCurrentScope:()=>ty().getScope(),getIsolationScope:()=>ty().getIsolationScope()}}function tw(){return tk(eR()).getCurrentScope()}function tE(){return tk(eR()).getIsolationScope()}function tT(){return tw().getClient()}let tC=/^sentry-/;function tM(e){return e.split(",").map(e=>e.split("=").map(e=>decodeURIComponent(e.trim()))).reduce((e,[t,r])=>(t&&r&&(e[t]=r),e),{})}let tA=!1;function tO(e){return"number"==typeof e?tI(e):Array.isArray(e)?e[0]+e[1]/1e9:e instanceof Date?tI(e.getTime()):te()}function tI(e){return e>0x2540be3ff?e/1e3:e}function tP(e){if("function"==typeof e.getSpanJSON)return e.getSpanJSON();try{let{spanId:t,traceId:r}=e.spanContext();if(e.attributes&&e.startTime&&e.name&&e.endTime&&e.status){let{attributes:n,startTime:i,name:a,endTime:o,parentSpanId:s,status:c}=e;return e8({span_id:t,trace_id:r,data:n,description:a,parent_span_id:s,start_timestamp:tO(i),timestamp:tO(o)||void 0,status:function(e){if(e&&0!==e.code)return 1===e.code?"ok":e.message||"unknown_error"}(c),op:n["sentry.op"],origin:n["sentry.origin"],_metrics_summary:function(e){let t=e._sentryMetrics;if(!t)return;let r={};for(let[,[e,n]]of t)(r[e]||(r[e]=[])).push(e8(n));return r}(e)})}return{span_id:t,trace_id:r}}catch(e){return{}}}function tL(e){return e._sentryRootSpan||e}let tD="production";function tN(e,t){let r=t.getOptions(),{publicKey:n}=t.getDsn()||{},i=e8({environment:r.environment||tD,release:r.release,public_key:n,trace_id:e});return t.emit("createDsc",i),i}let tj=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function tR(e,t=!1){let{host:r,path:n,pass:i,port:a,projectId:o,protocol:s,publicKey:c}=e;return`${s}://${c}${t&&i?`:${i}`:""}@${r}${a?`:${a}`:""}/${n?`${n}/`:n}${o}`}function tx(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}function tF(e,t=100,n=1/0){try{return function e(t,n,i=1/0,a=1/0,o=function(){let e="function"==typeof WeakSet,t=e?new WeakSet:[];return[function(r){if(e)return!!t.has(r)||(t.add(r),!1);for(let e=0;e<t.length;e++)if(t[e]===r)return!0;return t.push(r),!1},function(r){if(e)t.delete(r);else for(let e=0;e<t.length;e++)if(t[e]===r){t.splice(e,1);break}}]}()){let[s,c]=o;if(null==n||["boolean","string"].includes(typeof n)||"number"==typeof n&&Number.isFinite(n))return n;let l=function(e,t){try{if("domain"===e&&t&&"object"==typeof t&&t._events)return"[Domain]";if("domainEmitter"===e)return"[DomainEmitter]";if(void 0!==r.g&&t===r.g)return"[Global]";if("undefined"!=typeof window&&t===window)return"[Window]";if("undefined"!=typeof document&&t===document)return"[Document]";if(eK(t))return"[VueViewModel]";if(eW(t)&&"nativeEvent"in t&&"preventDefault"in t&&"stopPropagation"in t)return"[SyntheticEvent]";if("number"==typeof t&&!Number.isFinite(t))return`[${t}]`;if("function"==typeof t)return`[Function: ${eT(t)}]`;if("symbol"==typeof t)return`[${String(t)}]`;if("bigint"==typeof t)return`[BigInt: ${String(t)}]`;let n=function(e){let t=Object.getPrototypeOf(e);return t?t.constructor.name:"null prototype"}(t);return/^HTML(\w*)Element$/.test(n)?`[HTMLElement: ${n}]`:`[object ${n}]`}catch(e){return`**non-serializable** (${e})`}}(t,n);if(!l.startsWith("[object "))return l;if(n.__sentry_skip_normalization__)return n;let u="number"==typeof n.__sentry_override_normalization_depth__?n.__sentry_override_normalization_depth__:i;if(0===u)return l.replace("object ","");if(s(n))return"[Circular ~]";if(n&&"function"==typeof n.toJSON)try{return e("",n.toJSON(),u-1,a,o)}catch(e){}let d=Array.isArray(n)?[]:{},h=0,p=e5(n);for(let t in p){if(!Object.prototype.hasOwnProperty.call(p,t))continue;if(h>=a){d[t]="[MaxProperties ~]";break}let r=p[t];d[t]=e(t,r,u-1,a,o),h++}return c(n),d}("",e,t,n)}catch(e){return{ERROR:`**non-serializable** (${e})`}}}function tV(e,t=[]){return[e,t]}function tU(e,t){for(let r of e[1])if(t(r,r[0].type))return!0;return!1}function tB(e){return ef.__SENTRY__&&ef.__SENTRY__.encodePolyfill?ef.__SENTRY__.encodePolyfill(e):(new TextEncoder).encode(e)}let tJ={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket",raw_security:"security"};function tY(e){if(!e||!e.sdk)return;let{name:t,version:r}=e.sdk;return{name:t,version:r}}function t$(e,t){let{extra:r,tags:n,user:i,contexts:a,level:o,sdkProcessingMetadata:s,breadcrumbs:c,fingerprint:l,eventProcessors:u,attachments:d,propagationContext:h,transactionName:p,span:f}=t;tq(e,"extra",r),tq(e,"tags",n),tq(e,"user",i),tq(e,"contexts",a),e.sdkProcessingMetadata=th(e.sdkProcessingMetadata,s,2),o&&(e.level=o),p&&(e.transactionName=p),f&&(e.span=f),c.length&&(e.breadcrumbs=[...e.breadcrumbs,...c]),l.length&&(e.fingerprint=[...e.fingerprint,...l]),u.length&&(e.eventProcessors=[...e.eventProcessors,...u]),d.length&&(e.attachments=[...e.attachments,...d]),e.propagationContext={...e.propagationContext,...h}}function tq(e,t,r){e[t]=th(e[t],r,1)}let tW=["user","level","extra","contexts","tags","fingerprint","requestSession","propagationContext"];function tz(e,t){return tw().captureEvent(e,t)}function tG(e){let t=tT(),r=tE(),n=tw(),{release:i,environment:a=tD}=t&&t.getOptions()||{},{userAgent:o}=ef.navigator||{},s=function(e){let t=te(),r={sid:tt(),init:!0,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>e8({sid:`${r.sid}`,init:r.init,started:new Date(1e3*r.started).toISOString(),timestamp:new Date(1e3*r.timestamp).toISOString(),status:r.status,errors:r.errors,did:"number"==typeof r.did||"string"==typeof r.did?`${r.did}`:void 0,duration:r.duration,abnormal_mechanism:r.abnormal_mechanism,attrs:{release:r.release,environment:r.environment,ip_address:r.ipAddress,user_agent:r.userAgent}})};return e&&tu(r,e),r}({release:i,environment:a,user:n.getUser()||r.getUser(),...o&&{userAgent:o},...e}),c=r.getSession();return c&&"ok"===c.status&&tu(c,{status:"exited"}),tH(),r.setSession(s),n.setSession(s),s}function tH(){let e;let t=tE(),r=tw(),n=r.getSession()||t.getSession();n&&(e={},"ok"===n.status&&(e={status:"exited"}),tu(n,e)),tK(),t.setSession(),r.setSession()}function tK(){let e=tE(),t=tw(),r=tT(),n=t.getSession()||e.getSession();n&&r&&r.captureSession(n)}function tQ(e=!1){e?tH():tK()}let tX=[];function tZ(e,t){for(let r of t)r&&r.afterAllSetup&&r.afterAllSetup(e)}function t0(e,t,r){if(r[t.name])eh&&eb.log(`Integration skipped because it was already installed: ${t.name}`);else{if(r[t.name]=t,-1===tX.indexOf(t.name)&&"function"==typeof t.setupOnce&&(t.setupOnce(),tX.push(t.name)),t.setup&&"function"==typeof t.setup&&t.setup(e),"function"==typeof t.preprocessEvent){let r=t.preprocessEvent.bind(t);e.on("preprocessEvent",(t,n)=>r(t,n,e))}if("function"==typeof t.processEvent){let r=t.processEvent.bind(t),n=Object.assign((t,n)=>r(t,n,e),{id:t.name});e.addEventProcessor(n)}eh&&eb.log(`Integration installed: ${t.name}`)}}class t1 extends Error{constructor(e,t="warn"){super(e),this.message=e,this.logLevel=t}}let t2="Not capturing exception because it's already been captured.";class t3{constructor(e){if(this._options=e,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn?this._dsn=function(e){let t="string"==typeof e?function(e){let t=tj.exec(e);if(!t)return void e_(()=>{console.error(`Invalid Sentry Dsn: ${e}`)});let[r,n,i="",a="",o="",s=""]=t.slice(1),c="",l=s,u=l.split("/");if(u.length>1&&(c=u.slice(0,-1).join("/"),l=u.pop()),l){let e=l.match(/^\d+/);e&&(l=e[0])}return tx({host:a,pass:i,path:c,projectId:l,port:o,protocol:r,publicKey:n})}(e):tx(e);if(t&&function(e){if(!eg)return!0;let{port:t,projectId:r,protocol:n}=e;return!(["protocol","publicKey","host","projectId"].find(t=>!e[t]&&(eb.error(`Invalid Sentry Dsn: ${t} missing`),!0))||(r.match(/^\d+$/)?"http"===n||"https"===n?t&&isNaN(parseInt(t,10))&&(eb.error(`Invalid Sentry Dsn: Invalid port ${t}`),1):(eb.error(`Invalid Sentry Dsn: Invalid protocol ${n}`),1):(eb.error(`Invalid Sentry Dsn: Invalid projectId ${r}`),1)))}(t))return t}(e.dsn):eh&&eb.warn("No DSN provided, client will not send events."),this._dsn){let t=function(e,t,r){return t||`${function(e){let t=e.protocol?`${e.protocol}:`:"",r=e.port?`:${e.port}`:"";return`${t}//${e.host}${r}${e.path?`/${e.path}`:""}/api/`}(e)}${e.projectId}/envelope/?${function(e,t){let r={sentry_version:"7"};return e.publicKey&&(r.sentry_key=e.publicKey),t&&(r.sentry_client=`${t.name}/${t.version}`),new URLSearchParams(r).toString()}(e,r)}`}(this._dsn,e.tunnel,e._metadata?e._metadata.sdk:void 0);this._transport=e.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:t})}let t=["enableTracing","tracesSampleRate","tracesSampler"].find(t=>t in e&&null==e[t]);t&&e_(()=>{console.warn(`[Sentry] Deprecation warning: \`${t}\` is set to undefined, which leads to tracing being enabled. In v9, a value of \`undefined\` will result in tracing being disabled.`)})}captureException(e,t,r){let n=tt();if(to(e))return eh&&eb.log(t2),n;let i={event_id:n,...t};return this._process(this.eventFromException(e,i).then(e=>this._captureEvent(e,i,r))),i.event_id}captureMessage(e,t,r,n){let i={event_id:tt(),...r},a=e$(e)?e:String(e),o=eq(e)?this.eventFromMessage(a,t,i):this.eventFromException(e,i);return this._process(o.then(e=>this._captureEvent(e,i,n))),i.event_id}captureEvent(e,t,r){let n=tt();if(t&&t.originalException&&to(t.originalException))return eh&&eb.log(t2),n;let i={event_id:n,...t},a=(e.sdkProcessingMetadata||{}).capturedSpanScope;return this._process(this._captureEvent(e,i,a||r)),i.event_id}captureSession(e){"string"!=typeof e.release?eh&&eb.warn("Discarded session because of missing or non-string release"):(this.sendSession(e),tu(e,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){let t=this._transport;return t?(this.emit("flush"),this._isClientDoneProcessing(e).then(r=>t.flush(e).then(e=>r&&e))):ts(!0)}close(e){return this.flush(e).then(e=>(this.getOptions().enabled=!1,this.emit("close"),e))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}init(){(this._isEnabled()||this._options.integrations.some(({name:e})=>e.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(e){return this._integrations[e]}addIntegration(e){let t=this._integrations[e.name];t0(this,e,this._integrations),t||tZ(this,[e])}sendEvent(e,t={}){this.emit("beforeSendEvent",e,t);let r=function(e,t,r,n){var i;let a=tY(r),o=e.type&&"replay_event"!==e.type?e.type:"event";(i=r&&r.sdk)&&(e.sdk=e.sdk||{},e.sdk.name=e.sdk.name||i.name,e.sdk.version=e.sdk.version||i.version,e.sdk.integrations=[...e.sdk.integrations||[],...i.integrations||[]],e.sdk.packages=[...e.sdk.packages||[],...i.packages||[]]);let s=function(e,t,r,n){let i=e.sdkProcessingMetadata&&e.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:e.event_id,sent_at:(new Date).toISOString(),...t&&{sdk:t},...!!r&&n&&{dsn:tR(n)},...i&&{trace:e8({...i})}}}(e,a,n,t);return delete e.sdkProcessingMetadata,tV(s,[[{type:o},e]])}(e,this._dsn,this._options._metadata,this._options.tunnel);for(let e of t.attachments||[])r=function(e,t){let[r,n]=e;return[r,[...n,t]]}(r,function(e){let t="string"==typeof e.data?tB(e.data):e.data;return[e8({type:"attachment",length:t.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType}),t]}(e));let n=this.sendEnvelope(r);n&&n.then(t=>this.emit("afterSendEvent",e,t),null)}sendSession(e){let t=function(e,t,r,n){let i=tY(r);return tV({sent_at:(new Date).toISOString(),...i&&{sdk:i},...!!n&&t&&{dsn:tR(t)}},["aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e.toJSON()]])}(e,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(t)}recordDroppedEvent(e,t,r){if(this._options.sendClientReports){let n="number"==typeof r?r:1,i=`${e}:${t}`;eh&&eb.log(`Recording outcome: "${i}"${n>1?` (${n} times)`:""}`),this._outcomes[i]=(this._outcomes[i]||0)+n}}on(e,t){let r=this._hooks[e]=this._hooks[e]||[];return r.push(t),()=>{let e=r.indexOf(t);e>-1&&r.splice(e,1)}}emit(e,...t){let r=this._hooks[e];r&&r.forEach(e=>e(...t))}sendEnvelope(e){return this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport?this._transport.send(e).then(null,e=>(eh&&eb.error("Error while sending envelope:",e),e)):(eh&&eb.error("Transport disabled"),ts({}))}_setupIntegrations(){let{integrations:e}=this._options;this._integrations=function(e,t){let r={};return t.forEach(t=>{t&&t0(e,t,r)}),r}(this,e),tZ(this,e)}_updateSessionFromEvent(e,t){let r="fatal"===t.level,n=!1,i=t.exception&&t.exception.values;if(i)for(let e of(n=!0,i)){let t=e.mechanism;if(t&&!1===t.handled){r=!0;break}}let a="ok"===e.status;(a&&0===e.errors||a&&r)&&(tu(e,{...r&&{status:"crashed"},errors:e.errors||Number(n||r)}),this.captureSession(e))}_isClientDoneProcessing(e){return new tl(t=>{let r=0,n=setInterval(()=>{0==this._numProcessing?(clearInterval(n),t(!0)):(r+=1,e&&r>=e&&(clearInterval(n),t(!1)))},1)})}_isEnabled(){return!1!==this.getOptions().enabled&&void 0!==this._transport}_prepareEvent(e,t,r=tw(),o=tE()){let s=this.getOptions(),c=Object.keys(this._integrations);return!t.integrations&&c.length>0&&(t.integrations=c),this.emit("preprocessEvent",e,t),e.type||o.setLastEventId(e.event_id||t.event_id),(function(e,t,r,o,s,c){let{normalizeDepth:l=3,normalizeMaxBreadth:u=1e3}=e,d={...t,event_id:t.event_id||r.event_id||tt(),timestamp:t.timestamp||e7()},h=r.integrations||e.integrations.map(e=>e.name);(function(e,t){let{environment:r,release:n,dist:i,maxValueLength:a=250}=t;e.environment=e.environment||r||tD,!e.release&&n&&(e.release=n),!e.dist&&i&&(e.dist=i),e.message&&(e.message=eX(e.message,a));let o=e.exception&&e.exception.values&&e.exception.values[0];o&&o.value&&(o.value=eX(o.value,a));let s=e.request;s&&s.url&&(s.url=eX(s.url,a))})(d,e),h.length>0&&(d.sdk=d.sdk||{},d.sdk.integrations=[...d.sdk.integrations||[],...h]),s&&s.emit("applyFrameMetadata",t),void 0===t.type&&function(e,t){let r=function(e){let t=ef._sentryDebugIds;if(!t)return{};let r=Object.keys(t);return a&&r.length===i||(i=r.length,a=r.reduce((r,i)=>{n||(n={});let a=n[i];if(a)r[a[0]]=a[1];else{let a=e(i);for(let e=a.length-1;e>=0;e--){let o=a[e],s=o&&o.filename,c=t[i];if(s&&c){r[s]=c,n[i]=[s,c];break}}}return r},{})),a}(t);try{e.exception.values.forEach(e=>{e.stacktrace.frames.forEach(e=>{r&&e.filename&&(e.debug_id=r[e.filename])})})}catch(e){}}(d,e.stackParser);let p=function(e,t){if(!t)return e;let r=e?e.clone():new tg;return r.update(t),r}(o,r.captureContext);r.mechanism&&ta(d,r.mechanism);let f=s?s.getEventProcessors():[],v=ev("globalScope",()=>new tg).getScopeData();c&&t$(v,c.getScopeData()),p&&t$(v,p.getScopeData());let g=[...r.attachments||[],...v.attachments];return g.length&&(r.attachments=g),function(e,t){let{fingerprint:r,span:n,breadcrumbs:i,sdkProcessingMetadata:a}=t;(function(e,t){let{extra:r,tags:n,user:i,contexts:a,level:o,transactionName:s}=t,c=e8(r);c&&Object.keys(c).length&&(e.extra={...c,...e.extra});let l=e8(n);l&&Object.keys(l).length&&(e.tags={...l,...e.tags});let u=e8(i);u&&Object.keys(u).length&&(e.user={...u,...e.user});let d=e8(a);d&&Object.keys(d).length&&(e.contexts={...d,...e.contexts}),o&&(e.level=o),s&&"transaction"!==e.type&&(e.transaction=s)})(e,t),n&&function(e,t){e.contexts={trace:function(e){let{spanId:t,traceId:r,isRemote:n}=e.spanContext();return e8({parent_span_id:n?t:tP(e).parent_span_id,span_id:n?td():t,trace_id:r})}(t),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:function(e){let t=tT();if(!t)return{};let r=tL(e),n=r._frozenDsc;if(n)return n;let i=r.spanContext().traceState,a=i&&i.get("sentry.dsc"),o=a&&function(e){let t=function(e){if(e&&(eY(e)||Array.isArray(e)))return Array.isArray(e)?e.reduce((e,t)=>(Object.entries(tM(t)).forEach(([t,r])=>{e[t]=r}),e),{}):tM(e)}(e);if(!t)return;let r=Object.entries(t).reduce((e,[t,r])=>(t.match(tC)&&(e[t.slice(7)]=r),e),{});return Object.keys(r).length>0?r:void 0}(a);if(o)return o;let s=tN(e.spanContext().traceId,t),c=tP(r),l=c.data||{},u=l["sentry.sample_rate"];null!=u&&(s.sample_rate=`${u}`);let d=l["sentry.source"],h=c.description;return"url"!==d&&h&&(s.transaction=h),function(e){if("boolean"==typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;let t=tT(),r=t&&t.getOptions();return!!r&&(r.enableTracing||"tracesSampleRate"in r||"tracesSampler"in r)}()&&(s.sampled=String(function(e){let{traceFlags:t}=e.spanContext();return 1===t}(r))),t.emit("createDsc",s,r),s}(t),...e.sdkProcessingMetadata};let r=tP(tL(t)).description;r&&!e.transaction&&"transaction"===e.type&&(e.transaction=r)}(e,n),e.fingerprint=e.fingerprint?Array.isArray(e.fingerprint)?e.fingerprint:[e.fingerprint]:[],r&&(e.fingerprint=e.fingerprint.concat(r)),e.fingerprint&&!e.fingerprint.length&&delete e.fingerprint,function(e,t){let r=[...e.breadcrumbs||[],...t];e.breadcrumbs=r.length?r:void 0}(e,i),e.sdkProcessingMetadata={...e.sdkProcessingMetadata,...a}}(d,v),(function e(t,r,n,i=0){return new tl((a,o)=>{let s=t[i];if(null===r||"function"!=typeof s)a(r);else{let c=s({...r},n);eh&&s.id&&null===c&&eb.log(`Event processor "${s.id}" dropped event`),eG(c)?c.then(r=>e(t,r,n,i+1).then(a)).then(null,o):e(t,c,n,i+1).then(a).then(null,o)}})})([...f,...v.eventProcessors],d,r).then(e=>(e&&function(e){let t={};try{e.exception.values.forEach(e=>{e.stacktrace.frames.forEach(e=>{e.debug_id&&(e.abs_path?t[e.abs_path]=e.debug_id:e.filename&&(t[e.filename]=e.debug_id),delete e.debug_id)})})}catch(e){}if(0===Object.keys(t).length)return;e.debug_meta=e.debug_meta||{},e.debug_meta.images=e.debug_meta.images||[];let r=e.debug_meta.images;Object.entries(t).forEach(([e,t])=>{r.push({type:"sourcemap",code_file:e,debug_id:t})})}(e),"number"==typeof l&&l>0?function(e,t,r){if(!e)return null;let n={...e,...e.breadcrumbs&&{breadcrumbs:e.breadcrumbs.map(e=>({...e,...e.data&&{data:tF(e.data,t,r)}}))},...e.user&&{user:tF(e.user,t,r)},...e.contexts&&{contexts:tF(e.contexts,t,r)},...e.extra&&{extra:tF(e.extra,t,r)}};return e.contexts&&e.contexts.trace&&n.contexts&&(n.contexts.trace=e.contexts.trace,e.contexts.trace.data&&(n.contexts.trace.data=tF(e.contexts.trace.data,t,r))),e.spans&&(n.spans=e.spans.map(e=>({...e,...e.data&&{data:tF(e.data,t,r)}}))),e.contexts&&e.contexts.flags&&n.contexts&&(n.contexts.flags=tF(e.contexts.flags,3,r)),n}(e,l,u):e))})(s,e,t,r,this,o).then(e=>(null===e||(e.contexts={trace:function(e){let{traceId:t,spanId:r,parentSpanId:n}=e.getPropagationContext();return e8({trace_id:t,span_id:r,parent_span_id:n})}(r),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:function(e,t){let r=t.getPropagationContext();return r.dsc||tN(r.traceId,e)}(this,r),...e.sdkProcessingMetadata}),e))}_captureEvent(e,t={},r){return this._processEvent(e,t,r).then(e=>e.event_id,e=>{eh&&(e instanceof t1&&"log"===e.logLevel?eb.log(e.message):eb.warn(e))})}_processEvent(e,t,r){let n=this.getOptions(),{sampleRate:i}=n,a=t5(e),o=t4(e),s=e.type||"error",c=`before send for type \`${s}\``,l=void 0===i?void 0:function(e){if("boolean"==typeof e)return Number(e);let t="string"==typeof e?parseFloat(e):e;if(!("number"!=typeof t||isNaN(t)||t<0||t>1))return t;eh&&eb.warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(e)} of type ${JSON.stringify(typeof e)}.`)}(i);if(o&&"number"==typeof l&&Math.random()>l)return this.recordDroppedEvent("sample_rate","error",e),tc(new t1(`Discarding event because it's not included in the random sample (sampling rate = ${i})`,"log"));let u="replay_event"===s?"replay":s,d=(e.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this._prepareEvent(e,t,r,d).then(r=>{if(null===r)throw this.recordDroppedEvent("event_processor",u,e),new t1("An event processor returned `null`, will not send event.","log");return t.data&&!0===t.data.__sentry__?r:function(e,t){let r=`${t} must return \`null\` or a valid event.`;if(eG(e))return e.then(e=>{if(!eW(e)&&null!==e)throw new t1(r);return e},e=>{throw new t1(`${t} rejected with ${e}`)});if(!eW(e)&&null!==e)throw new t1(r);return e}(function(e,t,r,n){let{beforeSend:i,beforeSendTransaction:a,beforeSendSpan:o}=t;if(t4(r)&&i)return i(r,n);if(t5(r)){if(r.spans&&o){let t=[];for(let n of r.spans){let r=o(n);r?t.push(r):(tA||(e_(()=>{console.warn("[Sentry] Deprecation warning: Returning null from `beforeSendSpan` will be disallowed from SDK version 9.0.0 onwards. The callback will only support mutating spans. To drop certain spans, configure the respective integrations directly.")}),tA=!0),e.recordDroppedEvent("before_send","span"))}r.spans=t}if(a){if(r.spans){let e=r.spans.length;r.sdkProcessingMetadata={...r.sdkProcessingMetadata,spanCountBeforeProcessing:e}}return a(r,n)}}return r}(this,n,r,t),c)}).then(n=>{if(null===n){if(this.recordDroppedEvent("before_send",u,e),a){let t=1+(e.spans||[]).length;this.recordDroppedEvent("before_send","span",t)}throw new t1(`${c} returned \`null\`, will not send event.`,"log")}let i=r&&r.getSession();if(!a&&i&&this._updateSessionFromEvent(i,n),a){let e=(n.sdkProcessingMetadata&&n.sdkProcessingMetadata.spanCountBeforeProcessing||0)-(n.spans?n.spans.length:0);e>0&&this.recordDroppedEvent("before_send","span",e)}let o=n.transaction_info;return a&&o&&n.transaction!==e.transaction&&(n.transaction_info={...o,source:"custom"}),this.sendEvent(n,t),n}).then(null,e=>{if(e instanceof t1)throw e;throw this.captureException(e,{data:{__sentry__:!0},originalException:e}),new t1(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${e}`)})}_process(e){this._numProcessing++,e.then(e=>(this._numProcessing--,e),e=>(this._numProcessing--,e))}_clearOutcomes(){let e=this._outcomes;return this._outcomes={},Object.entries(e).map(([e,t])=>{let[r,n]=e.split(":");return{reason:r,category:n,quantity:t}})}_flushOutcomes(){var e,t;eh&&eb.log("Flushing outcomes...");let r=this._clearOutcomes();if(0===r.length)return void(eh&&eb.log("No outcomes to send"));if(!this._dsn)return void(eh&&eb.log("No dsn provided, will not send outcomes"));eh&&eb.log("Sending outcomes:",r);let n=tV((e=this._options.tunnel&&tR(this._dsn))?{dsn:e}:{},[[{type:"client_report"},{timestamp:t||e7(),discarded_events:r}]]);this.sendEnvelope(n)}}function t4(e){return void 0===e.type}function t5(e){return"transaction"===e.type}function t6(e,t){if("event"===t||"transaction"===t)return Array.isArray(e)?e[1]:void 0}function t9(e,t){let r=tT(),n=tE();if(!r)return;let{beforeBreadcrumb:i=null,maxBreadcrumbs:a=100}=r.getOptions();if(a<=0)return;let o={timestamp:e7(),...e},s=i?e_(()=>i(o,t)):o;null!==s&&(r.emit&&r.emit("beforeAddBreadcrumb",s,t),n.addBreadcrumb(s,a))}let t8=new WeakMap,t7=()=>({name:"FunctionToString",setupOnce(){o=Function.prototype.toString;try{Function.prototype.toString=function(...e){let t=e4(this),r=t8.has(tT())&&void 0!==t?t:this;return o.apply(r,e)}}catch(e){}},setup(e){t8.set(e,!0)}}),re=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,"undefined is not an object (evaluating 'a.L')",'can\'t redefine non-configurable property "solana"',"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/],rt=(e={})=>({name:"InboundFilters",processEvent:(t,r,n)=>!function(e,t){var r;return t.ignoreInternal&&function(e){try{return"SentryError"===e.exception.values[0].type}catch(e){}return!1}(e)?(eh&&eb.warn(`Event dropped due to being internal Sentry Error.
Event: ${tn(e)}`),!0):(r=t.ignoreErrors,!e.type&&r&&r.length&&(function(e){let t;let r=[];e.message&&r.push(e.message);try{t=e.exception.values[e.exception.values.length-1]}catch(e){}return t&&t.value&&(r.push(t.value),t.type&&r.push(`${t.type}: ${t.value}`)),r})(e).some(e=>e0(e,r)))?(eh&&eb.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${tn(e)}`),!0):e.type||!e.exception||!e.exception.values||0===e.exception.values.length||e.message||e.exception.values.some(e=>e.stacktrace||e.type&&"Error"!==e.type||e.value)?!function(e,t){if("transaction"!==e.type||!t||!t.length)return!1;let r=e.transaction;return!!r&&e0(r,t)}(e,t.ignoreTransactions)?!function(e,t){if(!t||!t.length)return!1;let r=rr(e);return!!r&&e0(r,t)}(e,t.denyUrls)?!function(e,t){if(!t||!t.length)return!0;let r=rr(e);return!r||e0(r,t)}(e,t.allowUrls)&&(eh&&eb.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${tn(e)}.
Url: ${rr(e)}`),!0):(eh&&eb.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${tn(e)}.
Url: ${rr(e)}`),!0):(eh&&eb.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${tn(e)}`),!0):(eh&&eb.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${tn(e)}`),!0)}(t,function(e={},t={}){return{allowUrls:[...e.allowUrls||[],...t.allowUrls||[]],denyUrls:[...e.denyUrls||[],...t.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...t.ignoreErrors||[],...e.disableErrorDefaults?[]:re],ignoreTransactions:[...e.ignoreTransactions||[],...t.ignoreTransactions||[]],ignoreInternal:void 0===e.ignoreInternal||e.ignoreInternal}}(e,n.getOptions()))?t:null});function rr(e){try{let t;try{t=e.exception.values[0].stacktrace.frames}catch(e){}return t?function(e=[]){for(let t=e.length-1;t>=0;t--){let r=e[t];if(r&&"<anonymous>"!==r.filename&&"[native code]"!==r.filename)return r.filename||null}return null}(t):null}catch(t){return eh&&eb.error(`Cannot extract url for event ${tn(e)}`),null}}function rn(e,t){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,..."AggregateError"===e.type&&{is_exception_group:!0},exception_id:t}}function ri(e,t,r,n){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,type:"chained",source:t,exception_id:r,parent_id:n}}function ra(e){if(!e)return{};let t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};let r=t[6]||"",n=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],search:r,hash:n,relative:t[5]+r+n}}function ro(){"console"in ef&&em.forEach(function(e){e in ef.console&&e1(ef.console,e,function(t){return ey[e]=t,function(...t){eP("console",{args:t,level:e});let r=ey[e];r&&r.apply(ef.console,t)}})})}let rs=()=>{let e;return{name:"Dedupe",processEvent(t){if(t.type)return t;try{var r;if((r=e)&&(function(e,t){let r=e.message,n=t.message;return!!((r||n)&&(!r||n)&&(r||!n)&&r===n&&rl(e,t)&&rc(e,t))}(t,r)||function(e,t){let r=ru(t),n=ru(e);return!!(r&&n&&r.type===n.type&&r.value===n.value&&rl(e,t)&&rc(e,t))}(t,r)))return eh&&eb.warn("Event dropped due to being a duplicate of previously captured event."),null}catch(e){}return e=t}}};function rc(e,t){let r=eC(e),n=eC(t);if(!r&&!n)return!0;if(r&&!n||!r&&n||n.length!==r.length)return!1;for(let e=0;e<n.length;e++){let t=n[e],i=r[e];if(t.filename!==i.filename||t.lineno!==i.lineno||t.colno!==i.colno||t.function!==i.function)return!1}return!0}function rl(e,t){let r=e.fingerprint,n=t.fingerprint;if(!r&&!n)return!0;if(r&&!n||!r&&n)return!1;try{return r.join("")===n.join("")}catch(e){return!1}}function ru(e){return e.exception&&e.exception.values&&e.exception.values[0]}function rd(e){return void 0===e?void 0:e>=400&&e<500?"warning":e>=500?"error":void 0}function rh(e){return e&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}function rp(e,t){return!!e&&"object"==typeof e&&!!e[t]}function rf(e){return"string"==typeof e?e:e?rp(e,"url")?e.url:e.toString?e.toString():"":""}let rv=0;function rg(e,t={}){if("function"!=typeof e)return e;try{let t=e.__sentry_wrapped__;if(t)return"function"==typeof t?t:e;if(e4(e))return e}catch(t){return e}let r=function(...r){try{let n=r.map(e=>rg(e,t));return e.apply(this,n)}catch(e){throw rv++,setTimeout(()=>{rv--}),function(...e){let t=tk(eR());if(2===e.length){let[r,n]=e;return r?t.withSetScope(r,n):t.withScope(n)}t.withScope(e[0])}(n=>{var i;n.addEventProcessor(e=>(t.mechanism&&(ti(e,void 0,void 0),ta(e,t.mechanism)),e.extra={...e.extra,arguments:r},e)),tw().captureException(e,function(e){if(e)return e instanceof tg||"function"==typeof e||Object.keys(e).some(e=>tW.includes(e))?{captureContext:e}:e}(i))}),e}};try{for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[t]=e[t])}catch(e){}e3(r,e),e2(e,"__sentry_wrapped__",r);try{Object.getOwnPropertyDescriptor(r,"name").configurable&&Object.defineProperty(r,"name",{get:()=>e.name})}catch(e){}return r}let rm="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__;function ry(e,t){let r=rb(e,t),n={type:function(e){let t=e&&e.name;return!t&&rk(e)?e.message&&Array.isArray(e.message)&&2==e.message.length?e.message[0]:"WebAssembly.Exception":t}(t),value:function(e){let t=e&&e.message;return t?t.error&&"string"==typeof t.error.message?t.error.message:rk(e)&&Array.isArray(e.message)&&2==e.message.length?e.message[1]:t:"No error message"}(t)};return r.length&&(n.stacktrace={frames:r}),void 0===n.type&&""===n.value&&(n.value="Unrecoverable error caught"),n}function r_(e,t){return{exception:{values:[ry(e,t)]}}}function rb(e,t){let r=t.stacktrace||t.stack||"",n=t&&rS.test(t.message)?1:0,i="number"==typeof t.framesToPop?t.framesToPop:0;try{return e(r,n,i)}catch(e){}return[]}let rS=/Minified React error #\d+;/i;function rk(e){return"undefined"!=typeof WebAssembly&&void 0!==WebAssembly.Exception&&e instanceof WebAssembly.Exception}function rw(e,t,r,n,i){let a;if(eB(t)&&t.error)return r_(e,t.error);if(eJ(t)||eU(t,"DOMException")){if("stack"in t)a=r_(e,t);else{let i=t.name||(eJ(t)?"DOMError":"DOMException"),o=t.message?`${i}: ${t.message}`:i;ti(a=rE(e,o,r,n),o)}return"code"in t&&(a.tags={...a.tags,"DOMException.code":`${t.code}`}),a}return eV(t)?r_(e,t):(eW(t)||ez(t)?ta(a=function(e,t,r,n){let i=tT(),a=i&&i.getOptions().normalizeDepth,o=function(e){for(let t in e)if(Object.prototype.hasOwnProperty.call(e,t)){let r=e[t];if(r instanceof Error)return r}}(t),s={__serialized__:function e(t,r=3,n=102400){let i=tF(t,r);return~-encodeURI(JSON.stringify(i)).split(/%..|./).length>n?e(t,r-1,n):i}(t,a)};if(o)return{exception:{values:[ry(e,o)]},extra:s};let c={exception:{values:[{type:ez(t)?t.constructor.name:n?"UnhandledRejection":"Error",value:function(e,{isUnhandledRejection:t}){let r=function(e,t=40){let r=Object.keys(e5(e));r.sort();let n=r[0];if(!n)return"[object has no keys]";if(n.length>=t)return eX(n,t);for(let e=r.length;e>0;e--){let n=r.slice(0,e).join(", ");if(!(n.length>t))return e===r.length?n:eX(n,t)}return""}(e),n=t?"promise rejection":"exception";return eB(e)?`Event \`ErrorEvent\` captured as ${n} with message \`${e.message}\``:ez(e)?`Event \`${function(e){try{let t=Object.getPrototypeOf(e);return t?t.constructor.name:void 0}catch(e){}}(e)}\` (type=${e.type}) captured as ${n}`:`Object captured as ${n} with keys: ${r}`}(t,{isUnhandledRejection:n})}]},extra:s};if(r){let t=rb(e,r);t.length&&(c.exception.values[0].stacktrace={frames:t})}return c}(e,t,r,i),{synthetic:!0}):(ti(a=rE(e,t,r,n),`${t}`,void 0),ta(a,{synthetic:!0})),a)}function rE(e,t,r,n){let i={};if(n&&r){let n=rb(e,r);n.length&&(i.exception={values:[{value:t,stacktrace:{frames:n}}]}),ta(i,{synthetic:!0})}if(e$(t)){let{__sentry_template_string__:e,__sentry_template_values__:r}=t;return i.logentry={message:e,params:r},i}return i.message=t,i}class rT extends t3{constructor(e){let t={parentSpanIsAlwaysRootSpan:!0,...e};!function(e,t,r=[t],n="npm"){let i=e._metadata||{};i.sdk||(i.sdk={name:`sentry.javascript.${t}`,packages:r.map(e=>({name:`${n}:@sentry/${e}`,version:ep})),version:ep}),e._metadata=i}(t,"browser",["browser"],ef.SENTRY_SDK_SOURCE||"npm"),super(t),t.sendClientReports&&ef.document&&ef.document.addEventListener("visibilitychange",()=>{"hidden"===ef.document.visibilityState&&this._flushOutcomes()})}eventFromException(e,t){return function(e,t,r,n){let i=rw(e,t,r&&r.syntheticException||void 0,n);return ta(i),i.level="error",r&&r.event_id&&(i.event_id=r.event_id),ts(i)}(this._options.stackParser,e,t,this._options.attachStacktrace)}eventFromMessage(e,t="info",r){return function(e,t,r="info",n,i){let a=rE(e,t,n&&n.syntheticException||void 0,i);return a.level=r,n&&n.event_id&&(a.event_id=n.event_id),ts(a)}(this._options.stackParser,e,t,r,this._options.attachStacktrace)}captureUserFeedback(e){if(!this._isEnabled())return void(rm&&eb.warn("SDK not enabled, will not capture user feedback."));let t=function(e,{metadata:t,tunnel:r,dsn:n}){return tV({event_id:e.event_id,sent_at:(new Date).toISOString(),...t&&t.sdk&&{sdk:{name:t.sdk.name,version:t.sdk.version}},...!!r&&!!n&&{dsn:tR(n)}},[[{type:"user_report"},e]])}(e,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this.sendEnvelope(t)}_prepareEvent(e,t,r){return e.platform=e.platform||"javascript",super._prepareEvent(e,t,r)}}let rC="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__;function rM(){if(!ef.document)return;let e=eP.bind(null,"dom"),t=rA(e,!0);ef.document.addEventListener("click",t,!1),ef.document.addEventListener("keypress",t,!1),["EventTarget","Node"].forEach(t=>{let r=ef[t],n=r&&r.prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&(e1(n,"addEventListener",function(t){return function(r,n,i){if("click"===r||"keypress"==r)try{let n=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},a=n[r]=n[r]||{refCount:0};if(!a.handler){let n=rA(e);a.handler=n,t.call(this,r,n,i)}a.refCount++}catch(e){}return t.call(this,r,n,i)}}),e1(n,"removeEventListener",function(e){return function(t,r,n){if("click"===t||"keypress"==t)try{let r=this.__sentry_instrumentation_handlers__||{},i=r[t];i&&(i.refCount--,i.refCount<=0&&(e.call(this,t,i.handler,n),i.handler=void 0,delete r[t]),0===Object.keys(r).length&&delete this.__sentry_instrumentation_handlers__)}catch(e){}return e.call(this,t,r,n)}}))})}function rA(e,t=!1){return r=>{if(!r||r._sentryCaptured)return;let n=function(e){try{return e.target}catch(e){return null}}(r);if("keypress"===r.type&&(!n||!n.tagName||"INPUT"!==n.tagName&&"TEXTAREA"!==n.tagName&&!n.isContentEditable))return;e2(r,"_sentryCaptured",!0),n&&!n._sentryId&&e2(n,"_sentryId",tt());let i="keypress"===r.type?"input":r.type;!function(e){if(e.type!==c)return!1;try{if(!e.target||e.target._sentryId!==l)return!1}catch(e){}return!0}(r)&&(e({event:r,name:i,global:t}),c=r.type,l=n?n._sentryId:void 0),clearTimeout(s),s=ef.setTimeout(()=>{l=void 0,c=void 0},1e3)}}function rO(e){let t="history";eO(t,e),eI(t,rI)}function rI(){if(!function(){let e=ef.chrome,t=e&&e.app&&e.app.runtime,r="history"in ef&&!!ef.history.pushState&&!!ef.history.replaceState;return!t&&r}())return;let e=ef.onpopstate;function t(e){return function(...t){let r=t.length>2?t[2]:void 0;if(r){let e=u,t=String(r);u=t,eP("history",{from:e,to:t})}return e.apply(this,t)}}ef.onpopstate=function(...t){let r=ef.location.href,n=u;if(u=r,eP("history",{from:n,to:r}),e)try{return e.apply(this,t)}catch(e){}},e1(ef.history,"pushState",t),e1(ef.history,"replaceState",t)}let rP={},rL="__sentry_xhr_v3__";function rD(){if(!ef.XMLHttpRequest)return;let e=XMLHttpRequest.prototype;e.open=new Proxy(e.open,{apply(e,t,r){let n=Error(),i=1e3*te(),a=eY(r[0])?r[0].toUpperCase():void 0,o=function(e){if(eY(e))return e;try{return e.toString()}catch(e){}}(r[1]);if(!a||!o)return e.apply(t,r);t[rL]={method:a,url:o,request_headers:{}},"POST"===a&&o.match(/sentry_key/)&&(t.__sentry_own_request__=!0);let s=()=>{let e=t[rL];if(e&&4===t.readyState){try{e.status_code=t.status}catch(e){}eP("xhr",{endTimestamp:1e3*te(),startTimestamp:i,xhr:t,virtualError:n})}};return"onreadystatechange"in t&&"function"==typeof t.onreadystatechange?t.onreadystatechange=new Proxy(t.onreadystatechange,{apply:(e,t,r)=>(s(),e.apply(t,r))}):t.addEventListener("readystatechange",s),t.setRequestHeader=new Proxy(t.setRequestHeader,{apply(e,t,r){let[n,i]=r,a=t[rL];return a&&eY(n)&&eY(i)&&(a.request_headers[n.toLowerCase()]=i),e.apply(t,r)}}),e.apply(t,r)}}),e.send=new Proxy(e.send,{apply(e,t,r){let n=t[rL];return n&&(void 0!==r[0]&&(n.body=r[0]),eP("xhr",{startTimestamp:1e3*te(),xhr:t})),e.apply(t,r)}})}function rN(e,t=function(e){let t=rP[e];if(t)return t;let r=ef[e];if(rh(r))return rP[e]=r.bind(ef);let n=ef.document;if(n&&"function"==typeof n.createElement)try{let t=n.createElement("iframe");t.hidden=!0,n.head.appendChild(t);let i=t.contentWindow;i&&i[e]&&(r=i[e]),n.head.removeChild(t)}catch(t){rC&&eb.warn(`Could not create sandbox iframe for ${e} check, bailing to window.${e}: `,t)}return r?rP[e]=r.bind(ef):r}("fetch")){let r=0,n=0;return function(e,t,r=function(e){let t=[];function r(e){return t.splice(t.indexOf(e),1)[0]||Promise.resolve(void 0)}return{$:t,add:function(n){if(!(void 0===e||t.length<e))return tc(new t1("Not adding Promise because buffer limit was reached."));let i=n();return -1===t.indexOf(i)&&t.push(i),i.then(()=>r(i)).then(null,()=>r(i).then(null,()=>{})),i},drain:function(e){return new tl((r,n)=>{let i=t.length;if(!i)return r(!0);let a=setTimeout(()=>{e&&e>0&&r(!1)},e);t.forEach(e=>{ts(e).then(()=>{--i||(clearTimeout(a),r(!0))},n)})})}}}(e.bufferSize||64)){let n={};return{send:function(i){let a=[];if(tU(i,(t,r)=>{let i=tJ[r];if(function(e,t,r=Date.now()){return(e[t]||e.all||0)>r}(n,i)){let n=t6(t,r);e.recordDroppedEvent("ratelimit_backoff",i,n)}else a.push(t)}),0===a.length)return ts({});let o=tV(i[0],a),s=t=>{tU(o,(r,n)=>{let i=t6(r,n);e.recordDroppedEvent(t,tJ[n],i)})};return r.add(()=>t({body:function(e){let[t,r]=e,n=JSON.stringify(t);function i(e){"string"==typeof n?n="string"==typeof e?n+e:[tB(n),e]:n.push("string"==typeof e?tB(e):e)}for(let e of r){let[t,r]=e;if(i(`
${JSON.stringify(t)}
`),"string"==typeof r||r instanceof Uint8Array)i(r);else{let e;try{e=JSON.stringify(r)}catch(t){e=JSON.stringify(tF(r))}i(e)}}return"string"==typeof n?n:function(e){let t=new Uint8Array(e.reduce((e,t)=>e+t.length,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}(n)}(o)}).then(e=>(void 0!==e.statusCode&&(e.statusCode<200||e.statusCode>=300)&&eh&&eb.warn(`Sentry responded with status code ${e.statusCode} to sent event.`),n=function(e,{statusCode:t,headers:r},n=Date.now()){let i={...e},a=r&&r["x-sentry-rate-limits"],o=r&&r["retry-after"];if(a)for(let e of a.trim().split(",")){let[t,r,,,a]=e.split(":",5),o=parseInt(t,10),s=1e3*(isNaN(o)?60:o);if(r)for(let e of r.split(";"))"metric_bucket"===e&&a&&!a.split(";").includes("custom")||(i[e]=n+s);else i.all=n+s}else o?i.all=n+function(e,t=Date.now()){let r=parseInt(`${e}`,10);if(!isNaN(r))return 1e3*r;let n=Date.parse(`${e}`);return isNaN(n)?6e4:n-t}(o,n):429===t&&(i.all=n+6e4);return i}(n,e),e),e=>{throw s("network_error"),e})).then(e=>e,e=>{if(e instanceof t1)return eh&&eb.error("Skipped sending event because buffer is full."),s("queue_overflow"),ts({});throw e})},flush:e=>r.drain(e)}}(e,function(i){let a=i.body.length;r+=a,n++;let o={body:i.body,method:"POST",referrerPolicy:"origin",headers:e.headers,keepalive:r<=6e4&&n<15,...e.fetchOptions};if(!t)return rP.fetch=void 0,tc("No fetch implementation available");try{return t(e.url,o).then(e=>(r-=a,n--,{statusCode:e.status,headers:{"x-sentry-rate-limits":e.headers.get("X-Sentry-Rate-Limits"),"retry-after":e.headers.get("Retry-After")}}))}catch(e){return rP.fetch=void 0,r-=a,n--,tc(e)}})}function rj(e,t,r,n){let i={filename:e,function:"<anonymous>"===t?"?":t,in_app:!0};return void 0!==r&&(i.lineno=r),void 0!==n&&(i.colno=n),i}let rR=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,rx=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,rF=/\((\S*)(?::(\d+))(?::(\d+))\)/,rV=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,rU=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,rB=function(...e){let t=e.sort((e,t)=>e[0]-t[0]).map(e=>e[1]);return(e,r=0,n=0)=>{let i=[],a=e.split("\n");for(let e=r;e<a.length;e++){let r=a[e];if(r.length>1024)continue;let o=eS.test(r)?r.replace(eS,"$1"):r;if(!o.match(/\S*Error: /)){for(let e of t){let t=e(o);if(t){i.push(t);break}}if(i.length>=50+n)break}}return function(e){if(!e.length)return[];let t=Array.from(e);return/sentryWrapped/.test(ew(t).function||"")&&t.pop(),t.reverse(),ek.test(ew(t).function||"")&&(t.pop(),ek.test(ew(t).function||"")&&t.pop()),t.slice(0,50).map(e=>({...e,filename:e.filename||ew(t).filename,function:e.function||"?"}))}(i.slice(n))}}([30,e=>{let t=rR.exec(e);if(t){let[,e,r,n]=t;return rj(e,"?",+r,+n)}let r=rx.exec(e);if(r){if(r[2]&&0===r[2].indexOf("eval")){let e=rF.exec(r[2]);e&&(r[2]=e[1],r[3]=e[2],r[4]=e[3])}let[e,t]=rJ(r[1]||"?",r[2]);return rj(t,e,r[3]?+r[3]:void 0,r[4]?+r[4]:void 0)}}],[50,e=>{let t=rV.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){let e=rU.exec(t[3]);e&&(t[1]=t[1]||"eval",t[3]=e[1],t[4]=e[2],t[5]="")}let e=t[3],r=t[1]||"?";return[r,e]=rJ(r,e),rj(e,r,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}}]),rJ=(e,t)=>{let r=-1!==e.indexOf("safari-extension"),n=-1!==e.indexOf("safari-web-extension");return r||n?[-1!==e.indexOf("@")?e.split("@")[0]:"?",r?`safari-extension:${t}`:`safari-web-extension:${t}`]:[e,t]},rY=(e={})=>{let t={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...e};return{name:"Breadcrumbs",setup(e){var r;t.console&&function(e){let t="console";eO(t,e),eI(t,ro)}(function(t){var r;if(tT()!==e)return;let n={category:"console",data:{arguments:t.args,logger:"console"},level:"warn"===(r=t.level)?"warning":["fatal","error","warning","log","info","debug"].includes(r)?r:"log",message:eZ(t.args," ")};if("assert"===t.level){if(!1!==t.args[0])return;n.message=`Assertion failed: ${eZ(t.args.slice(1)," ")||"console.assert"}`,n.data.arguments=t.args.slice(1)}t9(n,{input:t.args,level:t.level})}),t.dom&&(r=t.dom,eO("dom",function(t){if(tT()!==e)return;let n,i,a="object"==typeof r?r.serializeAttribute:void 0,o="object"==typeof r&&"number"==typeof r.maxStringLength?r.maxStringLength:void 0;o&&o>1024&&(rm&&eb.warn(`\`dom.maxStringLength\` cannot exceed 1024, but a value of ${o} was configured. Sentry will use 1024 instead.`),o=1024),"string"==typeof a&&(a=[a]);try{let e=t.event,r=e&&e.target?e.target:e;n=eQ(r,{keyAttrs:a,maxStringLength:o}),i=function(e){if(!ef.HTMLElement)return null;let t=e;for(let e=0;e<5&&t;e++){if(t instanceof HTMLElement){if(t.dataset.sentryComponent)return t.dataset.sentryComponent;if(t.dataset.sentryElement)return t.dataset.sentryElement}t=t.parentNode}return null}(r)}catch(e){n="<unknown>"}if(0===n.length)return;let s={category:`ui.${t.name}`,message:n};i&&(s.data={"ui.component_name":i}),t9(s,{event:t.event,name:t.name,global:t.global})}),eI("dom",rM)),t.xhr&&(eO("xhr",function(t){if(tT()!==e)return;let{startTimestamp:r,endTimestamp:n}=t,i=t.xhr[rL];if(!r||!n||!i)return;let{method:a,url:o,status_code:s,body:c}=i,l={xhr:t.xhr,input:c,startTimestamp:r,endTimestamp:n};t9({category:"xhr",data:{method:a,url:o,status_code:s},type:"http",level:rd(s)},l)}),eI("xhr",rD)),t.fetch&&function(e,t){let r="fetch";eO(r,e),eI(r,()=>(function(e,t=!1){(!t||function(){if("string"==typeof EdgeRuntime)return!0;if(!function(){if(!("fetch"in ef))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch(e){return!1}}())return!1;if(rh(ef.fetch))return!0;let e=!1,t=ef.document;if(t&&"function"==typeof t.createElement)try{let r=t.createElement("iframe");r.hidden=!0,t.head.appendChild(r),r.contentWindow&&r.contentWindow.fetch&&(e=rh(r.contentWindow.fetch)),t.head.removeChild(r)}catch(e){eg&&eb.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",e)}return e}())&&e1(ef,"fetch",function(t){return function(...r){let n=Error(),{method:i,url:a}=function(e){if(0===e.length)return{method:"GET",url:""};if(2===e.length){let[t,r]=e;return{url:rf(t),method:rp(r,"method")?String(r.method).toUpperCase():"GET"}}let t=e[0];return{url:rf(t),method:rp(t,"method")?String(t.method).toUpperCase():"GET"}}(r),o={args:r,fetchData:{method:i,url:a},startTimestamp:1e3*te(),virtualError:n};return e||eP("fetch",{...o}),t.apply(ef,r).then(async t=>(e?e(t):eP("fetch",{...o,endTimestamp:1e3*te(),response:t}),t),e=>{throw eP("fetch",{...o,endTimestamp:1e3*te(),error:e}),eV(e)&&void 0===e.stack&&(e.stack=n.stack,e2(e,"framesToPop",1)),e})}})})(void 0,void 0))}(function(t){if(tT()!==e)return;let{startTimestamp:r,endTimestamp:n}=t;if(n&&(!t.fetchData.url.match(/sentry_key/)||"POST"!==t.fetchData.method)){if(t.error)t9({category:"fetch",data:t.fetchData,level:"error",type:"http"},{data:t.error,input:t.args,startTimestamp:r,endTimestamp:n});else{let e=t.response,i={...t.fetchData,status_code:e&&e.status},a={input:t.args,response:e,startTimestamp:r,endTimestamp:n};t9({category:"fetch",data:i,type:"http",level:rd(i.status_code)},a)}}}),t.history&&rO(function(t){if(tT()!==e)return;let r=t.from,n=t.to,i=ra(ef.location.href),a=r?ra(r):void 0,o=ra(n);a&&a.path||(a=i),i.protocol===o.protocol&&i.host===o.host&&(n=o.relative),i.protocol===a.protocol&&i.host===a.host&&(r=a.relative),t9({category:"navigation",data:{from:r,to:n}})}),t.sentry&&e.on("beforeSendEvent",function(t){tT()===e&&t9({category:"sentry."+("transaction"===t.type?"transaction":"event"),event_id:t.event_id,level:t.level,message:tn(t)},{event:t})})}}},r$=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],rq=(e={})=>{let t={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...e};return{name:"BrowserApiErrors",setupOnce(){t.setTimeout&&e1(ef,"setTimeout",rW),t.setInterval&&e1(ef,"setInterval",rW),t.requestAnimationFrame&&e1(ef,"requestAnimationFrame",rz),t.XMLHttpRequest&&"XMLHttpRequest"in ef&&e1(XMLHttpRequest.prototype,"send",rG);let e=t.eventTarget;e&&(Array.isArray(e)?e:r$).forEach(rH)}}};function rW(e){return function(...t){let r=t[0];return t[0]=rg(r,{mechanism:{data:{function:eT(e)},handled:!1,type:"instrument"}}),e.apply(this,t)}}function rz(e){return function(t){return e.apply(this,[rg(t,{mechanism:{data:{function:"requestAnimationFrame",handler:eT(e)},handled:!1,type:"instrument"}})])}}function rG(e){return function(...t){let r=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(e=>{e in r&&"function"==typeof r[e]&&e1(r,e,function(t){let r={mechanism:{data:{function:e,handler:eT(t)},handled:!1,type:"instrument"}},n=e4(t);return n&&(r.mechanism.data.handler=eT(n)),rg(t,r)})}),e.apply(this,t)}}function rH(e){let t=ef[e],r=t&&t.prototype;r&&r.hasOwnProperty&&r.hasOwnProperty("addEventListener")&&(e1(r,"addEventListener",function(t){return function(r,n,i){try{"function"==typeof n.handleEvent&&(n.handleEvent=rg(n.handleEvent,{mechanism:{data:{function:"handleEvent",handler:eT(n),target:e},handled:!1,type:"instrument"}}))}catch(e){}return t.apply(this,[r,rg(n,{mechanism:{data:{function:"addEventListener",handler:eT(n),target:e},handled:!1,type:"instrument"}}),i])}}),e1(r,"removeEventListener",function(e){return function(t,r,n){try{let i=r.__sentry_wrapped__;i&&e.call(this,t,i,n)}catch(e){}return e.call(this,t,r,n)}}))}let rK=()=>({name:"BrowserSession",setupOnce(){void 0!==ef.document?(tG({ignoreDuration:!0}),tQ(),rO(({from:e,to:t})=>{void 0!==e&&e!==t&&(tG({ignoreDuration:!0}),tQ())})):rm&&eb.warn("Using the `browserSessionIntegration` in non-browser environments is not supported.")}}),rQ=(e={})=>{let t={onerror:!0,onunhandledrejection:!0,...e};return{name:"GlobalHandlers",setupOnce(){Error.stackTraceLimit=50},setup(e){t.onerror&&(function(e){let t="error";eO(t,e),eI(t,eD)}(t=>{let{stackParser:r,attachStacktrace:n}=rZ();if(tT()!==e||rv>0)return;let{msg:i,url:a,line:o,column:s,error:c}=t,l=function(e,t,r,n){let i=e.exception=e.exception||{},a=i.values=i.values||[],o=a[0]=a[0]||{},s=o.stacktrace=o.stacktrace||{},c=s.frames=s.frames||[],l=eY(t)&&t.length>0?t:function(){try{return ef.document.location.href}catch(e){return""}}();return 0===c.length&&c.push({colno:n,filename:l,function:"?",in_app:!0,lineno:r}),e}(rw(r,c||i,void 0,n,!1),a,o,s);l.level="error",tz(l,{originalException:c,mechanism:{handled:!1,type:"onerror"}})}),rX("onerror")),t.onunhandledrejection&&(function(e){let t="unhandledrejection";eO(t,e),eI(t,ej)}(t=>{let{stackParser:r,attachStacktrace:n}=rZ();if(tT()!==e||rv>0)return;let i=function(e){if(eq(e))return e;try{if("reason"in e)return e.reason;if("detail"in e&&"reason"in e.detail)return e.detail.reason}catch(e){}return e}(t),a=eq(i)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(i)}`}]}}:rw(r,i,void 0,n,!0);a.level="error",tz(a,{originalException:i,mechanism:{handled:!1,type:"onunhandledrejection"}})}),rX("onunhandledrejection"))}}};function rX(e){rm&&eb.log(`Global Handler attached: ${e}`)}function rZ(){let e=tT();return e&&e.getOptions()||{stackParser:()=>[],attachStacktrace:!1}}let r0=()=>({name:"HttpContext",preprocessEvent(e){if(!ef.navigator&&!ef.location&&!ef.document)return;let t=e.request&&e.request.url||ef.location&&ef.location.href,{referrer:r}=ef.document||{},{userAgent:n}=ef.navigator||{},i={...e.request&&e.request.headers,...r&&{Referer:r},...n&&{"User-Agent":n}},a={...e.request,...t&&{url:t},headers:i};e.request=a}}),r1=(e={})=>{let t=e.limit||5,r=e.key||"cause";return{name:"LinkedErrors",preprocessEvent(e,n,i){let a=i.getOptions();!function(e,t,r=250,n,i,a,o){if(!(a.exception&&a.exception.values&&o&&eH(o.originalException,Error)))return;let s=a.exception.values.length>0?a.exception.values[a.exception.values.length-1]:void 0;s&&(a.exception.values=(function e(t,r,n,i,a,o,s,c){if(o.length>=n+1)return o;let l=[...o];if(eH(i[a],Error)){rn(s,c);let o=t(r,i[a]),u=l.length;ri(o,a,u,c),l=e(t,r,n,i[a],a,[o,...l],o,u)}return Array.isArray(i.errors)&&i.errors.forEach((i,o)=>{if(eH(i,Error)){rn(s,c);let u=t(r,i),d=l.length;ri(u,`errors[${o}]`,d,c),l=e(t,r,n,i,a,[u,...l],u,d)}}),l})(e,t,i,o.originalException,n,a.exception.values,s,0).map(e=>(e.value&&(e.value=eX(e.value,r)),e)))}(ry,a.stackParser,a.maxValueLength,r,t,e,n)}}};var r2="new",r3="loading",r4="loaded",r5="joining-meeting",r6="joined-meeting",r9="left-meeting",r8="error",r7="blocked",ne="off",nt="sendable",nr="loading",nn="interrupted",ni="playable",na="unknown",no="full",ns="lobby",nc="none",nl="base",nu="*",nd="ejected",nh="nbf-room",np="nbf-token",nf="exp-room",nv="exp-token",ng="no-room",nm="meeting-full",ny="end-of-life",n_="not-allowed",nb="connection-error",nS="cam-in-use",nk="mic-in-use",nw="cam-mic-in-use",nE="permissions",nT="undefined-mediadevices",nC="not-found",nM="constraints",nA="unknown",nO="iframe-ready-for-launch-config",nI="iframe-launch-config",nP="theme-updated",nL="loading",nD="load-attempt-failed",nN="loaded",nj="started-camera",nR="camera-error",nx="joining-meeting",nF="joined-meeting",nV="left-meeting",nU="participant-joined",nB="participant-updated",nJ="participant-left",nY="participant-counts-updated",n$="access-state-updated",nq="meeting-session-summary-updated",nW="meeting-session-state-updated",nz="meeting-session-data-error",nG="waiting-participant-added",nH="waiting-participant-updated",nK="waiting-participant-removed",nQ="track-started",nX="track-stopped",nZ="transcription-started",n0="transcription-stopped",n1="transcription-error",n2="recording-started",n3="recording-stopped",n4="recording-stats",n5="recording-error",n6="recording-upload-completed",n9="recording-data",n8="app-message",n7="transcription-message",ie="remote-media-player-started",it="remote-media-player-updated",ir="remote-media-player-stopped",ii="local-screen-share-started",ia="local-screen-share-stopped",io="local-screen-share-canceled",is="active-speaker-change",ic="active-speaker-mode-change",il="network-quality-change",iu="network-connection",id="cpu-load-change",ih="face-counts-updated",ip="fullscreen",iv="exited-fullscreen",ig="live-streaming-started",im="live-streaming-updated",iy="live-streaming-stopped",i_="live-streaming-error",ib="lang-updated",iS="receive-settings-updated",ik="input-settings-updated",iw="nonfatal-error",iE="error",iT="iframe-call-message",iC="local-screen-start",iM="daily-method-update-live-streaming-endpoints",iA="transmit-log",iO="daily-custom-track",iI={NONE:"none",BGBLUR:"background-blur",BGIMAGE:"background-image",FACE_DETECTION:"face-detection"},iP={NONE:"none",NOISE_CANCELLATION:"noise-cancellation"},iL={PLAY:"play",PAUSE:"pause"},iD=["jpg","png","jpeg"],iN="sip-call-transfer";function ij(){return!iR()&&"undefined"!=typeof window&&window.navigator&&window.navigator.userAgent?window.navigator.userAgent:""}function iR(){return"undefined"!=typeof navigator&&navigator.product&&"ReactNative"===navigator.product}function ix(){return navigator&&navigator.mediaDevices&&navigator.mediaDevices.getUserMedia}function iF(){if(iR()||!document)return!1;var e=document.createElement("iframe");return!!e.requestFullscreen||!!e.webkitRequestFullscreen}var iV="none",iU=function(){try{var e,t=document.createElement("canvas"),r=!1;(e=t.getContext("webgl2",{failIfMajorPerformanceCaveat:!0}))||(r=!0,e=t.getContext("webgl2"));var n=null!=e;return t.remove(),n?r?"software":"hardware":iV}catch(e){return iV}}();function iB(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return!iR()&&iU!==iV&&(e?!iq()&&["Chrome","Firefox"].includes(iW()):function(){if(iq())return!1;var e=iW();if("Safari"===e){var t=iQ();if(t.major<15||15===t.major&&t.minor<4)return!1}return"Chrome"===e?iG().major>=77:"Firefox"===e?iX().major>=97:["Chrome","Firefox","Safari"].includes(e)}())}function iJ(){if(iR()||i$()||"undefined"==typeof AudioWorkletNode)return!1;switch(iW()){case"Chrome":case"Firefox":return!0;case"Safari":var e=iz();return e.major>17||17===e.major&&e.minor>=4}return!1}function iY(){return ix()&&!function(){var e,t=iW();if(!ij())return!0;switch(t){case"Chrome":return(e=iG()).major&&e.major>0&&e.major<75;case"Firefox":return(e=iX()).major<91;case"Safari":return(e=iQ()).major<13||13===e.major&&e.minor<1;default:return!0}}()}function i$(){return ij().match(/Linux; Android/)}function iq(){var e,t=ij(),r=t.match(/Mac/)&&(!iR()&&"undefined"!=typeof window&&null!==(e=window)&&void 0!==e&&null!==(e=e.navigator)&&void 0!==e&&e.maxTouchPoints?window.navigator.maxTouchPoints:0)>=5;return!!(t.match(/Mobi/)||t.match(/Android/)||r)||!!ij().match(/DailyAnd\//)||void 0}function iW(){if("undefined"!=typeof window){var e=ij();return iH()?"Safari":e.indexOf("Edge")>-1?"Edge":e.match(/Chrome\//)?"Chrome":e.indexOf("Safari")>-1||iK()?"Safari":e.indexOf("Firefox")>-1?"Firefox":e.indexOf("MSIE")>-1||e.indexOf(".NET")>-1?"IE":"Unknown Browser"}}function iz(){switch(iW()){case"Chrome":return iG();case"Safari":return iQ();case"Firefox":return iX();case"Edge":return function(){var e=0,t=0;if("undefined"!=typeof window){var r=ij().match(/Edge\/(\d+).(\d+)/);if(r)try{e=parseInt(r[1]),t=parseInt(r[2])}catch(e){}}return{major:e,minor:t}}()}}function iG(){var e=0,t=0,r=0,n=0,i=!1;if("undefined"!=typeof window){var a=ij(),o=a.match(/Chrome\/(\d+).(\d+).(\d+).(\d+)/);if(o)try{e=parseInt(o[1]),t=parseInt(o[2]),r=parseInt(o[3]),n=parseInt(o[4]),i=a.indexOf("OPR/")>-1}catch(e){}}return{major:e,minor:t,build:r,patch:n,opera:i}}function iH(){return!!ij().match(/iPad|iPhone|iPod/i)&&ix()}function iK(){return ij().indexOf("AppleWebKit/605.1.15")>-1}function iQ(){var e=0,t=0,r=0;if("undefined"!=typeof window){var n=ij().match(/Version\/(\d+).(\d+)(.(\d+))?/);if(n)try{e=parseInt(n[1]),t=parseInt(n[2]),r=parseInt(n[4])}catch(e){}else(iH()||iK())&&(e=14,t=0,r=3)}return{major:e,minor:t,point:r}}function iX(){var e=0,t=0;if("undefined"!=typeof window){var r=ij().match(/Firefox\/(\d+).(\d+)/);if(r)try{e=parseInt(r[1]),t=parseInt(r[2])}catch(e){}}return{major:e,minor:t}}var iZ=g(function e(){h(this,e)},[{key:"addListenerForMessagesFromCallMachine",value:function(e,t,r){ec()}},{key:"addListenerForMessagesFromDailyJs",value:function(e,t,r){ec()}},{key:"sendMessageToCallMachine",value:function(e,t,r,n){ec()}},{key:"sendMessageToDailyJs",value:function(e,t){ec()}},{key:"removeListener",value:function(e){ec()}}]);function i0(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i1(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i0(Object(r),!0).forEach(function(t){S(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i0(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function i2(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(i2=function(){return!!e})()}var i3=function(){function e(){var t,r,n;return h(this,e),r=y(r=e),(t=m(this,i2()?Reflect.construct(r,n||[],y(this).constructor):r.apply(this,n)))._wrappedListeners={},t._messageCallbacks={},t}return b(e,iZ),g(e,[{key:"addListenerForMessagesFromCallMachine",value:function(e,t,r){var n=this,i=function(i){if(i.data&&"iframe-call-message"===i.data.what&&(!i.data.callClientId||i.data.callClientId===t)&&(!i.data.from||"module"!==i.data.from)){var a=i1({},i.data);if(delete a.from,a.callbackStamp&&n._messageCallbacks[a.callbackStamp]){var o=a.callbackStamp;n._messageCallbacks[o].call(r,a),delete n._messageCallbacks[o]}delete a.what,delete a.callbackStamp,e.call(r,a)}};this._wrappedListeners[e]=i,window.addEventListener("message",i)}},{key:"addListenerForMessagesFromDailyJs",value:function(e,t,r){var n=function(n){var i;if(!(!n.data||n.data.what!==iT||!n.data.action||n.data.from&&"module"!==n.data.from||n.data.callClientId&&t&&n.data.callClientId!==t||null!=n&&null!==(i=n.data)&&void 0!==i&&i.callFrameId)){var a=n.data;e.call(r,a)}};this._wrappedListeners[e]=n,window.addEventListener("message",n)}},{key:"sendMessageToCallMachine",value:function(e,t,r,n){if(!r)throw Error("undefined callClientId. Are you trying to use a DailyCall instance previously destroyed?");var i=i1({},e);if(i.what=iT,i.from="module",i.callClientId=r,t){var a=es();this._messageCallbacks[a]=t,i.callbackStamp=a}var o=n?n.contentWindow:window,s=this._callMachineTargetOrigin(n);s&&o.postMessage(i,s)}},{key:"sendMessageToDailyJs",value:function(e,t){e.what=iT,e.callClientId=t,e.from="embedded",window.postMessage(e,this._targetOriginFromWindowLocation())}},{key:"removeListener",value:function(e){var t=this._wrappedListeners[e];t&&(window.removeEventListener("message",t),delete this._wrappedListeners[e])}},{key:"forwardPackagedMessageToCallMachine",value:function(e,t,r){var n=i1({},e);n.callClientId=r;var i=t?t.contentWindow:window,a=this._callMachineTargetOrigin(t);a&&i.postMessage(n,a)}},{key:"addListenerForPackagedMessagesFromCallMachine",value:function(e,t){var r=function(r){!r.data||"iframe-call-message"!==r.data.what||r.data.callClientId&&r.data.callClientId!==t||r.data.from&&"module"===r.data.from||e(r.data)};return this._wrappedListeners[e]=r,window.addEventListener("message",r),e}},{key:"removeListenerForPackagedMessagesFromCallMachine",value:function(e){var t=this._wrappedListeners[e];t&&(window.removeEventListener("message",t),delete this._wrappedListeners[e])}},{key:"_callMachineTargetOrigin",value:function(e){return e?e.src?new URL(e.src).origin:void 0:this._targetOriginFromWindowLocation()}},{key:"_targetOriginFromWindowLocation",value:function(){return"file:"===window.location.protocol?"*":window.location.origin}}])}();function i4(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i5(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(i5=function(){return!!e})()}var i6=function(){function e(){var t,n,i;return h(this,e),n=y(n=e),t=m(this,i5()?Reflect.construct(n,i||[],y(this).constructor):n.apply(this,i)),r.g.callMachineToDailyJsEmitter=r.g.callMachineToDailyJsEmitter||new Y.EventEmitter,r.g.dailyJsToCallMachineEmitter=r.g.dailyJsToCallMachineEmitter||new Y.EventEmitter,t._wrappedListeners={},t._messageCallbacks={},t}return b(e,iZ),g(e,[{key:"addListenerForMessagesFromCallMachine",value:function(e,t,n){this._addListener(e,r.g.callMachineToDailyJsEmitter,t,n,"received call machine message")}},{key:"addListenerForMessagesFromDailyJs",value:function(e,t,n){this._addListener(e,r.g.dailyJsToCallMachineEmitter,t,n,"received daily-js message")}},{key:"sendMessageToCallMachine",value:function(e,t,n){this._sendMessage(e,r.g.dailyJsToCallMachineEmitter,n,t,"sending message to call machine")}},{key:"sendMessageToDailyJs",value:function(e,t){this._sendMessage(e,r.g.callMachineToDailyJsEmitter,t,null,"sending message to daily-js")}},{key:"removeListener",value:function(e){var t=this._wrappedListeners[e];t&&(r.g.callMachineToDailyJsEmitter.removeListener("message",t),r.g.dailyJsToCallMachineEmitter.removeListener("message",t),delete this._wrappedListeners[e])}},{key:"_addListener",value:function(e,t,r,n,i){var a=this,o=function(t){if(t.callClientId===r){if(t.callbackStamp&&a._messageCallbacks[t.callbackStamp]){var i=t.callbackStamp;a._messageCallbacks[i].call(n,t),delete a._messageCallbacks[i]}e.call(n,t)}};this._wrappedListeners[e]=o,t.addListener("message",o)}},{key:"_sendMessage",value:function(e,t,r,n,i){var a=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i4(Object(r),!0).forEach(function(t){S(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i4(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);if(a.callClientId=r,n){var o=es();this._messageCallbacks[o]=n,a.callbackStamp=o}t.emit("message",a)}}])}(),i9="replace",i8="shallow-merge",i7=[i9,i8],ae=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.data,n=t.mergeStrategy,i=void 0===n?i9:n;h(this,e),e._validateMergeStrategy(i),e._validateData(r,i),this.mergeStrategy=i,this.data=r}return g(e,[{key:"isNoOp",value:function(){return e.isNoOpUpdate(this.data,this.mergeStrategy)}}],[{key:"isNoOpUpdate",value:function(e,t){return 0===Object.keys(e).length&&t===i8}},{key:"_validateMergeStrategy",value:function(e){if(!i7.includes(e))throw Error("Unrecognized mergeStrategy provided. Options are: [".concat(i7,"]"))}},{key:"_validateData",value:function(e,t){if(!function(e){if(null==e||"object"!==p(e))return!1;var t=Object.getPrototypeOf(e);return null==t||t===Object.prototype}(e))throw Error("Meeting session data must be a plain (map-like) object");try{if(r=JSON.stringify(e),t===i9){var r,n=JSON.parse(r);z(n,e)||console.warn("The meeting session data provided will be modified when serialized.",n,e)}else if(t===i8){for(var i in e)if(Object.hasOwnProperty.call(e,i)&&void 0!==e[i]){var a=JSON.parse(JSON.stringify(e[i]));z(e[i],a)||console.warn("At least one key in the meeting session data provided will be modified when serialized.",a,e[i])}}}catch(e){throw Error("Meeting session data must be serializable to JSON: ".concat(e))}if(r.length>102400)throw Error("Meeting session data is too large (".concat(r.length," characters). Maximum size suppported is ").concat(102400,"."))}}])}();function at(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(at=function(){return!!e})()}function ar(e){var t="function"==typeof Map?new Map:void 0;return(ar=function(e){if(null===e||!function(e){try{return -1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return function(e,t,r){if(at())return Reflect.construct.apply(null,arguments);var n=[null];n.push.apply(n,t);var i=new(e.bind.apply(e,n));return r&&_(i,r.prototype),i}(e,arguments,y(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),_(r,e)})(e)}function an(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(an=function(){return!!e})()}function ai(e){var t,r=null===(t=window._daily)||void 0===t?void 0:t.pendings;if(r){var n=r.indexOf(e);-1!==n&&r.splice(n,1)}}var aa=g(function e(t){h(this,e),this._currentLoad=null,this._callClientId=t},[{key:"load",value:function(){var e,t=this,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0,i=arguments.length>2?arguments[2]:void 0;if(this.loaded)return window._daily.instances[this._callClientId].callMachine.reset(),void n(!0);e=this._callClientId,window._daily.pendings.push(e),this._currentLoad&&this._currentLoad.cancel(),this._currentLoad=new ao(r,function(){n(!1)},function(e,r){r||ai(t._callClientId),i(e,r)}),this._currentLoad.start()}},{key:"cancel",value:function(){this._currentLoad&&this._currentLoad.cancel(),ai(this._callClientId)}},{key:"loaded",get:function(){return this._currentLoad&&this._currentLoad.succeeded}}]),ao=g(function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;h(this,e),this._attemptsRemaining=3,this._currentAttempt=null,this._dailyConfig=t,this._successCallback=r,this._failureCallback=n},[{key:"start",value:function(){var e=this;if(!this._currentAttempt){var t=function(r){e._currentAttempt.cancelled||(e._attemptsRemaining--,e._failureCallback(r,e._attemptsRemaining>0),e._attemptsRemaining<=0||setTimeout(function(){e._currentAttempt.cancelled||(e._currentAttempt=new ac(e._dailyConfig,e._successCallback,t),e._currentAttempt.start())},3e3))};this._currentAttempt=new ac(this._dailyConfig,this._successCallback,t),this._currentAttempt.start()}}},{key:"cancel",value:function(){this._currentAttempt&&this._currentAttempt.cancel()}},{key:"cancelled",get:function(){return this._currentAttempt&&this._currentAttempt.cancelled}},{key:"succeeded",get:function(){return this._currentAttempt&&this._currentAttempt.succeeded}}]),as=function(){function e(){var t,r;return h(this,e),r=arguments,t=y(t=e),m(this,an()?Reflect.construct(t,r||[],y(this).constructor):t.apply(this,r))}return b(e,ar(Error)),g(e)}(),ac=function(){var e;return g(function e(t,r,n){h(this,e),this._loadAttemptImpl=iR()||!t.avoidEval?new al(t,r,n):new au(t,r,n)},[{key:"start",value:(e=w(function*(){return this._loadAttemptImpl.start()}),function(){return e.apply(this,arguments)})},{key:"cancel",value:function(){this._loadAttemptImpl.cancel()}},{key:"cancelled",get:function(){return this._loadAttemptImpl.cancelled}},{key:"succeeded",get:function(){return this._loadAttemptImpl.succeeded}}])}(),al=function(){var e,t,r,n;return g(function e(t,r,n){h(this,e),this.cancelled=!1,this.succeeded=!1,this._networkTimedOut=!1,this._networkTimeout=null,this._iosCache="undefined"!=typeof iOSCallObjectBundleCache&&iOSCallObjectBundleCache,this._refetchHeaders=null,this._dailyConfig=t,this._successCallback=r,this._failureCallback=n},[{key:"start",value:(n=w(function*(){var e=eu(this._dailyConfig);(yield this._tryLoadFromIOSCache(e))||this._loadFromNetwork(e)}),function(){return n.apply(this,arguments)})},{key:"cancel",value:function(){clearTimeout(this._networkTimeout),this.cancelled=!0}},{key:"_tryLoadFromIOSCache",value:(r=w(function*(e){if(!this._iosCache)return!1;try{var t=yield this._iosCache.get(e);return!!this.cancelled||!!t&&(t.code?(Function('"use strict";'+t.code)(),this.succeeded=!0,this._successCallback(),!0):(this._refetchHeaders=t.refetchHeaders,!1))}catch(e){return!1}}),function(e){return r.apply(this,arguments)})},{key:"_loadFromNetwork",value:(t=w(function*(e){var t=this;this._networkTimeout=setTimeout(function(){t._networkTimedOut=!0,t._failureCallback({msg:"Timed out (>".concat(2e4," ms) when loading call object bundle ").concat(e),type:"timeout"})},2e4);try{var r=this._refetchHeaders?{headers:this._refetchHeaders}:{},n=yield fetch(e,r);if(clearTimeout(this._networkTimeout),this.cancelled||this._networkTimedOut)throw new as;var i=yield this._getBundleCodeFromResponse(e,n);if(this.cancelled)throw new as;Function('"use strict";'+i)(),this._iosCache&&this._iosCache.set(e,i,n.headers),this.succeeded=!0,this._successCallback()}catch(t){if(clearTimeout(this._networkTimeout),t instanceof as||this.cancelled||this._networkTimedOut)return;this._failureCallback({msg:"Failed to load call object bundle ".concat(e,": ").concat(t),type:t.message})}}),function(e){return t.apply(this,arguments)})},{key:"_getBundleCodeFromResponse",value:(e=w(function*(e,t){if(t.ok)return yield t.text();if(this._iosCache&&304===t.status)return(yield this._iosCache.renew(e,t.headers)).code;throw Error("Received ".concat(t.status," response"))}),function(t,r){return e.apply(this,arguments)})}])}(),au=g(function e(t,r,n){h(this,e),this.cancelled=!1,this.succeeded=!1,this._dailyConfig=t,this._successCallback=r,this._failureCallback=n,this._attemptId=es(),this._networkTimeout=null,this._scriptElement=null},[{key:"start",value:function(){window._dailyCallMachineLoadWaitlist||(window._dailyCallMachineLoadWaitlist=new Set);var e=eu(this._dailyConfig);"object"===("undefined"==typeof document?"undefined":p(document))?this._startLoading(e):this._failureCallback({msg:"Call object bundle must be loaded in a DOM/web context",type:"missing context"})}},{key:"cancel",value:function(){this._stopLoading(),this.cancelled=!0}},{key:"_startLoading",value:function(e){var t=this;this._signUpForCallMachineLoadWaitlist(),this._networkTimeout=setTimeout(function(){t._stopLoading(),t._failureCallback({msg:"Timed out (>".concat(2e4," ms) when loading call object bundle ").concat(e),type:"timeout"})},2e4);var r=document.getElementsByTagName("head")[0],n=document.createElement("script");this._scriptElement=n,n.onload=function(){t._stopLoading(),t.succeeded=!0,t._successCallback()},n.onerror=function(e){t._stopLoading(),t._failureCallback({msg:"Failed to load call object bundle ".concat(e.target.src),type:e.message})},n.src=e,r.appendChild(n)}},{key:"_stopLoading",value:function(){this._withdrawFromCallMachineLoadWaitlist(),clearTimeout(this._networkTimeout),this._scriptElement&&(this._scriptElement.onload=null,this._scriptElement.onerror=null)}},{key:"_signUpForCallMachineLoadWaitlist",value:function(){window._dailyCallMachineLoadWaitlist.add(this._attemptId)}},{key:"_withdrawFromCallMachineLoadWaitlist",value:function(){window._dailyCallMachineLoadWaitlist.delete(this._attemptId)}}]),ad=function(e,t,r){return!0===ap(e.local,t,r)},ah=function(e,t,r,n){var i=af(e,t,r,n);return i&&i.pendingTrack},ap=function(e,t,r){if(!e)return!1;var n=function(e){switch(e){case"avatar":return!0;case"staged":return e;default:return!!e}},i=e.public.subscribedTracks;return i&&i[t]?n(-1===["cam-audio","cam-video","screen-video","screen-audio","rmpAudio","rmpVideo"].indexOf(r)&&i[t].custom?[!0,"staged"].includes(i[t].custom)?i[t].custom:i[t].custom[r]:i[t][r]):!i||n(i.ALL)},af=function(e,t,r,n){var i=Object.values(e.streams||{}).filter(function(e){return e.participantId===t&&e.type===r&&e.pendingTrack&&e.pendingTrack.kind===n}).sort(function(e,t){return new Date(t.starttime)-new Date(e.starttime)});return i&&i[0]},av=function(e,t){var r=e.local.public.customTracks;if(r&&r[t])return r[t].track};function ag(e,t){for(var r=t.getState(),n=0,i=["cam","screen"];n<i.length;n++)for(var a=i[n],o=0,s=["video","audio"];o<s.length;o++){var c=s[o],l="cam"===a?c:"screen".concat(c.charAt(0).toUpperCase()+c.slice(1)),u=e.tracks[l];if(u){var d=e.local?r.local.streams&&r.local.streams[a]&&r.local.streams[a].stream&&r.local.streams[a].stream["get".concat("video"===c?"Video":"Audio","Tracks")]()[0]:ah(r,e.session_id,a,c);"playable"===u.state&&(u.track=d),u.persistentTrack=d}}}function am(e,t){try{var r,n=t.getState();for(var i in e.tracks){if(r=i,!["video","audio","screenVideo","screenAudio"].includes(r)){var a=e.tracks[i].kind;if(a){var o=e.tracks[i];if(o){var s=e.local?av(n,i):ah(n,e.session_id,i,a);"playable"===o.state&&(e.tracks[i].track=s),o.persistentTrack=s}}else console.error("unknown type for custom track")}}}catch(e){console.error(e)}}function ay(e,t,r){var n=r.getState();if(e.local){if(e.audio)try{e.audioTrack=n.local.streams.cam.stream.getAudioTracks()[0],e.audioTrack||(e.audio=!1)}catch(e){}if(e.video)try{e.videoTrack=n.local.streams.cam.stream.getVideoTracks()[0],e.videoTrack||(e.video=!1)}catch(e){}if(e.screen)try{e.screenVideoTrack=n.local.streams.screen.stream.getVideoTracks()[0],e.screenAudioTrack=n.local.streams.screen.stream.getAudioTracks()[0],e.screenVideoTrack||e.screenAudioTrack||(e.screen=!1)}catch(e){}}else{var i=!0;try{var a=n.participants[e.session_id];a&&a.public&&a.public.rtcType&&"peer-to-peer"===a.public.rtcType.impl&&a.private&&!["connected","completed"].includes(a.private.peeringState)&&(i=!1)}catch(e){console.error(e)}if(!i)return e.audio=!1,e.audioTrack=!1,e.video=!1,e.videoTrack=!1,e.screen=!1,void(e.screenTrack=!1);try{if(n.streams,e.audio&&ad(n,e.session_id,"cam-audio")){var o=ah(n,e.session_id,"cam","audio");o&&(t&&t.audioTrack&&t.audioTrack.id===o.id?e.audioTrack=o:o.muted||(e.audioTrack=o)),e.audioTrack||(e.audio=!1)}if(e.video&&ad(n,e.session_id,"cam-video")){var s=ah(n,e.session_id,"cam","video");s&&(t&&t.videoTrack&&t.videoTrack.id===s.id?e.videoTrack=s:s.muted||(e.videoTrack=s)),e.videoTrack||(e.video=!1)}if(e.screen&&ad(n,e.session_id,"screen-audio")){var c=ah(n,e.session_id,"screen","audio");c&&(t&&t.screenAudioTrack&&t.screenAudioTrack.id===c.id?e.screenAudioTrack=c:c.muted||(e.screenAudioTrack=c))}if(e.screen&&ad(n,e.session_id,"screen-video")){var l=ah(n,e.session_id,"screen","video");l&&(t&&t.screenVideoTrack&&t.screenVideoTrack.id===l.id?e.screenVideoTrack=l:l.muted||(e.screenVideoTrack=l))}e.screenVideoTrack||e.screenAudioTrack||(e.screen=!1)}catch(e){console.error("unexpected error matching up tracks",e)}}}function a_(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var ab=new Map,aS=null;function ak(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var aw=new Map,aE=null;function aT(){var e;return iR()||void 0!==(null===(e=navigator.mediaDevices)||void 0===e?void 0:e.ondevicechange)}var aC=new Set;function aM(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aA(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aM(Object(r),!0).forEach(function(t){S(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aM(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var aO=Object.freeze({VIDEO:"video",AUDIO:"audio",SCREEN_VIDEO:"screenVideo",SCREEN_AUDIO:"screenAudio",CUSTOM_VIDEO:"customVideo",CUSTOM_AUDIO:"customAudio"}),aI=Object.freeze({PARTICIPANTS:"participants",STREAMING:"streaming",TRANSCRIPTION:"transcription"}),aP=Object.values(aO),aL=["v","a","sv","sa","cv","ca"];Object.freeze(aP.reduce(function(e,t,r){return e[t]=aL[r],e},{})),Object.freeze(aL.reduce(function(e,t,r){return e[t]=aP[r],e},{}));var aD=[aO.VIDEO,aO.AUDIO,aO.SCREEN_VIDEO,aO.SCREEN_AUDIO],aN=Object.values(aI),aj=["p","s","t"];Object.freeze(aN.reduce(function(e,t,r){return e[t]=aj[r],e},{})),Object.freeze(aj.reduce(function(e,t,r){return e[t]=aN[r],e},{}));var aR=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.base,n=t.byUserId,i=t.byParticipantId;h(this,e),this.base=r,this.byUserId=n,this.byParticipantId=i}return g(e,[{key:"clone",value:function(){var t=new e;if(this.base instanceof ax?t.base=this.base.clone():t.base=this.base,void 0!==this.byUserId)for(var r in t.byUserId={},this.byUserId){var n=this.byUserId[r];t.byUserId[r]=n instanceof ax?n.clone():n}if(void 0!==this.byParticipantId)for(var i in t.byParticipantId={},this.byParticipantId){var a=this.byParticipantId[i];t.byParticipantId[i]=a instanceof ax?a.clone():a}return t}},{key:"toJSONObject",value:function(){var e={};if("boolean"==typeof this.base?e.base=this.base:this.base instanceof ax&&(e.base=this.base.toJSONObject()),void 0!==this.byUserId)for(var t in e.byUserId={},this.byUserId){var r=this.byUserId[t];e.byUserId[t]=r instanceof ax?r.toJSONObject():r}if(void 0!==this.byParticipantId)for(var n in e.byParticipantId={},this.byParticipantId){var i=this.byParticipantId[n];e.byParticipantId[n]=i instanceof ax?i.toJSONObject():i}return e}},{key:"toMinifiedJSONObject",value:function(){var e={};if(void 0!==this.base&&("boolean"==typeof this.base?e.b=this.base:e.b=this.base.toMinifiedJSONObject()),void 0!==this.byUserId)for(var t in e.u={},this.byUserId){var r=this.byUserId[t];e.u[t]="boolean"==typeof r?r:r.toMinifiedJSONObject()}if(void 0!==this.byParticipantId)for(var n in e.p={},this.byParticipantId){var i=this.byParticipantId[n];e.p[n]="boolean"==typeof i?i:i.toMinifiedJSONObject()}return e}},{key:"normalize",value:function(){return this.base instanceof ax&&(this.base=this.base.normalize()),this.byUserId&&(this.byUserId=Object.fromEntries(Object.entries(this.byUserId).map(function(e){var t=T(e,2),r=t[0],n=t[1];return[r,n instanceof ax?n.normalize():n]}))),this.byParticipantId&&(this.byParticipantId=Object.fromEntries(Object.entries(this.byParticipantId).map(function(e){var t=T(e,2),r=t[0],n=t[1];return[r,n instanceof ax?n.normalize():n]}))),this}}],[{key:"fromJSONObject",value:function(t){var r,n,i;if(void 0!==t.base&&(r="boolean"==typeof t.base?t.base:ax.fromJSONObject(t.base)),void 0!==t.byUserId)for(var a in n={},t.byUserId){var o=t.byUserId[a];n[a]="boolean"==typeof o?o:ax.fromJSONObject(o)}if(void 0!==t.byParticipantId)for(var s in i={},t.byParticipantId){var c=t.byParticipantId[s];i[s]="boolean"==typeof c?c:ax.fromJSONObject(c)}return new e({base:r,byUserId:n,byParticipantId:i})}},{key:"fromMinifiedJSONObject",value:function(t){var r,n,i;if(void 0!==t.b&&(r="boolean"==typeof t.b?t.b:ax.fromMinifiedJSONObject(t.b)),void 0!==t.u)for(var a in n={},t.u){var o=t.u[a];n[a]="boolean"==typeof o?o:ax.fromMinifiedJSONObject(o)}if(void 0!==t.p)for(var s in i={},t.p){var c=t.p[s];i[s]="boolean"==typeof c?c:ax.fromMinifiedJSONObject(c)}return new e({base:r,byUserId:n,byParticipantId:i})}},{key:"validateJSONObject",value:function(e){if("object"!==p(e))return[!1,"canReceive must be an object"];for(var t=["base","byUserId","byParticipantId"],r=0,n=Object.keys(e);r<n.length;r++){var i=n[r];if(!t.includes(i))return[!1,"canReceive can only contain keys (".concat(t.join(", "),")")];if("base"===i){var a=T(ax.validateJSONObject(e.base,!0),2),o=a[0],s=a[1];if(!o)return[!1,s]}else{if("object"!==p(e[i]))return[!1,"invalid (non-object) value for field '".concat(i,"' in canReceive")];for(var c=0,l=Object.values(e[i]);c<l.length;c++){var u=l[c],d=T(ax.validateJSONObject(u),2),h=d[0],f=d[1];if(!h)return[!1,f]}}}return[!0]}}])}(),ax=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.video,n=t.audio,i=t.screenVideo,a=t.screenAudio,o=t.customVideo,s=t.customAudio;h(this,e),this.video=r,this.audio=n,this.screenVideo=i,this.screenAudio=a,this.customVideo=o,this.customAudio=s}return g(e,[{key:"clone",value:function(){var t=new e;return void 0!==this.video&&(t.video=this.video),void 0!==this.audio&&(t.audio=this.audio),void 0!==this.screenVideo&&(t.screenVideo=this.screenVideo),void 0!==this.screenAudio&&(t.screenAudio=this.screenAudio),void 0!==this.customVideo&&(t.customVideo=aA({},this.customVideo)),void 0!==this.customAudio&&(t.customAudio=aA({},this.customAudio)),t}},{key:"toJSONObject",value:function(){var e={};return void 0!==this.video&&(e.video=this.video),void 0!==this.audio&&(e.audio=this.audio),void 0!==this.screenVideo&&(e.screenVideo=this.screenVideo),void 0!==this.screenAudio&&(e.screenAudio=this.screenAudio),void 0!==this.customVideo&&(e.customVideo=aA({},this.customVideo)),void 0!==this.customAudio&&(e.customAudio=aA({},this.customAudio)),e}},{key:"toMinifiedJSONObject",value:function(){var e={};return void 0!==this.video&&(e.v=this.video),void 0!==this.audio&&(e.a=this.audio),void 0!==this.screenVideo&&(e.sv=this.screenVideo),void 0!==this.screenAudio&&(e.sa=this.screenAudio),void 0!==this.customVideo&&(e.cv=aA({},this.customVideo)),void 0!==this.customAudio&&(e.ca=aA({},this.customAudio)),e}},{key:"normalize",value:function(){function e(e,t){return e&&1===Object.keys(e).length&&e["*"]===t}return!(!0!==this.video||!0!==this.audio||!0!==this.screenVideo||!0!==this.screenAudio||!e(this.customVideo,!0)||!e(this.customAudio,!0))||(!1!==this.video||!1!==this.audio||!1!==this.screenVideo||!1!==this.screenAudio||!e(this.customVideo,!1)||!e(this.customAudio,!1))&&this}}],[{key:"fromBoolean",value:function(t){return new e({video:t,audio:t,screenVideo:t,screenAudio:t,customVideo:{"*":t},customAudio:{"*":t}})}},{key:"fromJSONObject",value:function(t){return new e({video:t.video,audio:t.audio,screenVideo:t.screenVideo,screenAudio:t.screenAudio,customVideo:void 0!==t.customVideo?aA({},t.customVideo):void 0,customAudio:void 0!==t.customAudio?aA({},t.customAudio):void 0})}},{key:"fromMinifiedJSONObject",value:function(t){return new e({video:t.v,audio:t.a,screenVideo:t.sv,screenAudio:t.sa,customVideo:t.cv,customAudio:t.ca})}},{key:"validateJSONObject",value:function(e,t){if("boolean"==typeof e)return[!0];if("object"!==p(e))return[!1,"invalid (non-object, non-boolean) value in canReceive"];for(var r=Object.keys(e),n=0;n<r.length;n++){var i=r[n];if(!aP.includes(i))return[!1,"invalid media type '".concat(i,"' in canReceive")];if(aD.includes(i)){if("boolean"!=typeof e[i])return[!1,"invalid (non-boolean) value for media type '".concat(i,"' in canReceive")]}else{if("object"!==p(e[i]))return[!1,"invalid (non-object) value for media type '".concat(i,"' in canReceive")];for(var a=0,o=Object.values(e[i]);a<o.length;a++)if("boolean"!=typeof o[a])return[!1,"invalid (non-boolean) value for entry within '".concat(i,"' in canReceive")];if(t&&void 0===e[i]["*"])return[!1,'canReceive "base" permission must specify "*" as an entry within \''.concat(i,"'")]}}return t&&r.length!==aP.length?[!1,'canReceive "base" permission must specify all media types: '.concat(aP.join(", ")," (or be set to a boolean shorthand)")]:[!0]}}])}(),aF=["result"],aV=["preserveIframe"];function aU(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aB(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aU(Object(r),!0).forEach(function(t){S(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aU(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function aJ(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(aJ=function(){return!!e})()}function aY(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return a$(e,void 0);var r=({}).toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?a$(e,void 0):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==r.return||r.return()}finally{if(s)throw a}}}}function a$(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var aq={},aW="video",az=iR()?{data:{}}:{data:{},topology:"none"},aG={present:0,hidden:0},aH={maxBitrate:{min:1e5,max:25e5},maxFramerate:{min:1,max:30},scaleResolutionDownBy:{min:1,max:8}},aK=Object.keys(aH),aQ=["state","volume","simulcastEncodings"],aX={androidInCallNotification:{title:"string",subtitle:"string",iconName:"string",disableForCustomOverride:"boolean"},disableAutoDeviceManagement:{audio:"boolean",video:"boolean"}},aZ={id:{iconPath:"string",iconPathDarkMode:"string",label:"string",tooltip:"string",visualState:"'default' | 'sidebar-open' | 'active'"}},a0={id:{allow:"string",controlledBy:"'*' | 'owners' | string[]",csp:"string",iconURL:"string",label:"string",loading:"'eager' | 'lazy'",location:"'main' | 'sidebar'",name:"string",referrerPolicy:"string",sandbox:"string",src:"string",srcdoc:"string",shared:"string[] | 'owners' | boolean"}},a1={customIntegrations:{validate:op,help:od()},customTrayButtons:{validate:oh,help:"customTrayButtons should be a dictionary of the type ".concat(JSON.stringify(aZ))},url:{validate:function(e){return"string"==typeof e},help:"url should be a string"},baseUrl:{validate:function(e){return"string"==typeof e},help:"baseUrl should be a string"},token:{validate:function(e){return"string"==typeof e},help:"token should be a string",queryString:"t"},dailyConfig:{validate:function(e,t){try{return t.validateDailyConfig(e),!0}catch(e){console.error("Failed to validate dailyConfig",e)}return!1},help:"Unsupported dailyConfig. Check error logs for detailed info."},reactNativeConfig:{validate:function(e){return function e(t,r){if(void 0===r)return!1;switch(p(r)){case"string":return p(t)===r;case"object":if("object"!==p(t))return!1;for(var n in t)if(!e(t[n],r[n]))return!1;return!0;default:return!1}}(e,aX)},help:"reactNativeConfig should look like ".concat(JSON.stringify(aX),", all fields optional")},lang:{validate:function(e){return["da","de","en-us","en","es","fi","fr","it","jp","ka","nl","no","pl","pt","pt-BR","ru","sv","tr","user"].includes(e)},help:"language not supported. Options are: da, de, en-us, en, es, fi, fr, it, jp, ka, nl, no, pl, pt, pt-BR, ru, sv, tr, user"},userName:!0,userData:{validate:function(e){try{return oi(e),!0}catch(e){return console.error(e),!1}},help:"invalid userData type provided"},startVideoOff:!0,startAudioOff:!0,allowLocalVideo:!0,allowLocalAudio:!0,activeSpeakerMode:!0,showLeaveButton:!0,showLocalVideo:!0,showParticipantsBar:!0,showFullscreenButton:!0,showUserNameChangeUI:!0,iframeStyle:!0,customLayout:!0,cssFile:!0,cssText:!0,bodyClass:!0,videoSource:{validate:function(e,t){var r;if("boolean"==typeof e)return t._preloadCache.allowLocalVideo=e,!0;if(e instanceof MediaStreamTrack)t._sharedTracks.videoTrack=e,r={customTrack:iO};else{if(delete t._sharedTracks.videoTrack,"string"!=typeof e)return console.error("videoSource must be a MediaStreamTrack, boolean, or a string"),!1;r={deviceId:e}}return t._updatePreloadCacheInputSettings({video:{settings:r}},!1),!0}},audioSource:{validate:function(e,t){var r;if("boolean"==typeof e)return t._preloadCache.allowLocalAudio=e,!0;if(e instanceof MediaStreamTrack)t._sharedTracks.audioTrack=e,r={customTrack:iO};else{if(delete t._sharedTracks.audioTrack,"string"!=typeof e)return console.error("audioSource must be a MediaStreamTrack, boolean, or a string"),!1;r={deviceId:e}}return t._updatePreloadCacheInputSettings({audio:{settings:r}},!1),!0}},subscribeToTracksAutomatically:{validate:function(e,t){return t._preloadCache.subscribeToTracksAutomatically=e,!0}},theme:{validate:function(e){var t=["accent","accentText","background","backgroundAccent","baseText","border","mainAreaBg","mainAreaBgAccent","mainAreaText","supportiveText"],r=function(e){for(var r=0,n=Object.keys(e);r<n.length;r++){var i=n[r];if(!t.includes(i))return console.error('unsupported color "'.concat(i,'". Valid colors: ').concat(t.join(", "))),!1;if(!e[i].match(/^#[0-9a-f]{6}|#[0-9a-f]{3}$/i))return console.error("".concat(i,' theme color should be provided in valid hex color format. Received: "').concat(e[i],'"')),!1}return!0};return"object"===p(e)&&("light"in e&&"dark"in e||"colors"in e)?"light"in e&&"dark"in e?"colors"in e.light?"colors"in e.dark?r(e.light.colors)&&r(e.dark.colors):(console.error('Dark theme is missing "colors" property.',e),!1):(console.error('Light theme is missing "colors" property.',e),!1):r(e.colors):(console.error('Theme must contain either both "light" and "dark" properties, or "colors".',e),!1)},help:"unsupported theme configuration. Check error logs for detailed info."},layoutConfig:{validate:function(e){if("grid"in e){var t=e.grid;if("maxTilesPerPage"in t){if(!Number.isInteger(t.maxTilesPerPage))return console.error("grid.maxTilesPerPage should be an integer. You passed ".concat(t.maxTilesPerPage,".")),!1;if(t.maxTilesPerPage>49)return console.error("grid.maxTilesPerPage can't be larger than 49 without sacrificing browser performance. Please contact us at https://www.daily.co/contact to talk about your use case."),!1}if("minTilesPerPage"in t){if(!Number.isInteger(t.minTilesPerPage))return console.error("grid.minTilesPerPage should be an integer. You passed ".concat(t.minTilesPerPage,".")),!1;if(t.minTilesPerPage<1)return console.error("grid.minTilesPerPage can't be lower than 1."),!1;if("maxTilesPerPage"in t&&t.minTilesPerPage>t.maxTilesPerPage)return console.error("grid.minTilesPerPage can't be higher than grid.maxTilesPerPage."),!1}}return!0},help:"unsupported layoutConfig. Check error logs for detailed info."},receiveSettings:{validate:function(e){return oa(e,{allowAllParticipantsKey:!1})},help:ou({allowAllParticipantsKey:!1})},sendSettings:{validate:function(e,t){return!!function(e,t){try{return t.validateUpdateSendSettings(e),!0}catch(e){return console.error("Failed to validate send settings",e),!1}}(e,t)&&(t._preloadCache.sendSettings=e,!0)},help:"Invalid sendSettings provided. Check error logs for detailed info."},inputSettings:{validate:function(e,t){var r;return!!oo(e)&&(t._inputSettings||(t._inputSettings={}),os(e,null===(r=t.properties)||void 0===r?void 0:r.dailyConfig,t._sharedTracks),t._updatePreloadCacheInputSettings(e,!0),!0)},help:ol()},layout:{validate:function(e){return"custom-v1"===e||"browser"===e||"none"===e},help:'layout may only be set to "custom-v1"',queryString:"layout"},emb:{queryString:"emb"},embHref:{queryString:"embHref"},dailyJsVersion:{queryString:"dailyJsVersion"},proxy:{queryString:"proxy"},strictMode:!0,allowMultipleCallInstances:!0},a2={styles:{validate:function(e){for(var t in e)if("cam"!==t&&"screen"!==t)return!1;if(e.cam){for(var r in e.cam)if("div"!==r&&"video"!==r)return!1}if(e.screen){for(var n in e.screen)if("div"!==n&&"video"!==n)return!1}return!0},help:"styles format should be a subset of: { cam: {div: {}, video: {}}, screen: {div: {}, video: {}} }"},setSubscribedTracks:{validate:function(e,t){if(t._preloadCache.subscribeToTracksAutomatically)return!1;var r=[!0,!1,"staged"];if(r.includes(e)||!iR()&&"avatar"===e)return!0;var n=["audio","video","screenAudio","screenVideo","rmpAudio","rmpVideo"],i=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];for(var a in e)if("custom"===a){if(!r.includes(e[a])&&!i(e[a],!0))return!1}else{var o=!t&&!n.includes(a),s=!r.includes(e[a]);if(o||s)return!1}return!0};return i(e)},help:"setSubscribedTracks cannot be used when setSubscribeToTracksAutomatically is enabled, and should be of the form: "+"true".concat(iR()?"":" | 'avatar'"," | false | 'staged' | { [audio: true|false|'staged'], [video: true|false|'staged'], [screenAudio: true|false|'staged'], [screenVideo: true|false|'staged'] }")},setAudio:!0,setVideo:!0,setScreenShare:{validate:function(e){return!1===e},help:"setScreenShare must be false, as it's only meant for stopping remote participants' screen shares"},eject:!0,updatePermissions:{validate:function(e){for(var t=0,r=Object.entries(e);t<r.length;t++){var n=T(r[t],2),i=n[0],a=n[1];switch(i){case"hasPresence":if("boolean"!=typeof a)return!1;break;case"canSend":if(a instanceof Set||a instanceof Array||Array.isArray(a)){var o,s=["video","audio","screenVideo","screenAudio","customVideo","customAudio"],c=aY(a);try{for(c.s();!(o=c.n()).done;){var l=o.value;if(!s.includes(l))return!1}}catch(e){c.e(e)}finally{c.f()}}else if("boolean"!=typeof a)return!1;(a instanceof Array||Array.isArray(a))&&(e.canSend=new Set(a));break;case"canReceive":var u=T(aR.validateJSONObject(a),2),d=u[0],h=u[1];if(!d)return console.error(h),!1;break;case"canAdmin":if(a instanceof Set||a instanceof Array||Array.isArray(a)){var p,f=["participants","streaming","transcription"],v=aY(a);try{for(v.s();!(p=v.n()).done;){var g=p.value;if(!f.includes(g))return!1}}catch(e){v.e(e)}finally{v.f()}}else if("boolean"!=typeof a)return!1;(a instanceof Array||Array.isArray(a))&&(e.canAdmin=new Set(a));break;default:return!1}}return!0},help:"updatePermissions can take hasPresence, canSend, canReceive, and canAdmin permissions. hasPresence must be a boolean. canSend can be a boolean or an Array or Set of media types (video, audio, screenVideo, screenAudio, customVideo, customAudio). canReceive must be an object specifying base, byUserId, and/or byParticipantId fields (see documentation for more details). canAdmin can be a boolean or an Array or Set of admin types (participants, streaming, transcription)."}};Promise.any||(Promise.any=function(){var e=w(function*(e){return new Promise(function(t,r){var n=[];e.forEach(function(i){return Promise.resolve(i).then(function(e){t(e)}).catch(function(t){n.push(t),n.length===e.length&&r(n)})})})});return function(t){return e.apply(this,arguments)}}());var a3=function(){var e,t,r,n,i,a,o,s,c,l,u,f,v,_,k,E,C,M,A,O,I,P,L,D,N,j,R,x,F,V,U,B,J,Y,q,W,G,H;function K(e){var t,r,n,i,a,o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(h(this,K),i=y(i=K),S(n=m(this,aJ()?Reflect.construct(i,a||[],y(this).constructor):i.apply(this,a)),"startListeningForDeviceChanges",function(){var e;e=n.handleDeviceChange,aT()?ab.has(e)||(ab.set(e,{}),navigator.mediaDevices.enumerateDevices().then(function(t){var r;ab.has(e)&&(ab.get(e).lastDevicesString=JSON.stringify(t),aS||(r=w(function*(){var e,t=yield navigator.mediaDevices.enumerateDevices(),r=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return a_(e,void 0);var r=({}).toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?a_(e,void 0):void 0}}(e))){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==r.return||r.return()}finally{if(s)throw a}}}}(ab.keys());try{for(r.s();!(e=r.n()).done;){var n=e.value,i=JSON.stringify(t);i!==ab.get(n).lastDevicesString&&(ab.get(n).lastDevicesString=i,n(t))}}catch(e){r.e(e)}finally{r.f()}}),aS=function(){return r.apply(this,arguments)},navigator.mediaDevices.addEventListener("devicechange",aS)))}).catch(function(){})):aw.has(e)||(aw.set(e,{}),navigator.mediaDevices.enumerateDevices().then(function(t){aw.has(e)&&(aw.get(e).lastDevicesString=JSON.stringify(t),aE||(aE=setInterval(w(function*(){var e,t=yield navigator.mediaDevices.enumerateDevices(),r=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return ak(e,void 0);var r=({}).toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ak(e,void 0):void 0}}(e))){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,o=!0,s=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return o=e.done,e},e:function(e){s=!0,a=e},f:function(){try{o||null==r.return||r.return()}finally{if(s)throw a}}}}(aw.keys());try{for(r.s();!(e=r.n()).done;){var n=e.value,i=JSON.stringify(t);i!==aw.get(n).lastDevicesString&&(aw.get(n).lastDevicesString=i,n(t))}}catch(e){r.e(e)}finally{r.f()}}),3e3)))}))}),S(n,"stopListeningForDeviceChanges",function(){var e;e=n.handleDeviceChange,aT()?ab.has(e)&&(ab.delete(e),0===ab.size&&aS&&(navigator.mediaDevices.removeEventListener("devicechange",aS),aS=null)):aw.has(e)&&(aw.delete(e),0===aw.size&&aE&&(clearInterval(aE),aE=null))}),S(n,"handleDeviceChange",function(e){e=e.map(function(e){return JSON.parse(JSON.stringify(e))}),n.emitDailyJSEvent({action:"available-devices-updated",availableDevices:e})}),S(n,"handleNativeAppStateChange",(t=w(function*(e){if("destroyed"===e)return console.warn("App has been destroyed before leaving the meeting. Cleaning up all the resources!"),void(yield n.destroy());n.disableReactNativeAutoDeviceManagement("video")||("active"===e?n.camUnmutedBeforeLosingNativeActiveState&&n.setLocalVideo(!0):(n.camUnmutedBeforeLosingNativeActiveState=n.localVideo(),n.camUnmutedBeforeLosingNativeActiveState&&n.setLocalVideo(!1)))}),function(e){return t.apply(this,arguments)})),S(n,"handleNativeAudioFocusChange",function(e){n.disableReactNativeAutoDeviceManagement("audio")||(n._hasNativeAudioFocus=e,n.toggleParticipantAudioBasedOnNativeAudioFocus(),n._hasNativeAudioFocus?n.micUnmutedBeforeLosingNativeAudioFocus&&n.setLocalAudio(!0):(n.micUnmutedBeforeLosingNativeAudioFocus=n.localAudio(),n.setLocalAudio(!1)))}),S(n,"handleNativeSystemScreenCaptureStop",function(){n.stopScreenShare()}),n.strictMode=void 0===o.strictMode||o.strictMode,n.allowMultipleCallInstances=null!==(r=o.allowMultipleCallInstances)&&void 0!==r&&r,Object.keys(aq).length&&(n._logDuplicateInstanceAttempt(),!n.allowMultipleCallInstances)){if(n.strictMode)throw Error("Duplicate DailyIframe instances are not allowed");console.warn("Using strictMode: false to allow multiple call instances is now deprecated. Set `allowMultipleCallInstances: true`")}if(window._daily||(window._daily={pendings:[],instances:{}}),n.callClientId=es(),aq[n.callClientId]=n,window._daily.instances[n.callClientId]={},n._sharedTracks={},window._daily.instances[n.callClientId].tracks=n._sharedTracks,o.dailyJsVersion=K.version(),n._iframe=e,n._callObjectMode="none"===o.layout&&!n._iframe,n._preloadCache={subscribeToTracksAutomatically:!0,outputDeviceId:null,inputSettings:null,sendSettings:null,videoTrackForNetworkConnectivityTest:null,videoTrackForConnectionQualityTest:null},void 0!==o.showLocalVideo?n._callObjectMode?console.error("showLocalVideo is not available in call object mode"):n._showLocalVideo=!!o.showLocalVideo:n._showLocalVideo=!0,void 0!==o.showParticipantsBar?n._callObjectMode?console.error("showParticipantsBar is not available in call object mode"):n._showParticipantsBar=!!o.showParticipantsBar:n._showParticipantsBar=!0,void 0!==o.customIntegrations?n._callObjectMode?console.error("customIntegrations is not available in call object mode"):n._customIntegrations=o.customIntegrations:n._customIntegrations={},void 0!==o.customTrayButtons?n._callObjectMode?console.error("customTrayButtons is not available in call object mode"):n._customTrayButtons=o.customTrayButtons:n._customTrayButtons={},void 0!==o.activeSpeakerMode?n._callObjectMode?console.error("activeSpeakerMode is not available in call object mode"):n._activeSpeakerMode=!!o.activeSpeakerMode:n._activeSpeakerMode=!1,o.receiveSettings?n._callObjectMode?n._receiveSettings=o.receiveSettings:console.error("receiveSettings is only available in call object mode"):n._receiveSettings={},n.validateProperties(o),n.properties=aB({},o),n._inputSettings||(n._inputSettings={}),n._callObjectLoader=n._callObjectMode?new aa(n.callClientId):null,n._callState=r2,n._isPreparingToJoin=!1,n._accessState={access:na},n._meetingSessionSummary={},n._finalSummaryOfPrevSession={},n._meetingSessionState=om(az,n._callObjectMode),n._nativeInCallAudioMode=aW,n._participants={},n._isScreenSharing=!1,n._participantCounts=aG,n._rmpPlayerState={},n._waitingParticipants={},n._network={threshold:"good",quality:100,networkState:"unknown",stats:{}},n._activeSpeaker={},n._localAudioLevel=0,n._isLocalAudioLevelObserverRunning=!1,n._remoteParticipantsAudioLevel={},n._isRemoteParticipantsAudioLevelObserverRunning=!1,n._maxAppMessageSize=4096,n._messageChannel=iR()?new i6:new i3,n._iframe&&(n._iframe.requestFullscreen?n._iframe.addEventListener("fullscreenchange",function(){document.fullscreenElement===n._iframe?(n.emitDailyJSEvent({action:ip}),n.sendMessageToCallMachine({action:ip})):(n.emitDailyJSEvent({action:iv}),n.sendMessageToCallMachine({action:iv}))}):n._iframe.webkitRequestFullscreen&&n._iframe.addEventListener("webkitfullscreenchange",function(){document.webkitFullscreenElement===n._iframe?(n.emitDailyJSEvent({action:ip}),n.sendMessageToCallMachine({action:ip})):(n.emitDailyJSEvent({action:iv}),n.sendMessageToCallMachine({action:iv}))})),iR()){var s=n.nativeUtils();s.addAudioFocusChangeListener&&s.removeAudioFocusChangeListener&&s.addAppStateChangeListener&&s.removeAppStateChangeListener&&s.addSystemScreenCaptureStopListener&&s.removeSystemScreenCaptureStopListener||console.warn("expected (add|remove)(AudioFocusChange|AppActiveStateChange|SystemScreenCaptureStop)Listener to be available in React Native"),n._hasNativeAudioFocus=!0,s.addAudioFocusChangeListener(n.handleNativeAudioFocusChange),s.addAppStateChangeListener(n.handleNativeAppStateChange),s.addSystemScreenCaptureStopListener(n.handleNativeSystemScreenCaptureStop)}return n._callObjectMode&&n.startListeningForDeviceChanges(),n._messageChannel.addListenerForMessagesFromCallMachine(n.handleMessageFromCallMachine,n.callClientId,n),n}return b(K,$),g(K,[{key:"destroy",value:(H=w(function*(){try{yield this.leave()}catch(e){}var e,t=this._iframe;if(t){var r=t.parentElement;r&&r.removeChild(t)}if(this._messageChannel.removeListener(this.handleMessageFromCallMachine),iR()){var n=this.nativeUtils();n.removeAudioFocusChangeListener(this.handleNativeAudioFocusChange),n.removeAppStateChangeListener(this.handleNativeAppStateChange),n.removeSystemScreenCaptureStopListener(this.handleNativeSystemScreenCaptureStop)}this._callObjectMode&&this.stopListeningForDeviceChanges(),this.resetMeetingDependentVars(),this._destroyed=!0,this.emitDailyJSEvent({action:"call-instance-destroyed"}),delete aq[this.callClientId],(null===(e=window)||void 0===e||null===(e=e._daily)||void 0===e?void 0:e.instances)&&delete window._daily.instances[this.callClientId],this.strictMode&&(this.callClientId=void 0)}),function(){return H.apply(this,arguments)})},{key:"isDestroyed",value:function(){return!!this._destroyed}},{key:"loadCss",value:function(e){var t=e.bodyClass,r=e.cssFile,n=e.cssText;return or(),this.sendMessageToCallMachine({action:"load-css",cssFile:this.absoluteUrl(r),bodyClass:t,cssText:n}),this}},{key:"iframe",value:function(){return or(),this._iframe}},{key:"meetingState",value:function(){return this._callState}},{key:"accessState",value:function(){return oe(this._callObjectMode,"accessState()"),this._accessState}},{key:"participants",value:function(){return this._participants}},{key:"participantCounts",value:function(){return this._participantCounts}},{key:"waitingParticipants",value:function(){return oe(this._callObjectMode,"waitingParticipants()"),this._waitingParticipants}},{key:"validateParticipantProperties",value:function(e,t){for(var r in t){if(!a2[r])throw Error("unrecognized updateParticipant property ".concat(r));if(a2[r].validate&&!a2[r].validate(t[r],this,this._participants[e]))throw Error(a2[r].help)}}},{key:"updateParticipant",value:function(e,t){return this._participants.local&&this._participants.local.session_id===e&&(e="local"),e&&t&&(this.validateParticipantProperties(e,t),this.sendMessageToCallMachine({action:"update-participant",id:e,properties:t})),this}},{key:"updateParticipants",value:function(e){var t=this._participants.local&&this._participants.local.session_id;for(var r in e)r===t&&(r="local"),r&&e[r]&&this.validateParticipantProperties(r,e[r]);return this.sendMessageToCallMachine({action:"update-participants",participants:e}),this}},{key:"updateWaitingParticipant",value:(G=w(function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(oe(this._callObjectMode,"updateWaitingParticipant()"),a5(this._callState,"updateWaitingParticipant()"),"string"!=typeof t||"object"!==p(r))throw Error("updateWaitingParticipant() must take an id string and a updates object");return new Promise(function(n,i){e.sendMessageToCallMachine({action:"daily-method-update-waiting-participant",id:t,updates:r},function(e){e.error&&i(e.error),e.id||i(Error("unknown error in updateWaitingParticipant()")),n({id:e.id})})})}),function(){return G.apply(this,arguments)})},{key:"updateWaitingParticipants",value:(W=w(function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(oe(this._callObjectMode,"updateWaitingParticipants()"),a5(this._callState,"updateWaitingParticipants()"),"object"!==p(t))throw Error("updateWaitingParticipants() must take a mapping between ids and update objects");return new Promise(function(r,n){e.sendMessageToCallMachine({action:"daily-method-update-waiting-participants",updatesById:t},function(e){e.error&&n(e.error),e.ids||n(Error("unknown error in updateWaitingParticipants()")),r({ids:e.ids})})})}),function(){return W.apply(this,arguments)})},{key:"requestAccess",value:(q=w(function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.access,n=void 0===r?{level:no}:r,i=t.name,a=void 0===i?"":i;return oe(this._callObjectMode,"requestAccess()"),a5(this._callState,"requestAccess()"),new Promise(function(t,r){e.sendMessageToCallMachine({action:"daily-method-request-access",access:n,name:a},function(e){e.error&&r(e.error),e.access||r(Error("unknown error in requestAccess()")),t({access:e.access,granted:e.granted})})})}),function(){return q.apply(this,arguments)})},{key:"localAudio",value:function(){return this._participants.local?!["blocked","off"].includes(this._participants.local.tracks.audio.state):null}},{key:"localVideo",value:function(){return this._participants.local?!["blocked","off"].includes(this._participants.local.tracks.video.state):null}},{key:"setLocalAudio",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return"forceDiscardTrack"in t&&(iR()?(console.warn("forceDiscardTrack option not supported in React Native; ignoring"),t={}):e&&(console.warn("forceDiscardTrack option only supported when calling setLocalAudio(false); ignoring"),t={})),this.sendMessageToCallMachine({action:"local-audio",state:e,options:t}),this}},{key:"localScreenAudio",value:function(){return this._participants.local?!["blocked","off"].includes(this._participants.local.tracks.screenAudio.state):null}},{key:"localScreenVideo",value:function(){return this._participants.local?!["blocked","off"].includes(this._participants.local.tracks.screenVideo.state):null}},{key:"updateScreenShare",value:function(e){if(this._isScreenSharing)return this.sendMessageToCallMachine({action:"local-screen-update",options:e}),this;console.warn("There is no screen share in progress. Try calling startScreenShare first.")}},{key:"setLocalVideo",value:function(e){return this.sendMessageToCallMachine({action:"local-video",state:e}),this}},{key:"_setAllowLocalAudio",value:function(e){if(this._preloadCache.allowLocalAudio=e,this._callMachineInitialized)return this.sendMessageToCallMachine({action:"set-allow-local-audio",state:e}),this}},{key:"_setAllowLocalVideo",value:function(e){if(this._preloadCache.allowLocalVideo=e,this._callMachineInitialized)return this.sendMessageToCallMachine({action:"set-allow-local-video",state:e}),this}},{key:"getReceiveSettings",value:(Y=w(function*(e){var t=this,r=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).showInheritedValues,n=void 0!==r&&r;if(oe(this._callObjectMode,"getReceiveSettings()"),!this._callMachineInitialized)return this._receiveSettings;switch(p(e)){case"string":return new Promise(function(r){t.sendMessageToCallMachine({action:"get-single-participant-receive-settings",id:e,showInheritedValues:n},function(e){r(e.receiveSettings)})});case"undefined":return this._receiveSettings;default:throw Error('first argument to getReceiveSettings() must be a participant id (or "base"), or there should be no arguments')}}),function(e){return Y.apply(this,arguments)})},{key:"updateReceiveSettings",value:(J=w(function*(e){var t=this;if(oe(this._callObjectMode,"updateReceiveSettings()"),!oa(e,{allowAllParticipantsKey:!0}))throw Error(ou({allowAllParticipantsKey:!0}));return a5(this._callState,"updateReceiveSettings()","To specify receive settings earlier, use the receiveSettings config property."),new Promise(function(r){t.sendMessageToCallMachine({action:"update-receive-settings",receiveSettings:e},function(e){r({receiveSettings:e.receiveSettings})})})}),function(e){return J.apply(this,arguments)})},{key:"_prepInputSettingsForSharing",value:function(e,t){if(e){var r={};if(e.audio){e.audio.settings&&(!Object.keys(e.audio.settings).length&&t||(r.audio={settings:aB({},e.audio.settings)})),t&&null!==(n=r.audio)&&void 0!==n&&null!==(n=n.settings)&&void 0!==n&&n.customTrack&&(r.audio.settings={customTrack:this._sharedTracks.audioTrack});var n,i,a,o="none"===(null===(i=e.audio.processor)||void 0===i?void 0:i.type)&&(null===(a=e.audio.processor)||void 0===a?void 0:a._isDefaultWhenNone);if(e.audio.processor&&!o){var s=aB({},e.audio.processor);delete s._isDefaultWhenNone,r.audio=aB(aB({},r.audio),{},{processor:s})}}if(e.video){e.video.settings&&(!Object.keys(e.video.settings).length&&t||(r.video={settings:aB({},e.video.settings)})),t&&null!==(c=r.video)&&void 0!==c&&null!==(c=c.settings)&&void 0!==c&&c.customTrack&&(r.video.settings={customTrack:this._sharedTracks.videoTrack});var c,l,u,d="none"===(null===(l=e.video.processor)||void 0===l?void 0:l.type)&&(null===(u=e.video.processor)||void 0===u?void 0:u._isDefaultWhenNone);if(e.video.processor&&!d){var h=aB({},e.video.processor);delete h._isDefaultWhenNone,r.video=aB(aB({},r.video),{},{processor:h})}}return r}}},{key:"getInputSettings",value:function(){var e=this;return or(),new Promise(function(t){t(e._getInputSettings())})}},{key:"_getInputSettings",value:function(){var e,t,r,n,i,a,o={processor:{type:"none",_isDefaultWhenNone:!0}};this._inputSettings?(e=(null===(r=this._inputSettings)||void 0===r?void 0:r.video)||o,t=(null===(n=this._inputSettings)||void 0===n?void 0:n.audio)||o):(e=(null===(i=this._preloadCache)||void 0===i||null===(i=i.inputSettings)||void 0===i?void 0:i.video)||o,t=(null===(a=this._preloadCache)||void 0===a||null===(a=a.inputSettings)||void 0===a?void 0:a.audio)||o);var s={audio:t,video:e};return this._prepInputSettingsForSharing(s,!0)}},{key:"_updatePreloadCacheInputSettings",value:function(e,t){var r,n,i,a,o,s,c=this._inputSettings||{},l={};e.video?((l.video={},e.video.settings)?(l.video.settings={},t||e.video.settings.customTrack||null===(i=c.video)||void 0===i||!i.settings?l.video.settings=e.video.settings:l.video.settings=aB(aB({},c.video.settings),e.video.settings),Object.keys(l.video.settings).length||delete l.video.settings):null!==(r=c.video)&&void 0!==r&&r.settings&&(l.video.settings=c.video.settings),e.video.processor?l.video.processor=e.video.processor:null!==(n=c.video)&&void 0!==n&&n.processor&&(l.video.processor=c.video.processor)):c.video&&(l.video=c.video),e.audio?((l.audio={},e.audio.settings)?(l.audio.settings={},t||e.audio.settings.customTrack||null===(s=c.audio)||void 0===s||!s.settings?l.audio.settings=e.audio.settings:l.audio.settings=aB(aB({},c.audio.settings),e.audio.settings),Object.keys(l.audio.settings).length||delete l.audio.settings):null!==(a=c.audio)&&void 0!==a&&a.settings&&(l.audio.settings=c.audio.settings),e.audio.processor?l.audio.processor=e.audio.processor:null!==(o=c.audio)&&void 0!==o&&o.processor&&(l.audio.processor=c.audio.processor)):c.audio&&(l.audio=c.audio),this._maybeUpdateInputSettings(l)}},{key:"_devicesFromInputSettings",value:function(e){var t,r,n=(null==e||null===(t=e.video)||void 0===t||null===(t=t.settings)||void 0===t?void 0:t.deviceId)||null,i=(null==e||null===(r=e.audio)||void 0===r||null===(r=r.settings)||void 0===r?void 0:r.deviceId)||null,a=this._preloadCache.outputDeviceId||null;return{camera:n?{deviceId:n}:{},mic:i?{deviceId:i}:{},speaker:a?{deviceId:a}:{}}}},{key:"updateInputSettings",value:(B=w(function*(e){var t=this;return or(),oo(e)?e.video||e.audio?(os(e,this.properties.dailyConfig,this._sharedTracks),this._callObjectMode&&!this._callMachineInitialized?(this._updatePreloadCacheInputSettings(e,!0),this._getInputSettings()):new Promise(function(r,n){t.sendMessageToCallMachine({action:"update-input-settings",inputSettings:e},function(i){if(i.error)n(i.error);else{if(i.returnPreloadCache)return t._updatePreloadCacheInputSettings(e,!0),void r(t._getInputSettings());t._maybeUpdateInputSettings(i.inputSettings),r(t._prepInputSettingsForSharing(i.inputSettings,!0))}})})):this._getInputSettings():(console.error(ol()),Promise.reject(ol()))}),function(e){return B.apply(this,arguments)})},{key:"setBandwidth",value:function(e){var t=e.kbs,r=e.trackConstraints;if(or(),this._callMachineInitialized)return this.sendMessageToCallMachine({action:"set-bandwidth",kbs:t,trackConstraints:r}),this}},{key:"getDailyLang",value:function(){var e=this;if(or(),this._callMachineInitialized)return new Promise(function(t){e.sendMessageToCallMachine({action:"get-daily-lang"},function(e){delete e.action,delete e.callbackStamp,t(e)})})}},{key:"setDailyLang",value:function(e){return or(),this.sendMessageToCallMachine({action:"set-daily-lang",lang:e}),this}},{key:"setProxyUrl",value:function(e){return this.sendMessageToCallMachine({action:"set-proxy-url",proxyUrl:e}),this}},{key:"setIceConfig",value:function(e){return this.sendMessageToCallMachine({action:"set-ice-config",iceConfig:e}),this}},{key:"meetingSessionSummary",value:function(){return[r9,r8].includes(this._callState)?this._finalSummaryOfPrevSession:this._meetingSessionSummary}},{key:"getMeetingSession",value:(U=w(function*(){var e=this;return console.warn("getMeetingSession() is deprecated: use meetingSessionSummary(), which will return immediately"),a5(this._callState,"getMeetingSession()"),new Promise(function(t){e.sendMessageToCallMachine({action:"get-meeting-session"},function(e){delete e.action,delete e.callbackStamp,t(e)})})}),function(){return U.apply(this,arguments)})},{key:"meetingSessionState",value:function(){return a5(this._callState,"meetingSessionState"),this._meetingSessionState}},{key:"setMeetingSessionData",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"replace";oe(this._callObjectMode,"setMeetingSessionData()"),a5(this._callState,"setMeetingSessionData");try{new ae({data:e,mergeStrategy:t})}catch(e){throw console.error(e),e}try{this.sendMessageToCallMachine({action:"set-session-data",data:e,mergeStrategy:t})}catch(e){throw Error("Error setting meeting session data: ".concat(e))}}},{key:"setUserName",value:function(e,t){var r=this;return this.properties.userName=e,new Promise(function(n){r.sendMessageToCallMachine({action:"set-user-name",name:null!=e?e:"",thisMeetingOnly:iR()||!!t&&!!t.thisMeetingOnly},function(e){delete e.action,delete e.callbackStamp,n(e)})})}},{key:"setUserData",value:(V=w(function*(e){var t=this;try{oi(e)}catch(e){throw console.error(e),e}if(this.properties.userData=e,this._callMachineInitialized)return new Promise(function(r){try{t.sendMessageToCallMachine({action:"set-user-data",userData:e},function(e){delete e.action,delete e.callbackStamp,r(e)})}catch(e){throw Error("Error setting user data: ".concat(e))}})}),function(e){return V.apply(this,arguments)})},{key:"validateAudioLevelInterval",value:function(e){if(e&&(e<100||"number"!=typeof e))throw Error("The interval must be a number greater than or equal to 100 milliseconds.")}},{key:"startLocalAudioLevelObserver",value:function(e){var t=this;if("undefined"==typeof AudioWorkletNode&&!iR())throw Error("startLocalAudioLevelObserver() is not supported on this browser");if(this.validateAudioLevelInterval(e),this._callMachineInitialized)return this._isLocalAudioLevelObserverRunning=!0,new Promise(function(r,n){t.sendMessageToCallMachine({action:"start-local-audio-level-observer",interval:e},function(e){t._isLocalAudioLevelObserverRunning=!e.error,e.error?n({error:e.error}):r()})});this._preloadCache.localAudioLevelObserver={enabled:!0,interval:e}}},{key:"isLocalAudioLevelObserverRunning",value:function(){return this._isLocalAudioLevelObserverRunning}},{key:"stopLocalAudioLevelObserver",value:function(){this._preloadCache.localAudioLevelObserver=null,this._localAudioLevel=0,this._isLocalAudioLevelObserverRunning=!1,this.sendMessageToCallMachine({action:"stop-local-audio-level-observer"})}},{key:"startRemoteParticipantsAudioLevelObserver",value:function(e){var t=this;if(this.validateAudioLevelInterval(e),this._callMachineInitialized)return this._isRemoteParticipantsAudioLevelObserverRunning=!0,new Promise(function(r,n){t.sendMessageToCallMachine({action:"start-remote-participants-audio-level-observer",interval:e},function(e){t._isRemoteParticipantsAudioLevelObserverRunning=!e.error,e.error?n({error:e.error}):r()})});this._preloadCache.remoteParticipantsAudioLevelObserver={enabled:!0,interval:e}}},{key:"isRemoteParticipantsAudioLevelObserverRunning",value:function(){return this._isRemoteParticipantsAudioLevelObserverRunning}},{key:"stopRemoteParticipantsAudioLevelObserver",value:function(){this._preloadCache.remoteParticipantsAudioLevelObserver=null,this._remoteParticipantsAudioLevel={},this._isRemoteParticipantsAudioLevelObserverRunning=!1,this.sendMessageToCallMachine({action:"stop-remote-participants-audio-level-observer"})}},{key:"startCamera",value:(F=w(function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(oe(this._callObjectMode,"startCamera()"),a9(this._callState,this._isPreparingToJoin,"startCamera()","Did you mean to use setLocalAudio() and/or setLocalVideo() instead?"),this.needsLoad())try{yield this.load(t)}catch(e){return Promise.reject(e)}else{if(this._didPreAuth){if(t.url&&t.url!==this.properties.url)return console.error("url in startCamera() is different than the one used in preAuth()"),Promise.reject();if(t.token&&t.token!==this.properties.token)return console.error("token in startCamera() is different than the one used in preAuth()"),Promise.reject()}this.validateProperties(t),this.properties=aB(aB({},this.properties),t)}return new Promise(function(t){e._preloadCache.inputSettings=e._prepInputSettingsForSharing(e._inputSettings,!1),e.sendMessageToCallMachine({action:"start-camera",properties:a4(e.properties,e.callClientId),preloadCache:a4(e._preloadCache,e.callClientId)},function(e){t({camera:e.camera,mic:e.mic,speaker:e.speaker})})})}),function(){return F.apply(this,arguments)})},{key:"validateCustomTrack",value:function(e,t,r){if(r&&r.length>50)throw Error("Custom track `trackName` must not be more than 50 characters");if(t&&"music"!==t&&"speech"!==t&&!(t instanceof Object))throw Error("Custom track `mode` must be either `music` | `speech` | `DailyMicAudioModeSettings` or `undefined`");if(r&&["cam-audio","cam-video","screen-video","screen-audio","rmpAudio","rmpVideo","customVideoDefaults"].includes(r))throw Error("Custom track `trackName` must not match a track name already used by daily: cam-audio, cam-video, customVideoDefaults, screen-video, screen-audio, rmpAudio, rmpVideo");if(!(e instanceof MediaStreamTrack))throw Error("Custom tracks provided must be instances of MediaStreamTrack")}},{key:"startCustomTrack",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{track:track,mode:mode,trackName:trackName};return or(),a5(this._callState,"startCustomTrack()"),this.validateCustomTrack(t.track,t.mode,t.trackName),new Promise(function(r,n){e._sharedTracks.customTrack=t.track,t.track=iO,e.sendMessageToCallMachine({action:"start-custom-track",properties:t},function(e){e.error?n({error:e.error}):r(e.mediaTag)})})}},{key:"stopCustomTrack",value:function(e){var t=this;return or(),a5(this._callState,"stopCustomTrack()"),new Promise(function(r){t.sendMessageToCallMachine({action:"stop-custom-track",mediaTag:e},function(e){r(e.mediaTag)})})}},{key:"setCamera",value:function(e){var t=this;return on(),a8(this._callMachineInitialized,"setCamera()"),new Promise(function(r){t.sendMessageToCallMachine({action:"set-camera",cameraDeviceId:e},function(e){r({device:e.device})})})}},{key:"setAudioDevice",value:(x=w(function*(e){return on(),this.nativeUtils().setAudioDevice(e),{deviceId:yield this.nativeUtils().getAudioDevice()}}),function(e){return x.apply(this,arguments)})},{key:"cycleCamera",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new Promise(function(r){e.sendMessageToCallMachine({action:"cycle-camera",properties:t},function(e){r({device:e.device})})})}},{key:"cycleMic",value:function(){var e=this;return or(),new Promise(function(t){e.sendMessageToCallMachine({action:"cycle-mic"},function(e){t({device:e.device})})})}},{key:"getCameraFacingMode",value:function(){var e=this;return on(),new Promise(function(t){e.sendMessageToCallMachine({action:"get-camera-facing-mode"},function(e){t(e.facingMode)})})}},{key:"setInputDevicesAsync",value:(R=w(function*(e){var t=this,r=e.audioDeviceId,n=e.videoDeviceId,i=e.audioSource,a=e.videoSource;if(or(),void 0!==i&&(r=i),void 0!==a&&(n=a),"boolean"==typeof r&&(this._setAllowLocalAudio(r),r=void 0),"boolean"==typeof n&&(this._setAllowLocalVideo(n),n=void 0),!r&&!n)return yield this.getInputDevices();var o={};return r&&(r instanceof MediaStreamTrack?(this._sharedTracks.audioTrack=r,o.audio={settings:{customTrack:r=iO}}):(delete this._sharedTracks.audioTrack,o.audio={settings:{deviceId:r}})),n&&(n instanceof MediaStreamTrack?(this._sharedTracks.videoTrack=n,o.video={settings:{customTrack:n=iO}}):(delete this._sharedTracks.videoTrack,o.video={settings:{deviceId:n}})),this._callObjectMode&&this.needsLoad()?(this._updatePreloadCacheInputSettings(o,!1),this._devicesFromInputSettings(this._inputSettings)):new Promise(function(e){t.sendMessageToCallMachine({action:"set-input-devices",audioDeviceId:r,videoDeviceId:n},function(r){if(delete r.action,delete r.callbackStamp,r.returnPreloadCache)return t._updatePreloadCacheInputSettings(o,!1),void e(t._devicesFromInputSettings(t._inputSettings));e(r)})})}),function(e){return R.apply(this,arguments)})},{key:"setOutputDeviceAsync",value:(j=w(function*(e){var t=this,r=e.outputDeviceId;return or(),r&&(this._preloadCache.outputDeviceId=r),this._callObjectMode&&this.needsLoad()?this._devicesFromInputSettings(this._inputSettings):new Promise(function(e){t.sendMessageToCallMachine({action:"set-output-device",outputDeviceId:r},function(r){delete r.action,delete r.callbackStamp,r.returnPreloadCache?e(t._devicesFromInputSettings(t._inputSettings)):e(r)})})}),function(e){return j.apply(this,arguments)})},{key:"getInputDevices",value:(N=w(function*(){var e=this;return this._callObjectMode&&this.needsLoad()?this._devicesFromInputSettings(this._inputSettings):new Promise(function(t){e.sendMessageToCallMachine({action:"get-input-devices"},function(r){r.returnPreloadCache?t(e._devicesFromInputSettings(e._inputSettings)):t({camera:r.camera,mic:r.mic,speaker:r.speaker})})})}),function(){return N.apply(this,arguments)})},{key:"nativeInCallAudioMode",value:function(){return on(),this._nativeInCallAudioMode}},{key:"setNativeInCallAudioMode",value:function(e){if(on(),[aW,"voice"].includes(e)){if(e!==this._nativeInCallAudioMode)return this._nativeInCallAudioMode=e,!this.disableReactNativeAutoDeviceManagement("audio")&&a6(this._callState,this._isPreparingToJoin)&&this.nativeUtils().setAudioMode(this._nativeInCallAudioMode),this}else console.error("invalid in-call audio mode specified: ",e)}},{key:"preAuth",value:(D=w(function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(oe(this._callObjectMode,"preAuth()"),a9(this._callState,this._isPreparingToJoin,"preAuth()"),this.needsLoad()&&(yield this.load(t)),!t.url)throw Error("preAuth() requires at least a url to be provided");return this.validateProperties(t),this.properties=aB(aB({},this.properties),t),new Promise(function(t,r){e._preloadCache.inputSettings=e._prepInputSettingsForSharing(e._inputSettings,!1),e.sendMessageToCallMachine({action:"daily-method-preauth",properties:a4(e.properties,e.callClientId),preloadCache:a4(e._preloadCache,e.callClientId)},function(n){return n.error?r(n.error):n.access?(e._didPreAuth=!0,void t({access:n.access})):r(Error("unknown error in preAuth()"))})})}),function(){return D.apply(this,arguments)})},{key:"load",value:(L=w(function*(e){var t=this;if(this.needsLoad()){if(this._destroyed&&(this._logUseAfterDestroy(),this.strictMode))throw Error("Use after destroy");if(e&&(this.validateProperties(e),this.properties=aB(aB({},this.properties),e)),!this._callObjectMode&&!this.properties.url)throw Error("can't load iframe meeting because url property isn't set");return this._updateCallState(r3),this.emitDailyJSEvent({action:nL}),this._callObjectMode?new Promise(function(e,r){t._callObjectLoader.cancel();var n=Date.now();t._callObjectLoader.load(t.properties.dailyConfig,function(r){t._bundleLoadTime=r?"no-op":Date.now()-n,t._updateCallState(r4),r&&t.emitDailyJSEvent({action:nN}),e()},function(e,n){if(t.emitDailyJSEvent({action:nD}),!n){t._updateCallState(r8),t.resetMeetingDependentVars();var i={action:iE,errorMsg:e.msg,error:{type:"connection-error",msg:"Failed to load call object bundle.",details:{on:"load",sourceError:e,bundleUrl:eu(t.properties.dailyConfig)}}};t._maybeSendToSentry(i),t.emitDailyJSEvent(i),r(e.msg)}})}):(this._iframe.src=el(this.assembleMeetingUrl(),this.properties.dailyConfig),new Promise(function(e,r){t._loadedCallback=function(n){t._callState!==r8?(t._updateCallState(r4),(t.properties.cssFile||t.properties.cssText)&&t.loadCss(t.properties),e()):r(n)}}))}}),function(e){return L.apply(this,arguments)})},{key:"join",value:(P=w(function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this._testCallInProgress&&this.stopTestCallQuality();var r=!1;if(this.needsLoad()){this.updateIsPreparingToJoin(!0);try{yield this.load(t)}catch(e){return this.updateIsPreparingToJoin(!1),Promise.reject(e)}}else{if(r=!(!this.properties.cssFile&&!this.properties.cssText),this._didPreAuth){if(t.url&&t.url!==this.properties.url)return console.error("url in join() is different than the one used in preAuth()"),this.updateIsPreparingToJoin(!1),Promise.reject();if(t.token&&t.token!==this.properties.token)return console.error("token in join() is different than the one used in preAuth()"),this.updateIsPreparingToJoin(!1),Promise.reject()}if(t.url&&!this._callObjectMode&&t.url&&t.url!==this.properties.url)return console.error("url in join() is different than the one used in load() (".concat(this.properties.url," -> ").concat(t.url,")")),this.updateIsPreparingToJoin(!1),Promise.reject();this.validateProperties(t),this.properties=aB(aB({},this.properties),t)}return void 0!==t.showLocalVideo&&(this._callObjectMode?console.error("showLocalVideo is not available in callObject mode"):this._showLocalVideo=!!t.showLocalVideo),void 0!==t.showParticipantsBar&&(this._callObjectMode?console.error("showParticipantsBar is not available in callObject mode"):this._showParticipantsBar=!!t.showParticipantsBar),this._callState===r6||this._callState===r5?(console.warn("already joined meeting, call leave() before joining again"),void this.updateIsPreparingToJoin(!1)):(this._updateCallState(r5,!1),this.emitDailyJSEvent({action:nx}),this._preloadCache.inputSettings=this._prepInputSettingsForSharing(this._inputSettings||{},!1),this.sendMessageToCallMachine({action:"join-meeting",properties:a4(this.properties,this.callClientId),preloadCache:a4(this._preloadCache,this.callClientId)}),new Promise(function(t,n){e._joinedCallback=function(i,a){if(e._callState!==r8){if(e._updateCallState(r6),i)for(var o in i){if(e._callObjectMode){var s=e._callMachine().store;ag(i[o],s),am(i[o],s),ay(i[o],e._participants[o],s)}e._participants[o]=aB({},i[o]),e.toggleParticipantAudioBasedOnNativeAudioFocus()}r&&e.loadCss(e.properties),t(i)}else n(a)}}))}),function(){return P.apply(this,arguments)})},{key:"leave",value:(I=w(function*(){var e=this;return this._testCallInProgress&&this.stopTestCallQuality(),new Promise(function(t){e._callState===r9||e._callState===r8?t():e._callObjectLoader&&!e._callObjectLoader.loaded?(e._callObjectLoader.cancel(),e._updateCallState(r9),e.resetMeetingDependentVars(),e.emitDailyJSEvent({action:r9}),t()):(e._resolveLeave=t,e.sendMessageToCallMachine({action:"leave-meeting"}))})}),function(){return I.apply(this,arguments)})},{key:"startScreenShare",value:(O=w(function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(a8(this._callMachineInitialized,"startScreenShare()"),t.screenVideoSendSettings&&this._validateVideoSendSettings("screenVideo",t.screenVideoSendSettings),t.mediaStream&&(this._sharedTracks.screenMediaStream=t.mediaStream,t.mediaStream=iO),"undefined"!=typeof DailyNativeUtils&&void 0!==DailyNativeUtils.isIOS&&DailyNativeUtils.isIOS){var r=this.nativeUtils();if(yield r.isScreenBeingCaptured())return void this.emitDailyJSEvent({action:iw,type:"screen-share-error",errorMsg:"Could not start the screen sharing. The screen is already been captured!"});r.setSystemScreenCaptureStartCallback(function(){r.setSystemScreenCaptureStartCallback(null),e.sendMessageToCallMachine({action:iC,captureOptions:t})}),r.presentSystemScreenCapturePrompt()}else this.sendMessageToCallMachine({action:iC,captureOptions:t})}),function(){return O.apply(this,arguments)})},{key:"stopScreenShare",value:function(){a8(this._callMachineInitialized,"stopScreenShare()"),this.sendMessageToCallMachine({action:"local-screen-stop"})}},{key:"startRecording",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.type;if(t&&"cloud"!==t&&"raw-tracks"!==t&&"local"!==t)throw Error("invalid type: ".concat(t,", allowed values 'cloud', 'raw-tracks', or 'local'"));this.sendMessageToCallMachine(aB({action:"local-recording-start"},e))}},{key:"updateRecording",value:function(e){var t=e.layout,r=e.instanceId;this.sendMessageToCallMachine({action:"daily-method-update-recording",layout:void 0===t?{preset:"default"}:t,instanceId:r})}},{key:"stopRecording",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.sendMessageToCallMachine(aB({action:"local-recording-stop"},e))}},{key:"startLiveStreaming",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.sendMessageToCallMachine(aB({action:"daily-method-start-live-streaming"},e))}},{key:"updateLiveStreaming",value:function(e){var t=e.layout,r=e.instanceId;this.sendMessageToCallMachine({action:"daily-method-update-live-streaming",layout:void 0===t?{preset:"default"}:t,instanceId:r})}},{key:"addLiveStreamingEndpoints",value:function(e){var t=e.endpoints,r=e.instanceId;this.sendMessageToCallMachine({action:iM,endpointsOp:"add-endpoints",endpoints:t,instanceId:r})}},{key:"removeLiveStreamingEndpoints",value:function(e){var t=e.endpoints,r=e.instanceId;this.sendMessageToCallMachine({action:iM,endpointsOp:"remove-endpoints",endpoints:t,instanceId:r})}},{key:"stopLiveStreaming",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.sendMessageToCallMachine(aB({action:"daily-method-stop-live-streaming"},e))}},{key:"validateDailyConfig",value:function(e){e.camSimulcastEncodings&&(console.warn("camSimulcastEncodings is deprecated. Use sendSettings, found in DailyCallOptions, to provide camera simulcast settings."),this.validateSimulcastEncodings(e.camSimulcastEncodings)),e.screenSimulcastEncodings&&console.warn("screenSimulcastEncodings is deprecated. Use sendSettings, found in DailyCallOptions, to provide screen simulcast settings."),i$()&&e.noAutoDefaultDeviceChange&&console.warn("noAutoDefaultDeviceChange is not supported on Android, and will be ignored.")}},{key:"validateSimulcastEncodings",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(e){if(!(e instanceof Array||Array.isArray(e)))throw Error("encodings must be an Array");if(!og(e.length,1,3))throw Error("encodings must be an Array with between 1 to ".concat(3," layers"));for(var n=0;n<e.length;n++){var i=e[n];for(var a in this._validateEncodingLayerHasValidProperties(i),i)if(aK.includes(a)){if("number"!=typeof i[a])throw Error("".concat(a," must be a number"));if(t){var o=t[a],s=o.min,c=o.max;if(!og(i[a],s,c))throw Error("".concat(a," value not in range. valid range: ").concat(s," to ").concat(c))}}else if(!["active","scalabilityMode"].includes(a))throw Error("Invalid key ".concat(a,", valid keys are:")+Object.values(aK));if(r&&!i.hasOwnProperty("maxBitrate"))throw Error("maxBitrate is not specified")}}}},{key:"startRemoteMediaPlayer",value:(A=w(function*(e){var t=this,r=e.url,n=e.settings,i=void 0===n?{state:iL.PLAY}:n;try{(function(e){if("string"!=typeof e)throw Error('url parameter must be "string" type')})(r),ov(i),function(e){for(var t in e)if(!aQ.includes(t))throw Error("Invalid key ".concat(t,", valid keys are: ").concat(aQ));e.simulcastEncodings&&this.validateSimulcastEncodings(e.simulcastEncodings,aH,!0)}(i)}catch(e){throw console.error("invalid argument Error: ".concat(e)),console.error('startRemoteMediaPlayer arguments must be of the form:\n  { url: "playback url",\n  settings?:\n  {state: "play"|"pause", simulcastEncodings?: [{}] } }'),e}return new Promise(function(e,n){t.sendMessageToCallMachine({action:"daily-method-start-remote-media-player",url:r,settings:i},function(t){t.error?n({error:t.error,errorMsg:t.errorMsg}):e({session_id:t.session_id,remoteMediaPlayerState:{state:t.state,settings:t.settings}})})})}),function(e){return A.apply(this,arguments)})},{key:"stopRemoteMediaPlayer",value:(M=w(function*(e){var t=this;if("string"!=typeof e)throw Error(" remotePlayerID must be of type string");return new Promise(function(r,n){t.sendMessageToCallMachine({action:"daily-method-stop-remote-media-player",session_id:e},function(e){e.error?n({error:e.error,errorMsg:e.errorMsg}):r()})})}),function(e){return M.apply(this,arguments)})},{key:"updateRemoteMediaPlayer",value:(C=w(function*(e){var t=this,r=e.session_id,n=e.settings;try{ov(n)}catch(e){throw console.error("invalid argument Error: ".concat(e)),console.error('updateRemoteMediaPlayer arguments must be of the form:\n  session_id: "participant session",\n  { settings?: {state: "play"|"pause"} }'),e}return new Promise(function(e,i){t.sendMessageToCallMachine({action:"daily-method-update-remote-media-player",session_id:r,settings:n},function(t){t.error?i({error:t.error,errorMsg:t.errorMsg}):e({session_id:t.session_id,remoteMediaPlayerState:{state:t.state,settings:t.settings}})})})}),function(e){return C.apply(this,arguments)})},{key:"startTranscription",value:function(e){a5(this._callState,"startTranscription()"),this.sendMessageToCallMachine(aB({action:"daily-method-start-transcription"},e))}},{key:"updateTranscription",value:function(e){if(a5(this._callState,"updateTranscription()"),!e)throw Error("updateTranscription Error: options is mandatory");if("object"!==p(e))throw Error("updateTranscription Error: options must be object type");if(e.participants&&!Array.isArray(e.participants))throw Error("updateTranscription Error: participants must be an array");this.sendMessageToCallMachine(aB({action:"daily-method-update-transcription"},e))}},{key:"stopTranscription",value:function(e){if(a5(this._callState,"stopTranscription()"),e&&"object"!==p(e))throw Error("stopTranscription Error: options must be object type");if(e&&!e.instanceId)throw Error('"instanceId" not provided');this.sendMessageToCallMachine(aB({action:"daily-method-stop-transcription"},e))}},{key:"startDialOut",value:(E=w(function*(e){var t=this;a5(this._callState,"startDialOut()");var r=function(e){if(e){if(!Array.isArray(e))throw Error("Error starting dial out: audio codec must be an array");if(e.length<=0)throw Error("Error starting dial out: audio codec array specified but empty");e.forEach(function(e){if("string"!=typeof e)throw Error("Error starting dial out: audio codec must be a string");if("OPUS"!==e&&"PCMU"!==e&&"PCMA"!==e&&"G722"!==e)throw Error("Error starting dial out: audio codec must be one of OPUS, PCMU, PCMA, G722")})}};if(!e.sipUri&&!e.phoneNumber)throw Error("Error starting dial out: either a sip uri or phone number must be provided");if(e.sipUri&&e.phoneNumber)throw Error("Error starting dial out: only one of sip uri or phone number must be provided");if(e.sipUri){if("string"!=typeof e.sipUri)throw Error("Error starting dial out: sipUri must be a string");if(!e.sipUri.startsWith("sip:"))throw Error("Error starting dial out: Invalid SIP URI, must start with 'sip:'");if(e.video&&"boolean"!=typeof e.video)throw Error("Error starting dial out: video must be a boolean value");!function(e){if(e&&(r(e.audio),e.video)){if(!Array.isArray(e.video))throw Error("Error starting dial out: video codec must be an array");if(e.video.length<=0)throw Error("Error starting dial out: video codec array specified but empty");e.video.forEach(function(e){if("string"!=typeof e)throw Error("Error starting dial out: video codec must be a string");if("H264"!==e&&"VP8"!==e)throw Error("Error starting dial out: video codec must be H264 or VP8")})}}(e.codecs)}if(e.phoneNumber){if("string"!=typeof e.phoneNumber)throw Error("Error starting dial out: phoneNumber must be a string");if(!/^\+\d{1,}$/.test(e.phoneNumber))throw Error("Error starting dial out: Invalid phone number, must be valid phone number as per E.164");e.codecs&&r(e.codecs.audio)}if(e.callerId){if("string"!=typeof e.callerId)throw Error("Error starting dial out: callerId must be a string");if(e.sipUri)throw Error("Error starting dial out: callerId not allowed with sipUri")}if(e.displayName){if("string"!=typeof e.displayName)throw Error("Error starting dial out: displayName must be a string");if(e.displayName.length>=200)throw Error("Error starting dial out: displayName length must be less than 200")}if(e.userId){if("string"!=typeof e.userId)throw Error("Error starting dial out: userId must be a string");if(e.userId.length>36)throw Error("Error starting dial out: userId length must be less than or equal to 36")}return new Promise(function(r,n){t.sendMessageToCallMachine(aB({action:"dialout-start"},e),function(e){e.error?n(e.error):r(e)})})}),function(e){return E.apply(this,arguments)})},{key:"stopDialOut",value:function(e){var t=this;return a5(this._callState,"stopDialOut()"),new Promise(function(r,n){t.sendMessageToCallMachine(aB({action:"dialout-stop"},e),function(e){e.error?n(e.error):r(e)})})}},{key:"sipCallTransfer",value:(k=w(function*(e){var t=this;if(a5(this._callState,"sipCallTransfer()"),!e)throw Error("sipCallTransfer() requires a sessionId and toEndPoint");return e.useSipRefer=!1,of(e,"sipCallTransfer"),new Promise(function(r,n){t.sendMessageToCallMachine(aB({action:iN},e),function(e){e.error?n(e.error):r(e)})})}),function(e){return k.apply(this,arguments)})},{key:"sipRefer",value:(_=w(function*(e){var t=this;if(a5(this._callState,"sipRefer()"),!e)throw Error("sessionId and toEndPoint are mandatory parameter");return e.useSipRefer=!0,of(e,"sipRefer"),new Promise(function(r,n){t.sendMessageToCallMachine(aB({action:iN},e),function(e){e.error?n(e.error):r(e)})})}),function(e){return _.apply(this,arguments)})},{key:"sendDTMF",value:(v=w(function*(e){var t=this;return a5(this._callState,"sendDTMF()"),function(e){var t=e.sessionId,r=e.tones;if(!t||!r)throw Error("sessionId and tones are mandatory parameter");if("string"!=typeof t||"string"!=typeof r)throw Error("sessionId and tones should be of string type");if(r.length>20)throw Error("tones string must be upto 20 characters");var n=r.match(/[^0-9A-D*#]/g);if(n&&n[0])throw Error("".concat(n[0]," is not valid DTMF tone"))}(e),new Promise(function(r,n){t.sendMessageToCallMachine(aB({action:"send-dtmf"},e),function(e){e.error?n(e.error):r(e)})})}),function(e){return v.apply(this,arguments)})},{key:"getNetworkStats",value:function(){var e=this;return this._callState!==r6?Promise.resolve(aB({stats:{latest:{}}},this._network)):new Promise(function(t){e.sendMessageToCallMachine({action:"get-calc-stats"},function(r){t(aB(aB({},e._network),{},{stats:r.stats}))})})}},{key:"testWebsocketConnectivity",value:(f=w(function*(){var e=this;if(a7(this._testCallInProgress,"testWebsocketConnectivity()"),this.needsLoad())try{yield this.load()}catch(e){return Promise.reject(e)}return new Promise(function(t,r){e.sendMessageToCallMachine({action:"test-websocket-connectivity"},function(e){e.error?r(e.error):t(e.results)})})}),function(){return f.apply(this,arguments)})},{key:"abortTestWebsocketConnectivity",value:function(){this.sendMessageToCallMachine({action:"abort-test-websocket-connectivity"})}},{key:"_validateVideoTrackForNetworkTests",value:function(e){return e?e instanceof MediaStreamTrack?!!(e&&"live"===e.readyState&&(!e.muted||aC.has(e.id)))||(console.error("Video track is not playable. This test needs a live video track."),!1):(console.error("Video track needs to be of type `MediaStreamTrack`."),!1):(console.error("Missing video track. You must provide a video track in order to run this test."),!1)}},{key:"testCallQuality",value:(u=w(function*(){var e=this;or(),oe(this._callObjectMode,"testCallQuality()"),a8(this._callMachineInitialized,"testCallQuality()",null,!0),a9(this._callState,this._isPreparingToJoin,"testCallQuality()");var t=this._testCallAlreadyInProgress,r=function(r){t||(e._testCallInProgress=r)};if(r(!0),this.needsLoad())try{var n=this._callState;yield this.load(),this._callState=n}catch(e){return r(!1),Promise.reject(e)}return new Promise(function(t){e.sendMessageToCallMachine({action:"test-call-quality",dailyJsVersion:e.properties.dailyJsVersion},function(n){var i=n.results,a=i.result,o=d(i,aF);if("failed"===a){var s,c=aB({},o);null!==(s=o.error)&&void 0!==s&&s.details?(o.error.details=JSON.parse(o.error.details),c.error=aB(aB({},c.error),{},{details:aB({},c.error.details)}),c.error.details.duringTest="testCallQuality"):(c.error=c.error?aB({},c.error):{},c.error.details={duringTest:"testCallQuality"}),e._maybeSendToSentry(c)}r(!1),t(aB({result:a},o))})})}),function(){return u.apply(this,arguments)})},{key:"stopTestCallQuality",value:function(){this.sendMessageToCallMachine({action:"stop-test-call-quality"})}},{key:"testConnectionQuality",value:(l=w(function*(e){iR()?(console.warn("testConnectionQuality() is deprecated: use testPeerToPeerCallQuality() instead"),t=yield this.testPeerToPeerCallQuality(e)):(console.warn("testConnectionQuality() is deprecated: use testCallQuality() instead"),t=yield this.testCallQuality());var t,r={result:t.result,secondsElapsed:t.secondsElapsed};return t.data&&(r.data={maxRTT:t.data.maxRoundTripTime,packetLoss:t.data.avgRecvPacketLoss}),r}),function(e){return l.apply(this,arguments)})},{key:"testPeerToPeerCallQuality",value:(c=w(function*(e){var t=this;if(a7(this._testCallInProgress,"testPeerToPeerCallQuality()"),this.needsLoad())try{yield this.load()}catch(e){return Promise.reject(e)}var r=e.videoTrack,n=e.duration;if(!this._validateVideoTrackForNetworkTests(r))throw Error("Video track error");return this._sharedTracks.videoTrackForConnectionQualityTest=r,new Promise(function(e,r){t.sendMessageToCallMachine({action:"test-p2p-call-quality",duration:n},function(t){t.error?r(t.error):e(t.results)})})}),function(e){return c.apply(this,arguments)})},{key:"stopTestConnectionQuality",value:function(){iR()?(console.warn("stopTestConnectionQuality() is deprecated: use testPeerToPeerCallQuality() and stopTestPeerToPeerCallQuality() instead"),this.stopTestPeerToPeerCallQuality()):(console.warn("stopTestConnectionQuality() is deprecated: use testCallQuality() and stopTestCallQuality() instead"),this.stopTestCallQuality())}},{key:"stopTestPeerToPeerCallQuality",value:function(){this.sendMessageToCallMachine({action:"stop-test-p2p-call-quality"})}},{key:"testNetworkConnectivity",value:(s=w(function*(e){var t=this;if(a7(this._testCallInProgress,"testNetworkConnectivity()"),this.needsLoad())try{yield this.load()}catch(e){return Promise.reject(e)}if(!this._validateVideoTrackForNetworkTests(e))throw Error("Video track error");return this._sharedTracks.videoTrackForNetworkConnectivityTest=e,new Promise(function(e,r){t.sendMessageToCallMachine({action:"test-network-connectivity"},function(t){t.error?r(t.error):e(t.results)})})}),function(e){return s.apply(this,arguments)})},{key:"abortTestNetworkConnectivity",value:function(){this.sendMessageToCallMachine({action:"abort-test-network-connectivity"})}},{key:"getCpuLoadStats",value:function(){var e=this;return new Promise(function(t){e._callState===r6?e.sendMessageToCallMachine({action:"get-cpu-load-stats"},function(e){t(e.cpuStats)}):t({cpuLoadState:void 0,cpuLoadStateReason:void 0,stats:{}})})}},{key:"_validateEncodingLayerHasValidProperties",value:function(e){var t;if(!((null===(t=Object.keys(e))||void 0===t?void 0:t.length)>0))throw Error("Empty encoding is not allowed. At least one of these valid keys should be specified:"+Object.values(aK))}},{key:"_validateVideoSendSettings",value:function(e,t){var r="screenVideo"===e?["default-screen-video","detail-optimized","motion-optimized","motion-and-detail-balanced"]:["default-video","bandwidth-optimized","bandwidth-and-quality-balanced","quality-optimized","adaptive-2-layers","adaptive-3-layers"],n="Video send settings should be either an object or one of the supported presets: ".concat(r.join());if("string"==typeof t){if(!r.includes(t))throw Error(n)}else{if("object"!==p(t))throw Error(n);if(!t.maxQuality&&!t.encodings&&void 0===t.allowAdaptiveLayers)throw Error("Video send settings must contain at least maxQuality, allowAdaptiveLayers or encodings attribute");if(t.maxQuality&&-1===["low","medium","high"].indexOf(t.maxQuality))throw Error("maxQuality must be either low, medium or high");if(t.encodings){var i=!1;switch(Object.keys(t.encodings).length){case 1:i=!t.encodings.low;break;case 2:i=!t.encodings.low||!t.encodings.medium;break;case 3:i=!t.encodings.low||!t.encodings.medium||!t.encodings.high;break;default:i=!0}if(i)throw Error("Encodings must be defined as: low, low and medium, or low, medium and high.");t.encodings.low&&this._validateEncodingLayerHasValidProperties(t.encodings.low),t.encodings.medium&&this._validateEncodingLayerHasValidProperties(t.encodings.medium),t.encodings.high&&this._validateEncodingLayerHasValidProperties(t.encodings.high)}}}},{key:"validateUpdateSendSettings",value:function(e){var t=this;if(!e||0===Object.keys(e).length)throw Error("Send settings must contain at least information for one track!");Object.entries(e).forEach(function(e){var r=T(e,2),n=r[0],i=r[1];t._validateVideoSendSettings(n,i)})}},{key:"updateSendSettings",value:function(e){var t=this;return this.validateUpdateSendSettings(e),this.needsLoad()?(this._preloadCache.sendSettings=e,{sendSettings:this._preloadCache.sendSettings}):new Promise(function(r,n){t.sendMessageToCallMachine({action:"update-send-settings",sendSettings:e},function(e){e.error?n(e.error):r(e.sendSettings)})})}},{key:"getSendSettings",value:function(){return this._sendSettings||this._preloadCache.sendSettings}},{key:"getLocalAudioLevel",value:function(){return this._localAudioLevel}},{key:"getRemoteParticipantsAudioLevel",value:function(){return this._remoteParticipantsAudioLevel}},{key:"getActiveSpeaker",value:function(){return or(),this._activeSpeaker}},{key:"setActiveSpeakerMode",value:function(e){return or(),this.sendMessageToCallMachine({action:"set-active-speaker-mode",enabled:e}),this}},{key:"activeSpeakerMode",value:function(){return or(),this._activeSpeakerMode}},{key:"subscribeToTracksAutomatically",value:function(){return this._preloadCache.subscribeToTracksAutomatically}},{key:"setSubscribeToTracksAutomatically",value:function(e){return a5(this._callState,"setSubscribeToTracksAutomatically()","Use the subscribeToTracksAutomatically configuration property."),this._preloadCache.subscribeToTracksAutomatically=e,this.sendMessageToCallMachine({action:"daily-method-subscribe-to-tracks-automatically",enabled:e}),this}},{key:"enumerateDevices",value:(o=w(function*(){var e=this;if(this._callObjectMode){var t=yield navigator.mediaDevices.enumerateDevices();return"Firefox"===iW()&&iz().major>115&&iz().major<123&&(t=t.filter(function(e){return"audiooutput"!==e.kind})),{devices:t.map(function(e){var t=JSON.parse(JSON.stringify(e));if(!iR()&&"videoinput"===e.kind&&e.getCapabilities){var r,n=e.getCapabilities();t.facing=(null==n||null===(r=n.facingMode)||void 0===r?void 0:r.length)>=1?n.facingMode[0]:void 0}return t})}}return new Promise(function(t){e.sendMessageToCallMachine({action:"enumerate-devices"},function(e){t({devices:e.devices})})})}),function(){return o.apply(this,arguments)})},{key:"sendAppMessage",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"*";if(a5(this._callState,"sendAppMessage()"),JSON.stringify(e).length>this._maxAppMessageSize)throw Error("Message data too large. Max size is "+this._maxAppMessageSize);return this.sendMessageToCallMachine({action:"app-msg",data:e,to:t}),this}},{key:"addFakeParticipant",value:function(e){return or(),a5(this._callState,"addFakeParticipant()"),this.sendMessageToCallMachine(aB({action:"add-fake-participant"},e)),this}},{key:"setShowNamesMode",value:function(e){return ot(this._callObjectMode,"setShowNamesMode()"),or(),e&&"always"!==e&&"never"!==e?console.error('setShowNamesMode argument should be "always", "never", or false'):this.sendMessageToCallMachine({action:"set-show-names",mode:e}),this}},{key:"setShowLocalVideo",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return ot(this._callObjectMode,"setShowLocalVideo()"),or(),a5(this._callState,"setShowLocalVideo()"),"boolean"!=typeof e?console.error("setShowLocalVideo only accepts a boolean value"):(this.sendMessageToCallMachine({action:"set-show-local-video",show:e}),this._showLocalVideo=e),this}},{key:"showLocalVideo",value:function(){return ot(this._callObjectMode,"showLocalVideo()"),or(),this._showLocalVideo}},{key:"setShowParticipantsBar",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return ot(this._callObjectMode,"setShowParticipantsBar()"),or(),a5(this._callState,"setShowParticipantsBar()"),"boolean"!=typeof e?console.error("setShowParticipantsBar only accepts a boolean value"):(this.sendMessageToCallMachine({action:"set-show-participants-bar",show:e}),this._showParticipantsBar=e),this}},{key:"showParticipantsBar",value:function(){return ot(this._callObjectMode,"showParticipantsBar()"),or(),this._showParticipantsBar}},{key:"customIntegrations",value:function(){return or(),ot(this._callObjectMode,"customIntegrations()"),this._customIntegrations}},{key:"setCustomIntegrations",value:function(e){return or(),ot(this._callObjectMode,"setCustomIntegrations()"),a5(this._callState,"setCustomIntegrations()"),op(e)&&(this.sendMessageToCallMachine({action:"set-custom-integrations",integrations:e}),this._customIntegrations=e),this}},{key:"startCustomIntegrations",value:function(e){var t=this;if(or(),ot(this._callObjectMode,"startCustomIntegrations()"),a5(this._callState,"startCustomIntegrations()"),Array.isArray(e)&&e.some(function(e){return"string"!=typeof e})||!Array.isArray(e)&&"string"!=typeof e)return console.error("startCustomIntegrations() only accepts string | string[]"),this;var r="string"==typeof e?[e]:e,n=r.filter(function(e){return!(e in t._customIntegrations)});return n.length?console.error("Can't find custom integration(s): \"".concat(n.join(", "),'"')):this.sendMessageToCallMachine({action:"start-custom-integrations",ids:r}),this}},{key:"stopCustomIntegrations",value:function(e){var t=this;if(or(),ot(this._callObjectMode,"stopCustomIntegrations()"),a5(this._callState,"stopCustomIntegrations()"),Array.isArray(e)&&e.some(function(e){return"string"!=typeof e})||!Array.isArray(e)&&"string"!=typeof e)return console.error("stopCustomIntegrations() only accepts string | string[]"),this;var r="string"==typeof e?[e]:e,n=r.filter(function(e){return!(e in t._customIntegrations)});return n.length?console.error("Can't find custom integration(s): \"".concat(n.join(", "),'"')):this.sendMessageToCallMachine({action:"stop-custom-integrations",ids:r}),this}},{key:"customTrayButtons",value:function(){return ot(this._callObjectMode,"customTrayButtons()"),or(),this._customTrayButtons}},{key:"updateCustomTrayButtons",value:function(e){return ot(this._callObjectMode,"updateCustomTrayButtons()"),or(),a5(this._callState,"updateCustomTrayButtons()"),oh(e)?(this.sendMessageToCallMachine({action:"update-custom-tray-buttons",btns:e}),this._customTrayButtons=e):console.error("updateCustomTrayButtons only accepts a dictionary of the type ".concat(JSON.stringify(aZ))),this}},{key:"theme",value:function(){return ot(this._callObjectMode,"theme()"),this.properties.theme}},{key:"setTheme",value:function(e){var t=this;return ot(this._callObjectMode,"setTheme()"),new Promise(function(r,n){try{t.validateProperties({theme:e}),t.properties.theme=aB({},e),t.sendMessageToCallMachine({action:"set-theme",theme:t.properties.theme});try{t.emitDailyJSEvent({action:nP,theme:t.properties.theme})}catch(e){console.log("could not emit 'theme-updated'",e)}r(t.properties.theme)}catch(e){n(e)}})}},{key:"requestFullscreen",value:(a=w(function*(){if(or(),this._iframe&&!document.fullscreenElement&&iF())try{(yield this._iframe.requestFullscreen)?this._iframe.requestFullscreen():this._iframe.webkitRequestFullscreen()}catch(e){console.log("could not make video call fullscreen",e)}}),function(){return a.apply(this,arguments)})},{key:"exitFullscreen",value:function(){or(),document.fullscreenElement?document.exitFullscreen():document.webkitFullscreenElement&&document.webkitExitFullscreen()}},{key:"getSidebarView",value:(i=w(function*(){var e=this;return this._callObjectMode?(console.error("getSidebarView is not available in callObject mode"),Promise.resolve(null)):new Promise(function(t){e.sendMessageToCallMachine({action:"get-sidebar-view"},function(e){t(e.view)})})}),function(){return i.apply(this,arguments)})},{key:"setSidebarView",value:function(e){return this._callObjectMode?console.error("setSidebarView is not available in callObject mode"):this.sendMessageToCallMachine({action:"set-sidebar-view",view:e}),this}},{key:"room",value:(n=w(function*(){var e=this,t=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).includeRoomConfigDefaults,r=void 0===t||t;return this._accessState.access===na||this.needsLoad()?this.properties.url?{roomUrlPendingJoin:this.properties.url}:null:new Promise(function(t){e.sendMessageToCallMachine({action:"lib-room-info",includeRoomConfigDefaults:r},function(e){delete e.action,delete e.callbackStamp,t(e)})})}),function(){return n.apply(this,arguments)})},{key:"geo",value:(r=w(function*(){try{var e=yield fetch("https://gs.daily.co/_ks_/x-swsl/:");return{current:(yield e.json()).geo}}catch(e){return console.error("geo lookup failed",e),{current:""}}}),function(){return r.apply(this,arguments)})},{key:"setNetworkTopology",value:(t=w(function*(e){var t=this;return or(),a5(this._callState,"setNetworkTopology()"),new Promise(function(r,n){t.sendMessageToCallMachine({action:"set-network-topology",opts:e},function(e){e.error?n({error:e.error}):r({workerId:e.workerId})})})}),function(e){return t.apply(this,arguments)})},{key:"getNetworkTopology",value:(e=w(function*(){var e=this;return new Promise(function(t,r){e.needsLoad()&&t({topology:"none"}),e.sendMessageToCallMachine({action:"get-network-topology"},function(e){e.error?r({error:e.error}):t({topology:e.topology})})})}),function(){return e.apply(this,arguments)})},{key:"setPlayNewParticipantSound",value:function(e){if(or(),"number"!=typeof e&&!0!==e&&!1!==e)throw Error("argument to setShouldPlayNewParticipantSound should be true, false, or a number, but is ".concat(e));this.sendMessageToCallMachine({action:"daily-method-set-play-ding",arg:e})}},{key:"on",value:function(e,t){return $.prototype.on.call(this,e,t)}},{key:"once",value:function(e,t){return $.prototype.once.call(this,e,t)}},{key:"off",value:function(e,t){return $.prototype.off.call(this,e,t)}},{key:"validateProperties",value:function(e){var t,r;if(null!=e&&null!==(t=e.dailyConfig)&&void 0!==t&&t.userMediaAudioConstraints){iR()||console.warn("userMediaAudioConstraints is deprecated. You can override constraints with inputSettings.audio.settings, found in DailyCallOptions.");var n,i,a=e.inputSettings||{};a.audio=(null===(n=e.inputSettings)||void 0===n?void 0:n.audio)||{},a.audio.settings=(null===(i=e.inputSettings)||void 0===i||null===(i=i.audio)||void 0===i?void 0:i.settings)||{},a.audio.settings=aB(aB({},a.audio.settings),e.dailyConfig.userMediaAudioConstraints),e.inputSettings=a,delete e.dailyConfig.userMediaAudioConstraints}if(null!=e&&null!==(r=e.dailyConfig)&&void 0!==r&&r.userMediaVideoConstraints){iR()||console.warn("userMediaVideoConstraints is deprecated. You can override constraints with inputSettings.video.settings, found in DailyCallOptions.");var o,s,c=e.inputSettings||{};c.video=(null===(o=e.inputSettings)||void 0===o?void 0:o.video)||{},c.video.settings=(null===(s=e.inputSettings)||void 0===s||null===(s=s.video)||void 0===s?void 0:s.settings)||{},c.video.settings=aB(aB({},c.video.settings),e.dailyConfig.userMediaVideoConstraints),e.inputSettings=c,delete e.dailyConfig.userMediaVideoConstraints}for(var l in e){if(!a1[l])throw Error("unrecognized property '".concat(l,"'"));if(a1[l].validate&&!a1[l].validate(e[l],this))throw Error("property '".concat(l,"': ").concat(a1[l].help))}}},{key:"assembleMeetingUrl",value:function(){var e,t,r=aB(aB({},this.properties),{},{emb:this.callClientId,embHref:encodeURIComponent(window.location.href),proxy:null!==(e=this.properties.dailyConfig)&&void 0!==e&&e.proxyUrl?encodeURIComponent(null===(t=this.properties.dailyConfig)||void 0===t?void 0:t.proxyUrl):void 0}),n=r.url.match(/\?/)?"&":"?";return r.url+n+Object.keys(a1).filter(function(e){return a1[e].queryString&&void 0!==r[e]}).map(function(e){return"".concat(a1[e].queryString,"=").concat(r[e])}).join("&")}},{key:"needsLoad",value:function(){return[r2,r3,r9,r8].includes(this._callState)}},{key:"sendMessageToCallMachine",value:function(e,t){if(this._destroyed&&(this._logUseAfterDestroy(),this.strictMode))throw Error("Use after destroy");this._messageChannel.sendMessageToCallMachine(e,t,this.callClientId,this._iframe)}},{key:"forwardPackagedMessageToCallMachine",value:function(e){this._messageChannel.forwardPackagedMessageToCallMachine(e,this._iframe,this.callClientId)}},{key:"addListenerForPackagedMessagesFromCallMachine",value:function(e){return this._messageChannel.addListenerForPackagedMessagesFromCallMachine(e,this.callClientId)}},{key:"removeListenerForPackagedMessagesFromCallMachine",value:function(e){this._messageChannel.removeListenerForPackagedMessagesFromCallMachine(e)}},{key:"handleMessageFromCallMachine",value:function(e){switch(e.action){case nO:this.sendMessageToCallMachine(aB({action:nI},this.properties));break;case"call-machine-initialized":this._callMachineInitialized=!0;var t={action:iA,level:"log",code:1011,stats:{event:"bundle load",time:"no-op"===this._bundleLoadTime?0:this._bundleLoadTime,preLoaded:"no-op"===this._bundleLoadTime,url:eu(this.properties.dailyConfig)}};this.sendMessageToCallMachine(t),this._delayDuplicateInstanceLog&&this._logDuplicateInstanceAttempt();break;case nN:this._loadedCallback&&(this._loadedCallback(),this._loadedCallback=null),this.emitDailyJSEvent(e);break;case nF:var r,n=aB({},e);delete n.internal,this._maxAppMessageSize=(null===(r=e.internal)||void 0===r?void 0:r._maxAppMessageSize)||4096,this._joinedCallback&&(this._joinedCallback(e.participants),this._joinedCallback=null),this.emitDailyJSEvent(n);break;case nU:case nB:if(this._callState===r9)return;if(e.participant&&e.participant.session_id){var i=e.participant.local?"local":e.participant.session_id;if(this._callObjectMode){var a=this._callMachine().store;ag(e.participant,a),am(e.participant,a),ay(e.participant,this._participants[i],a)}try{this.maybeParticipantTracksStopped(this._participants[i],e.participant),this.maybeParticipantTracksStarted(this._participants[i],e.participant),this.maybeEventRecordingStopped(this._participants[i],e.participant),this.maybeEventRecordingStarted(this._participants[i],e.participant)}catch(e){console.error("track events error",e)}this.compareEqualForParticipantUpdateEvent(e.participant,this._participants[i])||(this._participants[i]=aB({},e.participant),this.toggleParticipantAudioBasedOnNativeAudioFocus(),this.emitDailyJSEvent(e))}break;case nJ:if(e.participant&&e.participant.session_id){var o=this._participants[e.participant.session_id];o&&this.maybeParticipantTracksStopped(o,null),delete this._participants[e.participant.session_id],this.emitDailyJSEvent(e)}break;case nY:z(this._participantCounts,e.participantCounts)||(this._participantCounts=e.participantCounts,this.emitDailyJSEvent(e));break;case n$:var s={access:e.access};e.awaitingAccess&&(s.awaitingAccess=e.awaitingAccess),z(this._accessState,s)||(this._accessState=s,this.emitDailyJSEvent(e));break;case nq:if(e.meetingSession){this._meetingSessionSummary=e.meetingSession,this.emitDailyJSEvent(e);var c=aB(aB({},e),{},{action:"meeting-session-updated"});this.emitDailyJSEvent(c)}break;case iE:this._iframe&&!e.preserveIframe&&(this._iframe.src=""),this._updateCallState(r8),this.resetMeetingDependentVars(),this._loadedCallback&&(this._loadedCallback(e.errorMsg),this._loadedCallback=null),e.preserveIframe;var l,u=d(e,aV);null!=u&&null!==(l=u.error)&&void 0!==l&&l.details&&(u.error.details=JSON.parse(u.error.details)),this._maybeSendToSentry(e),this._joinedCallback&&(this._joinedCallback(null,u),this._joinedCallback=null),this.emitDailyJSEvent(u);break;case nV:this._callState!==r8&&this._updateCallState(r9),this.resetMeetingDependentVars(),this._resolveLeave&&(this._resolveLeave(),this._resolveLeave=null),this.emitDailyJSEvent(e);break;case"selected-devices-updated":e.devices&&this.emitDailyJSEvent(e);break;case il:var h=e.state,p=e.threshold,f=e.quality,v=h.state,g=h.reasons;v===this._network.networkState&&z(g,this._network.networkStateReasons)&&p===this._network.threshold&&f===this._network.quality||(this._network.networkState=v,this._network.networkStateReasons=g,this._network.quality=f,this._network.threshold=p,e.networkState=v,g.length&&(e.networkStateReasons=g),delete e.state,this.emitDailyJSEvent(e));break;case id:e&&e.cpuLoadState&&this.emitDailyJSEvent(e);break;case ih:e&&void 0!==e.faceCounts&&this.emitDailyJSEvent(e);break;case is:var m=e.activeSpeaker;this._activeSpeaker.peerId!==m.peerId&&(this._activeSpeaker.peerId=m.peerId,this.emitDailyJSEvent({action:e.action,activeSpeaker:this._activeSpeaker}));break;case"show-local-video-changed":if(this._callObjectMode)return;var y=e.show;this._showLocalVideo=y,this.emitDailyJSEvent({action:e.action,show:y});break;case ic:var _=e.enabled;this._activeSpeakerMode!==_&&(this._activeSpeakerMode=_,this.emitDailyJSEvent({action:e.action,enabled:this._activeSpeakerMode}));break;case nG:case nH:case nK:this._waitingParticipants=e.allWaitingParticipants,this.emitDailyJSEvent({action:e.action,participant:e.participant});break;case iS:z(this._receiveSettings,e.receiveSettings)||(this._receiveSettings=e.receiveSettings,this.emitDailyJSEvent({action:e.action,receiveSettings:e.receiveSettings}));break;case ik:this._maybeUpdateInputSettings(e.inputSettings);break;case"send-settings-updated":z(this._sendSettings,e.sendSettings)||(this._sendSettings=e.sendSettings,this._preloadCache.sendSettings=null,this.emitDailyJSEvent({action:e.action,sendSettings:e.sendSettings}));break;case"local-audio-level":this._localAudioLevel=e.audioLevel,this._preloadCache.localAudioLevelObserver=null,this.emitDailyJSEvent(e);break;case"remote-participants-audio-level":this._remoteParticipantsAudioLevel=e.participantsAudioLevel,this._preloadCache.remoteParticipantsAudioLevelObserver=null,this.emitDailyJSEvent(e);break;case ie:var b=e.session_id;this._rmpPlayerState[b]=e.playerState,this.emitDailyJSEvent(e);break;case ir:delete this._rmpPlayerState[e.session_id],this.emitDailyJSEvent(e);break;case it:var S=e.session_id,k=this._rmpPlayerState[S];k&&this.compareEqualForRMPUpdateEvent(k,e.remoteMediaPlayerState)||(this._rmpPlayerState[S]=e.remoteMediaPlayerState,this.emitDailyJSEvent(e));break;case"custom-button-click":case"sidebar-view-changed":this.emitDailyJSEvent(e);break;case nW:var w=this._meetingSessionState.topology!==(e.meetingSessionState&&e.meetingSessionState.topology);this._meetingSessionState=om(e.meetingSessionState,this._callObjectMode),(this._callObjectMode||w)&&this.emitDailyJSEvent(e);break;case ii:this._isScreenSharing=!0,this.emitDailyJSEvent(e);break;case ia:case io:this._isScreenSharing=!1,this.emitDailyJSEvent(e);break;case n2:case n3:case n4:case n5:case n6:case nZ:case n0:case n1:case nj:case nR:case n8:case n7:case"test-completed":case iu:case n9:case ig:case im:case iy:case i_:case iw:case ib:case"dialin-ready":case"dialin-connected":case"dialin-error":case"dialin-stopped":case"dialin-warning":case"dialout-connected":case"dialout-answered":case"dialout-error":case"dialout-stopped":case"dialout-warning":this.emitDailyJSEvent(e);break;case"request-fullscreen":this.requestFullscreen();break;case"request-exit-fullscreen":this.exitFullscreen()}}},{key:"maybeEventRecordingStopped",value:function(e,t){var r="record";e&&(t.local||!1!==t[r]||e[r]===t[r]||this.emitDailyJSEvent({action:n3}))}},{key:"maybeEventRecordingStarted",value:function(e,t){var r="record";e&&(t.local||!0!==t[r]||e[r]===t[r]||this.emitDailyJSEvent({action:n2}))}},{key:"_trackStatePlayable",value:function(e){return!(!e||e.state!==ni)}},{key:"_trackChanged",value:function(e,t){return(null==e?void 0:e.id)!==(null==t?void 0:t.id)}},{key:"maybeEventTrackStopped",value:function(e,t,r){var n,i,a=null!==(n=null==t?void 0:t.tracks[e])&&void 0!==n?n:null,o=null!==(i=null==r?void 0:r.tracks[e])&&void 0!==i?i:null,s=null==a?void 0:a.track;if(s){var c=this._trackStatePlayable(a),l=this._trackStatePlayable(o),u=this._trackChanged(s,null==o?void 0:o.track);c&&(l&&!u||this.emitDailyJSEvent({action:nX,track:s,participant:null!=r?r:t,type:e}))}}},{key:"maybeEventTrackStarted",value:function(e,t,r){var n,i,a=null!==(n=null==t?void 0:t.tracks[e])&&void 0!==n?n:null,o=null!==(i=null==r?void 0:r.tracks[e])&&void 0!==i?i:null,s=null==o?void 0:o.track;if(s){var c=this._trackStatePlayable(a),l=this._trackStatePlayable(o),u=this._trackChanged(null==a?void 0:a.track,s);l&&(c&&!u||this.emitDailyJSEvent({action:nQ,track:s,participant:r,type:e}))}}},{key:"maybeParticipantTracksStopped",value:function(e,t){if(e)for(var r in e.tracks)this.maybeEventTrackStopped(r,e,t)}},{key:"maybeParticipantTracksStarted",value:function(e,t){if(t)for(var r in t.tracks)this.maybeEventTrackStarted(r,e,t)}},{key:"compareEqualForRMPUpdateEvent",value:function(e,t){var r,n;return e.state===t.state&&(null===(r=e.settings)||void 0===r?void 0:r.volume)===(null===(n=t.settings)||void 0===n?void 0:n.volume)}},{key:"emitDailyJSEvent",value:function(e){try{e.callClientId=this.callClientId,this.emit(e.action,e)}catch(t){console.log("could not emit",e,t)}}},{key:"compareEqualForParticipantUpdateEvent",value:function(e,t){return!!z(e,t)&&(!e.videoTrack||!t.videoTrack||e.videoTrack.id===t.videoTrack.id&&e.videoTrack.muted===t.videoTrack.muted&&e.videoTrack.enabled===t.videoTrack.enabled)&&(!e.audioTrack||!t.audioTrack||e.audioTrack.id===t.audioTrack.id&&e.audioTrack.muted===t.audioTrack.muted&&e.audioTrack.enabled===t.audioTrack.enabled)}},{key:"nativeUtils",value:function(){return iR()?"undefined"==typeof DailyNativeUtils?(console.warn("in React Native, DailyNativeUtils is expected to be available"),null):DailyNativeUtils:null}},{key:"updateIsPreparingToJoin",value:function(e){this._updateCallState(this._callState,e)}},{key:"_updateCallState",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._isPreparingToJoin;if(e!==this._callState||t!==this._isPreparingToJoin){var r=this._callState,n=this._isPreparingToJoin;this._callState=e,this._isPreparingToJoin=t;var i=this._callState===r6;this.updateShowAndroidOngoingMeetingNotification(i);var a=a6(r,n),o=a6(this._callState,this._isPreparingToJoin);a!==o&&(this.updateKeepDeviceAwake(o),this.updateDeviceAudioMode(o),this.updateNoOpRecordingEnsuringBackgroundContinuity(o))}}},{key:"resetMeetingDependentVars",value:function(){this._participants={},this._participantCounts=aG,this._waitingParticipants={},this._activeSpeaker={},this._activeSpeakerMode=!1,this._didPreAuth=!1,this._accessState={access:na},this._finalSummaryOfPrevSession=this._meetingSessionSummary,this._meetingSessionSummary={},this._meetingSessionState=om(az,this._callObjectMode),this._isScreenSharing=!1,this._receiveSettings={},this._inputSettings=void 0,this._sendSettings={},this._localAudioLevel=0,this._isLocalAudioLevelObserverRunning=!1,this._remoteParticipantsAudioLevel={},this._isRemoteParticipantsAudioLevelObserverRunning=!1,this._maxAppMessageSize=4096,this._callMachineInitialized=!1,this._bundleLoadTime=void 0,this._preloadCache}},{key:"updateKeepDeviceAwake",value:function(e){iR()&&this.nativeUtils().setKeepDeviceAwake(e,this.callClientId)}},{key:"updateDeviceAudioMode",value:function(e){if(iR()&&!this.disableReactNativeAutoDeviceManagement("audio")){var t=e?this._nativeInCallAudioMode:"idle";this.nativeUtils().setAudioMode(t)}}},{key:"updateShowAndroidOngoingMeetingNotification",value:function(e){if(iR()&&this.nativeUtils().setShowOngoingMeetingNotification){var t,r,n,i;if(this.properties.reactNativeConfig&&this.properties.reactNativeConfig.androidInCallNotification){var a=this.properties.reactNativeConfig.androidInCallNotification;t=a.title,r=a.subtitle,n=a.iconName,i=a.disableForCustomOverride}i&&(e=!1),this.nativeUtils().setShowOngoingMeetingNotification(e,t,r,n,this.callClientId)}}},{key:"updateNoOpRecordingEnsuringBackgroundContinuity",value:function(e){iR()&&this.nativeUtils().enableNoOpRecordingEnsuringBackgroundContinuity&&this.nativeUtils().enableNoOpRecordingEnsuringBackgroundContinuity(e)}},{key:"toggleParticipantAudioBasedOnNativeAudioFocus",value:function(){var e;if(iR()){var t=null===(e=this._callMachine())||void 0===e||null===(e=e.store)||void 0===e?void 0:e.getState();for(var r in null==t?void 0:t.streams){var n=t.streams[r];n&&n.pendingTrack&&"audio"===n.pendingTrack.kind&&(n.pendingTrack.enabled=this._hasNativeAudioFocus)}}}},{key:"disableReactNativeAutoDeviceManagement",value:function(e){return this.properties.reactNativeConfig&&this.properties.reactNativeConfig.disableAutoDeviceManagement&&this.properties.reactNativeConfig.disableAutoDeviceManagement[e]}},{key:"absoluteUrl",value:function(e){if(void 0!==e){var t=document.createElement("a");return t.href=e,t.href}}},{key:"sayHello",value:function(){var e="hello, world.";return console.log(e),e}},{key:"_logUseAfterDestroy",value:function(){var e=Object.values(aq)[0];if(this.needsLoad()){if(e&&!e.needsLoad()){var t={action:iA,level:"error",code:this.strictMode?9995:9997};e.sendMessageToCallMachine(t)}else this.strictMode||console.error("You are are attempting to use a call instance that was previously destroyed, which is unsupported. Please remove `strictMode: false` from your constructor properties to enable strict mode to track down and fix this unsupported usage.")}else{var r={action:iA,level:"error",code:this.strictMode?9995:9997};this._messageChannel.sendMessageToCallMachine(r,null,this.callClientId,this._iframe)}}},{key:"_logDuplicateInstanceAttempt",value:function(){for(var e=0,t=Object.values(aq);e<t.length;e++){var r=t[e];r._callMachineInitialized?(r.sendMessageToCallMachine({action:iA,level:"warn",code:this.allowMultipleCallInstances?9993:9992}),r._delayDuplicateInstanceLog=!1):r._delayDuplicateInstanceLog=!0}}},{key:"_maybeSendToSentry",value:function(e){if(!(null!==(n=e.error)&&void 0!==n&&n.type&&(![nb,ny,ng].includes(e.error.type)||e.error.type===ng&&e.error.msg.includes("deleted")))){var t=null!==(i=this.properties)&&void 0!==i&&i.url?new URL(this.properties.url):void 0,r="production";t&&t.host.includes(".staging.daily")&&(r="staging");var n,i,a,o,s,c,l,u,d,h=new rT({dsn:"https://<EMAIL>/168844",transport:rN,stackParser:rB,integrations:(function(e){let t=[rt(),t7(),rq(),rY(),rQ(),r1(),rs(),r0()];return!1!==e.autoSessionTracking&&t.push(rK()),t})({}).filter(function(e){return!["BrowserApiErrors","Breadcrumbs","GlobalHandlers"].includes(e.name)}),environment:r}),p=new tg;if(p.setClient(h),h.init(),this.session_id&&p.setExtra("sessionId",this.session_id),this.properties){var f=aB({},this.properties);f.userName=f.userName?"[Filtered]":void 0,f.userData=f.userData?"[Filtered]":void 0,f.token=f.token?"[Filtered]":void 0,p.setExtra("properties",f)}if(t){var v=t.searchParams.get("domain");if(!v){var g=t.host.match(/(.*?)\./);v=g&&g[1]||""}v&&p.setTag("domain",v)}e.error&&(p.setTag("fatalErrorType",e.error.type),p.setExtra("errorDetails",e.error.details),(null===(s=e.error.details)||void 0===s?void 0:s.uri)&&p.setTag("serverAddress",e.error.details.uri),(null===(c=e.error.details)||void 0===c?void 0:c.workerGroup)&&p.setTag("workerGroup",e.error.details.workerGroup),(null===(l=e.error.details)||void 0===l?void 0:l.geoGroup)&&p.setTag("geoGroup",e.error.details.geoGroup),(null===(u=e.error.details)||void 0===u?void 0:u.on)&&p.setTag("connectionAttempt",e.error.details.on),null!==(d=e.error.details)&&void 0!==d&&d.bundleUrl&&(p.setTag("bundleUrl",e.error.details.bundleUrl),p.setTag("bundleError",e.error.details.sourceError.type))),p.setTags({callMode:this._callObjectMode?iR()?"reactNative":null!==(a=this.properties)&&void 0!==a&&null!==(a=a.dailyConfig)&&void 0!==a&&null!==(a=a.callMode)&&void 0!==a&&a.includes("prebuilt")?this.properties.dailyConfig.callMode:"custom":"prebuilt-frame",version:K.version()});var m=(null===(o=e.error)||void 0===o?void 0:o.msg)||e.errorMsg;p.captureException(Error(m))}}},{key:"_callMachine",value:function(){var e;return null===(e=window._daily)||void 0===e||null===(e=e.instances)||void 0===e||null===(e=e[this.callClientId])||void 0===e?void 0:e.callMachine}},{key:"_maybeUpdateInputSettings",value:function(e){if(!z(this._inputSettings,e)){var t=this._getInputSettings();this._inputSettings=e;var r=this._getInputSettings();z(t,r)||this.emitDailyJSEvent({action:ik,inputSettings:r})}}}],[{key:"supportedBrowser",value:function(){if(iR())return{supported:!0,mobile:!0,name:"React Native",version:null,supportsScreenShare:!0,supportsSfu:!0,supportsVideoProcessing:!1,supportsAudioProcessing:!1};var e=eo.getParser(ij());return{supported:!!iY(),mobile:"mobile"===e.getPlatformType(),name:e.getBrowserName(),version:e.getBrowserVersion(),supportsFullscreen:!!iF(),supportsScreenShare:!!(navigator&&navigator.mediaDevices&&navigator.mediaDevices.getDisplayMedia&&(function(e,t){if(!e||!t)return!0;switch(e){case"Chrome":return t.major>=75;case"Safari":return RTCRtpTransceiver.prototype.hasOwnProperty("currentDirection")&&(13!==t.major||0!==t.minor||0!==t.point);case"Firefox":return t.major>=67}return!0}(iW(),iz())||iR())),supportsSfu:!!iY(),supportsVideoProcessing:iB(),supportsAudioProcessing:iJ()}}},{key:"version",value:function(){return"0.79.0"}},{key:"createCallObject",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.layout="none",new K(null,e)}},{key:"wrap",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(or(),!e||!e.contentWindow||"string"!=typeof e.src)throw Error("DailyIframe::Wrap needs an iframe-like first argument");return t.layout||(t.customLayout?t.layout="custom-v1":t.layout="browser"),new K(e,t)}},{key:"createFrame",value:function(e,t){or(),e&&t?(r=e,n=t):e&&e.append?(r=e,n={}):(r=document.body,n=e||{});var r,n,i=n.iframeStyle;i||(i=r===document.body?{position:"fixed",border:"1px solid black",backgroundColor:"white",width:"375px",height:"450px",right:"1em",bottom:"1em"}:{border:0,width:"100%",height:"100%"});var a=document.createElement("iframe");window.navigator&&window.navigator.userAgent.match(/Chrome\/61\./)?a.allow="microphone, camera":a.allow="microphone; camera; autoplay; display-capture; screen-wake-lock",a.style.visibility="hidden",r.appendChild(a),a.style.visibility=null,Object.keys(i).forEach(function(e){return a.style[e]=i[e]}),n.layout||(n.customLayout?n.layout="custom-v1":n.layout="browser");try{return new K(a,n)}catch(e){throw r.removeChild(a),e}}},{key:"createTransparentFrame",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};or();var t=document.createElement("iframe");return t.allow="microphone; camera; autoplay",t.style.cssText="\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      border: 0;\n      pointer-events: none;\n    ",document.body.appendChild(t),e.layout||(e.layout="custom-v1"),K.wrap(t,e)}},{key:"getCallInstance",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;return e?aq[e]:Object.values(aq)[0]}}])}();function a4(e,t){var r={};for(var n in e)if(e[n]instanceof MediaStreamTrack)console.warn("MediaStreamTrack found in props or cache.",n),r[n]=iO;else if("dailyConfig"===n){if(e[n].modifyLocalSdpHook){var i=window._daily.instances[t].customCallbacks||{};i.modifyLocalSdpHook=e[n].modifyLocalSdpHook,window._daily.instances[t].customCallbacks=i,delete e[n].modifyLocalSdpHook}if(e[n].modifyRemoteSdpHook){var a=window._daily.instances[t].customCallbacks||{};a.modifyRemoteSdpHook=e[n].modifyRemoteSdpHook,window._daily.instances[t].customCallbacks=a,delete e[n].modifyRemoteSdpHook}r[n]=e[n]}else r[n]=e[n];return r}function a5(e){var t=arguments.length>2?arguments[2]:void 0;if(e!==r6){var r="".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"This daily-js method"," only supported after join.");throw t&&(r+=" ".concat(t)),console.error(r),Error(r)}}function a6(e,t){return[r5,r6].includes(e)||t}function a9(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"This daily-js method",n=arguments.length>3?arguments[3]:void 0;if(a6(e,t)){var i="".concat(r," not supported after joining a meeting.");throw n&&(i+=" ".concat(n)),console.error(i),Error(i)}}function a8(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"This daily-js method",r=arguments.length>2?arguments[2]:void 0;if(!e){var n="".concat(t,arguments.length>3&&void 0!==arguments[3]&&arguments[3]?" requires preAuth() or startCamera() to initialize call state.":" requires preAuth(), startCamera(), or join() to initialize call state.");throw r&&(n+=" ".concat(r)),console.error(n),Error(n)}}function a7(e){if(e){var t="A pre-call quality test is in progress. Please try ".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"This daily-js method"," again once testing has completed. Use stopTestCallQuality() to end it early.");throw console.error(t),Error(t)}}function oe(e){if(!e){var t="".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"This daily-js method"," is only supported on custom callObject instances");throw console.error(t),Error(t)}}function ot(e){if(e){var t="".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"This daily-js method"," is only supported as part of Daily's Prebuilt");throw console.error(t),Error(t)}}function or(){if(iR())throw Error("This daily-js method is not currently supported in React Native")}function on(){if(!iR())throw Error("This daily-js method is only supported in React Native")}function oi(e){var t;if(void 0===e)return!0;if("string"==typeof e)t=e;else try{t=JSON.stringify(e),z(JSON.parse(t),e)||console.warn("The userData provided will be modified when serialized.")}catch(e){throw Error("userData must be serializable to JSON: ".concat(e))}if(t.length>4096)throw Error("userData is too large (".concat(t.length," characters). Maximum size suppported is ").concat(4096,"."));return!0}function oa(e,t){for(var r=t.allowAllParticipantsKey,n=function(e){return!!(void 0===e.layer||Number.isInteger(e.layer)&&e.layer>=0||"inherit"===e.layer)},i=0,a=Object.entries(e);i<a.length;i++){var o=T(a[i],2),s=o[0],c=o[1];if(!function(e){var t=["local"];return r||t.push("*"),e&&!t.includes(e)}(s)||!(c&&!(c.video&&!n(c.video))&&!(c.screenVideo&&!n(c.screenVideo))))return!1}return!0}function oo(e){if("object"!==p(e))return!1;for(var t=0,r=Object.entries(e);t<r.length;t++){var n=T(r[t],2),i=n[0],a=n[1];switch(i){case"video":if("object"!==p(a))return!1;for(var o=0,s=Object.entries(a);o<s.length;o++){var c=T(s[o],2),l=c[0],u=c[1];switch(l){case"processor":if(!function(e){if(iR())return console.warn("Video processing is not yet supported in React Native"),!1;var t,r=["type","config"];return!!e&&"object"===p(e)&&!!("string"==typeof(t=e.type)&&(Object.values(iI).includes(t)||(console.error("inputSettings video processor type invalid"),0)))&&(!e.config||"object"===p(e.config)&&!!function(e,t){var r,n=Object.keys(t);if(0===n.length)return!0;var i="invalid object in inputSettings -> video -> processor -> config";switch(e){case iI.BGBLUR:return n.length>1||"strength"!==n[0]?(console.error(i),!1):!("number"!=typeof t.strength||t.strength<=0||t.strength>1||isNaN(t.strength))||(console.error("".concat(i,"; expected: {0 < strength <= 1}, got: ").concat(t.strength)),!1);case iI.BGIMAGE:return!(void 0!==t.source&&!("default"===t.source?(t.type="default",!0):t.source instanceof ArrayBuffer||(ed(t.source)?(t.type="url",!!function(e){var t=new URL(e),r=t.pathname;if("data:"===t.protocol)try{var n=r.substring(r.indexOf(":")+1,r.indexOf(";")).split("/")[1];return iD.includes(n)}catch(e){return console.error("failed to deduce blob content type",e),!1}var i=r.split(".").at(-1).toLowerCase().trim();return iD.includes(i)}(t.source)||(console.error("invalid image type; supported types: [".concat(iD.join(", "),"]")),!1)):isNaN(r=Number(t.source))||!Number.isInteger(r)||r<=0||r>10?(console.error("invalid image selection; must be an int, > 0, <= ".concat(10)),!1):(t.type="daily-preselect",!0))));default:return!0}}(e.type,e.config))&&(Object.keys(e).filter(function(e){return!r.includes(e)}).forEach(function(t){console.warn("invalid key inputSettings -> video -> processor : ".concat(t)),delete e[t]}),!0)}(u))return!1;break;case"settings":if(!oc(u))return!1;break;default:return!1}}break;case"audio":if("object"!==p(a))return!1;for(var d=0,h=Object.entries(a);d<h.length;d++){var f=T(h[d],2),v=f[0],g=f[1];switch(v){case"processor":if(!function(e){if(iR())return console.warn("Video processing is not yet supported in React Native"),!1;var t,r=["type"];return!!e&&"object"===p(e)&&(Object.keys(e).filter(function(e){return!r.includes(e)}).forEach(function(t){console.warn("invalid key inputSettings -> audio -> processor : ".concat(t)),delete e[t]}),!!("string"==typeof(t=e.type)&&(Object.values(iP).includes(t)||(console.error("inputSettings audio processor type invalid"),0))))}(g))return!1;break;case"settings":if(!oc(g))return!1;break;default:return!1}}break;default:return!1}}return!0}function os(e,t,r){var n,i=[];e.video&&e.video.processor&&(iB(null!==(n=null==t?void 0:t.useLegacyVideoProcessor)&&void 0!==n&&n)||(e.video.settings?delete e.video.processor:delete e.video,i.push("video"))),e.audio&&e.audio.processor&&(iJ()||(e.audio.settings?delete e.audio.processor:delete e.audio,i.push("audio"))),i.length>0&&console.error("Ignoring settings for browser- or platform-unsupported input processor(s): ".concat(i.join(", "))),e.audio&&e.audio.settings&&(e.audio.settings.customTrack?(r.audioTrack=e.audio.settings.customTrack,e.audio.settings={customTrack:iO}):delete r.audioTrack),e.video&&e.video.settings&&(e.video.settings.customTrack?(r.videoTrack=e.video.settings.customTrack,e.video.settings={customTrack:iO}):delete r.videoTrack)}function oc(e){return"object"===p(e)&&(!e.customTrack||e.customTrack instanceof MediaStreamTrack)}function ol(){var e=Object.values(iI).join(" | "),t=Object.values(iP).join(" | ");return"inputSettings must be of the form: { video?: { processor?: { type: [ ".concat(e," ], config?: {} } }, audio?: { processor: {type: [ ").concat(t," ] } } }")}function ou(e){var t=e.allowAllParticipantsKey;return"receiveSettings must be of the form { [<remote participant id> | ".concat(nl).concat(t?' | "'.concat("*",'"'):"","]: ")+'{ [video: [{ layer: [<non-negative integer> | "inherit"] } | "inherit"]], [screenVideo: [{ layer: [<non-negative integer> | "inherit"] } | "inherit"]] }}}'}function od(){return"customIntegrations should be an object of type ".concat(JSON.stringify(a0),".")}function oh(e){if(e&&"object"!==p(e)||Array.isArray(e))return console.error("customTrayButtons should be an Object of the type ".concat(JSON.stringify(aZ),".")),!1;if(e)for(var t=0,r=Object.entries(e);t<r.length;t++)for(var n=T(r[t],1)[0],i=0,a=Object.entries(e[n]);i<a.length;i++){var o=T(a[i],2),s=o[0],c=o[1],l=aZ.id[s];if(!l)return console.error("customTrayButton does not support key ".concat(s)),!1;switch(s){case"iconPath":case"iconPathDarkMode":if(!ed(c))return console.error("customTrayButton ".concat(s," should be a url.")),!1;break;case"visualState":if(!["default","sidebar-open","active"].includes(c))return console.error("customTrayButton ".concat(s," should be ").concat(l,". Got: ").concat(c)),!1;break;default:if(p(c)!==l)return console.error("customTrayButton ".concat(s," should be a ").concat(l,".")),!1}}return!0}function op(e){if(!e||e&&"object"!==p(e)||Array.isArray(e))return console.error(od()),!1;for(var t=function(e){return"".concat(e," should be ").concat(a0.id[e])},r=function(e,t){return console.error("customIntegration ".concat(e,": ").concat(t))},n=0,i=Object.entries(e);n<i.length;n++){var a=T(i[n],1)[0];if(!("label"in e[a]))return r(a,"label is required"),!1;if(!("location"in e[a]))return r(a,"location is required"),!1;if(!("src"in e[a])&&!("srcdoc"in e[a]))return r(a,"src or srcdoc is required"),!1;for(var o=0,s=Object.entries(e[a]);o<s.length;o++){var c=T(s[o],2),l=c[0],u=c[1];switch(l){case"allow":case"csp":case"name":case"referrerPolicy":case"sandbox":if("string"!=typeof u)return r(a,t(l)),!1;break;case"iconURL":if(!ed(u))return r(a,"".concat(l," should be a url")),!1;break;case"src":if("srcdoc"in e[a])return r(a,"cannot have both src and srcdoc"),!1;if(!ed(u))return r(a,'src "'.concat(u,'" is not a valid URL')),!1;break;case"srcdoc":if("src"in e[a])return r(a,"cannot have both src and srcdoc"),!1;if("string"!=typeof u)return r(a,t(l)),!1;break;case"location":if(!["main","sidebar"].includes(u))return r(a,t(l)),!1;break;case"controlledBy":if("*"!==u&&"owners"!==u&&(!Array.isArray(u)||u.some(function(e){return"string"!=typeof e})))return r(a,t(l)),!1;break;case"shared":if((!Array.isArray(u)||u.some(function(e){return"string"!=typeof e}))&&"owners"!==u&&"boolean"!=typeof u)return r(a,t(l)),!1;break;default:if(!a0.id[l])return console.error("customIntegration does not support key ".concat(l)),!1}}}return!0}function of(e,t){var r=e.sessionId,n=e.toEndPoint,i=e.callerId,a=e.useSipRefer;if(!r||!n)throw Error("".concat(t,"() requires a sessionId and toEndPoint"));if("string"!=typeof r||"string"!=typeof n)throw Error("Invalid paramater: sessionId and toEndPoint must be of type string");if(a&&!n.startsWith("sip:"))throw Error('"toEndPoint" must be a "sip" address');if(!n.startsWith("sip:")&&!n.startsWith("+"))throw Error("toEndPoint: ".concat(n,' must starts with either "sip:" or "+"'));if(i&&"string"!=typeof i)throw Error("callerId must be of type string");if(i&&!n.startsWith("+"))throw Error("callerId is only valid when transferring to a PSTN number")}function ov(e){if("object"!==p(e))throw Error('RemoteMediaPlayerSettings: must be "object" type');if(e.state&&!Object.values(iL).includes(e.state))throw Error("Invalid value for RemoteMediaPlayerSettings.state, valid values are: "+JSON.stringify(iL));if(e.volume){if("number"!=typeof e.volume)throw Error('RemoteMediaPlayerSettings.volume: must be "number" type');if(e.volume<0||e.volume>2)throw Error("RemoteMediaPlayerSettings.volume: must be between 0.0 - 2.0")}}function og(e,t,r){return!("number"!=typeof e||e<t||e>r)}function om(e,t){return e&&!t&&delete e.data,e}}}]);