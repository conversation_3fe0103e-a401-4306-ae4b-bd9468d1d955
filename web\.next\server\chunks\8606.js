"use strict";exports.id=8606,exports.ids=[8606],exports.modules={11860:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},24851:(e,t,r)=>{r.d(t,{CC:()=>T,Q6:()=>B,bL:()=>K,zi:()=>U});var n=r(43210),o=r(67969),i=r(70569),a=r(98599),l=r(11273),s=r(65551),d=r(43),u=r(83721),c=r(18853),f=r(14163),p=r(9510),m=r(60687),g=["PageUp","PageDown"],h=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],v={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},w="Slider",[x,y,b]=(0,p.N)(w),[D,R]=(0,l.A)(w,[b]),[j,S]=D(w),C=n.forwardRef((e,t)=>{let{name:r,min:a=0,max:l=100,step:d=1,orientation:u="horizontal",disabled:c=!1,minStepsBetweenThumbs:f=0,defaultValue:p=[a],value:v,onValueChange:w=()=>{},onValueCommit:y=()=>{},inverted:b=!1,form:D,...R}=e,S=n.useRef(new Set),C=n.useRef(0),E="horizontal"===u,[P=[],M]=(0,s.i)({prop:v,defaultProp:p,onChange:e=>{let t=[...S.current];t[C.current]?.focus(),w(e)}}),_=n.useRef(P);function N(e,t,{commit:r}={commit:!1}){let n=(String(d).split(".")[1]||"").length,i=function(e,t){let r=Math.pow(10,t);return Math.round(e*r)/r}(Math.round((e-a)/d)*d+a,n),s=(0,o.q)(i,[a,l]);M((e=[])=>{let n=function(e=[],t,r){let n=[...e];return n[r]=t,n.sort((e,t)=>e-t)}(e,s,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,r)=>e[r+1]-t))>=t;return!0}(n,f*d))return e;{C.current=n.indexOf(s);let t=String(n)!==String(e);return t&&r&&y(n),t?n:e}})}return(0,m.jsx)(j,{scope:e.__scopeSlider,name:r,disabled:c,min:a,max:l,valueIndexToChangeRef:C,thumbs:S.current,values:P,orientation:u,form:D,children:(0,m.jsx)(x.Provider,{scope:e.__scopeSlider,children:(0,m.jsx)(x.Slot,{scope:e.__scopeSlider,children:(0,m.jsx)(E?A:I,{"aria-disabled":c,"data-disabled":c?"":void 0,...R,ref:t,onPointerDown:(0,i.m)(R.onPointerDown,()=>{c||(_.current=P)}),min:a,max:l,inverted:b,onSlideStart:c?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let r=e.map(e=>Math.abs(e-t)),n=Math.min(...r);return r.indexOf(n)}(P,e);N(e,t)},onSlideMove:c?void 0:function(e){N(e,C.current)},onSlideEnd:c?void 0:function(){let e=_.current[C.current];P[C.current]!==e&&y(P)},onHomeKeyDown:()=>!c&&N(a,0,{commit:!0}),onEndKeyDown:()=>!c&&N(l,P.length-1,{commit:!0}),onStepKeyDown:({event:e,direction:t})=>{if(!c){let r=g.includes(e.key)||e.shiftKey&&h.includes(e.key),n=C.current;N(P[n]+d*(r?10:1)*t,n,{commit:!0})}}})})})})});C.displayName=w;var[E,P]=D(w,{startEdge:"left",endEdge:"right",size:"width",direction:1}),A=n.forwardRef((e,t)=>{let{min:r,max:o,dir:i,inverted:l,onSlideStart:s,onSlideMove:u,onSlideEnd:c,onStepKeyDown:f,...p}=e,[g,h]=n.useState(null),w=(0,a.s)(t,e=>h(e)),x=n.useRef(void 0),y=(0,d.jH)(i),b="ltr"===y,D=b&&!l||!b&&l;function R(e){let t=x.current||g.getBoundingClientRect(),n=z([0,t.width],D?[r,o]:[o,r]);return x.current=t,n(e-t.left)}return(0,m.jsx)(E,{scope:e.__scopeSlider,startEdge:D?"left":"right",endEdge:D?"right":"left",direction:D?1:-1,size:"width",children:(0,m.jsx)(M,{dir:y,"data-orientation":"horizontal",...p,ref:w,style:{...p.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=R(e.clientX);s?.(t)},onSlideMove:e=>{let t=R(e.clientX);u?.(t)},onSlideEnd:()=>{x.current=void 0,c?.()},onStepKeyDown:e=>{let t=v[D?"from-left":"from-right"].includes(e.key);f?.({event:e,direction:t?-1:1})}})})}),I=n.forwardRef((e,t)=>{let{min:r,max:o,inverted:i,onSlideStart:l,onSlideMove:s,onSlideEnd:d,onStepKeyDown:u,...c}=e,f=n.useRef(null),p=(0,a.s)(t,f),g=n.useRef(void 0),h=!i;function w(e){let t=g.current||f.current.getBoundingClientRect(),n=z([0,t.height],h?[o,r]:[r,o]);return g.current=t,n(e-t.top)}return(0,m.jsx)(E,{scope:e.__scopeSlider,startEdge:h?"bottom":"top",endEdge:h?"top":"bottom",size:"height",direction:h?1:-1,children:(0,m.jsx)(M,{"data-orientation":"vertical",...c,ref:p,style:{...c.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=w(e.clientY);l?.(t)},onSlideMove:e=>{let t=w(e.clientY);s?.(t)},onSlideEnd:()=>{g.current=void 0,d?.()},onStepKeyDown:e=>{let t=v[h?"from-bottom":"from-top"].includes(e.key);u?.({event:e,direction:t?-1:1})}})})}),M=n.forwardRef((e,t)=>{let{__scopeSlider:r,onSlideStart:n,onSlideMove:o,onSlideEnd:a,onHomeKeyDown:l,onEndKeyDown:s,onStepKeyDown:d,...u}=e,c=S(w,r);return(0,m.jsx)(f.sG.span,{...u,ref:t,onKeyDown:(0,i.m)(e.onKeyDown,e=>{"Home"===e.key?(l(e),e.preventDefault()):"End"===e.key?(s(e),e.preventDefault()):g.concat(h).includes(e.key)&&(d(e),e.preventDefault())}),onPointerDown:(0,i.m)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),c.thumbs.has(t)?t.focus():n(e)}),onPointerMove:(0,i.m)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&o(e)}),onPointerUp:(0,i.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),a(e))})})}),_="SliderTrack",N=n.forwardRef((e,t)=>{let{__scopeSlider:r,...n}=e,o=S(_,r);return(0,m.jsx)(f.sG.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...n,ref:t})});N.displayName=_;var O="SliderRange",k=n.forwardRef((e,t)=>{let{__scopeSlider:r,...o}=e,i=S(O,r),l=P(O,r),s=n.useRef(null),d=(0,a.s)(t,s),u=i.values.length,c=i.values.map(e=>$(e,i.min,i.max)),p=u>1?Math.min(...c):0,g=100-Math.max(...c);return(0,m.jsx)(f.sG.span,{"data-orientation":i.orientation,"data-disabled":i.disabled?"":void 0,...o,ref:d,style:{...e.style,[l.startEdge]:p+"%",[l.endEdge]:g+"%"}})});k.displayName=O;var F="SliderThumb",G=n.forwardRef((e,t)=>{let r=y(e.__scopeSlider),[o,i]=n.useState(null),l=(0,a.s)(t,e=>i(e)),s=n.useMemo(()=>o?r().findIndex(e=>e.ref.current===o):-1,[r,o]);return(0,m.jsx)(H,{...e,ref:l,index:s})}),H=n.forwardRef((e,t)=>{let{__scopeSlider:r,index:o,name:l,...s}=e,d=S(F,r),u=P(F,r),[p,g]=n.useState(null),h=(0,a.s)(t,e=>g(e)),v=!p||d.form||!!p.closest("form"),w=(0,c.X)(p),y=d.values[o],b=void 0===y?0:$(y,d.min,d.max),D=function(e,t){return t>2?`Value ${e+1} of ${t}`:2===t?["Minimum","Maximum"][e]:void 0}(o,d.values.length),R=w?.[u.size],j=R?function(e,t,r){let n=e/2,o=z([0,50],[0,n]);return(n-o(t)*r)*r}(R,b,u.direction):0;return n.useEffect(()=>{if(p)return d.thumbs.add(p),()=>{d.thumbs.delete(p)}},[p,d.thumbs]),(0,m.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[u.startEdge]:`calc(${b}% + ${j}px)`},children:[(0,m.jsx)(x.ItemSlot,{scope:e.__scopeSlider,children:(0,m.jsx)(f.sG.span,{role:"slider","aria-label":e["aria-label"]||D,"aria-valuemin":d.min,"aria-valuenow":y,"aria-valuemax":d.max,"aria-orientation":d.orientation,"data-orientation":d.orientation,"data-disabled":d.disabled?"":void 0,tabIndex:d.disabled?void 0:0,...s,ref:h,style:void 0===y?{display:"none"}:e.style,onFocus:(0,i.m)(e.onFocus,()=>{d.valueIndexToChangeRef.current=o})})}),v&&(0,m.jsx)(L,{name:l??(d.name?d.name+(d.values.length>1?"[]":""):void 0),form:d.form,value:y},o)]})});G.displayName=F;var L=e=>{let{value:t,...r}=e,o=n.useRef(null),i=(0,u.Z)(t);return n.useEffect(()=>{let e=o.current,r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(i!==t&&r){let n=new Event("input",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[i,t]),(0,m.jsx)("input",{style:{display:"none"},...r,ref:o,defaultValue:t})};function $(e,t,r){return(0,o.q)(100/(r-t)*(e-t),[0,100])}function z(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var K=C,T=N,B=k,U=G},26134:(e,t,r)=>{r.d(t,{G$:()=>X,Hs:()=>b,UC:()=>et,VY:()=>en,ZL:()=>Q,bL:()=>Z,bm:()=>eo,hE:()=>er,hJ:()=>ee,l9:()=>J,lG:()=>j});var n=r(43210),o=r(70569),i=r(98599),a=r(11273),l=r(96963),s=r(65551),d=r(31355),u=r(32547),c=r(25028),f=r(46059),p=r(14163),m=r(1359),g=r(42247),h=r(63376),v=r(8730),w=r(60687),x="Dialog",[y,b]=(0,a.A)(x),[D,R]=y(x),j=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:i,onOpenChange:a,modal:d=!0}=e,u=n.useRef(null),c=n.useRef(null),[f=!1,p]=(0,s.i)({prop:o,defaultProp:i,onChange:a});return(0,w.jsx)(D,{scope:t,triggerRef:u,contentRef:c,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:d,children:r})};j.displayName=x;var S="DialogTrigger",C=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=R(S,r),l=(0,i.s)(t,a.triggerRef);return(0,w.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":U(a.open),...n,ref:l,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});C.displayName=S;var E="DialogPortal",[P,A]=y(E,{forceMount:void 0}),I=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:i}=e,a=R(E,t);return(0,w.jsx)(P,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,w.jsx)(f.C,{present:r||a.open,children:(0,w.jsx)(c.Z,{asChild:!0,container:i,children:e})}))})};I.displayName=E;var M="DialogOverlay",_=n.forwardRef((e,t)=>{let r=A(M,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,i=R(M,e.__scopeDialog);return i.modal?(0,w.jsx)(f.C,{present:n||i.open,children:(0,w.jsx)(N,{...o,ref:t})}):null});_.displayName=M;var N=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(M,r);return(0,w.jsx)(g.A,{as:v.DX,allowPinchZoom:!0,shards:[o.contentRef],children:(0,w.jsx)(p.sG.div,{"data-state":U(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),O="DialogContent",k=n.forwardRef((e,t)=>{let r=A(O,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,i=R(O,e.__scopeDialog);return(0,w.jsx)(f.C,{present:n||i.open,children:i.modal?(0,w.jsx)(F,{...o,ref:t}):(0,w.jsx)(G,{...o,ref:t})})});k.displayName=O;var F=n.forwardRef((e,t)=>{let r=R(O,e.__scopeDialog),a=n.useRef(null),l=(0,i.s)(t,r.contentRef,a);return n.useEffect(()=>{let e=a.current;if(e)return(0,h.Eq)(e)},[]),(0,w.jsx)(H,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),G=n.forwardRef((e,t)=>{let r=R(O,e.__scopeDialog),o=n.useRef(!1),i=n.useRef(!1);return(0,w.jsx)(H,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),H=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:l,...s}=e,c=R(O,r),f=n.useRef(null),p=(0,i.s)(t,f);return(0,m.Oh)(),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(u.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:l,children:(0,w.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":U(c.open),...s,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(W,{titleId:c.titleId}),(0,w.jsx)(Y,{contentRef:f,descriptionId:c.descriptionId})]})]})}),L="DialogTitle",$=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(L,r);return(0,w.jsx)(p.sG.h2,{id:o.titleId,...n,ref:t})});$.displayName=L;var z="DialogDescription",K=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=R(z,r);return(0,w.jsx)(p.sG.p,{id:o.descriptionId,...n,ref:t})});K.displayName=z;var T="DialogClose",B=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=R(T,r);return(0,w.jsx)(p.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function U(e){return e?"open":"closed"}B.displayName=T;var q="DialogTitleWarning",[X,V]=(0,a.q)(q,{contentName:O,titleName:L,docsSlug:"dialog"}),W=({titleId:e})=>{let t=V(q),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},Y=({contentRef:e,descriptionId:t})=>{let r=V("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(o)},[o,e,t]),null},Z=j,J=C,Q=I,ee=_,et=k,er=$,en=K,eo=B},28559:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},78148:(e,t,r)=>{r.d(t,{b:()=>l});var n=r(43210),o=r(14163),i=r(60687),a=n.forwardRef((e,t)=>(0,i.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));a.displayName="Label";var l=a}};