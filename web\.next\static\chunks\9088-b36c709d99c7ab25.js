"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9088],{12421:(e,t,r)=>{r.d(t,{t:()=>s});var a=r(57297);async function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=localStorage.getItem("access_token");if(!r){let e=await (0,a.J1)();if(!e.success)throw Error("No authentication token available");r=e.newAccessToken}let s=new Headers(t.headers||{});s.has("Authorization")||s.set("Authorization","Bearer ".concat(r));let n=await fetch(e,{...t,headers:s});if(401===n.status||403===n.status){console.log("Token expired, attempting refresh...");let r=await (0,a.J1)();if(!r.success)throw console.error("Token refresh failed"),window.location.href="/login",Error("Authentication failed");console.log("Token refreshed, retrying request...");let s=new Headers(t.headers||{});return s.set("Authorization","Bearer ".concat(r.newAccessToken)),fetch(e,{...t,headers:s})}return n}},14853:(e,t,r)=>{r.d(t,{O:()=>u});var a=r(95155),s=r(1243),n=r(54416),i=r(55365),o=r(30285),l=r(35695),d=r(4672),c=r(12115);function u(e){let{credits:t,threshold:r}=e,u=(0,l.useRouter)(),{totalAvailable:A,organizationCreditThreshold:g,isLoading:h,totalMinutesAvailable:f,callPricePerMinute:x,monthlyAllowance:m,isConnected:v,hasValidData:w,lastSuccessfulFetch:b}=(0,d.I)(),[p,k]=(0,c.useState)(!1),y=void 0!==t?t:A,N=void 0!==r?r:g,j=2*N;if((0,c.useEffect)(()=>{let e="credit_warning_dismissed_".concat(j),t=localStorage.getItem(e),r=!1;if(t)try{let{timestamp:a}=JSON.parse(t);(r=Date.now()-a<72e5)||localStorage.removeItem(e)}catch(t){localStorage.removeItem(e)}k(r),console.log("CreditWarningAlert Debug:",{credits:y,threshold:N,warningThreshold:j,isDismissed:r,dismissedKey:e,dismissedData:t,shouldShow:!h&&!r&&y<j&&y>=N})},[j,y,N,h]),h)return console.log("CreditWarningAlert: Not showing - still loading"),null;if(!w)return console.log("CreditWarningAlert: Not showing - no valid data"),null;let C=b&&Date.now()-b<12e4;if(!v&&!C)return console.log("CreditWarningAlert: Not showing - connection lost and data is stale"),null;if(p)return console.log("CreditWarningAlert: Not showing - dismissed"),null;if(y>=j||y<N)return console.log("CreditWarningAlert: Not showing - outside threshold range",{credits:y,threshold:N,warningThreshold:j,belowWarning:y<j,aboveCritical:y>=N}),null;console.log("CreditWarningAlert: Showing warning alert");let E=x>0?y/x:0;return(0,a.jsx)("div",{className:"flex justify-center mb-3",children:(0,a.jsx)(i.Fc,{className:"border-yellow-200 bg-yellow-50 dark:bg-yellow-900/10 dark:border-yellow-800 w-auto p-2",children:(0,a.jsx)(i.TN,{className:"text-yellow-800 dark:text-yellow-200",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(s.A,{className:"h-4 w-4 text-yellow-600 dark:text-yellow-400 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:m>0?"".concat(Math.floor(f)," minutes remaining"):"$".concat(y.toFixed(2)," balance (").concat(Math.floor(E)," min)")}),(0,a.jsx)("span",{className:"text-xs text-yellow-600 dark:text-yellow-400 ml-2",children:"Credits running low - consider adding funds soon"})]}),(0,a.jsxs)("div",{className:"flex items-center gap-2 flex-shrink-0",children:[(0,a.jsx)(o.$,{size:"sm",variant:"outline",className:"h-7 px-3 text-xs border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/20",onClick:()=>u.push("/billing"),children:"Add Funds"}),(0,a.jsx)(o.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-yellow-600 hover:bg-yellow-100 dark:text-yellow-400 dark:hover:bg-yellow-900/20",onClick:()=>{let e={timestamp:Date.now(),warningThreshold:j};localStorage.setItem("credit_warning_dismissed_".concat(j),JSON.stringify(e)),k(!0)},children:(0,a.jsx)(n.A,{className:"h-3 w-3"})})]})]})})})})}},26126:(e,t,r)=>{r.d(t,{E:()=>l});var a=r(95155);r(12115);var s=r(99708),n=r(74466),i=r(59434);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:r,asChild:n=!1,...l}=e,d=n?s.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(o({variant:r}),t),...l})}},38009:(e,t,r)=>{r.d(t,{m:()=>d});var a=r(95155),s=r(85339),n=r(55365),i=r(30285),o=r(35695),l=r(4672);function d(e){let{credits:t,threshold:r}=e,d=(0,o.useRouter)(),{totalAvailable:c,organizationCreditThreshold:u,isLoading:A,totalMinutesAvailable:g,callPricePerMinute:h,monthlyAllowance:f,isConnected:x,hasValidData:m,lastSuccessfulFetch:v}=(0,l.I)(),w=void 0!==t?t:c,b=void 0!==r?r:u;if(A||!m)return null;let p=v&&Date.now()-v<12e4;if(!x&&!p||w>=b)return null;let k=h>0?w/h:0;return(0,a.jsx)("div",{className:"flex justify-center mb-5",children:(0,a.jsx)(n.Fc,{className:"border-red-200 bg-red-50 dark:bg-red-900/10 dark:border-red-800 p-1.5 w-auto",children:(0,a.jsx)(n.TN,{className:"text-red-700 dark:text-red-200",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(s.A,{className:"h-4 w-4 text-orange-600 dark:text-red-400 flex-shrink-0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-sm font-medium",children:f>0?"".concat(Math.floor(g)," minutes remaining"):"$".concat(w.toFixed(2)," balance (").concat(Math.floor(k)," min) it should be atleast $").concat(b.toFixed(0))}),(0,a.jsx)("span",{className:"text-xs text-red-600 dark:text-red-400 ml-2",children:"Add funds to continue making calls"})]}),(0,a.jsx)(i.$,{size:"sm",variant:"outline",className:"h-7 px-3 text-xs border-red-300 text-red-700 hover:bg-red-100 dark:border-red-600 dark:text-red-300 dark:hover:bg-red-900/20 flex-shrink-0",onClick:()=>d.push("/billing"),children:"Add Funds"})]})})})})}},55365:(e,t,r)=>{r.d(t,{Fc:()=>o,TN:()=>d,XL:()=>l});var a=r(95155);r(12115);var s=r(74466),n=r(59434);let i=(0,s.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-background text-foreground",destructive:"text-destructive-foreground [&>svg]:text-current *:data-[slot=alert-description]:text-destructive-foreground/80"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:r}),t),...s})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-title",className:(0,n.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r})}},57297:(e,t,r)=>{r.d(t,{HW:()=>n,J1:()=>o,_f:()=>i});var a=r(12421);let s="http://localhost:4000";async function n(){try{let e=await (0,a.t)("".concat(s,"/api/auth/me"),{method:"GET"});if(!e.ok)return{success:!1,error:"Error: ".concat(e.status)};let t=await e.json(),r=t.userId||t._id||t.id,n=t.email;if(r&&n)return{success:!0,user:{fullName:t.fullName||n.split("@")[0],userId:r,email:n,role:t.role||"user"}};return{success:!1,error:"Invalid user data received"}}catch(e){return console.error("Error fetching user data:",e),{success:!1,error:"An error occurred while fetching user data"}}}function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=setInterval(async()=>{if(localStorage.getItem("access_token"))try{await o()}catch(e){console.error("Background token refresh failed:",e)}},6e4*e);return()=>clearInterval(t)}async function o(){let e=localStorage.getItem("refresh_token");if(!e)return{success:!1};try{let t=await fetch("".concat(s,"/api/auth/refresh"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!t.ok)return{success:!1};let r=await t.json();if(r.access_token)return localStorage.setItem("access_token",r.access_token),{success:!0,newAccessToken:r.access_token};return{success:!1}}catch(e){return console.error("Token refresh error:",e),{success:!1}}}},62523:(e,t,r)=>{r.d(t,{p:()=>n});var a=r(95155);r(12115);var s=r(59434);function n(e){let{className:t,type:r,...n}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},62829:(e,t,r)=>{r.d(t,{A:()=>a});let a={src:"/_next/static/media/Binghatti-Lisa.85c81ecb.jpeg",height:1586,width:1586,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/2wBDAQoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/wgARCAAIAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAX/xAAUAQEAAAAAAAAAAAAAAAAAAAAC/9oADAMBAAIQAxAAAACeA//EABsQAAEFAQEAAAAAAAAAAAAAAAECAwQREwAi/9oACAEBAAE/AG6e3khuoyZAbQNDWYR6SO//xAAVEQEBAAAAAAAAAAAAAAAAAAABAP/aAAgBAgEBPwAL/8QAFhEAAwAAAAAAAAAAAAAAAAAAAAFB/9oACAEDAQE/AHD/2Q==",blurWidth:8,blurHeight:8}}}]);