"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[226],{226:(t,e,i)=>{let s;function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function r(t){let e=[{},{}];return null==t||t.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function o(t,e,i,s){if("function"==typeof e){let[n,o]=r(s);e=e(void 0!==i?i:t.custom,n,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,o]=r(s);e=e(void 0!==i?i:t.custom,n,o)}return e}function a(t,e,i){let s=t.getProps();return o(s,e,void 0!==i?i:s.custom,t)}function l(t){let e;return()=>(void 0===e&&(e=t()),e)}i.d(e,{P:()=>rv});let u=l(()=>void 0!==window.ScrollTimeline);class h{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t,e){let i=this.animations.map(i=>u()&&i.attachTimeline?i.attachTimeline(t):"function"==typeof e?e(i):void 0);return()=>{i.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class d extends h{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function c(t,e){return t?t[e]||t.default||t:void 0}function p(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function m(t){return"function"==typeof t}function f(t,e){t.timeline=e,t.onfinish=null}let v=t=>Array.isArray(t)&&"number"==typeof t[0],g={linearEasing:void 0},y=function(t,e){let i=l(t);return()=>{var t;return null!==(t=g[e])&&void 0!==t?t:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),x=(t,e,i)=>{let s=e-t;return 0===s?1:(i-t)/s},P=(t,e,i=10)=>{let s="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)s+=t(x(0,n-1,e))+", ";return`linear(${s.substring(0,s.length-2)})`},T=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,w={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:T([0,.65,.55,1]),circOut:T([.55,0,1,.45]),backIn:T([.31,.01,.66,-.59]),backOut:T([.33,1.53,.69,.99])},b={x:!1,y:!1};function S(t,e){let i=function(t,e,i){var s;if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(s=void 0,e.querySelectorAll(t));return i?Array.from(i):[]}return Array.from(t)}(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function A(t){return!("touch"===t.pointerType||b.x||b.y)}function E(t,e){let i=`${e}PointerCapture`;if(t.target instanceof Element&&i in t.target&&void 0!==t.pointerId)try{t.target[i](t.pointerId)}catch(t){}}let V=(t,e)=>!!e&&(t===e||V(t,e.parentElement)),M=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,C=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),D=new WeakSet;function k(t){return e=>{"Enter"===e.key&&t(e)}}function R(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let L=(t,e)=>{let i=t.currentTarget;if(!i)return;let s=k(()=>{if(D.has(i))return;R(i,"down");let t=k(()=>{R(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>R(i,"cancel"),e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)};function j(t){return M(t)&&!(b.x||b.y)}let F=t=>1e3*t,B=t=>t/1e3,O=t=>t,I=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],U=new Set(I),$=new Set(["width","height","top","left","right","bottom",...I]),N=t=>Array.isArray(t),W=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),z=t=>N(t)?t[t.length-1]||0:t,H={skipAnimations:!1,useManualTiming:!1},Y=["read","resolveKeyframes","update","preRender","render","postRender"],X={value:null,addProjectionMetrics:null};function K(t,e){let i=!1,s=!0,n={delta:0,timestamp:0,isProcessing:!1},r=()=>i=!0,o=Y.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,s=new Set,n=!1,r=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(e){o.has(e)&&(h.schedule(e),t()),l++,e(a)}let h={schedule:(t,e=!1,r=!1)=>{let a=r&&n?i:s;return e&&o.add(t),a.has(t)||a.add(t),t},cancel:t=>{s.delete(t),o.delete(t)},process:t=>{if(a=t,n){r=!0;return}n=!0,[i,s]=[s,i],i.forEach(u),e&&X.value&&X.value.frameloop[e].push(l),l=0,i.clear(),n=!1,r&&(r=!1,h.process(t))}};return h}(r,e?i:void 0),t),{}),{read:a,resolveKeyframes:l,update:u,preRender:h,render:d,postRender:c}=o,p=()=>{let r=H.useManualTiming?n.timestamp:performance.now();i=!1,H.useManualTiming||(n.delta=s?1e3/60:Math.max(Math.min(r-n.timestamp,40),1)),n.timestamp=r,n.isProcessing=!0,a.process(n),l.process(n),u.process(n),h.process(n),d.process(n),c.process(n),n.isProcessing=!1,i&&e&&(s=!1,t(p))},m=()=>{i=!0,s=!0,n.isProcessing||t(p)};return{schedule:Y.reduce((t,e)=>{let s=o[e];return t[e]=(t,e=!1,n=!1)=>(i||m(),s.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<Y.length;e++)o[Y[e]].cancel(t)},state:n,steps:o}}let{schedule:q,cancel:G,state:_,steps:Z}=K("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:O,!0);function J(){s=void 0}let Q={now:()=>(void 0===s&&Q.set(_.isProcessing||H.useManualTiming?_.timestamp:performance.now()),s),set:t=>{s=t,queueMicrotask(J)}};function tt(t,e){-1===t.indexOf(e)&&t.push(e)}function te(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class ti{constructor(){this.subscriptions=[]}add(t){return tt(this.subscriptions,t),()=>te(this.subscriptions,t)}notify(t,e,i){let s=this.subscriptions.length;if(s){if(1===s)this.subscriptions[0](t,e,i);else for(let n=0;n<s;n++){let s=this.subscriptions[n];s&&s(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let ts=t=>!isNaN(parseFloat(t)),tn={current:void 0};class tr{constructor(t,e={}){this.version="12.4.10",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=Q.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=Q.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=ts(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new ti);let i=this.events[t].add(e);return"change"===t?()=>{i(),q.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return tn.current&&tn.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=Q.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function to(t,e){return new tr(t,e)}let ta=t=>!!(t&&t.getVelocity);function tl(t,e){let i=t.getValue("willChange");if(ta(i)&&i.add)return i.add(e)}let tu=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),th="data-"+tu("framerAppearId"),td={current:!1},tc=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tp(t,e,i,s){if(t===e&&i===s)return O;let n=e=>(function(t,e,i,s,n){let r,o;let a=0;do(r=tc(o=e+(i-e)/2,s,n)-t)>0?i=o:e=o;while(Math.abs(r)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tc(n(t),e,s)}let tm=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,tf=t=>e=>1-t(1-e),tv=tp(.33,1.53,.69,.99),tg=tf(tv),ty=tm(tg),tx=t=>(t*=2)<1?.5*tg(t):.5*(2-Math.pow(2,-10*(t-1))),tP=t=>1-Math.sin(Math.acos(t)),tT=tf(tP),tw=tm(tP),tb=t=>/^0[^.\s]+$/u.test(t),tS=(t,e,i)=>i>e?e:i<t?t:i,tA={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},tE={...tA,transform:t=>tS(0,1,t)},tV={...tA,default:1},tM=t=>Math.round(1e5*t)/1e5,tC=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tD=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tk=(t,e)=>i=>!!("string"==typeof i&&tD.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tR=(t,e,i)=>s=>{if("string"!=typeof s)return s;let[n,r,o,a]=s.match(tC);return{[t]:parseFloat(n),[e]:parseFloat(r),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tL=t=>tS(0,255,t),tj={...tA,transform:t=>Math.round(tL(t))},tF={test:tk("rgb","red"),parse:tR("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+tj.transform(t)+", "+tj.transform(e)+", "+tj.transform(i)+", "+tM(tE.transform(s))+")"},tB={test:tk("#"),parse:function(t){let e="",i="",s="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,s+=s,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:n?parseInt(n,16)/255:1}},transform:tF.transform},tO=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tI=tO("deg"),tU=tO("%"),t$=tO("px"),tN=tO("vh"),tW=tO("vw"),tz={...tU,parse:t=>tU.parse(t)/100,transform:t=>tU.transform(100*t)},tH={test:tk("hsl","hue"),parse:tR("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:s=1})=>"hsla("+Math.round(t)+", "+tU.transform(tM(e))+", "+tU.transform(tM(i))+", "+tM(tE.transform(s))+")"},tY={test:t=>tF.test(t)||tB.test(t)||tH.test(t),parse:t=>tF.test(t)?tF.parse(t):tH.test(t)?tH.parse(t):tB.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tF.transform(t):tH.transform(t)},tX=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,tK="number",tq="color",tG=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function t_(t){let e=t.toString(),i=[],s={color:[],number:[],var:[]},n=[],r=0,o=e.replace(tG,t=>(tY.test(t)?(s.color.push(r),n.push(tq),i.push(tY.parse(t))):t.startsWith("var(")?(s.var.push(r),n.push("var"),i.push(t)):(s.number.push(r),n.push(tK),i.push(parseFloat(t))),++r,"${}")).split("${}");return{values:i,split:o,indexes:s,types:n}}function tZ(t){return t_(t).values}function tJ(t){let{split:e,types:i}=t_(t),s=e.length;return t=>{let n="";for(let r=0;r<s;r++)if(n+=e[r],void 0!==t[r]){let e=i[r];e===tK?n+=tM(t[r]):e===tq?n+=tY.transform(t[r]):n+=t[r]}return n}}let tQ=t=>"number"==typeof t?0:t,t0={test:function(t){var e,i;return isNaN(t)&&"string"==typeof t&&((null===(e=t.match(tC))||void 0===e?void 0:e.length)||0)+((null===(i=t.match(tX))||void 0===i?void 0:i.length)||0)>0},parse:tZ,createTransformer:tJ,getAnimatableNone:function(t){let e=tZ(t);return tJ(t)(e.map(tQ))}},t1=new Set(["brightness","contrast","saturate","opacity"]);function t5(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(tC)||[];if(!s)return t;let n=i.replace(s,""),r=+!!t1.has(e);return s!==i&&(r*=100),e+"("+r+n+")"}let t2=/\b([a-z-]*)\(.*?\)/gu,t3={...t0,getAnimatableNone:t=>{let e=t.match(t2);return e?e.map(t5).join(" "):t}},t9={...tA,transform:Math.round},t4={borderWidth:t$,borderTopWidth:t$,borderRightWidth:t$,borderBottomWidth:t$,borderLeftWidth:t$,borderRadius:t$,radius:t$,borderTopLeftRadius:t$,borderTopRightRadius:t$,borderBottomRightRadius:t$,borderBottomLeftRadius:t$,width:t$,maxWidth:t$,height:t$,maxHeight:t$,top:t$,right:t$,bottom:t$,left:t$,padding:t$,paddingTop:t$,paddingRight:t$,paddingBottom:t$,paddingLeft:t$,margin:t$,marginTop:t$,marginRight:t$,marginBottom:t$,marginLeft:t$,backgroundPositionX:t$,backgroundPositionY:t$,rotate:tI,rotateX:tI,rotateY:tI,rotateZ:tI,scale:tV,scaleX:tV,scaleY:tV,scaleZ:tV,skew:tI,skewX:tI,skewY:tI,distance:t$,translateX:t$,translateY:t$,translateZ:t$,x:t$,y:t$,z:t$,perspective:t$,transformPerspective:t$,opacity:tE,originX:tz,originY:tz,originZ:t$,zIndex:t9,size:t$,fillOpacity:tE,strokeOpacity:tE,numOctaves:t9},t6={...t4,color:tY,backgroundColor:tY,outlineColor:tY,fill:tY,stroke:tY,borderColor:tY,borderTopColor:tY,borderRightColor:tY,borderBottomColor:tY,borderLeftColor:tY,filter:t3,WebkitFilter:t3},t8=t=>t6[t];function t7(t,e){let i=t8(t);return i!==t3&&(i=t0),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let et=new Set(["auto","none","0"]),ee=t=>t===tA||t===t$,ei=(t,e)=>parseFloat(t.split(", ")[e]),es=(t,e)=>(i,{transform:s})=>{if("none"===s||!s)return 0;let n=s.match(/^matrix3d\((.+)\)$/u);if(n)return ei(n[1],e);{let e=s.match(/^matrix\((.+)\)$/u);return e?ei(e[1],t):0}},en=new Set(["x","y","z"]),er=I.filter(t=>!en.has(t)),eo={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:es(4,13),y:es(5,14)};eo.translateX=eo.x,eo.translateY=eo.y;let ea=new Set,el=!1,eu=!1;function eh(){if(eu){let t=Array.from(ea).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return er.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{var s;null===(s=t.getValue(e))||void 0===s||s.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eu=!1,el=!1,ea.forEach(t=>t.complete()),ea.clear()}function ed(){ea.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eu=!0)})}class ec{constructor(t,e,i,s,n,r=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=n,this.isAsync=r}scheduleResolve(){this.isScheduled=!0,this.isAsync?(ea.add(this),el||(el=!0,q.read(ed),q.resolveKeyframes(eh))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;for(let n=0;n<t.length;n++)if(null===t[n]){if(0===n){let n=null==s?void 0:s.get(),r=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let s=i.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===n&&s.set(t[0])}else t[n]=t[n-1]}}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),ea.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,ea.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let ep=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),em=t=>e=>"string"==typeof e&&e.startsWith(t),ef=em("--"),ev=em("var(--"),eg=t=>!!ev(t)&&ey.test(t.split("/*")[0].trim()),ey=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,ex=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,eP=t=>e=>e.test(t),eT=[tA,t$,tU,tI,tW,tN,{test:t=>"auto"===t,parse:t=>t}],ew=t=>eT.find(eP(t));class eb extends ec{constructor(t,e,i,s,n){super(t,e,i,s,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&eg(s=s.trim())){let n=function t(e,i,s=1){O(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,r]=function(t){let e=ex.exec(t);if(!e)return[,];let[,i,s,n]=e;return[`--${null!=i?i:s}`,n]}(e);if(!n)return;let o=window.getComputedStyle(i).getPropertyValue(n);if(o){let t=o.trim();return ep(t)?parseFloat(t):t}return eg(r)?t(r,i,s+1):r}(s,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!$.has(i)||2!==t.length)return;let[s,n]=t,r=ew(s),o=ew(n);if(r!==o){if(ee(r)&&ee(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||tb(s))&&i.push(e)}i.length&&function(t,e,i){let s,n=0;for(;n<t.length&&!s;){let e=t[n];"string"==typeof e&&!et.has(e)&&t_(e).values.length&&(s=t[n]),n++}if(s&&i)for(let n of e)t[n]=t7(i,s)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=eo[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){var t;let{element:e,name:i,unresolvedKeyframes:s}=this;if(!e||!e.current)return;let n=e.getValue(i);n&&n.jump(this.measuredOrigin,!1);let r=s.length-1,o=s[r];s[r]=eo[i](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),(null===(t=this.removedTransforms)||void 0===t?void 0:t.length)&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let eS=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(t0.test(t)||"0"===t)&&!t.startsWith("url(")),eA=t=>null!==t;function eE(t,{repeat:e,repeatType:i="loop"},s){let n=t.filter(eA),r=e&&"loop"!==i&&e%2==1?0:n.length-1;return r&&void 0!==s?s:n[r]}class eV{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:r="loop",...o}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=Q.now(),this.options={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:n,repeatType:r,...o},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(ed(),eh()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=Q.now(),this.hasAttemptedResolve=!0;let{name:i,type:s,velocity:n,delay:r,onComplete:o,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(t,e,i,s){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],o=eS(n,e),a=eS(r,e);return O(o===a,`You are trying to animate ${e} from "${n}" to "${r}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${r} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||m(i))&&s)}(t,i,s,n)){if(td.current||!r){a&&a(eE(t,this.options,e)),o&&o(),this.resolveFinishedPromise();return}this.options.duration=0}let u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}let eM={layout:0,mainThread:0,waapi:0},eC=(t,e,i)=>t+(e-t)*i;function eD(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function ek(t,e){return i=>i>0?e:t}let eR=(t,e,i)=>{let s=t*t,n=i*(e*e-s)+s;return n<0?0:Math.sqrt(n)},eL=[tB,tF,tH],ej=t=>eL.find(e=>e.test(t));function eF(t){let e=ej(t);if(O(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tH&&(i=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let n=0,r=0,o=0;if(e/=100){let s=i<.5?i*(1+e):i+e-i*e,a=2*i-s;n=eD(a,s,t+1/3),r=eD(a,s,t),o=eD(a,s,t-1/3)}else n=r=o=i;return{red:Math.round(255*n),green:Math.round(255*r),blue:Math.round(255*o),alpha:s}}(i)),i}let eB=(t,e)=>{let i=eF(t),s=eF(e);if(!i||!s)return ek(t,e);let n={...i};return t=>(n.red=eR(i.red,s.red,t),n.green=eR(i.green,s.green,t),n.blue=eR(i.blue,s.blue,t),n.alpha=eC(i.alpha,s.alpha,t),tF.transform(n))},eO=(t,e)=>i=>e(t(i)),eI=(...t)=>t.reduce(eO),eU=new Set(["none","hidden"]);function e$(t,e){return i=>eC(t,e,i)}function eN(t){return"number"==typeof t?e$:"string"==typeof t?eg(t)?ek:tY.test(t)?eB:eH:Array.isArray(t)?eW:"object"==typeof t?tY.test(t)?eB:ez:ek}function eW(t,e){let i=[...t],s=i.length,n=t.map((t,i)=>eN(t)(t,e[i]));return t=>{for(let e=0;e<s;e++)i[e]=n[e](t);return i}}function ez(t,e){let i={...t,...e},s={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(s[n]=eN(t[n])(t[n],e[n]));return t=>{for(let e in s)i[e]=s[e](t);return i}}let eH=(t,e)=>{let i=t0.createTransformer(e),s=t_(t),n=t_(e);return s.indexes.var.length===n.indexes.var.length&&s.indexes.color.length===n.indexes.color.length&&s.indexes.number.length>=n.indexes.number.length?eU.has(t)&&!n.values.length||eU.has(e)&&!s.values.length?function(t,e){return eU.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):eI(eW(function(t,e){var i;let s=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let o=e.types[r],a=t.indexes[o][n[o]],l=null!==(i=t.values[a])&&void 0!==i?i:0;s[r]=l,n[o]++}return s}(s,n),n.values),i):(O(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),ek(t,e))};function eY(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?eC(t,e,i):eN(t)(t,e)}function eX(t,e,i){var s,n;let r=Math.max(e-5,0);return s=i-t(r),(n=e-r)?1e3/n*s:0}let eK={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function eq(t,e){return t*Math.sqrt(1-e*e)}let eG=["duration","bounce"],e_=["stiffness","damping","mass"];function eZ(t,e){return e.some(e=>void 0!==t[e])}function eJ(t=eK.visualDuration,e=eK.bounce){let i;let s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:r}=s,o=s.keyframes[0],a=s.keyframes[s.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:m,isResolvedFromDuration:f}=function(t){let e={velocity:eK.velocity,stiffness:eK.stiffness,damping:eK.damping,mass:eK.mass,isResolvedFromDuration:!1,...t};if(!eZ(t,e_)&&eZ(t,eG)){if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,n=2*tS(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:eK.mass,stiffness:s,damping:n}}else{let i=function({duration:t=eK.duration,bounce:e=eK.bounce,velocity:i=eK.velocity,mass:s=eK.mass}){let n,r;O(t<=F(eK.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=tS(eK.minDamping,eK.maxDamping,o),t=tS(eK.minDuration,eK.maxDuration,B(t)),o<1?(n=e=>{let s=e*o,n=s*t;return .001-(s-i)/eq(e,o)*Math.exp(-n)},r=e=>{let s=e*o*t,r=Math.pow(o,2)*Math.pow(e,2)*t,a=Math.exp(-s),l=eq(Math.pow(e,2),o);return(s*i+i-r)*a*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(n,r,5/t);if(t=F(t),isNaN(a))return{stiffness:eK.stiffness,damping:eK.damping,duration:t};{let e=Math.pow(a,2)*s;return{stiffness:e,damping:2*o*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:eK.mass}).isResolvedFromDuration=!0}}return e}({...s,velocity:-B(s.velocity||0)}),v=m||0,g=h/(2*Math.sqrt(u*d)),y=a-o,x=B(Math.sqrt(u/d)),T=5>Math.abs(y);if(n||(n=T?eK.restSpeed.granular:eK.restSpeed.default),r||(r=T?eK.restDelta.granular:eK.restDelta.default),g<1){let t=eq(x,g);i=e=>a-Math.exp(-g*x*e)*((v+g*x*y)/t*Math.sin(t*e)+y*Math.cos(t*e))}else if(1===g)i=t=>a-Math.exp(-x*t)*(y+(v+x*y)*t);else{let t=x*Math.sqrt(g*g-1);i=e=>{let i=Math.exp(-g*x*e),s=Math.min(t*e,300);return a-i*((v+g*x*y)*Math.sinh(s)+t*y*Math.cosh(s))/t}}let w={calculatedDuration:f&&c||null,next:t=>{let e=i(t);if(f)l.done=t>=c;else{let s=0;g<1&&(s=0===t?F(v):eX(i,t,e));let o=Math.abs(a-e)<=r;l.done=Math.abs(s)<=n&&o}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(p(w),2e4),e=P(e=>w.next(t*e).value,t,30);return t+"ms "+e}};return w}function eQ({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:n=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c;let p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,v=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,g=i*e,y=p+g,x=void 0===o?y:o(y);x!==y&&(g=x-p);let P=t=>-g*Math.exp(-t/s),T=t=>x+P(t),w=t=>{let e=P(t),i=T(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},b=t=>{f(m.value)&&(d=t,c=eJ({keyframes:[m.value,v(m.value)],velocity:eX(T,t,m.value),damping:n,stiffness:r,restDelta:u,restSpeed:h}))};return b(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,w(t),b(t)),void 0!==d&&t>=d)?c.next(t-d):(e||w(t),m)}}}let e0=tp(.42,0,1,1),e1=tp(0,0,.58,1),e5=tp(.42,0,.58,1),e2=t=>Array.isArray(t)&&"number"!=typeof t[0],e3={linear:O,easeIn:e0,easeInOut:e5,easeOut:e1,circIn:tP,circInOut:tw,circOut:tT,backIn:tg,backInOut:ty,backOut:tv,anticipate:tx},e9=t=>{if(v(t)){O(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,s,n]=t;return tp(e,i,s,n)}return"string"==typeof t?(O(void 0!==e3[t],`Invalid easing type '${t}'`),e3[t]):t};function e4({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){let n=e2(s)?s.map(e9):e9(s),r={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:s,mixer:n}={}){let r=t.length;if(O(r===e.length,"Both input and output ranges must be the same length"),1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let s=[],n=i||eY,r=t.length-1;for(let i=0;i<r;i++){let r=n(t[i],t[i+1]);e&&(r=eI(Array.isArray(e)?e[i]||O:e,r)),s.push(r)}return s}(e,s,n),l=a.length,u=i=>{if(o&&i<t[0])return e[0];let s=0;if(l>1)for(;s<t.length-2&&!(i<t[s+1]);s++);let n=x(t[s],t[s+1],i);return a[s](n)};return i?e=>u(tS(t[0],t[r-1],e)):u}((i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let n=x(0,e,s);t.push(eC(i,1,n))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(n)?n:e.map(()=>n||e5).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(r.value=o(e),r.done=e>=t,r)}}let e6=t=>{let e=({timestamp:e})=>t(e);return{start:()=>q.update(e,!0),stop:()=>G(e),now:()=>_.isProcessing?_.timestamp:Q.now()}},e8={decay:eQ,inertia:eQ,tween:e4,keyframes:e4,spring:eJ},e7=t=>t/100;class it extends eV{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()};let{name:e,motionValue:i,element:s,keyframes:n}=this.options,r=(null==s?void 0:s.KeyframeResolver)||ec;this.resolver=new r(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,s),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){let e,i;let{type:s="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:o,velocity:a=0}=this.options,l=m(s)?s:e8[s]||e4;l!==e4&&"number"!=typeof t[0]&&(e=eI(e7,eY(t[0],t[1])),t=[0,100]);let u=l({...this.options,keyframes:t});"mirror"===o&&(i=l({...this.options,keyframes:[...t].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=p(u));let{calculatedDuration:h}=u,d=h+r;return{generator:u,mirroredGenerator:i,mapPercentToKeyframes:e,calculatedDuration:h,resolvedDuration:d,totalDuration:d*(n+1)-r}}onPostResolved(){let{autoplay:t=!0}=this.options;eM.mainThread++,this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){let{resolved:i}=this;if(!i){let{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}let{finalKeyframe:s,generator:n,mirroredGenerator:r,mapPercentToKeyframes:o,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:h}=i;if(null===this.startTime)return n.next(0);let{delay:d,repeat:c,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;let v=this.currentTime-d*(this.speed>=0?1:-1),g=this.speed>=0?v<0:v>u;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let y=this.currentTime,x=n;if(c){let t=Math.min(this.currentTime,u)/h,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/h)):"mirror"===p&&(x=r)),y=tS(0,1,i)*h}let P=g?{done:!1,value:a[0]}:x.next(y);o&&(P.value=o(P.value));let{done:T}=P;g||null===l||(T=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let w=null===this.holdTime&&("finished"===this.state||"running"===this.state&&T);return w&&void 0!==s&&(P.value=eE(a,this.options,s)),f&&f(P.value),w&&this.finish(),P}get duration(){let{resolved:t}=this;return t?B(t.calculatedDuration):0}get time(){return B(this.currentTime)}set time(t){t=F(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=B(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:t=e6,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let s=this.driver.now();null!==this.holdTime?this.startTime=s-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=s):this.startTime=null!=i?i:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!==(t=this.currentTime)&&void 0!==t?t:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel(),eM.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}let ie=new Set(["opacity","clipPath","filter","transform"]),ii=l(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),is={anticipate:tx,backInOut:ty,circInOut:tw};class ir extends eV{constructor(t){super(t);let{name:e,motionValue:i,element:s,keyframes:n}=this.options;this.resolver=new eb(n,(t,e)=>this.onKeyframesResolved(t,e),e,i,s),this.resolver.scheduleResolve()}initPlayback(t,e){var i;let{duration:s=300,times:n,ease:r,type:o,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if("string"==typeof r&&y()&&r in is&&(r=is[r]),m((i=this.options).type)||"spring"===i.type||!function t(e){return!!("function"==typeof e&&y()||!e||"string"==typeof e&&(e in w||y())||v(e)||Array.isArray(e)&&e.every(t))}(i.ease)){let{onComplete:e,onUpdate:i,motionValue:a,element:l,...u}=this.options,h=function(t,e){let i=new it({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0}),s={done:!1,value:t[0]},n=[],r=0;for(;!s.done&&r<2e4;)n.push((s=i.sample(r)).value),r+=10;return{times:void 0,keyframes:n,duration:r-10,ease:"linear"}}(t,u);1===(t=h.keyframes).length&&(t[1]=t[0]),s=h.duration,n=h.times,r=h.ease,o="keyframes"}let h=function(t,e,i,{delay:s=0,duration:n=300,repeat:r=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){let u={[e]:i};l&&(u.offset=l);let h=function t(e,i){if(e)return"function"==typeof e&&y()?P(e,i):v(e)?T(e):Array.isArray(e)?e.map(e=>t(e,i)||w.easeOut):w[e]}(a,n);Array.isArray(h)&&(u.easing=h),X.value&&eM.waapi++;let d=t.animate(u,{delay:s,duration:n,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal"});return X.value&&d.finished.finally(()=>{eM.waapi--}),d}(a.owner.current,l,t,{...this.options,duration:s,times:n,ease:r});return h.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(f(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{let{onComplete:i}=this.options;a.set(eE(t,this.options,e)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:s,times:n,type:o,ease:r,keyframes:t}}get duration(){let{resolved:t}=this;if(!t)return 0;let{duration:e}=t;return B(e)}get time(){let{resolved:t}=this;if(!t)return 0;let{animation:e}=t;return B(e.currentTime||0)}set time(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.currentTime=F(t)}get speed(){let{resolved:t}=this;if(!t)return 1;let{animation:e}=t;return e.playbackRate}set speed(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.playbackRate=t}get state(){let{resolved:t}=this;if(!t)return"idle";let{animation:e}=t;return e.playState}get startTime(){let{resolved:t}=this;if(!t)return null;let{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){let{resolved:e}=this;if(!e)return O;let{animation:i}=e;f(i,t)}else this.pendingTimeline=t;return O}play(){if(this.isStopped)return;let{resolved:t}=this;if(!t)return;let{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){let{resolved:t}=this;if(!t)return;let{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:t}=this;if(!t)return;let{animation:e,keyframes:i,duration:s,type:n,ease:r,times:o}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){let{motionValue:t,onUpdate:e,onComplete:a,element:l,...u}=this.options,h=new it({...u,keyframes:i,duration:s,type:n,ease:r,times:o,isGenerator:!0}),d=F(this.time);t.setWithVelocity(h.sample(d-10).value,h.sample(d).value,10)}let{onStop:a}=this.options;a&&a(),this.cancel()}complete(){let{resolved:t}=this;t&&t.animation.finish()}cancel(){let{resolved:t}=this;t&&t.animation.cancel()}static supports(t){let{motionValue:e,name:i,repeatDelay:s,repeatType:n,damping:r,type:o}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return ii()&&i&&ie.has(i)&&!a&&!l&&!s&&"mirror"!==n&&0!==r&&"inertia"!==o}}let io={type:"spring",stiffness:500,damping:25,restSpeed:10},ia=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),il={type:"keyframes",duration:.8},iu={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ih=(t,{keyframes:e})=>e.length>2?il:U.has(t)?t.startsWith("scale")?ia(e[1]):io:iu,id=(t,e,i,s={},n,r)=>o=>{let a=c(s,t)||{},l=a.delay||s.delay||0,{elapsed:u=0}=s;u-=F(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:r?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:n,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&(h={...h,...ih(t,h)}),h.duration&&(h.duration=F(h.duration)),h.repeatDelay&&(h.repeatDelay=F(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let p=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0!==h.delay||(p=!0)),(td.current||H.skipAnimations)&&(p=!0,h.duration=0,h.delay=0),p&&!r&&void 0!==e.get()){let t=eE(h.keyframes,a);if(void 0!==t)return q.update(()=>{h.onUpdate(t),h.onComplete()}),new d([])}return!r&&ir.supports(h)?new ir(h):new it(h)};function ic(t,e,{delay:i=0,transitionOverride:s,type:n}={}){var r;let{transition:o=t.getDefaultTransition(),transitionEnd:l,...u}=e;s&&(o=s);let h=[],d=n&&t.animationState&&t.animationState.getState()[n];for(let e in u){let s=t.getValue(e,null!==(r=t.latestValues[e])&&void 0!==r?r:null),n=u[e];if(void 0===n||d&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(d,e))continue;let a={delay:i,...c(o||{},e)},l=!1;if(window.MotionHandoffAnimation){let i=t.props[th];if(i){let t=window.MotionHandoffAnimation(i,e,q);null!==t&&(a.startTime=t,l=!0)}}tl(t,e),s.start(id(e,s,n,t.shouldReduceMotion&&$.has(e)?{type:!1}:a,t,l));let p=s.animation;p&&h.push(p)}return l&&Promise.all(h).then(()=>{q.update(()=>{l&&function(t,e){let{transitionEnd:i={},transition:s={},...n}=a(t,e)||{};for(let e in n={...n,...i}){let i=z(n[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,to(i))}}(t,l)})}),h}function ip(t,e,i={}){var s;let n=a(t,e,"exit"===i.type?null===(s=t.presenceContext)||void 0===s?void 0:s.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=n||{};i.transitionOverride&&(r=i.transitionOverride);let o=n?()=>Promise.all(ic(t,n,i)):()=>Promise.resolve(),l=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:n=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,s=0,n=1,r){let o=[],a=(t.variantChildren.size-1)*s,l=1===n?(t=0)=>t*s:(t=0)=>a-t*s;return Array.from(t.variantChildren).sort(im).forEach((t,s)=>{t.notify("AnimationStart",e),o.push(ip(t,e,{...r,delay:i+l(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,n+s,o,a,i)}:()=>Promise.resolve(),{when:u}=r;if(!u)return Promise.all([o(),l(i.delay)]);{let[t,e]="beforeChildren"===u?[o,l]:[l,o];return t().then(()=>e())}}function im(t,e){return t.sortNodePosition(e)}function iv(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}function ig(t){return"string"==typeof t||Array.isArray(t)}let iy=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ix=["initial",...iy],iP=ix.length,iT=[...iy].reverse(),iw=iy.length;function ib(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iS(){return{animate:ib(!0),whileInView:ib(),whileHover:ib(),whileTap:ib(),whileDrag:ib(),whileFocus:ib(),exit:ib()}}class iA{constructor(t){this.isMounted=!1,this.node=t}update(){}}class iE extends iA{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>ip(t,e,i)));else if("string"==typeof e)s=ip(t,e,i);else{let n="function"==typeof e?a(t,e,i.custom):e;s=Promise.all(ic(t,n,i))}return s.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iS(),s=!0,r=e=>(i,s)=>{var n;let r=a(t,s,"exit"===e?null===(n=t.presenceContext)||void 0===n?void 0:n.custom:void 0);if(r){let{transition:t,transitionEnd:e,...s}=r;i={...i,...s,...e}}return i};function o(o){let{props:l}=t,u=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<iP;t++){let s=ix[t],n=e.props[s];(ig(n)||!1===n)&&(i[s]=n)}return i}(t.parent)||{},h=[],d=new Set,c={},p=1/0;for(let e=0;e<iw;e++){var m,f;let a=iT[e],v=i[a],g=void 0!==l[a]?l[a]:u[a],y=ig(g),x=a===o?v.isActive:null;!1===x&&(p=e);let P=g===u[a]&&g!==l[a]&&y;if(P&&s&&t.manuallyAnimateOnMount&&(P=!1),v.protectedKeys={...c},!v.isActive&&null===x||!g&&!v.prevProp||n(g)||"boolean"==typeof g)continue;let T=(m=v.prevProp,"string"==typeof(f=g)?f!==m:!!Array.isArray(f)&&!iv(f,m)),w=T||a===o&&v.isActive&&!P&&y||e>p&&y,b=!1,S=Array.isArray(g)?g:[g],A=S.reduce(r(a),{});!1===x&&(A={});let{prevResolvedValues:E={}}=v,V={...E,...A},M=e=>{w=!0,d.has(e)&&(b=!0,d.delete(e)),v.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in V){let e=A[t],i=E[t];if(c.hasOwnProperty(t))continue;let s=!1;(N(e)&&N(i)?iv(e,i):e===i)?void 0!==e&&d.has(t)?M(t):v.protectedKeys[t]=!0:null!=e?M(t):d.add(t)}v.prevProp=g,v.prevResolvedValues=A,v.isActive&&(c={...c,...A}),s&&t.blockInitialAnimation&&(w=!1);let C=!(P&&T)||b;w&&C&&h.push(...S.map(t=>({animation:t,options:{type:a}})))}if(d.size){let e={};if("boolean"!=typeof l.initial){let i=a(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}d.forEach(i=>{let s=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=null!=s?s:null}),h.push({animation:e})}let v=!!h.length;return s&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(v=!1),s=!1,v?e(h):Promise.resolve()}return{animateChanges:o,setActive:function(e,s){var n;if(i[e].isActive===s)return Promise.resolve();null===(n=t.variantChildren)||void 0===n||n.forEach(t=>{var i;return null===(i=t.animationState)||void 0===i?void 0:i.setActive(e,s)}),i[e].isActive=s;let r=o(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iS(),s=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null===(t=this.unmountControls)||void 0===t||t.call(this)}}let iV=0;class iM extends iA{constructor(){super(...arguments),this.id=iV++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}function iC(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}function iD(t){return{point:{x:t.pageX,y:t.pageY}}}let ik=t=>e=>M(e)&&t(e,iD(e));function iR(t,e,i,s){return iC(t,e,ik(i),s)}function iL({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function ij(t){return t.max-t.min}function iF(t,e,i,s=.5){t.origin=s,t.originPoint=eC(e.min,e.max,t.origin),t.scale=ij(i)/ij(e),t.translate=eC(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iB(t,e,i,s){iF(t.x,e.x,i.x,s?s.originX:void 0),iF(t.y,e.y,i.y,s?s.originY:void 0)}function iO(t,e,i){t.min=i.min+e.min,t.max=t.min+ij(e)}function iI(t,e,i){t.min=e.min-i.min,t.max=t.min+ij(e)}function iU(t,e,i){iI(t.x,e.x,i.x),iI(t.y,e.y,i.y)}let i$=()=>({translate:0,scale:1,origin:0,originPoint:0}),iN=()=>({x:i$(),y:i$()}),iW=()=>({min:0,max:0}),iz=()=>({x:iW(),y:iW()});function iH(t){return[t("x"),t("y")]}function iY(t){return void 0===t||1===t}function iX({scale:t,scaleX:e,scaleY:i}){return!iY(t)||!iY(e)||!iY(i)}function iK(t){return iX(t)||iq(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iq(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iG(t,e,i,s,n){return void 0!==n&&(t=s+n*(t-s)),s+i*(t-s)+e}function i_(t,e=0,i=1,s,n){t.min=iG(t.min,e,i,s,n),t.max=iG(t.max,e,i,s,n)}function iZ(t,{x:e,y:i}){i_(t.x,e.translate,e.scale,e.originPoint),i_(t.y,i.translate,i.scale,i.originPoint)}function iJ(t,e){t.min=t.min+e,t.max=t.max+e}function iQ(t,e,i,s,n=.5){let r=eC(t.min,t.max,n);i_(t,e,i,r,s)}function i0(t,e){iQ(t.x,e.x,e.scaleX,e.scale,e.originX),iQ(t.y,e.y,e.scaleY,e.scale,e.originY)}function i1(t,e){return iL(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}function i5(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let i2=(t,e)=>Math.abs(t-e);class i3{constructor(t,e,{transformPagePoint:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=i6(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(i2(t.x,e.x)**2+i2(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:n}=_;this.history.push({...s,timestamp:n});let{onStart:r,onMove:o}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{if(this.index=i7(t.currentTarget),t.target instanceof Element&&t.target.hasPointerCapture&&void 0!==t.pointerId)try{if(!t.target.hasPointerCapture(t.pointerId))return}catch(t){}this.lastMoveEvent=t,this.lastMoveEventInfo=i9(e,this.transformPagePoint),q.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{E(t,"release"),this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=i6("pointercancel"===t.type||"lostpointercapture"===t.type?this.lastMoveEventInfo:i9(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),s&&s(t,r)},!M(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=i;let n=i9(iD(t),this.transformPagePoint),{point:r}=n,{timestamp:o}=_;this.history=[{...r,timestamp:o}];let{onSessionStart:a}=e;a&&a(t,i6(n,this.history)),E(t,"set"),this.removeListeners=eI(iR(t.currentTarget,"pointermove",this.handlePointerMove),iR(t.currentTarget,"pointerup",this.handlePointerUp),iR(t.currentTarget,"pointercancel",this.handlePointerUp),iR(t.currentTarget,"lostpointercapture",(t,e)=>{i7(t.currentTarget)!==this.index?E(t,"set"):this.handlePointerUp(t,e)}))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),G(this.updatePoint)}}function i9(t,e){return e?{point:e(t.point)}:t}function i4(t,e){return{x:t.x-e.x,y:t.y-e.y}}function i6({point:t},e){return{point:t,delta:i4(t,i8(e)),offset:i4(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,n=i8(t);for(;i>=0&&(s=t[i],!(n.timestamp-s.timestamp>F(.1)));)i--;if(!s)return{x:0,y:0};let r=B(n.timestamp-s.timestamp);if(0===r)return{x:0,y:0};let o={x:(n.x-s.x)/r,y:(n.y-s.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function i8(t){return t[t.length-1]}function i7(t){return t.parentNode?Array.from(t.parentNode.children).indexOf(t):-1}function st(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function se(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function si(t,e,i){return{min:ss(t,e),max:ss(t,i)}}function ss(t,e){return"number"==typeof t?t:t[e]||0}let sn=new WeakMap;class sr{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iz(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new i3(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iD(t).point)},onStart:(t,e)=>{var i;let{drag:s,dragPropagation:n,onDragStart:r}=this.getProps();if(s&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(i=s)||"y"===i?b[i]?null:(b[i]=!0,()=>{b[i]=!1}):b.x||b.y?null:(b.x=b.y=!0,()=>{b.x=b.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iH(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tU.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=ij(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&q.postRender(()=>r(t,e)),tl(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:n,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iH(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:n}=this.getProps();n&&q.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!so(t,s,this.currentDirection))return;let n=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?eC(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?eC(i,t,s.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),n.set(r)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),s=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,n=this.constraints;e&&i5(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&s?this.constraints=function(t,{top:e,left:i,bottom:s,right:n}){return{x:st(t.x,i,n),y:st(t.y,e,s)}}(s.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:si(t,"left","right"),y:si(t,"top","bottom")}}(i),n!==this.constraints&&s&&this.constraints&&!this.hasMutatedConstraints&&iH(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(s.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!i5(e))return!1;let s=e.current;O(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let r=function(t,e,i){let s=i1(t,i),{scroll:n}=e;return n&&(iJ(s.x,n.offset.x),iJ(s.y,n.offset.y)),s}(s,n.root,this.visualElement.getTransformPagePoint()),o={x:se((t=n.layout.layoutBox).x,r.x),y:se(t.y,r.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iL(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:n,dragSnapToOrigin:r,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iH(o=>{if(!so(o,e,this.currentDirection))return;let l=a&&a[o]||{};r&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return tl(this.visualElement,t),i.start(id(t,i,0,e,this.visualElement,!1))}stopAnimation(){iH(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iH(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iH(e=>{let{drag:i}=this.getProps();if(!so(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,n=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:r}=s.layout.layoutBox[e];n.set(t[e]-eC(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!i5(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};iH(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=ij(t),n=ij(e);return n>s?i=x(e.min,e.max-s,t.min):s>n&&(i=x(t.min,t.max-n,e.min)),tS(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iH(e=>{if(!so(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:r}=this.constraints[e];i.set(eC(n,r,s[e]))})}addListeners(){if(!this.visualElement.current)return;sn.set(this.visualElement,this);let t=iR(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();i5(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),q.read(e);let n=iC(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iH(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),s(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:n=!1,dragElastic:r=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:n,dragElastic:r,dragMomentum:o}}}function so(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class sa extends iA{constructor(t){super(t),this.removeGroupControls=O,this.removeListeners=O,this.controls=new sr(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||O}unmount(){this.removeGroupControls(),this.removeListeners()}}let sl=t=>(e,i)=>{t&&q.postRender(()=>t(e,i))};class su extends iA{constructor(){super(...arguments),this.removePointerDownListener=O}onPointerDown(t){this.session=new i3(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint()})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:sl(t),onStart:sl(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&q.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=iR(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var sh,sd,sc=i(95155),sp=i(12115);let sm=(0,sp.createContext)(null),sf=(0,sp.createContext)({}),sv=(0,sp.createContext)({}),{schedule:sg,cancel:sy}=K(queueMicrotask,!1),sx={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function sP(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let sT={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!t$.test(t))return t;t=parseFloat(t)}let i=sP(t,e.target.x),s=sP(t,e.target.y);return`${i}% ${s}%`}},sw={};class sb extends sp.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:n}=t;!function(t){for(let e in t)sw[e]=t[e],ef(e)&&(sw[e].isCSSVariable=!0)}(sA),n&&(e.group&&e.group.add(n),i&&i.register&&s&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),sx.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:n}=this.props,r=i.projection;return r&&(r.isPresent=n,s||t.layoutDependency!==e||void 0===e||t.isPresent!==n?r.willUpdate():this.safeToRemove(),t.isPresent===n||(n?r.promote():r.relegate()||q.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),sg.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function sS(t){let[e,i]=function(t=!0){let e=(0,sp.useContext)(sm);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:s,register:n}=e,r=(0,sp.useId)();(0,sp.useEffect)(()=>{if(t)return n(r)},[t]);let o=(0,sp.useCallback)(()=>t&&s&&s(r),[r,s,t]);return!i&&s?[!1,o]:[!0]}(),s=(0,sp.useContext)(sf);return(0,sc.jsx)(sb,{...t,layoutGroup:s,switchLayoutGroup:(0,sp.useContext)(sv),isPresent:e,safeToRemove:i})}let sA={borderRadius:{...sT,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:sT,borderTopRightRadius:sT,borderBottomLeftRadius:sT,borderBottomRightRadius:sT,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=t0.parse(t);if(s.length>5)return t;let n=t0.createTransformer(t),r=+("number"!=typeof s[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;s[0+r]/=o,s[1+r]/=a;let l=eC(o,a,.5);return"number"==typeof s[2+r]&&(s[2+r]/=l),"number"==typeof s[3+r]&&(s[3+r]/=l),n(s)}}},sE=(t,e)=>t.depth-e.depth;class sV{constructor(){this.children=[],this.isDirty=!1}add(t){tt(this.children,t),this.isDirty=!0}remove(t){te(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(sE),this.isDirty=!1,this.children.forEach(t)}}function sM(t){let e=ta(t)?t.get():t;return W(e)?e.toValue():e}let sC=["TopLeft","TopRight","BottomLeft","BottomRight"],sD=sC.length,sk=t=>"string"==typeof t?parseFloat(t):t,sR=t=>"number"==typeof t||t$.test(t);function sL(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let sj=sB(0,.5,tT),sF=sB(.5,.95,O);function sB(t,e,i){return s=>s<t?0:s>e?1:i(x(t,e,s))}function sO(t,e){t.min=e.min,t.max=e.max}function sI(t,e){sO(t.x,e.x),sO(t.y,e.y)}function sU(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function s$(t,e,i,s,n){return t-=e,t=s+1/i*(t-s),void 0!==n&&(t=s+1/n*(t-s)),t}function sN(t,e,[i,s,n],r,o){!function(t,e=0,i=1,s=.5,n,r=t,o=t){if(tU.test(e)&&(e=parseFloat(e),e=eC(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=eC(r.min,r.max,s);t===r&&(a-=e),t.min=s$(t.min,e,i,a,n),t.max=s$(t.max,e,i,a,n)}(t,e[i],e[s],e[n],e.scale,r,o)}let sW=["x","scaleX","originX"],sz=["y","scaleY","originY"];function sH(t,e,i,s){sN(t.x,e,sW,i?i.x:void 0,s?s.x:void 0),sN(t.y,e,sz,i?i.y:void 0,s?s.y:void 0)}function sY(t){return 0===t.translate&&1===t.scale}function sX(t){return sY(t.x)&&sY(t.y)}function sK(t,e){return t.min===e.min&&t.max===e.max}function sq(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function sG(t,e){return sq(t.x,e.x)&&sq(t.y,e.y)}function s_(t){return ij(t.x)/ij(t.y)}function sZ(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class sJ{constructor(){this.members=[]}add(t){tt(this.members,t),t.scheduleRender()}remove(t){if(te(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let sQ={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},s0=["","X","Y","Z"],s1={visibility:"hidden"},s5=0;function s2(t,e,i,s){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),s&&(s[t]=0))}function s3({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:n}){return class{constructor(t={},i=null==e?void 0:e()){this.id=s5++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,X.value&&(sQ.nodes=sQ.calculatedTargetDeltas=sQ.calculatedProjections=0),this.nodes.forEach(s6),this.nodes.forEach(nn),this.nodes.forEach(nr),this.nodes.forEach(s8),X.addProjectionMetrics&&X.addProjectionMetrics(sQ)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new sV)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new ti),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:s,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(n||s)&&(this.isLayoutDirty=!0),t){let i;let s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=Q.now(),s=({timestamp:n})=>{let r=n-i;r>=250&&(G(s),t(r-e))};return q.read(s,!0),()=>G(s)}(s,250),sx.hasAnimatedSinceResize&&(sx.hasAnimatedSinceResize=!1,this.nodes.forEach(ns))})}s&&this.root.registerSharedNode(s,this),!1!==this.options.animate&&r&&(s||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||r.getDefaultTransition()||nd,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!sG(this.targetLayout,s),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...c(n,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||ns(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,G(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(no),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=i.props[th];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",q,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(nt);return}this.isUpdating||this.nodes.forEach(ne),this.isUpdating=!1,this.nodes.forEach(ni),this.nodes.forEach(s9),this.nodes.forEach(s4),this.clearAllSnapshots();let t=Q.now();_.delta=tS(0,1e3/60,t-_.timestamp),_.timestamp=t,_.isProcessing=!0,Z.update.process(_),Z.preRender.process(_),Z.render.process(_),_.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,sg.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(s7),this.sharedNodes.forEach(na)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,q.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){q.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||ij(this.snapshot.measuredBox.x)||ij(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iz(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!sX(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,r=s!==this.prevTransformTemplateValue;t&&(e||iK(this.latestValues)||r)&&(n(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),nm((e=s).x),nm(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return iz();let i=e.measureViewportBox();if(!((null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)||this.path.some(nv))){let{scroll:t}=this.root;t&&(iJ(i.x,t.offset.x),iJ(i.y,t.offset.y))}return i}removeElementScroll(t){var e;let i=iz();if(sI(i,t),null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)return i;for(let e=0;e<this.path.length;e++){let s=this.path[e],{scroll:n,options:r}=s;s!==this.root&&n&&r.layoutScroll&&(n.wasRoot&&sI(i,t),iJ(i.x,n.offset.x),iJ(i.y,n.offset.y))}return i}applyTransform(t,e=!1){let i=iz();sI(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&i0(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),iK(s.latestValues)&&i0(i,s.latestValues)}return iK(this.latestValues)&&i0(i,this.latestValues),i}removeTransform(t){let e=iz();sI(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iK(i.latestValues))continue;iX(i.latestValues)&&i.updateSnapshot();let s=iz();sI(s,i.measurePageBox()),sH(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return iK(this.latestValues)&&sH(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==_.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,i,s,n;let r=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=r.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=r.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=r.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==r;if(!(t||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=_.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iz(),this.relativeTargetOrigin=iz(),iU(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),sI(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=iz(),this.targetWithTransforms=iz()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,s=this.relativeTarget,n=this.relativeParent.target,iO(i.x,s.x,n.x),iO(i.y,s.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sI(this.target,this.layout.layoutBox),iZ(this.target,this.targetDelta)):sI(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iz(),this.relativeTargetOrigin=iz(),iU(this.relativeTargetOrigin,this.target,t.target),sI(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}X.value&&sQ.calculatedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||iX(this.parent.latestValues)||iq(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,s=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(s=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(s=!1),this.resolvedRelativeTargetAt===_.timestamp&&(s=!1),s)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;sI(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,s=!1){let n,r;let o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){r=(n=i[a]).projectionDelta;let{visualElement:o}=n.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&i0(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,iZ(t,r)),s&&iK(n.latestValues)&&i0(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=iz());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(sU(this.prevProjectionDelta.x,this.projectionDelta.x),sU(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iB(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&sZ(this.projectionDelta.x,this.prevProjectionDelta.x)&&sZ(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),X.value&&sQ.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null===(e=this.options.visualElement)||void 0===e||e.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iN(),this.projectionDelta=iN(),this.projectionDeltaWithTransform=iN()}setAnimationOrigin(t,e=!1){let i;let s=this.snapshot,n=s?s.latestValues:{},r={...this.latestValues},o=iN();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iz(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(nh));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(nl(o.x,t.x,s),nl(o.y,t.y,s),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m,f,v;if(iU(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=a,v=s,nu(p.x,m.x,f.x,v),nu(p.y,m.y,f.y,v),i&&(u=this.relativeTarget,c=i,sK(u.x,c.x)&&sK(u.y,c.y)))this.isProjectionDirty=!1;i||(i=iz()),sI(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,s,n,r){n?(t.opacity=eC(0,void 0!==i.opacity?i.opacity:1,sj(s)),t.opacityExit=eC(void 0!==e.opacity?e.opacity:1,0,sF(s))):r&&(t.opacity=eC(void 0!==e.opacity?e.opacity:1,void 0!==i.opacity?i.opacity:1,s));for(let n=0;n<sD;n++){let r=`border${sC[n]}Radius`,o=sL(e,r),a=sL(i,r);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||sR(o)===sR(a)?(t[r]=Math.max(eC(sk(o),sk(a),s),0),(tU.test(a)||tU.test(o))&&(t[r]+="%")):t[r]=a)}(e.rotate||i.rotate)&&(t.rotate=eC(e.rotate||0,i.rotate||0,s))}(r,n,this.latestValues,s,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(G(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=q.update(()=>{sx.hasAnimatedSinceResize=!0,eM.layout++,this.currentAnimation=function(t,e,i){let s=ta(0)?0:to(t);return s.start(id("",s,1e3,i)),s.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{eM.layout--},onComplete:()=>{eM.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:n}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&nf(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||iz();let e=ij(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=ij(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}sI(e,i),i0(e,n),iB(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new sJ),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&s2("z",t,s,this.animationValues);for(let e=0;e<s0.length;e++)s2(`rotate${s0[e]}`,t,s,this.animationValues),s2(`skew${s0[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return s1;let s={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,s.opacity="",s.pointerEvents=sM(null==t?void 0:t.pointerEvents)||"",s.transform=n?n(this.latestValues,""):"none",s;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=sM(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!iK(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}let o=r.animationValues||r.latestValues;this.applyTransformsToTarget(),s.transform=function(t,e,i){let s="",n=t.x.translate/e.x,r=t.y.translate/e.y,o=(null==i?void 0:i.z)||0;if((n||r||o)&&(s=`translate3d(${n}px, ${r}px, ${o}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:r,skewX:o,skewY:a}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),n&&(s+=`rotateX(${n}deg) `),r&&(s+=`rotateY(${r}deg) `),o&&(s+=`skewX(${o}deg) `),a&&(s+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(s+=`scale(${a}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,o),n&&(s.transform=n(o,s.transform));let{x:a,y:l}=this.projectionDelta;for(let t in s.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,r.animationValues?s.opacity=r===this?null!==(i=null!==(e=o.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==i?i:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:s.opacity=r===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,sw){if(void 0===o[t])continue;let{correct:e,applyTo:i,isCSSVariable:n}=sw[t],a="none"===s.transform?o[t]:e(o[t],r);if(i){let t=i.length;for(let e=0;e<t;e++)s[i[e]]=a}else n?this.options.visualElement.renderState.vars[t]=a:s[t]=a}return this.options.layoutId&&(s.pointerEvents=r===this?sM(null==t?void 0:t.pointerEvents)||"":"none"),s}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(nt),this.root.sharedNodes.clear()}}}function s9(t){t.updateLayout()}function s4(t){var e;let i=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:s}=t.layout,{animationType:n}=t.options,r=i.source!==t.layout.source;"size"===n?iH(t=>{let s=r?i.measuredBox[t]:i.layoutBox[t],n=ij(s);s.min=e[t].min,s.max=s.min+n}):nf(n,i.layoutBox,e)&&iH(s=>{let n=r?i.measuredBox[s]:i.layoutBox[s],o=ij(e[s]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+o)});let o=iN();iB(o,e,i.layoutBox);let a=iN();r?iB(a,t.applyTransform(s,!0),i.measuredBox):iB(a,e,i.layoutBox);let l=!sX(o),u=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:n,layout:r}=s;if(n&&r){let o=iz();iU(o,i.layoutBox,n.layoutBox);let a=iz();iU(a,e,r.layoutBox),sG(o,a)||(u=!0),s.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function s6(t){X.value&&sQ.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function s8(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function s7(t){t.clearSnapshot()}function nt(t){t.clearMeasurements()}function ne(t){t.isLayoutDirty=!1}function ni(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function ns(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function nn(t){t.resolveTargetDelta()}function nr(t){t.calcProjection()}function no(t){t.resetSkewAndRotation()}function na(t){t.removeLeadSnapshot()}function nl(t,e,i){t.translate=eC(e.translate,0,i),t.scale=eC(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function nu(t,e,i,s){t.min=eC(e.min,i.min,s),t.max=eC(e.max,i.max,s)}function nh(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let nd={duration:.45,ease:[.4,0,.1,1]},nc=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),np=nc("applewebkit/")&&!nc("chrome/")?Math.round:O;function nm(t){t.min=np(t.min),t.max=np(t.max)}function nf(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(s_(e)-s_(i)))}function nv(t){var e;return t!==t.root&&(null===(e=t.scroll)||void 0===e?void 0:e.wasRoot)}let ng=s3({attachResizeListener:(t,e)=>iC(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ny={current:void 0},nx=s3({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ny.current){let t=new ng({});t.mount(window),t.setOptions({layoutScroll:!0}),ny.current=t}return ny.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function nP(t,e,i){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=s["onHover"+i];n&&q.postRender(()=>n(e,iD(e)))}class nT extends iA{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=S(t,i),o=t=>{if(!A(t))return;let{target:i}=t,s=e(i,t);if("function"!=typeof s||!i)return;let r=t=>{A(t)&&(s(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,n)};return s.forEach(t=>{t.addEventListener("pointerenter",o,n)}),r}(t,(t,e)=>(nP(this.node,e,"Start"),t=>nP(this.node,t,"End"))))}unmount(){}}class nw extends iA{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eI(iC(this.node.current,"focus",()=>this.onFocus()),iC(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function nb(t,e,i){let{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=s["onTap"+("End"===i?"":i)];n&&q.postRender(()=>n(e,iD(e)))}class nS extends iA{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=S(t,i),o=t=>{let i=t.currentTarget;if(!i||!j(t)||D.has(i))return;D.add(i),E(t,"set");let s=e(i,t),r=(t,e)=>{i.removeEventListener("pointerup",o),i.removeEventListener("pointercancel",a),E(t,"release"),j(t)&&D.has(i)&&(D.delete(i),"function"==typeof s&&s(t,{success:e}))},o=t=>{var e,s;t.isTrusted&&(e=t,s=i instanceof Element?i.getBoundingClientRect():{left:0,top:0,right:window.innerWidth,bottom:window.innerHeight},e.clientX<s.left||e.clientX>s.right||e.clientY<s.top||e.clientY>s.bottom)?r(t,!1):r(t,!(i instanceof Element)||V(i,t.target))},a=t=>{r(t,!1)};i.addEventListener("pointerup",o,n),i.addEventListener("pointercancel",a,n),i.addEventListener("lostpointercapture",a,n)};return s.forEach(t=>{t=i.useGlobalTarget?window:t;let e=!1;if(t instanceof HTMLElement){var s;e=!0,s=t,!C.has(s.tagName)&&-1===s.tabIndex&&null===t.getAttribute("tabindex")&&(t.tabIndex=0)}t.addEventListener("pointerdown",o,n),e&&t.addEventListener("focus",t=>L(t,n),n)}),r}(t,(t,e)=>(nb(this.node,e,"Start"),(t,{success:e})=>nb(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nA=new WeakMap,nE=new WeakMap,nV=t=>{let e=nA.get(t.target);e&&e(t)},nM=t=>{t.forEach(nV)},nC={some:0,all:1};class nD extends iA{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:n}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:nC[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;nE.has(i)||nE.set(i,{});let s=nE.get(i),n=JSON.stringify(e);return s[n]||(s[n]=new IntersectionObserver(nM,{root:t,...e})),s[n]}(e);return nA.set(t,i),s.observe(t),()=>{nA.delete(t),s.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),r=e?i:s;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let nk=(0,sp.createContext)({strict:!1}),nR=(0,sp.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),nL=(0,sp.createContext)({});function nj(t){return n(t.animate)||ix.some(e=>ig(t[e]))}function nF(t){return!!(nj(t)||t.variants)}function nB(t){return Array.isArray(t)?t.join(" "):t}let nO="undefined"!=typeof window,nI={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nU={};for(let t in nI)nU[t]={isEnabled:e=>nI[t].some(t=>!!e[t])};let n$=Symbol.for("motionComponentSymbol"),nN=nO?sp.useLayoutEffect:sp.useEffect;function nW(t,{layout:e,layoutId:i}){return U.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!sw[t]||"opacity"===t)}let nz=(t,e)=>e&&"number"==typeof t?e.transform(t):t,nH={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nY=I.length;function nX(t,e,i){let{style:s,vars:n,transformOrigin:r}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(U.has(t)){o=!0;continue}if(ef(t)){n[t]=i;continue}{let e=nz(i,t4[t]);t.startsWith("origin")?(a=!0,r[t]=e):s[t]=e}}if(!e.transform&&(o||i?s.transform=function(t,e,i){let s="",n=!0;for(let r=0;r<nY;r++){let o=I[r],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||i){let t=nz(a,t4[o]);if(!l){n=!1;let e=nH[o]||o;s+=`${e}(${t}) `}i&&(e[o]=t)}}return s=s.trim(),i?s=i(e,n?"":s):n&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;s.transformOrigin=`${t} ${e} ${i}`}}let nK=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nq(t,e,i){for(let s in e)ta(e[s])||nW(s,i)||(t[s]=e[s])}let nG=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function n_(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||nG.has(t)}let nZ=t=>!n_(t);try{!function(t){t&&(nZ=e=>e.startsWith("on")?!n_(e):t(e))}(require("@emotion/is-prop-valid").default)}catch(t){}let nJ=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function nQ(t){if("string"!=typeof t||t.includes("-"));else if(nJ.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}let n0={offset:"stroke-dashoffset",array:"stroke-dasharray"},n1={offset:"strokeDashoffset",array:"strokeDasharray"};function n5(t,e,i){return"string"==typeof t?t:t$.transform(e+i*t)}function n2(t,{attrX:e,attrY:i,attrScale:s,originX:n,originY:r,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},h,d){if(nX(t,u,d),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:p,dimensions:m}=t;c.transform&&(m&&(p.transform=c.transform),delete c.transform),m&&(void 0!==n||void 0!==r||p.transform)&&(p.transformOrigin=function(t,e,i){let s=n5(e,t.x,t.width),n=n5(i,t.y,t.height);return`${s} ${n}`}(m,void 0!==n?n:.5,void 0!==r?r:.5)),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==s&&(c.scale=s),void 0!==o&&function(t,e,i=1,s=0,n=!0){t.pathLength=1;let r=n?n0:n1;t[r.offset]=t$.transform(-s);let o=t$.transform(e),a=t$.transform(i);t[r.array]=`${o} ${a}`}(c,o,a,l,!1)}let n3=()=>({...nK(),attrs:{}}),n9=t=>"string"==typeof t&&"svg"===t.toLowerCase(),n4=t=>(e,i)=>{let s=(0,sp.useContext)(nL),r=(0,sp.useContext)(sm),a=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:i},s,r,a){let l={latestValues:function(t,e,i,s){let r={},a=s(t,{});for(let t in a)r[t]=sM(a[t]);let{initial:l,animate:u}=t,h=nj(t),d=nF(t);e&&d&&!h&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===u&&(u=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!n(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let s=o(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(s,r,a,t),renderState:e()};return i&&(l.onMount=t=>i({props:s,current:t,...l}),l.onUpdate=t=>i(t)),l})(t,e,s,r);return i?a():function(t){let e=(0,sp.useRef)(null);return null===e.current&&(e.current=t()),e.current}(a)};function n6(t,e,i){var s;let{style:n}=t,r={};for(let o in n)(ta(n[o])||e.style&&ta(e.style[o])||nW(o,t)||(null===(s=null==i?void 0:i.getValue(o))||void 0===s?void 0:s.liveStyle)!==void 0)&&(r[o]=n[o]);return r}let n8={useVisualState:n4({scrapeMotionValuesFromProps:n6,createRenderState:nK})};function n7(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}function rt(t,{style:e,vars:i},s,n){for(let r in Object.assign(t.style,e,n&&n.getProjectionStyles(s)),i)t.style.setProperty(r,i[r])}let re=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function ri(t,e,i,s){for(let i in rt(t,e,void 0,s),e.attrs)t.setAttribute(re.has(i)?i:tu(i),e.attrs[i])}function rs(t,e,i){let s=n6(t,e,i);for(let i in t)(ta(t[i])||ta(e[i]))&&(s[-1!==I.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}let rn=["x","y","width","height","cx","cy","r"],rr={useVisualState:n4({scrapeMotionValuesFromProps:rs,createRenderState:n3,onUpdate:({props:t,prevProps:e,current:i,renderState:s,latestValues:n})=>{if(!i)return;let r=!!t.drag;if(!r){for(let t in n)if(U.has(t)){r=!0;break}}if(!r)return;let o=!e;if(e)for(let i=0;i<rn.length;i++){let s=rn[i];t[s]!==e[s]&&(o=!0)}o&&q.read(()=>{n7(i,s),q.render(()=>{n2(s,n,n9(i.tagName),t.transformTemplate),ri(i,s)})})}})},ro={current:null},ra={current:!1},rl=[...eT,tY,t0],ru=t=>rl.find(eP(t)),rh=new WeakMap,rd=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class rc{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:n,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=ec,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=Q.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,q.render(this.render,!1,!0))};let{latestValues:a,renderState:l,onUpdate:u}=r;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=o,this.blockInitialAnimation=!!n,this.isControllingVariants=nj(e),this.isVariantNode=nF(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...d}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in d){let e=d[t];void 0!==a[t]&&ta(e)&&e.set(a[t],!1)}}mount(t){this.current=t,rh.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),ra.current||function(){if(ra.current=!0,nO){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ro.current=t.matches;t.addListener(e),e()}else ro.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ro.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),G(this.notifyUpdate),G(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=U.has(t);s&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&q.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in nU){let e=nU[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iz()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<rd.length;e++){let i=rd[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let n=e[s],r=i[s];if(ta(n))t.addValue(s,n);else if(ta(r))t.addValue(s,to(n,{owner:t}));else if(r!==n){if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(s);t.addValue(s,to(void 0!==e?e:n,{owner:t}))}}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=to(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){var i;let s=void 0===this.latestValues[t]&&this.current?null!==(i=this.getBaseTargetFromProps(this.props,t))&&void 0!==i?i:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=s&&("string"==typeof s&&(ep(s)||tb(s))?s=parseFloat(s):!ru(s)&&t0.test(e)&&(s=t7(t,e)),this.setBaseTarget(t,ta(s)?s.get():s)),ta(s)?s.get():s}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let i;let{initial:s}=this.props;if("string"==typeof s||"object"==typeof s){let n=o(this.props,s,null===(e=this.presenceContext)||void 0===e?void 0:e.custom);n&&(i=n[t])}if(s&&void 0!==i)return i;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||ta(n)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new ti),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class rp extends rc{constructor(){super(...arguments),this.KeyframeResolver=eb}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;ta(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}class rm extends rp{constructor(){super(...arguments),this.type="html",this.renderInstance=rt}readValueFromInstance(t,e){if(U.has(e)){let t=t8(e);return t&&t.default||0}{let i=window.getComputedStyle(t),s=(ef(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return i1(t,e)}build(t,e,i){nX(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return n6(t,e,i)}}class rf extends rp{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iz,this.updateDimensions=()=>{this.current&&!this.renderState.dimensions&&n7(this.current,this.renderState)}}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(U.has(e)){let t=t8(e);return t&&t.default||0}return e=re.has(e)?e:tu(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return rs(t,e,i)}onBindTransform(){this.current&&!this.renderState.dimensions&&q.postRender(this.updateDimensions)}build(t,e,i){n2(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,s){ri(t,e,i,s)}mount(t){this.isSVGTag=n9(t.tagName),super.mount(t)}}let rv=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}((sh={animation:{Feature:iE},exit:{Feature:iM},inView:{Feature:nD},tap:{Feature:nS},focus:{Feature:nw},hover:{Feature:nT},pan:{Feature:su},drag:{Feature:sa,ProjectionNode:nx,MeasureLayout:sS},layout:{ProjectionNode:nx,MeasureLayout:sS}},sd=(t,e)=>nQ(t)?new rf(e):new rm(e,{allowProjection:t!==sp.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:s,createVisualElement:n,useRender:r,useVisualState:o,Component:a}=t;function l(t,e){var i,s,l;let u;let h={...(0,sp.useContext)(nR),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,sp.useContext)(sf).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:d}=h,c=function(t){let{initial:e,animate:i}=function(t,e){if(nj(t)){let{initial:e,animate:i}=t;return{initial:!1===e||ig(e)?e:void 0,animate:ig(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,sp.useContext)(nL));return(0,sp.useMemo)(()=>({initial:e,animate:i}),[nB(e),nB(i)])}(t),p=o(t,d);if(!d&&nO){s=0,l=0,(0,sp.useContext)(nk).strict;let t=function(t){let{drag:e,layout:i}=nU;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(h);u=t.MeasureLayout,c.visualElement=function(t,e,i,s,n){var r,o;let{visualElement:a}=(0,sp.useContext)(nL),l=(0,sp.useContext)(nk),u=(0,sp.useContext)(sm),h=(0,sp.useContext)(nR).reducedMotion,d=(0,sp.useRef)(null);s=s||l.renderer,!d.current&&s&&(d.current=s(t,{visualState:e,parent:a,props:i,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:h}));let c=d.current,p=(0,sp.useContext)(sv);c&&!c.projection&&n&&("html"===c.type||"svg"===c.type)&&function(t,e,i,s){let{layoutId:n,layout:r,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:r,alwaysMeasureLayout:!!o||a&&i5(a),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:s,layoutScroll:l,layoutRoot:u})}(d.current,i,n,p);let m=(0,sp.useRef)(!1);(0,sp.useInsertionEffect)(()=>{c&&m.current&&c.update(i,u)});let f=i[th],v=(0,sp.useRef)(!!f&&!(null===(r=window.MotionHandoffIsComplete)||void 0===r?void 0:r.call(window,f))&&(null===(o=window.MotionHasOptimisedAnimation)||void 0===o?void 0:o.call(window,f)));return nN(()=>{c&&(m.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),sg.render(c.render),v.current&&c.animationState&&c.animationState.animateChanges())}),(0,sp.useEffect)(()=>{c&&(!v.current&&c.animationState&&c.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var t;null===(t=window.MotionHandoffMarkAsComplete)||void 0===t||t.call(window,f)}),v.current=!1))}),c}(a,p,h,n,t.ProjectionNode)}return(0,sc.jsxs)(nL.Provider,{value:c,children:[u&&c.visualElement?(0,sc.jsx)(u,{visualElement:c.visualElement,...h}):null,r(a,t,(i=c.visualElement,(0,sp.useCallback)(t=>{t&&p.onMount&&p.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):i5(e)&&(e.current=t))},[i])),p,d,c.visualElement)]})}s&&function(t){for(let e in t)nU[e]={...nU[e],...t[e]}}(s),l.displayName="motion.".concat("string"==typeof a?a:"create(".concat(null!==(i=null!==(e=a.displayName)&&void 0!==e?e:a.name)&&void 0!==i?i:"",")"));let u=(0,sp.forwardRef)(l);return u[n$]=a,u}({...nQ(t)?rr:n8,preloadedFeatures:sh,useRender:function(t=!1){return(e,i,s,{latestValues:n},r)=>{let o=(nQ(e)?function(t,e,i,s){let n=(0,sp.useMemo)(()=>{let i=n3();return n2(i,e,n9(s),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};nq(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return nq(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,sp.useMemo)(()=>{let i=nK();return nX(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,n,r,e),a=function(t,e,i){let s={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(nZ(n)||!0===i&&n_(n)||!e&&!n_(n)||t.draggable&&n.startsWith("onDrag"))&&(s[n]=t[n]);return s}(i,"string"==typeof e,t),l=e!==sp.Fragment?{...a,...o,ref:s}:{},{children:u}=i,h=(0,sp.useMemo)(()=>ta(u)?u.get():u,[u]);return(0,sp.createElement)(e,{...l,children:h})}}(e),createVisualElement:sd,Component:t})}))}}]);