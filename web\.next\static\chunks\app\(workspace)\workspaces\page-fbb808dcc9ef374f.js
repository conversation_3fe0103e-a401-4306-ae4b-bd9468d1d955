(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7456],{7016:(e,t,a)=>{"use strict";a.d(t,{default:()=>k});var r=a(95155),s=a(12115),n=a(35695),i=a(66695),o=a(85127),c=a(54165),l=a(30285),d=a(62523),u=a(85057),h=a(88539),g=a(26126),x=a(84616),p=a(51154),m=a(13717),f=a(17580),v=a(62525),b=a(56671),j=a(99672),y=a(59434);function k(){let[e,t]=(0,s.useState)([]),[a,k]=(0,s.useState)(!0),[w,N]=(0,s.useState)(!1),[z,A]=(0,s.useState)(!1),[C,E]=(0,s.useState)(null),[_,S]=(0,s.useState)({name:"",description:"",status:"active"}),[T,F]=(0,s.useState)(!1),D=(0,n.useRouter)();(0,s.useEffect)(()=>{H()},[]);let H=async()=>{try{k(!0);let e=await (0,j.h6)();t(e)}catch(e){console.error("Error fetching Workspaces:",e),b.o.error("Failed to fetch Workspaces")}finally{k(!1)}},W=async()=>{try{F(!0);let a=await (0,j.EC)(_);t([...e,a]),N(!1),S({name:"",description:"",status:"active"}),b.o.success("Workspace created successfully")}catch(e){console.error("Error creating Workspace:",e),b.o.error("Failed to create Workspace")}finally{F(!1)}},M=async()=>{if(C)try{F(!0),await (0,j.Dp)(C._id),t(e.filter(e=>e._id!==C._id)),A(!1),E(null),b.o.success("Workspace deleted successfully")}catch(e){console.error("Error deleting Workspace:",e),b.o.error("Failed to delete Workspace")}finally{F(!1)}},P=e=>{switch(e){case"active":return(0,r.jsx)(g.E,{className:"bg-green-500",children:"Active"});case"inactive":return(0,r.jsx)(g.E,{className:"bg-gray-500",children:"Inactive"});case"suspended":return(0,r.jsx)(g.E,{className:"bg-red-500",children:"Suspended"});default:return(0,r.jsx)(g.E,{children:e})}};return(0,r.jsxs)("div",{className:"container mx-auto py-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:"Workspaces"}),(0,r.jsxs)(c.lG,{open:w,onOpenChange:N,children:[(0,r.jsx)(c.zM,{asChild:!0,children:(0,r.jsxs)(l.$,{children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Create Workspaces"]})}),(0,r.jsxs)(c.Cf,{children:[(0,r.jsxs)(c.c7,{children:[(0,r.jsx)(c.L3,{children:"Create New Workspaces"}),(0,r.jsx)(c.rr,{children:"Add a new Workspaces to the system."})]}),(0,r.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(u.J,{htmlFor:"name",className:"text-right",children:"Name"}),(0,r.jsx)(d.p,{id:"name",value:_.name,onChange:e=>S({..._,name:e.target.value}),className:"col-span-3"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(u.J,{htmlFor:"description",className:"text-right",children:"Description"}),(0,r.jsx)(h.T,{id:"description",value:_.description,onChange:e=>S({..._,description:e.target.value}),className:"col-span-3"})]})]}),(0,r.jsxs)(c.Es,{children:[(0,r.jsx)(l.$,{variant:"outline",onClick:()=>N(!1),children:"Cancel"}),(0,r.jsx)(l.$,{onClick:W,disabled:T||!_.name,children:T?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating..."]}):"Create"})]})]})]})]}),(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"All Workspaces"}),(0,r.jsx)(i.BT,{children:"Manage your Workspaces and their settings."})]}),(0,r.jsx)(i.Wu,{children:a?(0,r.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,r.jsx)(p.A,{className:"h-8 w-8 animate-spin"})}):0===e.length?(0,r.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No Workspaces found. Create one to get started."}):(0,r.jsxs)(o.XI,{children:[(0,r.jsx)(o.A0,{children:(0,r.jsxs)(o.Hj,{children:[(0,r.jsx)(o.nd,{children:"Name"}),(0,r.jsx)(o.nd,{children:"Status"}),(0,r.jsx)(o.nd,{children:"Credits"}),(0,r.jsx)(o.nd,{children:"Users"}),(0,r.jsx)(o.nd,{children:"Created"}),(0,r.jsx)(o.nd,{className:"text-right",children:"Actions"})]})}),(0,r.jsx)(o.BF,{children:e.map(e=>{var t,a;return(0,r.jsxs)(o.Hj,{children:[(0,r.jsx)(o.nA,{className:"font-medium",children:e.name}),(0,r.jsx)(o.nA,{children:P(e.status)}),(0,r.jsx)(o.nA,{children:(0,y.v)(e.credits||0)}),(0,r.jsx)(o.nA,{children:((null===(t=e.users)||void 0===t?void 0:t.length)||0)+((null===(a=e.adminUsers)||void 0===a?void 0:a.length)||0)}),(0,r.jsx)(o.nA,{children:new Date(e.createdAt).toLocaleDateString()}),(0,r.jsx)(o.nA,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,r.jsx)(l.$,{variant:"outline",size:"icon",onClick:()=>D.push("/workspaces/".concat(e._id)),children:(0,r.jsx)(m.A,{className:"h-4 w-4"})}),(0,r.jsx)(l.$,{variant:"outline",size:"icon",onClick:()=>D.push("/workspaces/".concat(e._id,"/users")),children:(0,r.jsx)(f.A,{className:"h-4 w-4"})}),(0,r.jsx)(l.$,{variant:"outline",size:"icon",className:"text-red-500",onClick:()=>{E(e),A(!0)},children:(0,r.jsx)(v.A,{className:"h-4 w-4"})})]})})]},e._id)})})]})})]}),(0,r.jsx)(c.lG,{open:z,onOpenChange:A,children:(0,r.jsxs)(c.Cf,{children:[(0,r.jsxs)(c.c7,{children:[(0,r.jsx)(c.L3,{children:"Delete Organization"}),(0,r.jsxs)(c.rr,{children:['Are you sure you want to delete the organization "',null==C?void 0:C.name,'"? This action cannot be undone.']})]}),(0,r.jsxs)(c.Es,{children:[(0,r.jsx)(l.$,{variant:"outline",onClick:()=>A(!1),children:"Cancel"}),(0,r.jsx)(l.$,{variant:"destructive",onClick:M,disabled:T,children:T?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Deleting..."]}):"Delete"})]})]})})]})}},13717:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},17580:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},26126:(e,t,a)=>{"use strict";a.d(t,{E:()=>c});var r=a(95155);a(12115);var s=a(99708),n=a(74466),i=a(59434);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:a,asChild:n=!1,...c}=e,l=n?s.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,i.cn)(o({variant:a}),t),...c})}},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>c,r:()=>o});var r=a(95155);a(12115);var s=a(99708),n=a(74466),i=a(59434);let o=(0,n.F)("inline-flex items-center cursor-pointer justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:a,size:n,asChild:c=!1,...l}=e,d=c?s.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:a,size:n,className:t})),...l})}},35181:(e,t,a)=>{Promise.resolve().then(a.bind(a,7016))},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}})},40026:(e,t,a)=>{"use strict";a.d(t,{H:()=>r,e:()=>s});let r="http://localhost:4000",s="pk_test_51ROz1YRpJ0zLf0aTbgbDkpShvfpNxdZPet1QXClapTckA7Cy0tsaxY2qY1dp8oSBGOFqnh0vugjd8mDluFWgKpRL00bACyumT8"},40968:(e,t,a)=>{"use strict";a.d(t,{b:()=>o});var r=a(12115),s=a(63655),n=a(95155),i=r.forwardRef((e,t)=>(0,n.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null===(a=e.onMouseDown)||void 0===a||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=i},51154:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>h,Es:()=>x,HM:()=>d,L3:()=>p,c7:()=>g,lG:()=>o,rr:()=>m,zM:()=>c});var r=a(95155);a(12115);var s=a(15452),n=a(54416),i=a(59434);function o(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"dialog",...t})}function c(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"dialog-trigger",...t})}function l(e){let{...t}=e;return(0,r.jsx)(s.ZL,{"data-slot":"dialog-portal",...t})}function d(e){let{...t}=e;return(0,r.jsx)(s.bm,{"data-slot":"dialog-close",...t})}function u(e){let{className:t,...a}=e;return(0,r.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-650 bg-black/50",t),...a})}function h(e){let{className:t,children:a,...o}=e;return(0,r.jsxs)(l,{"data-slot":"dialog-portal",children:[(0,r.jsx)(u,{}),(0,r.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-650 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...o,children:[a,(0,r.jsxs)(s.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(n.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function g(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function x(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function p(e){let{className:t,...a}=e;return(0,r.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",t),...a})}function m(e){let{className:t,...a}=e;return(0,r.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...a})}},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n,v:()=>i});var r=a(52596),s=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}function i(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var r=a(95155);a(12115);var s=a(59434);function n(e){let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},62525:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>c,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>d});var r=a(95155);a(12115);var s=a(59434);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("flex flex-col gap-1.5 px-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6",t),...a})}},84616:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>i});var r=a(95155);a(12115);var s=a(40968),n=a(59434);function i(e){let{className:t,...a}=e;return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},85127:(e,t,a)=>{"use strict";a.d(t,{A0:()=>i,BF:()=>o,Hj:()=>c,XI:()=>n,nA:()=>d,nd:()=>l});var r=a(95155);a(12115);var s=a(59434);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",t),...a})})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-muted-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}},88539:(e,t,a)=>{"use strict";a.d(t,{T:()=>n});var r=a(95155);a(12115);var s=a(59434);function n(e){let{className:t,...a}=e;return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}},99672:(e,t,a)=>{"use strict";a.d(t,{Dp:()=>l,EC:()=>i,J:()=>u,L_:()=>o,SA:()=>n,VO:()=>d,co:()=>c,h6:()=>s});var r=a(40026);let s=async()=>{let e=await fetch("".concat(r.H,"/api/organizations"),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch organizations");return e.json()},n=async e=>{let t=await fetch("".concat(r.H,"/api/organizations/").concat(e),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch organization");return t.json()},i=async e=>{let t=await fetch("".concat(r.H,"/api/organizations"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Failed to create organization");return t.json()},o=async(e,t)=>{let a=await fetch("".concat(r.H,"/api/organizations/").concat(e),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify(t)});if(!a.ok)throw Error((await a.json()).message||"Failed to update organization");return a.json()},c=async(e,t)=>{let a=await fetch("".concat(r.H,"/api/organizations/").concat(e,"/billing"),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify(t)});if(!a.ok)throw Error((await a.json()).message||"Failed to update organization billing");return a.json()},l=async e=>{let t=await fetch("".concat(r.H,"/api/organizations/").concat(e),{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!t.ok)throw Error((await t.json()).message||"Failed to delete organization");return t.json()},d=async(e,t,a)=>{let s=await fetch("".concat(r.H,"/api/organizations/").concat(e,"/users/").concat(t),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify({isAdmin:a})});if(!s.ok)throw Error((await s.json()).message||"Failed to add user to organization");return s.json()},u=async(e,t)=>{let a=await fetch("".concat(r.H,"/api/organizations/").concat(e,"/users/").concat(t),{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!a.ok)throw Error((await a.json()).message||"Failed to remove user from organization");return a.json()}}},e=>{var t=t=>e(e.s=t);e.O(0,[4201,4341,1071,6671,8441,1684,7358],()=>t(35181)),_N_E=e.O()}]);