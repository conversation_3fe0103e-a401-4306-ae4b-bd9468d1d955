(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{27735:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},42714:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"setAttributesFromProps",{enumerable:!0,get:function(){return a}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"},s=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"];function n(e){return["async","defer","noModule"].includes(e)}function a(e,t){for(let[a,i]of Object.entries(t)){if(!t.hasOwnProperty(a)||s.includes(a)||void 0===i)continue;let l=r[a]||a.toLowerCase();"SCRIPT"===e.tagName&&n(l)?e[l]=!!i:e.setAttribute(l,String(i)),(!1===i||"SCRIPT"===e.tagName&&n(l)&&(!i||"false"===i))&&(e.setAttribute(l,""),e.removeAttribute(l))}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},51362:(e,t,r)=>{"use strict";r.d(t,{D:()=>o,N:()=>c});var s=r(12115),n=(e,t,r,s,n,a,i,l)=>{let u=document.documentElement,o=["light","dark"];function c(t){var r;(Array.isArray(e)?e:[e]).forEach(e=>{let r="class"===e,s=r&&a?n.map(e=>a[e]||e):n;r?(u.classList.remove(...s),u.classList.add(t)):u.setAttribute(e,t)}),r=t,l&&o.includes(r)&&(u.style.colorScheme=r)}if(s)c(s);else try{let e=localStorage.getItem(t)||r,s=i&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;c(s)}catch(e){}},a=["light","dark"],i="(prefers-color-scheme: dark)",l=s.createContext(void 0),u={setTheme:e=>{},themes:[]},o=()=>{var e;return null!=(e=s.useContext(l))?e:u},c=e=>s.useContext(l)?s.createElement(s.Fragment,null,e.children):s.createElement(h,{...e}),d=["light","dark"],h=e=>{let{forcedTheme:t,disableTransitionOnChange:r=!1,enableSystem:n=!0,enableColorScheme:u=!0,storageKey:o="theme",themes:c=d,defaultTheme:h=n?"system":"light",attribute:g="data-theme",value:b,children:v,nonce:O,scriptProps:C}=e,[_,q]=s.useState(()=>m(o,h)),[w,P]=s.useState(()=>m(o)),M=b?Object.values(b):c,E=s.useCallback(e=>{let t=e;if(!t)return;"system"===e&&n&&(t=p());let s=b?b[t]:t,i=r?y(O):null,l=document.documentElement,o=e=>{"class"===e?(l.classList.remove(...M),s&&l.classList.add(s)):e.startsWith("data-")&&(s?l.setAttribute(e,s):l.removeAttribute(e))};if(Array.isArray(g)?g.forEach(o):o(g),u){let e=a.includes(h)?h:null,r=a.includes(t)?t:e;l.style.colorScheme=r}null==i||i()},[O]),Q=s.useCallback(e=>{let t="function"==typeof e?e(_):e;q(t);try{localStorage.setItem(o,t)}catch(e){}},[_]),S=s.useCallback(e=>{P(p(e)),"system"===_&&n&&!t&&E("system")},[_,t]);s.useEffect(()=>{let e=window.matchMedia(i);return e.addListener(S),S(e),()=>e.removeListener(S)},[S]),s.useEffect(()=>{let e=e=>{e.key===o&&(e.newValue?q(e.newValue):Q(h))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[Q]),s.useEffect(()=>{E(null!=t?t:_)},[t,_]);let A=s.useMemo(()=>({theme:_,setTheme:Q,forcedTheme:t,resolvedTheme:"system"===_?w:_,themes:n?[...c,"system"]:c,systemTheme:n?w:void 0}),[_,Q,t,w,n,c]);return s.createElement(l.Provider,{value:A},s.createElement(f,{forcedTheme:t,storageKey:o,attribute:g,enableSystem:n,enableColorScheme:u,defaultTheme:h,value:b,themes:c,nonce:O,scriptProps:C}),v)},f=s.memo(e=>{let{forcedTheme:t,storageKey:r,attribute:a,enableSystem:i,enableColorScheme:l,defaultTheme:u,value:o,themes:c,nonce:d,scriptProps:h}=e,f=JSON.stringify([a,r,u,t,c,o,i,l]).slice(1,-1);return s.createElement("script",{...h,suppressHydrationWarning:!0,nonce:"",dangerouslySetInnerHTML:{__html:"(".concat(n.toString(),")(").concat(f,")")}})}),m=(e,t)=>{let r;try{r=localStorage.getItem(e)||void 0}catch(e){}return r||t},y=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},p=e=>(e||(e=window.matchMedia(i)),e.matches?"dark":"light")},52626:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>a});var s=r(95155),n=r(51362);function a(e){let{children:t,...r}=e;return(0,s.jsx)(n.N,{...r,enableSystem:!0,attribute:"class",defaultTheme:"system",disableTransitionOnChange:!1,children:t})}},52728:()=>{},62093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},69243:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return b},handleClientScriptLoad:function(){return y},initScriptLoader:function(){return p}});let s=r(88229),n=r(6966),a=r(95155),i=s._(r(47650)),l=n._(r(12115)),u=r(82830),o=r(42714),c=r(92374),d=new Map,h=new Set,f=e=>{if(i.default.preinit){e.forEach(e=>{i.default.preinit(e,{as:"style"})});return}{let t=document.head;e.forEach(e=>{let r=document.createElement("link");r.type="text/css",r.rel="stylesheet",r.href=e,t.appendChild(r)})}},m=e=>{let{src:t,id:r,onLoad:s=()=>{},onReady:n=null,dangerouslySetInnerHTML:a,children:i="",strategy:l="afterInteractive",onError:u,stylesheets:c}=e,m=r||t;if(m&&h.has(m))return;if(d.has(t)){h.add(m),d.get(t).then(s,u);return}let y=()=>{n&&n(),h.add(m)},p=document.createElement("script"),g=new Promise((e,t)=>{p.addEventListener("load",function(t){e(),s&&s.call(this,t),y()}),p.addEventListener("error",function(e){t(e)})}).catch(function(e){u&&u(e)});a?(p.innerHTML=a.__html||"",y()):i?(p.textContent="string"==typeof i?i:Array.isArray(i)?i.join(""):"",y()):t&&(p.src=t,d.set(t,g)),(0,o.setAttributesFromProps)(p,e),"worker"===l&&p.setAttribute("type","text/partytown"),p.setAttribute("data-nscript",l),c&&f(c),document.body.appendChild(p)};function y(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>m(e))}):m(e)}function p(e){e.forEach(y),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");h.add(t)})}function g(e){let{id:t,src:r="",onLoad:s=()=>{},onReady:n=null,strategy:o="afterInteractive",onError:d,stylesheets:f,...y}=e,{updateScripts:p,scripts:g,getIsSsr:b,appDir:v,nonce:O}=(0,l.useContext)(u.HeadManagerContext),C=(0,l.useRef)(!1);(0,l.useEffect)(()=>{let e=t||r;C.current||(n&&e&&h.has(e)&&n(),C.current=!0)},[n,t,r]);let _=(0,l.useRef)(!1);if((0,l.useEffect)(()=>{if(!_.current){if("afterInteractive"===o)m(e);else if("lazyOnload"===o)"complete"===document.readyState?(0,c.requestIdleCallback)(()=>m(e)):window.addEventListener("load",()=>{(0,c.requestIdleCallback)(()=>m(e))});_.current=!0}},[e,o]),("beforeInteractive"===o||"worker"===o)&&(p?(g[o]=(g[o]||[]).concat([{id:t,src:r,onLoad:s,onReady:n,onError:d,...y}]),p(g)):b&&b()?h.add(t||r):b&&!b()&&m(e)),v){if(f&&f.forEach(e=>{i.default.preinit(e,{as:"style"})}),"beforeInteractive"===o)return r?(i.default.preload(r,y.integrity?{as:"script",integrity:y.integrity,nonce:O,crossOrigin:y.crossOrigin}:{as:"script",nonce:O,crossOrigin:y.crossOrigin}),(0,a.jsx)("script",{nonce:O,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([r,{...y,id:t}])+")"}})):(y.dangerouslySetInnerHTML&&(y.children=y.dangerouslySetInnerHTML.__html,delete y.dangerouslySetInnerHTML),(0,a.jsx)("script",{nonce:O,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...y,id:t}])+")"}}));"afterInteractive"===o&&r&&i.default.preload(r,y.integrity?{as:"script",integrity:y.integrity,nonce:O,crossOrigin:y.crossOrigin}:{as:"script",nonce:O,crossOrigin:y.crossOrigin})}return null}Object.defineProperty(g,"__nextScript",{value:!0});let b=g;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71456:(e,t,r)=>{"use strict";r.d(t,{default:()=>v});var s=r(95155),n=r(52020),a=r(39853),i=r(7165),l=r(25910),u=class extends l.Q{constructor(e={}){super(),this.config=e,this.#e=new Map}#e;build(e,t,r){let s=t.queryKey,i=t.queryHash??(0,n.F$)(s,t),l=this.get(i);return l||(l=new a.X({client:e,queryKey:s,queryHash:i,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(s)}),this.add(l)),l}add(e){this.#e.has(e.queryHash)||(this.#e.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#e.get(e.queryHash);t&&(e.destroy(),t===e&&this.#e.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){i.jG.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#e.get(e)}getAll(){return[...this.#e.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,n.MK)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,n.MK)(e,t)):t}notify(e){i.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){i.jG.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){i.jG.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},o=r(34560),c=class extends l.Q{constructor(e={}){super(),this.config=e,this.#t=new Set,this.#r=new Map,this.#s=0}#t;#r;#s;build(e,t,r){let s=new o.s({mutationCache:this,mutationId:++this.#s,options:e.defaultMutationOptions(t),state:r});return this.add(s),s}add(e){this.#t.add(e);let t=d(e);if("string"==typeof t){let r=this.#r.get(t);r?r.push(e):this.#r.set(t,[e])}this.notify({type:"added",mutation:e})}remove(e){if(this.#t.delete(e)){let t=d(e);if("string"==typeof t){let r=this.#r.get(t);if(r){if(r.length>1){let t=r.indexOf(e);-1!==t&&r.splice(t,1)}else r[0]===e&&this.#r.delete(t)}}}this.notify({type:"removed",mutation:e})}canRun(e){let t=d(e);if("string"!=typeof t)return!0;{let r=this.#r.get(t),s=r?.find(e=>"pending"===e.state.status);return!s||s===e}}runNext(e){let t=d(e);if("string"!=typeof t)return Promise.resolve();{let r=this.#r.get(t)?.find(t=>t!==e&&t.state.isPaused);return r?.continue()??Promise.resolve()}}clear(){i.jG.batch(()=>{this.#t.forEach(e=>{this.notify({type:"removed",mutation:e})}),this.#t.clear(),this.#r.clear()})}getAll(){return Array.from(this.#t)}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,n.nJ)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,n.nJ)(e,t))}notify(e){i.jG.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return i.jG.batch(()=>Promise.all(e.map(e=>e.continue().catch(n.lQ))))}};function d(e){return e.options.scope?.id}var h=r(50920),f=r(21239);function m(e){return{onFetch:(t,r)=>{let s=t.options,a=t.fetchOptions?.meta?.fetchMore?.direction,i=t.state.data?.pages||[],l=t.state.data?.pageParams||[],u={pages:[],pageParams:[]},o=0,c=async()=>{let r=!1,c=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?r=!0:t.signal.addEventListener("abort",()=>{r=!0}),t.signal)})},d=(0,n.ZM)(t.options,t.fetchOptions),h=async(e,s,a)=>{if(r)return Promise.reject();if(null==s&&e.pages.length)return Promise.resolve(e);let i={client:t.client,queryKey:t.queryKey,pageParam:s,direction:a?"backward":"forward",meta:t.options.meta};c(i);let l=await d(i),{maxPages:u}=t.options,o=a?n.ZZ:n.y9;return{pages:o(e.pages,l,u),pageParams:o(e.pageParams,s,u)}};if(a&&i.length){let e="backward"===a,t={pages:i,pageParams:l},r=(e?function(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}:y)(s,t);u=await h(t,r,e)}else{let t=e??i.length;do{let e=0===o?l[0]??s.initialPageParam:y(s,u);if(o>0&&null==e)break;u=await h(u,e),o++}while(o<t)}return u};t.options.persister?t.fetchFn=()=>t.options.persister?.(c,{client:t.client,queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=c}}}function y(e,{pages:t,pageParams:r}){let s=t.length-1;return t.length>0?e.getNextPageParam(t[s],t,r[s],r):void 0}var p=class{#n;#a;#i;#l;#u;#o;#c;#d;constructor(e={}){this.#n=e.queryCache||new u,this.#a=e.mutationCache||new c,this.#i=e.defaultOptions||{},this.#l=new Map,this.#u=new Map,this.#o=0}mount(){this.#o++,1===this.#o&&(this.#c=h.m.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#n.onFocus())}),this.#d=f.t.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#n.onOnline())}))}unmount(){this.#o--,0===this.#o&&(this.#c?.(),this.#c=void 0,this.#d?.(),this.#d=void 0)}isFetching(e){return this.#n.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#a.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#n.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.defaultQueryOptions(e),r=this.#n.build(this,t),s=r.state.data;return void 0===s?this.fetchQuery(e):(e.revalidateIfStale&&r.isStaleByTime((0,n.d2)(t.staleTime,r))&&this.prefetchQuery(t),Promise.resolve(s))}getQueriesData(e){return this.#n.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,r){let s=this.defaultQueryOptions({queryKey:e}),a=this.#n.get(s.queryHash),i=a?.state.data,l=(0,n.Zw)(t,i);if(void 0!==l)return this.#n.build(this,s).setData(l,{...r,manual:!0})}setQueriesData(e,t,r){return i.jG.batch(()=>this.#n.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,r)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#n.get(t.queryHash)?.state}removeQueries(e){let t=this.#n;i.jG.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let r=this.#n;return i.jG.batch(()=>(r.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries({type:"active",...e},t)))}cancelQueries(e,t={}){let r={revert:!0,...t};return Promise.all(i.jG.batch(()=>this.#n.findAll(e).map(e=>e.cancel(r)))).then(n.lQ).catch(n.lQ)}invalidateQueries(e,t={}){return i.jG.batch(()=>(this.#n.findAll(e).forEach(e=>{e.invalidate()}),e?.refetchType==="none")?Promise.resolve():this.refetchQueries({...e,type:e?.refetchType??e?.type??"active"},t))}refetchQueries(e,t={}){let r={...t,cancelRefetch:t.cancelRefetch??!0};return Promise.all(i.jG.batch(()=>this.#n.findAll(e).filter(e=>!e.isDisabled()).map(e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(n.lQ)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(n.lQ)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let r=this.#n.build(this,t);return r.isStaleByTime((0,n.d2)(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(n.lQ).catch(n.lQ)}fetchInfiniteQuery(e){return e.behavior=m(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(n.lQ).catch(n.lQ)}ensureInfiniteQueryData(e){return e.behavior=m(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return f.t.isOnline()?this.#a.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#n}getMutationCache(){return this.#a}getDefaultOptions(){return this.#i}setDefaultOptions(e){this.#i=e}setQueryDefaults(e,t){this.#l.set((0,n.EN)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#l.values()],r={};return t.forEach(t=>{(0,n.Cp)(e,t.queryKey)&&Object.assign(r,t.defaultOptions)}),r}setMutationDefaults(e,t){this.#u.set((0,n.EN)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#u.values()],r={};return t.forEach(t=>{(0,n.Cp)(e,t.mutationKey)&&Object.assign(r,t.defaultOptions)}),r}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#i.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,n.F$)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.queryFn===n.hT&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#i.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#n.clear(),this.#a.clear()}},g=r(26715),b=r(12115);function v(e){let{children:t}=e,[r]=(0,b.useState)(()=>new p);return(0,s.jsx)(g.Ht,{client:r,children:t})}},89907:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,69243,23)),Promise.resolve().then(r.t.bind(r,62093,23)),Promise.resolve().then(r.t.bind(r,27735,23)),Promise.resolve().then(r.bind(r,56671)),Promise.resolve().then(r.t.bind(r,52728,23)),Promise.resolve().then(r.bind(r,52626)),Promise.resolve().then(r.bind(r,71456))},92374:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return s},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},s="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}},e=>{var t=t=>e(e.s=t);e.O(0,[6360,6671,3860,8441,1684,7358],()=>t(89907)),_N_E=e.O()}]);