(()=>{var e={};e.id=1717,e.ids=[1717],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,r)=>{"use strict";r.d(t,{A0:()=>o,BF:()=>i,Hj:()=>l,XI:()=>n,nA:()=>c,nd:()=>d});var s=r(60687);r(43210);var a=r(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...t})})}function o({className:e,...t}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...t})}function i({className:e,...t}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-muted-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11853:(e,t,r)=>{Promise.resolve().then(r.bind(r,45772))},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>m,gC:()=>p,l6:()=>d,yv:()=>c});var s=r(60687);r(43210);var a=r(22670),n=r(78272),o=r(13964),i=r(3589),l=r(4780);function d({...e}){return(0,s.jsx)(a.bL,{"data-slot":"select",...e})}function c({...e}){return(0,s.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,children:t,...r}){return(0,s.jsxs)(a.l9,{"data-slot":"select-trigger",className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...r,children:[t,(0,s.jsx)(a.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:r="popper",...n}){return(0,s.jsx)(a.ZL,{children:(0,s.jsxs)(a.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[(0,s.jsx)(x,{}),(0,s.jsx)(a.LM,{className:(0,l.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(f,{})]})})}function m({className:e,children:t,...r}){return(0,s.jsxs)(a.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(a.VF,{children:(0,s.jsx)(o.A,{className:"size-4"})})}),(0,s.jsx)(a.p4,{children:t})]})}function x({className:e,...t}){return(0,s.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(i.A,{className:"size-4"})})}function f({className:e,...t}){return(0,s.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(n.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26134:(e,t,r)=>{"use strict";r.d(t,{G$:()=>W,Hs:()=>y,UC:()=>et,VY:()=>es,ZL:()=>Q,bL:()=>Y,bm:()=>ea,hE:()=>er,hJ:()=>ee,l9:()=>K,lG:()=>N});var s=r(43210),a=r(70569),n=r(98599),o=r(11273),i=r(96963),l=r(65551),d=r(31355),c=r(32547),u=r(25028),p=r(46059),m=r(14163),x=r(1359),f=r(42247),h=r(63376),g=r(8730),v=r(60687),j="Dialog",[b,y]=(0,o.A)(j),[w,k]=b(j),N=e=>{let{__scopeDialog:t,children:r,open:a,defaultOpen:n,onOpenChange:o,modal:d=!0}=e,c=s.useRef(null),u=s.useRef(null),[p=!1,m]=(0,l.i)({prop:a,defaultProp:n,onChange:o});return(0,v.jsx)(w,{scope:t,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:m,onOpenToggle:s.useCallback(()=>m(e=>!e),[m]),modal:d,children:r})};N.displayName=j;var C="DialogTrigger",z=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,o=k(C,r),i=(0,n.s)(t,o.triggerRef);return(0,v.jsx)(m.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":V(o.open),...s,ref:i,onClick:(0,a.m)(e.onClick,o.onOpenToggle)})});z.displayName=C;var A="DialogPortal",[_,E]=b(A,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:r,children:a,container:n}=e,o=k(A,t);return(0,v.jsx)(_,{scope:t,forceMount:r,children:s.Children.map(a,e=>(0,v.jsx)(p.C,{present:r||o.open,children:(0,v.jsx)(u.Z,{asChild:!0,container:n,children:e})}))})};O.displayName=A;var D="DialogOverlay",S=s.forwardRef((e,t)=>{let r=E(D,e.__scopeDialog),{forceMount:s=r.forceMount,...a}=e,n=k(D,e.__scopeDialog);return n.modal?(0,v.jsx)(p.C,{present:s||n.open,children:(0,v.jsx)($,{...a,ref:t})}):null});S.displayName=D;var $=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,a=k(D,r);return(0,v.jsx)(f.A,{as:g.DX,allowPinchZoom:!0,shards:[a.contentRef],children:(0,v.jsx)(m.sG.div,{"data-state":V(a.open),...s,ref:t,style:{pointerEvents:"auto",...s.style}})})}),R="DialogContent",P=s.forwardRef((e,t)=>{let r=E(R,e.__scopeDialog),{forceMount:s=r.forceMount,...a}=e,n=k(R,e.__scopeDialog);return(0,v.jsx)(p.C,{present:s||n.open,children:n.modal?(0,v.jsx)(F,{...a,ref:t}):(0,v.jsx)(q,{...a,ref:t})})});P.displayName=R;var F=s.forwardRef((e,t)=>{let r=k(R,e.__scopeDialog),o=s.useRef(null),i=(0,n.s)(t,r.contentRef,o);return s.useEffect(()=>{let e=o.current;if(e)return(0,h.Eq)(e)},[]),(0,v.jsx)(I,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault())})}),q=s.forwardRef((e,t)=>{let r=k(R,e.__scopeDialog),a=s.useRef(!1),n=s.useRef(!1);return(0,v.jsx)(I,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(a.current||r.triggerRef.current?.focus(),t.preventDefault()),a.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(a.current=!0,"pointerdown"!==t.detail.originalEvent.type||(n.current=!0));let s=t.target;r.triggerRef.current?.contains(s)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),I=s.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:a,onOpenAutoFocus:o,onCloseAutoFocus:i,...l}=e,u=k(R,r),p=s.useRef(null),m=(0,n.s)(t,p);return(0,x.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(c.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:o,onUnmountAutoFocus:i,children:(0,v.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":V(u.open),...l,ref:m,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(Z,{titleId:u.titleId}),(0,v.jsx)(X,{contentRef:p,descriptionId:u.descriptionId})]})]})}),T="DialogTitle",B=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,a=k(T,r);return(0,v.jsx)(m.sG.h2,{id:a.titleId,...s,ref:t})});B.displayName=T;var U="DialogDescription",M=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,a=k(U,r);return(0,v.jsx)(m.sG.p,{id:a.descriptionId,...s,ref:t})});M.displayName=U;var L="DialogClose",H=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,n=k(L,r);return(0,v.jsx)(m.sG.button,{type:"button",...s,ref:t,onClick:(0,a.m)(e.onClick,()=>n.onOpenChange(!1))})});function V(e){return e?"open":"closed"}H.displayName=L;var G="DialogTitleWarning",[W,J]=(0,o.q)(G,{contentName:R,titleName:T,docsSlug:"dialog"}),Z=({titleId:e})=>{let t=J(G),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return s.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},X=({contentRef:e,descriptionId:t})=>{let r=J("DialogDescriptionWarning"),a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return s.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(a)},[a,e,t]),null},Y=N,K=z,Q=O,ee=S,et=P,er=B,es=M,ea=H},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},35821:(e,t,r)=>{Promise.resolve().then(r.bind(r,94341))},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>i,Zp:()=>n,aR:()=>o,wL:()=>c});var s=r(60687);r(43210);var a=r(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("flex flex-col gap-1.5 px-6",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6",e),...t})}},45772:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\app\\\\(workspace)\\\\workspaces\\\\[id]\\\\users\\\\OrganizationUsersContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\[id]\\users\\OrganizationUsersContent.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>p,Es:()=>x,HM:()=>c,L3:()=>f,c7:()=>m,lG:()=>i,rr:()=>h,zM:()=>l});var s=r(60687);r(43210);var a=r(26134),n=r(11860),o=r(4780);function i({...e}){return(0,s.jsx)(a.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,s.jsx)(a.l9,{"data-slot":"dialog-trigger",...e})}function d({...e}){return(0,s.jsx)(a.ZL,{"data-slot":"dialog-portal",...e})}function c({...e}){return(0,s.jsx)(a.bm,{"data-slot":"dialog-close",...e})}function u({className:e,...t}){return(0,s.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-650 bg-black/50",e),...t})}function p({className:e,children:t,...r}){return(0,s.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,s.jsx)(u,{}),(0,s.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-650 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...r,children:[t,(0,s.jsxs)(a.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(n.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function x({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function f({className:e,...t}){return(0,s.jsx)(a.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",e),...t})}function h({className:e,...t}){return(0,s.jsx)(a.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...t})}},74075:e=>{"use strict";e.exports=require("zlib")},74678:(e,t,r)=>{"use strict";r.d(t,{H:()=>s,e:()=>a});let s="http://localhost:4000",a="pk_test_51ROz1YRpJ0zLf0aTbgbDkpShvfpNxdZPet1QXClapTckA7Cy0tsaxY2qY1dp8oSBGOFqnh0vugjd8mDluFWgKpRL00bACyumT8"},76450:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),o=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["(workspace)",{children:["workspaces",{children:["[id]",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,83971)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\[id]\\users\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,50184)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\[id]\\users\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(workspace)/workspaces/[id]/users/page",pathname:"/workspaces/[id]/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},78148:(e,t,r)=>{"use strict";r.d(t,{b:()=>i});var s=r(43210),a=r(14163),n=r(60687),o=s.forwardRef((e,t)=>(0,n.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var i=o},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var s=r(60687);r(43210);var a=r(78148),n=r(4780);function o({className:e,...t}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},83971:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(37413);r(61120);var a=r(45772);function n({params:e}){return(0,s.jsx)(a.default,{organizationId:e.id})}},83997:e=>{"use strict";e.exports=require("tty")},87610:(e,t,r)=>{"use strict";r.d(t,{Dp:()=>d,EC:()=>o,J:()=>u,L_:()=>i,SA:()=>n,VO:()=>c,co:()=>l,h6:()=>a});var s=r(74678);let a=async()=>{let e=await fetch(`${s.H}/api/organizations`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch organizations");return e.json()},n=async e=>{let t=await fetch(`${s.H}/api/organizations/${e}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch organization");return t.json()},o=async e=>{let t=await fetch(`${s.H}/api/organizations`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Failed to create organization");return t.json()},i=async(e,t)=>{let r=await fetch(`${s.H}/api/organizations/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify(t)});if(!r.ok)throw Error((await r.json()).message||"Failed to update organization");return r.json()},l=async(e,t)=>{let r=await fetch(`${s.H}/api/organizations/${e}/billing`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify(t)});if(!r.ok)throw Error((await r.json()).message||"Failed to update organization billing");return r.json()},d=async e=>{let t=await fetch(`${s.H}/api/organizations/${e}`,{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!t.ok)throw Error((await t.json()).message||"Failed to delete organization");return t.json()},c=async(e,t,r)=>{let a=await fetch(`${s.H}/api/organizations/${e}/users/${t}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify({isAdmin:r})});if(!a.ok)throw Error((await a.json()).message||"Failed to add user to organization");return a.json()},u=async(e,t)=>{let r=await fetch(`${s.H}/api/organizations/${e}/users/${t}`,{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!r.ok)throw Error((await r.json()).message||"Failed to remove user from organization");return r.json()}},88233:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(60687);r(43210);var a=r(4780);function n({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},91645:e=>{"use strict";e.exports=require("net")},94341:(e,t,r)=>{"use strict";r.d(t,{default:()=>k});var s=r(60687),a=r(43210),n=r(16189),o=r(44493),i=r(6211),l=r(63503),d=r(15079),c=r(29523),u=r(89667),p=r(80013),m=r(96834),x=r(41862),f=r(28559),h=r(62688);let g=(0,h.A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),v=(0,h.A)("ShieldOff",[["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M5 5a1 1 0 0 0-1 1v7c0 5 3.5 7.5 7.67 8.94a1 1 0 0 0 .67.01c2.35-.82 4.48-1.97 5.9-3.71",key:"1jlk70"}],["path",{d:"M9.309 3.652A12.252 12.252 0 0 0 11.24 2.28a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1v7a9.784 9.784 0 0 1-.08 1.264",key:"18rp1v"}]]),j=(0,h.A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var b=r(88233),y=r(52581),w=r(87610);function k({organizationId:e}){let[t,r]=(0,a.useState)(null),[h,k]=(0,a.useState)([]),[N,C]=(0,a.useState)(!0),[z,A]=(0,a.useState)(!1),[_,E]=(0,a.useState)(!1),[O,D]=(0,a.useState)(""),[S,$]=(0,a.useState)(!1),[R,P]=(0,a.useState)(null),[F,q]=(0,a.useState)(!1),[I,T]=(0,a.useState)(""),B=(0,n.useRouter)(),U=async()=>{if(O)try{q(!0);let t=await (0,w.VO)(e,O,S);r(t),A(!1),D(""),$(!1),y.o.success("User added to workspace successfully")}catch(e){console.error("Error adding user to Workspace:",e),y.o.error("Failed to add user to workspace")}finally{q(!1)}},M=async()=>{if(R)try{q(!0);let t=await (0,w.J)(e,R);r(t),E(!1),P(null),y.o.success("User removed from workspace successfully")}catch(e){console.error("Error removing user from Workspace:",e),y.o.error("Failed to remove user from workspace")}finally{q(!1)}},L=async(t,s)=>{try{q(!0);let a=await (0,w.VO)(e,t,s);r(a),y.o.success(`User ${s?"promoted to admin":"demoted from admin"} successfully`)}catch(e){console.error("Error updating user role:",e),y.o.error("Failed to update user role")}finally{q(!1)}},H=e=>h.find(t=>t._id===e);if(N)return(0,s.jsx)("div",{className:"container mx-auto py-6 flex justify-center items-center min-h-[60vh]",children:(0,s.jsx)(x.A,{className:"h-8 w-8 animate-spin"})});if(!t)return(0,s.jsx)("div",{className:"container mx-auto py-6",children:(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Workspace not found."})});let V=(()=>{if(!t)return[];let e=[...t.users||[],...t.adminUsers||[]];return h.filter(t=>!e.includes(t._id)&&(""===I||t.fullName.toLowerCase().includes(I.toLowerCase())||t.email.toLowerCase().includes(I.toLowerCase())))})(),G=[...(t.adminUsers||[]).map(e=>({id:e,isAdmin:!0})),...(t.users||[]).map(e=>({id:e,isAdmin:!1}))];return(0,s.jsxs)("div",{className:"container mx-auto py-6",children:[(0,s.jsxs)("div",{className:"flex items-center mb-6",children:[(0,s.jsxs)(c.$,{variant:"outline",onClick:()=>B.push("/workspaces"),className:"mr-4",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,s.jsxs)("h1",{className:"text-3xl font-bold",children:[t.name," - Users"]})]}),(0,s.jsx)("div",{className:"flex justify-end mb-6",children:(0,s.jsxs)(l.lG,{open:z,onOpenChange:A,children:[(0,s.jsx)(l.zM,{asChild:!0,children:(0,s.jsxs)(c.$,{children:[(0,s.jsx)(g,{className:"mr-2 h-4 w-4"}),"Add User"]})}),(0,s.jsxs)(l.Cf,{children:[(0,s.jsxs)(l.c7,{children:[(0,s.jsx)(l.L3,{children:"Add User to Organization"}),(0,s.jsx)(l.rr,{children:"Select a user to add to this organization."})]}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(p.J,{htmlFor:"search",className:"text-right",children:"Search"}),(0,s.jsx)(u.p,{id:"search",placeholder:"Search by name or email",value:I,onChange:e=>T(e.target.value),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(p.J,{htmlFor:"user",className:"text-right",children:"User"}),(0,s.jsxs)(d.l6,{value:O,onValueChange:D,children:[(0,s.jsx)(d.bq,{className:"col-span-3",children:(0,s.jsx)(d.yv,{placeholder:"Select a user"})}),(0,s.jsx)(d.gC,{className:"z-[700]",children:0===V.length?(0,s.jsx)(d.eb,{value:"none",disabled:!0,children:"No available users"}):V.map(e=>(0,s.jsxs)(d.eb,{value:e._id,children:[e.fullName," (",e.email,")"]},e._id))})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(p.J,{htmlFor:"role",className:"text-right",children:"Role"}),(0,s.jsxs)(d.l6,{value:S?"admin":"user",onValueChange:e=>$("admin"===e),children:[(0,s.jsx)(d.bq,{className:"col-span-3",children:(0,s.jsx)(d.yv,{placeholder:"Select role"})}),(0,s.jsxs)(d.gC,{className:"z-[700]",children:[(0,s.jsx)(d.eb,{value:"user",children:"Regular User"}),(0,s.jsx)(d.eb,{value:"admin",children:"Admin"})]})]})]})]}),(0,s.jsxs)(l.Es,{children:[(0,s.jsx)(c.$,{variant:"outline",onClick:()=>A(!1),children:"Cancel"}),(0,s.jsx)(c.$,{onClick:U,disabled:F||!O,children:F?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(x.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Adding..."]}):"Add User"})]})]})]})}),(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsx)(o.ZB,{children:"Organization Users"}),(0,s.jsx)(o.BT,{children:"Manage users in this organization."})]}),(0,s.jsx)(o.Wu,{children:0===G.length?(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No users in this organization. Add users to get started."}):(0,s.jsxs)(i.XI,{children:[(0,s.jsx)(i.A0,{children:(0,s.jsxs)(i.Hj,{children:[(0,s.jsx)(i.nd,{children:"Name"}),(0,s.jsx)(i.nd,{children:"Email"}),(0,s.jsx)(i.nd,{children:"Role"}),(0,s.jsx)(i.nd,{className:"text-right",children:"Actions"})]})}),(0,s.jsx)(i.BF,{children:G.map(({id:e,isAdmin:t})=>{let r=H(e);return r?(0,s.jsxs)(i.Hj,{children:[(0,s.jsx)(i.nA,{className:"font-medium",children:r.fullName}),(0,s.jsx)(i.nA,{children:r.email}),(0,s.jsx)(i.nA,{children:t?(0,s.jsx)(m.E,{className:"bg-blue-500",children:"Admin"}):(0,s.jsx)(m.E,{children:"User"})}),(0,s.jsx)(i.nA,{className:"text-right",children:(0,s.jsxs)("div",{className:"flex justify-end gap-2",children:[t?(0,s.jsx)(c.$,{variant:"outline",size:"icon",title:"Demote to regular user",onClick:()=>L(e,!1),disabled:F,children:(0,s.jsx)(v,{className:"h-4 w-4"})}):(0,s.jsx)(c.$,{variant:"outline",size:"icon",title:"Promote to admin",onClick:()=>L(e,!0),disabled:F,children:(0,s.jsx)(j,{className:"h-4 w-4"})}),(0,s.jsx)(c.$,{variant:"outline",size:"icon",className:"text-red-500",title:"Remove from organization",onClick:()=>{P(e),E(!0)},disabled:F,children:(0,s.jsx)(b.A,{className:"h-4 w-4"})})]})})]},e):null})})]})})]}),(0,s.jsx)(l.lG,{open:_,onOpenChange:E,children:(0,s.jsxs)(l.Cf,{children:[(0,s.jsxs)(l.c7,{children:[(0,s.jsx)(l.L3,{children:"Remove User"}),(0,s.jsx)(l.rr,{children:"Are you sure you want to remove this user from the organization? They will lose access to all organization resources."})]}),(0,s.jsxs)(l.Es,{children:[(0,s.jsx)(c.$,{variant:"outline",onClick:()=>E(!1),children:"Cancel"}),(0,s.jsx)(c.$,{variant:"destructive",onClick:M,disabled:F,children:F?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(x.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Removing..."]}):"Remove"})]})]})})]})}r(24258)},94735:e=>{"use strict";e.exports=require("events")},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(60687);r(43210);var a=r(8730),n=r(24224),o=r(4780);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...n}){let l=r?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,o.cn)(i({variant:t}),e),...n})}}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[287,9176,7674,5814,598,5188,6034,1476,4772],()=>r(76450));module.exports=s})();