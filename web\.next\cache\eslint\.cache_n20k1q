[{"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\animations\\AnimatedSection.tsx": "1", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\animations\\FadeIn.tsx": "2", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\animations\\ScaleIn.tsx": "3", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\animations\\SlideIn.tsx": "4", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\animations\\StaggerContainer.tsx": "5", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(auth)\\actions\\auth.ts": "6", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(auth)\\forgotpassword\\page.tsx": "7", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(auth)\\login\\page.tsx": "8", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(auth)\\register\\page.tsx": "9", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\AgentsContent.tsx": "10", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\create\\CreateAgentContent.tsx": "11", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\create\\page.tsx": "12", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\edit\\[id]\\EditAgentContent.tsx": "13", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\edit\\[id]\\page.tsx": "14", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\page.tsx": "15", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\billing\\BillingContent.tsx": "16", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\billing\\page.tsx": "17", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\brain\\BrainContent.tsx": "18", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\brain\\page.tsx": "19", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\CampaignContent.tsx": "20", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\create\\CreateCampaign.tsx": "21", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\create\\page.tsx": "22", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\create\\steps\\AgentsStep.tsx": "23", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\create\\steps\\ScheduleStep.tsx": "24", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\create\\steps\\SettingsSteps.tsx": "25", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\create\\steps\\SourcesStep.tsx": "26", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\edit\\[id]\\EditCampaign.tsx": "27", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\edit\\[id]\\page.tsx": "28", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\page.tsx": "29", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\ContactsContent.tsx": "30", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\create\\CreateContact.tsx": "31", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\create\\page.tsx": "32", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\edit\\[contactName]\\page.tsx": "33", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\edit\\[contactName]\\[contactId]\\EditContact.tsx": "34", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\edit\\[contactName]\\[contactId]\\page.tsx": "35", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\page.tsx": "36", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\dashboard\\DashboardContent.tsx": "37", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\dashboard\\page.tsx": "38", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\history\\HistoryContent.tsx": "39", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\history\\page.tsx": "40", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\history\\[fullName]\\page.tsx": "41", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\integration\\IntegrationContent.tsx": "42", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\integration\\page.tsx": "43", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\layout.tsx": "44", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\phonenumber\\buy\\BuyPhoneNumberContent.tsx": "45", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\phonenumber\\buy\\page.tsx": "46", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\phonenumber\\page.tsx": "47", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\phonenumber\\PhoneNumberContent.tsx": "48", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\profile\\page.tsx": "49", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\profile\\ProfileContent.tsx": "50", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\schedule\\page.tsx": "51", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\schedule\\ScheduleContent.tsx": "52", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\settings\\page.tsx": "53", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\settings\\SettingsContent.tsx": "54", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\users\\page.tsx": "55", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\users\\UsersManagement.tsx": "56", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\voices\\page.tsx": "57", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\voices\\VoicesContent.tsx": "58", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\OrganizationsContent.tsx": "59", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\page.tsx": "60", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\[id]\\OrganizationDetailsContent.tsx": "61", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\[id]\\page.tsx": "62", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\[id]\\users\\OrganizationUsersContent.tsx": "63", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\[id]\\users\\page.tsx": "64", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\api\\billing.ts": "65", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\api\\campaign.ts": "66", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\api\\config.ts": "67", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\api\\contacts.ts": "68", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\api\\globalSettings.ts": "69", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\api\\organizations.ts": "70", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\api\\users.ts": "71", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\layout.tsx": "72", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\page.tsx": "73", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentActionsTab.tsx": "74", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentPerformanceTab.tsx": "75", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentProfileTab.tsx": "76", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentRoleTab.tsx": "77", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\APIKeyInput.tsx": "78", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\braincomponents\\AddParagraphModal.tsx": "79", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\braincomponents\\AddWebLinkModal.tsx": "80", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\braincomponents\\EmptyState.tsx": "81", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\braincomponents\\FilesView.tsx": "82", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\braincomponents\\FileUploadModal.tsx": "83", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\braincomponents\\KnowledgeBaseSidebar.tsx": "84", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\braincomponents\\ParagraphsView.tsx": "85", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\braincomponents\\SpreadsheetsView.tsx": "86", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\braincomponents\\WebLinksView.tsx": "87", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ChatWidget.tsx": "88", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\CreditDisplay.tsx": "89", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\CTASection.tsx": "90", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\FAQSection.tsx": "91", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\FeaturesGrid.tsx": "92", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\Footer.tsx": "93", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\HeroSection.tsx": "94", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\IntegrationCard.tsx": "95", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\LanguageSwitcher.tsx": "96", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\LowCreditAlert.tsx": "97", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\Navigation.tsx": "98", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\PricingSection.tsx": "99", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ProfileMenu.tsx": "100", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\settingscomponents\\APISettings.tsx": "101", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\settingscomponents\\GeneralSettings.tsx": "102", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\settingscomponents\\LogsSettings.tsx": "103", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\settingscomponents\\VoiceSettings.tsx": "104", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\stripe\\AutoRechargeSettings.tsx": "105", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\stripe\\CardPaymentForm.tsx": "106", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\stripe\\SavedPaymentMethods.tsx": "107", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\stripe\\StripeProvider.tsx": "108", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\Testimonials.tsx": "109", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\theme\\ThemeProvider.tsx": "110", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\accordion.tsx": "111", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\alert-dialog.tsx": "112", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\alert.tsx": "113", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\avatar.tsx": "114", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\badge.tsx": "115", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\button.tsx": "116", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\calendar.tsx": "117", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\card.tsx": "118", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\checkbox.tsx": "119", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\command.tsx": "120", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\dialog.tsx": "121", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\dropdown-menu.tsx": "122", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\input.tsx": "123", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\label.tsx": "124", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\popover.tsx": "125", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\radio-group.tsx": "126", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\select.tsx": "127", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\slider.tsx": "128", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\success-dialog.tsx": "129", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\switch.tsx": "130", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\table.tsx": "131", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\tabs.tsx": "132", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\textarea.tsx": "133", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\timezone-selector.tsx": "134", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\tooltip.tsx": "135", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\VoiceCallModal.tsx": "136", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\contexts\\CreditContext.tsx": "137", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\hooks\\useWebSocket.ts": "138", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\lib\\auth-client.ts": "139", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\lib\\authFetch.ts": "140", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\lib\\dateUtils.ts": "141", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\lib\\phone-utils.ts": "142", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\lib\\utils.ts": "143", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\lib\\validations\\authSchema.ts": "144", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\api\\organizationSettings.ts": "145", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentAdvancedTab.tsx": "146", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentBrainTab.tsx": "147", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentSidebar.tsx": "148", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentVoiceTab.tsx": "149", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\hooks\\useAgent.ts": "150", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\hooks\\useAgentList.ts": "151", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\hooks\\useAuth.ts": "152", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\types\\agent.types.ts": "153", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\types\\campaign.types.ts": "154", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\types\\dashboard.types.ts": "155", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\lib\\providers\\ReactQueryProvider.tsx": "156", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\CreditWarningAlert.tsx": "157", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentChatWidget.tsx": "158", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentPhoneCallDialog.tsx": "159", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\types\\contact.types.ts": "160", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentWebCallDialog.tsx": "161", "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\scroll-area.tsx": "162"}, {"size": 2263, "mtime": 1741708664350, "results": "163", "hashOfConfig": "164"}, {"size": 1293, "mtime": 1741708664352, "results": "165", "hashOfConfig": "164"}, {"size": 908, "mtime": 1741708664353, "results": "166", "hashOfConfig": "164"}, {"size": 1282, "mtime": 1741708664355, "results": "167", "hashOfConfig": "164"}, {"size": 1364, "mtime": 1741708664357, "results": "168", "hashOfConfig": "164"}, {"size": 10468, "mtime": 1743678049548, "results": "169", "hashOfConfig": "164"}, {"size": 4352, "mtime": 1747915874029, "results": "170", "hashOfConfig": "164"}, {"size": 9912, "mtime": 1747930066045, "results": "171", "hashOfConfig": "164"}, {"size": 11884, "mtime": 1742347439393, "results": "172", "hashOfConfig": "164"}, {"size": 15642, "mtime": 1748881932154, "results": "173", "hashOfConfig": "164"}, {"size": 9287, "mtime": 1748881932164, "results": "174", "hashOfConfig": "164"}, {"size": 192, "mtime": 1741708664384, "results": "175", "hashOfConfig": "164"}, {"size": 13456, "mtime": 1748881932169, "results": "176", "hashOfConfig": "164"}, {"size": 294, "mtime": 1748336128741, "results": "177", "hashOfConfig": "164"}, {"size": 147, "mtime": 1741708664390, "results": "178", "hashOfConfig": "164"}, {"size": 42807, "mtime": 1748516500981, "results": "179", "hashOfConfig": "164"}, {"size": 172, "mtime": 1742205273063, "results": "180", "hashOfConfig": "164"}, {"size": 7565, "mtime": 1741708664393, "results": "181", "hashOfConfig": "164"}, {"size": 256, "mtime": 1741708664395, "results": "182", "hashOfConfig": "164"}, {"size": 14962, "mtime": 1748535871952, "results": "183", "hashOfConfig": "164"}, {"size": 12768, "mtime": 1748336128752, "results": "184", "hashOfConfig": "164"}, {"size": 172, "mtime": 1744322480662, "results": "185", "hashOfConfig": "164"}, {"size": 4409, "mtime": 1747655786022, "results": "186", "hashOfConfig": "164"}, {"size": 24972, "mtime": 1747655786027, "results": "187", "hashOfConfig": "164"}, {"size": 10569, "mtime": 1747655786030, "results": "188", "hashOfConfig": "164"}, {"size": 14740, "mtime": 1748434147301, "results": "189", "hashOfConfig": "164"}, {"size": 20676, "mtime": 1748446035174, "results": "190", "hashOfConfig": "164"}, {"size": 268, "mtime": 1747930066062, "results": "191", "hashOfConfig": "164"}, {"size": 175, "mtime": 1742205273072, "results": "192", "hashOfConfig": "164"}, {"size": 76939, "mtime": 1748854683031, "results": "193", "hashOfConfig": "164"}, {"size": 26687, "mtime": 1747655786098, "results": "194", "hashOfConfig": "164"}, {"size": 146, "mtime": 1743072543224, "results": "195", "hashOfConfig": "164"}, {"size": 1739, "mtime": 1747655786168, "results": "196", "hashOfConfig": "164"}, {"size": 33245, "mtime": 1747930066069, "results": "197", "hashOfConfig": "164"}, {"size": 356, "mtime": 1748446035179, "results": "198", "hashOfConfig": "164"}, {"size": 156, "mtime": 1741708664398, "results": "199", "hashOfConfig": "164"}, {"size": 31846, "mtime": 1748336128775, "results": "200", "hashOfConfig": "164"}, {"size": 259, "mtime": 1741708664402, "results": "201", "hashOfConfig": "164"}, {"size": 69351, "mtime": 1748597258917, "results": "202", "hashOfConfig": "164"}, {"size": 148, "mtime": 1742554681055, "results": "203", "hashOfConfig": "164"}, {"size": 277, "mtime": 1747930066080, "results": "204", "hashOfConfig": "164"}, {"size": 6040, "mtime": 1741708664409, "results": "205", "hashOfConfig": "164"}, {"size": 166, "mtime": 1741708664411, "results": "206", "hashOfConfig": "164"}, {"size": 19573, "mtime": 1748511600976, "results": "207", "hashOfConfig": "164"}, {"size": 19315, "mtime": 1741708664416, "results": "208", "hashOfConfig": "164"}, {"size": 204, "mtime": 1741708664419, "results": "209", "hashOfConfig": "164"}, {"size": 167, "mtime": 1741708664420, "results": "210", "hashOfConfig": "164"}, {"size": 4026, "mtime": 1741708664414, "results": "211", "hashOfConfig": "164"}, {"size": 146, "mtime": 1742205273114, "results": "212", "hashOfConfig": "164"}, {"size": 5898, "mtime": 1743675904861, "results": "213", "hashOfConfig": "164"}, {"size": 150, "mtime": 1742983856072, "results": "214", "hashOfConfig": "164"}, {"size": 35822, "mtime": 1748955240259, "results": "215", "hashOfConfig": "164"}, {"size": 155, "mtime": 1741708664423, "results": "216", "hashOfConfig": "164"}, {"size": 2731, "mtime": 1748532376488, "results": "217", "hashOfConfig": "164"}, {"size": 176, "mtime": 1742257294884, "results": "218", "hashOfConfig": "164"}, {"size": 19728, "mtime": 1742347439419, "results": "219", "hashOfConfig": "164"}, {"size": 149, "mtime": 1741708664427, "results": "220", "hashOfConfig": "164"}, {"size": 7209, "mtime": 1741708664425, "results": "221", "hashOfConfig": "164"}, {"size": 10486, "mtime": 1748434391052, "results": "222", "hashOfConfig": "164"}, {"size": 176, "mtime": 1747930066102, "results": "223", "hashOfConfig": "164"}, {"size": 21751, "mtime": 1748604143758, "results": "224", "hashOfConfig": "164"}, {"size": 313, "mtime": 1747930066097, "results": "225", "hashOfConfig": "164"}, {"size": 14073, "mtime": 1747995787549, "results": "226", "hashOfConfig": "164"}, {"size": 305, "mtime": 1747930066101, "results": "227", "hashOfConfig": "164"}, {"size": 5806, "mtime": 1747930066104, "results": "228", "hashOfConfig": "164"}, {"size": 4851, "mtime": 1747655786240, "results": "229", "hashOfConfig": "164"}, {"size": 206, "mtime": 1747655786243, "results": "230", "hashOfConfig": "164"}, {"size": 10125, "mtime": 1748854683036, "results": "231", "hashOfConfig": "164"}, {"size": 1359, "mtime": 1748532457251, "results": "232", "hashOfConfig": "164"}, {"size": 6792, "mtime": 1748605292256, "results": "233", "hashOfConfig": "164"}, {"size": 11252, "mtime": 1748515359010, "results": "234", "hashOfConfig": "164"}, {"size": 2548, "mtime": 1748511134046, "results": "235", "hashOfConfig": "164"}, {"size": 3328, "mtime": 1742554681060, "results": "236", "hashOfConfig": "164"}, {"size": 3999, "mtime": 1748336128796, "results": "237", "hashOfConfig": "164"}, {"size": 7462, "mtime": 1748336128807, "results": "238", "hashOfConfig": "164"}, {"size": 20348, "mtime": 1748881932182, "results": "239", "hashOfConfig": "164"}, {"size": 5326, "mtime": 1748540227728, "results": "240", "hashOfConfig": "164"}, {"size": 3011, "mtime": 1741708664442, "results": "241", "hashOfConfig": "164"}, {"size": 2746, "mtime": 1741708664476, "results": "242", "hashOfConfig": "164"}, {"size": 2656, "mtime": 1741708664478, "results": "243", "hashOfConfig": "164"}, {"size": 1075, "mtime": 1741708664480, "results": "244", "hashOfConfig": "164"}, {"size": 2016, "mtime": 1741708664483, "results": "245", "hashOfConfig": "164"}, {"size": 6094, "mtime": 1741708664481, "results": "246", "hashOfConfig": "164"}, {"size": 1563, "mtime": 1741708664485, "results": "247", "hashOfConfig": "164"}, {"size": 2038, "mtime": 1741708664486, "results": "248", "hashOfConfig": "164"}, {"size": 2140, "mtime": 1741708664489, "results": "249", "hashOfConfig": "164"}, {"size": 2675, "mtime": 1741708664490, "results": "250", "hashOfConfig": "164"}, {"size": 7129, "mtime": 1741708664445, "results": "251", "hashOfConfig": "164"}, {"size": 4260, "mtime": 1748858320698, "results": "252", "hashOfConfig": "164"}, {"size": 817, "mtime": 1741708664444, "results": "253", "hashOfConfig": "164"}, {"size": 2222, "mtime": 1741708664447, "results": "254", "hashOfConfig": "164"}, {"size": 3168, "mtime": 1741708664449, "results": "255", "hashOfConfig": "164"}, {"size": 3092, "mtime": 1741708664451, "results": "256", "hashOfConfig": "164"}, {"size": 1302, "mtime": 1741708664452, "results": "257", "hashOfConfig": "164"}, {"size": 3631, "mtime": 1741708664454, "results": "258", "hashOfConfig": "164"}, {"size": 1628, "mtime": 1741708664455, "results": "259", "hashOfConfig": "164"}, {"size": 3225, "mtime": 1748858273178, "results": "260", "hashOfConfig": "164"}, {"size": 2202, "mtime": 1741708664456, "results": "261", "hashOfConfig": "164"}, {"size": 3757, "mtime": 1741708664458, "results": "262", "hashOfConfig": "164"}, {"size": 3621, "mtime": 1747930066121, "results": "263", "hashOfConfig": "164"}, {"size": 2939, "mtime": 1741708664492, "results": "264", "hashOfConfig": "164"}, {"size": 3945, "mtime": 1748358200926, "results": "265", "hashOfConfig": "164"}, {"size": 12058, "mtime": 1747655786377, "results": "266", "hashOfConfig": "164"}, {"size": 2464, "mtime": 1741708664497, "results": "267", "hashOfConfig": "164"}, {"size": 6485, "mtime": 1748440439693, "results": "268", "hashOfConfig": "164"}, {"size": 7840, "mtime": 1747930066127, "results": "269", "hashOfConfig": "164"}, {"size": 13995, "mtime": 1748015485374, "results": "270", "hashOfConfig": "164"}, {"size": 622, "mtime": 1747930066132, "results": "271", "hashOfConfig": "164"}, {"size": 3719, "mtime": 1741708664462, "results": "272", "hashOfConfig": "164"}, {"size": 461, "mtime": 1741708664499, "results": "273", "hashOfConfig": "164"}, {"size": 2119, "mtime": 1741708664502, "results": "274", "hashOfConfig": "164"}, {"size": 4023, "mtime": 1747930066135, "results": "275", "hashOfConfig": "164"}, {"size": 1695, "mtime": 1741708664503, "results": "276", "hashOfConfig": "164"}, {"size": 1150, "mtime": 1741708664504, "results": "277", "hashOfConfig": "164"}, {"size": 1654, "mtime": 1741708664505, "results": "278", "hashOfConfig": "164"}, {"size": 2197, "mtime": 1744322480728, "results": "279", "hashOfConfig": "164"}, {"size": 2995, "mtime": 1744322480732, "results": "280", "hashOfConfig": "164"}, {"size": 1573, "mtime": 1748434147324, "results": "281", "hashOfConfig": "164"}, {"size": 1258, "mtime": 1744322480736, "results": "282", "hashOfConfig": "164"}, {"size": 4833, "mtime": 1743115434015, "results": "283", "hashOfConfig": "164"}, {"size": 3950, "mtime": 1747930066137, "results": "284", "hashOfConfig": "164"}, {"size": 8389, "mtime": 1741708664513, "results": "285", "hashOfConfig": "164"}, {"size": 971, "mtime": 1741708664515, "results": "286", "hashOfConfig": "164"}, {"size": 635, "mtime": 1741708664517, "results": "287", "hashOfConfig": "164"}, {"size": 1683, "mtime": 1743115434022, "results": "288", "hashOfConfig": "164"}, {"size": 1494, "mtime": 1741708664519, "results": "289", "hashOfConfig": "164"}, {"size": 6164, "mtime": 1741708664521, "results": "290", "hashOfConfig": "164"}, {"size": 2064, "mtime": 1741708664522, "results": "291", "hashOfConfig": "164"}, {"size": 3122, "mtime": 1747655786403, "results": "292", "hashOfConfig": "164"}, {"size": 1067, "mtime": 1741708664525, "results": "293", "hashOfConfig": "164"}, {"size": 2570, "mtime": 1741708664527, "results": "294", "hashOfConfig": "164"}, {"size": 1863, "mtime": 1741708664529, "results": "295", "hashOfConfig": "164"}, {"size": 760, "mtime": 1741708664531, "results": "296", "hashOfConfig": "164"}, {"size": 3041, "mtime": 1744322480741, "results": "297", "hashOfConfig": "164"}, {"size": 1902, "mtime": 1741708664532, "results": "298", "hashOfConfig": "164"}, {"size": 9363, "mtime": 1741708664464, "results": "299", "hashOfConfig": "164"}, {"size": 13391, "mtime": 1748858257713, "results": "300", "hashOfConfig": "164"}, {"size": 5721, "mtime": 1747930066141, "results": "301", "hashOfConfig": "164"}, {"size": 3264, "mtime": 1747930066144, "results": "302", "hashOfConfig": "164"}, {"size": 2174, "mtime": 1742257294906, "results": "303", "hashOfConfig": "164"}, {"size": 6963, "mtime": 1744322490781, "results": "304", "hashOfConfig": "164"}, {"size": 14578, "mtime": 1748881932207, "results": "305", "hashOfConfig": "164"}, {"size": 1017, "mtime": 1747930066146, "results": "306", "hashOfConfig": "164"}, {"size": 641, "mtime": 1741708664537, "results": "307", "hashOfConfig": "164"}, {"size": 1111, "mtime": 1748604056638, "results": "308", "hashOfConfig": "164"}, {"size": 10824, "mtime": 1748540227717, "results": "309", "hashOfConfig": "164"}, {"size": 490, "mtime": 1748336128804, "results": "310", "hashOfConfig": "164"}, {"size": 3137, "mtime": 1748881932191, "results": "311", "hashOfConfig": "164"}, {"size": 16308, "mtime": 1748540227732, "results": "312", "hashOfConfig": "164"}, {"size": 5640, "mtime": 1748540227735, "results": "313", "hashOfConfig": "164"}, {"size": 2524, "mtime": 1748540227739, "results": "314", "hashOfConfig": "164"}, {"size": 1472, "mtime": 1748558708323, "results": "315", "hashOfConfig": "164"}, {"size": 1456, "mtime": 1748881932211, "results": "316", "hashOfConfig": "164"}, {"size": 1272, "mtime": 1748434147333, "results": "317", "hashOfConfig": "164"}, {"size": 1367, "mtime": 1748336128861, "results": "318", "hashOfConfig": "164"}, {"size": 405, "mtime": 1748511134066, "results": "319", "hashOfConfig": "164"}, {"size": 6064, "mtime": 1748858295981, "results": "320", "hashOfConfig": "164"}, {"size": 5179, "mtime": 1748854683042, "results": "321", "hashOfConfig": "164"}, {"size": 6050, "mtime": 1748881932178, "results": "322", "hashOfConfig": "164"}, {"size": 1170, "mtime": 1748540227745, "results": "323", "hashOfConfig": "164"}, {"size": 6492, "mtime": 1748881932195, "results": "324", "hashOfConfig": "164"}, {"size": 1703, "mtime": 1748881932198, "results": "325", "hashOfConfig": "164"}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "cppzq2", {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "797", "messages": "798", "suppressedMessages": "799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "800", "messages": "801", "suppressedMessages": "802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "803", "messages": "804", "suppressedMessages": "805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "806", "messages": "807", "suppressedMessages": "808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "809", "messages": "810", "suppressedMessages": "811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\animations\\AnimatedSection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\animations\\FadeIn.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\animations\\ScaleIn.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\animations\\SlideIn.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\animations\\StaggerContainer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(auth)\\actions\\auth.ts", [], ["812"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(auth)\\forgotpassword\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(auth)\\login\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(auth)\\register\\page.tsx", [], ["813", "814", "815", "816", "817", "818", "819", "820", "821", "822"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\AgentsContent.tsx", [], ["823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835", "836", "837"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\create\\CreateAgentContent.tsx", [], ["838"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\create\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\edit\\[id]\\EditAgentContent.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\edit\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\billing\\BillingContent.tsx", [], ["839", "840", "841", "842", "843"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\billing\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\brain\\BrainContent.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\brain\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\CampaignContent.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\create\\CreateCampaign.tsx", [], ["844"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\create\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\create\\steps\\AgentsStep.tsx", [], ["845", "846"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\create\\steps\\ScheduleStep.tsx", [], ["847", "848"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\create\\steps\\SettingsSteps.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\create\\steps\\SourcesStep.tsx", [], ["849", "850"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\edit\\[id]\\EditCampaign.tsx", [], ["851"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\edit\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\ContactsContent.tsx", [], ["852", "853", "854", "855", "856", "857", "858", "859", "860"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\create\\CreateContact.tsx", [], ["861", "862"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\create\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\edit\\[contactName]\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\edit\\[contactName]\\[contactId]\\EditContact.tsx", [], ["863", "864", "865", "866", "867", "868"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\edit\\[contactName]\\[contactId]\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\dashboard\\DashboardContent.tsx", [], ["869", "870", "871", "872", "873", "874"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\dashboard\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\history\\HistoryContent.tsx", [], ["875", "876", "877", "878", "879", "880", "881", "882", "883", "884", "885", "886", "887", "888"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\history\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\history\\[fullName]\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\integration\\IntegrationContent.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\integration\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\phonenumber\\buy\\BuyPhoneNumberContent.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\phonenumber\\buy\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\phonenumber\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\phonenumber\\PhoneNumberContent.tsx", [], ["889"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\profile\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\profile\\ProfileContent.tsx", [], ["890", "891", "892", "893", "894"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\schedule\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\schedule\\ScheduleContent.tsx", [], ["895", "896", "897", "898", "899"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\settings\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\settings\\SettingsContent.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\users\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\users\\UsersManagement.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\voices\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\voices\\VoicesContent.tsx", [], ["900"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\OrganizationsContent.tsx", [], ["901", "902", "903", "904", "905", "906", "907"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\[id]\\OrganizationDetailsContent.tsx", [], ["908", "909", "910"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\[id]\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\[id]\\users\\OrganizationUsersContent.tsx", [], ["911", "912", "913"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\[id]\\users\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\api\\billing.ts", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\api\\campaign.ts", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\api\\config.ts", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\api\\contacts.ts", [], ["914", "915", "916"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\api\\globalSettings.ts", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\api\\organizations.ts", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\api\\users.ts", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\page.tsx", [], ["917", "918", "919", "920", "921", "922", "923", "924", "925", "926", "927", "928", "929", "930"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentActionsTab.tsx", [], ["931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentPerformanceTab.tsx", [], ["948", "949"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentProfileTab.tsx", [], ["950", "951", "952", "953", "954", "955", "956"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentRoleTab.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\APIKeyInput.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\braincomponents\\AddParagraphModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\braincomponents\\AddWebLinkModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\braincomponents\\EmptyState.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\braincomponents\\FilesView.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\braincomponents\\FileUploadModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\braincomponents\\KnowledgeBaseSidebar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\braincomponents\\ParagraphsView.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\braincomponents\\SpreadsheetsView.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\braincomponents\\WebLinksView.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ChatWidget.tsx", [], ["957", "958"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\CreditDisplay.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\CTASection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\FAQSection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\FeaturesGrid.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\Footer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\HeroSection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\IntegrationCard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\LanguageSwitcher.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\LowCreditAlert.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\Navigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\PricingSection.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ProfileMenu.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\settingscomponents\\APISettings.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\settingscomponents\\GeneralSettings.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\settingscomponents\\LogsSettings.tsx", [], ["959", "960"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\settingscomponents\\VoiceSettings.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\stripe\\AutoRechargeSettings.tsx", [], ["961", "962"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\stripe\\CardPaymentForm.tsx", [], ["963"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\stripe\\SavedPaymentMethods.tsx", [], ["964"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\stripe\\StripeProvider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\Testimonials.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\theme\\ThemeProvider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\accordion.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\alert-dialog.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\calendar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\checkbox.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\command.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\popover.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\radio-group.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\success-dialog.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\timezone-selector.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\VoiceCallModal.tsx", [], ["965", "966", "967", "968"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\contexts\\CreditContext.tsx", ["969"], ["970", "971", "972"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\hooks\\useWebSocket.ts", [], ["973", "974", "975", "976", "977"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\lib\\auth-client.ts", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\lib\\authFetch.ts", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\lib\\dateUtils.ts", [], ["978", "979", "980"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\lib\\phone-utils.ts", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\lib\\validations\\authSchema.ts", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\api\\organizationSettings.ts", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentAdvancedTab.tsx", [], ["981", "982"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentBrainTab.tsx", [], ["983", "984", "985"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentSidebar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentVoiceTab.tsx", [], ["986", "987"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\hooks\\useAgent.ts", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\hooks\\useAgentList.ts", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\hooks\\useAuth.ts", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\types\\agent.types.ts", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\types\\campaign.types.ts", [], ["988"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\types\\dashboard.types.ts", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\lib\\providers\\ReactQueryProvider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\CreditWarningAlert.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentChatWidget.tsx", [], ["989"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentPhoneCallDialog.tsx", [], ["990", "991", "992"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\types\\contact.types.ts", [], ["993"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\agentscomponents\\AgentWebCallDialog.tsx", [], ["994", "995"], "C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\ui\\scroll-area.tsx", [], [], {"ruleId": "996", "severity": 2, "message": "997", "line": 175, "column": 16, "nodeType": null, "messageId": "998", "endLine": 175, "endColumn": 25, "suppressions": "999"}, {"ruleId": "996", "severity": 2, "message": "1000", "line": 9, "column": 10, "nodeType": null, "messageId": "998", "endLine": 9, "endColumn": 15, "suppressions": "1001"}, {"ruleId": "996", "severity": 2, "message": "1002", "line": 10, "column": 29, "nodeType": null, "messageId": "998", "endLine": 10, "endColumn": 39, "suppressions": "1003"}, {"ruleId": "996", "severity": 2, "message": "1004", "line": 10, "column": 41, "nodeType": null, "messageId": "998", "endLine": 10, "endColumn": 50, "suppressions": "1005"}, {"ruleId": "996", "severity": 2, "message": "1006", "line": 11, "column": 10, "nodeType": null, "messageId": "998", "endLine": 11, "endColumn": 15, "suppressions": "1007"}, {"ruleId": "996", "severity": 2, "message": "1008", "line": 11, "column": 17, "nodeType": null, "messageId": "998", "endLine": 11, "endColumn": 33, "suppressions": "1009"}, {"ruleId": "996", "severity": 2, "message": "1010", "line": 14, "column": 8, "nodeType": null, "messageId": "998", "endLine": 14, "endColumn": 13, "suppressions": "1011"}, {"ruleId": "996", "severity": 2, "message": "1012", "line": 15, "column": 8, "nodeType": null, "messageId": "998", "endLine": 15, "endColumn": 17, "suppressions": "1013"}, {"ruleId": "996", "severity": 2, "message": "1014", "line": 17, "column": 10, "nodeType": null, "messageId": "998", "endLine": 17, "endColumn": 22, "suppressions": "1015"}, {"ruleId": "996", "severity": 2, "message": "1016", "line": 117, "column": 10, "nodeType": null, "messageId": "998", "endLine": 117, "endColumn": 20, "suppressions": "1017"}, {"ruleId": "996", "severity": 2, "message": "1018", "line": 120, "column": 18, "nodeType": null, "messageId": "998", "endLine": 120, "endColumn": 30, "suppressions": "1019"}, {"ruleId": "996", "severity": 2, "message": "1020", "line": 8, "column": 9, "nodeType": null, "messageId": "998", "endLine": 8, "endColumn": 14, "suppressions": "1021"}, {"ruleId": "996", "severity": 2, "message": "1022", "line": 8, "column": 47, "nodeType": null, "messageId": "998", "endLine": 8, "endColumn": 50, "suppressions": "1023"}, {"ruleId": "996", "severity": 2, "message": "1024", "line": 8, "column": 58, "nodeType": null, "messageId": "998", "endLine": 8, "endColumn": 63, "suppressions": "1025"}, {"ruleId": "996", "severity": 2, "message": "1026", "line": 8, "column": 78, "nodeType": null, "messageId": "998", "endLine": 8, "endColumn": 82, "suppressions": "1027"}, {"ruleId": "996", "severity": 2, "message": "1028", "line": 8, "column": 96, "nodeType": null, "messageId": "998", "endLine": 8, "endColumn": 101, "suppressions": "1029"}, {"ruleId": "996", "severity": 2, "message": "1030", "line": 12, "column": 24, "nodeType": null, "messageId": "998", "endLine": 12, "endColumn": 32, "suppressions": "1031"}, {"ruleId": "996", "severity": 2, "message": "1032", "line": 12, "column": 42, "nodeType": null, "messageId": "998", "endLine": 12, "endColumn": 51, "suppressions": "1033"}, {"ruleId": "996", "severity": 2, "message": "1034", "line": 14, "column": 17, "nodeType": null, "messageId": "998", "endLine": 14, "endColumn": 32, "suppressions": "1035"}, {"ruleId": "996", "severity": 2, "message": "1036", "line": 21, "column": 10, "nodeType": null, "messageId": "998", "endLine": 21, "endColumn": 18, "suppressions": "1037"}, {"ruleId": "996", "severity": 2, "message": "1038", "line": 40, "column": 22, "nodeType": null, "messageId": "998", "endLine": 40, "endColumn": 35, "suppressions": "1039"}, {"ruleId": "996", "severity": 2, "message": "1040", "line": 151, "column": 37, "nodeType": null, "messageId": "998", "endLine": 151, "endColumn": 42, "suppressions": "1041"}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 210, "column": 15, "nodeType": "1044", "endLine": 214, "endColumn": 17, "suppressions": "1045"}, {"ruleId": "996", "severity": 2, "message": "1046", "line": 283, "column": 21, "nodeType": null, "messageId": "998", "endLine": 283, "endColumn": 22, "suppressions": "1047"}, {"ruleId": "996", "severity": 2, "message": "1046", "line": 311, "column": 21, "nodeType": null, "messageId": "998", "endLine": 311, "endColumn": 22, "suppressions": "1048"}, {"ruleId": "996", "severity": 2, "message": "1046", "line": 332, "column": 21, "nodeType": null, "messageId": "998", "endLine": 332, "endColumn": 22, "suppressions": "1049"}, {"ruleId": "996", "severity": 2, "message": "1050", "line": 58, "column": 21, "nodeType": null, "messageId": "998", "endLine": 58, "endColumn": 33, "suppressions": "1051"}, {"ruleId": "996", "severity": 2, "message": "1052", "line": 11, "column": 3, "nodeType": null, "messageId": "998", "endLine": 11, "endColumn": 13, "suppressions": "1053"}, {"ruleId": "996", "severity": 2, "message": "1054", "line": 22, "column": 3, "nodeType": null, "messageId": "998", "endLine": 22, "endColumn": 15, "suppressions": "1055"}, {"ruleId": "996", "severity": 2, "message": "1056", "line": 66, "column": 7, "nodeType": null, "messageId": "998", "endLine": 66, "endColumn": 27, "suppressions": "1057"}, {"ruleId": "996", "severity": 2, "message": "1058", "line": 82, "column": 25, "nodeType": null, "messageId": "998", "endLine": 82, "endColumn": 41, "suppressions": "1059"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 229, "column": 19, "nodeType": "1062", "messageId": "1063", "endLine": 229, "endColumn": 22, "suggestions": "1064", "suppressions": "1065"}, {"ruleId": "996", "severity": 2, "message": "1066", "line": 197, "column": 13, "nodeType": null, "messageId": "998", "endLine": 197, "endColumn": 24, "suppressions": "1067"}, {"ruleId": "996", "severity": 2, "message": "1068", "line": 4, "column": 10, "nodeType": null, "messageId": "998", "endLine": 4, "endColumn": 19, "suppressions": "1069"}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 79, "column": 19, "nodeType": "1044", "endLine": 83, "endColumn": 21, "suppressions": "1070"}, {"ruleId": "1071", "severity": 1, "message": "1072", "line": 101, "column": 11, "nodeType": "1073", "endLine": 101, "endColumn": 103, "suppressions": "1074"}, {"ruleId": "1071", "severity": 1, "message": "1075", "line": 356, "column": 6, "nodeType": "1076", "endLine": 356, "endColumn": 8, "suggestions": "1077", "suppressions": "1078"}, {"ruleId": "996", "severity": 2, "message": "1079", "line": 8, "column": 19, "nodeType": null, "messageId": "998", "endLine": 8, "endColumn": 32, "suppressions": "1080"}, {"ruleId": "996", "severity": 2, "message": "1081", "line": 34, "column": 18, "nodeType": null, "messageId": "998", "endLine": 34, "endColumn": 27, "suppressions": "1082"}, {"ruleId": "1071", "severity": 1, "message": "1083", "line": 222, "column": 6, "nodeType": "1076", "endLine": 222, "endColumn": 18, "suggestions": "1084", "suppressions": "1085"}, {"ruleId": "996", "severity": 2, "message": "1086", "line": 10, "column": 9, "nodeType": null, "messageId": "998", "endLine": 10, "endColumn": 15, "suppressions": "1087"}, {"ruleId": "996", "severity": 2, "message": "1088", "line": 10, "column": 125, "nodeType": null, "messageId": "998", "endLine": 10, "endColumn": 134, "suppressions": "1089"}, {"ruleId": "996", "severity": 2, "message": "1090", "line": 10, "column": 136, "nodeType": null, "messageId": "998", "endLine": 10, "endColumn": 143, "suppressions": "1091"}, {"ruleId": "996", "severity": 2, "message": "1079", "line": 17, "column": 58, "nodeType": null, "messageId": "998", "endLine": 17, "endColumn": 71, "suppressions": "1092"}, {"ruleId": "996", "severity": 2, "message": "1093", "line": 56, "column": 42, "nodeType": null, "messageId": "998", "endLine": 56, "endColumn": 49, "suppressions": "1094"}, {"ruleId": "996", "severity": 2, "message": "1095", "line": 166, "column": 22, "nodeType": null, "messageId": "998", "endLine": 166, "endColumn": 35, "suppressions": "1096"}, {"ruleId": "1071", "severity": 1, "message": "1097", "line": 309, "column": 6, "nodeType": "1076", "endLine": 309, "endColumn": 32, "suggestions": "1098", "suppressions": "1099"}, {"ruleId": "1071", "severity": 1, "message": "1100", "line": 373, "column": 6, "nodeType": "1076", "endLine": 373, "endColumn": 8, "suggestions": "1101", "suppressions": "1102"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 727, "column": 35, "nodeType": "1062", "messageId": "1063", "endLine": 727, "endColumn": 38, "suggestions": "1103", "suppressions": "1104"}, {"ruleId": "996", "severity": 2, "message": "1105", "line": 29, "column": 10, "nodeType": null, "messageId": "998", "endLine": 29, "endColumn": 18, "suppressions": "1106"}, {"ruleId": "996", "severity": 2, "message": "1107", "line": 29, "column": 20, "nodeType": null, "messageId": "998", "endLine": 29, "endColumn": 31, "suppressions": "1108"}, {"ruleId": "996", "severity": 2, "message": "1109", "line": 56, "column": 9, "nodeType": null, "messageId": "998", "endLine": 56, "endColumn": 20, "suppressions": "1110"}, {"ruleId": "1071", "severity": 1, "message": "1111", "line": 183, "column": 6, "nodeType": "1076", "endLine": 183, "endColumn": 24, "suggestions": "1112", "suppressions": "1113"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 206, "column": 61, "nodeType": "1062", "messageId": "1063", "endLine": 206, "endColumn": 64, "suggestions": "1114", "suppressions": "1115"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 348, "column": 79, "nodeType": "1062", "messageId": "1063", "endLine": 348, "endColumn": 82, "suggestions": "1116", "suppressions": "1117"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 353, "column": 97, "nodeType": "1062", "messageId": "1063", "endLine": 353, "endColumn": 100, "suggestions": "1118", "suppressions": "1119"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 357, "column": 85, "nodeType": "1062", "messageId": "1063", "endLine": 357, "endColumn": 88, "suggestions": "1120", "suppressions": "1121"}, {"ruleId": "996", "severity": 2, "message": "1122", "line": 10, "column": 133, "nodeType": null, "messageId": "998", "endLine": 10, "endColumn": 142, "suppressions": "1123"}, {"ruleId": "996", "severity": 2, "message": "1124", "line": 18, "column": 10, "nodeType": null, "messageId": "998", "endLine": 18, "endColumn": 17, "suppressions": "1125"}, {"ruleId": "996", "severity": 2, "message": "1126", "line": 18, "column": 19, "nodeType": null, "messageId": "998", "endLine": 18, "endColumn": 33, "suppressions": "1127"}, {"ruleId": "996", "severity": 2, "message": "1128", "line": 18, "column": 35, "nodeType": null, "messageId": "998", "endLine": 18, "endColumn": 50, "suppressions": "1129"}, {"ruleId": "996", "severity": 2, "message": "1130", "line": 18, "column": 52, "nodeType": null, "messageId": "998", "endLine": 18, "endColumn": 66, "suppressions": "1131"}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 770, "column": 15, "nodeType": "1044", "endLine": 778, "endColumn": 17, "suppressions": "1132"}, {"ruleId": "996", "severity": 2, "message": "1133", "line": 216, "column": 10, "nodeType": null, "messageId": "998", "endLine": 216, "endColumn": 24, "suppressions": "1134"}, {"ruleId": "996", "severity": 2, "message": "1135", "line": 216, "column": 26, "nodeType": null, "messageId": "998", "endLine": 216, "endColumn": 43, "suppressions": "1136"}, {"ruleId": "996", "severity": 2, "message": "1137", "line": 221, "column": 10, "nodeType": null, "messageId": "998", "endLine": 221, "endColumn": 21, "suppressions": "1138"}, {"ruleId": "996", "severity": 2, "message": "1139", "line": 221, "column": 23, "nodeType": null, "messageId": "998", "endLine": 221, "endColumn": 37, "suppressions": "1140"}, {"ruleId": "996", "severity": 2, "message": "1141", "line": 230, "column": 10, "nodeType": null, "messageId": "998", "endLine": 230, "endColumn": 14, "suppressions": "1142"}, {"ruleId": "996", "severity": 2, "message": "1143", "line": 240, "column": 10, "nodeType": null, "messageId": "998", "endLine": 240, "endColumn": 18, "suppressions": "1144"}, {"ruleId": "996", "severity": 2, "message": "1145", "line": 241, "column": 10, "nodeType": null, "messageId": "998", "endLine": 241, "endColumn": 22, "suppressions": "1146"}, {"ruleId": "1071", "severity": 1, "message": "1147", "line": 347, "column": 6, "nodeType": "1076", "endLine": 347, "endColumn": 41, "suggestions": "1148", "suppressions": "1149"}, {"ruleId": "996", "severity": 2, "message": "1150", "line": 358, "column": 9, "nodeType": null, "messageId": "998", "endLine": 358, "endColumn": 20, "suppressions": "1151"}, {"ruleId": "996", "severity": 2, "message": "1152", "line": 369, "column": 9, "nodeType": null, "messageId": "998", "endLine": 369, "endColumn": 24, "suppressions": "1153"}, {"ruleId": "1071", "severity": 1, "message": "1147", "line": 445, "column": 6, "nodeType": "1076", "endLine": 445, "endColumn": 8, "suggestions": "1154", "suppressions": "1155"}, {"ruleId": "996", "severity": 2, "message": "1156", "line": 448, "column": 10, "nodeType": null, "messageId": "998", "endLine": 448, "endColumn": 22, "suppressions": "1157"}, {"ruleId": "996", "severity": 2, "message": "1158", "line": 448, "column": 24, "nodeType": null, "messageId": "998", "endLine": 448, "endColumn": 39, "suppressions": "1159"}, {"ruleId": "996", "severity": 2, "message": "1160", "line": 590, "column": 9, "nodeType": null, "messageId": "998", "endLine": 590, "endColumn": 20, "suppressions": "1161"}, {"ruleId": "996", "severity": 2, "message": "1162", "line": 22, "column": 24, "nodeType": null, "messageId": "998", "endLine": 22, "endColumn": 39, "suppressions": "1163"}, {"ruleId": "996", "severity": 2, "message": "1164", "line": 6, "column": 10, "nodeType": null, "messageId": "998", "endLine": 6, "endColumn": 16, "suppressions": "1165"}, {"ruleId": "996", "severity": 2, "message": "1166", "line": 15, "column": 10, "nodeType": null, "messageId": "998", "endLine": 15, "endColumn": 13, "suppressions": "1167"}, {"ruleId": "996", "severity": 2, "message": "1168", "line": 19, "column": 10, "nodeType": null, "messageId": "998", "endLine": 19, "endColumn": 14, "suppressions": "1169"}, {"ruleId": "996", "severity": 2, "message": "1170", "line": 21, "column": 10, "nodeType": null, "messageId": "998", "endLine": 21, "endColumn": 16, "suppressions": "1171"}, {"ruleId": "996", "severity": 2, "message": "1172", "line": 21, "column": 18, "nodeType": null, "messageId": "998", "endLine": 21, "endColumn": 27, "suppressions": "1173"}, {"ruleId": "1071", "severity": 1, "message": "1174", "line": 197, "column": 6, "nodeType": "1076", "endLine": 197, "endColumn": 8, "suggestions": "1175", "suppressions": "1176"}, {"ruleId": "1071", "severity": 1, "message": "1177", "line": 227, "column": 6, "nodeType": "1076", "endLine": 227, "endColumn": 12, "suggestions": "1178", "suppressions": "1179"}, {"ruleId": "1071", "severity": 1, "message": "1180", "line": 252, "column": 6, "nodeType": "1076", "endLine": 252, "endColumn": 20, "suggestions": "1181", "suppressions": "1182"}, {"ruleId": "996", "severity": 2, "message": "1183", "line": 474, "column": 14, "nodeType": null, "messageId": "998", "endLine": 474, "endColumn": 19, "suppressions": "1184"}, {"ruleId": "996", "severity": 2, "message": "1183", "line": 482, "column": 14, "nodeType": null, "messageId": "998", "endLine": 482, "endColumn": 19, "suppressions": "1185"}, {"ruleId": "996", "severity": 2, "message": "1186", "line": 131, "column": 18, "nodeType": null, "messageId": "998", "endLine": 131, "endColumn": 27, "suppressions": "1187"}, {"ruleId": "996", "severity": 2, "message": "1052", "line": 10, "column": 3, "nodeType": null, "messageId": "998", "endLine": 10, "endColumn": 13, "suppressions": "1188"}, {"ruleId": "996", "severity": 2, "message": "1189", "line": 32, "column": 3, "nodeType": null, "messageId": "998", "endLine": 32, "endColumn": 9, "suppressions": "1190"}, {"ruleId": "996", "severity": 2, "message": "1191", "line": 33, "column": 3, "nodeType": null, "messageId": "998", "endLine": 33, "endColumn": 16, "suppressions": "1192"}, {"ruleId": "996", "severity": 2, "message": "1193", "line": 34, "column": 3, "nodeType": null, "messageId": "998", "endLine": 34, "endColumn": 13, "suppressions": "1194"}, {"ruleId": "996", "severity": 2, "message": "1195", "line": 35, "column": 3, "nodeType": null, "messageId": "998", "endLine": 35, "endColumn": 16, "suppressions": "1196"}, {"ruleId": "996", "severity": 2, "message": "1197", "line": 36, "column": 3, "nodeType": null, "messageId": "998", "endLine": 36, "endColumn": 14, "suppressions": "1198"}, {"ruleId": "996", "severity": 2, "message": "1199", "line": 43, "column": 46, "nodeType": null, "messageId": "998", "endLine": 43, "endColumn": 56, "suppressions": "1200"}, {"ruleId": "1071", "severity": 1, "message": "1201", "line": 75, "column": 6, "nodeType": "1076", "endLine": 75, "endColumn": 22, "suggestions": "1202", "suppressions": "1203"}, {"ruleId": "1204", "severity": 2, "message": "1205", "line": 191, "column": 52, "nodeType": "1206", "messageId": "1207", "suggestions": "1208", "suppressions": "1209"}, {"ruleId": "1204", "severity": 2, "message": "1205", "line": 259, "column": 52, "nodeType": "1206", "messageId": "1207", "suggestions": "1210", "suppressions": "1211"}, {"ruleId": "996", "severity": 2, "message": "1052", "line": 11, "column": 3, "nodeType": null, "messageId": "998", "endLine": 11, "endColumn": 13, "suppressions": "1212"}, {"ruleId": "996", "severity": 2, "message": "1213", "line": 43, "column": 19, "nodeType": null, "messageId": "998", "endLine": 43, "endColumn": 23, "suppressions": "1214"}, {"ruleId": "1071", "severity": 1, "message": "1215", "line": 68, "column": 6, "nodeType": "1076", "endLine": 68, "endColumn": 22, "suggestions": "1216", "suppressions": "1217"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 17, "column": 15, "nodeType": "1062", "messageId": "1063", "endLine": 17, "endColumn": 18, "suggestions": "1218", "suppressions": "1219"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 128, "column": 48, "nodeType": "1062", "messageId": "1063", "endLine": 128, "endColumn": 51, "suggestions": "1220", "suppressions": "1221"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 195, "column": 13, "nodeType": "1062", "messageId": "1063", "endLine": 195, "endColumn": 16, "suggestions": "1222", "suppressions": "1223"}, {"ruleId": "996", "severity": 2, "message": "1224", "line": 4, "column": 8, "nodeType": null, "messageId": "998", "endLine": 4, "endColumn": 23, "suppressions": "1225"}, {"ruleId": "996", "severity": 2, "message": "1226", "line": 5, "column": 8, "nodeType": null, "messageId": "998", "endLine": 5, "endColumn": 14, "suppressions": "1227"}, {"ruleId": "996", "severity": 2, "message": "1228", "line": 6, "column": 8, "nodeType": null, "messageId": "998", "endLine": 6, "endColumn": 15, "suppressions": "1229"}, {"ruleId": "996", "severity": 2, "message": "1230", "line": 7, "column": 8, "nodeType": null, "messageId": "998", "endLine": 7, "endColumn": 15, "suppressions": "1231"}, {"ruleId": "996", "severity": 2, "message": "1232", "line": 8, "column": 8, "nodeType": null, "messageId": "998", "endLine": 8, "endColumn": 24, "suppressions": "1233"}, {"ruleId": "996", "severity": 2, "message": "1234", "line": 9, "column": 8, "nodeType": null, "messageId": "998", "endLine": 9, "endColumn": 18, "suppressions": "1235"}, {"ruleId": "996", "severity": 2, "message": "1236", "line": 10, "column": 8, "nodeType": null, "messageId": "998", "endLine": 10, "endColumn": 18, "suppressions": "1237"}, {"ruleId": "996", "severity": 2, "message": "1238", "line": 11, "column": 8, "nodeType": null, "messageId": "998", "endLine": 11, "endColumn": 20, "suppressions": "1239"}, {"ruleId": "996", "severity": 2, "message": "1240", "line": 12, "column": 8, "nodeType": null, "messageId": "998", "endLine": 12, "endColumn": 14, "suppressions": "1241"}, {"ruleId": "996", "severity": 2, "message": "1242", "line": 13, "column": 8, "nodeType": null, "messageId": "998", "endLine": 13, "endColumn": 19, "suppressions": "1243"}, {"ruleId": "996", "severity": 2, "message": "1244", "line": 14, "column": 8, "nodeType": null, "messageId": "998", "endLine": 14, "endColumn": 18, "suppressions": "1245"}, {"ruleId": "996", "severity": 2, "message": "1246", "line": 15, "column": 8, "nodeType": null, "messageId": "998", "endLine": 15, "endColumn": 22, "suppressions": "1247"}, {"ruleId": "996", "severity": 2, "message": "1248", "line": 16, "column": 8, "nodeType": null, "messageId": "998", "endLine": 16, "endColumn": 20, "suppressions": "1249"}, {"ruleId": "996", "severity": 2, "message": "1250", "line": 27, "column": 10, "nodeType": null, "messageId": "998", "endLine": 27, "endColumn": 25, "suppressions": "1251"}, {"ruleId": "996", "severity": 2, "message": "1213", "line": 4, "column": 10, "nodeType": null, "messageId": "998", "endLine": 4, "endColumn": 14, "suppressions": "1252"}, {"ruleId": "996", "severity": 2, "message": "1253", "line": 9, "column": 11, "nodeType": null, "messageId": "998", "endLine": 9, "endColumn": 31, "suppressions": "1254"}, {"ruleId": "996", "severity": 2, "message": "1255", "line": 24, "column": 43, "nodeType": null, "messageId": "998", "endLine": 24, "endColumn": 48, "suppressions": "1256"}, {"ruleId": "996", "severity": 2, "message": "1257", "line": 24, "column": 50, "nodeType": null, "messageId": "998", "endLine": 24, "endColumn": 58, "suppressions": "1258"}, {"ruleId": "996", "severity": 2, "message": "1259", "line": 24, "column": 60, "nodeType": null, "messageId": "998", "endLine": 24, "endColumn": 72, "suppressions": "1260"}, {"ruleId": "996", "severity": 2, "message": "1261", "line": 25, "column": 10, "nodeType": null, "messageId": "998", "endLine": 25, "endColumn": 24, "suppressions": "1262"}, {"ruleId": "996", "severity": 2, "message": "1263", "line": 26, "column": 10, "nodeType": null, "messageId": "998", "endLine": 26, "endColumn": 19, "suppressions": "1264"}, {"ruleId": "996", "severity": 2, "message": "1265", "line": 29, "column": 25, "nodeType": null, "messageId": "998", "endLine": 29, "endColumn": 41, "suppressions": "1266"}, {"ruleId": "996", "severity": 2, "message": "1267", "line": 81, "column": 10, "nodeType": null, "messageId": "998", "endLine": 81, "endColumn": 20, "suppressions": "1268"}, {"ruleId": "996", "severity": 2, "message": "1269", "line": 82, "column": 10, "nodeType": null, "messageId": "998", "endLine": 82, "endColumn": 18, "suppressions": "1270"}, {"ruleId": "996", "severity": 2, "message": "1271", "line": 83, "column": 10, "nodeType": null, "messageId": "998", "endLine": 83, "endColumn": 17, "suppressions": "1272"}, {"ruleId": "996", "severity": 2, "message": "1273", "line": 84, "column": 10, "nodeType": null, "messageId": "998", "endLine": 84, "endColumn": 21, "suppressions": "1274"}, {"ruleId": "996", "severity": 2, "message": "1275", "line": 85, "column": 10, "nodeType": null, "messageId": "998", "endLine": 85, "endColumn": 17, "suppressions": "1276"}, {"ruleId": "996", "severity": 2, "message": "1277", "line": 86, "column": 10, "nodeType": null, "messageId": "998", "endLine": 86, "endColumn": 23, "suppressions": "1278"}, {"ruleId": "996", "severity": 2, "message": "1279", "line": 88, "column": 9, "nodeType": null, "messageId": "998", "endLine": 88, "endColumn": 26, "suppressions": "1280"}, {"ruleId": "996", "severity": 2, "message": "1281", "line": 101, "column": 9, "nodeType": null, "messageId": "998", "endLine": 101, "endColumn": 24, "suppressions": "1282"}, {"ruleId": "996", "severity": 2, "message": "1283", "line": 112, "column": 9, "nodeType": null, "messageId": "998", "endLine": 112, "endColumn": 25, "suppressions": "1284"}, {"ruleId": "996", "severity": 2, "message": "1255", "line": 21, "column": 48, "nodeType": null, "messageId": "998", "endLine": 21, "endColumn": 53, "suppressions": "1285"}, {"ruleId": "996", "severity": 2, "message": "1257", "line": 21, "column": 55, "nodeType": null, "messageId": "998", "endLine": 21, "endColumn": 63, "suppressions": "1286"}, {"ruleId": "996", "severity": 2, "message": "1287", "line": 8, "column": 10, "nodeType": null, "messageId": "998", "endLine": 8, "endColumn": 16, "suppressions": "1288"}, {"ruleId": "996", "severity": 2, "message": "1289", "line": 9, "column": 10, "nodeType": null, "messageId": "998", "endLine": 9, "endColumn": 16, "suppressions": "1290"}, {"ruleId": "996", "severity": 2, "message": "1291", "line": 11, "column": 10, "nodeType": null, "messageId": "998", "endLine": 11, "endColumn": 15, "suppressions": "1292"}, {"ruleId": "996", "severity": 2, "message": "1293", "line": 13, "column": 49, "nodeType": null, "messageId": "998", "endLine": 13, "endColumn": 57, "suppressions": "1294"}, {"ruleId": "996", "severity": 2, "message": "1295", "line": 17, "column": 10, "nodeType": null, "messageId": "998", "endLine": 17, "endColumn": 15, "suppressions": "1296"}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 316, "column": 15, "nodeType": "1044", "endLine": 320, "endColumn": 17, "suppressions": "1297"}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 544, "column": 21, "nodeType": "1044", "endLine": 550, "endColumn": 23, "suppressions": "1298"}, {"ruleId": "996", "severity": 2, "message": "1010", "line": 9, "column": 8, "nodeType": null, "messageId": "998", "endLine": 9, "endColumn": 13, "suppressions": "1299"}, {"ruleId": "996", "severity": 2, "message": "1300", "line": 42, "column": 33, "nodeType": null, "messageId": "998", "endLine": 42, "endColumn": 40, "suppressions": "1301"}, {"ruleId": "1071", "severity": 1, "message": "1302", "line": 171, "column": 6, "nodeType": "1076", "endLine": 171, "endColumn": 8, "suggestions": "1303", "suppressions": "1304"}, {"ruleId": "1071", "severity": 1, "message": "1302", "line": 178, "column": 6, "nodeType": "1076", "endLine": 178, "endColumn": 12, "suggestions": "1305", "suppressions": "1306"}, {"ruleId": "996", "severity": 2, "message": "1307", "line": 101, "column": 13, "nodeType": null, "messageId": "998", "endLine": 101, "endColumn": 21, "suppressions": "1308"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 107, "column": 21, "nodeType": "1062", "messageId": "1063", "endLine": 107, "endColumn": 24, "suggestions": "1309", "suppressions": "1310"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 134, "column": 19, "nodeType": "1062", "messageId": "1063", "endLine": 134, "endColumn": 22, "suggestions": "1311", "suppressions": "1312"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 188, "column": 19, "nodeType": "1062", "messageId": "1063", "endLine": 188, "endColumn": 22, "suggestions": "1313", "suppressions": "1314"}, {"ruleId": "996", "severity": 2, "message": "1068", "line": 5, "column": 20, "nodeType": null, "messageId": "998", "endLine": 5, "endColumn": 29, "suppressions": "1315"}, {"ruleId": "996", "severity": 2, "message": "1300", "line": 44, "column": 33, "nodeType": null, "messageId": "998", "endLine": 44, "endColumn": 40, "suppressions": "1316"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 73, "column": 33, "nodeType": "1062", "messageId": "1063", "endLine": 73, "endColumn": 36, "suggestions": "1317", "suppressions": "1318"}, {"ruleId": "996", "severity": 2, "message": "1319", "line": 119, "column": 9, "nodeType": null, "messageId": "998", "endLine": 119, "endColumn": 26, "suppressions": "1320"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 75, "column": 42, "nodeType": "1062", "messageId": "1063", "endLine": 75, "endColumn": 45, "suggestions": "1321"}, {"ruleId": "1071", "severity": 1, "message": "1322", "line": 209, "column": 6, "nodeType": "1076", "endLine": 209, "endColumn": 8, "suggestions": "1323", "suppressions": "1324"}, {"ruleId": "1071", "severity": 1, "message": "1325", "line": 276, "column": 6, "nodeType": "1076", "endLine": 276, "endColumn": 39, "suggestions": "1326", "suppressions": "1327"}, {"ruleId": "1071", "severity": 1, "message": "1322", "line": 310, "column": 6, "nodeType": "1076", "endLine": 310, "endColumn": 26, "suggestions": "1328", "suppressions": "1329"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 74, "column": 46, "nodeType": "1062", "messageId": "1063", "endLine": 74, "endColumn": 49, "suggestions": "1330", "suppressions": "1331"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 100, "column": 50, "nodeType": "1062", "messageId": "1063", "endLine": 100, "endColumn": 53, "suggestions": "1332", "suppressions": "1333"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 100, "column": 77, "nodeType": "1062", "messageId": "1063", "endLine": 100, "endColumn": 80, "suggestions": "1334", "suppressions": "1335"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 113, "column": 62, "nodeType": "1062", "messageId": "1063", "endLine": 113, "endColumn": 65, "suggestions": "1336", "suppressions": "1337"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 132, "column": 69, "nodeType": "1062", "messageId": "1063", "endLine": 132, "endColumn": 72, "suggestions": "1338", "suppressions": "1339"}, {"ruleId": "996", "severity": 2, "message": "1340", "line": 127, "column": 9, "nodeType": null, "messageId": "998", "endLine": 127, "endColumn": 16, "suppressions": "1341"}, {"ruleId": "996", "severity": 2, "message": "1046", "line": 166, "column": 12, "nodeType": null, "messageId": "998", "endLine": 166, "endColumn": 13, "suppressions": "1342"}, {"ruleId": "996", "severity": 2, "message": "1183", "line": 217, "column": 12, "nodeType": null, "messageId": "998", "endLine": 217, "endColumn": 17, "suppressions": "1343"}, {"ruleId": "996", "severity": 2, "message": "1344", "line": 10, "column": 10, "nodeType": null, "messageId": "998", "endLine": 10, "endColumn": 16, "suppressions": "1345"}, {"ruleId": "996", "severity": 2, "message": "1259", "line": 15, "column": 62, "nodeType": null, "messageId": "998", "endLine": 15, "endColumn": 74, "suppressions": "1346"}, {"ruleId": "996", "severity": 2, "message": "1255", "line": 7, "column": 42, "nodeType": null, "messageId": "998", "endLine": 7, "endColumn": 47, "suppressions": "1347"}, {"ruleId": "996", "severity": 2, "message": "1257", "line": 7, "column": 49, "nodeType": null, "messageId": "998", "endLine": 7, "endColumn": 57, "suppressions": "1348"}, {"ruleId": "996", "severity": 2, "message": "1259", "line": 7, "column": 59, "nodeType": null, "messageId": "998", "endLine": 7, "endColumn": 71, "suppressions": "1349"}, {"ruleId": "996", "severity": 2, "message": "1000", "line": 5, "column": 10, "nodeType": null, "messageId": "998", "endLine": 5, "endColumn": 15, "suppressions": "1350"}, {"ruleId": "996", "severity": 2, "message": "1344", "line": 10, "column": 10, "nodeType": null, "messageId": "998", "endLine": 10, "endColumn": 16, "suppressions": "1351"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 12, "column": 15, "nodeType": "1062", "messageId": "1063", "endLine": 12, "endColumn": 18, "suggestions": "1352", "suppressions": "1353"}, {"ruleId": "1042", "severity": 1, "message": "1043", "line": 88, "column": 15, "nodeType": "1044", "endLine": 92, "endColumn": 17, "suppressions": "1354"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 46, "column": 23, "nodeType": "1062", "messageId": "1063", "endLine": 46, "endColumn": 26, "suggestions": "1355", "suppressions": "1356"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 71, "column": 21, "nodeType": "1062", "messageId": "1063", "endLine": 71, "endColumn": 24, "suggestions": "1357", "suppressions": "1358"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 121, "column": 33, "nodeType": "1062", "messageId": "1063", "endLine": 121, "endColumn": 36, "suggestions": "1359", "suppressions": "1360"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 10, "column": 15, "nodeType": "1062", "messageId": "1063", "endLine": 10, "endColumn": 18, "suggestions": "1361", "suppressions": "1362"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 65, "column": 40, "nodeType": "1062", "messageId": "1063", "endLine": 65, "endColumn": 43, "suggestions": "1363", "suppressions": "1364"}, {"ruleId": "1060", "severity": 2, "message": "1061", "line": 115, "column": 21, "nodeType": "1062", "messageId": "1063", "endLine": 115, "endColumn": 24, "suggestions": "1365", "suppressions": "1366"}, "@typescript-eslint/no-unused-vars", "'jsonError' is defined but never used.", "unusedVar", ["1367"], "'Input' is defined but never used.", ["1368"], "'CardHeader' is defined but never used.", ["1369"], "'CardTitle' is defined but never used.", ["1370"], "'Alert' is defined but never used.", ["1371"], "'AlertDescription' is defined but never used.", ["1372"], "'Image' is defined but never used.", ["1373"], "'OrovaLogo' is defined but never used.", ["1374"], "'SubmitButton' is defined but never used.", ["1375"], "'formErrors' is assigned a value but never used.", ["1376"], "'handleSubmit' is defined but never used.", ["1377"], "'Globe' is defined but never used.", ["1378"], "'Mic' is defined but never used.", ["1379"], "'Phone' is defined but never used.", ["1380"], "'Play' is defined but never used.", ["1381"], "'Video' is defined but never used.", ["1382"], "'Settings' is defined but never used.", ["1383"], "'FileAudio' is defined but never used.", ["1384"], "'StaticImageData' is defined but never used.", ["1385"], "'useAgent' is defined but never used.", ["1386"], "'setIsDeleting' is assigned a value but never used.", ["1387"], "'index' is defined but never used.", ["1388"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["1389"], "'e' is defined but never used.", ["1390"], ["1391"], ["1392"], "'setIsLoading' is assigned a value but never used.", ["1393"], "'CardFooter' is defined but never used.", ["1394"], "'TableCaption' is defined but never used.", ["1395"], "'paymentMethodOptions' is assigned a value but never used.", ["1396"], "'setPaymentMethod' is assigned a value but never used.", ["1397"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1398", "1399"], ["1400"], "'newCampaign' is assigned a value but never used.", ["1401"], "'useEffect' is defined but never used.", ["1402"], ["1403"], "react-hooks/exhaustive-deps", "The 'followUpDays' logical expression could make the dependencies of useEffect Hook (at line 364) change on every render. To fix this, wrap the initialization of 'followUpDays' in its own useMemo() Hook.", "VariableDeclarator", ["1404"], "React Hook useEffect has missing dependencies: 'data.startDate', 'endDate', 'endTime', 'hasEndDate', 'startDate', 'startTime', and 'updateDates'. Either include them or remove the dependency array.", "ArrayExpression", ["1405"], ["1406"], "'fetchContacts' is defined but never used.", ["1407"], "'setErrors' is assigned a value but never used.", ["1408"], "React Hook useEffect has a missing dependency: 'fetchCampaignData'. Either include it or remove the dependency array.", ["1409"], ["1410"], "'Filter' is defined but never used.", ["1411"], "'PhoneCall' is defined but never used.", ["1412"], "'Columns' is defined but never used.", ["1413"], ["1414"], "'minutes' is assigned a value but never used.", ["1415"], "'setFilterType' is assigned a value but never used.", ["1416"], "React Hook useEffect has missing dependencies: 'loadContacts' and 'searchTerm'. Either include them or remove the dependency array.", ["1417"], ["1418"], "React Hook useEffect has missing dependencies: 'fetchAgents' and 'loadContacts'. Either include them or remove the dependency array.", ["1419"], ["1420"], ["1421", "1422"], ["1423"], "'contacts' is assigned a value but never used.", ["1424"], "'setContacts' is assigned a value but never used.", ["1425"], "'contactName' is assigned a value but never used.", ["1426"], "React Hook useEffect has a missing dependency: 'loadContacts'. Either include it or remove the dependency array.", ["1427"], ["1428"], ["1429", "1430"], ["1431"], ["1432", "1433"], ["1434"], ["1435", "1436"], ["1437"], ["1438", "1439"], ["1440"], "'RefreshCw' is defined but never used.", ["1441"], "'Tooltip' is defined but never used.", ["1442"], "'TooltipContent' is defined but never used.", ["1443"], "'TooltipProvider' is defined but never used.", ["1444"], "'TooltipTrigger' is defined but never used.", ["1445"], ["1446"], "'totalCallCount' is assigned a value but never used.", ["1447"], "'setTotalCallCount' is assigned a value but never used.", ["1448"], "'accessToken' is assigned a value but never used.", ["1449"], "'setAccessToken' is assigned a value but never used.", ["1450"], "'page' is assigned a value but never used.", ["1451"], "'isMobile' is assigned a value but never used.", ["1452"], "'showCallList' is assigned a value but never used.", ["1453"], "React Hook useEffect has a missing dependency: 'fetchCalls'. Either include it or remove the dependency array.", ["1454"], ["1455"], "'hasNewCalls' is assigned a value but never used.", ["1456"], "'updateCallsData' is assigned a value but never used.", ["1457"], ["1458"], ["1459"], "'isRefreshing' is assigned a value but never used.", ["1460"], "'setIsRefreshing' is assigned a value but never used.", ["1461"], "'sortedCalls' is assigned a value but never used.", ["1462"], "'setPhoneNumbers' is assigned a value but never used.", ["1463"], "'Button' is defined but never used.", ["1464"], "'set' is defined but never used.", ["1465"], "'user' is assigned a value but never used.", ["1466"], "'saving' is assigned a value but never used.", ["1467"], "'setSaving' is assigned a value but never used.", ["1468"], "React Hook useEffect has missing dependencies: 'fetchAgents' and 'fetchSchedules'. Either include them or remove the dependency array.", ["1469"], ["1470"], "React Hook useEffect has a missing dependency: 'fetchSchedules'. Either include it or remove the dependency array.", ["1471"], ["1472"], "React Hook useEffect has missing dependencies: 'fetchSchedules' and 'searchTerm'. Either include them or remove the dependency array.", ["1473"], ["1474"], "'error' is defined but never used.", ["1475"], ["1476"], "'setVoices' is assigned a value but never used.", ["1477"], ["1478"], "'Select' is defined but never used.", ["1479"], "'SelectContent' is defined but never used.", ["1480"], "'SelectItem' is defined but never used.", ["1481"], "'SelectTrigger' is defined but never used.", ["1482"], "'SelectValue' is defined but never used.", ["1483"], "'CreditCard' is defined but never used.", ["1484"], "React Hook useEffect has a missing dependency: 'fetchOrganization'. Either include it or remove the dependency array.", ["1485"], ["1486"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["1487", "1488", "1489", "1490"], ["1491"], ["1492", "1493", "1494", "1495"], ["1496"], ["1497"], "'Plus' is defined but never used.", ["1498"], "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["1499"], ["1500"], ["1501", "1502"], ["1503"], ["1504", "1505"], ["1506"], ["1507", "1508"], ["1509"], "'AnimatedSection' is defined but never used.", ["1510"], "'FadeIn' is defined but never used.", ["1511"], "'ScaleIn' is defined but never used.", ["1512"], "'SlideIn' is defined but never used.", ["1513"], "'StaggerContainer' is defined but never used.", ["1514"], "'CTASection' is defined but never used.", ["1515"], "'FAQSection' is defined but never used.", ["1516"], "'FeaturesGrid' is defined but never used.", ["1517"], "'Footer' is defined but never used.", ["1518"], "'HeroSection' is defined but never used.", ["1519"], "'Navigation' is defined but never used.", ["1520"], "'PricingSection' is defined but never used.", ["1521"], "'Testimonials' is defined but never used.", ["1522"], "'isAuthenticated' is assigned a value but never used.", ["1523"], ["1524"], "'AgentActionsTabProps' is defined but never used.", ["1525"], "'agent' is defined but never used.", ["1526"], "'setAgent' is defined but never used.", ["1527"], "'phoneNumbers' is defined but never used.", ["1528"], "'selectedAction' is assigned a value but never used.", ["1529"], "'dialogTab' is assigned a value but never used.", ["1530"], "'setActionConfigs' is assigned a value but never used.", ["1531"], "'actionName' is assigned a value but never used.", ["1532"], "'callType' is assigned a value but never used.", ["1533"], "'trigger' is assigned a value but never used.", ["1534"], "'phoneNumber' is assigned a value but never used.", ["1535"], "'content' is assigned a value but never used.", ["1536"], "'emailTemplate' is assigned a value but never used.", ["1537"], "'handleActionClick' is assigned a value but never used.", ["1538"], "'handleAddAction' is assigned a value but never used.", ["1539"], "'handleSaveAction' is assigned a value but never used.", ["1540"], ["1541"], ["1542"], "'Slider' is defined but never used.", ["1543"], "'Switch' is defined but never used.", ["1544"], "'Badge' is defined but never used.", ["1545"], "'PlusIcon' is defined but never used.", ["1546"], "'Agent' is defined but never used.", ["1547"], ["1548"], ["1549"], ["1550"], "'history' is defined but never used.", ["1551"], "React Hook useEffect has a missing dependency: 'fetchLogs'. Either include it or remove the dependency array.", ["1552"], ["1553"], ["1554"], ["1555"], "'response' is assigned a value but never used.", ["1556"], ["1557", "1558"], ["1559"], ["1560", "1561"], ["1562"], ["1563", "1564"], ["1565"], ["1566"], ["1567"], ["1568", "1569"], ["1570"], "'processVoiceInput' is assigned a value but never used.", ["1571"], ["1572", "1573"], "React Hook useEffect has a missing dependency: 'fetchCredits'. Either include it or remove the dependency array.", ["1574"], ["1575"], "React Hook useEffect has a missing dependency: 'callPricePerMinute'. Either include it or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setMinutes' needs the current value of 'callPricePerMinute'.", ["1576"], ["1577"], ["1578"], ["1579"], ["1580", "1581"], ["1582"], ["1583", "1584"], ["1585"], ["1586", "1587"], ["1588"], ["1589", "1590"], ["1591"], ["1592", "1593"], ["1594"], "'seconds' is assigned a value but never used.", ["1595"], ["1596"], ["1597"], "'useRef' is defined but never used.", ["1598"], ["1599"], ["1600"], ["1601"], ["1602"], ["1603"], ["1604"], ["1605", "1606"], ["1607"], ["1608"], ["1609", "1610"], ["1611"], ["1612", "1613"], ["1614"], ["1615", "1616"], ["1617"], ["1618", "1619"], ["1620"], ["1621", "1622"], ["1623"], ["1624", "1625"], ["1626"], {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1630", "desc": "1631"}, {"messageId": "1632", "fix": "1633", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"desc": "1635", "fix": "1636"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"desc": "1637", "fix": "1638"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"desc": "1639", "fix": "1640"}, {"kind": "1627", "justification": "1628"}, {"desc": "1641", "fix": "1642"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1643", "desc": "1631"}, {"messageId": "1632", "fix": "1644", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"desc": "1645", "fix": "1646"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1647", "desc": "1631"}, {"messageId": "1632", "fix": "1648", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1649", "desc": "1631"}, {"messageId": "1632", "fix": "1650", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1651", "desc": "1631"}, {"messageId": "1632", "fix": "1652", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1653", "desc": "1631"}, {"messageId": "1632", "fix": "1654", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"desc": "1655", "fix": "1656"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"desc": "1657", "fix": "1658"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"desc": "1659", "fix": "1660"}, {"kind": "1627", "justification": "1628"}, {"desc": "1661", "fix": "1662"}, {"kind": "1627", "justification": "1628"}, {"desc": "1663", "fix": "1664"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"desc": "1665", "fix": "1666"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1667", "data": "1668", "fix": "1669", "desc": "1670"}, {"messageId": "1667", "data": "1671", "fix": "1672", "desc": "1673"}, {"messageId": "1667", "data": "1674", "fix": "1675", "desc": "1676"}, {"messageId": "1667", "data": "1677", "fix": "1678", "desc": "1679"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1667", "data": "1680", "fix": "1681", "desc": "1670"}, {"messageId": "1667", "data": "1682", "fix": "1683", "desc": "1673"}, {"messageId": "1667", "data": "1684", "fix": "1685", "desc": "1676"}, {"messageId": "1667", "data": "1686", "fix": "1687", "desc": "1679"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"desc": "1688", "fix": "1689"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1690", "desc": "1631"}, {"messageId": "1632", "fix": "1691", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1692", "desc": "1631"}, {"messageId": "1632", "fix": "1693", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1694", "desc": "1631"}, {"messageId": "1632", "fix": "1695", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"desc": "1696", "fix": "1697"}, {"kind": "1627", "justification": "1628"}, {"desc": "1698", "fix": "1699"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1700", "desc": "1631"}, {"messageId": "1632", "fix": "1701", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1702", "desc": "1631"}, {"messageId": "1632", "fix": "1703", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1704", "desc": "1631"}, {"messageId": "1632", "fix": "1705", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1706", "desc": "1631"}, {"messageId": "1632", "fix": "1707", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1708", "desc": "1631"}, {"messageId": "1632", "fix": "1709", "desc": "1634"}, {"desc": "1710", "fix": "1711"}, {"kind": "1627", "justification": "1628"}, {"desc": "1712", "fix": "1713"}, {"kind": "1627", "justification": "1628"}, {"desc": "1714", "fix": "1715"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1716", "desc": "1631"}, {"messageId": "1632", "fix": "1717", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1718", "desc": "1631"}, {"messageId": "1632", "fix": "1719", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1720", "desc": "1631"}, {"messageId": "1632", "fix": "1721", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1722", "desc": "1631"}, {"messageId": "1632", "fix": "1723", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1724", "desc": "1631"}, {"messageId": "1632", "fix": "1725", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1726", "desc": "1631"}, {"messageId": "1632", "fix": "1727", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1728", "desc": "1631"}, {"messageId": "1632", "fix": "1729", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1730", "desc": "1631"}, {"messageId": "1632", "fix": "1731", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1732", "desc": "1631"}, {"messageId": "1632", "fix": "1733", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1734", "desc": "1631"}, {"messageId": "1632", "fix": "1735", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1736", "desc": "1631"}, {"messageId": "1632", "fix": "1737", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, {"messageId": "1629", "fix": "1738", "desc": "1631"}, {"messageId": "1632", "fix": "1739", "desc": "1634"}, {"kind": "1627", "justification": "1628"}, "directive", "", "suggestUnknown", {"range": "1740", "text": "1741"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1742", "text": "1743"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "Update the dependencies array to be: [data.startDate, endDate, endTime, hasEndDate, startDate, startTime, updateDates]", {"range": "1744", "text": "1745"}, "Update the dependencies array to be: [campaignId, fetchCampaignData]", {"range": "1746", "text": "1747"}, "Update the dependencies array to be: [loadContacts, page, searchTerm, selectedCampaignId]", {"range": "1748", "text": "1749"}, "Update the dependencies array to be: [fetchAgents, loadContacts]", {"range": "1750", "text": "1751"}, {"range": "1752", "text": "1741"}, {"range": "1753", "text": "1743"}, "Update the dependencies array to be: [decodedContactId, loadContacts]", {"range": "1754", "text": "1755"}, {"range": "1756", "text": "1741"}, {"range": "1757", "text": "1743"}, {"range": "1758", "text": "1741"}, {"range": "1759", "text": "1743"}, {"range": "1760", "text": "1741"}, {"range": "1761", "text": "1743"}, {"range": "1762", "text": "1741"}, {"range": "1763", "text": "1743"}, "Update the dependencies array to be: [hasMore, isLoadingMore, isLoading, fetchCalls]", {"range": "1764", "text": "1765"}, "Update the dependencies array to be: [fetchCalls]", {"range": "1766", "text": "1767"}, "Update the dependencies array to be: [fetchAgents, fetchSchedules]", {"range": "1768", "text": "1769"}, "Update the dependencies array to be: [fetchSchedules, page]", {"range": "1770", "text": "1771"}, "Update the dependencies array to be: [activeFilter, fetchSchedules, searchTerm]", {"range": "1772", "text": "1773"}, "Update the dependencies array to be: [fetchOrganization, organizationId]", {"range": "1774", "text": "1775"}, "replaceWithAlt", {"alt": "1776"}, {"range": "1777", "text": "1778"}, "Replace with `&apos;`.", {"alt": "1779"}, {"range": "1780", "text": "1781"}, "Replace with `&lsquo;`.", {"alt": "1782"}, {"range": "1783", "text": "1784"}, "Replace with `&#39;`.", {"alt": "1785"}, {"range": "1786", "text": "1787"}, "Replace with `&rsquo;`.", {"alt": "1776"}, {"range": "1788", "text": "1789"}, {"alt": "1779"}, {"range": "1790", "text": "1791"}, {"alt": "1782"}, {"range": "1792", "text": "1793"}, {"alt": "1785"}, {"range": "1794", "text": "1795"}, "Update the dependencies array to be: [fetchData, organizationId]", {"range": "1796", "text": "1797"}, {"range": "1798", "text": "1741"}, {"range": "1799", "text": "1743"}, {"range": "1800", "text": "1741"}, {"range": "1801", "text": "1743"}, {"range": "1802", "text": "1741"}, {"range": "1803", "text": "1743"}, "Update the dependencies array to be: [fetchLogs]", {"range": "1804", "text": "1805"}, "Update the dependencies array to be: [fetchLogs, page]", {"range": "1806", "text": "1807"}, {"range": "1808", "text": "1741"}, {"range": "1809", "text": "1743"}, {"range": "1810", "text": "1741"}, {"range": "1811", "text": "1743"}, {"range": "1812", "text": "1741"}, {"range": "1813", "text": "1743"}, {"range": "1814", "text": "1741"}, {"range": "1815", "text": "1743"}, {"range": "1816", "text": "1741"}, {"range": "1817", "text": "1743"}, "Update the dependencies array to be: [fetchCredits]", {"range": "1818", "text": "1819"}, "Update the dependencies array to be: [callPricePerMinute, isConnected, on, organizationId]", {"range": "1820", "text": "1821"}, "Update the dependencies array to be: [callPricePerMinute, fetchCredits]", {"range": "1822", "text": "1823"}, {"range": "1824", "text": "1741"}, {"range": "1825", "text": "1743"}, {"range": "1826", "text": "1741"}, {"range": "1827", "text": "1743"}, {"range": "1828", "text": "1741"}, {"range": "1829", "text": "1743"}, {"range": "1830", "text": "1741"}, {"range": "1831", "text": "1743"}, {"range": "1832", "text": "1741"}, {"range": "1833", "text": "1743"}, {"range": "1834", "text": "1741"}, {"range": "1835", "text": "1743"}, {"range": "1836", "text": "1741"}, {"range": "1837", "text": "1743"}, {"range": "1838", "text": "1741"}, {"range": "1839", "text": "1743"}, {"range": "1840", "text": "1741"}, {"range": "1841", "text": "1743"}, {"range": "1842", "text": "1741"}, {"range": "1843", "text": "1743"}, {"range": "1844", "text": "1741"}, {"range": "1845", "text": "1743"}, {"range": "1846", "text": "1741"}, {"range": "1847", "text": "1743"}, [7442, 7445], "unknown", [7442, 7445], "never", [12107, 12109], "[data.startDate, endDate, endTime, hasEndDate, startDate, startTime, updateDates]", [7960, 7972], "[campaignId, fetchCampaignData]", [11083, 11109], "[loadContacts, page, searchTerm, selectedCampaignId]", [12890, 12892], "[fetchAgents, loadContacts]", [23980, 23983], [23980, 23983], [6465, 6483], "[decodedContactId, loadContacts]", [7368, 7371], [7368, 7371], [13091, 13094], [13091, 13094], [13497, 13500], [13497, 13500], [13796, 13799], [13796, 13799], [11283, 11318], "[has<PERSON>ore, isLoadingMore, isLoading, fetchCalls]", [14564, 14566], "[fetchCalls]", [6990, 6992], "[fetchAgents, fetchSchedules]", [7682, 7688], "[fetchSchedules, page]", [8256, 8270], "[activeFilter, fetchSchedules, searchTerm]", [2229, 2245], "[fetchOrganization, organizationId]", "&apos;", [6132, 6173], "Update the Workspace&apos;s basic information.", "&lsquo;", [6132, 6173], "Update the Workspace&lsquo;s basic information.", "&#39;", [6132, 6173], "Update the Workspace&#39;s basic information.", "&rsquo;", [6132, 6173], "Update the Workspace&rsquo;s basic information.", [8976, 9019], "Manage the Workspace&apos;s billing and credits.", [8976, 9019], "Manage the Workspace&lsquo;s billing and credits.", [8976, 9019], "Manage the Workspace&#39;s billing and credits.", [8976, 9019], "Manage the Workspace&rsquo;s billing and credits.", [2147, 2163], "[fetchData, organizationId]", [341, 344], [341, 344], [3055, 3058], [3055, 3058], [4670, 4673], [4670, 4673], [5172, 5174], "[fetchLogs]", [5298, 5304], "[fetchLogs, page]", [3390, 3393], [3390, 3393], [4535, 4538], [4535, 4538], [11523, 11526], [11523, 11526], [2208, 2211], [2208, 2211], [2967, 2970], [2967, 2970], [7979, 7981], "[fetchCredits]", [10644, 10677], "[callPricePerMinute, isConnected, on, organizationId]", [12130, 12150], "[callPricePerMinute, fetchCredits]", [2686, 2689], [2686, 2689], [3461, 3464], [3461, 3464], [3488, 3491], [3488, 3491], [3872, 3875], [3872, 3875], [4508, 4511], [4508, 4511], [308, 311], [308, 311], [1725, 1728], [1725, 1728], [2540, 2543], [2540, 2543], [4320, 4323], [4320, 4323], [213, 216], [213, 216], [2099, 2102], [2099, 2102], [3407, 3410], [3407, 3410]]