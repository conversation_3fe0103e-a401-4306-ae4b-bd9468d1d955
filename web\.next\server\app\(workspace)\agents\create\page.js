(()=>{var e={};e.id=1545,e.ids=[1545],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},25926:(e,r,t)=>{Promise.resolve().then(t.bind(t,58178))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34174:(e,r,t)=>{Promise.resolve().then(t.bind(t,77900))},34631:e=>{"use strict";e.exports=require("tls")},52071:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(37413);t(61120);var n=t(77900);function a(){return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(n.default,{})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},58178:(e,r,t)=>{"use strict";t.d(r,{default:()=>b});var s=t(60687),n=t(43210),a=t(16189),o=t(28559),i=t(29523),l=t(85763),c=t(34688),d=t(93187),u=t(22758),p=t(63503),x=t(45685),m=t(78885),h=t(283),v=t(21696);let g=[{value:"profile",label:"Profile",component:c.A},{value:"prompt",label:"Prompt",component:d.A},{value:"voice",label:"Voice",component:h.A},{value:"brain",label:"Brain",component:x.A},{value:"actions",label:"Actions",component:u.A},{value:"advanced",label:"Advanced",component:m.A}];function b(){let e=(0,a.useRouter)(),[r,t]=(0,n.useState)("profile"),[c,d]=(0,n.useState)(!1),[u,x]=(0,n.useState)(!1),[m,h]=(0,n.useState)(!1),[b,f]=(0,n.useState)(null),{agent:C,setAgent:j,phoneNumbers:w,createAgentMutation:y}=(0,v.f)(),N=async()=>{x(!0),f(null);try{await y.mutateAsync(C),h(!0)}catch(e){console.error("Error creating agent:",e),f(e instanceof Error?e.message:"Failed to create agent")}finally{x(!1)}};return c?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[60vh] space-y-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"}),(0,s.jsx)("p",{className:"text-lg font-medium",children:"Loading..."})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[(0,s.jsxs)(i.$,{variant:"ghost",size:"icon",onClick:()=>e.push("/agents"),className:"rounded-full hover:bg-gray-100 dark:hover:bg-gray-800",children:[(0,s.jsx)(o.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{className:"sr-only",children:"Back"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-semibold tracking-tight",children:"Create Your New Agent"}),(0,s.jsx)("p",{className:"text-muted-foreground mt-1",children:"Configure your AI agent's capabilities and personality"})]})]}),(0,s.jsxs)(l.tU,{value:r,onValueChange:t,className:"space-y-6",children:[(0,s.jsx)("div",{className:"border-b",children:(0,s.jsx)(l.j7,{className:"w-full justify-start h-auto bg-transparent p-0",children:g.map(e=>(0,s.jsx)(l.Xi,{value:e.value,className:"data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none rounded-none px-4 py-3 bg-transparent",children:e.label},e.value))})}),(0,s.jsx)("div",{className:"bg-card rounded-lg border shadow-sm w-4/5 mx-auto",children:g.map(e=>(0,s.jsx)(l.av,{value:e.value,className:"m-0 focus-visible:outline-none focus-visible:ring-0",children:(0,s.jsx)(e.component,{agent:C,setAgent:j,phoneNumbers:w,isCreateMode:!0})},e.value))})]}),(0,s.jsx)("div",{className:"mt-6 flex justify-end",children:(0,s.jsx)(i.$,{onClick:N,disabled:u,className:"bg-black text-white dark:text-black dark:bg-white hover:from-purple-700 hover:to-blue-600",children:u?"Creating...":"Create Agent"})})]}),(0,s.jsx)(p.lG,{open:m,onOpenChange:h,children:(0,s.jsxs)(p.Cf,{className:"sm:max-w-md",children:[(0,s.jsx)(p.c7,{children:(0,s.jsxs)(p.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"bg-green-100 p-1 rounded-full dark:bg-green-900",children:(0,s.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-green-600 dark:text-green-400",children:(0,s.jsx)("path",{d:"M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})})}),"Agent Created Successfully"]})}),(0,s.jsx)("div",{className:"text-center py-4",children:(0,s.jsx)("p",{children:"Your new agent has been created successfully."})}),(0,s.jsx)(p.Es,{className:"sm:justify-center",children:(0,s.jsx)(i.$,{onClick:()=>{h(!1),e.push("/agents")},children:"Go to Agents"})})]})}),b&&(0,s.jsx)(p.lG,{open:!!b,onOpenChange:()=>f(null),children:(0,s.jsxs)(p.Cf,{className:"sm:max-w-md",children:[(0,s.jsx)(p.c7,{children:(0,s.jsxs)(p.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)("span",{className:"bg-red-100 p-1 rounded-full dark:bg-red-900",children:(0,s.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-red-600 dark:text-red-400",children:(0,s.jsx)("path",{d:"M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM7.49991 7.50019C7.77605 7.50019 8.00009 7.27615 8.00009 7.00001V4.00001C8.00009 3.72387 7.77605 3.49983 7.49991 3.49983C7.22377 3.49983 6.99973 3.72387 6.99973 4.00001V7.00001C6.99973 7.27615 7.22377 7.50019 7.49991 7.50019ZM7.49991 9.00001C7.22377 9.00001 6.99973 9.22405 6.99973 9.50019C6.99973 9.77633 7.22377 10.0004 7.49991 10.0004C7.77605 10.0004 8.00009 9.77633 8.00009 9.50019C8.00009 9.22405 7.77605 9.00001 7.49991 9.00001Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})})}),"Error"]})}),(0,s.jsx)("div",{className:"text-center py-4",children:(0,s.jsx)("p",{children:b})}),(0,s.jsx)(p.Es,{className:"sm:justify-center",children:(0,s.jsx)(i.$,{onClick:()=>f(null),variant:"outline",children:"Close"})})]})})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},77900:(e,r,t)=>{"use strict";t.d(r,{default:()=>n});var s=t(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call agentTabs() from the server but agentTabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\create\\CreateAgentContent.tsx","agentTabs");let n=(0,s.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\app\\\\(workspace)\\\\agents\\\\create\\\\CreateAgentContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\create\\CreateAgentContent.tsx","default")},79258:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=t(65239),n=t(48088),a=t(88170),o=t.n(a),i=t(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);t.d(r,l);let c={children:["",{children:["(workspace)",{children:["agents",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,52071)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\create\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,50184)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\create\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(workspace)/agents/create/page",pathname:"/agents/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[287,9176,7674,5814,598,5188,6034,8606,6913,5901,1476,4772,2093,1816],()=>t(79258));module.exports=s})();