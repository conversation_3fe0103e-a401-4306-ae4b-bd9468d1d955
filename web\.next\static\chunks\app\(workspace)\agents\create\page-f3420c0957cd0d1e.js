(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1545],{14610:(e,s,a)=>{Promise.resolve().then(a.bind(a,16840))},16840:(e,s,a)=>{"use strict";a.d(s,{default:()=>C});var l=a(95155),n=a(12115),r=a(35695),t=a(35169),c=a(30285),i=a(17313),d=a(38816),o=a(21773),u=a(88006),h=a(54165),m=a(29281),x=a(79167),p=a(26083),g=a(19940);let v=[{value:"profile",label:"Profile",component:d.A},{value:"prompt",label:"Prompt",component:o.A},{value:"voice",label:"Voice",component:p.A},{value:"brain",label:"Brain",component:m.A},{value:"actions",label:"Actions",component:u.A},{value:"advanced",label:"Advanced",component:x.A}];function C(){let e=(0,r.useRouter)(),[s,a]=(0,n.useState)("profile"),[d,o]=(0,n.useState)(!1),[u,m]=(0,n.useState)(!1),[x,p]=(0,n.useState)(!1),[C,j]=(0,n.useState)(null),{agent:b,setAgent:f,phoneNumbers:N,createAgentMutation:w}=(0,g.f)(),y=async()=>{m(!0),j(null);try{await w.mutateAsync(b),p(!0)}catch(e){console.error("Error creating agent:",e),j(e instanceof Error?e.message:"Failed to create agent")}finally{m(!1)}};return d?(0,l.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[60vh] space-y-4",children:[(0,l.jsx)("div",{className:"w-10 h-10 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"}),(0,l.jsx)("p",{className:"text-lg font-medium",children:"Loading..."})]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsxs)("div",{className:"container mx-auto py-8 px-4",children:[(0,l.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[(0,l.jsxs)(c.$,{variant:"ghost",size:"icon",onClick:()=>e.push("/agents"),className:"rounded-full hover:bg-gray-100 dark:hover:bg-gray-800",children:[(0,l.jsx)(t.A,{className:"h-5 w-5"}),(0,l.jsx)("span",{className:"sr-only",children:"Back"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-semibold tracking-tight",children:"Create Your New Agent"}),(0,l.jsx)("p",{className:"text-muted-foreground mt-1",children:"Configure your AI agent's capabilities and personality"})]})]}),(0,l.jsxs)(i.tU,{value:s,onValueChange:a,className:"space-y-6",children:[(0,l.jsx)("div",{className:"border-b",children:(0,l.jsx)(i.j7,{className:"w-full justify-start h-auto bg-transparent p-0",children:v.map(e=>(0,l.jsx)(i.Xi,{value:e.value,className:"data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none rounded-none px-4 py-3 bg-transparent",children:e.label},e.value))})}),(0,l.jsx)("div",{className:"bg-card rounded-lg border shadow-sm w-4/5 mx-auto",children:v.map(e=>(0,l.jsx)(i.av,{value:e.value,className:"m-0 focus-visible:outline-none focus-visible:ring-0",children:(0,l.jsx)(e.component,{agent:b,setAgent:f,phoneNumbers:N,isCreateMode:!0})},e.value))})]}),(0,l.jsx)("div",{className:"mt-6 flex justify-end",children:(0,l.jsx)(c.$,{onClick:y,disabled:u,className:"bg-black text-white dark:text-black dark:bg-white hover:from-purple-700 hover:to-blue-600",children:u?"Creating...":"Create Agent"})})]}),(0,l.jsx)(h.lG,{open:x,onOpenChange:p,children:(0,l.jsxs)(h.Cf,{className:"sm:max-w-md",children:[(0,l.jsx)(h.c7,{children:(0,l.jsxs)(h.L3,{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{className:"bg-green-100 p-1 rounded-full dark:bg-green-900",children:(0,l.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-green-600 dark:text-green-400",children:(0,l.jsx)("path",{d:"M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM10.1589 5.53774C10.3178 5.31191 10.2636 5.00001 10.0378 4.84109C9.81194 4.68217 9.50004 4.73642 9.34112 4.96225L6.51977 8.97154L5.35681 7.78706C5.16334 7.59002 4.84677 7.58711 4.64973 7.78058C4.45268 7.97404 4.44978 8.29061 4.64325 8.48765L6.22658 10.1003C6.33054 10.2062 6.47617 10.2604 6.62407 10.2483C6.77197 10.2363 6.90686 10.1591 6.99226 10.0377L10.1589 5.53774Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})})}),"Agent Created Successfully"]})}),(0,l.jsx)("div",{className:"text-center py-4",children:(0,l.jsx)("p",{children:"Your new agent has been created successfully."})}),(0,l.jsx)(h.Es,{className:"sm:justify-center",children:(0,l.jsx)(c.$,{onClick:()=>{p(!1),e.push("/agents")},children:"Go to Agents"})})]})}),C&&(0,l.jsx)(h.lG,{open:!!C,onOpenChange:()=>j(null),children:(0,l.jsxs)(h.Cf,{className:"sm:max-w-md",children:[(0,l.jsx)(h.c7,{children:(0,l.jsxs)(h.L3,{className:"flex items-center gap-2",children:[(0,l.jsx)("span",{className:"bg-red-100 p-1 rounded-full dark:bg-red-900",children:(0,l.jsx)("svg",{width:"20",height:"20",viewBox:"0 0 15 15",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"text-red-600 dark:text-red-400",children:(0,l.jsx)("path",{d:"M7.49991 0.877045C3.84222 0.877045 0.877075 3.84219 0.877075 7.49988C0.877075 11.1575 3.84222 14.1227 7.49991 14.1227C11.1576 14.1227 14.1227 11.1575 14.1227 7.49988C14.1227 3.84219 11.1576 0.877045 7.49991 0.877045ZM1.82708 7.49988C1.82708 4.36686 4.36689 1.82704 7.49991 1.82704C10.6329 1.82704 13.1727 4.36686 13.1727 7.49988C13.1727 10.6329 10.6329 13.1727 7.49991 13.1727C4.36689 13.1727 1.82708 10.6329 1.82708 7.49988ZM7.49991 7.50019C7.77605 7.50019 8.00009 7.27615 8.00009 7.00001V4.00001C8.00009 3.72387 7.77605 3.49983 7.49991 3.49983C7.22377 3.49983 6.99973 3.72387 6.99973 4.00001V7.00001C6.99973 7.27615 7.22377 7.50019 7.49991 7.50019ZM7.49991 9.00001C7.22377 9.00001 6.99973 9.22405 6.99973 9.50019C6.99973 9.77633 7.22377 10.0004 7.49991 10.0004C7.77605 10.0004 8.00009 9.77633 8.00009 9.50019C8.00009 9.22405 7.77605 9.00001 7.49991 9.00001Z",fill:"currentColor",fillRule:"evenodd",clipRule:"evenodd"})})}),"Error"]})}),(0,l.jsx)("div",{className:"text-center py-4",children:(0,l.jsx)("p",{children:C})}),(0,l.jsx)(h.Es,{className:"sm:justify-center",children:(0,l.jsx)(c.$,{onClick:()=>j(null),variant:"outline",children:"Close"})})]})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[2111,7540,4201,4341,6403,1071,6671,6544,6766,3860,7812,9735,6910,9621,4194,8441,1684,7358],()=>s(14610)),_N_E=e.O()}]);