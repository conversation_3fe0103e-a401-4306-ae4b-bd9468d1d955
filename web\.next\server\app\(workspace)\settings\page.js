(()=>{var e={};e.id=3211,e.ids=[3211],e.modules={846:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\app\\\\(workspace)\\\\settings\\\\SettingsContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\settings\\SettingsContent.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9725:(e,t,s)=>{"use strict";s.d(t,{default:()=>A});var a=s(60687),r=s(29523),n=s(43210),i=s(89667),o=s(80013),l=s(41862),d=s(5336);function c({label:e,description:t,value:s,onChange:c,onTest:u,placeholder:p="Enter API key"}){let[x,m]=(0,n.useState)(!1),[h,f]=(0,n.useState)(!1),[g,v]=(0,n.useState)(null),b=async()=>{if(u&&s.trim()){f(!0),v(null);try{let e=await u();v(e)}catch(e){v(!1),console.error("API test failed:",e)}finally{f(!1)}setTimeout(()=>v(null),3e3)}};return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-baseline justify-between",children:[(0,a.jsx)(o.J,{htmlFor:e.replace(/\s+/g,"-").toLowerCase(),children:e}),t&&(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:t})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(i.p,{id:e.replace(/\s+/g,"-").toLowerCase(),type:x?"text":"password",value:s,onChange:e=>c(e.target.value),placeholder:p,className:"pr-20"}),(0,a.jsx)(r.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-0 text-xs font-normal",onClick:()=>m(!x),children:x?"Hide":"Show"})]}),u&&(0,a.jsx)(r.$,{type:"button",variant:"outline",size:"sm",onClick:b,disabled:h||!s.trim(),className:"flex items-center gap-1 whitespace-nowrap",children:h?(0,a.jsx)(l.A,{className:"h-3 w-3 animate-spin"}):!0===g?(0,a.jsx)(d.A,{className:"h-3 w-3 text-green-500"}):!1===g?(0,a.jsx)("span",{className:"text-red-500",children:"Failed"}):"Test"})]})]})})}function u(){let[e,t]=(0,n.useState)(""),[s,i]=(0,n.useState)(""),[o,l]=(0,n.useState)(""),[d,u]=(0,n.useState)(""),[p,x]=(0,n.useState)(!1),m=async()=>(await new Promise(e=>setTimeout(e,1e3)),!0),h=async()=>(await new Promise(e=>setTimeout(e,1e3)),!0),f=async()=>(await new Promise(e=>setTimeout(e,1e3)),!0),g=async()=>(await new Promise(e=>setTimeout(e,1e3)),!1),v=async()=>{x(!0),await new Promise(e=>setTimeout(e,1e3)),x(!1)};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Language Model APIs"}),(0,a.jsxs)("div",{className:"grid gap-6",children:[(0,a.jsx)(c,{label:"OpenAI API Key",description:"Required for GPT-3.5, GPT-4, and DALL-E models",value:e,onChange:t,onTest:m,placeholder:"sk-..."}),(0,a.jsx)(c,{label:"Anthropic API Key",description:"Required for Claude models",value:s,onChange:i,onTest:h,placeholder:"sk-ant-..."}),(0,a.jsx)(c,{label:"Google API Key",description:"Required for Gemini models",value:o,onChange:l,onTest:f}),(0,a.jsx)(c,{label:"DeepSeek API Key",description:"Required for DeepSeek models",value:d,onChange:u,onTest:g})]})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)(r.$,{onClick:v,disabled:p,className:"bg-primary text-primary-foreground hover:bg-primary/90",children:p?"Saving...":"Save Changes"})})]})}var p=s(15079),x=s(54987),m=s(10218);function h(){let{theme:e,setTheme:t}=(0,m.D)(),[s,l]=(0,n.useState)(""),[d,c]=(0,n.useState)(""),[u,h]=(0,n.useState)("en"),[f,g]=(0,n.useState)(!0),[v,b]=(0,n.useState)(!1),j=async()=>{b(!0),await new Promise(e=>setTimeout(e,800)),b(!1)};return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Profile Information"}),(0,a.jsxs)("div",{className:"grid gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"name",children:"Name"}),(0,a.jsx)(i.p,{id:"name",value:s,onChange:e=>l(e.target.value),placeholder:"Your name"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{htmlFor:"email",children:"Email"}),(0,a.jsx)(i.p,{id:"email",type:"email",value:d,onChange:e=>c(e.target.value),placeholder:"<EMAIL>"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Preferences"}),(0,a.jsxs)("div",{className:"grid gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{children:"Theme"}),(0,a.jsxs)(p.l6,{value:e,onValueChange:t,children:[(0,a.jsx)(p.bq,{children:(0,a.jsx)(p.yv,{placeholder:"Select theme"})}),(0,a.jsxs)(p.gC,{children:[(0,a.jsx)(p.eb,{value:"light",children:"Light"}),(0,a.jsx)(p.eb,{value:"dark",children:"Dark"}),(0,a.jsx)(p.eb,{value:"system",children:"System"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.J,{children:"Language"}),(0,a.jsxs)(p.l6,{value:u,onValueChange:h,children:[(0,a.jsx)(p.bq,{children:(0,a.jsx)(p.yv,{placeholder:"Select language"})}),(0,a.jsxs)(p.gC,{children:[(0,a.jsx)(p.eb,{value:"en",children:"English"}),(0,a.jsx)(p.eb,{value:"es",children:"Espa\xf1ol"}),(0,a.jsx)(p.eb,{value:"fr",children:"Fran\xe7ais"})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(o.J,{children:"Notifications"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive notifications about your agents and tasks"})]}),(0,a.jsx)(x.d,{checked:f,onCheckedChange:g})]})]})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)(r.$,{onClick:j,disabled:v,className:"bg-primary text-primary-foreground hover:bg-primary/90",children:v?"Saving...":"Save Changes"})})]})})}var f=s(26373),g=s(40988),v=s(73437),b=s(78122),j=s(99270),y=s(40228),w=s(98492),N=s(11860);function C(){let[e,t]=(0,n.useState)([]),[s,o]=(0,n.useState)(!0),[l,d]=(0,n.useState)(""),[c,u]=(0,n.useState)(""),[x,m]=(0,n.useState)("all"),[h,C]=(0,n.useState)(void 0),[P,k]=(0,n.useState)(void 0),[S,A]=(0,n.useState)(1),[_,R]=(0,n.useState)(!0),[q,I]=(0,n.useState)(0),[T,z]=(0,n.useState)(0),[D,L]=(0,n.useState)(!1),O=(0,n.useRef)(null),E="http://localhost:4000",F=async(e=1,s=!1)=>{try{L(!0);let a=localStorage.getItem("access_token");if(!a){d("No access token available"),o(!1),L(!1);return}let r=new URLSearchParams;r.append("page",e.toString()),r.append("limit","20"),x&&"all"!==x&&r.append("level",x),c.trim()&&r.append("search",c.trim()),h&&r.append("startDate",h.toISOString().split("T")[0]),P&&r.append("endDate",P.toISOString().split("T")[0]);let n=await fetch(`${E}/api/logs?${r.toString()}`,{headers:{Authorization:`Bearer ${a}`}});if(!n.ok)throw Error("Failed to fetch logs");let i=await n.json();s?t(i.logs):t(e=>[...e,...i.logs]),I(i.total),z(i.totalPages),R(e<i.totalPages)}catch(e){console.error("Error fetching logs:",e),d("Failed to fetch logs. Please try again.")}finally{o(!1),L(!1)}},M=()=>{A(1),F(1,!0)},$=async()=>{try{o(!0);let e=localStorage.getItem("access_token");if(!e){d("No access token available"),o(!1);return}let t=await fetch(`${E}/api/logs/cleanup`,{headers:{Authorization:`Bearer ${e}`}});if(!t.ok)throw Error("Failed to cleanup logs");let s=await t.json();alert(`Successfully deleted ${s.deletedCount} logs older than 4 days`),A(1),F(1,!0)}catch(e){console.error("Error cleaning up logs:",e),d("Failed to cleanup logs. Please try again.")}finally{o(!1)}},G=(0,n.useCallback)(e=>{!s&&(O.current&&O.current.disconnect(),O.current=new IntersectionObserver(e=>{e[0].isIntersecting&&_&&!D&&A(e=>e+1)}),e&&O.current.observe(e))},[s,_,D]),V=e=>{switch(e.toLowerCase()){case"error":return"text-red-500";case"warn":return"text-yellow-500";case"info":return"text-green-500";default:return"text-gray-900"}};return(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-xl font-bold",children:"Logs"}),(0,a.jsxs)(r.$,{variant:"outline",size:"sm",onClick:$,disabled:s,children:[(0,a.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Clean Old Logs"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(j.A,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-gray-500"}),(0,a.jsx)(i.p,{placeholder:"Search logs...",value:c,onChange:e=>u(e.target.value),className:"pl-8",onKeyDown:e=>"Enter"===e.key&&M()})]}),(0,a.jsxs)(p.l6,{value:x||"all",onValueChange:m,children:[(0,a.jsx)(p.bq,{children:(0,a.jsx)(p.yv,{placeholder:"Filter by level"})}),(0,a.jsxs)(p.gC,{children:[(0,a.jsx)(p.eb,{value:"all",children:"All Levels"}),(0,a.jsx)(p.eb,{value:"INFO",children:"Info"}),(0,a.jsx)(p.eb,{value:"WARN",children:"Warning"}),(0,a.jsx)(p.eb,{value:"ERROR",children:"Error"})]})]}),(0,a.jsxs)(g.AM,{children:[(0,a.jsx)(g.Wv,{asChild:!0,children:(0,a.jsxs)(r.$,{variant:"outline",className:"justify-start text-left font-normal",children:[(0,a.jsx)(y.A,{className:"mr-2 h-4 w-4"}),h?(0,v.GP)(h,"PPP"):"Start date"]})}),(0,a.jsx)(g.hl,{className:"w-auto p-0",align:"start",children:(0,a.jsx)(f.V,{mode:"single",selected:h,onSelect:C,initialFocus:!0})})]}),(0,a.jsxs)(g.AM,{children:[(0,a.jsx)(g.Wv,{asChild:!0,children:(0,a.jsxs)(r.$,{variant:"outline",className:"justify-start text-left font-normal",children:[(0,a.jsx)(y.A,{className:"mr-2 h-4 w-4"}),P?(0,v.GP)(P,"PPP"):"End date"]})}),(0,a.jsx)(g.hl,{className:"w-auto p-0",align:"start",children:(0,a.jsx)(f.V,{mode:"single",selected:P,onSelect:k,initialFocus:!0})})]})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(r.$,{onClick:M,disabled:s||D,children:[(0,a.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Apply Filters"]}),(0,a.jsxs)(r.$,{variant:"outline",onClick:()=>{u(""),m("all"),C(void 0),k(void 0),A(1),F(1,!0)},disabled:s||D,children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"Reset"]})]}),!s&&e.length>0&&(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Showing ",e.length," of ",q," logs ",x&&"all"!==x&&`(filtered by ${x})`,T>1&&` - Page ${S} of ${T}`]}),s&&1===S?(0,a.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,a.jsx)("p",{children:"Loading logs..."})}):l?(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:(0,a.jsxs)("p",{children:["Error: ",l]})}):0===e.length?(0,a.jsx)("div",{className:"bg-gray-50 border border-gray-200 text-gray-700 px-4 py-8 rounded text-center",children:(0,a.jsx)("p",{children:"No logs found."})}):(0,a.jsxs)("div",{className:"overflow-x-auto border rounded-md",children:[(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Timestamp"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Level"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Message"}),(0,a.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Trace"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map((t,s)=>(0,a.jsxs)("tr",{ref:s===e.length-5?G:null,className:"hover:bg-gray-50 transition-colors",children:[(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:new Date(t.timestamp).toLocaleString()}),(0,a.jsx)("td",{className:`px-4 py-3 text-sm ${V(t.level)}`,children:(0,a.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium",children:t.level})}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900 max-w-md truncate",children:t.message}),(0,a.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900 max-w-xs truncate",children:t.trace||"-"})]},t._id||s))})]}),s&&S>1&&(0,a.jsx)("div",{className:"py-4 text-center text-gray-500",children:"Loading more logs..."}),!_&&e.length>0&&(0,a.jsx)("div",{className:"py-4 text-center text-gray-500",children:"End of logs"})]})]})}function P(){let[e,t]=(0,n.useState)(""),[s,i]=(0,n.useState)(""),[o,l]=(0,n.useState)(""),[d,u]=(0,n.useState)(!1),p=async()=>(await new Promise(e=>setTimeout(e,1e3)),!0),x=async()=>(await new Promise(e=>setTimeout(e,1e3)),!0),m=async()=>(await new Promise(e=>setTimeout(e,1e3)),!0),h=async()=>{u(!0),await new Promise(e=>setTimeout(e,1e3)),u(!1)};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Voice APIs"}),(0,a.jsxs)("div",{className:"grid gap-6",children:[(0,a.jsx)(c,{label:"ElevenLabs API Key",description:"Required for high-quality text-to-speech",value:e,onChange:t,onTest:p}),(0,a.jsx)(c,{label:"Deepgram API Key",description:"Required for real-time speech recognition",value:s,onChange:i,onTest:x}),(0,a.jsx)(c,{label:"AssemblyAI API Key",description:"Required for advanced speech recognition and analysis",value:o,onChange:l,onTest:m})]})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)(r.$,{onClick:h,disabled:d,className:"bg-primary text-primary-foreground hover:bg-primary/90",children:d?"Saving...":"Save Changes"})})]})}var k=s(44493),S=s(85763);function A(){let[e,t]=(0,n.useState)("general"),[s,r]=(0,n.useState)(null),i=s?.role==="superadmin",o="grid-cols-3";return i&&(o="grid-cols-4"),(0,a.jsx)("div",{className:"container py-6 px-4 md:px-6",children:(0,a.jsxs)(k.Zp,{children:[(0,a.jsx)(k.aR,{children:(0,a.jsx)(k.ZB,{className:"text-2xl",children:"Settings"})}),(0,a.jsx)(k.Wu,{children:(0,a.jsxs)(S.tU,{value:e,onValueChange:t,className:"w-full",children:[(0,a.jsxs)(S.j7,{className:`grid w-full ${o} mb-8`,children:[(0,a.jsx)(S.Xi,{value:"general",children:"General"}),(0,a.jsx)(S.Xi,{value:"api",children:"API Keys"}),(0,a.jsx)(S.Xi,{value:"voice",children:"Voice Settings"}),i&&(0,a.jsx)(S.Xi,{value:"logs",children:"Logs"})]}),(0,a.jsx)(S.av,{value:"general",children:(0,a.jsx)(h,{})}),(0,a.jsx)(S.av,{value:"api",children:(0,a.jsx)(u,{})}),(0,a.jsx)(S.av,{value:"voice",children:(0,a.jsx)(P,{})}),i&&(0,a.jsx)(S.av,{value:"logs",children:(0,a.jsx)(C,{})})]})})]})})}s(6607)},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>x,gC:()=>p,l6:()=>d,yv:()=>c});var a=s(60687);s(43210);var r=s(22670),n=s(78272),i=s(13964),o=s(3589),l=s(4780);function d({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e})}function u({className:e,children:t,...s}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger",className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s,children:[t,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function p({className:e,children:t,position:s="popper",...n}){return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...n,children:[(0,a.jsx)(m,{}),(0,a.jsx)(r.LM,{className:(0,l.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(h,{})]})})}function x({className:e,children:t,...s}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(i.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:t})]})}function m({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(o.A,{className:"size-4"})})}function h({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(n.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26373:(e,t,s)=>{"use strict";s.d(t,{V:()=>d});var a=s(60687);s(43210);var r=s(47033),n=s(14952),i=s(2438),o=s(4780),l=s(29523);function d({className:e,classNames:t,showOutsideDays:s=!0,...d}){return(0,a.jsx)(i.hv,{showOutsideDays:s,className:(0,o.cn)("p-3",e),classNames:{months:"flex flex-col sm:flex-row gap-2",month:"flex flex-col gap-4",caption:"flex justify-center pt-1 relative items-center w-full",caption_label:"text-sm font-medium",nav:"flex items-center gap-1",nav_button:(0,o.cn)((0,l.r)({variant:"outline"}),"size-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-x-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:(0,o.cn)("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md","range"===d.mode?"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md":"[&:has([aria-selected])]:rounded-md"),day:(0,o.cn)((0,l.r)({variant:"ghost"}),"size-8 p-0 font-normal aria-selected:opacity-100"),day_range_start:"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground",day_range_end:"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},components:{IconLeft:({className:e,...t})=>(0,a.jsx)(r.A,{className:(0,o.cn)("size-4",e),...t}),IconRight:({className:e,...t})=>(0,a.jsx)(n.A,{className:(0,o.cn)("size-4",e),...t})},...d})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40599:(e,t,s)=>{"use strict";s.d(t,{UC:()=>B,ZL:()=>V,bL:()=>$,l9:()=>G});var a=s(43210),r=s(70569),n=s(98599),i=s(11273),o=s(31355),l=s(1359),d=s(32547),c=s(96963),u=s(55509),p=s(25028),x=s(46059),m=s(14163),h=s(8730),f=s(65551),g=s(63376),v=s(42247),b=s(60687),j="Popover",[y,w]=(0,i.A)(j,[u.Bk]),N=(0,u.Bk)(),[C,P]=y(j),k=e=>{let{__scopePopover:t,children:s,open:r,defaultOpen:n,onOpenChange:i,modal:o=!1}=e,l=N(t),d=a.useRef(null),[p,x]=a.useState(!1),[m=!1,h]=(0,f.i)({prop:r,defaultProp:n,onChange:i});return(0,b.jsx)(u.bL,{...l,children:(0,b.jsx)(C,{scope:t,contentId:(0,c.B)(),triggerRef:d,open:m,onOpenChange:h,onOpenToggle:a.useCallback(()=>h(e=>!e),[h]),hasCustomAnchor:p,onCustomAnchorAdd:a.useCallback(()=>x(!0),[]),onCustomAnchorRemove:a.useCallback(()=>x(!1),[]),modal:o,children:s})})};k.displayName=j;var S="PopoverAnchor";a.forwardRef((e,t)=>{let{__scopePopover:s,...r}=e,n=P(S,s),i=N(s),{onCustomAnchorAdd:o,onCustomAnchorRemove:l}=n;return a.useEffect(()=>(o(),()=>l()),[o,l]),(0,b.jsx)(u.Mz,{...i,...r,ref:t})}).displayName=S;var A="PopoverTrigger",_=a.forwardRef((e,t)=>{let{__scopePopover:s,...a}=e,i=P(A,s),o=N(s),l=(0,n.s)(t,i.triggerRef),d=(0,b.jsx)(m.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":M(i.open),...a,ref:l,onClick:(0,r.m)(e.onClick,i.onOpenToggle)});return i.hasCustomAnchor?d:(0,b.jsx)(u.Mz,{asChild:!0,...o,children:d})});_.displayName=A;var R="PopoverPortal",[q,I]=y(R,{forceMount:void 0}),T=e=>{let{__scopePopover:t,forceMount:s,children:a,container:r}=e,n=P(R,t);return(0,b.jsx)(q,{scope:t,forceMount:s,children:(0,b.jsx)(x.C,{present:s||n.open,children:(0,b.jsx)(p.Z,{asChild:!0,container:r,children:a})})})};T.displayName=R;var z="PopoverContent",D=a.forwardRef((e,t)=>{let s=I(z,e.__scopePopover),{forceMount:a=s.forceMount,...r}=e,n=P(z,e.__scopePopover);return(0,b.jsx)(x.C,{present:a||n.open,children:n.modal?(0,b.jsx)(L,{...r,ref:t}):(0,b.jsx)(O,{...r,ref:t})})});D.displayName=z;var L=a.forwardRef((e,t)=>{let s=P(z,e.__scopePopover),i=a.useRef(null),o=(0,n.s)(t,i),l=a.useRef(!1);return a.useEffect(()=>{let e=i.current;if(e)return(0,g.Eq)(e)},[]),(0,b.jsx)(v.A,{as:h.DX,allowPinchZoom:!0,children:(0,b.jsx)(E,{...e,ref:o,trapFocus:s.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),l.current||s.triggerRef.current?.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,s=0===t.button&&!0===t.ctrlKey;l.current=2===t.button||s},{checkForDefaultPrevented:!1}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),O=a.forwardRef((e,t)=>{let s=P(z,e.__scopePopover),r=a.useRef(!1),n=a.useRef(!1);return(0,b.jsx)(E,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(r.current||s.triggerRef.current?.focus(),t.preventDefault()),r.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(n.current=!0));let a=t.target;s.triggerRef.current?.contains(a)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),E=a.forwardRef((e,t)=>{let{__scopePopover:s,trapFocus:a,onOpenAutoFocus:r,onCloseAutoFocus:n,disableOutsidePointerEvents:i,onEscapeKeyDown:c,onPointerDownOutside:p,onFocusOutside:x,onInteractOutside:m,...h}=e,f=P(z,s),g=N(s);return(0,l.Oh)(),(0,b.jsx)(d.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:r,onUnmountAutoFocus:n,children:(0,b.jsx)(o.qW,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:m,onEscapeKeyDown:c,onPointerDownOutside:p,onFocusOutside:x,onDismiss:()=>f.onOpenChange(!1),children:(0,b.jsx)(u.UC,{"data-state":M(f.open),role:"dialog",id:f.contentId,...g,...h,ref:t,style:{...h.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),F="PopoverClose";function M(e){return e?"open":"closed"}a.forwardRef((e,t)=>{let{__scopePopover:s,...a}=e,n=P(F,s);return(0,b.jsx)(m.sG.button,{type:"button",...a,ref:t,onClick:(0,r.m)(e.onClick,()=>n.onOpenChange(!1))})}).displayName=F,a.forwardRef((e,t)=>{let{__scopePopover:s,...a}=e,r=N(s);return(0,b.jsx)(u.i3,{...r,...a,ref:t})}).displayName="PopoverArrow";var $=k,G=_,V=T,B=D},40988:(e,t,s)=>{"use strict";s.d(t,{AM:()=>i,Wv:()=>o,hl:()=>l});var a=s(60687);s(43210);var r=s(40599),n=s(4780);function i({...e}){return(0,a.jsx)(r.bL,{"data-slot":"popover",...e})}function o({...e}){return(0,a.jsx)(r.l9,{"data-slot":"popover-trigger",...e})}function l({className:e,align:t="center",sideOffset:s=4,...i}){return(0,a.jsx)(r.ZL,{children:(0,a.jsx)(r.UC,{"data-slot":"popover-content",align:t,sideOffset:s,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...i})})}},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>c});var a=s(60687);s(43210);var r=s(4780);function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",e),...t})}function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("flex flex-col gap-1.5 px-6",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6",e),...t})}},45792:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=s(65239),r=s(48088),n=s(88170),i=s.n(n),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["(workspace)",{children:["settings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,67519)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\settings\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,50184)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\settings\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(workspace)/settings/page",pathname:"/settings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},48731:(e,t,s)=>{Promise.resolve().then(s.bind(s,9725))},54987:(e,t,s)=>{"use strict";s.d(t,{d:()=>i});var a=s(60687);s(43210);var r=s(90270),n=s(4780);function i({className:e,...t}){return(0,a.jsx)(r.bL,{"data-slot":"switch",className:(0,n.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 inline-flex h-5 w-9 shrink-0 items-center rounded-full border-2 border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,a.jsx)(r.zi,{"data-slot":"switch-thumb",className:(0,n.cn)("bg-background pointer-events-none block size-4 rounded-full ring-0 shadow-lg transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")})})}},55146:(e,t,s)=>{"use strict";s.d(t,{B8:()=>_,UC:()=>q,bL:()=>A,l9:()=>R});var a=s(43210),r=s(70569),n=s(11273),i=s(72942),o=s(46059),l=s(14163),d=s(43),c=s(65551),u=s(96963),p=s(60687),x="Tabs",[m,h]=(0,n.A)(x,[i.RG]),f=(0,i.RG)(),[g,v]=m(x),b=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,onValueChange:r,defaultValue:n,orientation:i="horizontal",dir:o,activationMode:x="automatic",...m}=e,h=(0,d.jH)(o),[f,v]=(0,c.i)({prop:a,onChange:r,defaultProp:n});return(0,p.jsx)(g,{scope:s,baseId:(0,u.B)(),value:f,onValueChange:v,orientation:i,dir:h,activationMode:x,children:(0,p.jsx)(l.sG.div,{dir:h,"data-orientation":i,...m,ref:t})})});b.displayName=x;var j="TabsList",y=a.forwardRef((e,t)=>{let{__scopeTabs:s,loop:a=!0,...r}=e,n=v(j,s),o=f(s);return(0,p.jsx)(i.bL,{asChild:!0,...o,orientation:n.orientation,dir:n.dir,loop:a,children:(0,p.jsx)(l.sG.div,{role:"tablist","aria-orientation":n.orientation,...r,ref:t})})});y.displayName=j;var w="TabsTrigger",N=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,disabled:n=!1,...o}=e,d=v(w,s),c=f(s),u=k(d.baseId,a),x=S(d.baseId,a),m=a===d.value;return(0,p.jsx)(i.q7,{asChild:!0,...c,focusable:!n,active:m,children:(0,p.jsx)(l.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":x,"data-state":m?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:u,...o,ref:t,onMouseDown:(0,r.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(a)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(a)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;m||n||!e||d.onValueChange(a)})})})});N.displayName=w;var C="TabsContent",P=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,forceMount:n,children:i,...d}=e,c=v(C,s),u=k(c.baseId,r),x=S(c.baseId,r),m=r===c.value,h=a.useRef(m);return a.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(o.C,{present:n||m,children:({present:s})=>(0,p.jsx)(l.sG.div,{"data-state":m?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!s,id:x,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:s&&i})})});function k(e,t){return`${e}-trigger-${t}`}function S(e,t){return`${e}-content-${t}`}P.displayName=C;var A=b,_=y,R=N,q=P},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67519:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var a=s(37413),r=s(846);function n(){return(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(r.default,{})})}},74075:e=>{"use strict";e.exports=require("zlib")},78122:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},78148:(e,t,s)=>{"use strict";s.d(t,{b:()=>o});var a=s(43210),r=s(14163),n=s(60687),i=a.forwardRef((e,t)=>(0,n.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=i},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80013:(e,t,s)=>{"use strict";s.d(t,{J:()=>i});var a=s(60687);s(43210);var r=s(78148),n=s(4780);function i({className:e,...t}){return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},83075:(e,t,s)=>{Promise.resolve().then(s.bind(s,846))},83997:e=>{"use strict";e.exports=require("tty")},85763:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>l,av:()=>d,j7:()=>o,tU:()=>i});var a=s(60687);s(43210);var r=s(55146),n=s(4780);function i({className:e,...t}){return(0,a.jsx)(r.bL,{"data-slot":"tabs",className:(0,n.cn)("flex flex-col gap-2",e),...t})}function o({className:e,...t}){return(0,a.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,n.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-1",e),...t})}function l({className:e,...t}){return(0,a.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,n.cn)("data-[state=active]:bg-background data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring inline-flex flex-1 items-center justify-center gap-1.5 rounded-md px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function d({className:e,...t}){return(0,a.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,n.cn)("flex-1 outline-none",e),...t})}},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var a=s(60687);s(43210);var r=s(4780);function n({className:e,type:t,...s}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},90270:(e,t,s)=>{"use strict";s.d(t,{bL:()=>w,zi:()=>N});var a=s(43210),r=s(70569),n=s(98599),i=s(11273),o=s(65551),l=s(83721),d=s(18853),c=s(14163),u=s(60687),p="Switch",[x,m]=(0,i.A)(p),[h,f]=x(p),g=a.forwardRef((e,t)=>{let{__scopeSwitch:s,name:i,checked:l,defaultChecked:d,required:p,disabled:x,value:m="on",onCheckedChange:f,form:g,...v}=e,[b,w]=a.useState(null),N=(0,n.s)(t,e=>w(e)),C=a.useRef(!1),P=!b||g||!!b.closest("form"),[k=!1,S]=(0,o.i)({prop:l,defaultProp:d,onChange:f});return(0,u.jsxs)(h,{scope:s,checked:k,disabled:x,children:[(0,u.jsx)(c.sG.button,{type:"button",role:"switch","aria-checked":k,"aria-required":p,"data-state":y(k),"data-disabled":x?"":void 0,disabled:x,value:m,...v,ref:N,onClick:(0,r.m)(e.onClick,e=>{S(e=>!e),P&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),P&&(0,u.jsx)(j,{control:b,bubbles:!C.current,name:i,value:m,checked:k,required:p,disabled:x,form:g,style:{transform:"translateX(-100%)"}})]})});g.displayName=p;var v="SwitchThumb",b=a.forwardRef((e,t)=>{let{__scopeSwitch:s,...a}=e,r=f(v,s);return(0,u.jsx)(c.sG.span,{"data-state":y(r.checked),"data-disabled":r.disabled?"":void 0,...a,ref:t})});b.displayName=v;var j=e=>{let{control:t,checked:s,bubbles:r=!0,...n}=e,i=a.useRef(null),o=(0,l.Z)(s),c=(0,d.X)(t);return a.useEffect(()=>{let e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(o!==s&&t){let a=new Event("click",{bubbles:r});t.call(e,s),e.dispatchEvent(a)}},[o,s,r]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:s,...n,tabIndex:-1,ref:i,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function y(e){return e?"checked":"unchecked"}var w=g,N=b},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},98492:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[287,9176,7674,5814,598,5188,6034,2766,3697,1476,4772],()=>s(45792));module.exports=a})();