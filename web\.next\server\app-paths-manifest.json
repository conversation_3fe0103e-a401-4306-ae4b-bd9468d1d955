{"/_not-found/page": "app/_not-found/page.js", "/(auth)/forgotpassword/page": "app/(auth)/forgotpassword/page.js", "/(auth)/register/page": "app/(auth)/register/page.js", "/(auth)/login/page": "app/(auth)/login/page.js", "/page": "app/page.js", "/(workspace)/agents/create/page": "app/(workspace)/agents/create/page.js", "/(workspace)/agents/edit/[id]/page": "app/(workspace)/agents/edit/[id]/page.js", "/(workspace)/agents/page": "app/(workspace)/agents/page.js", "/(workspace)/campaign/create/page": "app/(workspace)/campaign/create/page.js", "/(workspace)/brain/page": "app/(workspace)/brain/page.js", "/(workspace)/billing/page": "app/(workspace)/billing/page.js", "/(workspace)/campaign/page": "app/(workspace)/campaign/page.js", "/(workspace)/campaign/edit/[id]/page": "app/(workspace)/campaign/edit/[id]/page.js", "/(workspace)/contacts/page": "app/(workspace)/contacts/page.js", "/(workspace)/contacts/create/page": "app/(workspace)/contacts/create/page.js", "/(workspace)/contacts/edit/[contactName]/[contactId]/page": "app/(workspace)/contacts/edit/[contactName]/[contactId]/page.js", "/(workspace)/contacts/edit/[contactName]/page": "app/(workspace)/contacts/edit/[contactName]/page.js", "/(workspace)/dashboard/page": "app/(workspace)/dashboard/page.js", "/(workspace)/history/[fullName]/page": "app/(workspace)/history/[fullName]/page.js", "/(workspace)/history/page": "app/(workspace)/history/page.js", "/(workspace)/integration/page": "app/(workspace)/integration/page.js", "/(workspace)/phonenumber/buy/page": "app/(workspace)/phonenumber/buy/page.js", "/(workspace)/profile/page": "app/(workspace)/profile/page.js", "/(workspace)/schedule/page": "app/(workspace)/schedule/page.js", "/(workspace)/phonenumber/page": "app/(workspace)/phonenumber/page.js", "/(workspace)/settings/page": "app/(workspace)/settings/page.js", "/(workspace)/workspaces/[id]/users/page": "app/(workspace)/workspaces/[id]/users/page.js", "/(workspace)/voices/page": "app/(workspace)/voices/page.js", "/(workspace)/users/page": "app/(workspace)/users/page.js", "/(workspace)/workspaces/[id]/page": "app/(workspace)/workspaces/[id]/page.js", "/(workspace)/workspaces/page": "app/(workspace)/workspaces/page.js"}