(()=>{var e={};e.id=7456,e.ids=[7456],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,r)=>{"use strict";r.d(t,{A0:()=>o,BF:()=>i,Hj:()=>l,XI:()=>n,nA:()=>c,nd:()=>d});var s=r(60687);r(43210);var a=r(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...t})})}function o({className:e,...t}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...t})}function i({className:e,...t}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-muted-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},6760:(e,t,r)=>{"use strict";r.d(t,{default:()=>w});var s=r(60687),a=r(43210),n=r(16189),o=r(44493),i=r(6211),l=r(63503),d=r(29523),c=r(89667),u=r(80013),p=r(34729),x=r(96834),f=r(96474),g=r(41862),h=r(63143),m=r(41312),v=r(88233),b=r(52581),j=r(87610),y=r(4780);function w(){let[e,t]=(0,a.useState)([]),[r,w]=(0,a.useState)(!0),[k,C]=(0,a.useState)(!1),[N,A]=(0,a.useState)(!1),[D,_]=(0,a.useState)(null),[z,E]=(0,a.useState)({name:"",description:"",status:"active"}),[O,$]=(0,a.useState)(!1),P=(0,n.useRouter)(),S=async()=>{try{$(!0);let r=await (0,j.EC)(z);t([...e,r]),C(!1),E({name:"",description:"",status:"active"}),b.o.success("Workspace created successfully")}catch(e){console.error("Error creating Workspace:",e),b.o.error("Failed to create Workspace")}finally{$(!1)}},F=async()=>{if(D)try{$(!0),await (0,j.Dp)(D._id),t(e.filter(e=>e._id!==D._id)),A(!1),_(null),b.o.success("Workspace deleted successfully")}catch(e){console.error("Error deleting Workspace:",e),b.o.error("Failed to delete Workspace")}finally{$(!1)}},R=e=>{switch(e){case"active":return(0,s.jsx)(x.E,{className:"bg-green-500",children:"Active"});case"inactive":return(0,s.jsx)(x.E,{className:"bg-gray-500",children:"Inactive"});case"suspended":return(0,s.jsx)(x.E,{className:"bg-red-500",children:"Suspended"});default:return(0,s.jsx)(x.E,{children:e})}};return(0,s.jsxs)("div",{className:"container mx-auto py-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Workspaces"}),(0,s.jsxs)(l.lG,{open:k,onOpenChange:C,children:[(0,s.jsx)(l.zM,{asChild:!0,children:(0,s.jsxs)(d.$,{children:[(0,s.jsx)(f.A,{className:"mr-2 h-4 w-4"}),"Create Workspaces"]})}),(0,s.jsxs)(l.Cf,{children:[(0,s.jsxs)(l.c7,{children:[(0,s.jsx)(l.L3,{children:"Create New Workspaces"}),(0,s.jsx)(l.rr,{children:"Add a new Workspaces to the system."})]}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(u.J,{htmlFor:"name",className:"text-right",children:"Name"}),(0,s.jsx)(c.p,{id:"name",value:z.name,onChange:e=>E({...z,name:e.target.value}),className:"col-span-3"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,s.jsx)(u.J,{htmlFor:"description",className:"text-right",children:"Description"}),(0,s.jsx)(p.T,{id:"description",value:z.description,onChange:e=>E({...z,description:e.target.value}),className:"col-span-3"})]})]}),(0,s.jsxs)(l.Es,{children:[(0,s.jsx)(d.$,{variant:"outline",onClick:()=>C(!1),children:"Cancel"}),(0,s.jsx)(d.$,{onClick:S,disabled:O||!z.name,children:O?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Creating..."]}):"Create"})]})]})]})]}),(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsx)(o.ZB,{children:"All Workspaces"}),(0,s.jsx)(o.BT,{children:"Manage your Workspaces and their settings."})]}),(0,s.jsx)(o.Wu,{children:r?(0,s.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,s.jsx)(g.A,{className:"h-8 w-8 animate-spin"})}):0===e.length?(0,s.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No Workspaces found. Create one to get started."}):(0,s.jsxs)(i.XI,{children:[(0,s.jsx)(i.A0,{children:(0,s.jsxs)(i.Hj,{children:[(0,s.jsx)(i.nd,{children:"Name"}),(0,s.jsx)(i.nd,{children:"Status"}),(0,s.jsx)(i.nd,{children:"Credits"}),(0,s.jsx)(i.nd,{children:"Users"}),(0,s.jsx)(i.nd,{children:"Created"}),(0,s.jsx)(i.nd,{className:"text-right",children:"Actions"})]})}),(0,s.jsx)(i.BF,{children:e.map(e=>(0,s.jsxs)(i.Hj,{children:[(0,s.jsx)(i.nA,{className:"font-medium",children:e.name}),(0,s.jsx)(i.nA,{children:R(e.status)}),(0,s.jsx)(i.nA,{children:(0,y.v)(e.credits||0)}),(0,s.jsx)(i.nA,{children:(e.users?.length||0)+(e.adminUsers?.length||0)}),(0,s.jsx)(i.nA,{children:new Date(e.createdAt).toLocaleDateString()}),(0,s.jsx)(i.nA,{className:"text-right",children:(0,s.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,s.jsx)(d.$,{variant:"outline",size:"icon",onClick:()=>P.push(`/workspaces/${e._id}`),children:(0,s.jsx)(h.A,{className:"h-4 w-4"})}),(0,s.jsx)(d.$,{variant:"outline",size:"icon",onClick:()=>P.push(`/workspaces/${e._id}/users`),children:(0,s.jsx)(m.A,{className:"h-4 w-4"})}),(0,s.jsx)(d.$,{variant:"outline",size:"icon",className:"text-red-500",onClick:()=>{_(e),A(!0)},children:(0,s.jsx)(v.A,{className:"h-4 w-4"})})]})})]},e._id))})]})})]}),(0,s.jsx)(l.lG,{open:N,onOpenChange:A,children:(0,s.jsxs)(l.Cf,{children:[(0,s.jsxs)(l.c7,{children:[(0,s.jsx)(l.L3,{children:"Delete Organization"}),(0,s.jsxs)(l.rr,{children:['Are you sure you want to delete the organization "',D?.name,'"? This action cannot be undone.']})]}),(0,s.jsxs)(l.Es,{children:[(0,s.jsx)(d.$,{variant:"outline",onClick:()=>A(!1),children:"Cancel"}),(0,s.jsx)(d.$,{variant:"destructive",onClick:F,disabled:O,children:O?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Deleting..."]}):"Delete"})]})]})})]})}},9328:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(37413);r(61120);var a=r(12770);function n(){return(0,s.jsx)(a.default,{})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12770:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\app\\\\(workspace)\\\\workspaces\\\\OrganizationsContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\OrganizationsContent.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},23419:(e,t,r)=>{Promise.resolve().then(r.bind(r,12770))},26134:(e,t,r)=>{"use strict";r.d(t,{G$:()=>J,Hs:()=>y,UC:()=>et,VY:()=>es,ZL:()=>Q,bL:()=>Y,bm:()=>ea,hE:()=>er,hJ:()=>ee,l9:()=>K,lG:()=>C});var s=r(43210),a=r(70569),n=r(98599),o=r(11273),i=r(96963),l=r(65551),d=r(31355),c=r(32547),u=r(25028),p=r(46059),x=r(14163),f=r(1359),g=r(42247),h=r(63376),m=r(8730),v=r(60687),b="Dialog",[j,y]=(0,o.A)(b),[w,k]=j(b),C=e=>{let{__scopeDialog:t,children:r,open:a,defaultOpen:n,onOpenChange:o,modal:d=!0}=e,c=s.useRef(null),u=s.useRef(null),[p=!1,x]=(0,l.i)({prop:a,defaultProp:n,onChange:o});return(0,v.jsx)(w,{scope:t,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:x,onOpenToggle:s.useCallback(()=>x(e=>!e),[x]),modal:d,children:r})};C.displayName=b;var N="DialogTrigger",A=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,o=k(N,r),i=(0,n.s)(t,o.triggerRef);return(0,v.jsx)(x.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":L(o.open),...s,ref:i,onClick:(0,a.m)(e.onClick,o.onOpenToggle)})});A.displayName=N;var D="DialogPortal",[_,z]=j(D,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:r,children:a,container:n}=e,o=k(D,t);return(0,v.jsx)(_,{scope:t,forceMount:r,children:s.Children.map(a,e=>(0,v.jsx)(p.C,{present:r||o.open,children:(0,v.jsx)(u.Z,{asChild:!0,container:n,children:e})}))})};E.displayName=D;var O="DialogOverlay",$=s.forwardRef((e,t)=>{let r=z(O,e.__scopeDialog),{forceMount:s=r.forceMount,...a}=e,n=k(O,e.__scopeDialog);return n.modal?(0,v.jsx)(p.C,{present:s||n.open,children:(0,v.jsx)(P,{...a,ref:t})}):null});$.displayName=O;var P=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,a=k(O,r);return(0,v.jsx)(g.A,{as:m.DX,allowPinchZoom:!0,shards:[a.contentRef],children:(0,v.jsx)(x.sG.div,{"data-state":L(a.open),...s,ref:t,style:{pointerEvents:"auto",...s.style}})})}),S="DialogContent",F=s.forwardRef((e,t)=>{let r=z(S,e.__scopeDialog),{forceMount:s=r.forceMount,...a}=e,n=k(S,e.__scopeDialog);return(0,v.jsx)(p.C,{present:s||n.open,children:n.modal?(0,v.jsx)(R,{...a,ref:t}):(0,v.jsx)(T,{...a,ref:t})})});F.displayName=S;var R=s.forwardRef((e,t)=>{let r=k(S,e.__scopeDialog),o=s.useRef(null),i=(0,n.s)(t,r.contentRef,o);return s.useEffect(()=>{let e=o.current;if(e)return(0,h.Eq)(e)},[]),(0,v.jsx)(q,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=s.forwardRef((e,t)=>{let r=k(S,e.__scopeDialog),a=s.useRef(!1),n=s.useRef(!1);return(0,v.jsx)(q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(a.current||r.triggerRef.current?.focus(),t.preventDefault()),a.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(a.current=!0,"pointerdown"!==t.detail.originalEvent.type||(n.current=!0));let s=t.target;r.triggerRef.current?.contains(s)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),q=s.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:a,onOpenAutoFocus:o,onCloseAutoFocus:i,...l}=e,u=k(S,r),p=s.useRef(null),x=(0,n.s)(t,p);return(0,f.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(c.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:o,onUnmountAutoFocus:i,children:(0,v.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":L(u.open),...l,ref:x,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(Z,{titleId:u.titleId}),(0,v.jsx)(X,{contentRef:p,descriptionId:u.descriptionId})]})]})}),I="DialogTitle",B=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,a=k(I,r);return(0,v.jsx)(x.sG.h2,{id:a.titleId,...s,ref:t})});B.displayName=I;var W="DialogDescription",M=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,a=k(W,r);return(0,v.jsx)(x.sG.p,{id:a.descriptionId,...s,ref:t})});M.displayName=W;var H="DialogClose",G=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,n=k(H,r);return(0,v.jsx)(x.sG.button,{type:"button",...s,ref:t,onClick:(0,a.m)(e.onClick,()=>n.onOpenChange(!1))})});function L(e){return e?"open":"closed"}G.displayName=H;var V="DialogTitleWarning",[J,U]=(0,o.q)(V,{contentName:S,titleName:I,docsSlug:"dialog"}),Z=({titleId:e})=>{let t=U(V),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return s.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},X=({contentRef:e,descriptionId:t})=>{let r=U("DialogDescriptionWarning"),a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return s.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(a)},[a,e,t]),null},Y=C,K=A,Q=E,ee=$,et=F,er=B,es=M,ea=G},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34035:(e,t,r)=>{Promise.resolve().then(r.bind(r,6760))},34631:e=>{"use strict";e.exports=require("tls")},34729:(e,t,r)=>{"use strict";r.d(t,{T:()=>n});var s=r(60687);r(43210);var a=r(4780);function n({className:e,...t}){return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,a.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>i,Zp:()=>n,aR:()=>o,wL:()=>c});var s=r(60687);r(43210);var a=r(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("flex flex-col gap-1.5 px-6",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6",e),...t})}},46714:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var s=r(65239),a=r(48088),n=r(88170),o=r.n(n),i=r(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let d={children:["",{children:["(workspace)",{children:["workspaces",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9328)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,50184)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(workspace)/workspaces/page",pathname:"/workspaces",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63503:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>p,Es:()=>f,HM:()=>c,L3:()=>g,c7:()=>x,lG:()=>i,rr:()=>h,zM:()=>l});var s=r(60687);r(43210);var a=r(26134),n=r(11860),o=r(4780);function i({...e}){return(0,s.jsx)(a.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,s.jsx)(a.l9,{"data-slot":"dialog-trigger",...e})}function d({...e}){return(0,s.jsx)(a.ZL,{"data-slot":"dialog-portal",...e})}function c({...e}){return(0,s.jsx)(a.bm,{"data-slot":"dialog-close",...e})}function u({className:e,...t}){return(0,s.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-650 bg-black/50",e),...t})}function p({className:e,children:t,...r}){return(0,s.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,s.jsx)(u,{}),(0,s.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-650 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...r,children:[t,(0,s.jsxs)(a.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(n.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,o.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function f({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,o.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function g({className:e,...t}){return(0,s.jsx)(a.hE,{"data-slot":"dialog-title",className:(0,o.cn)("text-lg leading-none font-semibold",e),...t})}function h({className:e,...t}){return(0,s.jsx)(a.VY,{"data-slot":"dialog-description",className:(0,o.cn)("text-muted-foreground text-sm",e),...t})}},74075:e=>{"use strict";e.exports=require("zlib")},74678:(e,t,r)=>{"use strict";r.d(t,{H:()=>s,e:()=>a});let s="http://localhost:4000",a="pk_test_51ROz1YRpJ0zLf0aTbgbDkpShvfpNxdZPet1QXClapTckA7Cy0tsaxY2qY1dp8oSBGOFqnh0vugjd8mDluFWgKpRL00bACyumT8"},78148:(e,t,r)=>{"use strict";r.d(t,{b:()=>i});var s=r(43210),a=r(14163),n=r(60687),o=s.forwardRef((e,t)=>(0,n.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var i=o},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>o});var s=r(60687);r(43210);var a=r(78148),n=r(4780);function o({className:e,...t}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87610:(e,t,r)=>{"use strict";r.d(t,{Dp:()=>d,EC:()=>o,J:()=>u,L_:()=>i,SA:()=>n,VO:()=>c,co:()=>l,h6:()=>a});var s=r(74678);let a=async()=>{let e=await fetch(`${s.H}/api/organizations`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch organizations");return e.json()},n=async e=>{let t=await fetch(`${s.H}/api/organizations/${e}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch organization");return t.json()},o=async e=>{let t=await fetch(`${s.H}/api/organizations`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Failed to create organization");return t.json()},i=async(e,t)=>{let r=await fetch(`${s.H}/api/organizations/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify(t)});if(!r.ok)throw Error((await r.json()).message||"Failed to update organization");return r.json()},l=async(e,t)=>{let r=await fetch(`${s.H}/api/organizations/${e}/billing`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify(t)});if(!r.ok)throw Error((await r.json()).message||"Failed to update organization billing");return r.json()},d=async e=>{let t=await fetch(`${s.H}/api/organizations/${e}`,{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!t.ok)throw Error((await t.json()).message||"Failed to delete organization");return t.json()},c=async(e,t,r)=>{let a=await fetch(`${s.H}/api/organizations/${e}/users/${t}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify({isAdmin:r})});if(!a.ok)throw Error((await a.json()).message||"Failed to add user to organization");return a.json()},u=async(e,t)=>{let r=await fetch(`${s.H}/api/organizations/${e}/users/${t}`,{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!r.ok)throw Error((await r.json()).message||"Failed to remove user from organization");return r.json()}},88233:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(60687);r(43210);var a=r(4780);function n({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96474:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var s=r(60687);r(43210);var a=r(8730),n=r(24224),o=r(4780);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...n}){let l=r?a.DX:"span";return(0,s.jsx)(l,{"data-slot":"badge",className:(0,o.cn)(i({variant:t}),e),...n})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[287,9176,7674,5814,598,5188,1476,4772],()=>r(46714));module.exports=s})();