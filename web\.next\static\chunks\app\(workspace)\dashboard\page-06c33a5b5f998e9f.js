(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2050],{2488:(e,s,t)=>{"use strict";t.d(s,{default:()=>r});var a=t(95155),l=t(226);function r(e){let{children:s,duration:t=.5,delay:r=0,direction:n="up",distance:i=30,className:c="",once:d=!0,viewOffset:o=.1}=e,m=0,x=0;return"up"===n&&(m=i),"down"===n&&(m=-i),"left"===n&&(x=i),"right"===n&&(x=-i),(0,a.jsx)(l.P.div,{initial:{y:m,x:x,opacity:0},whileInView:{y:0,x:0,opacity:1},transition:{duration:t,delay:r,ease:"easeOut"},viewport:{once:d,amount:o},className:c,children:s})}},26126:(e,s,t)=>{"use strict";t.d(s,{E:()=>c});var a=t(95155);t(12115);var l=t(99708),r=t(74466),n=t(59434);let i=(0,r.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:s,variant:t,asChild:r=!1,...c}=e,d=r?l.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(i({variant:t}),s),...c})}},30285:(e,s,t)=>{"use strict";t.d(s,{$:()=>c,r:()=>i});var a=t(95155);t(12115);var l=t(99708),r=t(74466),n=t(59434);let i=(0,r.F)("inline-flex items-center cursor-pointer justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:s,variant:t,size:r,asChild:c=!1,...d}=e,o=c?l.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,n.cn)(i({variant:t,size:r,className:s})),...d})}},58358:(e,s,t)=>{"use strict";t.d(s,{default:()=>F});var a=t(95155),l=t(66695),r=t(30285),n=t(26126),i=t(59409),c=t(14186),d=t(6076),o=t(9446),m=t(15865),x=t(94788),u=t(19420),h=t(62209),g=t(50741),v=t(69074),b=t(81284),f=t(38611),j=t(50675),p=t(17580),A=t(83540),N=t(69893),y=t(62341),w=t(12115),C=t(62829),E=t(2488),k=t(57476),D=t(83013),M=t(6874),z=t.n(M),S=t(66766);let F=function(){let[e,s]=(0,w.useState)(!0),[t,M]=(0,w.useState)(null),[F,Q]=(0,w.useState)(null),[_,R]=(0,w.useState)("all"),[P,I]=(0,w.useState)("all"),[T,W]=(0,w.useState)(null);(0,w.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("access_token");if(!e){console.error("No access token available");return}let s=await fetch("".concat("http://localhost:4000","/api/auth/me"),{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!s.ok)throw Error("Failed to fetch user profile: ".concat(s.status));let t=await s.json();Q(t.role)}catch(e){console.error("Failed to load user profile:",e)}})()},[]),(0,w.useEffect)(()=>{(async()=>{s(!0);try{let e=localStorage.getItem("access_token");if(!e){console.error("No access token available"),M("Authentication required. Please log in again."),s(!1);return}let t=await fetch("".concat("http://localhost:4000","/api/dashboard/metrics?timeRange=").concat(_,"&agentType=").concat(P),{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch dashboard data: ".concat(t.status));let a=await t.json();W(a)}catch(e){console.error("Failed to load dashboard data:",e),M("Error loading dashboard data. Please try again later.")}finally{s(!1)}})()},[_,P]);let Z=e=>{let s="string"==typeof e?parseInt(e):e,t=Math.floor(s/6e4),a=Math.floor(s%6e4/1e3);return"".concat(t,":").concat(a.toString().padStart(2,"0"))},B=Array.from({length:10},(e,s)=>{let t=(null==T?void 0:T.callMetrics.connectionRate)||0,a=t;return s<4?a=t-(13-s):s>8?a=t+(s-2):6===s&&(a=t+2),{day:s+1,value:Math.max(0,Math.min(100,a))}}),L=e=>{switch(e){case"customer-busy":return{icon:(0,a.jsx)(c.A,{className:"h-4 w-4"}),label:"Customer Busy"};case"customer-ended-call":return{icon:(0,a.jsx)(d.A,{className:"h-4 w-4"}),label:"Customer Ended The call"};case"assistant-ended-call":return{icon:(0,a.jsx)(o.A,{className:"h-4 w-4"}),label:"Agent Ended The call"};case"customer-did-not-answer":return{icon:(0,a.jsx)(d.A,{className:"h-4 w-4"}),label:"Customer Did not Answer"};case"customer-out-of-reach":return{icon:(0,a.jsx)(d.A,{className:"h-4 w-4"}),label:"Customer Out Of Reach"};case"voicemail":return{icon:(0,a.jsx)(m.A,{className:"h-4 w-4"}),label:"Voicemail"};case"silence-timed-out":return{icon:(0,a.jsx)(c.A,{className:"h-4 w-4"}),label:"Silence Timed Out"};default:return{icon:(0,a.jsx)(x.A,{className:"h-4 w-4"}),label:"Others (Timed out or failed to reach)"}}},U=Array.from({length:10},(e,s)=>{let t=(null==T?void 0:T.callMetrics.answerRate)||0,a=t;return s<3?a=t-(10-s):s>7?a=t+(s-5):5===s&&(a=t+3),{day:s+1,value:Math.max(0,Math.min(100,a))}});return e?(0,a.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[60vh] space-y-4",children:[(0,a.jsx)("div",{className:"w-10 h-10 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:"Loading..."})]}):t?(0,a.jsx)("div",{className:"flex flex-col items-center justify-center min-h-[60vh] space-y-4",children:(0,a.jsxs)("div",{className:"p-4 bg-red-50 text-red-500 rounded-lg",children:[(0,a.jsxs)("p",{children:["Error loading dashboard: ",t]}),(0,a.jsx)("button",{className:"mt-2 px-4 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200",onClick:()=>window.location.reload(),children:"Retry"})]})}):(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)(E.default,{direction:"up",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6",children:[(0,a.jsxs)("h1",{className:"text-2xl font-bold",children:["Dashboard ","all"!==P&&"(".concat(P," Agents)"),"all"!==_&&" | Last ".concat(_," days")]}),(0,a.jsx)("div",{className:"flex flex-col sm:flex-row gap-4 w-full md:w-auto",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full",children:[(0,a.jsxs)(i.l6,{defaultValue:"30",value:_,onValueChange:R,children:[(0,a.jsx)(i.bq,{className:"h-9 w-full sm:w-[180px]",children:(0,a.jsx)(i.yv,{placeholder:"Last 30 days"})}),(0,a.jsxs)(i.gC,{children:[(0,a.jsx)(i.eb,{value:"all",children:"All"}),(0,a.jsx)(i.eb,{value:"7",children:"Last 7 days"}),(0,a.jsx)(i.eb,{value:"14",children:"Last 14 days"}),(0,a.jsx)(i.eb,{value:"30",children:"Last 30 days"}),(0,a.jsx)(i.eb,{value:"90",children:"Last 90 days"})]})]}),(0,a.jsxs)(i.l6,{defaultValue:"all",value:P,onValueChange:I,children:[(0,a.jsx)(i.bq,{className:"h-9 w-full sm:w-[180px]",children:(0,a.jsx)(i.yv,{placeholder:"All Agents",children:"all"===P?"All Agents":"".concat(P," Agents")})}),(0,a.jsxs)(i.gC,{children:[(0,a.jsx)(i.eb,{value:"all",children:"All Agents"}),(null==T?void 0:T.agentRoles)&&T.agentRoles.map(e=>(0,a.jsxs)(i.eb,{value:e,children:[e," Agents"]},e))]})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 md:gap-6 mb-6",children:[(0,a.jsx)(l.Zp,{className:"border rounded-lg ",children:(0,a.jsx)(l.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total Calls"}),(0,a.jsx)("div",{className:"flex items-baseline",children:(0,a.jsx)("span",{className:"text-2xl font-bold mr-2",children:(null==T?void 0:T.callMetrics.totalCalls)||0})})]}),(0,a.jsx)(u.A,{className:"h-5 w-5 text-gray-400"})]})})}),(0,a.jsx)(l.Zp,{className:"border rounded-lg ",children:(0,a.jsx)(l.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total Call in Minutes"}),(0,a.jsxs)("div",{className:"flex items-baseline",children:[(0,a.jsx)("span",{className:"text-2xl font-bold mr-1",children:(null==T?void 0:T.callMetrics.totalMinutes.toFixed(1))||0}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"min"})]})]}),(0,a.jsx)(c.A,{className:"h-5 w-5 text-gray-400"})]})})}),(0,a.jsx)(l.Zp,{className:"border rounded-lg ",children:(0,a.jsx)(l.Wu,{className:"pt-6",children:(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Av. Call Length"}),(0,a.jsxs)("div",{className:"flex items-baseline",children:[(0,a.jsx)("span",{className:"text-2xl font-bold mr-1",children:(e=>{let s=Math.round(e/1e3),t=Math.floor(s/60);return"".concat(t,"m ").concat((s%60).toString().padStart(2,"0"),"s")})((null==T?void 0:T.callMetrics.averageLength)||0)}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:"sec"})]})]}),(0,a.jsx)(c.A,{className:"h-5 w-5 text-gray-400"})]})})}),(0,a.jsx)(l.Zp,{className:"border rounded-lg ",children:(0,a.jsxs)(l.Wu,{className:"pt-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Call Engagement Rate"}),(0,a.jsx)("div",{className:"flex items-baseline",children:(0,a.jsxs)("span",{className:"text-2xl font-bold mr-1",children:[(null==T?void 0:T.callMetrics.connectionRate)||0,"%"]})})]}),(0,a.jsx)(h.A,{className:"h-5 w-5 text-gray-400"})]}),(0,a.jsx)(E.default,{delay:.3,direction:"right",children:(0,a.jsx)("div",{className:"h-12 mt-2",children:(0,a.jsx)(A.u,{width:"100%",height:"100%",children:(0,a.jsx)(N.Q,{data:B,margin:{top:0,right:0,left:0,bottom:0},children:(0,a.jsx)(y.G,{type:"monotone",dataKey:"value",stroke:"#4157ea",strokeWidth:2,fill:"#4157ea20"})})})})})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6",children:[(0,a.jsx)(l.Zp,{className:"border rounded-lg",children:(0,a.jsxs)(l.Wu,{className:"pt-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total Campaigns"}),(0,a.jsx)(g.A,{className:"h-5 w-5 text-gray-400"})]}),(0,a.jsxs)("div",{className:"flex items-baseline",children:[(0,a.jsx)("span",{className:"text-2xl font-bold mr-2",children:(null==T?void 0:T.totalCounts.totalCampaigns)||0}),(0,a.jsx)("span",{className:"text-lg text-gray-500",children:"campaigns"})]})]})}),(0,a.jsx)(l.Zp,{className:"border rounded-lg",children:(0,a.jsxs)(l.Wu,{className:"pt-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total Scheduled Calls"}),(0,a.jsx)(v.A,{className:"h-5 w-5 text-gray-400"})]}),(0,a.jsxs)("div",{className:"flex items-baseline",children:[(0,a.jsx)("span",{className:"text-2xl font-bold mr-2",children:(null==T?void 0:T.totalCounts.totalScheduledCalls)||0}),(0,a.jsx)("span",{className:"text-lg text-gray-500",children:"scheduled"})]})]})}),(0,a.jsx)(l.Zp,{className:"border rounded-lg",children:(0,a.jsxs)(l.Wu,{className:"pt-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total Contacts"}),(0,a.jsx)(b.A,{className:"h-5 w-5 text-gray-400"})]}),(0,a.jsxs)("div",{className:"flex items-baseline",children:[(0,a.jsx)("div",{className:"text-2xl font-bold mr-2",children:(null==T?void 0:T.totalCounts.totalContacts)||0}),(0,a.jsx)("span",{className:"text-lg text-gray-500",children:"contacts"})]})]})}),(0,a.jsx)(l.Zp,{className:"border rounded-lg",children:(0,a.jsxs)(l.Wu,{className:"pt-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Answer Call Rate"}),(0,a.jsx)("div",{className:"flex items-baseline",children:(0,a.jsxs)("span",{className:"text-2xl font-bold mr-1",children:[(null==T?void 0:T.callMetrics.answerRate)||0,"%"]})})]}),(0,a.jsx)(f.A,{className:"h-5 w-5 text-gray-400"})]}),(0,a.jsx)(E.default,{delay:.3,direction:"right",children:(0,a.jsx)("div",{className:"h-10",children:(0,a.jsx)(A.u,{width:"100%",height:"100%",children:(0,a.jsx)(N.Q,{data:U,margin:{top:0,right:0,left:0,bottom:0},children:(0,a.jsx)(y.G,{type:"monotone",dataKey:"value",stroke:"#4157ea",strokeWidth:2,fill:"#4157ea20"})})})})})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6 mb-6",children:[(0,a.jsx)(l.Zp,{className:"border rounded-lg",children:(0,a.jsxs)(l.Wu,{className:"pt-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("h3",{className:"text-sm font-medium",children:["Recent Calls ","all"!==P&&"(".concat(P,")")]}),(0,a.jsxs)(n.E,{variant:"outline",className:"bg-blue-50 text-blue-800 hover:bg-blue-50 border-none",children:[(null==T?void 0:T.callMetrics.totalCalls)||0," calls"]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[null==T?void 0:T.recentCalls.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between border-b pb-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3",children:(0,a.jsx)(j.A,{className:"h-4 w-4 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-sm font-medium",children:[" ",e.fullName," | ",e.mobileNumber]}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:(0,k.m)(new Date(e.callStartTime),{addSuffix:!0})})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"h-3.5 w-3.5 text-blue-600 mr-2"}),(0,a.jsx)(n.E,{variant:"outline",className:"bg-blue-100 text-blue-800 hover:bg-blue-100 border-none mr-1",children:Z(e.callDuration)})]})]},e._id)),(0,a.jsx)(z(),{href:"/history",children:(0,a.jsx)(r.$,{variant:"ghost",size:"sm",className:"w-full text-blue-600 hover:text-blue-700 hover:bg-blue-50",children:"View all calls"})})]})]})}),(0,a.jsx)(l.Zp,{className:"border rounded-lg",children:(0,a.jsxs)(l.Wu,{className:"pt-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("h3",{className:"text-sm font-medium",children:["Last Scheduled Cost ","all"!==P&&"(".concat(P,")")]}),(0,a.jsxs)(n.E,{variant:"outline",className:"bg-amber-50 text-amber-800 hover:bg-amber-50 border-none",children:[(null==T?void 0:T.recentSchedules.length)||0," scheduled"]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[null==T?void 0:T.recentSchedules.slice(0,3).slice(0,3).map(e=>{var s,t;return(0,a.jsxs)("div",{className:"flex items-center justify-between border-b pb-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-amber-100 flex items-center justify-center mr-3",children:(0,a.jsx)(v.A,{className:"h-4 w-4 text-amber-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("p",{className:"text-sm font-medium",children:[(null===(s=e.contacts[0])||void 0===s?void 0:s.Name)||"N/A"," | ",(null===(t=e.contacts[0])||void 0===t?void 0:t.MobileNumber)||"N/A"]}),(0,a.jsxs)("p",{className:"text-xs text-gray-400",children:[(0,D.GP)(new Date(e.scheduledTime),"MMM d, yyyy h:mm a")," • ",e.region||"N/A"]})]})]}),(0,a.jsx)(n.E,{variant:"outline",className:"\n              ".concat("executed"===e.status?"bg-green-100 text-green-800 hover:bg-green-100":"pending"===e.status?"bg-amber-100 text-yellow-800 hover:bg-yellow-100":"bg-red-100 text-red-800 hover:bg-red-100"," border-none\n            "),children:e.status||"pending"})]},e._id)}),(0,a.jsx)(z(),{href:"/schedule",children:(0,a.jsx)(r.$,{variant:"ghost",size:"sm",className:"w-full text-amber-600 hover:text-amber-700 hover:bg-amber-50",children:"View all scheduled calls"})})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6",children:[(0,a.jsx)(l.Zp,{className:"border rounded-lg ",children:(0,a.jsxs)(l.Wu,{className:"pt-3",children:[(0,a.jsxs)("h3",{className:"text-sm font-medium mb-6",children:[" Sentiment Overview ","all"!==P&&"(".concat(P,")")," "]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center mt-9",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-2",children:(0,a.jsx)("span",{className:"text-xl",children:"\uD83D\uDE0A"})}),(0,a.jsx)("h4",{className:"text-sm",children:"Positive"}),(0,a.jsxs)("p",{className:"text-xl font-bold",children:[(null==T?void 0:T.sentiments.positive)||0,"%"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-2",children:(0,a.jsx)("span",{className:"text-xl",children:"\uD83D\uDE10"})}),(0,a.jsx)("h4",{className:"text-sm",children:"Neutral"}),(0,a.jsxs)("p",{className:"text-xl font-bold",children:[(null==T?void 0:T.sentiments.neutral)||0,"%"]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mx-auto mb-2",children:(0,a.jsx)("span",{className:"text-xl",children:"\uD83D\uDE1E"})}),(0,a.jsx)("h4",{className:"text-sm",children:"Negative"}),(0,a.jsxs)("p",{className:"text-xl font-bold",children:[(null==T?void 0:T.sentiments.negative)||0,"%"]})]})]})]})}),(0,a.jsx)(l.Zp,{className:"border rounded-lg",children:(0,a.jsxs)(l.Wu,{className:"pt-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("h3",{className:"text-sm font-medium",children:["  Recent Campaigns ","all"!==P&&"(".concat(P,")")," "]}),(0,a.jsxs)(n.E,{variant:"outline",className:"bg-green-50 text-green-800 hover:bg-green-50 border-none",children:[(null==T?void 0:T.recentCampaigns.length)||0," campaigns"]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[null==T?void 0:T.recentCampaigns.slice(0,3).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between border-b pb-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3",children:(0,a.jsx)(p.A,{className:"h-4 w-4 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:e.name}),(0,a.jsxs)("p",{className:"text-xs text-gray-400",children:[(0,D.GP)(new Date(e.startDate),"MMM d, yyyy")," - ",e.endDate?(0,D.GP)(new Date(e.endDate),"MMM d, yyyy"):"No End Date"]})]})]}),(0,a.jsx)(n.E,{variant:"outline",className:"\n                  ".concat("active"===e.status?"bg-green-100 text-green-800 hover:bg-green-100":"paused"===e.status?"bg-amber-100 text-amber-800 hover:bg-amber-100":"bg-gray-100 text-gray-800 hover:bg-gray-100"," border-none\n                "),children:"active"===e.status?"Active":"paused"===e.status?"Paused":"completed"===e.status?"Completed":"Inactive"})]},e._id||s)),(0,a.jsx)(z(),{href:"/campaign",children:(0,a.jsx)(r.$,{variant:"ghost",size:"sm",className:"w-full text-green-600 hover:text-green-700 hover:bg-green-50",children:"View all campaigns"})})]})]})}),(0,a.jsx)(l.Zp,{className:"border rounded-lg",children:(0,a.jsxs)(l.Wu,{className:"pt-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("h3",{className:"text-sm font-medium",children:["Most Used Agents ","all"!==P&&"(".concat(P,")")]}),(0,a.jsx)(n.E,{variant:"outline",className:"bg-purple-50 text-purple-800 hover:bg-purple-50 border-none",children:"superadmin"===F?"all"===P?"".concat(null==T?void 0:T.topAgents.length," agents"):"".concat(null==T?void 0:T.topAgents.filter(e=>e.role===P).length," agents"):"all"===P?"".concat(null==T?void 0:T.topAgents.filter(e=>"active"===e.status).length," agents"):"".concat(null==T?void 0:T.topAgents.filter(e=>"active"===e.status&&e.role===P).length," agents")})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[null==T?void 0:T.topAgents.slice(0,5).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between border-b pb-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3",children:e.avatar?(0,a.jsx)("img",{src:e.avatar,alt:"".concat(e.name," avatar"),className:"h-full w-full rounded-full object-cover",onError:e=>{e.currentTarget.onerror=null,e.currentTarget.src=C.A.src}}):(0,a.jsx)(S.default,{src:C.A,alt:"".concat(null==e?void 0:e.name," avatar"),width:64,height:64,className:"object-cover h-full w-full"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:(null==e?void 0:e.name)||"Agent"}),(0,a.jsx)("p",{className:"text-xs text-gray-400",children:(null==e?void 0:e.role)||"Assistant"})]})]}),(0,a.jsxs)(n.E,{variant:"outline",className:"bg-purple-100 text-purple-800 hover:bg-purple-100 border-none",children:[(null==e?void 0:e.callCount)||0," calls"]})]},e.id||s)),(0,a.jsx)(z(),{href:"/agents",children:(0,a.jsx)(r.$,{variant:"ghost",size:"sm",className:"w-full text-purple-600 hover:text-purple-700 hover:bg-purple-50",children:"View all agents"})})]})]})}),(0,a.jsx)(l.Zp,{className:"border rounded-lg",children:(0,a.jsxs)(l.Wu,{className:"pt-6",children:[(0,a.jsxs)("h3",{className:"text-sm font-medium mb-4",children:["Call End Reasons ","all"!==P&&"(".concat(P,")")]}),(0,a.jsx)("div",{className:"space-y-4",children:null==T?void 0:T.callEndReasons.slice(0,6).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between border-b pb-3",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3",children:L(e.reason).icon}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:L(e.reason).label}),(0,a.jsxs)("p",{className:"text-xs text-gray-400",children:[String(e.count)," calls"]})]})]}),(0,a.jsxs)(n.E,{variant:"outline",className:"bg-blue-100 text-blue-800 hover:bg-blue-100 border-none",children:[e.percentage,"%"]})]},s))})]})})]})]})})}},59409:(e,s,t)=>{"use strict";t.d(s,{bq:()=>m,eb:()=>u,gC:()=>x,l6:()=>d,yv:()=>o});var a=t(95155);t(12115);var l=t(31992),r=t(66474),n=t(5196),i=t(47863),c=t(59434);function d(e){let{...s}=e;return(0,a.jsx)(l.bL,{"data-slot":"select",...s})}function o(e){let{...s}=e;return(0,a.jsx)(l.WT,{"data-slot":"select-value",...s})}function m(e){let{className:s,children:t,...n}=e;return(0,a.jsxs)(l.l9,{"data-slot":"select-trigger",className:(0,c.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...n,children:[t,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(r.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:s,children:t,position:r="popper",...n}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{"data-slot":"select-content",className:(0,c.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:r,...n,children:[(0,a.jsx)(h,{}),(0,a.jsx)(l.LM,{className:(0,c.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(g,{})]})})}function u(e){let{className:s,children:t,...r}=e;return(0,a.jsxs)(l.q7,{"data-slot":"select-item",className:(0,c.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",s),...r,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(l.p4,{children:t})]})}function h(e){let{className:s,...t}=e;return(0,a.jsx)(l.PP,{"data-slot":"select-scroll-up-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,a.jsx)(i.A,{className:"size-4"})})}function g(e){let{className:s,...t}=e;return(0,a.jsx)(l.wn,{"data-slot":"select-scroll-down-button",className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...t,children:(0,a.jsx)(r.A,{className:"size-4"})})}},59434:(e,s,t)=>{"use strict";t.d(s,{cn:()=>r,v:()=>n});var a=t(52596),l=t(39688);function r(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,l.QP)((0,a.$)(s))}function n(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}},62829:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a={src:"/_next/static/media/Binghatti-Lisa.85c81ecb.jpeg",height:1586,width:1586,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/2wBDAQoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/wgARCAAIAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAX/xAAUAQEAAAAAAAAAAAAAAAAAAAAC/9oADAMBAAIQAxAAAACeA//EABsQAAEFAQEAAAAAAAAAAAAAAAECAwQREwAi/9oACAEBAAE/AG6e3khuoyZAbQNDWYR6SO//xAAVEQEBAAAAAAAAAAAAAAAAAAABAP/aAAgBAgEBPwAL/8QAFhEAAwAAAAAAAAAAAAAAAAAAAAFB/9oACAEDAQE/AHD/2Q==",blurWidth:8,blurHeight:8}},66695:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>d,ZB:()=>i,Zp:()=>r,aR:()=>n,wL:()=>o});var a=t(95155);t(12115);var l=t(59434);function r(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,l.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",s),...t})}function n(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,l.cn)("flex flex-col gap-1.5 px-6",s),...t})}function i(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,l.cn)("leading-none font-semibold",s),...t})}function c(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,l.cn)("text-muted-foreground text-sm",s),...t})}function d(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,l.cn)("px-6",s),...t})}function o(e){let{className:s,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,l.cn)("flex items-center px-6",s),...t})}},90521:(e,s,t)=>{Promise.resolve().then(t.bind(t,2488)),Promise.resolve().then(t.bind(t,58358))}},e=>{var s=s=>e(e.s=s);e.O(0,[4201,4341,6403,6544,226,6766,6874,2593,7331,8441,1684,7358],()=>s(90521)),_N_E=e.O()}]);