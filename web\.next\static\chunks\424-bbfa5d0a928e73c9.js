"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[424],{35695:(e,r,n)=>{var t=n(18999);n.o(t,"useParams")&&n.d(r,{useParams:function(){return t.useParams}}),n.o(t,"usePathname")&&n.d(r,{usePathname:function(){return t.usePathname}}),n.o(t,"useRouter")&&n.d(r,{useRouter:function(){return t.useRouter}})},48698:(e,r,n)=>{n.d(r,{UC:()=>eY,YJ:()=>eZ,q7:()=>eW,JU:()=>eJ,ZL:()=>ez,bL:()=>eq,wv:()=>eQ,l9:()=>eH});var t=n(12115),o=n(85185),a=n(6101),u=n(46081),l=n(5845),i=n(63655),s=n(82284),d=n(94315),c=n(19178),f=n(92293),p=n(25519),v=n(61285),m=n(35152),h=n(34378),w=n(28905),g=n(89196),x=n(99708),y=n(39033),b=n(38168),R=n(93795),C=n(95155),M=["Enter"," "],j=["ArrowUp","PageDown","End"],D=["ArrowDown","PageUp","Home",...j],_={ltr:[...M,"ArrowRight"],rtl:[...M,"ArrowLeft"]},k={ltr:["ArrowLeft"],rtl:["ArrowRight"]},P="Menu",[I,E,T]=(0,s.N)(P),[F,N]=(0,u.A)(P,[T,m.Bk,g.RG]),A=(0,m.Bk)(),S=(0,g.RG)(),[L,O]=F(P),[K,G]=F(P),B=e=>{let{__scopeMenu:r,open:n=!1,children:o,dir:a,onOpenChange:u,modal:l=!0}=e,i=A(r),[s,c]=t.useState(null),f=t.useRef(!1),p=(0,y.c)(u),v=(0,d.jH)(a);return t.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,C.jsx)(m.bL,{...i,children:(0,C.jsx)(L,{scope:r,open:n,onOpenChange:p,content:s,onContentChange:c,children:(0,C.jsx)(K,{scope:r,onClose:t.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:v,modal:l,children:o})})})};B.displayName=P;var U=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=A(n);return(0,C.jsx)(m.Mz,{...o,...t,ref:r})});U.displayName="MenuAnchor";var V="MenuPortal",[X,q]=F(V,{forceMount:void 0}),H=e=>{let{__scopeMenu:r,forceMount:n,children:t,container:o}=e,a=O(V,r);return(0,C.jsx)(X,{scope:r,forceMount:n,children:(0,C.jsx)(w.C,{present:n||a.open,children:(0,C.jsx)(h.Z,{asChild:!0,container:o,children:t})})})};H.displayName=V;var z="MenuContent",[Y,Z]=F(z),J=t.forwardRef((e,r)=>{let n=q(z,e.__scopeMenu),{forceMount:t=n.forceMount,...o}=e,a=O(z,e.__scopeMenu),u=G(z,e.__scopeMenu);return(0,C.jsx)(I.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(w.C,{present:t||a.open,children:(0,C.jsx)(I.Slot,{scope:e.__scopeMenu,children:u.modal?(0,C.jsx)(W,{...o,ref:r}):(0,C.jsx)(Q,{...o,ref:r})})})})}),W=t.forwardRef((e,r)=>{let n=O(z,e.__scopeMenu),u=t.useRef(null),l=(0,a.s)(r,u);return t.useEffect(()=>{let e=u.current;if(e)return(0,b.Eq)(e)},[]),(0,C.jsx)($,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),Q=t.forwardRef((e,r)=>{let n=O(z,e.__scopeMenu);return(0,C.jsx)($,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),$=t.forwardRef((e,r)=>{let{__scopeMenu:n,loop:u=!1,trapFocus:l,onOpenAutoFocus:i,onCloseAutoFocus:s,disableOutsidePointerEvents:d,onEntryFocus:v,onEscapeKeyDown:h,onPointerDownOutside:w,onFocusOutside:y,onInteractOutside:b,onDismiss:M,disableOutsideScroll:_,...k}=e,P=O(z,n),I=G(z,n),T=A(n),F=S(n),N=E(n),[L,K]=t.useState(null),B=t.useRef(null),U=(0,a.s)(r,B,P.onContentChange),V=t.useRef(0),X=t.useRef(""),q=t.useRef(0),H=t.useRef(null),Z=t.useRef("right"),J=t.useRef(0),W=_?R.A:t.Fragment,Q=_?{as:x.DX,allowPinchZoom:!0}:void 0,$=e=>{var r,n;let t=X.current+e,o=N().filter(e=>!e.disabled),a=document.activeElement,u=null===(r=o.find(e=>e.ref.current===a))||void 0===r?void 0:r.textValue,l=function(e,r,n){var t;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=(t=Math.max(n?e.indexOf(n):-1,0),e.map((r,n)=>e[(t+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==n?u:void 0}(o.map(e=>e.textValue),t,u),i=null===(n=o.find(e=>e.textValue===l))||void 0===n?void 0:n.ref.current;!function e(r){X.current=r,window.clearTimeout(V.current),""!==r&&(V.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};t.useEffect(()=>()=>window.clearTimeout(V.current),[]),(0,f.Oh)();let ee=t.useCallback(e=>{var r,n;return Z.current===(null===(r=H.current)||void 0===r?void 0:r.side)&&function(e,r){return!!r&&function(e,r){let{x:n,y:t}=e,o=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let u=r[e].x,l=r[e].y,i=r[a].x,s=r[a].y;l>t!=s>t&&n<(i-u)*(t-l)/(s-l)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},r)}(e,null===(n=H.current)||void 0===n?void 0:n.area)},[]);return(0,C.jsx)(Y,{scope:n,searchRef:X,onItemEnter:t.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),onItemLeave:t.useCallback(e=>{var r;ee(e)||(null===(r=B.current)||void 0===r||r.focus(),K(null))},[ee]),onTriggerLeave:t.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),pointerGraceTimerRef:q,onPointerGraceIntentChange:t.useCallback(e=>{H.current=e},[]),children:(0,C.jsx)(W,{...Q,children:(0,C.jsx)(p.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.m)(i,e=>{var r;e.preventDefault(),null===(r=B.current)||void 0===r||r.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,C.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:h,onPointerDownOutside:w,onFocusOutside:y,onInteractOutside:b,onDismiss:M,children:(0,C.jsx)(g.bL,{asChild:!0,...F,dir:I.dir,orientation:"vertical",loop:u,currentTabStopId:L,onCurrentTabStopIdChange:K,onEntryFocus:(0,o.m)(v,e=>{I.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,C.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":ej(P.open),"data-radix-menu-content":"",dir:I.dir,...T,...k,ref:U,style:{outline:"none",...k.style},onKeyDown:(0,o.m)(k.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!n&&t&&$(e.key));let o=B.current;if(e.target!==o||!D.includes(e.key))return;e.preventDefault();let a=N().filter(e=>!e.disabled).map(e=>e.ref.current);j.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let n of e)if(n===r||(n.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,o.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(V.current),X.current="")}),onPointerMove:(0,o.m)(e.onPointerMove,ek(e=>{let r=e.target,n=J.current!==e.clientX;e.currentTarget.contains(r)&&n&&(Z.current=e.clientX>J.current?"right":"left",J.current=e.clientX)}))})})})})})})});J.displayName=z;var ee=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,C.jsx)(i.sG.div,{role:"group",...t,ref:r})});ee.displayName="MenuGroup";var er=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,C.jsx)(i.sG.div,{...t,ref:r})});er.displayName="MenuLabel";var en="MenuItem",et="menu.itemSelect",eo=t.forwardRef((e,r)=>{let{disabled:n=!1,onSelect:u,...l}=e,s=t.useRef(null),d=G(en,e.__scopeMenu),c=Z(en,e.__scopeMenu),f=(0,a.s)(r,s),p=t.useRef(!1);return(0,C.jsx)(ea,{...l,ref:f,disabled:n,onClick:(0,o.m)(e.onClick,()=>{let e=s.current;if(!n&&e){let r=new CustomEvent(et,{bubbles:!0,cancelable:!0});e.addEventListener(et,e=>null==u?void 0:u(e),{once:!0}),(0,i.hO)(e,r),r.defaultPrevented?p.current=!1:d.onClose()}}),onPointerDown:r=>{var n;null===(n=e.onPointerDown)||void 0===n||n.call(e,r),p.current=!0},onPointerUp:(0,o.m)(e.onPointerUp,e=>{var r;p.current||null===(r=e.currentTarget)||void 0===r||r.click()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=""!==c.searchRef.current;!n&&(!r||" "!==e.key)&&M.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eo.displayName=en;var ea=t.forwardRef((e,r)=>{let{__scopeMenu:n,disabled:u=!1,textValue:l,...s}=e,d=Z(en,n),c=S(n),f=t.useRef(null),p=(0,a.s)(r,f),[v,m]=t.useState(!1),[h,w]=t.useState("");return t.useEffect(()=>{let e=f.current;if(e){var r;w((null!==(r=e.textContent)&&void 0!==r?r:"").trim())}},[s.children]),(0,C.jsx)(I.ItemSlot,{scope:n,disabled:u,textValue:null!=l?l:h,children:(0,C.jsx)(g.q7,{asChild:!0,...c,focusable:!u,children:(0,C.jsx)(i.sG.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":u||void 0,"data-disabled":u?"":void 0,...s,ref:p,onPointerMove:(0,o.m)(e.onPointerMove,ek(e=>{u?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.m)(e.onPointerLeave,ek(e=>d.onItemLeave(e))),onFocus:(0,o.m)(e.onFocus,()=>m(!0)),onBlur:(0,o.m)(e.onBlur,()=>m(!1))})})})}),eu=t.forwardRef((e,r)=>{let{checked:n=!1,onCheckedChange:t,...a}=e;return(0,C.jsx)(ev,{scope:e.__scopeMenu,checked:n,children:(0,C.jsx)(eo,{role:"menuitemcheckbox","aria-checked":eD(n)?"mixed":n,...a,ref:r,"data-state":e_(n),onSelect:(0,o.m)(a.onSelect,()=>null==t?void 0:t(!!eD(n)||!n),{checkForDefaultPrevented:!1})})})});eu.displayName="MenuCheckboxItem";var el="MenuRadioGroup",[ei,es]=F(el,{value:void 0,onValueChange:()=>{}}),ed=t.forwardRef((e,r)=>{let{value:n,onValueChange:t,...o}=e,a=(0,y.c)(t);return(0,C.jsx)(ei,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,C.jsx)(ee,{...o,ref:r})})});ed.displayName=el;var ec="MenuRadioItem",ef=t.forwardRef((e,r)=>{let{value:n,...t}=e,a=es(ec,e.__scopeMenu),u=n===a.value;return(0,C.jsx)(ev,{scope:e.__scopeMenu,checked:u,children:(0,C.jsx)(eo,{role:"menuitemradio","aria-checked":u,...t,ref:r,"data-state":e_(u),onSelect:(0,o.m)(t.onSelect,()=>{var e;return null===(e=a.onValueChange)||void 0===e?void 0:e.call(a,n)},{checkForDefaultPrevented:!1})})})});ef.displayName=ec;var ep="MenuItemIndicator",[ev,em]=F(ep,{checked:!1}),eh=t.forwardRef((e,r)=>{let{__scopeMenu:n,forceMount:t,...o}=e,a=em(ep,n);return(0,C.jsx)(w.C,{present:t||eD(a.checked)||!0===a.checked,children:(0,C.jsx)(i.sG.span,{...o,ref:r,"data-state":e_(a.checked)})})});eh.displayName=ep;var ew=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,C.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...t,ref:r})});ew.displayName="MenuSeparator";var eg=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=A(n);return(0,C.jsx)(m.i3,{...o,...t,ref:r})});eg.displayName="MenuArrow";var[ex,ey]=F("MenuSub"),eb="MenuSubTrigger",eR=t.forwardRef((e,r)=>{let n=O(eb,e.__scopeMenu),u=G(eb,e.__scopeMenu),l=ey(eb,e.__scopeMenu),i=Z(eb,e.__scopeMenu),s=t.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:c}=i,f={__scopeMenu:e.__scopeMenu},p=t.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return t.useEffect(()=>p,[p]),t.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),c(null)}},[d,c]),(0,C.jsx)(U,{asChild:!0,...f,children:(0,C.jsx)(ea,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":l.contentId,"data-state":ej(n.open),...e,ref:(0,a.t)(r,l.onTriggerChange),onClick:r=>{var t;null===(t=e.onClick)||void 0===t||t.call(e,r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.m)(e.onPointerMove,ek(r=>{i.onItemEnter(r),r.defaultPrevented||e.disabled||n.open||s.current||(i.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.m)(e.onPointerLeave,ek(e=>{var r,t;p();let o=null===(r=n.content)||void 0===r?void 0:r.getBoundingClientRect();if(o){let r=null===(t=n.content)||void 0===t?void 0:t.dataset.side,a="right"===r,u=o[a?"left":"right"],l=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:u,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:u,y:o.bottom}],side:r}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.m)(e.onKeyDown,r=>{let t=""!==i.searchRef.current;if(!e.disabled&&(!t||" "!==r.key)&&_[u.dir].includes(r.key)){var o;n.onOpenChange(!0),null===(o=n.content)||void 0===o||o.focus(),r.preventDefault()}})})})});eR.displayName=eb;var eC="MenuSubContent",eM=t.forwardRef((e,r)=>{let n=q(z,e.__scopeMenu),{forceMount:u=n.forceMount,...l}=e,i=O(z,e.__scopeMenu),s=G(z,e.__scopeMenu),d=ey(eC,e.__scopeMenu),c=t.useRef(null),f=(0,a.s)(r,c);return(0,C.jsx)(I.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(w.C,{present:u||i.open,children:(0,C.jsx)(I.Slot,{scope:e.__scopeMenu,children:(0,C.jsx)($,{id:d.contentId,"aria-labelledby":d.triggerId,...l,ref:f,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var r;s.isUsingKeyboardRef.current&&(null===(r=c.current)||void 0===r||r.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>{e.target!==d.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),n=k[s.dir].includes(e.key);if(r&&n){var t;i.onOpenChange(!1),null===(t=d.trigger)||void 0===t||t.focus(),e.preventDefault()}})})})})})});function ej(e){return e?"open":"closed"}function eD(e){return"indeterminate"===e}function e_(e){return eD(e)?"indeterminate":e?"checked":"unchecked"}function ek(e){return r=>"mouse"===r.pointerType?e(r):void 0}eM.displayName=eC;var eP="DropdownMenu",[eI,eE]=(0,u.A)(eP,[N]),eT=N(),[eF,eN]=eI(eP),eA=e=>{let{__scopeDropdownMenu:r,children:n,dir:o,open:a,defaultOpen:u,onOpenChange:i,modal:s=!0}=e,d=eT(r),c=t.useRef(null),[f=!1,p]=(0,l.i)({prop:a,defaultProp:u,onChange:i});return(0,C.jsx)(eF,{scope:r,triggerId:(0,v.B)(),triggerRef:c,contentId:(0,v.B)(),open:f,onOpenChange:p,onOpenToggle:t.useCallback(()=>p(e=>!e),[p]),modal:s,children:(0,C.jsx)(B,{...d,open:f,onOpenChange:p,dir:o,modal:s,children:n})})};eA.displayName=eP;var eS="DropdownMenuTrigger",eL=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,disabled:t=!1,...u}=e,l=eN(eS,n),s=eT(n);return(0,C.jsx)(U,{asChild:!0,...s,children:(0,C.jsx)(i.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...u,ref:(0,a.t)(r,l.triggerRef),onPointerDown:(0,o.m)(e.onPointerDown,e=>{t||0!==e.button||!1!==e.ctrlKey||(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eL.displayName=eS;var eO=e=>{let{__scopeDropdownMenu:r,...n}=e,t=eT(r);return(0,C.jsx)(H,{...t,...n})};eO.displayName="DropdownMenuPortal";var eK="DropdownMenuContent",eG=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...a}=e,u=eN(eK,n),l=eT(n),i=t.useRef(!1);return(0,C.jsx)(J,{id:u.contentId,"aria-labelledby":u.triggerId,...l,...a,ref:r,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var r;i.current||null===(r=u.triggerRef.current)||void 0===r||r.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.m)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,n=0===r.button&&!0===r.ctrlKey,t=2===r.button||n;(!u.modal||t)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eG.displayName=eK;var eB=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,C.jsx)(ee,{...o,...t,ref:r})});eB.displayName="DropdownMenuGroup";var eU=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,C.jsx)(er,{...o,...t,ref:r})});eU.displayName="DropdownMenuLabel";var eV=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,C.jsx)(eo,{...o,...t,ref:r})});eV.displayName="DropdownMenuItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,C.jsx)(eu,{...o,...t,ref:r})}).displayName="DropdownMenuCheckboxItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,C.jsx)(ed,{...o,...t,ref:r})}).displayName="DropdownMenuRadioGroup",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,C.jsx)(ef,{...o,...t,ref:r})}).displayName="DropdownMenuRadioItem",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,C.jsx)(eh,{...o,...t,ref:r})}).displayName="DropdownMenuItemIndicator";var eX=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,C.jsx)(ew,{...o,...t,ref:r})});eX.displayName="DropdownMenuSeparator",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,C.jsx)(eg,{...o,...t,ref:r})}).displayName="DropdownMenuArrow",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,C.jsx)(eR,{...o,...t,ref:r})}).displayName="DropdownMenuSubTrigger",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eT(n);return(0,C.jsx)(eM,{...o,...t,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eq=eA,eH=eL,ez=eO,eY=eG,eZ=eB,eJ=eU,eW=eV,eQ=eX},89196:(e,r,n)=>{n.d(r,{RG:()=>b,bL:()=>I,q7:()=>E});var t=n(12115),o=n(85185),a=n(82284),u=n(6101),l=n(46081),i=n(61285),s=n(63655),d=n(39033),c=n(5845),f=n(94315),p=n(95155),v="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[w,g,x]=(0,a.N)(h),[y,b]=(0,l.A)(h,[x]),[R,C]=y(h),M=t.forwardRef((e,r)=>(0,p.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(j,{...e,ref:r})})}));M.displayName=h;var j=t.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:l=!1,dir:i,currentTabStopId:h,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:x,onEntryFocus:y,preventScrollOnEntryFocus:b=!1,...C}=e,M=t.useRef(null),j=(0,u.s)(r,M),D=(0,f.jH)(i),[_=null,k]=(0,c.i)({prop:h,defaultProp:w,onChange:x}),[I,E]=t.useState(!1),T=(0,d.c)(y),F=g(n),N=t.useRef(!1),[A,S]=t.useState(0);return t.useEffect(()=>{let e=M.current;if(e)return e.addEventListener(v,T),()=>e.removeEventListener(v,T)},[T]),(0,p.jsx)(R,{scope:n,orientation:a,dir:D,loop:l,currentTabStopId:_,onItemFocus:t.useCallback(e=>k(e),[k]),onItemShiftTab:t.useCallback(()=>E(!0),[]),onFocusableItemAdd:t.useCallback(()=>S(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>S(e=>e-1),[]),children:(0,p.jsx)(s.sG.div,{tabIndex:I||0===A?-1:0,"data-orientation":a,...C,ref:j,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{N.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let r=!N.current;if(e.target===e.currentTarget&&r&&!I){let r=new CustomEvent(v,m);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=F().filter(e=>e.focusable);P([e.find(e=>e.active),e.find(e=>e.id===_),...e].filter(Boolean).map(e=>e.ref.current),b)}}N.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>E(!1))})})}),D="RovingFocusGroupItem",_=t.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:u=!1,tabStopId:l,...d}=e,c=(0,i.B)(),f=l||c,v=C(D,n),m=v.currentTabStopId===f,h=g(n),{onFocusableItemAdd:x,onFocusableItemRemove:y}=v;return t.useEffect(()=>{if(a)return x(),()=>y()},[a,x,y]),(0,p.jsx)(w.ItemSlot,{scope:n,id:f,focusable:a,active:u,children:(0,p.jsx)(s.sG.span,{tabIndex:m?0:-1,"data-orientation":v.orientation,...d,ref:r,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?v.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let r=function(e,r,n){var t;let o=(t=e.key,"rtl"!==n?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(o)))return k[o]}(e,v.orientation,v.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)n.reverse();else if("prev"===r||"next"===r){"prev"===r&&n.reverse();let t=n.indexOf(e.currentTarget);n=v.loop?function(e,r){return e.map((n,t)=>e[(r+t)%e.length])}(n,t+1):n.slice(t+1)}setTimeout(()=>P(n))}})})})});_.displayName=D;var k={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function P(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let t of e)if(t===n||(t.focus({preventScroll:r}),document.activeElement!==n))return}var I=M,E=_}}]);