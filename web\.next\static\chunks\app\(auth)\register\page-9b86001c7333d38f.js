(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2678],{6654:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"useMergedRef",{enumerable:!0,get:function(){return a}});let n=t(12115);function a(e,r){let t=(0,n.useRef)(null),a=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=t.current;e&&(t.current=null,e());let r=a.current;r&&(a.current=null,r())}else e&&(t.current=l(e,n)),r&&(a.current=l(r,n))},[e,r])}function l(e,r){if("function"!=typeof e)return e.current=r,()=>{e.current=null};{let t=e(r);return"function"==typeof t?t:()=>e(null)}}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),e.exports=r.default)},30285:(e,r,t)=>{"use strict";t.d(r,{$:()=>d,r:()=>i});var n=t(95155);t(12115);var a=t(99708),l=t(74466),s=t(59434);let i=(0,l.F)("inline-flex items-center cursor-pointer justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:r,variant:t,size:l,asChild:d=!1,...c}=e,o=d?a.DX:"button";return(0,n.jsx)(o,{"data-slot":"button",className:(0,s.cn)(i({variant:t,size:l,className:r})),...c})}},34477:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var t in r)Object.defineProperty(e,t,{enumerable:!0,get:r[t]})}(r,{callServer:function(){return n.callServer},createServerReference:function(){return l},findSourceMapURL:function(){return a.findSourceMapURL}});let n=t(53806),a=t(31818),l=t(34979).createServerReference},39088:(e,r,t)=>{Promise.resolve().then(t.bind(t,52237))},43453:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},52237:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>g});var n=t(95155),a=t(12115),l=t(6874),s=t.n(l);t(47650);var i=t(30285),d=t(66695),c=t(34477);c.callServer,c.findSourceMapURL;var o=t(43453),u=t(92138);let f=(0,t(19946).A)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]);function x(e){let{message:r}=e;return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8",children:(0,n.jsx)(d.Zp,{className:"w-full max-w-md bg-white dark:bg-gray-800 shadow-xl rounded-xl",children:(0,n.jsxs)(d.Wu,{className:"p-8 flex flex-col items-center text-center",children:[(0,n.jsx)("div",{className:"h-24 w-24 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center mb-6",children:(0,n.jsx)(o.A,{className:"h-12 w-12 text-green-600 dark:text-green-400"})}),(0,n.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"Registration Successful!"}),(0,n.jsx)("p",{className:"text-gray-800 dark:text-gray-300 mb-8",children:r}),(0,n.jsxs)("div",{className:"flex flex-col space-y-4 w-full",children:[(0,n.jsxs)(s(),{href:"/login",className:"inline-flex items-center justify-center h-12 px-6 w-full bg-gradient-to-r from-[#383D73] to-[#74546D] text-white font-semibold rounded-md transition-all duration-200 hover:scale-[1.02] hover:shadow-md",children:["Go to Login ",(0,n.jsx)(u.A,{className:"ml-2 h-4 w-4"})]}),(0,n.jsx)(s(),{href:"/",className:"inline-flex items-center justify-center h-12 px-6 w-full text-gray-700 dark:text-gray-300 font-medium border border-gray-300 dark:border-gray-700 rounded-md transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800",children:"Return to Home"})]})]})})})}function m(e){let{message:r,onRetry:t}=e;return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8",children:(0,n.jsx)(d.Zp,{className:"w-full max-w-md bg-white dark:bg-gray-800 shadow-xl rounded-xl",children:(0,n.jsxs)(d.Wu,{className:"p-8 flex flex-col items-center text-center",children:[(0,n.jsx)("div",{className:"h-24 w-24 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center mb-6",children:(0,n.jsx)(f,{className:"h-12 w-12 text-red-600 dark:text-red-400"})}),(0,n.jsx)("h2",{className:"text-2xl font-bold mb-2",children:"Registration Failed"}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mb-8",children:r}),(0,n.jsxs)("div",{className:"flex flex-col space-y-4 w-full",children:[(0,n.jsx)(i.$,{onClick:t,className:"h-12 w-full bg-gradient-to-r from-[#383D73] to-[#74546D] text-white font-semibold transition-all duration-200 hover:scale-[1.02] hover:shadow-md",children:"Try Again"}),(0,n.jsx)(s(),{href:"/",className:"inline-flex items-center justify-center h-12 px-6 w-full text-gray-700 dark:text-gray-300 font-medium border border-gray-300 dark:border-gray-700 rounded-md transition-all duration-200 hover:bg-gray-100 dark:hover:bg-gray-800",children:"Return to Home"})]})]})})})}function g(){let[e,r]=(0,a.useState)(null),[t,l]=(0,a.useState)({}),[s,i]=(0,a.useState)(!1);return s&&(null==e?void 0:e.success)?(0,n.jsx)(x,{message:e.message}):!s||(null==e?void 0:e.success)||(null==e?void 0:e.fieldErrors)?(0,n.jsx)(n.Fragment,{}):(0,n.jsx)(m,{message:(null==e?void 0:e.message)||"An error occurred during registration",onRetry:()=>{r(null),l({}),i(!1)}})}},59434:(e,r,t)=>{"use strict";t.d(r,{cn:()=>l,v:()=>s});var n=t(52596),a=t(39688);function l(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,n.$)(r))}function s(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}},66695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>i,Zp:()=>l,aR:()=>s,wL:()=>o});var n=t(95155);t(12115);var a=t(59434);function l(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",r),...t})}function s(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("flex flex-col gap-1.5 px-6",r),...t})}function i(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",r),...t})}function d(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",r),...t})}function c(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",r),...t})}function o(e){let{className:r,...t}=e;return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6",r),...t})}},74466:(e,r,t)=>{"use strict";t.d(r,{F:()=>s});var n=t(52596);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,l=n.$,s=(e,r)=>t=>{var n;if((null==r?void 0:r.variants)==null)return l(e,null==t?void 0:t.class,null==t?void 0:t.className);let{variants:s,defaultVariants:i}=r,d=Object.keys(s).map(e=>{let r=null==t?void 0:t[e],n=null==i?void 0:i[e];if(null===r)return null;let l=a(r)||a(n);return s[e][l]}),c=t&&Object.entries(t).reduce((e,r)=>{let[t,n]=r;return void 0===n||(e[t]=n),e},{});return l(e,d,null==r?void 0:null===(n=r.compoundVariants)||void 0===n?void 0:n.reduce((e,r)=>{let{class:t,className:n,...a}=r;return Object.entries(a).every(e=>{let[r,t]=e;return Array.isArray(t)?t.includes({...i,...c}[r]):({...i,...c})[r]===t})?[...e,t,n]:e},[]),null==t?void 0:t.class,null==t?void 0:t.className)}},92138:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});let n=(0,t(19946).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])}},e=>{var r=r=>e(e.s=r);e.O(0,[4201,6874,8441,1684,7358],()=>r(39088)),_N_E=e.O()}]);