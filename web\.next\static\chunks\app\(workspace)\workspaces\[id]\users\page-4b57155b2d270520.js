(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1717],{7524:(e,t,a)=>{"use strict";a.d(t,{LB:()=>c,TK:()=>i,hG:()=>l,hU:()=>s,lo:()=>o,mP:()=>u,nu:()=>n,s2:()=>d});let r="http://localhost:4000";async function s(){try{let e=localStorage.getItem("access_token");if(!e)throw console.error("No access token available"),Error("No access token available");let t=await fetch("".concat(r,"/api/users"),{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error("Failed to fetch users");return await t.json()}catch(e){throw console.error("Error fetching users:",e),e}}let o=async()=>{let e=localStorage.getItem("access_token");if(!e)throw Error("No access token available");let t=await fetch("".concat(r,"/api/users"),{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch users");return t.json()};async function n(e){try{if(!localStorage.getItem("access_token"))throw console.error("No access token available"),Error("No access token available");let t=await fetch("".concat(r,"/api/users/register"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.text();throw Error(e||"Failed to add user")}return}catch(e){throw console.error("Error adding user:",e),e}}async function i(e,t){try{let a=localStorage.getItem("access_token");if(!a)throw console.error("No access token available"),Error("No access token available");let s=await fetch("".concat(r,"/api/users/").concat(e),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)},body:JSON.stringify(t)});if(!s.ok)throw Error("Failed to update user");return await s.json()}catch(e){throw console.error("Error updating user:",e),e}}async function l(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");if(!(await fetch("".concat(r,"/api/users/").concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(t)}})).ok)throw Error("Failed to delete user")}catch(e){throw console.error("Error deleting user:",e),e}}async function c(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");let a=await fetch("".concat(r,"/api/users/approve/").concat(e),{method:"PATCH",headers:{Authorization:"Bearer ".concat(t)}});if(!a.ok)throw Error("Failed to approve user");return await a.json()}catch(e){throw console.error("Error approving user:",e),e}}async function d(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");let a=await fetch("".concat(r,"/api/users/").concat(e),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify({isApproved:!1})});if(!a.ok)throw Error("Failed to revoke user access");return await a.json()}catch(e){throw console.error("Error revoking user access:",e),e}}async function u(){try{let e=localStorage.getItem("access_token");if(!e)throw console.error("No access token available"),Error("No access token available");let t=await fetch("".concat(r,"/api/users/me/credits"),{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error("Failed to fetch user credits");let a=await t.json();return{credits:a.credits||0,minutes:a.minutes||0,freeCreditsRemaining:a.freeCreditsRemaining||0,paidCredits:a.paidCredits||0,totalAvailable:a.totalAvailable||0,usingFreeCredits:a.usingFreeCredits||!1,freeMinutesRemaining:a.freeMinutesRemaining||0,paidMinutes:a.paidMinutes||0,totalMinutesAvailable:a.totalMinutesAvailable||0,callPricePerMinute:a.callPricePerMinute||.1,monthlyResetDate:a.monthlyResetDate||1,monthlyAllowance:a.monthlyAllowance||0,minimumCreditsThreshold:a.minimumCreditsThreshold||1}}catch(e){return console.error("Error fetching user credits:",e),{credits:0,minutes:0,freeCreditsRemaining:0,paidCredits:0,totalAvailable:0,usingFreeCredits:!1,freeMinutesRemaining:0,paidMinutes:0,totalMinutesAvailable:0,callPricePerMinute:.1,monthlyResetDate:1,monthlyAllowance:0,minimumCreditsThreshold:1}}}},26126:(e,t,a)=>{"use strict";a.d(t,{E:()=>l});var r=a(95155);a(12115);var s=a(99708),o=a(74466),n=a(59434);let i=(0,o.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:a,asChild:o=!1,...l}=e,c=o?s.DX:"span";return(0,r.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(i({variant:a}),t),...l})}},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>l,r:()=>i});var r=a(95155);a(12115);var s=a(99708),o=a(74466),n=a(59434);let i=(0,o.F)("inline-flex items-center cursor-pointer justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:o,asChild:l=!1,...c}=e,d=l?s.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,n.cn)(i({variant:a,size:o,className:t})),...c})}},35169:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35695:(e,t,a)=>{"use strict";var r=a(18999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"usePathname")&&a.d(t,{usePathname:function(){return r.usePathname}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}})},40026:(e,t,a)=>{"use strict";a.d(t,{H:()=>r,e:()=>s});let r="http://localhost:4000",s="pk_test_51ROz1YRpJ0zLf0aTbgbDkpShvfpNxdZPet1QXClapTckA7Cy0tsaxY2qY1dp8oSBGOFqnh0vugjd8mDluFWgKpRL00bACyumT8"},40968:(e,t,a)=>{"use strict";a.d(t,{b:()=>i});var r=a(12115),s=a(63655),o=a(95155),n=r.forwardRef((e,t)=>(0,o.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null===(a=e.onMouseDown)||void 0===a||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var i=n},50507:(e,t,a)=>{Promise.resolve().then(a.bind(a,62652))},51154:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>h,Es:()=>g,HM:()=>d,L3:()=>f,c7:()=>m,lG:()=>i,rr:()=>p,zM:()=>l});var r=a(95155);a(12115);var s=a(15452),o=a(54416),n=a(59434);function i(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"dialog",...t})}function l(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"dialog-trigger",...t})}function c(e){let{...t}=e;return(0,r.jsx)(s.ZL,{"data-slot":"dialog-portal",...t})}function d(e){let{...t}=e;return(0,r.jsx)(s.bm,{"data-slot":"dialog-close",...t})}function u(e){let{className:t,...a}=e;return(0,r.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-650 bg-black/50",t),...a})}function h(e){let{className:t,children:a,...i}=e;return(0,r.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,r.jsx)(u,{}),(0,r.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-650 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...i,children:[a,(0,r.jsxs)(s.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(o.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function g(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function f(e){let{className:t,...a}=e;return(0,r.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",t),...a})}function p(e){let{className:t,...a}=e;return(0,r.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}},59409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>m,gC:()=>h,l6:()=>c,yv:()=>d});var r=a(95155);a(12115);var s=a(31992),o=a(66474),n=a(5196),i=a(47863),l=a(59434);function c(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,r.jsx)(s.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,children:a,...n}=e;return(0,r.jsxs)(s.l9,{"data-slot":"select-trigger",className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n,children:[a,(0,r.jsx)(s.In,{asChild:!0,children:(0,r.jsx)(o.A,{className:"size-4 opacity-50"})})]})}function h(e){let{className:t,children:a,position:o="popper",...n}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsxs)(s.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md","popper"===o&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:o,...n,children:[(0,r.jsx)(g,{}),(0,r.jsx)(s.LM,{className:(0,l.cn)("p-1","popper"===o&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,r.jsx)(f,{})]})})}function m(e){let{className:t,children:a,...o}=e;return(0,r.jsxs)(s.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...o,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(n.A,{className:"size-4"})})}),(0,r.jsx)(s.p4,{children:a})]})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(i.A,{className:"size-4"})})}function f(e){let{className:t,...a}=e;return(0,r.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(o.A,{className:"size-4"})})}},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>o,v:()=>n});var r=a(52596),s=a(39688);function o(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}function n(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>o});var r=a(95155);a(12115);var s=a(59434);function o(e){let{className:t,type:a,...o}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...o})}},62525:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},62652:(e,t,a)=>{"use strict";a.d(t,{default:()=>N});var r=a(95155),s=a(12115),o=a(35695),n=a(66695),i=a(85127),l=a(54165),c=a(59409),d=a(30285),u=a(62523),h=a(85057),m=a(26126),g=a(51154),f=a(35169),p=a(19946);let x=(0,p.A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),v=(0,p.A)("ShieldOff",[["path",{d:"m2 2 20 20",key:"1ooewy"}],["path",{d:"M5 5a1 1 0 0 0-1 1v7c0 5 3.5 7.5 7.67 8.94a1 1 0 0 0 .67.01c2.35-.82 4.48-1.97 5.9-3.71",key:"1jlk70"}],["path",{d:"M9.309 3.652A12.252 12.252 0 0 0 11.24 2.28a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1v7a9.784 9.784 0 0 1-.08 1.264",key:"18rp1v"}]]),b=(0,p.A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var j=a(62525),y=a(56671),w=a(99672),k=a(7524);function N(e){let{organizationId:t}=e,[a,p]=(0,s.useState)(null),[N,z]=(0,s.useState)([]),[A,C]=(0,s.useState)(!0),[E,S]=(0,s.useState)(!1),[_,F]=(0,s.useState)(!1),[T,P]=(0,s.useState)(""),[M,R]=(0,s.useState)(!1),[B,L]=(0,s.useState)(null),[O,H]=(0,s.useState)(!1),[I,U]=(0,s.useState)(""),D=(0,o.useRouter)();(0,s.useEffect)(()=>{J()},[t]);let J=async()=>{try{C(!0);let[e,a]=await Promise.all([(0,w.SA)(t),(0,k.lo)()]);p(e),z(a)}catch(e){console.error("Error fetching data:",e),y.o.error("Failed to fetch workspace data")}finally{C(!1)}},V=async()=>{if(T)try{H(!0);let e=await (0,w.VO)(t,T,M);p(e),S(!1),P(""),R(!1),y.o.success("User added to workspace successfully")}catch(e){console.error("Error adding user to Workspace:",e),y.o.error("Failed to add user to workspace")}finally{H(!1)}},$=async()=>{if(B)try{H(!0);let e=await (0,w.J)(t,B);p(e),F(!1),L(null),y.o.success("User removed from workspace successfully")}catch(e){console.error("Error removing user from Workspace:",e),y.o.error("Failed to remove user from workspace")}finally{H(!1)}},G=async(e,a)=>{try{H(!0);let r=await (0,w.VO)(t,e,a);p(r),y.o.success("User ".concat(a?"promoted to admin":"demoted from admin"," successfully"))}catch(e){console.error("Error updating user role:",e),y.o.error("Failed to update user role")}finally{H(!1)}},q=e=>N.find(t=>t._id===e);if(A)return(0,r.jsx)("div",{className:"container mx-auto py-6 flex justify-center items-center min-h-[60vh]",children:(0,r.jsx)(g.A,{className:"h-8 w-8 animate-spin"})});if(!a)return(0,r.jsx)("div",{className:"container mx-auto py-6",children:(0,r.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Workspace not found."})});let W=(()=>{if(!a)return[];let e=[...a.users||[],...a.adminUsers||[]];return N.filter(t=>!e.includes(t._id)&&(""===I||t.fullName.toLowerCase().includes(I.toLowerCase())||t.email.toLowerCase().includes(I.toLowerCase())))})(),Z=[...(a.adminUsers||[]).map(e=>({id:e,isAdmin:!0})),...(a.users||[]).map(e=>({id:e,isAdmin:!1}))];return(0,r.jsxs)("div",{className:"container mx-auto py-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-6",children:[(0,r.jsxs)(d.$,{variant:"outline",onClick:()=>D.push("/workspaces"),className:"mr-4",children:[(0,r.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,r.jsxs)("h1",{className:"text-3xl font-bold",children:[a.name," - Users"]})]}),(0,r.jsx)("div",{className:"flex justify-end mb-6",children:(0,r.jsxs)(l.lG,{open:E,onOpenChange:S,children:[(0,r.jsx)(l.zM,{asChild:!0,children:(0,r.jsxs)(d.$,{children:[(0,r.jsx)(x,{className:"mr-2 h-4 w-4"}),"Add User"]})}),(0,r.jsxs)(l.Cf,{children:[(0,r.jsxs)(l.c7,{children:[(0,r.jsx)(l.L3,{children:"Add User to Organization"}),(0,r.jsx)(l.rr,{children:"Select a user to add to this organization."})]}),(0,r.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(h.J,{htmlFor:"search",className:"text-right",children:"Search"}),(0,r.jsx)(u.p,{id:"search",placeholder:"Search by name or email",value:I,onChange:e=>U(e.target.value),className:"col-span-3"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(h.J,{htmlFor:"user",className:"text-right",children:"User"}),(0,r.jsxs)(c.l6,{value:T,onValueChange:P,children:[(0,r.jsx)(c.bq,{className:"col-span-3",children:(0,r.jsx)(c.yv,{placeholder:"Select a user"})}),(0,r.jsx)(c.gC,{className:"z-[700]",children:0===W.length?(0,r.jsx)(c.eb,{value:"none",disabled:!0,children:"No available users"}):W.map(e=>(0,r.jsxs)(c.eb,{value:e._id,children:[e.fullName," (",e.email,")"]},e._id))})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,r.jsx)(h.J,{htmlFor:"role",className:"text-right",children:"Role"}),(0,r.jsxs)(c.l6,{value:M?"admin":"user",onValueChange:e=>R("admin"===e),children:[(0,r.jsx)(c.bq,{className:"col-span-3",children:(0,r.jsx)(c.yv,{placeholder:"Select role"})}),(0,r.jsxs)(c.gC,{className:"z-[700]",children:[(0,r.jsx)(c.eb,{value:"user",children:"Regular User"}),(0,r.jsx)(c.eb,{value:"admin",children:"Admin"})]})]})]})]}),(0,r.jsxs)(l.Es,{children:[(0,r.jsx)(d.$,{variant:"outline",onClick:()=>S(!1),children:"Cancel"}),(0,r.jsx)(d.$,{onClick:V,disabled:O||!T,children:O?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Adding..."]}):"Add User"})]})]})]})}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsx)(n.ZB,{children:"Organization Users"}),(0,r.jsx)(n.BT,{children:"Manage users in this organization."})]}),(0,r.jsx)(n.Wu,{children:0===Z.length?(0,r.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No users in this organization. Add users to get started."}):(0,r.jsxs)(i.XI,{children:[(0,r.jsx)(i.A0,{children:(0,r.jsxs)(i.Hj,{children:[(0,r.jsx)(i.nd,{children:"Name"}),(0,r.jsx)(i.nd,{children:"Email"}),(0,r.jsx)(i.nd,{children:"Role"}),(0,r.jsx)(i.nd,{className:"text-right",children:"Actions"})]})}),(0,r.jsx)(i.BF,{children:Z.map(e=>{let{id:t,isAdmin:a}=e,s=q(t);return s?(0,r.jsxs)(i.Hj,{children:[(0,r.jsx)(i.nA,{className:"font-medium",children:s.fullName}),(0,r.jsx)(i.nA,{children:s.email}),(0,r.jsx)(i.nA,{children:a?(0,r.jsx)(m.E,{className:"bg-blue-500",children:"Admin"}):(0,r.jsx)(m.E,{children:"User"})}),(0,r.jsx)(i.nA,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex justify-end gap-2",children:[a?(0,r.jsx)(d.$,{variant:"outline",size:"icon",title:"Demote to regular user",onClick:()=>G(t,!1),disabled:O,children:(0,r.jsx)(v,{className:"h-4 w-4"})}):(0,r.jsx)(d.$,{variant:"outline",size:"icon",title:"Promote to admin",onClick:()=>G(t,!0),disabled:O,children:(0,r.jsx)(b,{className:"h-4 w-4"})}),(0,r.jsx)(d.$,{variant:"outline",size:"icon",className:"text-red-500",title:"Remove from organization",onClick:()=>{L(t),F(!0)},disabled:O,children:(0,r.jsx)(j.A,{className:"h-4 w-4"})})]})})]},t):null})})]})})]}),(0,r.jsx)(l.lG,{open:_,onOpenChange:F,children:(0,r.jsxs)(l.Cf,{children:[(0,r.jsxs)(l.c7,{children:[(0,r.jsx)(l.L3,{children:"Remove User"}),(0,r.jsx)(l.rr,{children:"Are you sure you want to remove this user from the organization? They will lose access to all organization resources."})]}),(0,r.jsxs)(l.Es,{children:[(0,r.jsx)(d.$,{variant:"outline",onClick:()=>F(!1),children:"Cancel"}),(0,r.jsx)(d.$,{variant:"destructive",onClick:$,disabled:O,children:O?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Removing..."]}):"Remove"})]})]})})]})}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>i,Zp:()=>o,aR:()=>n,wL:()=>d});var r=a(95155);a(12115);var s=a(59434);function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",t),...a})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("flex flex-col gap-1.5 px-6",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6",t),...a})}},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>n});var r=a(95155);a(12115);var s=a(40968),o=a(59434);function n(e){let{className:t,...a}=e;return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,o.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},85127:(e,t,a)=>{"use strict";a.d(t,{A0:()=>n,BF:()=>i,Hj:()=>l,XI:()=>o,nA:()=>d,nd:()=>c});var r=a(95155);a(12115);var s=a(59434);function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",t),...a})})}function n(e){let{className:t,...a}=e;return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-muted-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}},99672:(e,t,a)=>{"use strict";a.d(t,{Dp:()=>c,EC:()=>n,J:()=>u,L_:()=>i,SA:()=>o,VO:()=>d,co:()=>l,h6:()=>s});var r=a(40026);let s=async()=>{let e=await fetch("".concat(r.H,"/api/organizations"),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch organizations");return e.json()},o=async e=>{let t=await fetch("".concat(r.H,"/api/organizations/").concat(e),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch organization");return t.json()},n=async e=>{let t=await fetch("".concat(r.H,"/api/organizations"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Failed to create organization");return t.json()},i=async(e,t)=>{let a=await fetch("".concat(r.H,"/api/organizations/").concat(e),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify(t)});if(!a.ok)throw Error((await a.json()).message||"Failed to update organization");return a.json()},l=async(e,t)=>{let a=await fetch("".concat(r.H,"/api/organizations/").concat(e,"/billing"),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify(t)});if(!a.ok)throw Error((await a.json()).message||"Failed to update organization billing");return a.json()},c=async e=>{let t=await fetch("".concat(r.H,"/api/organizations/").concat(e),{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!t.ok)throw Error((await t.json()).message||"Failed to delete organization");return t.json()},d=async(e,t,a)=>{let s=await fetch("".concat(r.H,"/api/organizations/").concat(e,"/users/").concat(t),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify({isAdmin:a})});if(!s.ok)throw Error((await s.json()).message||"Failed to add user to organization");return s.json()},u=async(e,t)=>{let a=await fetch("".concat(r.H,"/api/organizations/").concat(e,"/users/").concat(t),{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!a.ok)throw Error((await a.json()).message||"Failed to remove user from organization");return a.json()}}},e=>{var t=t=>e(e.s=t);e.O(0,[4201,4341,6403,1071,6671,6544,8441,1684,7358],()=>t(50507)),_N_E=e.O()}]);