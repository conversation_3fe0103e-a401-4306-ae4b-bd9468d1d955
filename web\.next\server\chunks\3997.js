exports.id=3997,exports.ids=[3997],exports.modules={10069:(e,s,t)=>{"use strict";t.d(s,{default:()=>$});var a=t(60687),r=t(43210),i=t(89667),l=t(85763),n=t(29523),d=t(28559),c=t(99270),o=t(98492),m=t(81904),x=t(88233),u=t(13943),h=t(62688);let f=(0,h.A)("Pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]);var v=t(97840);let j=(0,h.A)("FastForward",[["polygon",{points:"13 19 22 12 13 5 13 19",key:"587y9g"}],["polygon",{points:"2 19 11 12 2 5 2 19",key:"3pweh0"}]]);var g=t(31158),N=t(93613),p=t(96834),A=t(32584),b=t(75256),w=t(98585),y=t(79857),C=t(16189),P=t(76104),k=t(21342),S=t(93500);t(6607);var T=function(e){return e.Positive="Positive",e.Neutral="Neutral",e.SlightlyPositive="Slightly Positive",e.SlightlyNegative="Slightly Negative",e.Negative="Negative",e}(T||{});let E="http://localhost:4000";async function R(e){try{let s=localStorage.getItem("access_token");if(!s)return console.error("No access token available"),!1;let t=await fetch(`${E}/api/history/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${s}`}});if(!t.ok)throw Error(`Failed to delete call: ${t.status}`);return!0}catch(e){return console.error("Error deleting call:",e),!1}}function D(e){let s=Math.floor(e/60),t=Math.floor(e%60);return`${s.toString().padStart(2,"0")}:${t.toString().padStart(2,"0")}`}function B(e){if(!e)return"N/A";let s=new Date(e);return isNaN(s.getTime())?"N/A":s.toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric"})}let F={Positive:{emoji:"\uD83D\uDE0A",color:"bg-green-500"},Neutral:{emoji:"\uD83D\uDE10",color:"bg-blue-500"},"Slightly Positive":{emoji:"\uD83D\uDE42",color:"bg-cyan-500"},"Slightly Negative":{emoji:"\uD83D\uDE15",color:"bg-slate-500"},Negative:{emoji:"\uD83D\uDE1F",color:"bg-red-500"}},I=e=>F[e]||F.Neutral;function $({contactName:e}){let s=(0,C.useParams)(),t=e||s?.fullName,h=(0,C.useRouter)(),T=t?decodeURIComponent(t):"",[F,$]=(0,r.useState)(""),{credits:L,organizationCreditThreshold:U}=(0,b.I)(),[M,z]=(0,r.useState)([]),[Q,_]=(0,r.useState)([]),[V,O]=(0,r.useState)(null),[G,H]=(0,r.useState)(0),[q,W]=(0,r.useState)("overview"),[X,K]=(0,r.useState)(T||""),[Z,Y]=(0,r.useState)(0),[J,ee]=(0,r.useState)(null),[es,et]=(0,r.useState)(!0),[ea,er]=(0,r.useState)(null),ei=(0,r.useRef)(null),[el,en]=(0,r.useState)(!1),[ed,ec]=(0,r.useState)("all"),[eo,em]=(0,r.useState)(1),[ex,eu]=(0,r.useState)(!0),[eh,ef]=(0,r.useState)(!1),ev=(0,r.useRef)(null),[ej,eg]=(0,r.useState)(!1),[eN,ep]=(0,r.useState)(null),[eA,eb]=(0,r.useState)(!1),[ew,ey]=(0,r.useState)(!0);async function eC(e=1,s=!1){try{let t=localStorage.getItem("access_token");if(!t)return console.error("No access token available"),[];s||et(!0),s&&ef(!0);let a=new URLSearchParams;a.append("page",e.toString()),a.append("limit","20"),X.trim()&&(a.append("search",X.trim()),a.append("filterType",ed));let r=await fetch(`${E}/api/history?${a.toString()}`,{headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`}});if(!r.ok)throw Error(`Failed to fetch calls: ${r.status}`);let i=await r.json();if(!Array.isArray(i))throw console.error("Unexpected response format from API:",i),Error("Invalid response format from API");return s?z(e=>{let s=new Set(e.map(e=>e._id)),t=i.filter(e=>!s.has(e._id));return[...e,...t]}):z(i),eu(20===i.length),i}catch(e){return console.error("Error fetching calls:",e),[]}finally{s||et(!1),s&&ef(!1)}}let[eP,ek]=(0,r.useState)(!1),eS=async()=>{if(eN)try{if(await R(eN._id)){let e=await eC();z(e),eg(!1),ep(null),V?._id===eN._id&&O(null)}}catch(e){console.error("Error in delete handler:",e)}},eT=e=>parseInt(e.callDuration)/1e3,eE=e=>"voicemail"===e.callEndReason||"customer-did-not-answer"===e.callEndReason||"customer-busy"===e.callEndReason||"customer-out-of-reach"===e.callEndReason;[...M].sort((e,s)=>{let t=new Date(e.callStartTime).getTime();return new Date(s.callStartTime).getTime()-t});let eR=t?`Calls for ${T}`:"Recent Calls";function eD(e){if(!e)return null;let s=e.toLowerCase().trim(),t=Q.find(e=>e.name.toLowerCase().trim()===s||s.includes(e.name.toLowerCase().trim())||e.name.toLowerCase().trim().includes(s));return t&&t.avatar?t.avatar:P.A.src}return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(y.O,{credits:L,threshold:U}),(0,a.jsx)(w.m,{credits:L,threshold:U}),(0,a.jsxs)("div",{className:"flex h-[calc(100vh-60px)] w-full overflow-hidden overflow-x-auto bg-background",children:[(0,a.jsxs)("div",{className:"w-80 border-r border-border flex flex-col h-full dark:bg-card",children:[(0,a.jsxs)("div",{className:"p-4 border-b border-border",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[t&&(0,a.jsx)(n.$,{variant:"ghost",size:"icon",onClick:()=>h.back(),className:"mr-1",children:(0,a.jsx)(d.A,{className:"h-4 w-4"})}),(0,a.jsx)("h2",{className:"text-lg font-semibold",children:eR})]}),M.length>0&&!es?(0,a.jsx)(p.E,{className:"rounded-full bg-primary/10 hover:bg-primary/10 text-primary",children:X.trim()&&M[0].filteredCalls?`${M.length} of ${M[0].filteredCalls} results`:`${M.length} of ${M[0].totalCalls}`}):null]}),(0,a.jsxs)("div",{className:"relative mt-3 flex gap-2",children:[(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),em(1),eC(1)},className:"relative flex-1",children:[(0,a.jsx)(c.A,{className:"absolute left-2.5 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(i.p,{placeholder:"agent"===ed?"Filter by agent...":"Filter by name...",className:"pl-9 h-9 bg-background dark:bg-muted",value:X,onChange:e=>K(e.target.value)})]}),(0,a.jsxs)(k.rI,{children:[(0,a.jsx)(k.ty,{asChild:!0,children:(0,a.jsx)(n.$,{variant:"ghost",size:"icon",className:"h-9 w-9",children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(k.SQ,{align:"end",children:[(0,a.jsx)(k._2,{className:"all"===ed?"bg-muted":"",onClick:()=>ec("all"),children:"All Fields"}),(0,a.jsx)(k._2,{className:"name"===ed?"bg-muted":"",onClick:()=>ec("name"),children:"Filter by Name"}),(0,a.jsx)(k._2,{className:"agent"===ed?"bg-muted":"",onClick:()=>ec("agent"),children:"Filter by Agent"})]})]})]})]}),(0,a.jsxs)("div",{className:"overflow-y-auto flex-1",children:[es?(0,a.jsx)("div",{className:"flex justify-center items-center h-24",children:(0,a.jsx)("div",{className:"animate-spin h-5 w-5 border-2 border-primary border-t-transparent rounded-full"})}):M.map((e,s)=>(0,a.jsx)("div",{className:`w-full text-left px-4 py-3 border-b border-border hover:bg-muted/50 cursor-pointer transition-colors ${V?._id===e._id?"bg-muted/50":""}`,onClick:()=>O(e),children:(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(A.eu,{className:"h-8 w-8",children:eD(e.agent)?(0,a.jsx)("div",{className:"relative h-full w-full rounded-full overflow-hidden",children:(0,a.jsx)(A.BK,{src:eD(e.agent),alt:"Agent",className:"object-cover"})}):(0,a.jsx)(A.q5,{className:"bg-muted text-muted-foreground",children:(0,a.jsxs)("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 122.88 119.35",children:[(0,a.jsx)("title",{children:"chatbot"}),(0,a.jsx)("path",{d:"M57.49,29.2V23.53a14.41,14.41,0,0,1-2-.93A12.18,12.18,0,0,1,50.44,7.5a12.39,12.39,0,0,1,2.64-3.95A12.21,12.21,0,0,1,57,.92,12,12,0,0,1,61.66,0,12.14,12.14,0,0,1,72.88,7.5a12.14,12.14,0,0,1,0,9.27,12.08,12.08,0,0,1-2.64,3.94l-.06.06a12.74,12.74,0,0,1-2.36,1.83,11.26,11.26,0,0,1-2,.93V29.2H94.3a15.47,15.47,0,0,1,15.42,15.43v2.29H115a7.93,7.93,0,0,1,7.9,7.91V73.2A7.93,7.93,0,0,1,115,81.11h-5.25v2.07A15.48,15.48,0,0,1,94.3,98.61H55.23L31.81,118.72a2.58,2.58,0,0,1-3.65-.29,2.63,2.63,0,0,1-.63-1.85l1.25-18h-.21A15.45,15.45,0,0,1,13.16,83.18V81.11H7.91A7.93,7.93,0,0,1,0,73.2V54.83a7.93,7.93,0,0,1,7.9-7.91h5.26v-2.3A15.45,15.45,0,0,1,28.57,29.2H57.49ZM82.74,47.32a9.36,9.36,0,1,1-9.36,9.36,9.36,9.36,0,0,1,9.36-9.36Zm-42.58,0a9.36,9.36,0,1,1-9.36,9.36,9.36,9.36,0,0,1,9.36-9.36Zm6.38,31.36a2.28,2.28,0,0,1-.38-.38,2.18,2.18,0,0,1-.52-1.36,2.21,2.21,0,0,1,.46-1.39,2.4,2.4,0,0,1,.39-.39,3.22,3.22,0,0,1,3.88-.08A22.36,22.36,0,0,0,56,78.32a14.86,14.86,0,0,0,5.47,1A16.18,16.18,0,0,0,67,78.22,25.39,25.39,0,0,0,72.75,75a3.24,3.24,0,0,1,3.89.18,3,3,0,0,1,.37.41,2.22,2.22,0,0,1,.42,1.4,2.33,2.33,0,0,1-.58,1.35,2.29,2.29,0,0,1-.43.38,30.59,30.59,0,0,1-7.33,4,22.28,22.28,0,0,1-7.53,1.43A21.22,21.22,0,0,1,54,82.87a27.78,27.78,0,0,1-7.41-4.16l0,0ZM94.29,34.4H28.57A10.26,10.26,0,0,0,18.35,44.63V83.18A10.26,10.26,0,0,0,28.57,93.41h3.17a2.61,2.61,0,0,1,2.41,2.77l-1,14.58L52.45,94.15a2.56,2.56,0,0,1,1.83-.75h40a10.26,10.26,0,0,0,10.22-10.23V44.62A10.24,10.24,0,0,0,94.29,34.4Z"})]})})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("p",{className:"text-sm font-medium truncate",children:e.fullName||"Unknown Agent"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:function(e){if(!e)return"N/A";let s=new Date(e);return isNaN(s.getTime())?"N/A":s.toLocaleDateString("en-US",{month:"short",day:"numeric"})}(e.callStartTime)}),"superadmin"===F&&(0,a.jsxs)(k.rI,{children:[(0,a.jsx)(k.ty,{asChild:!0,children:(0,a.jsx)(n.$,{variant:"ghost",size:"icon",className:"h-6 w-6 hover:bg-muted",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})})}),(0,a.jsx)(k.SQ,{align:"end",children:(0,a.jsxs)(k._2,{className:"text-red-600",onClick:s=>{s.stopPropagation(),ep(e),eg(!0)},children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Delete"]})})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-muted-foreground mt-0.5",children:[(0,a.jsx)("div",{className:"flex items-center",children:eE(e)?(0,a.jsx)(p.E,{className:"bg-red-500 text-white hover:bg-red-600 px-1 py-0 text-[10px]",children:"didn't pick"}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{className:"h-3 w-3 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),D(eT(e))]})}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("svg",{className:"h-3 w-3 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),new Date(e.callStartTime).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]})]})]})]})},`${e._id}-${s}`)),(0,a.jsx)("div",{ref:ev,className:"py-4 text-center",children:eh?(0,a.jsx)("div",{className:"flex justify-center items-center py-4",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary"})}):ex?(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Scroll for more"}):(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"No more calls"})}),ea&&(0,a.jsx)("div",{className:"p-4 text-sm text-amber-600",children:ea})]})]}),(0,a.jsx)("div",{className:"flex-1 min-w-[500px] flex flex-col overflow-hidden",children:V?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"p-4 border-b border-border",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(A.eu,{className:"h-10 w-10 mr-3",children:V.agent?(0,a.jsx)("div",{className:"relative h-full w-full rounded-full overflow-hidden",children:(0,a.jsx)(A.BK,{src:eD(V.agent),alt:V.agent||"Agent",className:"object-cover"})}):(0,a.jsx)(A.q5,{className:"bg-primary/10 text-primary",children:V.agent?.charAt(0)||"A"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold",children:V.fullName||"Caller"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Agent: ",V.agent||""]})]})]}),eE(V)?(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(p.E,{className:"bg-red-500 text-white px-2 py-1",children:"didn't pick"})}):(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n.$,{variant:"ghost",size:"icon",className:"h-9 w-9",onClick:()=>{ei.current&&(ei.current.currentTime=0,Y(0),el&&ei.current.play())},children:(0,a.jsx)(u.A,{size:16})}),(0,a.jsx)(n.$,{variant:"ghost",size:"icon",className:"h-9 w-9",onClick:()=>{V?.recordingUrl&&(ei.current||(ei.current=new Audio(V.recordingUrl),ei.current.addEventListener("timeupdate",()=>{if(ei.current){let e=eT(V);Y(ei.current.currentTime/e*100)}}),ei.current.addEventListener("ended",()=>{en(!1),Y(0)})),el?(ei.current.pause(),en(!1)):(ei.current.play(),en(!0)))},children:el?(0,a.jsx)(f,{size:16}):(0,a.jsx)(v.A,{size:16})}),(0,a.jsx)(n.$,{variant:"ghost",size:"icon",className:"h-9 w-9",onClick:()=>{ei.current&&(ei.current.currentTime=Math.min(ei.current.currentTime+10,eT(V)))},children:(0,a.jsx)(j,{size:16})}),(0,a.jsx)(n.$,{variant:"ghost",size:"icon",className:"h-9 w-9",onClick:()=>{V?.recordingUrl&&window.open(V.recordingUrl,"_blank")},children:(0,a.jsx)(g.A,{size:16})})]})]}),!eE(V)&&(0,a.jsxs)("div",{className:"mt-6 flex items-center",children:[(0,a.jsxs)("div",{className:"relative w-full h-12 bg-muted rounded-md overflow-hidden cursor-pointer",onClick:e=>{if(!ei.current||!V)return;let s=e.currentTarget.getBoundingClientRect(),t=(e.clientX-s.left)/s.width,a=eT(V);ei.current.currentTime=a*t,Y(100*t),!el&&ei.current.paused&&(ei.current.play(),en(!0))},children:[(0,a.jsx)("div",{className:"absolute top-0 left-0 h-full bg-primary/20 pointer-events-none",style:{width:`${Z}%`}}),(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-between px-1 pointer-events-none",children:Array.from({length:80}).map((e,s)=>{let t=s*V._id.charCodeAt(s%V._id.length),r=30+25*Math.sin(.1*t)+20*Math.cos(.2*t);return(0,a.jsx)("div",{className:s<.8*Z?"bg-primary/50":"bg-muted-foreground/20",style:{height:`${Math.max(15,Math.min(85,r))}%`,width:"1px"}},s)})}),(0,a.jsx)("div",{className:"absolute top-0 w-0.5 h-full bg-primary z-10 pointer-events-none",style:{left:`${Z}%`}})]}),(0,a.jsx)("div",{className:"ml-2 text-sm text-foreground whitespace-nowrap",children:`${D(eT(V)*Z/100)} / ${D(eT(V))}`})]})]}),(0,a.jsxs)(l.tU,{value:q,onValueChange:W,className:"flex-1 overflow-hidden",children:[(0,a.jsx)("div",{className:"border-b border-border",children:(0,a.jsxs)(l.j7,{className:"h-12 bg-transparent border-b-0 px-4 gap-6",children:[(0,a.jsx)(l.Xi,{value:"overview",className:"data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none h-12 rounded-none px-0 text-muted-foreground data-[state=active]:text-foreground",children:"Overview"}),!eE(V)&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l.Xi,{value:"summary",className:"data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none h-12 rounded-none px-0 text-muted-foreground data-[state=active]:text-foreground",children:"Summary"}),(0,a.jsx)(l.Xi,{value:"transcript",className:"data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none h-12 rounded-none px-0 text-muted-foreground data-[state=active]:text-foreground",children:"Transcript"}),(0,a.jsx)(l.Xi,{value:"additionalInfo",className:"data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none h-12 rounded-none px-0 text-muted-foreground data-[state=active]:text-foreground",children:"Additional Info"})]})]})}),(0,a.jsxs)(l.av,{value:"overview",className:"flex-1 p-6 overflow-y-auto",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Information"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"Full Name"}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("span",{className:"text-sm",children:V.fullName||"Unknown"})})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"Agent"}),(0,a.jsx)("div",{className:"text-sm",children:V.agent||""})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"Mobile"}),(0,a.jsx)("div",{className:"text-sm",children:V.mobileNumber||"N/A"})]}),!eE(V)&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"Duration"}),(0,a.jsx)("div",{className:"text-sm",children:D(eT(V))})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"Started at"}),(0,a.jsx)("div",{className:"text-sm",children:B(V.callStartTime)})]}),!eE(V)&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"Ended at"}),(0,a.jsx)("div",{className:"text-sm",children:B(V.callEndTime)})]})]}),(0,a.jsx)("h3",{className:"text-lg font-medium mt-8 mb-4",children:"Analysis"}),(0,a.jsx)("div",{className:"space-y-4",children:eE(V)?(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"End call reason"}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(p.E,{className:"bg-red-500 text-white",children:V.callEndReason||"N/A"})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"Call sentiment"}),(0,a.jsx)("div",{className:"flex items-center",children:(()=>{let e=I(V.emotions);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:`h-2 w-2 rounded-full ${e.color} mr-2 mt-1`}),(0,a.jsxs)("span",{className:"text-sm flex items-center",children:[e.emoji," ",(0,a.jsx)("span",{className:"ml-1",children:V.emotions})]})]})})()})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"Call status"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"h-2 w-2 rounded-full bg-green-500 mr-2 mt-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Completed"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"End call reason"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"h-2 w-2 rounded-full bg-green-500 mr-2 mt-1"}),(0,a.jsx)("span",{className:"text-sm",children:V.callEndReason||"N/A"})]})]})]})})]}),(0,a.jsx)(l.av,{value:"summary",className:"flex-1 p-6 overflow-y-auto",children:(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("h3",{className:"text-md font-medium mb-4 text-muted-foreground",children:"Summary"}),(0,a.jsx)("div",{className:"p-4 bg-muted/50 rounded-md",children:(0,a.jsx)("p",{children:V.callSummary?V.callSummary:"No summary available."})})]})}),(0,a.jsxs)(l.av,{value:"transcript",className:"flex-1 overflow-hidden flex flex-col",children:[(0,a.jsx)("div",{className:"p-4 border-b border-border",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,a.jsx)(i.p,{placeholder:"Search",className:"pl-10 pr-10 bg-background dark:bg-card"})]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:(0,a.jsx)("div",{className:"space-y-6",children:V.callTranscript?V.callTranscript.split("\n").map((e,s)=>{if(e.startsWith("AI:")||e.startsWith("User:")){let t=e.startsWith("User:"),r=e.substring(e.indexOf(":")+1).trim();return(0,a.jsxs)("div",{className:`flex ${t?"justify-end":""}`,children:[!t&&(0,a.jsx)("div",{className:"flex-shrink-0 mr-3",children:(0,a.jsx)(A.eu,{className:"h-8 w-8",children:(0,a.jsx)(A.q5,{className:"bg-primary/10 text-primary",children:V.agent?.charAt(0)||"A"})})}),(0,a.jsxs)("div",{className:`max-w-[75%] ${t?"ml-auto":"mr-auto"}`,children:[(0,a.jsx)("div",{className:"text-xs text-muted-foreground mb-1",children:t?"You":V.agent}),(0,a.jsx)("div",{className:`text-sm p-3 rounded-lg ${t?"bg-primary/10":"bg-muted"}`,children:r})]}),t&&(0,a.jsx)("div",{className:"flex-shrink-0 ml-3",children:(0,a.jsx)(A.eu,{className:"h-8 w-8",children:(0,a.jsx)(A.q5,{className:"bg-muted",children:V.fullName?.charAt(0)||"U"})})})]},s)}return null}):(0,a.jsx)("div",{className:"text-center text-muted-foreground",children:"No transcript available"})})})]}),(0,a.jsxs)(l.av,{value:"additionalInfo",className:"flex-1 p-6 overflow-y-auto",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Preferences"}),(0,a.jsxs)("div",{className:"space-y-4",children:[V.interest&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Interest"}),(0,a.jsx)("div",{className:"text-sm",children:V.interest})]}),V.preferredProject&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Preferred Project"}),(0,a.jsx)("div",{className:"text-sm",children:V.preferredProject})]}),V.preferredLocation&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Location"}),(0,a.jsx)("div",{className:"text-sm",children:V.preferredLocation})]}),V.preferredUnitType&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Unit Type"}),(0,a.jsx)("div",{className:"text-sm",children:V.preferredUnitType})]}),V.projectType&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Project Type"}),(0,a.jsx)("div",{className:"text-sm",children:V.projectType})]}),V.investmentType&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Investment Type"}),(0,a.jsx)("div",{className:"text-sm",children:V.investmentType})]}),V.budget&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Budget"}),(0,a.jsx)("div",{className:"text-sm",children:V.budget})]}),V.brokenPromise&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Broken Promise"}),(0,a.jsx)("div",{className:"text-sm",children:V.brokenPromise})]}),V.callBackLanguage&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Call Back Language"}),(0,a.jsx)("div",{className:"text-sm",children:V.callBackLanguage})]}),V.callBackRequest&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Call Back Request"}),(0,a.jsx)("div",{className:"text-sm",children:V.callBackRequest})]}),V.claimedPaidAwaitingPOP&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Claimed Paid Awaiting POP"}),(0,a.jsx)("div",{className:"text-sm",children:V.claimedPaidAwaitingPOP})]}),V.doNotCall&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Do Not Call"}),(0,a.jsx)("div",{className:"text-sm",children:V.doNotCall})]}),V.followingPaymentPlan&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Following Payment Plan"}),(0,a.jsx)("div",{className:"text-sm",children:V.followingPaymentPlan})]}),V.fullyPaid&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Fully Paid"}),(0,a.jsx)("div",{className:"text-sm",children:V.fullyPaid})]}),V.fullyPaidByPDC&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Fully Paid By PDC"}),(0,a.jsx)("div",{className:"text-sm",children:V.fullyPaidByPDC})]}),V.incorrectContactDetails&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Incorrect Contact Details"}),(0,a.jsx)("div",{className:"text-sm",children:V.incorrectContactDetails})]}),V.mortgage&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Mortgage"}),(0,a.jsx)("div",{className:"text-sm",children:V.mortgage})]}),V.notResponding&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Not Responding"}),(0,a.jsx)("div",{className:"text-sm",children:V.notResponding})]}),V.notRespondingSOASent&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Not Responding (SOA Sent)"}),(0,a.jsx)("div",{className:"text-sm",children:V.notRespondingSOASent})]}),V.notWillingToPay&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Not Willing To Pay"}),(0,a.jsx)("div",{className:"text-sm",children:V.notWillingToPay})]}),V.popRaised&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"POP Raised"}),(0,a.jsx)("div",{className:"text-sm",children:V.popRaised})]}),V.promiseToPay&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Promise To Pay"}),(0,a.jsx)("div",{className:"text-sm",children:V.promiseToPay})]}),V.promiseToPayPartial&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Promise To Pay (Partial)"}),(0,a.jsx)("div",{className:"text-sm",children:V.promiseToPayPartial})]}),V.refuseToPay&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Refuse To Pay"}),(0,a.jsx)("div",{className:"text-sm",children:V.refuseToPay})]}),V.thirdParty&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Third Party"}),(0,a.jsx)("div",{className:"text-sm",children:V.thirdParty})]}),V.willingToPay&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Willing To Pay"}),(0,a.jsx)("div",{className:"text-sm",children:V.willingToPay})]})]}),V.bookedStatus&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mt-8 mb-4",children:"Booking Information"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Booked Status"}),(0,a.jsx)("div",{className:"text-sm",children:V.bookedStatus})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Confirmed Status"}),(0,a.jsx)("div",{className:"text-sm",children:V.confirmedStatus||"N/A"})]})]})]}),V.additionalQuestions&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mt-8 mb-4",children:"Additional Questions"}),(0,a.jsx)("div",{className:"p-4 bg-muted/50 rounded-md",children:(0,a.jsx)("p",{className:"text-sm",children:V.additionalQuestions})})]}),V.Response&&(0,a.jsxs)("div",{className:"flex mt-5",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Response"}),(0,a.jsx)("div",{className:"text-sm",children:V.Response})]}),V.Notes&&(0,a.jsxs)("div",{className:"flex mt-3",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Notes"}),(0,a.jsx)("div",{className:"text-sm",children:V.Notes})]}),V.Channel&&(0,a.jsxs)("div",{className:"flex mt-3",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Channel"}),(0,a.jsx)("div",{className:"text-sm",children:V.Channel})]}),V.GuestRequest&&(0,a.jsxs)("div",{className:"flex mt-3",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"GuestRequest"}),(0,a.jsx)("div",{className:"text-sm",children:V.GuestRequest})]})]})]})]}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full text-muted-foreground",children:es?(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)("div",{className:"animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full mb-4"}),(0,a.jsx)("p",{children:"Loading call data..."})]}):(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(N.A,{className:"h-12 w-12 text-muted-foreground mb-4"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:ea||"Select a call to view details"})]})})}),(0,a.jsx)(S.Lt,{open:ej,onOpenChange:eg,children:(0,a.jsxs)(S.EO,{children:[(0,a.jsxs)(S.wd,{children:[(0,a.jsx)(S.r7,{children:"Are you sure?"}),(0,a.jsx)(S.$v,{children:"This will permanently delete this call history record. This action cannot be undone."})]}),(0,a.jsxs)(S.ck,{children:[(0,a.jsx)(S.Zr,{children:"Cancel"}),(0,a.jsx)(S.Rx,{onClick:eS,className:"bg-red-600 hover:bg-red-700 focus:ring-red-600",children:"Delete"})]})]})})]})]})}},25727:(e,s,t)=>{Promise.resolve().then(t.bind(t,10069))},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},31158:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},37966:(e,s,t)=>{"use strict";t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\app\\\\(workspace)\\\\history\\\\HistoryContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\history\\HistoryContent.tsx","default")},55146:(e,s,t)=>{"use strict";t.d(s,{B8:()=>T,UC:()=>R,bL:()=>S,l9:()=>E});var a=t(43210),r=t(70569),i=t(11273),l=t(72942),n=t(46059),d=t(14163),c=t(43),o=t(65551),m=t(96963),x=t(60687),u="Tabs",[h,f]=(0,i.A)(u,[l.RG]),v=(0,l.RG)(),[j,g]=h(u),N=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:a,onValueChange:r,defaultValue:i,orientation:l="horizontal",dir:n,activationMode:u="automatic",...h}=e,f=(0,c.jH)(n),[v,g]=(0,o.i)({prop:a,onChange:r,defaultProp:i});return(0,x.jsx)(j,{scope:t,baseId:(0,m.B)(),value:v,onValueChange:g,orientation:l,dir:f,activationMode:u,children:(0,x.jsx)(d.sG.div,{dir:f,"data-orientation":l,...h,ref:s})})});N.displayName=u;var p="TabsList",A=a.forwardRef((e,s)=>{let{__scopeTabs:t,loop:a=!0,...r}=e,i=g(p,t),n=v(t);return(0,x.jsx)(l.bL,{asChild:!0,...n,orientation:i.orientation,dir:i.dir,loop:a,children:(0,x.jsx)(d.sG.div,{role:"tablist","aria-orientation":i.orientation,...r,ref:s})})});A.displayName=p;var b="TabsTrigger",w=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:a,disabled:i=!1,...n}=e,c=g(b,t),o=v(t),m=P(c.baseId,a),u=k(c.baseId,a),h=a===c.value;return(0,x.jsx)(l.q7,{asChild:!0,...o,focusable:!i,active:h,children:(0,x.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":u,"data-state":h?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:m,...n,ref:s,onMouseDown:(0,r.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(a)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(a)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;h||i||!e||c.onValueChange(a)})})})});w.displayName=b;var y="TabsContent",C=a.forwardRef((e,s)=>{let{__scopeTabs:t,value:r,forceMount:i,children:l,...c}=e,o=g(y,t),m=P(o.baseId,r),u=k(o.baseId,r),h=r===o.value,f=a.useRef(h);return a.useEffect(()=>{let e=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,x.jsx)(n.C,{present:i||h,children:({present:t})=>(0,x.jsx)(d.sG.div,{"data-state":h?"active":"inactive","data-orientation":o.orientation,role:"tabpanel","aria-labelledby":m,hidden:!t,id:u,tabIndex:0,...c,ref:s,style:{...e.style,animationDuration:f.current?"0s":void 0},children:t&&l})})});function P(e,s){return`${e}-trigger-${s}`}function k(e,s){return`${e}-content-${s}`}C.displayName=y;var S=N,T=A,E=w,R=C},76104:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a={src:"/_next/static/media/Binghatti-Lisa.85c81ecb.jpeg",height:1586,width:1586,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/2wBDAQoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/wgARCAAIAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAX/xAAUAQEAAAAAAAAAAAAAAAAAAAAC/9oADAMBAAIQAxAAAACeA//EABsQAAEFAQEAAAAAAAAAAAAAAAECAwQREwAi/9oACAEBAAE/AG6e3khuoyZAbQNDWYR6SO//xAAVEQEBAAAAAAAAAAAAAAAAAAABAP/aAAgBAgEBPwAL/8QAFhEAAwAAAAAAAAAAAAAAAAAAAAFB/9oACAEDAQE/AHD/2Q==",blurWidth:8,blurHeight:8}},85763:(e,s,t)=>{"use strict";t.d(s,{Xi:()=>d,av:()=>c,j7:()=>n,tU:()=>l});var a=t(60687);t(43210);var r=t(55146),i=t(4780);function l({className:e,...s}){return(0,a.jsx)(r.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",e),...s})}function n({className:e,...s}){return(0,a.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-1",e),...s})}function d({className:e,...s}){return(0,a.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring inline-flex flex-1 items-center justify-center gap-1.5 rounded-md px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s})}function c({className:e,...s}){return(0,a.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",e),...s})}},95463:(e,s,t)=>{Promise.resolve().then(t.bind(t,37966))},96834:(e,s,t)=>{"use strict";t.d(s,{E:()=>d});var a=t(60687);t(43210);var r=t(8730),i=t(24224),l=t(4780);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:s,asChild:t=!1,...i}){let d=t?r.DX:"span";return(0,a.jsx)(d,{"data-slot":"badge",className:(0,l.cn)(n({variant:s}),e),...i})}},97840:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},98492:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(62688).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])}};