(()=>{var e={};e.id=3914,e.ids=[3914],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7352:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(37413);s(61120);var r=s(87345);function i({params:e}){return(0,a.jsx)(r.default,{organizationId:e.id})}},8819:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15079:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>h,gC:()=>m,l6:()=>d,yv:()=>c});var a=s(60687);s(43210);var r=s(22670),i=s(78272),n=s(13964),o=s(3589),l=s(4780);function d({...e}){return(0,a.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,a.jsx)(r.WT,{"data-slot":"select-value",...e})}function u({className:e,children:t,...s}){return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger",className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s,children:[t,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function m({className:e,children:t,position:s="popper",...i}){return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...i,children:[(0,a.jsx)(p,{}),(0,a.jsx)(r.LM,{className:(0,l.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,a.jsx)(x,{})]})})}function h({className:e,children:t,...s}){return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:t})]})}function p({className:e,...t}){return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(o.A,{className:"size-4"})})}function x({className:e,...t}){return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,a.jsx)(i.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20621:(e,t,s)=>{Promise.resolve().then(s.bind(s,87345))},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34729:(e,t,s)=>{"use strict";s.d(t,{T:()=>i});var a=s(60687);s(43210);var r=s(4780);function i({className:e,...t}){return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...t})}},40156:(e,t,s)=>{"use strict";s.d(t,{default:()=>y});var a=s(60687),r=s(43210),i=s(16189),n=s(44493),o=s(85763),l=s(29523),d=s(89667),c=s(80013),u=s(34729),m=s(15079),h=s(41862),p=s(28559),x=s(8819),g=s(52581),f=s(87610),v=s(6607);let j=async(e,t)=>{let s=await (0,v.t)(`http://localhost:4000/api/organizations/${e}/settings`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!s.ok)throw Error((await s.json()).message||"Failed to update organization settings");return s.json()};var b=s(4780);function y({organizationId:e}){let[t,s]=(0,r.useState)(null),[v,y]=(0,r.useState)(!0),[w,N]=(0,r.useState)(!1),[C,k]=(0,r.useState)("details"),[z,A]=(0,r.useState)({name:"",description:"",status:"active"}),[T,S]=(0,r.useState)({credits:0,autoRechargeEnabled:!1,autoRechargeThreshold:1,autoRechargeAmount:0,callPricePerMinute:.1,minimumCreditsThreshold:1,monthlyMinutesAllowance:0,monthlyResetDate:1}),[P,F]=(0,r.useState)({fullName:"",email:""}),_=(0,i.useRouter)(),$=async()=>{try{y(!0);let t=await (0,f.SA)(e);s(t),A({name:t.name,description:t.description||"",status:t.status}),S({credits:t.credits||0,autoRechargeEnabled:t.autoRechargeEnabled||!1,autoRechargeThreshold:t.autoRechargeThreshold||1,autoRechargeAmount:t.autoRechargeAmount||0,callPricePerMinute:t.callPricePerMinute||.1,minimumCreditsThreshold:t.minimumCreditsThreshold||1,monthlyMinutesAllowance:t.monthlyMinutesAllowance||0,monthlyResetDate:t.monthlyResetDate||1}),F({fullName:t.fullName||"",email:t.email||""})}catch(e){console.error("Error fetching Workspace:",e),g.o.error("Failed to fetch workspace details")}finally{y(!1)}},E=async()=>{try{N(!0);let t=await (0,f.L_)(e,z);s(t),g.o.success("Workspace details updated successfully")}catch(e){console.error("Error updating Workspace:",e),g.o.error("Failed to update Workspace details")}finally{N(!1)}},R=async()=>{try{N(!0);let t=await (0,f.co)(e,T);s(t),g.o.success("Workspace billing settings updated successfully")}catch(e){console.error("Error updating Workspace billing:",e),g.o.error("Failed to update Workspace billing settings")}finally{N(!1)}},M=async()=>{try{N(!0),await j(e,P),await $(),g.o.success("Notification settings updated successfully")}catch(e){console.error("Error updating notification settings:",e),g.o.error("Failed to update notification settings")}finally{N(!1)}};return v?(0,a.jsx)("div",{className:"container mx-auto py-6 flex justify-center items-center min-h-[60vh]",children:(0,a.jsx)(h.A,{className:"h-8 w-8 animate-spin"})}):t?(0,a.jsxs)("div",{className:"container mx-auto py-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsxs)(l.$,{variant:"outline",onClick:()=>_.back(),className:"mr-4",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,a.jsx)("h1",{className:"text-3xl font-bold",children:t.name})]}),(0,a.jsxs)(o.tU,{value:C,onValueChange:k,children:[(0,a.jsxs)(o.j7,{className:"mb-6",children:[(0,a.jsx)(o.Xi,{value:"details",children:"Details"}),(0,a.jsx)(o.Xi,{value:"billing",children:"Billing"}),(0,a.jsx)(o.Xi,{value:"notifications",children:"Notifications"})]}),(0,a.jsx)(o.av,{value:"details",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Workspace Details"}),(0,a.jsx)(n.BT,{children:"Update the Workspace's basic information."})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"name",className:"md:text-right",children:"Name"}),(0,a.jsx)(d.p,{id:"name",value:z.name,onChange:e=>A({...z,name:e.target.value}),className:"md:col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"description",className:"md:text-right",children:"Description"}),(0,a.jsx)(u.T,{id:"description",value:z.description,onChange:e=>A({...z,description:e.target.value}),className:"md:col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"status",className:"md:text-right",children:"Status"}),(0,a.jsxs)(m.l6,{value:z.status,onValueChange:e=>A({...z,status:e}),children:[(0,a.jsx)(m.bq,{className:"md:col-span-3",children:(0,a.jsx)(m.yv,{placeholder:"Select status"})}),(0,a.jsxs)(m.gC,{children:[(0,a.jsx)(m.eb,{value:"active",children:"Active"}),(0,a.jsx)(m.eb,{value:"inactive",children:"Inactive"}),(0,a.jsx)(m.eb,{value:"suspended",children:"Suspended"})]})]})]})]}),(0,a.jsx)(n.wL,{className:"flex justify-end",children:(0,a.jsx)(l.$,{onClick:E,disabled:w,children:w?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Save Changes"]})})})]})}),(0,a.jsx)(o.av,{value:"billing",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Billing Settings"}),(0,a.jsx)(n.BT,{children:"Manage the Workspace's billing and credits."})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"credits",className:"md:text-right",children:"Credits"}),(0,a.jsxs)("div",{className:"md:col-span-3 flex items-center",children:[(0,a.jsx)(d.p,{id:"credits",type:"number",min:"0",step:"0.01",value:T.credits,onChange:e=>S({...T,credits:parseFloat(e.target.value)||0}),className:"w-full"}),(0,a.jsxs)("span",{className:"ml-2 text-sm text-gray-500",children:["Current: ",(0,b.v)(t.credits||0)]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"callPricePerMinute",className:"md:text-right",children:"Call Price Per Minute ($)"}),(0,a.jsxs)("div",{className:"md:col-span-3",children:[(0,a.jsx)(d.p,{id:"callPricePerMinute",type:"number",min:"0.01",step:"0.01",value:T.callPricePerMinute,onChange:e=>S({...T,callPricePerMinute:parseFloat(e.target.value)||.1}),className:"w-full"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"The price charged per minute for calls"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"minimumCreditsThreshold",className:"md:text-right",children:"Minimum Credits Threshold ($)"}),(0,a.jsxs)("div",{className:"md:col-span-3",children:[(0,a.jsx)(d.p,{id:"minimumCreditsThreshold",type:"number",min:"0",step:"0.01",value:T.minimumCreditsThreshold,onChange:e=>S({...T,minimumCreditsThreshold:parseFloat(e.target.value)||1}),className:"w-full"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"When credits fall below this amount, calls will be blocked"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"monthlyMinutesAllowance",className:"md:text-right",children:"Monthly Minutes Allowance"}),(0,a.jsxs)("div",{className:"md:col-span-3",children:[(0,a.jsx)(d.p,{id:"monthlyMinutesAllowance",type:"number",min:"0",step:"1",value:T.monthlyMinutesAllowance,onChange:e=>S({...T,monthlyMinutesAllowance:parseFloat(e.target.value)||0}),className:"w-full"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Monthly minutes allowance for this workspace (0 to disable)"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"monthlyResetDate",className:"md:text-right",children:"Monthly Reset Date"}),(0,a.jsxs)("div",{className:"md:col-span-3",children:[(0,a.jsxs)(m.l6,{value:T.monthlyResetDate.toString(),onValueChange:e=>S({...T,monthlyResetDate:parseInt(e)}),children:[(0,a.jsx)(m.bq,{children:(0,a.jsx)(m.yv,{placeholder:"Select reset date"})}),(0,a.jsx)(m.gC,{children:Array.from({length:28},(e,t)=>t+1).map(e=>(0,a.jsxs)(m.eb,{value:e.toString(),children:[e,1===e?"st":2===e?"nd":3===e?"rd":"th"," of each month"]},e))})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Day of the month when monthly credits and allowances reset"})]})]})]}),(0,a.jsx)(n.wL,{className:"flex justify-end",children:(0,a.jsx)(l.$,{onClick:R,disabled:w,children:w?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Save Billing Settings"]})})})]})}),(0,a.jsx)(o.av,{value:"notifications",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Email Notification Settings"}),(0,a.jsx)(n.BT,{children:"Configure email notifications for credit alerts and warnings."})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"fullName",className:"md:text-right",children:"Client Full Name"}),(0,a.jsxs)("div",{className:"md:col-span-3",children:[(0,a.jsx)(d.p,{id:"fullName",type:"text",value:P.fullName,onChange:e=>F({...P,fullName:e.target.value}),placeholder:"Enter client's full name",className:"w-full"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"This name will be used in email notifications for personalization"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(c.J,{htmlFor:"email",className:"md:text-right",children:"Notification Email"}),(0,a.jsxs)("div",{className:"md:col-span-3",children:[(0,a.jsx)(d.p,{id:"email",type:"email",value:P.email,onChange:e=>F({...P,email:e.target.value}),placeholder:"Enter email address for notifications",className:"w-full"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Credit alerts and warnings will be sent to this email address"})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Email Notification Types"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Credit Runout Alert:"})," Sent when credits are completely depleted"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Credit Warning:"})," Sent when credits fall below 2x the minimum threshold"]}),(0,a.jsx)("li",{children:"• Both emails include a direct link to add funds to the account"})]})]}),t.minimumCreditsThreshold&&(0,a.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Current Thresholds"}),(0,a.jsxs)("div",{className:"text-sm text-gray-700 space-y-1",children:[(0,a.jsxs)("p",{children:["• ",(0,a.jsx)("strong",{children:"Minimum Credits Threshold:"})," $",t.minimumCreditsThreshold.toFixed(2)]}),(0,a.jsxs)("p",{children:["• ",(0,a.jsx)("strong",{children:"Warning Threshold:"})," $",(2*t.minimumCreditsThreshold).toFixed(2)]}),(0,a.jsxs)("p",{children:["• ",(0,a.jsx)("strong",{children:"Current Credits:"})," $",t.credits.toFixed(2)]})]})]})]}),(0,a.jsx)(n.wL,{className:"flex justify-end",children:(0,a.jsx)(l.$,{onClick:M,disabled:w,children:w?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"mr-2 h-4 w-4"}),"Save Notification Settings"]})})})]})})]})]}):(0,a.jsx)("div",{className:"container mx-auto py-6",children:(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Workspace not found."})})}},44493:(e,t,s)=>{"use strict";s.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>i,aR:()=>n,wL:()=>c});var a=s(60687);s(43210);var r=s(4780);function i({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",e),...t})}function n({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("flex flex-col gap-1.5 px-6",e),...t})}function o({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6",e),...t})}},55146:(e,t,s)=>{"use strict";s.d(t,{B8:()=>S,UC:()=>F,bL:()=>T,l9:()=>P});var a=s(43210),r=s(70569),i=s(11273),n=s(72942),o=s(46059),l=s(14163),d=s(43),c=s(65551),u=s(96963),m=s(60687),h="Tabs",[p,x]=(0,i.A)(h,[n.RG]),g=(0,n.RG)(),[f,v]=p(h),j=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,onValueChange:r,defaultValue:i,orientation:n="horizontal",dir:o,activationMode:h="automatic",...p}=e,x=(0,d.jH)(o),[g,v]=(0,c.i)({prop:a,onChange:r,defaultProp:i});return(0,m.jsx)(f,{scope:s,baseId:(0,u.B)(),value:g,onValueChange:v,orientation:n,dir:x,activationMode:h,children:(0,m.jsx)(l.sG.div,{dir:x,"data-orientation":n,...p,ref:t})})});j.displayName=h;var b="TabsList",y=a.forwardRef((e,t)=>{let{__scopeTabs:s,loop:a=!0,...r}=e,i=v(b,s),o=g(s);return(0,m.jsx)(n.bL,{asChild:!0,...o,orientation:i.orientation,dir:i.dir,loop:a,children:(0,m.jsx)(l.sG.div,{role:"tablist","aria-orientation":i.orientation,...r,ref:t})})});y.displayName=b;var w="TabsTrigger",N=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:a,disabled:i=!1,...o}=e,d=v(w,s),c=g(s),u=z(d.baseId,a),h=A(d.baseId,a),p=a===d.value;return(0,m.jsx)(n.q7,{asChild:!0,...c,focusable:!i,active:p,children:(0,m.jsx)(l.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":h,"data-state":p?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:u,...o,ref:t,onMouseDown:(0,r.m)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(a)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(a)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;p||i||!e||d.onValueChange(a)})})})});N.displayName=w;var C="TabsContent",k=a.forwardRef((e,t)=>{let{__scopeTabs:s,value:r,forceMount:i,children:n,...d}=e,c=v(C,s),u=z(c.baseId,r),h=A(c.baseId,r),p=r===c.value,x=a.useRef(p);return a.useEffect(()=>{let e=requestAnimationFrame(()=>x.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,m.jsx)(o.C,{present:i||p,children:({present:s})=>(0,m.jsx)(l.sG.div,{"data-state":p?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!s,id:h,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:x.current?"0s":void 0},children:s&&n})})});function z(e,t){return`${e}-trigger-${t}`}function A(e,t){return`${e}-content-${t}`}k.displayName=C;var T=j,S=y,P=N,F=k},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67885:(e,t,s)=>{Promise.resolve().then(s.bind(s,40156))},68632:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=s(65239),r=s(48088),i=s(88170),n=s.n(i),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["(workspace)",{children:["workspaces",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,7352)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\[id]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,50184)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\[id]\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(workspace)/workspaces/[id]/page",pathname:"/workspaces/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},74075:e=>{"use strict";e.exports=require("zlib")},74678:(e,t,s)=>{"use strict";s.d(t,{H:()=>a,e:()=>r});let a="http://localhost:4000",r="pk_test_51ROz1YRpJ0zLf0aTbgbDkpShvfpNxdZPet1QXClapTckA7Cy0tsaxY2qY1dp8oSBGOFqnh0vugjd8mDluFWgKpRL00bACyumT8"},78148:(e,t,s)=>{"use strict";s.d(t,{b:()=>o});var a=s(43210),r=s(14163),i=s(60687),n=a.forwardRef((e,t)=>(0,i.jsx)(r.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));n.displayName="Label";var o=n},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80013:(e,t,s)=>{"use strict";s.d(t,{J:()=>n});var a=s(60687);s(43210);var r=s(78148),i=s(4780);function n({className:e,...t}){return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85763:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>l,av:()=>d,j7:()=>o,tU:()=>n});var a=s(60687);s(43210);var r=s(55146),i=s(4780);function n({className:e,...t}){return(0,a.jsx)(r.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",e),...t})}function o({className:e,...t}){return(0,a.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-1",e),...t})}function l({className:e,...t}){return(0,a.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring inline-flex flex-1 items-center justify-center gap-1.5 rounded-md px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function d({className:e,...t}){return(0,a.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",e),...t})}},87345:(e,t,s)=>{"use strict";s.d(t,{default:()=>a});let a=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\app\\\\(workspace)\\\\workspaces\\\\[id]\\\\OrganizationDetailsContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\workspaces\\[id]\\OrganizationDetailsContent.tsx","default")},87610:(e,t,s)=>{"use strict";s.d(t,{Dp:()=>d,EC:()=>n,J:()=>u,L_:()=>o,SA:()=>i,VO:()=>c,co:()=>l,h6:()=>r});var a=s(74678);let r=async()=>{let e=await fetch(`${a.H}/api/organizations`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch organizations");return e.json()},i=async e=>{let t=await fetch(`${a.H}/api/organizations/${e}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch organization");return t.json()},n=async e=>{let t=await fetch(`${a.H}/api/organizations`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Failed to create organization");return t.json()},o=async(e,t)=>{let s=await fetch(`${a.H}/api/organizations/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify(t)});if(!s.ok)throw Error((await s.json()).message||"Failed to update organization");return s.json()},l=async(e,t)=>{let s=await fetch(`${a.H}/api/organizations/${e}/billing`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify(t)});if(!s.ok)throw Error((await s.json()).message||"Failed to update organization billing");return s.json()},d=async e=>{let t=await fetch(`${a.H}/api/organizations/${e}`,{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!t.ok)throw Error((await t.json()).message||"Failed to delete organization");return t.json()},c=async(e,t,s)=>{let r=await fetch(`${a.H}/api/organizations/${e}/users/${t}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify({isAdmin:s})});if(!r.ok)throw Error((await r.json()).message||"Failed to add user to organization");return r.json()},u=async(e,t)=>{let s=await fetch(`${a.H}/api/organizations/${e}/users/${t}`,{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!s.ok)throw Error((await s.json()).message||"Failed to remove user from organization");return s.json()}},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var a=s(60687);s(43210);var r=s(4780);function i({className:e,type:t,...s}){return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,r.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[287,9176,7674,5814,598,5188,6034,1476,4772],()=>s(68632));module.exports=a})();