(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8974],{33792:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(95155),r=s(90282),c=s(35695),n=s(12115),l=s(12421);function i(){let e=(0,c.useRouter)(),[t,s]=(0,n.useState)(!0),[i,o]=(0,n.useState)(!1);return((0,n.useEffect)(()=>{(async function(){try{if(!localStorage.getItem("access_token")){s(!1);return}(await (0,l.t)("".concat("http://localhost:4000","/api/auth/me"))).ok?(o(!0),e.push("/dashboard")):s(!1)}catch(e){console.error("Error checking authentication status:",e),s(!1)}})()},[e]),t)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsx)("div",{className:"h-12 w-12 border-4 border-t-blue-500 border-gray-200 rounded-full animate-spin"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:"Loading..."})]})}):(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(r.default,{})})}},36440:(e,t,s)=>{Promise.resolve().then(s.bind(s,33792))}},e=>{var t=t=>e(e.s=t);e.O(0,[4201,6766,7268,282,8441,1684,7358],()=>t(36440)),_N_E=e.O()}]);