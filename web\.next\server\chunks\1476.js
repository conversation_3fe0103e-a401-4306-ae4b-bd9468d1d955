exports.id=1476,exports.ids=[1476],exports.modules={709:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,47429,23)),Promise.resolve().then(t.bind(t,6931)),Promise.resolve().then(t.bind(t,46678)),Promise.resolve().then(t.bind(t,96988))},4780:(e,r,t)=>{"use strict";t.d(r,{cn:()=>n,v:()=>o});var s=t(49384),a=t(82348);function n(...e){return(0,a.QP)((0,s.$)(e))}function o(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}},11557:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},14260:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"00167d048878da237777678dc5d3be306a49e3666d":()=>h,"00bc05aa5f015675f0b9f86a97aab0cbaac22f9558":()=>m,"00bd6ed5ddcea8d6a33b0b0c1e7ed159ce451d0d84":()=>g,"400b4f47d463ad41f3246a7eb7d9d33cbcd971a846":()=>l,"40922dd30dbc05a2c82413a6ab04d49af413ddd216":()=>u});var s=t(91199);t(42087);var a=t(74208),n=t(68567);let o=n.z.object({fullName:n.z.string().min(2,{message:"Full name must be at least 2 characters"}),email:n.z.string().email({message:"Please enter a valid email address"}),password:n.z.string().min(3,{message:"Password must be at least 6 characters"})}),i=n.z.object({email:n.z.string().email({message:"Please enter a valid email address"}),password:n.z.string().min(1,{message:"Password is required"})});var c=t(33331);let d="http://localhost:4000";async function l(e){let r={fullName:e.get("fullName"),email:e.get("email"),password:e.get("password")},t=o.safeParse(r);if(!t.success)return{success:!1,message:"Validation failed",fieldErrors:t.error.flatten().fieldErrors};let{fullName:s,email:a,password:n}=t.data;try{let e=await fetch(`${d}/api/users/register`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({fullName:s,email:a,password:n}),cache:"no-store"});if(!e.ok)try{let r=await e.json();return{success:!1,message:r.message||"Registration failed. Please try again."}}catch{let r=await e.text().catch(()=>e.statusText);return{success:!1,message:r||"Registration failed. Please try again."}}let r=await e.text();return{success:!0,message:r||"User registered successfully! Awaiting admin approval."}}catch(e){return console.error("Registration error:",e),{success:!1,message:"An unexpected error occurred. Please try again later."}}}async function u(e){let r={email:e.get("email"),password:e.get("password")},t=i.safeParse(r);if(!t.success)return{success:!1,message:"Validation failed",fieldErrors:t.error.flatten().fieldErrors};let{email:s,password:a}=t.data;try{let e=await fetch(`${d}/api/auth/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:s,password:a}),cache:"no-store"});if(!e.ok)try{let r=await e.json();return{success:!1,message:r.message||"Login failed. Please check your credentials."}}catch{return{success:!1,message:e.statusText||"Login failed. Please try again."}}try{let r=await e.json();if(r.access_token&&r.refresh_token)return{success:!0,message:"Login successful!",redirect:"/dashboard",tokens:{access_token:r.access_token,refresh_token:r.refresh_token}};return{success:!1,message:"Invalid response from server. Missing authentication tokens."}}catch(e){return{success:!1,message:"Failed to process login response."}}}catch(e){return console.error("Login error:",e),{success:!1,message:"An unexpected error occurred. Please try again later."}}}async function h(){let e=(await a.UL()).get("refresh_token")?.value;if(!e)return{success:!1};try{let r=await fetch(`${d}/api/auth/refresh`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`},body:JSON.stringify({refreshToken:e}),cache:"no-store"});if(!r.ok)return{success:!1};let t=await r.json();if(t.access_token)return(await (0,a.UL)()).set("access_token",t.access_token,{httpOnly:!0,secure:!0,path:"/",sameSite:"lax",maxAge:3600}),{success:!0,newAccessToken:t.access_token};return{success:!1}}catch(e){return console.error("Token refresh error:",e),{success:!1}}}async function m(){let e;if(!(e=(await a.UL()).get("access_token")?.value))return console.log("No access token found"),{success:!1,error:"Not authenticated"};try{let r=await fetch(`${d}/api/auth/me`,{method:"GET",headers:{Authorization:`Bearer ${e}`},cache:"no-store"});if(!r.ok){if(401===r.status||403===r.status){let e=await h();if(e.success){let r=await fetch(`${d}/api/auth/me`,{method:"GET",headers:{Authorization:`Bearer ${e.newAccessToken}`},cache:"no-store"});if(r.ok){let e=await r.json();if(e&&e.userId&&e.email)return{success:!0,user:e};return{success:!1,error:"Account not authorized or pending approval"}}}return{success:!1,error:"Not authorized"}}return{success:!1,error:`Error: ${r.status}`}}let t=await r.json();if(t&&t.userId&&t.email)return{success:!0,user:t};return{success:!1,error:"Account not authorized or pending approval"}}catch(e){return console.error("Error fetching user data:",e),{success:!1,error:"An error occurred while fetching user data"}}}async function g(){try{let e=await (0,a.UL)();return e.delete("access_token"),e.delete("refresh_token"),{success:!0,message:"Logged out successfully",redirect:"/"}}catch(e){return console.error("Logout error:",e),{success:!1,message:"An error occurred during logout"}}}(0,c.D)([l,u,h,m,g]),(0,s.A)(l,"400b4f47d463ad41f3246a7eb7d9d33cbcd971a846",null),(0,s.A)(u,"40922dd30dbc05a2c82413a6ab04d49af413ddd216",null),(0,s.A)(h,"00167d048878da237777678dc5d3be306a49e3666d",null),(0,s.A)(m,"00bc05aa5f015675f0b9f86a97aab0cbaac22f9558",null),(0,s.A)(g,"00bd6ed5ddcea8d6a33b0b0c1e7ed159ce451d0d84",null)},23776:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>n});var s=t(60687),a=t(10218);function n({children:e,...r}){return(0,s.jsx)(a.N,{...r,enableSystem:!0,attribute:"class",defaultTheme:"system",disableTransitionOnChange:!1,children:e})}},24709:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},26674:(e,r,t)=>{"use strict";t.d(r,{default:()=>i});var s=t(60687),a=t(39091),n=t(8693),o=t(43210);function i({children:e}){let[r]=(0,o.useState)(()=>new a.E);return(0,s.jsx)(n.Ht,{client:r,children:e})}},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>c,r:()=>i});var s=t(60687);t(43210);var a=t(8730),n=t(24224),o=t(4780);let i=(0,n.F)("inline-flex items-center cursor-pointer justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c({className:e,variant:r,size:t,asChild:n=!1,...c}){let d=n?a.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,o.cn)(i({variant:r,size:t,className:e})),...c})}},46678:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\theme\\ThemeProvider.tsx","ThemeProvider")},61135:()=>{},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m,metadata:()=>h});var s=t(37413),a=t(22376),n=t.n(a),o=t(68726),i=t.n(o);t(61135);var c=t(46678),d=t(6931),l=t(36162),u=t(96988);let h={title:"Orova AI",description:"Create, customize, and deploy AI agents effortlessly.",icons:{icon:"./favicon.png"}};function m({children:e}){return(0,s.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,s.jsxs)("head",{children:[(0,s.jsx)(l.default,{id:"microsoft-clarity",strategy:"afterInteractive",children:`
            (function(c,l,a,r,i,t,y){
              c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
              t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
              y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "${process.env.NEXT_PUBLIC_CLARITY_ID}");
          `}),(0,s.jsx)(l.default,{src:`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`,strategy:"afterInteractive"}),(0,s.jsx)(l.default,{id:"google-analytics",strategy:"afterInteractive",children:`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}');
          `})]}),(0,s.jsx)("body",{className:`${n().variable} ${i().variable} antialiased`,suppressHydrationWarning:!0,children:(0,s.jsx)(u.default,{children:(0,s.jsxs)(c.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:[(0,s.jsx)(d.Toaster,{position:"top-right",closeButton:!0,richColors:!0}),e]})})})]})}},94629:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,79167,23)),Promise.resolve().then(t.bind(t,52581)),Promise.resolve().then(t.bind(t,23776)),Promise.resolve().then(t.bind(t,26674))},96988:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\lib\\\\providers\\\\ReactQueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\lib\\providers\\ReactQueryProvider.tsx","default")}};