"use strict";exports.id=6407,exports.ids=[6407],exports.modules={2280:(e,a,s)=>{s.d(a,{A:()=>j});var t=s(60687),l=s(43210),r=s(89667),n=s(80013),i=s(29523),c=s(44493),d=s(41312);let o=(0,s(62688).A)("FileUp",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 12v6",key:"3ahymv"}],["path",{d:"m15 15-3-3-3 3",key:"15xj92"}]]);var m=s(47342),u=s(99270),h=s(13964),x=s(41862),p=s(50812),y=s(6211);function j({data:e,updateData:a}){let[s,j]=(0,l.useState)([]),[f,g]=(0,l.useState)(!1),[v,N]=(0,l.useState)(e.sources||[]),[A,b]=(0,l.useState)(e.sourceType||"contacts"),[w,C]=(0,l.useState)(""),[k,S]=(0,l.useState)({}),[T,D]=(0,l.useState)(1),[F,$]=(0,l.useState)(!0),[E,W]=(0,l.useState)(!1),z=(0,l.useRef)(null),J=async(e=1,a)=>{try{1===e?g(!0):W(!0);let s=a?`&search=${encodeURIComponent(a)}`:"",t=await fetch(`http://localhost:4000/api/contacts?page=${e}&limit=20${s}`,{headers:{Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!t.ok)throw Error("Failed to fetch contacts");let l=await t.json();1===e?j(l):j(e=>[...e,...l]),$(20===l.length)}catch(e){console.error("Error loading contacts:",e)}finally{g(!1),W(!1)}},M=e=>{let s;N(s=v.some(a=>a.contactName===e.contactName&&a.phoneNumber===e.phoneNumber)?v.filter(a=>a.contactName!==e.contactName||a.phoneNumber!==e.phoneNumber):[...v,e]),a({sources:s,sourceType:A})},O=e=>{b(e),a({sourceType:e})};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(n.J,{htmlFor:"name",children:"Campaign Name*"}),(0,t.jsx)(r.p,{id:"name",value:e.name,onChange:e=>{a({name:e.target.value})},placeholder:"Enter campaign name",className:k.name?"border-red-500":""}),k.name&&(0,t.jsx)("p",{className:"text-red-500 text-sm",children:k.name})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(n.J,{children:"Source Type"}),(0,t.jsxs)(p.z,{value:A,onValueChange:O,className:"grid grid-cols-3 gap-4",children:[(0,t.jsx)(c.Zp,{className:`cursor-pointer transition-all ${"contacts"===A?"border-primary":""}`,onClick:()=>O("contacts"),children:(0,t.jsxs)(c.Wu,{className:"p-4 flex flex-col items-center justify-center space-y-2",children:[(0,t.jsx)(d.A,{className:"h-8 w-8 text-primary"}),(0,t.jsx)(n.J,{htmlFor:"contacts",className:"font-medium",children:"Contact List"})]})}),(0,t.jsx)(c.Zp,{className:`cursor-pointer transition-all ${"import"===A?"border-primary":""}`,onClick:()=>O("import"),children:(0,t.jsxs)(c.Wu,{className:"p-4 flex flex-col items-center justify-center space-y-2",children:[(0,t.jsx)(o,{className:"h-8 w-8 text-primary"}),(0,t.jsx)(n.J,{htmlFor:"import",className:"font-medium",children:"Import File"})]})}),(0,t.jsx)(c.Zp,{className:`cursor-pointer transition-all ${"thirdparty"===A?"border-primary":""}`,onClick:()=>O("thirdparty"),children:(0,t.jsxs)(c.Wu,{className:"p-4 flex flex-col items-center justify-center space-y-2",children:[(0,t.jsx)(m.A,{className:"h-8 w-8 text-primary"}),(0,t.jsx)(n.J,{htmlFor:"thirdparty",className:"font-medium",children:"3rd Party App"})]})})]})]}),(0,t.jsxs)("div",{className:"space-y-4 mt-6",children:["contacts"===A&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)(n.J,{children:"Select Contact Sources"}),(0,t.jsx)("form",{onSubmit:e=>{e.preventDefault(),D(1),J(1,w)},children:(0,t.jsxs)("div",{className:"relative w-64",children:[(0,t.jsx)(u.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(r.p,{placeholder:"Search contacts...",className:"pl-8",value:w,onChange:e=>C(e.target.value)})]})})]}),f?(0,t.jsx)("div",{className:"text-center py-4",children:"Loading contacts..."}):0===s.length?(0,t.jsx)("div",{className:"text-center py-4 text-muted-foreground",children:0===s.length?"No contacts available. Please create contacts first.":"No contacts match your search."}):(0,t.jsxs)("div",{className:"border max-h-[300px] overflow-y-auto rounded-md mt-4",children:[(0,t.jsxs)(y.XI,{children:[(0,t.jsx)(y.A0,{children:(0,t.jsxs)(y.Hj,{children:[(0,t.jsx)(y.nd,{className:"w-12"}),(0,t.jsx)(y.nd,{children:"Name"}),(0,t.jsx)(y.nd,{children:"Phone Number"})]})}),(0,t.jsxs)(y.BF,{children:[s.map((e,a)=>(0,t.jsxs)(y.Hj,{className:"cursor-pointer hover:bg-muted",onClick:()=>M(e),children:[(0,t.jsx)(y.nA,{children:(0,t.jsx)("div",{className:`w-5 h-5 rounded-full border ${v.some(a=>a.contactName===e.contactName&&a.phoneNumber===e.phoneNumber)?"bg-primary border-primary":"border-gray-300"}`,children:v.some(a=>a.contactName===e.contactName&&a.phoneNumber===e.phoneNumber)&&(0,t.jsx)(h.A,{className:"h-4.5 w-4.5 text-white"})})}),(0,t.jsx)(y.nA,{className:"font-medium",children:e.contactName}),(0,t.jsx)(y.nA,{children:e.phoneNumber})]},`${e._id}-${a}`)),F&&(0,t.jsx)(y.Hj,{ref:z,className:"h-20",children:(0,t.jsx)(y.nA,{colSpan:7,children:(0,t.jsx)("div",{className:"flex items-center justify-center py-4",children:E?(0,t.jsx)(x.A,{className:"h-6 w-6 animate-spin text-primary"}):(0,t.jsx)("div",{className:"h-8"})})})})]})]}),k.contacts&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-2",children:k.contacts})]}),(0,t.jsx)("div",{className:"mt-4",children:(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Selected ",v.length," contacts"]})})]}),"import"===A&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(n.J,{children:"Upload Contact File"}),(0,t.jsx)(c.Zp,{className:"border-dashed border-2",children:(0,t.jsxs)(c.Wu,{className:"p-6 flex flex-col items-center justify-center space-y-4",children:[(0,t.jsx)(o,{className:"h-12 w-12 text-muted-foreground"}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"font-medium",children:"Drag and drop your file here"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Supports CSV and XLSX files"})]}),(0,t.jsx)(r.p,{type:"file",accept:".csv,.xlsx",className:"hidden",id:"file-upload",onChange:e=>{let s=e.target.files?.[0];s&&(console.log("File selected:",s.name),a({sources:[{_id:Date.now().toString(),contactName:s.name,phoneNumber:"",email:"",type:"file"}],sourceType:A}))}}),(0,t.jsx)(i.$,{variant:"outline",onClick:()=>document.getElementById("file-upload")?.click(),children:"Browse Files"})]})}),v.length>0&&"file"===v[0].type&&(0,t.jsxs)("div",{className:"mt-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium",children:"Selected file:"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:v[0].contactName})]})]}),"thirdparty"===A&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(n.J,{children:"Connect to Third-Party App"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:["Zapier","Encharge"].map(e=>(0,t.jsx)(c.Zp,{className:"cursor-pointer hover:border-primary transition-all",children:(0,t.jsxs)(c.Wu,{className:"p-4 flex flex-col items-center justify-center space-y-2",children:[(0,t.jsx)("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center",children:(0,t.jsx)("span",{className:"text-primary font-medium",children:e[0]})}),(0,t.jsx)("p",{className:"font-medium",children:e}),(0,t.jsx)(i.$,{variant:"outline",size:"sm",children:"Connect"})]})},e))})]})]})]})}},5522:(e,a,s)=>{s.d(a,{K_:()=>d,Nt:()=>m,SX:()=>i,am:()=>u,bi:()=>n,ge:()=>r,tm:()=>o,yi:()=>c});var t=s(6607);let l="http://localhost:4000";async function r(e){let a=e&&"all"!==e?`${l}/api/campaigns?status=${e}`:`${l}/api/campaigns`,s=await (0,t.t)(a);if(!s.ok)throw Error("Failed to fetch campaigns");return await s.json()}async function n(e){let a=await (0,t.t)(`${l}/api/campaigns`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok)throw Error("Failed to create campaign");return await a.json()}async function i(e,a){let s=await (0,t.t)(`${l}/api/campaigns/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!s.ok)throw Error("Failed to update campaign");return await s.json()}async function c(e,a){let s=await (0,t.t)(`${l}/api/campaigns/${e}/status`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:a})});if(!s.ok)throw Error("Failed to update campaign status");return await s.json()}async function d(e){if(!(await (0,t.t)(`${l}/api/campaigns/${e}`,{method:"DELETE"})).ok)throw Error("Failed to delete campaign")}async function o(e){let a=await (0,t.t)(`${l}/api/campaigns/${e}`);if(!a.ok)throw Error("Failed to fetch campaign");return await a.json()}async function m(e){let a=await (0,t.t)(`${l}/api/scheduled-call/remove-duplicates`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok)throw Error("Failed to remove duplicate calls");return await a.json()}async function u(e){let a=await (0,t.t)(`${l}/api/scheduled-call/reschedule-campaign`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok)throw Error("Failed to reschedule campaign calls");return await a.json()}},18116:(e,a,s)=>{s.d(a,{A:()=>i});var t=s(60687),l=s(43210),r=s(24851),n=s(4780);function i({className:e,defaultValue:a,value:s,min:i=0,max:c=100,...d}){let o=l.useMemo(()=>Array.isArray(s)?s:Array.isArray(a)?a:[i,c],[s,a,i,c]);return(0,t.jsxs)(r.bL,{"data-slot":"slider",defaultValue:a,value:s,min:i,max:c,className:(0,n.cn)("relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",e),...d,children:[(0,t.jsx)(r.CC,{"data-slot":"slider-track",className:(0,n.cn)("bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),children:(0,t.jsx)(r.Q6,{"data-slot":"slider-range",className:(0,n.cn)("bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full")})}),Array.from({length:o.length},(e,a)=>(0,t.jsx)(r.zi,{"data-slot":"slider-thumb",className:"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"},a))]})}},19352:(e,a,s)=>{s.d(a,{A:()=>m});var t=s(60687),l=s(43210),r=s(80013),n=s(32584),i=s(44493),c=s(5336),d=s(30474),o=s(76104);function m({data:e,updateData:a,loading:s,userRole:m}){let[u,h]=(0,l.useState)(e.agentId),x=e=>{h(e),a({agentId:e})},p=(0,l.useMemo)(()=>e.agents?"superadmin"===m?e.agents:e.agents.filter(e=>"active"===e.status):[],[e.agents,m]);return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(r.J,{children:"Select an agent for this campaign"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:"Choose one agent who will handle all the calls in this campaign"})]}),s?(0,t.jsx)("div",{className:"text-center py-8",children:"Loading agents..."}):e.agents&&0!==e.agents.length?(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:p.map(e=>(0,t.jsxs)(i.Zp,{onClick:()=>x(e.id),className:`border rounded-lg p-4 flex items-center gap-3 cursor-pointer transition-all ${u===e.id?"border-green-500 bg-green-50 dark:bg-green-900/20":"border-gray-200 dark:border-gray-700"} hover:border-green-300 dark:hover:border-green-600`,children:[(0,t.jsxs)(n.eu,{className:"h-12 w-12 flex-shrink-0",children:[e.avatar?(0,t.jsx)("img",{src:e.avatar,alt:`${e.name} avatar`,className:"object-cover h-full w-full"}):(0,t.jsx)(d.default,{src:o.A,alt:`${e.name} avatar`,width:64,height:64,className:"object-cover h-full w-full"}),(0,t.jsx)(n.q5,{className:"bg-gray-100",children:e.name.charAt(0)})]}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"text-sm font-medium truncate",children:e.name}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:e.role})]}),u===e.id&&(0,t.jsx)(c.A,{className:"h-5 w-5 text-green-500 flex-shrink-0"})]},e.id))}):(0,t.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"No agents available. Please create agents first."}),u&&e.agents&&(0,t.jsx)("div",{className:"mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-md border border-green-200 dark:border-green-800",children:(0,t.jsxs)("p",{className:"text-sm font-medium text-green-800 dark:text-green-400",children:["Selected agent: ",e.agents.find(e=>e.id===u)?.name]})})]})}},26373:(e,a,s)=>{s.d(a,{V:()=>d});var t=s(60687);s(43210);var l=s(47033),r=s(14952),n=s(2438),i=s(4780),c=s(29523);function d({className:e,classNames:a,showOutsideDays:s=!0,...d}){return(0,t.jsx)(n.hv,{showOutsideDays:s,className:(0,i.cn)("p-3",e),classNames:{months:"flex flex-col sm:flex-row gap-2",month:"flex flex-col gap-4",caption:"flex justify-center pt-1 relative items-center w-full",caption_label:"text-sm font-medium",nav:"flex items-center gap-1",nav_button:(0,i.cn)((0,c.r)({variant:"outline"}),"size-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-x-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:(0,i.cn)("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md","range"===d.mode?"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md":"[&:has([aria-selected])]:rounded-md"),day:(0,i.cn)((0,c.r)({variant:"ghost"}),"size-8 p-0 font-normal aria-selected:opacity-100"),day_range_start:"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground",day_range_end:"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...a},components:{IconLeft:({className:e,...a})=>(0,t.jsx)(l.A,{className:(0,i.cn)("size-4",e),...a}),IconRight:({className:e,...a})=>(0,t.jsx)(r.A,{className:(0,i.cn)("size-4",e),...a})},...d})}},44493:(e,a,s)=>{s.d(a,{BT:()=>c,Wu:()=>d,ZB:()=>i,Zp:()=>r,aR:()=>n,wL:()=>o});var t=s(60687);s(43210);var l=s(4780);function r({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card",className:(0,l.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",e),...a})}function n({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,l.cn)("flex flex-col gap-1.5 px-6",e),...a})}function i({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,l.cn)("leading-none font-semibold",e),...a})}function c({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,l.cn)("text-muted-foreground text-sm",e),...a})}function d({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,l.cn)("px-6",e),...a})}function o({className:e,...a}){return(0,t.jsx)("div",{"data-slot":"card-footer",className:(0,l.cn)("flex items-center px-6",e),...a})}},47342:(e,a,s)=>{s.d(a,{A:()=>t});let t=(0,s(62688).A)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},50812:(e,a,s)=>{s.d(a,{C:()=>c,z:()=>i});var t=s(60687);s(43210);var l=s(14555),r=s(65822),n=s(4780);function i({className:e,...a}){return(0,t.jsx)(l.bL,{"data-slot":"radio-group",className:(0,n.cn)("grid gap-3",e),...a})}function c({className:e,...a}){return(0,t.jsx)(l.q7,{"data-slot":"radio-group-item",className:(0,n.cn)("border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,t.jsx)(l.C1,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,t.jsx)(r.A,{className:"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2"})})})}},56896:(e,a,s)=>{s.d(a,{S:()=>i});var t=s(60687);s(43210);var l=s(25112),r=s(13964),n=s(4780);function i({className:e,...a}){return(0,t.jsx)(l.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,t.jsx)(l.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(r.A,{className:"size-3.5"})})})}},61502:(e,a,s)=>{s.d(a,{A:()=>d});var t=s(60687),l=s(15079),r=s(89667),n=s(80013),i=s(18116),c=s(50812);function d({data:e,updateData:a}){let s=e.recallHours||24,d=e.maxRecalls||3,o=e.concurrentCalls||10,m=e.instantCall||!1,u=e.batchIntervalMinutes||3;return(0,t.jsxs)("div",{className:"space-y-10 ",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(n.J,{className:"text-base font-medium",children:"Call Settings"}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,t.jsx)(n.J,{htmlFor:"concurrent-calls",children:"Concurrent Calls"}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:o})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Set the maximum number of simultaneous calls for this campaign"}),(0,t.jsx)(i.A,{id:"concurrent-calls",min:10,max:100,step:10,value:[o],onValueChange:e=>{a({concurrentCalls:e[0]})}}),(0,t.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground",children:[(0,t.jsx)("span",{children:"10"}),(0,t.jsx)("span",{children:"20"}),(0,t.jsx)("span",{children:"30"}),(0,t.jsx)("span",{children:"40"}),(0,t.jsx)("span",{children:"50"}),(0,t.jsx)("span",{children:"60"}),(0,t.jsx)("span",{children:"70"}),(0,t.jsx)("span",{children:"80"}),(0,t.jsx)("span",{children:"90"}),(0,t.jsx)("span",{children:"100"})]}),(0,t.jsxs)("div",{className:"text-center font-medium",children:[e.concurrentCalls," ",1===e.concurrentCalls?"call":"calls"," at a time"]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(n.J,{htmlFor:"batch-interval",children:"Batch Interval (minutes)"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Set the time interval between batches of scheduled calls"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 w-32",children:[(0,t.jsx)(r.p,{id:"batch-interval",type:"number",min:"1",max:"60",value:u,onChange:e=>{let s=parseInt(e.target.value);!isNaN(s)&&s>0&&a({batchIntervalMinutes:s})},className:"text-center"}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"minutes"})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(n.J,{className:"text-base font-medium",children:"Recall Settings"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2 flex flex-col items-center text-center",children:[(0,t.jsx)(n.J,{htmlFor:"recall-hours",children:"Hours Between Recalls"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 w-32",children:[(0,t.jsx)(r.p,{id:"recall-hours",type:"number",min:"1",value:s,onChange:e=>{let s=parseInt(e.target.value);!isNaN(s)&&s>0&&a({recallHours:s})},className:"text-center"}),(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"hours"})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:"Time to wait before attempting to recall unanswered contacts"})]}),(0,t.jsxs)("div",{className:"space-y-2 flex flex-col items-center text-center",children:[(0,t.jsx)(n.J,{htmlFor:"max-recalls",children:"Maximum Recall Attempts"}),(0,t.jsxs)(l.l6,{value:d.toString(),onValueChange:e=>{a({maxRecalls:parseInt(e)})},children:[(0,t.jsx)(l.bq,{id:"max-recalls",className:"text-center",children:(0,t.jsx)(l.yv,{placeholder:"Select maximum recalls"})}),(0,t.jsxs)(l.gC,{children:[(0,t.jsx)(l.eb,{value:"1",children:"1 Attempt"}),(0,t.jsx)(l.eb,{value:"2",children:"2 Attempts"}),(0,t.jsx)(l.eb,{value:"3",children:"3 Attempts"}),(0,t.jsx)(l.eb,{value:"4",children:"4 Attempts"}),(0,t.jsx)(l.eb,{value:"5",children:"5 Attempts"}),(0,t.jsx)(l.eb,{value:"6",children:"6 Attempts"}),(0,t.jsx)(l.eb,{value:"7",children:"7 Attempts"}),(0,t.jsx)(l.eb,{value:"8",children:"8 Attempts"}),(0,t.jsx)(l.eb,{value:"9",children:"9 Attempts"}),(0,t.jsx)(l.eb,{value:"10",children:"10 Attempts"})]})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:"Number of times to retry reaching a contact"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(n.J,{className:"text-base font-medium",children:"Instant Call"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Enable instant calling to immediately connect with contacts when the campaign is active"}),(0,t.jsxs)(c.z,{value:m?"yes":"no",onValueChange:e=>{a({instantCall:"yes"===e})},className:"flex space-x-8",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.C,{value:"yes",id:"instant-call-yes"}),(0,t.jsx)(n.J,{htmlFor:"instant-call-yes",children:"Yes"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(c.C,{value:"no",id:"instant-call-no"}),(0,t.jsx)(n.J,{htmlFor:"instant-call-no",children:"No"})]})]})]})]})}},76104:(e,a,s)=>{s.d(a,{A:()=>t});let t={src:"/_next/static/media/Binghatti-Lisa.85c81ecb.jpeg",height:1586,width:1586,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/2wBDAQoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/wgARCAAIAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAX/xAAUAQEAAAAAAAAAAAAAAAAAAAAC/9oADAMBAAIQAxAAAACeA//EABsQAAEFAQEAAAAAAAAAAAAAAAECAwQREwAi/9oACAEBAAE/AG6e3khuoyZAbQNDWYR6SO//xAAVEQEBAAAAAAAAAAAAAAAAAAABAP/aAAgBAgEBPwAL/8QAFhEAAwAAAAAAAAAAAAAAAAAAAAFB/9oACAEDAQE/AHD/2Q==",blurWidth:8,blurHeight:8}},80013:(e,a,s)=>{s.d(a,{J:()=>n});var t=s(60687);s(43210);var l=s(78148),r=s(4780);function n({className:e,...a}){return(0,t.jsx)(l.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}},84925:(e,a,s)=>{s.d(a,{A:()=>f});var t=s(60687),l=s(43210),r=s(29523),n=s(80013),i=s(40988),c=s(26373),d=s(89667),o=s(63503),m=s(56896),u=s(18229),h=s(40228);let x=(0,s(62688).A)("Settings2",[["path",{d:"M20 7h-9",key:"3s1dr2"}],["path",{d:"M14 17H5",key:"gfn3mx"}],["circle",{cx:"17",cy:"17",r:"3",key:"18b49y"}],["circle",{cx:"7",cy:"7",r:"3",key:"dfmy0x"}]]);var p=s(15256),y=s(23328);let j=[{label:"Mon",value:"monday"},{label:"Tue",value:"tuesday"},{label:"Wed",value:"wednesday"},{label:"Thu",value:"thursday"},{label:"Fri",value:"friday"},{label:"Sat",value:"saturday"},{label:"Sun",value:"sunday"}];function f({data:e,updateData:a}){let[s,f]=(0,l.useState)(()=>{if(e.startDate){let a=new Date(e.startDate);return isNaN(a.getTime())?new Date:a}return new Date}),[g,v]=(0,l.useState)(e.callSchedule?.timezone||"America/New_York"),[N,A]=(0,l.useState)(e.callSchedule?.callTime||"09:00"),[b,w]=(0,l.useState)(()=>{if(e.endDate){let a=new Date(e.endDate);return isNaN(a.getTime())?new Date(Date.now()+6048e5):a}return new Date(Date.now()+6048e5)}),[C,k]=(0,l.useState)(e.callSchedule?.startTime||"09:00"),[S,T]=(0,l.useState)(e.callSchedule?.endTime||"17:00"),[D,F]=(0,l.useState)(!1),[$,E]=(0,l.useState)(null),[W,z]=(0,l.useState)(e.followUpDays||["monday","tuesday","wednesday","thursday","friday"]),[J,M]=(0,l.useState)(e.callSchedule?.callTime||"09:00"),[O,I]=(0,l.useState)(!!e.endDate),[P,_]=(0,l.useState)(e.callWindow?.daysOfWeek||["monday","tuesday","wednesday","thursday","friday"]),[B,Q]=(0,l.useState)(e.callWindow?.startTime||"09:00"),[L,R]=(0,l.useState)(e.callWindow?.endTime||"17:00"),U=e.followUpDays||["monday","tuesday","wednesday","thursday","friday"],H=e=>{let a=new Date,s=new Date;s.setDate(s.getDate()+e),f(a),w(s),E(e),O||I(!0),Z(a,s,C,S)},Z=(s,t,l,r)=>{a({startDate:(0,p.GP)(s,"yyyy-MM-dd'T'HH:mm"),endDate:t?(0,p.GP)(t,"yyyy-MM-dd'T'HH:mm"):null,callSchedule:{startTime:l,endTime:r,timezone:g,daysOfWeek:e.callSchedule?.daysOfWeek||["monday","tuesday","wednesday","thursday","friday"],callTime:N}})},G=(e,a)=>{z(s=>a?[...s,e]:s.filter(a=>a!==e))},V=e=>{let s=[];"weekdays"===e?s=["monday","tuesday","wednesday","thursday","friday"]:"weekend"===e?s=["saturday","sunday"]:"everyday"===e&&(s=["monday","tuesday","wednesday","thursday","friday","saturday","sunday"]),_(s),a({callWindow:{startTime:B,endTime:L,daysOfWeek:s}})},X=e=>{"weekdays"===e?z(["monday","tuesday","wednesday","thursday","friday"]):"weekend"===e?z(["saturday","sunday"]):"everyday"===e&&z(["monday","tuesday","wednesday","thursday","friday","saturday","sunday"])},q=(e,s)=>{let t=s?[...P,e].filter((e,a,s)=>s.indexOf(e)===a):P.filter(a=>a!==e);_(t),a({callWindow:{startTime:B,endTime:L,daysOfWeek:t}})};return(0,t.jsxs)("div",{className:"space-y-10",children:[(0,t.jsxs)("div",{className:"space-y-8 ",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)(n.J,{className:"text-base font-medium",children:"Campaign Duration"}),(0,t.jsx)("div",{className:"flex ",children:O&&(0,t.jsx)(y.default,{duration:.5,delay:.1,children:(0,t.jsx)("div",{className:"flex gap-2",children:[{label:"1 Week",value:7},{label:"2 Weeks",value:14},{label:"1 Month",value:30},{label:"3 Months",value:90}].map(e=>(0,t.jsx)(r.$,{variant:$===e.value?"default":"outline",size:"sm",onClick:()=>H(e.value),children:e.label},e.value))})})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(n.J,{htmlFor:"timezone",children:"Timezone"}),(0,t.jsx)("div",{className:"flex space-x-2",children:(0,t.jsx)(u.N,{value:g,onChange:s=>{v(s),a({callSchedule:{startTime:e.callSchedule?.startTime||"09:00",endTime:e.callSchedule?.endTime||"17:00",timezone:s,daysOfWeek:e.callSchedule?.daysOfWeek||["monday","tuesday","wednesday","thursday","friday"],callTime:N}})}})})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(n.J,{children:"Start Date & Time"}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(i.AM,{children:[(0,t.jsx)(i.Wv,{asChild:!0,children:(0,t.jsxs)(r.$,{variant:"outline",className:"flex-1 justify-start text-left font-normal",children:[(0,t.jsx)(h.A,{className:"mr-2 h-4 w-4"}),s?(0,p.GP)(s,"PPP"):"Select date"]})}),(0,t.jsx)(i.hl,{className:"w-auto p-0",align:"start",children:(0,t.jsx)(c.V,{mode:"single",selected:s,onSelect:e=>{e&&(f(e),E(null),O?Z(e,b,C,S):a({endDate:null}))},initialFocus:!0})})]}),(0,t.jsx)("div",{className:"relative",children:(0,t.jsx)(d.p,{type:"time",value:C,onChange:e=>{let a=e.target.value;a&&a.includes(":")||(a="00:00"),k(a),Z(s,b,a,S)},className:"w-24"})})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(m.S,{id:"hasEndDate",checked:O,onCheckedChange:t=>{I(t),t?Z(s,b,C,S):a({endDate:null,callSchedule:{startTime:C,endTime:S,timezone:g,daysOfWeek:e.callSchedule?.daysOfWeek||["monday","tuesday","wednesday","thursday","friday"],callTime:N}})},className:"cursor-pointer"}),(0,t.jsx)(n.J,{htmlFor:"hasEndDate",className:"cursor-pointer",children:"Set End Date & Time"})]}),O?(0,t.jsxs)("div",{className:"flex space-x-2 mt-2",children:[(0,t.jsxs)(i.AM,{children:[(0,t.jsx)(i.Wv,{asChild:!0,children:(0,t.jsxs)(r.$,{variant:"outline",className:"flex-1 justify-start text-left font-normal",children:[(0,t.jsx)(h.A,{className:"mr-2 h-4 w-4"}),b?(0,p.GP)(b,"PPP"):"Select date"]})}),(0,t.jsx)(i.hl,{className:"w-auto p-0",align:"start",children:(0,t.jsx)(c.V,{mode:"single",selected:b,onSelect:e=>{e&&(w(e),E(null),Z(s,e,C,S))},initialFocus:!0})})]}),(0,t.jsx)("div",{className:"relative",children:(0,t.jsx)(d.p,{type:"time",value:S,onChange:e=>{let a=e.target.value;a&&a.includes(":")||(a="00:00"),T(a),Z(s,b,C,a)},className:"w-24"})})]}):(0,t.jsx)("div",{className:"text-sm text-muted-foreground mt-2",children:"Campaign will run indefinitely"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-6 mt-5",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center ",children:[(0,t.jsx)(n.J,{className:"text-base font-medium",children:"Call Window"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>V("weekdays"),children:"Weekdays"}),(0,t.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>V("weekend"),children:"Weekend"}),(0,t.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>V("everyday"),children:"Every Day"})]})]}),(0,t.jsxs)("div",{className:"space-y-4 border rounded-md p-4 bg-muted/30",children:[(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)(n.J,{children:"Days of the Week"}),(0,t.jsx)("div",{className:"grid grid-cols-7 gap-2",children:j.map(e=>(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-1.5",children:[(0,t.jsx)(m.S,{id:`window-${e.value}`,checked:P.includes(e.value),onCheckedChange:a=>q(e.value,a),className:"h-6 w-6 cursor-pointer"}),(0,t.jsx)(n.J,{htmlFor:`window-${e.value}`,className:"text-xs cursor-pointer",children:e.label})]},`window-${e.value}`))})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(n.J,{children:"Start Time"}),(0,t.jsx)(d.p,{type:"time",value:B,onChange:e=>{let s=e.target.value;Q(s),a({callWindow:{startTime:s,endTime:L,daysOfWeek:P}})}})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(n.J,{children:"End Time"}),(0,t.jsx)(d.p,{type:"time",value:L,onChange:e=>{let s=e.target.value;R(s),a({callWindow:{startTime:B,endTime:s,daysOfWeek:P}})}})]})]}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["Calls will only be made during these hours on the selected days in the ",g," timezone."]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)(n.J,{className:"text-base font-medium",children:"Follow-up Schedule"}),(0,t.jsxs)(o.lG,{open:D,onOpenChange:F,children:[(0,t.jsx)(o.zM,{asChild:!0,children:(0,t.jsxs)(r.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(x,{className:"mr-2 h-4 w-4"}),"Configure Recurring"]})}),(0,t.jsxs)(o.Cf,{children:[(0,t.jsx)(o.c7,{children:(0,t.jsx)(o.L3,{children:"Configure Recurring Schedule"})}),(0,t.jsxs)("div",{className:"space-y-4 py-4",children:[(0,t.jsxs)("div",{className:"space-y-7",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)(n.J,{children:"Days of the Week"}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>X("weekdays"),children:"Weekdays"}),(0,t.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>X("weekend"),children:"Weekend"}),(0,t.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>X("everyday"),children:"Every Day"})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-7 gap-2",children:j.map(e=>(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-1.5",children:[(0,t.jsx)(m.S,{id:e.value,checked:W.includes(e.value),onCheckedChange:a=>G(e.value,a),className:"h-6 w-6"}),(0,t.jsx)(n.J,{htmlFor:e.value,className:"text-xs cursor-pointer",children:e.label})]},e.value))})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(n.J,{children:"Call Time"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(d.p,{type:"time",value:J,onChange:e=>{let a=e.target.value;a&&a.includes(":")||(a="00:00"),M(a)},className:"w-22"}),(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:["in ",g]})]}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Calls will be initiated at this time on the selected days."})]})]}),(0,t.jsxs)(o.Es,{children:[(0,t.jsx)(r.$,{variant:"outline",onClick:()=>{z(U),M(N),F(!1)},children:"Cancel"}),(0,t.jsx)(r.$,{onClick:()=>{a({followUpDays:W,callSchedule:{startTime:e.callSchedule?.startTime||"09:00",endTime:e.callSchedule?.endTime||"17:00",timezone:g,daysOfWeek:e.callSchedule?.daysOfWeek||["monday","tuesday","wednesday","thursday","friday"],callTime:J}}),A(J),F(!1)},children:"Save"})]})]})]})]}),(0,t.jsxs)("div",{className:"p-4 border rounded-md bg-muted/50",children:[(0,t.jsx)("div",{className:"text-sm font-medium mb-2",children:(()=>{if(7===U.length)return"Occurs every day";if(0===U.length)return"No recurrence set";if(5===U.length&&U.includes("monday")&&U.includes("tuesday")&&U.includes("wednesday")&&U.includes("thursday")&&U.includes("friday"))return"Occurs every weekday";{if(2===U.length&&U.includes("saturday")&&U.includes("sunday"))return"Occurs every weekend";let e=U.map(e=>e.charAt(0).toUpperCase()+e.slice(1));return`Occurs every ${e.join(", ")}`}})()}),(0,t.jsxs)("div",{className:"mt-2 text-sm",children:["Calls scheduled for (",g,")"]})]})]})]})}}};