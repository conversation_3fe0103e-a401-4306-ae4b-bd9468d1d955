(()=>{var e={};e.id=4716,e.ids=[4716],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3710:(e,t,r)=>{"use strict";r.d(t,{M:()=>h});var n=r(60687),i=r(43210),a=r(11860),s=r(41862),o=r(27900),l=r(76104),c=r(30474),u=r(29523),d=r(89667);function h({agent:e,isOpen:t,onClose:r}){let[h,p]=(0,i.useState)([]),[f,m]=(0,i.useState)(""),[g,v]=(0,i.useState)(!1),y=["I understand what you're saying. Let me help you with that.","That's interesting! Could you tell me more?","I'm processing your request. Here's what I think...","Based on what you've told me, I would suggest...","Let me check that for you quickly."],b=async e=>{if(!e.trim())return;let t={id:Date.now().toString(),content:e,sender:"user",timestamp:new Date};p(e=>[...e,t]),m(""),v(!0),setTimeout(()=>{let e=y[Math.floor(Math.random()*y.length)],t={id:(Date.now()+1).toString(),content:e,sender:"agent",timestamp:new Date};p(e=>[...e,t]),v(!1)},1e3)};return(0,i.useState)(()=>{t&&0===h.length&&p([{id:"initial",content:`Hello! My name is ${e?.name}. How can I assist you today?`,sender:"agent",timestamp:new Date}])}),(0,n.jsxs)("div",{className:`fixed right-0 top-19 h-[90vh] border-2 w-96 bg-white dark:bg-gray-800 shadow-xl transition-all duration-200 ${t?"opacity-100 visible":"opacity-0 invisible"} z-50 rounded-lg`,children:[(0,n.jsxs)("div",{className:"border-b p-4 flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsx)("div",{className:"relative h-10 w-10 rounded-full overflow-hidden",children:e?.avatar?(0,n.jsx)("img",{src:e.avatar,alt:`${e.name} avatar`,className:"h-full w-full object-cover"}):(0,n.jsx)(c.default,{src:l.A,alt:`${e?.name} avatar`,className:"object-cover",fill:!0})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"font-semibold",children:e?.name}),(0,n.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:e?.role})]})]}),(0,n.jsx)(u.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:r,children:(0,n.jsx)(a.A,{className:"h-4 w-4"})})]}),(0,n.jsxs)("div",{className:"h-[calc(100%-8rem)] overflow-y-auto p-4 space-y-4",children:[h.map(e=>(0,n.jsx)("div",{className:`flex ${"user"===e.sender?"justify-end":"justify-start"}`,children:(0,n.jsx)("div",{className:`max-w-[80%] rounded-lg p-3 ${"user"===e.sender?"bg-primary text-primary-foreground":"bg-gray-100 dark:bg-gray-700"}`,children:e.content})},e.id)),g&&(0,n.jsx)("div",{className:"flex justify-start",children:(0,n.jsx)("div",{className:"bg-gray-100 dark:bg-gray-700 rounded-lg p-3",children:(0,n.jsx)(s.A,{className:"h-4 w-4 animate-spin"})})})]}),(0,n.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-4 bg-white dark:bg-gray-800 border-t",children:(0,n.jsxs)("form",{onSubmit:e=>{e.preventDefault(),b(f)},className:"flex gap-2",children:[(0,n.jsx)(d.p,{value:f,onChange:e=>m(e.target.value),placeholder:"Type a message...",className:"flex-1"}),(0,n.jsx)(u.$,{type:"submit",size:"icon",children:(0,n.jsx)(o.A,{className:"h-4 w-4"})})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},17908:(e,t,r)=>{"use strict";r.d(t,{a:()=>o});var n=r(8693),i=r(51423),a=r(54050);async function s(){let e=localStorage.getItem("access_token"),t=await fetch("http://localhost:4000/api/agents",{headers:{Authorization:`Bearer ${e}`}});if(!t.ok)throw Error("Failed to fetch agents");return t.json()}function o(){let e=(0,n.jE)(),{data:t=[],isLoading:r,error:o}=(0,i.I)({queryKey:["agents"],queryFn:s,staleTime:1/0,gcTime:1/0}),{mutate:l}=(0,a.n)({mutationFn:e=>Promise.resolve(e),onSuccess:t=>{e.setQueryData(["agents"],t)}});return{agents:t,setAgents:l,agentsisLoading:r,deleteAgentMutation:(0,a.n)({mutationFn:async e=>{let t=localStorage.getItem("access_token");if(!t)throw Error("Authentication required");let r=await fetch(`http://localhost:4000/api/agents/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`}});if(!r.ok)throw Error(`Failed to delete agent: ${r.status}`)},onMutate:async t=>{await e.cancelQueries({queryKey:["agents"]});let r=e.getQueryData(["agents"]);return e.setQueryData(["agents"],e=>e?.filter(e=>e.id!==t)??[]),{previousAgents:r}},onSuccess:(t,r)=>{e.invalidateQueries({queryKey:["agents"]}),e.removeQueries({queryKey:["agent",r]})}}),AgentsError:o instanceof Error?o.message:null}}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26675:function(e,t,r){"use strict";var n=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let i=n(r(38500)),a=n(r(94735)),s=r(62010);async function o(e,t){e.muted=!1,e.autoplay=!0,null!=t&&(e.srcObject=new MediaStream([t]),await e.play())}async function l(e,t){let r=document.createElement("audio");return r.dataset.participantId=t,document.body.appendChild(r),await o(r,e),r}class c extends a.default{on(e,t){return super.on(e,t),this}once(e,t){return super.once(e,t),this}emit(e,...t){return super.emit(e,...t)}removeListener(e,t){return super.removeListener(e,t),this}removeAllListeners(e){return super.removeAllListeners(e),this}}class u extends c{started=!1;call=null;speakingTimeout=null;dailyCallConfig={};dailyCallObject={};hasEmittedCallEndedStatus=!1;constructor(e,t,r,n){super(),s.client.baseUrl=t??"https://api.vapi.ai",s.client.setSecurityData(e),this.dailyCallConfig=r??{},this.dailyCallObject=n??{}}cleanup(){this.started=!1,this.hasEmittedCallEndedStatus=!1,this.call?.destroy(),this.call=null,this.speakingTimeout=null}isMobileDevice(){if("undefined"==typeof navigator)return!1;let e=navigator.userAgent;return/android|iphone|ipad|ipod|iemobile|blackberry|bada/i.test(e.toLowerCase())}async sleep(e){return new Promise(t=>setTimeout(t,e))}async start(e,t,r,n){if(!e&&!r&&!n)throw Error("Assistant or Squad or Workflow must be provided.");if(this.started)return null;this.started=!0;try{let a=(await s.client.call.callControllerCreateWebCall({assistant:"string"==typeof e?void 0:e,assistantId:"string"==typeof e?e:void 0,assistantOverrides:t,squad:"string"==typeof r?void 0:r,squadId:"string"==typeof r?r:void 0,workflow:"string"==typeof n?void 0:n,workflowId:"string"==typeof n?n:void 0})).data;this.call&&this.cleanup();let o=a?.artifactPlan?.videoRecordingEnabled??!1,c=a?.assistant?.voice?.provider==="tavus";if(this.call=i.default.createCallObject({audioSource:this.dailyCallObject.audioSource??!0,videoSource:this.dailyCallObject.videoSource??o,dailyConfig:this.dailyCallConfig}),this.call.iframe()?.style.setProperty("display","none"),this.call.on("left-meeting",()=>{this.emit("call-end"),this.hasEmittedCallEndedStatus||(this.emit("message",{type:"status-update",status:"ended",endedReason:"customer-ended-call"}),this.hasEmittedCallEndedStatus=!0),o&&this.call?.stopRecording(),this.cleanup()}),this.call.on("error",e=>{this.emit("error",e),o&&this.call?.stopRecording()}),this.call.on("camera-error",e=>{this.emit("error",e)}),this.call.on("track-started",async e=>{!(!e||!e.participant||e.participant?.local)&&e.participant?.user_name==="Vapi Speaker"&&("video"===e.track.kind&&this.emit("video",e.track),"audio"===e.track.kind&&await l(e.track,e.participant.session_id),this.call?.sendAppMessage("playable"))}),this.call.on("participant-joined",e=>{if(e&&this.call){var t;t=this.call,e.participant.local||t.updateParticipant(e.participant.session_id,{setSubscribedTracks:{audio:!0,video:o||c}})}}),this.call.on("participant-updated",e=>{e&&this.emit("daily-participant-updated",e.participant)}),this.call.on("participant-left",e=>{e&&!function(e){let t=document.querySelector(`audio[data-participant-id="${e}"]`);t?.remove()}(e.participant.session_id)}),this.isMobileDevice()&&await this.sleep(1e3),await this.call.join({url:a.webCallUrl,subscribeToTracksAutomatically:!1}),o){let e=new Date().getTime();this.call.startRecording({width:1280,height:720,backgroundColor:"#FF1F2D3D",layout:{preset:"default"}}),this.call.on("recording-started",()=>{this.send({type:"control",control:"say-first-message",videoRecordingStartDelaySeconds:(new Date().getTime()-e)/1e3})})}return this.call.startRemoteParticipantsAudioLevelObserver(100),this.call.on("remote-participants-audio-level",e=>{e&&this.handleRemoteParticipantsAudioLevel(e)}),this.call.on("app-message",e=>this.onAppMessage(e)),this.call.on("nonfatal-error",e=>{e?.type==="audio-processor-error"&&this.call?.updateInputSettings({audio:{processor:{type:"none"}}}).then(()=>{this.call?.setLocalAudio(!0)})}),this.call.updateInputSettings({audio:{processor:{type:"noise-cancellation"}}}),a}catch(e){return console.error(e),this.emit("error",e),this.cleanup(),null}}onAppMessage(e){if(e)try{if("listening"===e.data)return this.emit("call-start");try{let t=JSON.parse(e.data);this.emit("message",t),t&&"type"in t&&"status"in t&&"status-update"===t.type&&"ended"===t.status&&(this.hasEmittedCallEndedStatus=!0)}catch(e){console.log("Error parsing message data: ",e)}}catch(e){console.error(e)}}handleRemoteParticipantsAudioLevel(e){let t=Object.values(e.participantsAudioLevel).reduce((e,t)=>e+t,0);this.emit("volume-level",Math.min(1,t/.15)),t>.01&&(this.speakingTimeout?(clearTimeout(this.speakingTimeout),this.speakingTimeout=null):this.emit("speech-start"),this.speakingTimeout=setTimeout(()=>{this.emit("speech-end"),this.speakingTimeout=null},1e3))}stop(){this.started=!1,this.call?.destroy(),this.call=null}send(e){this.call?.sendAppMessage(JSON.stringify(e))}setMuted(e){if(!this.call)throw Error("Call object is not available.");this.call.setLocalAudio(!e)}isMuted(){return!!this.call&&!1===this.call.localAudio()}say(e,t,r){this.send({type:"say",message:e,endCallAfterSpoken:t,interruptionsEnabled:r??!1})}setInputDevicesAsync(e){this.call?.setInputDevicesAsync(e)}async increaseMicLevel(e){if(!this.call)throw Error("Call object is not available.");try{let t=await navigator.mediaDevices.getUserMedia({audio:!0}),r=new AudioContext,n=r.createMediaStreamSource(t),i=r.createGain();i.gain.value=e,n.connect(i);let a=r.createMediaStreamDestination();i.connect(a);let[s]=a.stream.getAudioTracks();await this.call.setInputDevicesAsync({audioSource:s})}catch(e){console.error("Error adjusting microphone level:",e)}}setOutputDeviceAsync(e){this.call?.setOutputDeviceAsync(e)}getDailyCallObject(){return this.call}startScreenSharing(e,t){this.call?.startScreenShare({displayMediaOptions:e,screenVideoSendSettings:t})}stopScreenSharing(){this.call?.stopScreenShare()}}t.default=u},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29014:(e,t,r)=>{"use strict";r.d(t,{default:()=>R});var n=r(60687),i=r(43210),a=r(29523),s=r(89667),o=r(96474),l=r(99270),c=r(41862),u=r(57175),d=r(91391),h=r(58887),p=r(38894),f=r(21235),m=r(96834),g=r(23328),v=r(21342),y=r(81904),b=r(88233),_=r(76104),S=r(30474),w=r(16189),k=r(85814),E=r.n(k),C=r(93500),A=r(76242),T=r(87979),M=r(17908),x=r(95534),O=r(3710),j=r(63503),P=r(52581),I=r(62688);let N=(0,I.A)("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]),L=(0,I.A)("MicOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M18.89 13.23A7.12 7.12 0 0 0 19 12v-2",key:"80xlxr"}],["path",{d:"M5 10v2a7 7 0 0 0 12 5",key:"p2k8kg"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);function D({isOpen:e,onClose:t,agent:r}){let[s,o]=(0,i.useState)(!1),[l,u]=(0,i.useState)(!1),[d,h]=(0,i.useState)(null),[p,f]=(0,i.useState)(!1),[m,g]=(0,i.useState)(""),[v,y]=(0,i.useState)(null),b=async()=>{if(r&&v){if(s)v.stop(),o(!1);else{u(!0),h(null);try{(await navigator.mediaDevices.getUserMedia({audio:!0})).getTracks().forEach(e=>e.stop()),await v.start({assistantId:r.id,audioConfig:{sampleRate:16e3,channelCount:1},transcriber:{language:r.transcriber?.language||"en-US"}})}catch(e){console.error("Web call error details:",{message:e.message,stack:e.stack,name:e.name}),"NotAllowedError"===e.name?h("Microphone permission denied. Please enable microphone access."):navigator.onLine?h(e.message||"Failed to start web call. Please try again."):h("No internet connection. Please check your network."),P.o.error("Failed to start web call"),u(!1)}}}};return(0,n.jsx)(j.lG,{open:e,onOpenChange:e=>{e||(s&&v?.stop(),h(null),g("")),t()},children:(0,n.jsxs)(j.Cf,{className:"sm:max-w-[425px]",children:[(0,n.jsxs)(j.c7,{children:[(0,n.jsxs)(j.L3,{children:["Web Call with ",r?.name]}),(0,n.jsx)(j.rr,{children:s?"Call in progress":"Start a web call with your assistant"})]}),(0,n.jsxs)("div",{className:"py-4",children:[!s&&(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:"Make sure your microphone is connected and you have granted browser permissions."}),s&&m&&(0,n.jsxs)("div",{className:"mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-md",children:[(0,n.jsx)("p",{className:"text-sm font-medium mb-1",children:"Last Response:"}),(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:m})]}),d&&(0,n.jsx)("div",{className:"mb-4 text-sm text-red-500 bg-red-50 dark:bg-red-900/20 p-3 rounded-md",children:d}),(0,n.jsxs)("div",{className:"mt-6 flex justify-end gap-3",children:[(0,n.jsx)(a.$,{variant:"outline",onClick:t,disabled:l,children:"Cancel"}),(0,n.jsx)(a.$,{onClick:b,disabled:l,className:`${s?"bg-red-600 hover:bg-red-700":"bg-black hover:bg-gray-800 dark:bg-white dark:text-black"} text-white`,children:l?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(c.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Starting..."]}):s?(0,n.jsxs)(n.Fragment,{children:[p?(0,n.jsx)(N,{className:"mr-2 h-4 w-4"}):(0,n.jsx)(L,{className:"mr-2 h-4 w-4"}),"End Call"]}):"Start Call"})]})]})]})})}function R(){let[e,t]=(0,i.useState)(null),[r,k]=(0,i.useState)(!1),[j,P]=(0,i.useState)(!1),[I,N]=(0,i.useState)(!1),[L,R]=(0,i.useState)(!1),[F,q]=(0,i.useState)(null),[V,U]=(0,i.useState)(!1),[$,B]=(0,i.useState)(""),J=(0,w.useRouter)(),{userRole:G,authIsLoading:Y,authError:z}=(0,T.A)(),{agents:W,agentsisLoading:Q,AgentsError:H,deleteAgentMutation:K}=(0,M.a)(),X=async()=>{if(F)try{await K.mutateAsync(F.id),R(!1)}catch(e){console.error("Failed to delete agent:",e)}finally{q(null)}},Z=e=>({"en-US":"English (US)","en-GB":"English (UK)",en:"English","fr-FR":"French",fr:"French","es-ES":"Spanish","de-DE":"German","it-IT":"Italian","ar-SA":"Arabic"})[e]||e,ee=(0,i.useMemo)(()=>{let e=W.filter(e=>e.name.toLowerCase().includes($.toLowerCase()));return"superadmin"===G?e:e.filter(e=>"active"===e.status)},[W,$,G]);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold",children:"AI Agents"}),(0,n.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Manage and interact with your assistants"})]}),"superadmin"===G&&(0,n.jsx)(E(),{href:"/agents/create",children:(0,n.jsxs)(a.$,{size:"lg",className:" text-white transition-all duration-200 hover:scale-107 dark:text-black",children:[(0,n.jsx)(o.A,{className:"h-5 w-5"}),"Create New Agent"]})})]}),(0,n.jsx)("div",{className:"mb-8",children:(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(l.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,n.jsx)(s.p,{placeholder:"Search assistants...",className:"pl-10 bg-white dark:bg-gray-800",value:$,onChange:e=>B(e.target.value)})]})}),z&&(0,n.jsx)("div",{className:"mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md text-yellow-800",children:z}),H&&(0,n.jsx)("div",{className:"mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md text-yellow-800",children:H}),Y||Q?(0,n.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,n.jsxs)("div",{className:"flex flex-col items-center",children:[(0,n.jsx)(c.A,{className:"h-12 w-12 animate-spin text-primary mb-4"}),(0,n.jsx)("p",{className:"text-lg font-medium",children:"Loading your assistants..."})]})}):(0,n.jsx)("div",{className:"grid gap-6 grid-cols-1 sm:grod-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 auto-rows-fr",children:ee.map((r,i)=>(0,n.jsx)(g.default,{direction:"up",delay:.1,children:(0,n.jsxs)("div",{className:"text-card-foreground flex flex-col rounded-xl border-1  dark:border-gray-500 relative bg-white dark:bg-gray-800 hover:shadow-xl transition-shadow duration-200 overflow-hidden cursor-pointer h-full",onClick:e=>{e.target.closest(".clickPrevention")||J.push(`/agents/edit/${r.id}`)},children:[(0,n.jsx)("div",{className:"absolute top-2 right-2 z-20 clickPrevention",onClick:e=>e.stopPropagation(),children:"superadmin"===G&&(0,n.jsxs)(v.rI,{children:[(0,n.jsx)(v.ty,{asChild:!0,children:(0,n.jsx)(a.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 rounded-full",children:(0,n.jsx)(y.A,{className:"h-4 w-4"})})}),(0,n.jsxs)(v.SQ,{align:"end",children:[(0,n.jsxs)(v._2,{className:"text-blue-500 focus:text-blue-500 cursor-pointer",onSelect:e=>{e.preventDefault(),J.push(`/agents/edit/${r.id}`)},children:[(0,n.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,n.jsxs)(v._2,{className:"text-red-600 focus:text-red-600 cursor-pointer",onSelect:e=>{e.preventDefault(),q(r),R(!0)},children:[(0,n.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})}),(0,n.jsxs)("div",{className:"flex h-full",children:[(0,n.jsxs)("div",{className:"relative w-[120px] md:w-[120px] lg:w-[130px] xl:w-[140px]",children:[(0,n.jsx)("div",{className:"h-full w-full",onClick:()=>J.push(`/agents/edit/${r.id}`),children:r.avatar?(0,n.jsx)("img",{src:r.avatar,alt:`${r.name} avatar`,className:"h-full w-full object-cover"}):(0,n.jsx)("div",{className:"h-full w-full bg-black",children:(0,n.jsx)(S.default,{src:_.A,alt:`${r.name} avatar`,fill:!0,className:"object-cover"})})}),(0,n.jsx)("div",{className:`absolute bottom-2 right-2 h-2 sm:h-3 w-2 sm:w-3 rounded-full border-2 border-white dark:border-gray-800 
             ${"active"===r.status?"bg-green-500":"bg-gray-400"}`})]}),(0,n.jsxs)("div",{className:"flex flex-col flex-1 p-3 md:p-2 lg:p-3 xl:p-4",children:[(0,n.jsxs)("div",{className:"mb-2",onClick:()=>J.push(`/agents/edit/${r.id}`),children:[(0,n.jsx)("h3",{className:"font-semibold text-sm md:text-base lg:text-sm xl:text-base mb-1 truncate",children:r.name}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("p",{className:"text-xs md:text-sm lg:text-xs xl:text-sm text-[#702760] dark:text-white font-medium truncate",children:r?.role||"Assistant"}),(0,n.jsx)("div",{className:"flex items-center gap-1.5",children:r.transcriber?.language&&(0,n.jsx)(m.E,{variant:"outline",className:"text-[10px] md:text-xs lg:text-[10px] xl:text-xs bg-purple-50 dark:bg-purple-900/20 text-gray-700 dark:text-white border-purple-200 dark:border-purple-800",children:Z(r.transcriber.language)})})]})]}),(0,n.jsx)("div",{className:"flex items-center gap-1.5 md:gap-1 lg:gap-1.5 xl:gap-2 mt-auto",onClick:e=>e.stopPropagation(),children:(0,n.jsxs)(A.Bc,{children:[(0,n.jsxs)(A.m_,{children:[(0,n.jsx)(A.k$,{asChild:!0,children:(0,n.jsx)(a.$,{variant:"ghost",size:"sm",className:"h-7 w-7 md:h-6 md:w-6 lg:h-7 lg:w-7 xl:h-8 xl:w-8 p-0 rounded-full hover:bg-emerald-50 dark:hover:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-700 transition-all duration-200 hover:scale-115",children:(0,n.jsx)(d.A,{className:"h-3.5 w-3.5 md:h-3 md:w-3 lg:h-3.5 lg:w-3.5 xl:h-4 xl:w-4 text-emerald-500 dark:text-emerald-400"})})}),(0,n.jsx)(A.ZI,{className:"text-xs sm:text-sm border-gray-200 dark:border-gray-700",children:(0,n.jsx)("p",{children:"Test voice"})})]}),(0,n.jsxs)(A.m_,{children:[(0,n.jsx)(A.k$,{asChild:!0,children:(0,n.jsx)(a.$,{variant:"ghost",size:"sm",className:"h-7 w-7 md:h-6 md:w-6 lg:h-7 lg:w-7 xl:h-8 xl:w-8 p-0 rounded-full hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-blue-200 dark:border-gray-700 transition-all duration-200 hover:scale-115",onClick:n=>{j&&e?.id===r.id?(P(!1),t(null)):(t(r),P(!0))},children:(0,n.jsx)(h.A,{className:"h-3.5 w-3.5 md:h-3 md:w-3 lg:h-3.5 lg:w-3.5 xl:h-4 xl:w-4 text-blue-500 dark:text-blue-400"})})}),(0,n.jsx)(A.ZI,{className:"text-xs sm:text-sm border-gray-200 dark:border-gray-700",children:(0,n.jsx)("p",{children:"Chat with agent"})})]}),(0,n.jsxs)(A.m_,{children:[(0,n.jsx)(A.k$,{asChild:!0,children:(0,n.jsx)(a.$,{variant:"ghost",size:"sm",className:"h-7 w-7 md:h-6 md:w-6 lg:h-7 lg:w-7 xl:h-8 xl:w-8 p-0 rounded-full hover:bg-indigo-50 dark:hover:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-700 transition-all duration-200 hover:scale-115",onClick:e=>{t(r),N(!0)},children:(0,n.jsx)(p.A,{className:"h-3.5 w-3.5 md:h-3 md:w-3 lg:h-3.5 lg:w-3.5 xl:h-4 xl:w-4 text-indigo-500 dark:text-indigo-400"})})}),(0,n.jsx)(A.ZI,{className:"border-2 border-gray-200 dark:border-gray-700",children:(0,n.jsx)("p",{children:"Start web call"})})]}),(0,n.jsxs)(A.m_,{children:[(0,n.jsx)(A.k$,{asChild:!0,children:(0,n.jsx)(a.$,{variant:"ghost",size:"sm",className:"h-7 w-7 md:h-6 md:w-6 lg:h-7 lg:w-7 xl:h-8 xl:w-8 p-0 rounded-full hover:bg-purple-50 dark:hover:bg-purple-900/20 border border-purple-200 dark:border-gray-700 transition-all duration-200 hover:scale-115",onClick:e=>{t(r),k(!0)},children:(0,n.jsx)(f.A,{className:"h-3.5 w-3.5 md:h-3 md:w-3 lg:h-3.5 lg:w-3.5 xl:h-4 xl:w-4 text-purple-500 dark:text-purple-400"})})}),(0,n.jsx)(A.ZI,{className:"text-xs sm:text-sm border-2 border-gray-200 dark:border-gray-700",children:(0,n.jsx)("p",{children:"Start phone call"})})]})]})})]})]})]})},r.id))}),(0,n.jsx)(C.Lt,{open:L,onOpenChange:R,children:(0,n.jsxs)(C.EO,{children:[(0,n.jsxs)(C.wd,{children:[(0,n.jsx)(C.r7,{children:"Are you sure?"}),(0,n.jsxs)(C.$v,{children:['This will permanently delete the agent "',F?.name,'". This action cannot be undone.']})]}),(0,n.jsxs)(C.ck,{children:[(0,n.jsx)(C.Zr,{disabled:V,children:"Cancel"}),(0,n.jsx)(C.Rx,{onClick:X,disabled:V,className:"bg-red-600 hover:bg-red-700 text-white",children:V?"Deleting...":"Delete"})]})]})}),(0,n.jsx)(x.G,{agent:e,isOpen:r,onClose:()=>k(!1)}),(0,n.jsx)(O.M,{agent:e,isOpen:j,onClose:()=>P(!1)}),(0,n.jsx)(D,{agent:e,isOpen:I,onClose:()=>N(!1)})]})}r(26675)},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},38500:(e,t,r)=>{"use strict";let n,i,a,s,o,l,c,u;function d(e,t){if(null==e)return{};var r,n,i=function(e,t){if(null==e)return{};var r={};for(var n in e)if(({}).hasOwnProperty.call(e,n)){if(-1!==t.indexOf(n))continue;r[n]=e[n]}return r}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],-1===t.indexOf(r)&&({}).propertyIsEnumerable.call(e,r)&&(i[r]=e[r])}return i}function h(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")}function p(e){return(p="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function f(e){var t=function(e,t){if("object"!=p(e)||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=p(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==p(t)?t:t+""}function m(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,f(n.key),n)}}function g(e,t,r){return t&&m(e.prototype,t),r&&m(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function v(e,t){if(t&&("object"==p(t)||"function"==typeof t))return t;if(void 0!==t)throw TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}function y(e){return(y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function b(e,t){return(b=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function _(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&b(e,t)}function S(e,t,r){return(t=f(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function w(e,t,r,n,i,a,s){try{var o=e[a](s),l=o.value}catch(e){return void r(e)}o.done?t(l):Promise.resolve(l).then(n,i)}function k(e){return function(){var t=this,r=arguments;return new Promise(function(n,i){var a=e.apply(t,r);function s(e){w(a,n,i,s,o,"next",e)}function o(e){w(a,n,i,s,o,"throw",e)}s(void 0)})}}function E(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}function C(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,i,a,s,o=[],l=!0,c=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=a.call(r)).done)&&(o.push(n.value),o.length!==t);l=!0);}catch(e){c=!0,i=e}finally{try{if(!l&&null!=r.return&&(s=r.return(),Object(s)!==s))return}finally{if(c)throw i}}return o}}(e,t)||function(e,t){if(e){if("string"==typeof e)return E(e,t);var r=({}).toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?E(e,t):void 0}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}r.r(t),r.d(t,{DAILY_ACCESS_LEVEL_FULL:()=>ns,DAILY_ACCESS_LEVEL_LOBBY:()=>no,DAILY_ACCESS_LEVEL_NONE:()=>nl,DAILY_ACCESS_UNKNOWN:()=>na,DAILY_CAMERA_ERROR_CAM_AND_MIC_IN_USE:()=>nk,DAILY_CAMERA_ERROR_CAM_IN_USE:()=>nS,DAILY_CAMERA_ERROR_CONSTRAINTS:()=>nT,DAILY_CAMERA_ERROR_MIC_IN_USE:()=>nw,DAILY_CAMERA_ERROR_NOT_FOUND:()=>nA,DAILY_CAMERA_ERROR_PERMISSIONS:()=>nE,DAILY_CAMERA_ERROR_UNDEF_MEDIADEVICES:()=>nC,DAILY_CAMERA_ERROR_UNKNOWN:()=>nM,DAILY_EVENT_ACCESS_STATE_UPDATED:()=>nJ,DAILY_EVENT_ACTIVE_SPEAKER_CHANGE:()=>io,DAILY_EVENT_ACTIVE_SPEAKER_MODE_CHANGE:()=>il,DAILY_EVENT_APP_MSG:()=>n9,DAILY_EVENT_CAMERA_ERROR:()=>nD,DAILY_EVENT_CPU_LOAD_CHANGE:()=>id,DAILY_EVENT_ERROR:()=>iE,DAILY_EVENT_EXIT_FULLSCREEN:()=>im,DAILY_EVENT_FACE_COUNTS_UPDATED:()=>ih,DAILY_EVENT_FULLSCREEN:()=>ip,DAILY_EVENT_IFRAME_LAUNCH_CONFIG:()=>nO,DAILY_EVENT_IFRAME_READY_FOR_LAUNCH_CONFIG:()=>nx,DAILY_EVENT_INPUT_SETTINGS_UPDATED:()=>iw,DAILY_EVENT_JOINED_MEETING:()=>nF,DAILY_EVENT_JOINING_MEETING:()=>nR,DAILY_EVENT_LANG_UPDATED:()=>i_,DAILY_EVENT_LEFT_MEETING:()=>nq,DAILY_EVENT_LIVE_STREAMING_ERROR:()=>ib,DAILY_EVENT_LIVE_STREAMING_STARTED:()=>ig,DAILY_EVENT_LIVE_STREAMING_STOPPED:()=>iy,DAILY_EVENT_LIVE_STREAMING_UPDATED:()=>iv,DAILY_EVENT_LOADED:()=>nN,DAILY_EVENT_LOADING:()=>nP,DAILY_EVENT_LOAD_ATTEMPT_FAILED:()=>nI,DAILY_EVENT_LOCAL_SCREEN_SHARE_CANCELED:()=>is,DAILY_EVENT_LOCAL_SCREEN_SHARE_STARTED:()=>ii,DAILY_EVENT_LOCAL_SCREEN_SHARE_STOPPED:()=>ia,DAILY_EVENT_MEETING_SESSION_DATA_ERROR:()=>nz,DAILY_EVENT_MEETING_SESSION_STATE_UPDATED:()=>nY,DAILY_EVENT_MEETING_SESSION_SUMMARY_UPDATED:()=>nG,DAILY_EVENT_NETWORK_CONNECTION:()=>iu,DAILY_EVENT_NETWORK_QUALITY_CHANGE:()=>ic,DAILY_EVENT_NONFATAL_ERROR:()=>ik,DAILY_EVENT_PARTICIPANT_COUNTS_UPDATED:()=>nB,DAILY_EVENT_PARTICIPANT_JOINED:()=>nV,DAILY_EVENT_PARTICIPANT_LEFT:()=>n$,DAILY_EVENT_PARTICIPANT_UPDATED:()=>nU,DAILY_EVENT_RECEIVE_SETTINGS_UPDATED:()=>iS,DAILY_EVENT_RECORDING_DATA:()=>n6,DAILY_EVENT_RECORDING_ERROR:()=>n5,DAILY_EVENT_RECORDING_STARTED:()=>n2,DAILY_EVENT_RECORDING_STATS:()=>n4,DAILY_EVENT_RECORDING_STOPPED:()=>n3,DAILY_EVENT_RECORDING_UPLOAD_COMPLETED:()=>n8,DAILY_EVENT_REMOTE_MEDIA_PLAYER_STARTED:()=>ie,DAILY_EVENT_REMOTE_MEDIA_PLAYER_STOPPED:()=>ir,DAILY_EVENT_REMOTE_MEDIA_PLAYER_UPDATED:()=>it,DAILY_EVENT_STARTED_CAMERA:()=>nL,DAILY_EVENT_THEME_UPDATED:()=>nj,DAILY_EVENT_TRACK_STARTED:()=>nK,DAILY_EVENT_TRACK_STOPPED:()=>nX,DAILY_EVENT_TRANSCRIPTION_ERROR:()=>n1,DAILY_EVENT_TRANSCRIPTION_MSG:()=>n7,DAILY_EVENT_TRANSCRIPTION_STARTED:()=>nZ,DAILY_EVENT_TRANSCRIPTION_STOPPED:()=>n0,DAILY_EVENT_WAITING_PARTICIPANT_ADDED:()=>nW,DAILY_EVENT_WAITING_PARTICIPANT_REMOVED:()=>nH,DAILY_EVENT_WAITING_PARTICIPANT_UPDATED:()=>nQ,DAILY_FATAL_ERROR_CONNECTION:()=>n_,DAILY_FATAL_ERROR_EJECTED:()=>nd,DAILY_FATAL_ERROR_EOL:()=>ny,DAILY_FATAL_ERROR_EXP_ROOM:()=>nf,DAILY_FATAL_ERROR_EXP_TOKEN:()=>nm,DAILY_FATAL_ERROR_MEETING_FULL:()=>nv,DAILY_FATAL_ERROR_NBF_ROOM:()=>nh,DAILY_FATAL_ERROR_NBF_TOKEN:()=>np,DAILY_FATAL_ERROR_NOT_ALLOWED:()=>nb,DAILY_FATAL_ERROR_NO_ROOM:()=>ng,DAILY_RECEIVE_SETTINGS_ALL_PARTICIPANTS_KEY:()=>nu,DAILY_RECEIVE_SETTINGS_BASE_KEY:()=>nc,DAILY_STATE_ERROR:()=>r9,DAILY_STATE_JOINED:()=>r8,DAILY_STATE_JOINING:()=>r5,DAILY_STATE_LEFT:()=>r6,DAILY_STATE_NEW:()=>r2,DAILY_TRACK_STATE_BLOCKED:()=>r7,DAILY_TRACK_STATE_INTERRUPTED:()=>nn,DAILY_TRACK_STATE_LOADING:()=>nr,DAILY_TRACK_STATE_OFF:()=>ne,DAILY_TRACK_STATE_PLAYABLE:()=>ni,DAILY_TRACK_STATE_SENDABLE:()=>nt,default:()=>a3});var A,T,M={exports:{}},x="object"==typeof Reflect?Reflect:null,O=x&&"function"==typeof x.apply?x.apply:function(e,t,r){return Function.prototype.apply.call(e,t,r)};T=x&&"function"==typeof x.ownKeys?x.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var j=Number.isNaN||function(e){return e!=e};function P(){P.init.call(this)}M.exports=P,M.exports.once=function(e,t){return new Promise(function(r,n){function i(r){e.removeListener(t,a),n(r)}function a(){"function"==typeof e.removeListener&&e.removeListener("error",i),r([].slice.call(arguments))}$(e,t,a,{once:!0}),"error"!==t&&"function"==typeof e.on&&$(e,"error",i,{once:!0})})},P.EventEmitter=P,P.prototype._events=void 0,P.prototype._eventsCount=0,P.prototype._maxListeners=void 0;var I=10;function N(e){if("function"!=typeof e)throw TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function L(e){return void 0===e._maxListeners?P.defaultMaxListeners:e._maxListeners}function D(e,t,r,n){var i,a,s;if(N(r),void 0===(a=e._events)?(a=e._events=Object.create(null),e._eventsCount=0):(void 0!==a.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),a=e._events),s=a[t]),void 0===s)s=a[t]=r,++e._eventsCount;else if("function"==typeof s?s=a[t]=n?[r,s]:[s,r]:n?s.unshift(r):s.push(r),(i=L(e))>0&&s.length>i&&!s.warned){s.warned=!0;var o=Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");o.name="MaxListenersExceededWarning",o.emitter=e,o.type=t,o.count=s.length,console&&console.warn&&console.warn(o)}return e}function R(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0==arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function F(e,t,r){var n={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},i=R.bind(n);return i.listener=r,n.wrapFn=i,i}function q(e,t,r){var n=e._events;if(void 0===n)return[];var i=n[t];return void 0===i?[]:"function"==typeof i?r?[i.listener||i]:[i]:r?function(e){for(var t=Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(i):U(i,i.length)}function V(e){var t=this._events;if(void 0!==t){var r=t[e];if("function"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function U(e,t){for(var r=Array(t),n=0;n<t;++n)r[n]=e[n];return r}function $(e,t,r,n){if("function"==typeof e.on)n.once?e.once(t,r):e.on(t,r);else{if("function"!=typeof e.addEventListener)throw TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e);e.addEventListener(t,function i(a){n.once&&e.removeEventListener(t,i),r(a)})}}Object.defineProperty(P,"defaultMaxListeners",{enumerable:!0,get:function(){return I},set:function(e){if("number"!=typeof e||e<0||j(e))throw RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");I=e}}),P.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},P.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||j(e))throw RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},P.prototype.getMaxListeners=function(){return L(this)},P.prototype.emit=function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var n="error"===e,i=this._events;if(void 0!==i)n=n&&void 0===i.error;else if(!n)return!1;if(n){if(t.length>0&&(a=t[0]),a instanceof Error)throw a;var a,s=Error("Unhandled error."+(a?" ("+a.message+")":""));throw s.context=a,s}var o=i[e];if(void 0===o)return!1;if("function"==typeof o)O(o,this,t);else{var l=o.length,c=U(o,l);for(r=0;r<l;++r)O(c[r],this,t)}return!0},P.prototype.addListener=function(e,t){return D(this,e,t,!1)},P.prototype.on=P.prototype.addListener,P.prototype.prependListener=function(e,t){return D(this,e,t,!0)},P.prototype.once=function(e,t){return N(t),this.on(e,F(this,e,t)),this},P.prototype.prependOnceListener=function(e,t){return N(t),this.prependListener(e,F(this,e,t)),this},P.prototype.removeListener=function(e,t){var r,n,i,a,s;if(N(t),void 0===(n=this._events)||void 0===(r=n[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete n[e],n.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(i=-1,a=r.length-1;a>=0;a--)if(r[a]===t||r[a].listener===t){s=r[a].listener,i=a;break}if(i<0)return this;0===i?r.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(r,i),1===r.length&&(n[e]=r[0]),void 0!==n.removeListener&&this.emit("removeListener",e,s||t)}return this},P.prototype.off=P.prototype.removeListener,P.prototype.removeAllListeners=function(e){var t,r,n;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0==arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0==arguments.length){var i,a=Object.keys(r);for(n=0;n<a.length;++n)"removeListener"!==(i=a[n])&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(void 0!==t)for(n=t.length-1;n>=0;n--)this.removeListener(e,t[n]);return this},P.prototype.listeners=function(e){return q(this,e,!0)},P.prototype.rawListeners=function(e){return q(this,e,!1)},P.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):V.call(e,t)},P.prototype.listenerCount=V,P.prototype.eventNames=function(){return this._eventsCount>0?T(this._events):[]};var B=M.exports,J=function(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}(B),G=Object.prototype.hasOwnProperty;function Y(e,t,r){for(r of e.keys())if(z(r,t))return r}function z(e,t){var r,n,i;if(e===t)return!0;if(e&&t&&(r=e.constructor)===t.constructor){if(r===Date)return e.getTime()===t.getTime();if(r===RegExp)return e.toString()===t.toString();if(r===Array){if((n=e.length)===t.length)for(;n--&&z(e[n],t[n]););return -1===n}if(r===Set){if(e.size!==t.size)return!1;for(n of e)if((i=n)&&"object"==typeof i&&!(i=Y(t,i))||!t.has(i))return!1;return!0}if(r===Map){if(e.size!==t.size)return!1;for(n of e)if((i=n[0])&&"object"==typeof i&&!(i=Y(t,i))||!z(n[1],t.get(i)))return!1;return!0}if(r===ArrayBuffer)e=new Uint8Array(e),t=new Uint8Array(t);else if(r===DataView){if((n=e.byteLength)===t.byteLength)for(;n--&&e.getInt8(n)===t.getInt8(n););return -1===n}if(ArrayBuffer.isView(e)){if((n=e.byteLength)===t.byteLength)for(;n--&&e[n]===t[n];);return -1===n}if(!r||"object"==typeof e){for(r in n=0,e)if(G.call(e,r)&&++n&&!G.call(t,r)||!(r in t)||!z(e[r],t[r]))return!1;return Object.keys(t).length===n}}return e!=e&&t!=t}let W={"Amazon Silk":"amazon_silk","Android Browser":"android",Bada:"bada",BlackBerry:"blackberry",Chrome:"chrome",Chromium:"chromium",Electron:"electron",Epiphany:"epiphany",Firefox:"firefox",Focus:"focus",Generic:"generic","Google Search":"google_search",Googlebot:"googlebot","Internet Explorer":"ie","K-Meleon":"k_meleon",Maxthon:"maxthon","Microsoft Edge":"edge","MZ Browser":"mz","NAVER Whale Browser":"naver",Opera:"opera","Opera Coast":"opera_coast",PhantomJS:"phantomjs",Puffin:"puffin",QupZilla:"qupzilla",QQ:"qq",QQLite:"qqlite",Safari:"safari",Sailfish:"sailfish","Samsung Internet for Android":"samsung_internet",SeaMonkey:"seamonkey",Sleipnir:"sleipnir",Swing:"swing",Tizen:"tizen","UC Browser":"uc",Vivaldi:"vivaldi","WebOS Browser":"webos",WeChat:"wechat","Yandex Browser":"yandex",Roku:"roku"},Q={amazon_silk:"Amazon Silk",android:"Android Browser",bada:"Bada",blackberry:"BlackBerry",chrome:"Chrome",chromium:"Chromium",electron:"Electron",epiphany:"Epiphany",firefox:"Firefox",focus:"Focus",generic:"Generic",googlebot:"Googlebot",google_search:"Google Search",ie:"Internet Explorer",k_meleon:"K-Meleon",maxthon:"Maxthon",edge:"Microsoft Edge",mz:"MZ Browser",naver:"NAVER Whale Browser",opera:"Opera",opera_coast:"Opera Coast",phantomjs:"PhantomJS",puffin:"Puffin",qupzilla:"QupZilla",qq:"QQ Browser",qqlite:"QQ Browser Lite",safari:"Safari",sailfish:"Sailfish",samsung_internet:"Samsung Internet for Android",seamonkey:"SeaMonkey",sleipnir:"Sleipnir",swing:"Swing",tizen:"Tizen",uc:"UC Browser",vivaldi:"Vivaldi",webos:"WebOS Browser",wechat:"WeChat",yandex:"Yandex Browser"},H={tablet:"tablet",mobile:"mobile",desktop:"desktop",tv:"tv"},K={WindowsPhone:"Windows Phone",Windows:"Windows",MacOS:"macOS",iOS:"iOS",Android:"Android",WebOS:"WebOS",BlackBerry:"BlackBerry",Bada:"Bada",Tizen:"Tizen",Linux:"Linux",ChromeOS:"Chrome OS",PlayStation4:"PlayStation 4",Roku:"Roku"},X={EdgeHTML:"EdgeHTML",Blink:"Blink",Trident:"Trident",Presto:"Presto",Gecko:"Gecko",WebKit:"WebKit"};class Z{static getFirstMatch(e,t){let r=t.match(e);return r&&r.length>0&&r[1]||""}static getSecondMatch(e,t){let r=t.match(e);return r&&r.length>1&&r[2]||""}static matchAndReturnConst(e,t,r){if(e.test(t))return r}static getWindowsVersionName(e){switch(e){case"NT":return"NT";case"XP":case"NT 5.1":return"XP";case"NT 5.0":return"2000";case"NT 5.2":return"2003";case"NT 6.0":return"Vista";case"NT 6.1":return"7";case"NT 6.2":return"8";case"NT 6.3":return"8.1";case"NT 10.0":return"10";default:return}}static getMacOSVersionName(e){let t=e.split(".").splice(0,2).map(e=>parseInt(e,10)||0);if(t.push(0),10===t[0])switch(t[1]){case 5:return"Leopard";case 6:return"Snow Leopard";case 7:return"Lion";case 8:return"Mountain Lion";case 9:return"Mavericks";case 10:return"Yosemite";case 11:return"El Capitan";case 12:return"Sierra";case 13:return"High Sierra";case 14:return"Mojave";case 15:return"Catalina";default:return}}static getAndroidVersionName(e){let t=e.split(".").splice(0,2).map(e=>parseInt(e,10)||0);if(t.push(0),!(1===t[0]&&t[1]<5))return 1===t[0]&&t[1]<6?"Cupcake":1===t[0]&&t[1]>=6?"Donut":2===t[0]&&t[1]<2?"Eclair":2===t[0]&&2===t[1]?"Froyo":2===t[0]&&t[1]>2?"Gingerbread":3===t[0]?"Honeycomb":4===t[0]&&t[1]<1?"Ice Cream Sandwich":4===t[0]&&t[1]<4?"Jelly Bean":4===t[0]&&t[1]>=4?"KitKat":5===t[0]?"Lollipop":6===t[0]?"Marshmallow":7===t[0]?"Nougat":8===t[0]?"Oreo":9===t[0]?"Pie":void 0}static getVersionPrecision(e){return e.split(".").length}static compareVersions(e,t,r=!1){let n=Z.getVersionPrecision(e),i=Z.getVersionPrecision(t),a=Math.max(n,i),s=0,o=Z.map([e,t],e=>{let t=a-Z.getVersionPrecision(e),r=e+Array(t+1).join(".0");return Z.map(r.split("."),e=>Array(20-e.length).join("0")+e).reverse()});for(r&&(s=a-Math.min(n,i)),a-=1;a>=s;){if(o[0][a]>o[1][a])return 1;if(o[0][a]===o[1][a]){if(a===s)return 0;a-=1}else if(o[0][a]<o[1][a])return -1}}static map(e,t){let r;let n=[];if(Array.prototype.map)return Array.prototype.map.call(e,t);for(r=0;r<e.length;r+=1)n.push(t(e[r]));return n}static find(e,t){let r,n;if(Array.prototype.find)return Array.prototype.find.call(e,t);for(r=0,n=e.length;r<n;r+=1){let n=e[r];if(t(n,r))return n}}static assign(e,...t){let r,n;if(Object.assign)return Object.assign(e,...t);for(r=0,n=t.length;r<n;r+=1){let n=t[r];"object"==typeof n&&null!==n&&Object.keys(n).forEach(t=>{e[t]=n[t]})}return e}static getBrowserAlias(e){return W[e]}static getBrowserTypeByAlias(e){return Q[e]||""}}let ee=/version\/(\d+(\.?_?\d+)+)/i,et=[{test:[/googlebot/i],describe(e){let t={name:"Googlebot"},r=Z.getFirstMatch(/googlebot\/(\d+(\.\d+))/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/opera/i],describe(e){let t={name:"Opera"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:opera)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opr\/|opios/i],describe(e){let t={name:"Opera"},r=Z.getFirstMatch(/(?:opr|opios)[\s/](\S+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/SamsungBrowser/i],describe(e){let t={name:"Samsung Internet for Android"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:SamsungBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Whale/i],describe(e){let t={name:"NAVER Whale Browser"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:whale)[\s/](\d+(?:\.\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MZBrowser/i],describe(e){let t={name:"MZ Browser"},r=Z.getFirstMatch(/(?:MZBrowser)[\s/](\d+(?:\.\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/focus/i],describe(e){let t={name:"Focus"},r=Z.getFirstMatch(/(?:focus)[\s/](\d+(?:\.\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/swing/i],describe(e){let t={name:"Swing"},r=Z.getFirstMatch(/(?:swing)[\s/](\d+(?:\.\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/coast/i],describe(e){let t={name:"Opera Coast"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:coast)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/opt\/\d+(?:.?_?\d+)+/i],describe(e){let t={name:"Opera Touch"},r=Z.getFirstMatch(/(?:opt)[\s/](\d+(\.?_?\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/yabrowser/i],describe(e){let t={name:"Yandex Browser"},r=Z.getFirstMatch(/(?:yabrowser)[\s/](\d+(\.?_?\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/ucbrowser/i],describe(e){let t={name:"UC Browser"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:ucbrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/Maxthon|mxios/i],describe(e){let t={name:"Maxthon"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:Maxthon|mxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/epiphany/i],describe(e){let t={name:"Epiphany"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:epiphany)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/puffin/i],describe(e){let t={name:"Puffin"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:puffin)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sleipnir/i],describe(e){let t={name:"Sleipnir"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:sleipnir)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/k-meleon/i],describe(e){let t={name:"K-Meleon"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/(?:k-meleon)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/micromessenger/i],describe(e){let t={name:"WeChat"},r=Z.getFirstMatch(/(?:micromessenger)[\s/](\d+(\.?_?\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/qqbrowser/i],describe(e){let t={name:/qqbrowserlite/i.test(e)?"QQ Browser Lite":"QQ Browser"},r=Z.getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\d+(\.?_?\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/msie|trident/i],describe(e){let t={name:"Internet Explorer"},r=Z.getFirstMatch(/(?:msie |rv:)(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/\sedg\//i],describe(e){let t={name:"Microsoft Edge"},r=Z.getFirstMatch(/\sedg\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/edg([ea]|ios)/i],describe(e){let t={name:"Microsoft Edge"},r=Z.getSecondMatch(/edg([ea]|ios)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/vivaldi/i],describe(e){let t={name:"Vivaldi"},r=Z.getFirstMatch(/vivaldi\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/seamonkey/i],describe(e){let t={name:"SeaMonkey"},r=Z.getFirstMatch(/seamonkey\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/sailfish/i],describe(e){let t={name:"Sailfish"},r=Z.getFirstMatch(/sailfish\s?browser\/(\d+(\.\d+)?)/i,e);return r&&(t.version=r),t}},{test:[/silk/i],describe(e){let t={name:"Amazon Silk"},r=Z.getFirstMatch(/silk\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/phantom/i],describe(e){let t={name:"PhantomJS"},r=Z.getFirstMatch(/phantomjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/slimerjs/i],describe(e){let t={name:"SlimerJS"},r=Z.getFirstMatch(/slimerjs\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(e){let t={name:"BlackBerry"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/blackberry[\d]+\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(web|hpw)[o0]s/i],describe(e){let t={name:"WebOS Browser"},r=Z.getFirstMatch(ee,e)||Z.getFirstMatch(/w(?:eb)?[o0]sbrowser\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/bada/i],describe(e){let t={name:"Bada"},r=Z.getFirstMatch(/dolfin\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/tizen/i],describe(e){let t={name:"Tizen"},r=Z.getFirstMatch(/(?:tizen\s?)?browser\/(\d+(\.?_?\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/qupzilla/i],describe(e){let t={name:"QupZilla"},r=Z.getFirstMatch(/(?:qupzilla)[\s/](\d+(\.?_?\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/firefox|iceweasel|fxios/i],describe(e){let t={name:"Firefox"},r=Z.getFirstMatch(/(?:firefox|iceweasel|fxios)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/electron/i],describe(e){let t={name:"Electron"},r=Z.getFirstMatch(/(?:electron)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/MiuiBrowser/i],describe(e){let t={name:"Miui"},r=Z.getFirstMatch(/(?:MiuiBrowser)[\s/](\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/chromium/i],describe(e){let t={name:"Chromium"},r=Z.getFirstMatch(/(?:chromium)[\s/](\d+(\.?_?\d+)+)/i,e)||Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/chrome|crios|crmo/i],describe(e){let t={name:"Chrome"},r=Z.getFirstMatch(/(?:chrome|crios|crmo)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/GSA/i],describe(e){let t={name:"Google Search"},r=Z.getFirstMatch(/(?:GSA)\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test(e){let t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe(e){let t={name:"Android Browser"},r=Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/playstation 4/i],describe(e){let t={name:"PlayStation 4"},r=Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/safari|applewebkit/i],describe(e){let t={name:"Safari"},r=Z.getFirstMatch(ee,e);return r&&(t.version=r),t}},{test:[/.*/i],describe(e){let t=-1!==e.search("\\(")?/^(.*)\/(.*)[ \t]\((.*)/:/^(.*)\/(.*) /;return{name:Z.getFirstMatch(t,e),version:Z.getSecondMatch(t,e)}}}];var er=[{test:[/Roku\/DVP/],describe(e){let t=Z.getFirstMatch(/Roku\/DVP-(\d+\.\d+)/i,e);return{name:K.Roku,version:t}}},{test:[/windows phone/i],describe(e){let t=Z.getFirstMatch(/windows phone (?:os)?\s?(\d+(\.\d+)*)/i,e);return{name:K.WindowsPhone,version:t}}},{test:[/windows /i],describe(e){let t=Z.getFirstMatch(/Windows ((NT|XP)( \d\d?.\d)?)/i,e),r=Z.getWindowsVersionName(t);return{name:K.Windows,version:t,versionName:r}}},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe(e){let t={name:K.iOS},r=Z.getSecondMatch(/(Version\/)(\d[\d.]+)/,e);return r&&(t.version=r),t}},{test:[/macintosh/i],describe(e){let t=Z.getFirstMatch(/mac os x (\d+(\.?_?\d+)+)/i,e).replace(/[_\s]/g,"."),r=Z.getMacOSVersionName(t),n={name:K.MacOS,version:t};return r&&(n.versionName=r),n}},{test:[/(ipod|iphone|ipad)/i],describe(e){let t=Z.getFirstMatch(/os (\d+([_\s]\d+)*) like mac os x/i,e).replace(/[_\s]/g,".");return{name:K.iOS,version:t}}},{test(e){let t=!e.test(/like android/i),r=e.test(/android/i);return t&&r},describe(e){let t=Z.getFirstMatch(/android[\s/-](\d+(\.\d+)*)/i,e),r=Z.getAndroidVersionName(t),n={name:K.Android,version:t};return r&&(n.versionName=r),n}},{test:[/(web|hpw)[o0]s/i],describe(e){let t=Z.getFirstMatch(/(?:web|hpw)[o0]s\/(\d+(\.\d+)*)/i,e),r={name:K.WebOS};return t&&t.length&&(r.version=t),r}},{test:[/blackberry|\bbb\d+/i,/rim\stablet/i],describe(e){let t=Z.getFirstMatch(/rim\stablet\sos\s(\d+(\.\d+)*)/i,e)||Z.getFirstMatch(/blackberry\d+\/(\d+([_\s]\d+)*)/i,e)||Z.getFirstMatch(/\bbb(\d+)/i,e);return{name:K.BlackBerry,version:t}}},{test:[/bada/i],describe(e){let t=Z.getFirstMatch(/bada\/(\d+(\.\d+)*)/i,e);return{name:K.Bada,version:t}}},{test:[/tizen/i],describe(e){let t=Z.getFirstMatch(/tizen[/\s](\d+(\.\d+)*)/i,e);return{name:K.Tizen,version:t}}},{test:[/linux/i],describe:()=>({name:K.Linux})},{test:[/CrOS/],describe:()=>({name:K.ChromeOS})},{test:[/PlayStation 4/],describe(e){let t=Z.getFirstMatch(/PlayStation 4[/\s](\d+(\.\d+)*)/i,e);return{name:K.PlayStation4,version:t}}}],en=[{test:[/googlebot/i],describe:()=>({type:"bot",vendor:"Google"})},{test:[/huawei/i],describe(e){let t=Z.getFirstMatch(/(can-l01)/i,e)&&"Nova",r={type:H.mobile,vendor:"Huawei"};return t&&(r.model=t),r}},{test:[/nexus\s*(?:7|8|9|10).*/i],describe:()=>({type:H.tablet,vendor:"Nexus"})},{test:[/ipad/i],describe:()=>({type:H.tablet,vendor:"Apple",model:"iPad"})},{test:[/Macintosh(.*?) FxiOS(.*?)\//],describe:()=>({type:H.tablet,vendor:"Apple",model:"iPad"})},{test:[/kftt build/i],describe:()=>({type:H.tablet,vendor:"Amazon",model:"Kindle Fire HD 7"})},{test:[/silk/i],describe:()=>({type:H.tablet,vendor:"Amazon"})},{test:[/tablet(?! pc)/i],describe:()=>({type:H.tablet})},{test(e){let t=e.test(/ipod|iphone/i),r=e.test(/like (ipod|iphone)/i);return t&&!r},describe(e){let t=Z.getFirstMatch(/(ipod|iphone)/i,e);return{type:H.mobile,vendor:"Apple",model:t}}},{test:[/nexus\s*[0-6].*/i,/galaxy nexus/i],describe:()=>({type:H.mobile,vendor:"Nexus"})},{test:[/[^-]mobi/i],describe:()=>({type:H.mobile})},{test:e=>"blackberry"===e.getBrowserName(!0),describe:()=>({type:H.mobile,vendor:"BlackBerry"})},{test:e=>"bada"===e.getBrowserName(!0),describe:()=>({type:H.mobile})},{test:e=>"windows phone"===e.getBrowserName(),describe:()=>({type:H.mobile,vendor:"Microsoft"})},{test(e){let t=Number(String(e.getOSVersion()).split(".")[0]);return"android"===e.getOSName(!0)&&t>=3},describe:()=>({type:H.tablet})},{test:e=>"android"===e.getOSName(!0),describe:()=>({type:H.mobile})},{test:e=>"macos"===e.getOSName(!0),describe:()=>({type:H.desktop,vendor:"Apple"})},{test:e=>"windows"===e.getOSName(!0),describe:()=>({type:H.desktop})},{test:e=>"linux"===e.getOSName(!0),describe:()=>({type:H.desktop})},{test:e=>"playstation 4"===e.getOSName(!0),describe:()=>({type:H.tv})},{test:e=>"roku"===e.getOSName(!0),describe:()=>({type:H.tv})}],ei=[{test:e=>"microsoft edge"===e.getBrowserName(!0),describe(e){if(/\sedg\//i.test(e))return{name:X.Blink};let t=Z.getFirstMatch(/edge\/(\d+(\.?_?\d+)+)/i,e);return{name:X.EdgeHTML,version:t}}},{test:[/trident/i],describe(e){let t={name:X.Trident},r=Z.getFirstMatch(/trident\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:e=>e.test(/presto/i),describe(e){let t={name:X.Presto},r=Z.getFirstMatch(/presto\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test(e){let t=e.test(/gecko/i),r=e.test(/like gecko/i);return t&&!r},describe(e){let t={name:X.Gecko},r=Z.getFirstMatch(/gecko\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}},{test:[/(apple)?webkit\/537\.36/i],describe:()=>({name:X.Blink})},{test:[/(apple)?webkit/i],describe(e){let t={name:X.WebKit},r=Z.getFirstMatch(/webkit\/(\d+(\.?_?\d+)+)/i,e);return r&&(t.version=r),t}}];class ea{constructor(e,t=!1){if(null==e||""===e)throw Error("UserAgent parameter can't be empty");this._ua=e,this.parsedResult={},!0!==t&&this.parse()}getUA(){return this._ua}test(e){return e.test(this._ua)}parseBrowser(){this.parsedResult.browser={};let e=Z.find(et,e=>{if("function"==typeof e.test)return e.test(this);if(e.test instanceof Array)return e.test.some(e=>this.test(e));throw Error("Browser's test function is not valid")});return e&&(this.parsedResult.browser=e.describe(this.getUA())),this.parsedResult.browser}getBrowser(){return this.parsedResult.browser?this.parsedResult.browser:this.parseBrowser()}getBrowserName(e){return e?String(this.getBrowser().name).toLowerCase()||"":this.getBrowser().name||""}getBrowserVersion(){return this.getBrowser().version}getOS(){return this.parsedResult.os?this.parsedResult.os:this.parseOS()}parseOS(){this.parsedResult.os={};let e=Z.find(er,e=>{if("function"==typeof e.test)return e.test(this);if(e.test instanceof Array)return e.test.some(e=>this.test(e));throw Error("Browser's test function is not valid")});return e&&(this.parsedResult.os=e.describe(this.getUA())),this.parsedResult.os}getOSName(e){let{name:t}=this.getOS();return e?String(t).toLowerCase()||"":t||""}getOSVersion(){return this.getOS().version}getPlatform(){return this.parsedResult.platform?this.parsedResult.platform:this.parsePlatform()}getPlatformType(e=!1){let{type:t}=this.getPlatform();return e?String(t).toLowerCase()||"":t||""}parsePlatform(){this.parsedResult.platform={};let e=Z.find(en,e=>{if("function"==typeof e.test)return e.test(this);if(e.test instanceof Array)return e.test.some(e=>this.test(e));throw Error("Browser's test function is not valid")});return e&&(this.parsedResult.platform=e.describe(this.getUA())),this.parsedResult.platform}getEngine(){return this.parsedResult.engine?this.parsedResult.engine:this.parseEngine()}getEngineName(e){return e?String(this.getEngine().name).toLowerCase()||"":this.getEngine().name||""}parseEngine(){this.parsedResult.engine={};let e=Z.find(ei,e=>{if("function"==typeof e.test)return e.test(this);if(e.test instanceof Array)return e.test.some(e=>this.test(e));throw Error("Browser's test function is not valid")});return e&&(this.parsedResult.engine=e.describe(this.getUA())),this.parsedResult.engine}parse(){return this.parseBrowser(),this.parseOS(),this.parsePlatform(),this.parseEngine(),this}getResult(){return Z.assign({},this.parsedResult)}satisfies(e){let t={},r=0,n={},i=0;if(Object.keys(e).forEach(a=>{let s=e[a];"string"==typeof s?(n[a]=s,i+=1):"object"==typeof s&&(t[a]=s,r+=1)}),r>0){let e=Object.keys(t),r=Z.find(e,e=>this.isOS(e));if(r){let e=this.satisfies(t[r]);if(void 0!==e)return e}let n=Z.find(e,e=>this.isPlatform(e));if(n){let e=this.satisfies(t[n]);if(void 0!==e)return e}}if(i>0){let e=Object.keys(n),t=Z.find(e,e=>this.isBrowser(e,!0));if(void 0!==t)return this.compareVersion(n[t])}}isBrowser(e,t=!1){let r=this.getBrowserName().toLowerCase(),n=e.toLowerCase(),i=Z.getBrowserTypeByAlias(n);return t&&i&&(n=i.toLowerCase()),n===r}compareVersion(e){let t=[0],r=e,n=!1,i=this.getBrowserVersion();if("string"==typeof i)return">"===e[0]||"<"===e[0]?(r=e.substr(1),"="===e[1]?(n=!0,r=e.substr(2)):t=[],">"===e[0]?t.push(1):t.push(-1)):"="===e[0]?r=e.substr(1):"~"===e[0]&&(n=!0,r=e.substr(1)),t.indexOf(Z.compareVersions(i,r,n))>-1}isOS(e){return this.getOSName(!0)===String(e).toLowerCase()}isPlatform(e){return this.getPlatformType(!0)===String(e).toLowerCase()}isEngine(e){return this.getEngineName(!0)===String(e).toLowerCase()}is(e,t=!1){return this.isBrowser(e,t)||this.isOS(e)||this.isPlatform(e)}some(e=[]){return e.some(e=>this.is(e))}}class es{static getParser(e,t=!1){if("string"!=typeof e)throw Error("UserAgent should be a string");return new ea(e,t)}static parse(e){return new ea(e).getResult()}static get BROWSER_MAP(){return Q}static get ENGINE_MAP(){return X}static get OS_MAP(){return K}static get PLATFORMS_MAP(){return H}}function eo(){return Date.now()+Math.random().toString()}function el(){throw Error("Method must be implemented in subclass")}function ec(e,t){return null!=t&&t.proxyUrl?t.proxyUrl+("/"===t.proxyUrl.slice(-1)?"":"/")+e.substring(8):e}function eu(e){return null!=e&&e.callObjectBundleUrlOverride?e.callObjectBundleUrlOverride:ec("https://c.daily.co/call-machine/versioned/".concat("0.79.0","/static/call-machine-object-bundle.js"),e)}function ed(e){try{new URL(e)}catch(e){return!1}return!0}let eh="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__,ep="8.55.0",ef=globalThis;function em(e,t,r){let n=r||ef,i=n.__SENTRY__=n.__SENTRY__||{},a=i[ep]=i[ep]||{};return a[e]||(a[e]=t())}let eg="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__,ev=["debug","info","warn","error","log","assert","trace"],ey={};function eb(e){if(!("console"in ef))return e();let t=ef.console,r={},n=Object.keys(ey);n.forEach(e=>{let n=ey[e];r[e]=t[e],t[e]=n});try{return e()}finally{n.forEach(e=>{t[e]=r[e]})}}let e_=em("logger",function(){let e=!1,t={enable:()=>{e=!0},disable:()=>{e=!1},isEnabled:()=>e};return eg?ev.forEach(r=>{t[r]=(...t)=>{e&&eb(()=>{ef.console[r](`Sentry Logger [${r}]:`,...t)})}}):ev.forEach(e=>{t[e]=()=>{}}),t}),eS=/\(error: (.*)\)/,ew=/captureMessage|captureException/;function ek(e){return e[e.length-1]||{}}let eE="<anonymous>";function eC(e){try{return e&&"function"==typeof e&&e.name||eE}catch(e){return eE}}function eA(e){let t=e.exception;if(t){let e=[];try{return t.values.forEach(t=>{t.stacktrace.frames&&e.push(...t.stacktrace.frames)}),e}catch(e){return}}}let eT={},eM={};function ex(e,t){eT[e]=eT[e]||[],eT[e].push(t)}function eO(e,t){if(!eM[e]){eM[e]=!0;try{t()}catch(t){eg&&e_.error(`Error while instrumenting ${e}`,t)}}}function ej(e,t){let r=e&&eT[e];if(r)for(let n of r)try{n(t)}catch(t){eg&&e_.error(`Error while triggering instrumentation handler.
Type: ${e}
Name: ${eC(n)}
Error:`,t)}}let eP=null;function eI(){eP=ef.onerror,ef.onerror=function(e,t,r,n,i){return ej("error",{column:n,error:i,line:r,msg:e,url:t}),!!eP&&eP.apply(this,arguments)},ef.onerror.__SENTRY_INSTRUMENTED__=!0}let eN=null;function eL(){eN=ef.onunhandledrejection,ef.onunhandledrejection=function(e){return ej("unhandledrejection",e),!eN||eN.apply(this,arguments)},ef.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}function eD(){return eR(ef),ef}function eR(e){let t=e.__SENTRY__=e.__SENTRY__||{};return t.version=t.version||ep,t[ep]=t[ep]||{}}let eF=Object.prototype.toString;function eq(e){switch(eF.call(e)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return eQ(e,Error)}}function eV(e,t){return eF.call(e)===`[object ${t}]`}function eU(e){return eV(e,"ErrorEvent")}function e$(e){return eV(e,"DOMError")}function eB(e){return eV(e,"String")}function eJ(e){return"object"==typeof e&&null!==e&&"__sentry_template_string__"in e&&"__sentry_template_values__"in e}function eG(e){return null===e||eJ(e)||"object"!=typeof e&&"function"!=typeof e}function eY(e){return eV(e,"Object")}function ez(e){return"undefined"!=typeof Event&&eQ(e,Event)}function eW(e){return!!(e&&e.then&&"function"==typeof e.then)}function eQ(e,t){try{return e instanceof t}catch(e){return!1}}function eH(e){return!("object"!=typeof e||null===e||!e.__isVue&&!e._isVue)}function eK(e,t={}){if(!e)return"<unknown>";try{let r,n=e,i=[],a=0,s=0,o=Array.isArray(t)?t:t.keyAttrs,l=!Array.isArray(t)&&t.maxStringLength||80;for(;n&&a++<5&&(r=function(e,t){let r=[];if(!e||!e.tagName)return"";if(ef.HTMLElement&&e instanceof HTMLElement&&e.dataset){if(e.dataset.sentryComponent)return e.dataset.sentryComponent;if(e.dataset.sentryElement)return e.dataset.sentryElement}r.push(e.tagName.toLowerCase());let n=t&&t.length?t.filter(t=>e.getAttribute(t)).map(t=>[t,e.getAttribute(t)]):null;if(n&&n.length)n.forEach(e=>{r.push(`[${e[0]}="${e[1]}"]`)});else{e.id&&r.push(`#${e.id}`);let t=e.className;if(t&&eB(t))for(let e of t.split(/\s+/))r.push(`.${e}`)}for(let t of["aria-label","type","name","title","alt"]){let n=e.getAttribute(t);n&&r.push(`[${t}="${n}"]`)}return r.join("")}(n,o),!("html"===r||a>1&&s+3*i.length+r.length>=l));)i.push(r),s+=r.length,n=n.parentNode;return i.reverse().join(" > ")}catch(e){return"<unknown>"}}function eX(e,t=0){return"string"!=typeof e||0===t||e.length<=t?e:`${e.slice(0,t)}...`}function eZ(e,t){if(!Array.isArray(e))return"";let r=[];for(let t=0;t<e.length;t++){let n=e[t];try{eH(n)?r.push("[VueViewModel]"):r.push(String(n))}catch(e){r.push("[value cannot be serialized]")}}return r.join(t)}function e0(e,t=[],r=!1){return t.some(t=>(function(e,t,r=!1){return!!eB(e)&&(eV(t,"RegExp")?t.test(e):!!eB(t)&&(r?e===t:e.includes(t)))})(e,t,r))}function e1(e,t,r){if(!(t in e))return;let n=e[t],i=r(n);"function"==typeof i&&e3(i,n);try{e[t]=i}catch(r){eg&&e_.log(`Failed to replace method "${t}" in object`,e)}}function e2(e,t,r){try{Object.defineProperty(e,t,{value:r,writable:!0,configurable:!0})}catch(r){eg&&e_.log(`Failed to add non-enumerable property "${t}" to object`,e)}}function e3(e,t){try{let r=t.prototype||{};e.prototype=t.prototype=r,e2(e,"__sentry_original__",t)}catch(e){}}function e4(e){return e.__sentry_original__}function e5(e){if(eq(e))return{message:e.message,name:e.name,stack:e.stack,...e6(e)};if(ez(e)){let t={type:e.type,target:e8(e.target),currentTarget:e8(e.currentTarget),...e6(e)};return"undefined"!=typeof CustomEvent&&eQ(e,CustomEvent)&&(t.detail=e.detail),t}return e}function e8(e){try{return"undefined"!=typeof Element&&eQ(e,Element)?eK(e):Object.prototype.toString.call(e)}catch(e){return"<unknown>"}}function e6(e){if("object"==typeof e&&null!==e){let t={};for(let r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t}return{}}function e9(e){return function e(t,r){if(function(e){if(!eY(e))return!1;try{let t=Object.getPrototypeOf(e).constructor.name;return!t||"Object"===t}catch(e){return!0}}(t)){let n=r.get(t);if(void 0!==n)return n;let i={};for(let n of(r.set(t,i),Object.getOwnPropertyNames(t)))void 0!==t[n]&&(i[n]=e(t[n],r));return i}if(Array.isArray(t)){let n=r.get(t);if(void 0!==n)return n;let i=[];return r.set(t,i),t.forEach(t=>{i.push(e(t,r))}),i}return t}(e,new Map)}function e7(){return Date.now()/1e3}let te=function(){let{performance:e}=ef;if(!e||!e.now)return e7;let t=Date.now()-e.now(),r=null==e.timeOrigin?t:e.timeOrigin;return()=>(r+e.now())/1e3}();function tt(){let e=ef.crypto||ef.msCrypto,t=()=>16*Math.random();try{if(e&&e.randomUUID)return e.randomUUID().replace(/-/g,"");e&&e.getRandomValues&&(t=()=>{let t=new Uint8Array(1);return e.getRandomValues(t),t[0]})}catch(e){}return"10000000100040008000100000000000".replace(/[018]/g,e=>(e^(15&t())>>e/4).toString(16))}function tr(e){return e.exception&&e.exception.values?e.exception.values[0]:void 0}function tn(e){let{message:t,event_id:r}=e;if(t)return t;let n=tr(e);return n?n.type&&n.value?`${n.type}: ${n.value}`:n.type||n.value||r||"<unknown>":r||"<unknown>"}function ti(e,t,r){let n=e.exception=e.exception||{},i=n.values=n.values||[],a=i[0]=i[0]||{};a.value||(a.value=t||""),a.type||(a.type=r||"Error")}function ta(e,t){let r=tr(e);if(!r)return;let n=r.mechanism;if(r.mechanism={type:"generic",handled:!0,...n,...t},t&&"data"in t){let e={...n&&n.data,...t.data};r.mechanism.data=e}}function ts(e){if(function(e){try{return e.__sentry_captured__}catch(e){}}(e))return!0;try{e2(e,"__sentry_captured__",!0)}catch(e){}return!1}function to(e){return new tc(t=>{t(e)})}function tl(e){return new tc((t,r)=>{r(e)})}(()=>{let{performance:e}=ef;if(!e||!e.now)return;let t=e.now(),r=Date.now(),n=e.timeOrigin?Math.abs(e.timeOrigin+t-r):36e5,i=e.timing&&e.timing.navigationStart,a="number"==typeof i?Math.abs(i+t-r):36e5;(n<36e5||a<36e5)&&n<=a&&e.timeOrigin})(),function(e){e[e.PENDING=0]="PENDING",e[e.RESOLVED=1]="RESOLVED",e[e.REJECTED=2]="REJECTED"}(A||(A={}));class tc{constructor(e){tc.prototype.__init.call(this),tc.prototype.__init2.call(this),tc.prototype.__init3.call(this),tc.prototype.__init4.call(this),this._state=A.PENDING,this._handlers=[];try{e(this._resolve,this._reject)}catch(e){this._reject(e)}}then(e,t){return new tc((r,n)=>{this._handlers.push([!1,t=>{if(e)try{r(e(t))}catch(e){n(e)}else r(t)},e=>{if(t)try{r(t(e))}catch(e){n(e)}else n(e)}]),this._executeHandlers()})}catch(e){return this.then(e=>e,e)}finally(e){return new tc((t,r)=>{let n,i;return this.then(t=>{i=!1,n=t,e&&e()},t=>{i=!0,n=t,e&&e()}).then(()=>{i?r(n):t(n)})})}__init(){this._resolve=e=>{this._setResult(A.RESOLVED,e)}}__init2(){this._reject=e=>{this._setResult(A.REJECTED,e)}}__init3(){this._setResult=(e,t)=>{this._state===A.PENDING&&(eW(t)?t.then(this._resolve,this._reject):(this._state=e,this._value=t,this._executeHandlers()))}}__init4(){this._executeHandlers=()=>{if(this._state===A.PENDING)return;let e=this._handlers.slice();this._handlers=[],e.forEach(e=>{e[0]||(this._state===A.RESOLVED&&e[1](this._value),this._state===A.REJECTED&&e[2](this._value),e[0]=!0)})}}}function tu(e,t={}){if(t.user&&(!e.ipAddress&&t.user.ip_address&&(e.ipAddress=t.user.ip_address),e.did||t.did||(e.did=t.user.id||t.user.email||t.user.username)),e.timestamp=t.timestamp||te(),t.abnormal_mechanism&&(e.abnormal_mechanism=t.abnormal_mechanism),t.ignoreDuration&&(e.ignoreDuration=t.ignoreDuration),t.sid&&(e.sid=32===t.sid.length?t.sid:tt()),void 0!==t.init&&(e.init=t.init),!e.did&&t.did&&(e.did=`${t.did}`),"number"==typeof t.started&&(e.started=t.started),e.ignoreDuration)e.duration=void 0;else if("number"==typeof t.duration)e.duration=t.duration;else{let t=e.timestamp-e.started;e.duration=t>=0?t:0}t.release&&(e.release=t.release),t.environment&&(e.environment=t.environment),!e.ipAddress&&t.ipAddress&&(e.ipAddress=t.ipAddress),!e.userAgent&&t.userAgent&&(e.userAgent=t.userAgent),"number"==typeof t.errors&&(e.errors=t.errors),t.status&&(e.status=t.status)}function td(){return tt().substring(16)}function th(e,t,r=2){if(!t||"object"!=typeof t||r<=0)return t;if(e&&t&&0===Object.keys(t).length)return e;let n={...e};for(let e in t)Object.prototype.hasOwnProperty.call(t,e)&&(n[e]=th(n[e],t[e],r-1));return n}let tp="_sentrySpan";function tf(e,t){t?e2(e,tp,t):delete e[tp]}class tm{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:tt(),spanId:td()}}clone(){let e=new tm;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},this._contexts.flags&&(e._contexts.flags={values:[...this._contexts.flags.values]}),e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._requestSession=this._requestSession,e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,tf(e,this[tp]),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&tu(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}getRequestSession(){return this._requestSession}setRequestSession(e){return this._requestSession=e,this}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,t){return this._tags={...this._tags,[e]:t},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,t){return this._extra={...this._extra,[e]:t},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,t){return null===t?delete this._contexts[e]:this._contexts[e]=t,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;let t="function"==typeof e?e(this):e,[r,n]=t instanceof tg?[t.getScopeData(),t.getRequestSession()]:eY(t)?[e,e.requestSession]:[],{tags:i,extra:a,user:s,contexts:o,level:l,fingerprint:c=[],propagationContext:u}=r||{};return this._tags={...this._tags,...i},this._extra={...this._extra,...a},this._contexts={...this._contexts,...o},s&&Object.keys(s).length&&(this._user=s),l&&(this._level=l),c.length&&(this._fingerprint=c),u&&(this._propagationContext=u),n&&(this._requestSession=n),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._requestSession=void 0,this._session=void 0,tf(this,void 0),this._attachments=[],this.setPropagationContext({traceId:tt()}),this._notifyScopeListeners(),this}addBreadcrumb(e,t){let r="number"==typeof t?t:100;if(r<=0)return this;let n={timestamp:e7(),...e};return this._breadcrumbs.push(n),this._breadcrumbs.length>r&&(this._breadcrumbs=this._breadcrumbs.slice(-r),this._client&&this._client.recordDroppedEvent("buffer_overflow","log_item")),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:this[tp]}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata=th(this._sdkProcessingMetadata,e,2),this}setPropagationContext(e){return this._propagationContext={spanId:td(),...e},this}getPropagationContext(){return this._propagationContext}captureException(e,t){let r=t&&t.event_id?t.event_id:tt();if(!this._client)return e_.warn("No client configured on scope - will not capture exception!"),r;let n=Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:n,...t,event_id:r},this),r}captureMessage(e,t,r){let n=r&&r.event_id?r.event_id:tt();if(!this._client)return e_.warn("No client configured on scope - will not capture message!"),n;let i=Error(e);return this._client.captureMessage(e,t,{originalException:e,syntheticException:i,...r,event_id:n},this),n}captureEvent(e,t){let r=t&&t.event_id?t.event_id:tt();return this._client?this._client.captureEvent(e,{...t,event_id:r},this):e_.warn("No client configured on scope - will not capture event!"),r}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}let tg=tm;class tv{constructor(e,t){let r,n;r=e||new tg,n=t||new tg,this._stack=[{scope:r}],this._isolationScope=n}withScope(e){let t;let r=this._pushScope();try{t=e(r)}catch(e){throw this._popScope(),e}return eW(t)?t.then(e=>(this._popScope(),e),e=>{throw this._popScope(),e}):(this._popScope(),t)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){let e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function ty(){let e=eR(eD());return e.stack=e.stack||new tv(em("defaultCurrentScope",()=>new tg),em("defaultIsolationScope",()=>new tg))}function tb(e){return ty().withScope(e)}function t_(e,t){let r=ty();return r.withScope(()=>(r.getStackTop().scope=e,t(e)))}function tS(e){return ty().withScope(()=>e(ty().getIsolationScope()))}function tw(e){let t=eR(e);return t.acs?t.acs:{withIsolationScope:tS,withScope:tb,withSetScope:t_,withSetIsolationScope:(e,t)=>tS(t),getCurrentScope:()=>ty().getScope(),getIsolationScope:()=>ty().getIsolationScope()}}function tk(){return tw(eD()).getCurrentScope()}function tE(){return tw(eD()).getIsolationScope()}function tC(){return tk().getClient()}let tA=/^sentry-/;function tT(e){return e.split(",").map(e=>e.split("=").map(e=>decodeURIComponent(e.trim()))).reduce((e,[t,r])=>(t&&r&&(e[t]=r),e),{})}let tM=!1;function tx(e){return"number"==typeof e?tO(e):Array.isArray(e)?e[0]+e[1]/1e9:e instanceof Date?tO(e.getTime()):te()}function tO(e){return e>0x2540be3ff?e/1e3:e}function tj(e){if("function"==typeof e.getSpanJSON)return e.getSpanJSON();try{let{spanId:t,traceId:r}=e.spanContext();if(e.attributes&&e.startTime&&e.name&&e.endTime&&e.status){let{attributes:n,startTime:i,name:a,endTime:s,parentSpanId:o,status:l}=e;return e9({span_id:t,trace_id:r,data:n,description:a,parent_span_id:o,start_timestamp:tx(i),timestamp:tx(s)||void 0,status:function(e){if(e&&0!==e.code)return 1===e.code?"ok":e.message||"unknown_error"}(l),op:n["sentry.op"],origin:n["sentry.origin"],_metrics_summary:function(e){let t=e._sentryMetrics;if(!t)return;let r={};for(let[,[e,n]]of t)(r[e]||(r[e]=[])).push(e9(n));return r}(e)})}return{span_id:t,trace_id:r}}catch(e){return{}}}function tP(e){return e._sentryRootSpan||e}let tI="production";function tN(e,t){let r=t.getOptions(),{publicKey:n}=t.getDsn()||{},i=e9({environment:r.environment||tI,release:r.release,public_key:n,trace_id:e});return t.emit("createDsc",i),i}let tL=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function tD(e,t=!1){let{host:r,path:n,pass:i,port:a,projectId:s,protocol:o,publicKey:l}=e;return`${o}://${l}${t&&i?`:${i}`:""}@${r}${a?`:${a}`:""}/${n?`${n}/`:n}${s}`}function tR(e){return{protocol:e.protocol,publicKey:e.publicKey||"",pass:e.pass||"",host:e.host,port:e.port||"",path:e.path||"",projectId:e.projectId}}function tF(e,t=100,r=1/0){try{return function e(t,r,n=1/0,i=1/0,a=function(){let e="function"==typeof WeakSet,t=e?new WeakSet:[];return[function(r){if(e)return!!t.has(r)||(t.add(r),!1);for(let e=0;e<t.length;e++)if(t[e]===r)return!0;return t.push(r),!1},function(r){if(e)t.delete(r);else for(let e=0;e<t.length;e++)if(t[e]===r){t.splice(e,1);break}}]}()){let[s,o]=a;if(null==r||["boolean","string"].includes(typeof r)||"number"==typeof r&&Number.isFinite(r))return r;let l=function(e,t){try{if("domain"===e&&t&&"object"==typeof t&&t._events)return"[Domain]";if("domainEmitter"===e)return"[DomainEmitter]";if("undefined"!=typeof global&&t===global)return"[Global]";if("undefined"!=typeof window&&t===window)return"[Window]";if("undefined"!=typeof document&&t===document)return"[Document]";if(eH(t))return"[VueViewModel]";if(eY(t)&&"nativeEvent"in t&&"preventDefault"in t&&"stopPropagation"in t)return"[SyntheticEvent]";if("number"==typeof t&&!Number.isFinite(t))return`[${t}]`;if("function"==typeof t)return`[Function: ${eC(t)}]`;if("symbol"==typeof t)return`[${String(t)}]`;if("bigint"==typeof t)return`[BigInt: ${String(t)}]`;let r=function(e){let t=Object.getPrototypeOf(e);return t?t.constructor.name:"null prototype"}(t);return/^HTML(\w*)Element$/.test(r)?`[HTMLElement: ${r}]`:`[object ${r}]`}catch(e){return`**non-serializable** (${e})`}}(t,r);if(!l.startsWith("[object "))return l;if(r.__sentry_skip_normalization__)return r;let c="number"==typeof r.__sentry_override_normalization_depth__?r.__sentry_override_normalization_depth__:n;if(0===c)return l.replace("object ","");if(s(r))return"[Circular ~]";if(r&&"function"==typeof r.toJSON)try{return e("",r.toJSON(),c-1,i,a)}catch(e){}let u=Array.isArray(r)?[]:{},d=0,h=e5(r);for(let t in h){if(!Object.prototype.hasOwnProperty.call(h,t))continue;if(d>=i){u[t]="[MaxProperties ~]";break}let r=h[t];u[t]=e(t,r,c-1,i,a),d++}return o(r),u}("",e,t,r)}catch(e){return{ERROR:`**non-serializable** (${e})`}}}function tq(e,t=[]){return[e,t]}function tV(e,t){for(let r of e[1])if(t(r,r[0].type))return!0;return!1}function tU(e){return ef.__SENTRY__&&ef.__SENTRY__.encodePolyfill?ef.__SENTRY__.encodePolyfill(e):(new TextEncoder).encode(e)}let t$={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",statsd:"metric_bucket",raw_security:"security"};function tB(e){if(!e||!e.sdk)return;let{name:t,version:r}=e.sdk;return{name:t,version:r}}function tJ(e,t){let{extra:r,tags:n,user:i,contexts:a,level:s,sdkProcessingMetadata:o,breadcrumbs:l,fingerprint:c,eventProcessors:u,attachments:d,propagationContext:h,transactionName:p,span:f}=t;tG(e,"extra",r),tG(e,"tags",n),tG(e,"user",i),tG(e,"contexts",a),e.sdkProcessingMetadata=th(e.sdkProcessingMetadata,o,2),s&&(e.level=s),p&&(e.transactionName=p),f&&(e.span=f),l.length&&(e.breadcrumbs=[...e.breadcrumbs,...l]),c.length&&(e.fingerprint=[...e.fingerprint,...c]),u.length&&(e.eventProcessors=[...e.eventProcessors,...u]),d.length&&(e.attachments=[...e.attachments,...d]),e.propagationContext={...e.propagationContext,...h}}function tG(e,t,r){e[t]=th(e[t],r,1)}let tY=["user","level","extra","contexts","tags","fingerprint","requestSession","propagationContext"];function tz(e,t){return tk().captureEvent(e,t)}function tW(e){let t=tC(),r=tE(),n=tk(),{release:i,environment:a=tI}=t&&t.getOptions()||{},{userAgent:s}=ef.navigator||{},o=function(e){let t=te(),r={sid:tt(),init:!0,timestamp:t,started:t,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>e9({sid:`${r.sid}`,init:r.init,started:new Date(1e3*r.started).toISOString(),timestamp:new Date(1e3*r.timestamp).toISOString(),status:r.status,errors:r.errors,did:"number"==typeof r.did||"string"==typeof r.did?`${r.did}`:void 0,duration:r.duration,abnormal_mechanism:r.abnormal_mechanism,attrs:{release:r.release,environment:r.environment,ip_address:r.ipAddress,user_agent:r.userAgent}})};return e&&tu(r,e),r}({release:i,environment:a,user:n.getUser()||r.getUser(),...s&&{userAgent:s},...e}),l=r.getSession();return l&&"ok"===l.status&&tu(l,{status:"exited"}),tQ(),r.setSession(o),n.setSession(o),o}function tQ(){let e;let t=tE(),r=tk(),n=r.getSession()||t.getSession();n&&(e={},"ok"===n.status&&(e={status:"exited"}),tu(n,e)),tH(),t.setSession(),r.setSession()}function tH(){let e=tE(),t=tk(),r=tC(),n=t.getSession()||e.getSession();n&&r&&r.captureSession(n)}function tK(e=!1){e?tQ():tH()}let tX=[];function tZ(e,t){for(let r of t)r&&r.afterAllSetup&&r.afterAllSetup(e)}function t0(e,t,r){if(r[t.name])eh&&e_.log(`Integration skipped because it was already installed: ${t.name}`);else{if(r[t.name]=t,-1===tX.indexOf(t.name)&&"function"==typeof t.setupOnce&&(t.setupOnce(),tX.push(t.name)),t.setup&&"function"==typeof t.setup&&t.setup(e),"function"==typeof t.preprocessEvent){let r=t.preprocessEvent.bind(t);e.on("preprocessEvent",(t,n)=>r(t,n,e))}if("function"==typeof t.processEvent){let r=t.processEvent.bind(t),n=Object.assign((t,n)=>r(t,n,e),{id:t.name});e.addEventProcessor(n)}eh&&e_.log(`Integration installed: ${t.name}`)}}class t1 extends Error{constructor(e,t="warn"){super(e),this.message=e,this.logLevel=t}}let t2="Not capturing exception because it's already been captured.";class t3{constructor(e){if(this._options=e,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn?this._dsn=function(e){let t="string"==typeof e?function(e){let t=tL.exec(e);if(!t)return void eb(()=>{console.error(`Invalid Sentry Dsn: ${e}`)});let[r,n,i="",a="",s="",o=""]=t.slice(1),l="",c=o,u=c.split("/");if(u.length>1&&(l=u.slice(0,-1).join("/"),c=u.pop()),c){let e=c.match(/^\d+/);e&&(c=e[0])}return tR({host:a,pass:i,path:l,projectId:c,port:s,protocol:r,publicKey:n})}(e):tR(e);if(t&&function(e){if(!eg)return!0;let{port:t,projectId:r,protocol:n}=e;return!(["protocol","publicKey","host","projectId"].find(t=>!e[t]&&(e_.error(`Invalid Sentry Dsn: ${t} missing`),!0))||(r.match(/^\d+$/)?"http"===n||"https"===n?t&&isNaN(parseInt(t,10))&&(e_.error(`Invalid Sentry Dsn: Invalid port ${t}`),1):(e_.error(`Invalid Sentry Dsn: Invalid protocol ${n}`),1):(e_.error(`Invalid Sentry Dsn: Invalid projectId ${r}`),1)))}(t))return t}(e.dsn):eh&&e_.warn("No DSN provided, client will not send events."),this._dsn){let t=function(e,t,r){return t||`${function(e){let t=e.protocol?`${e.protocol}:`:"",r=e.port?`:${e.port}`:"";return`${t}//${e.host}${r}${e.path?`/${e.path}`:""}/api/`}(e)}${e.projectId}/envelope/?${function(e,t){let r={sentry_version:"7"};return e.publicKey&&(r.sentry_key=e.publicKey),t&&(r.sentry_client=`${t.name}/${t.version}`),new URLSearchParams(r).toString()}(e,r)}`}(this._dsn,e.tunnel,e._metadata?e._metadata.sdk:void 0);this._transport=e.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:t})}let t=["enableTracing","tracesSampleRate","tracesSampler"].find(t=>t in e&&null==e[t]);t&&eb(()=>{console.warn(`[Sentry] Deprecation warning: \`${t}\` is set to undefined, which leads to tracing being enabled. In v9, a value of \`undefined\` will result in tracing being disabled.`)})}captureException(e,t,r){let n=tt();if(ts(e))return eh&&e_.log(t2),n;let i={event_id:n,...t};return this._process(this.eventFromException(e,i).then(e=>this._captureEvent(e,i,r))),i.event_id}captureMessage(e,t,r,n){let i={event_id:tt(),...r},a=eJ(e)?e:String(e),s=eG(e)?this.eventFromMessage(a,t,i):this.eventFromException(e,i);return this._process(s.then(e=>this._captureEvent(e,i,n))),i.event_id}captureEvent(e,t,r){let n=tt();if(t&&t.originalException&&ts(t.originalException))return eh&&e_.log(t2),n;let i={event_id:n,...t},a=(e.sdkProcessingMetadata||{}).capturedSpanScope;return this._process(this._captureEvent(e,i,a||r)),i.event_id}captureSession(e){"string"!=typeof e.release?eh&&e_.warn("Discarded session because of missing or non-string release"):(this.sendSession(e),tu(e,{init:!1}))}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){let t=this._transport;return t?(this.emit("flush"),this._isClientDoneProcessing(e).then(r=>t.flush(e).then(e=>r&&e))):to(!0)}close(e){return this.flush(e).then(e=>(this.getOptions().enabled=!1,this.emit("close"),e))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}init(){(this._isEnabled()||this._options.integrations.some(({name:e})=>e.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(e){return this._integrations[e]}addIntegration(e){let t=this._integrations[e.name];t0(this,e,this._integrations),t||tZ(this,[e])}sendEvent(e,t={}){this.emit("beforeSendEvent",e,t);let r=function(e,t,r,n){var i;let a=tB(r),s=e.type&&"replay_event"!==e.type?e.type:"event";(i=r&&r.sdk)&&(e.sdk=e.sdk||{},e.sdk.name=e.sdk.name||i.name,e.sdk.version=e.sdk.version||i.version,e.sdk.integrations=[...e.sdk.integrations||[],...i.integrations||[]],e.sdk.packages=[...e.sdk.packages||[],...i.packages||[]]);let o=function(e,t,r,n){let i=e.sdkProcessingMetadata&&e.sdkProcessingMetadata.dynamicSamplingContext;return{event_id:e.event_id,sent_at:(new Date).toISOString(),...t&&{sdk:t},...!!r&&n&&{dsn:tD(n)},...i&&{trace:e9({...i})}}}(e,a,n,t);return delete e.sdkProcessingMetadata,tq(o,[[{type:s},e]])}(e,this._dsn,this._options._metadata,this._options.tunnel);for(let e of t.attachments||[])r=function(e,t){let[r,n]=e;return[r,[...n,t]]}(r,function(e){let t="string"==typeof e.data?tU(e.data):e.data;return[e9({type:"attachment",length:t.length,filename:e.filename,content_type:e.contentType,attachment_type:e.attachmentType}),t]}(e));let n=this.sendEnvelope(r);n&&n.then(t=>this.emit("afterSendEvent",e,t),null)}sendSession(e){let t=function(e,t,r,n){let i=tB(r);return tq({sent_at:(new Date).toISOString(),...i&&{sdk:i},...!!n&&t&&{dsn:tD(t)}},["aggregates"in e?[{type:"sessions"},e]:[{type:"session"},e.toJSON()]])}(e,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(t)}recordDroppedEvent(e,t,r){if(this._options.sendClientReports){let n="number"==typeof r?r:1,i=`${e}:${t}`;eh&&e_.log(`Recording outcome: "${i}"${n>1?` (${n} times)`:""}`),this._outcomes[i]=(this._outcomes[i]||0)+n}}on(e,t){let r=this._hooks[e]=this._hooks[e]||[];return r.push(t),()=>{let e=r.indexOf(t);e>-1&&r.splice(e,1)}}emit(e,...t){let r=this._hooks[e];r&&r.forEach(e=>e(...t))}sendEnvelope(e){return this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport?this._transport.send(e).then(null,e=>(eh&&e_.error("Error while sending envelope:",e),e)):(eh&&e_.error("Transport disabled"),to({}))}_setupIntegrations(){let{integrations:e}=this._options;this._integrations=function(e,t){let r={};return t.forEach(t=>{t&&t0(e,t,r)}),r}(this,e),tZ(this,e)}_updateSessionFromEvent(e,t){let r="fatal"===t.level,n=!1,i=t.exception&&t.exception.values;if(i)for(let e of(n=!0,i)){let t=e.mechanism;if(t&&!1===t.handled){r=!0;break}}let a="ok"===e.status;(a&&0===e.errors||a&&r)&&(tu(e,{...r&&{status:"crashed"},errors:e.errors||Number(n||r)}),this.captureSession(e))}_isClientDoneProcessing(e){return new tc(t=>{let r=0,n=setInterval(()=>{0==this._numProcessing?(clearInterval(n),t(!0)):(r+=1,e&&r>=e&&(clearInterval(n),t(!1)))},1)})}_isEnabled(){return!1!==this.getOptions().enabled&&void 0!==this._transport}_prepareEvent(e,t,r=tk(),s=tE()){let o=this.getOptions(),l=Object.keys(this._integrations);return!t.integrations&&l.length>0&&(t.integrations=l),this.emit("preprocessEvent",e,t),e.type||s.setLastEventId(e.event_id||t.event_id),(function(e,t,r,s,o,l){let{normalizeDepth:c=3,normalizeMaxBreadth:u=1e3}=e,d={...t,event_id:t.event_id||r.event_id||tt(),timestamp:t.timestamp||e7()},h=r.integrations||e.integrations.map(e=>e.name);(function(e,t){let{environment:r,release:n,dist:i,maxValueLength:a=250}=t;e.environment=e.environment||r||tI,!e.release&&n&&(e.release=n),!e.dist&&i&&(e.dist=i),e.message&&(e.message=eX(e.message,a));let s=e.exception&&e.exception.values&&e.exception.values[0];s&&s.value&&(s.value=eX(s.value,a));let o=e.request;o&&o.url&&(o.url=eX(o.url,a))})(d,e),h.length>0&&(d.sdk=d.sdk||{},d.sdk.integrations=[...d.sdk.integrations||[],...h]),o&&o.emit("applyFrameMetadata",t),void 0===t.type&&function(e,t){let r=function(e){let t=ef._sentryDebugIds;if(!t)return{};let r=Object.keys(t);return a&&r.length===i||(i=r.length,a=r.reduce((r,i)=>{n||(n={});let a=n[i];if(a)r[a[0]]=a[1];else{let a=e(i);for(let e=a.length-1;e>=0;e--){let s=a[e],o=s&&s.filename,l=t[i];if(o&&l){r[o]=l,n[i]=[o,l];break}}}return r},{})),a}(t);try{e.exception.values.forEach(e=>{e.stacktrace.frames.forEach(e=>{r&&e.filename&&(e.debug_id=r[e.filename])})})}catch(e){}}(d,e.stackParser);let p=function(e,t){if(!t)return e;let r=e?e.clone():new tg;return r.update(t),r}(s,r.captureContext);r.mechanism&&ta(d,r.mechanism);let f=o?o.getEventProcessors():[],m=em("globalScope",()=>new tg).getScopeData();l&&tJ(m,l.getScopeData()),p&&tJ(m,p.getScopeData());let g=[...r.attachments||[],...m.attachments];return g.length&&(r.attachments=g),function(e,t){let{fingerprint:r,span:n,breadcrumbs:i,sdkProcessingMetadata:a}=t;(function(e,t){let{extra:r,tags:n,user:i,contexts:a,level:s,transactionName:o}=t,l=e9(r);l&&Object.keys(l).length&&(e.extra={...l,...e.extra});let c=e9(n);c&&Object.keys(c).length&&(e.tags={...c,...e.tags});let u=e9(i);u&&Object.keys(u).length&&(e.user={...u,...e.user});let d=e9(a);d&&Object.keys(d).length&&(e.contexts={...d,...e.contexts}),s&&(e.level=s),o&&"transaction"!==e.type&&(e.transaction=o)})(e,t),n&&function(e,t){e.contexts={trace:function(e){let{spanId:t,traceId:r,isRemote:n}=e.spanContext();return e9({parent_span_id:n?t:tj(e).parent_span_id,span_id:n?td():t,trace_id:r})}(t),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:function(e){let t=tC();if(!t)return{};let r=tP(e),n=r._frozenDsc;if(n)return n;let i=r.spanContext().traceState,a=i&&i.get("sentry.dsc"),s=a&&function(e){let t=function(e){if(e&&(eB(e)||Array.isArray(e)))return Array.isArray(e)?e.reduce((e,t)=>(Object.entries(tT(t)).forEach(([t,r])=>{e[t]=r}),e),{}):tT(e)}(e);if(!t)return;let r=Object.entries(t).reduce((e,[t,r])=>(t.match(tA)&&(e[t.slice(7)]=r),e),{});return Object.keys(r).length>0?r:void 0}(a);if(s)return s;let o=tN(e.spanContext().traceId,t),l=tj(r),c=l.data||{},u=c["sentry.sample_rate"];null!=u&&(o.sample_rate=`${u}`);let d=c["sentry.source"],h=l.description;return"url"!==d&&h&&(o.transaction=h),function(e){if("boolean"==typeof __SENTRY_TRACING__&&!__SENTRY_TRACING__)return!1;let t=tC(),r=t&&t.getOptions();return!!r&&(r.enableTracing||"tracesSampleRate"in r||"tracesSampler"in r)}()&&(o.sampled=String(function(e){let{traceFlags:t}=e.spanContext();return 1===t}(r))),t.emit("createDsc",o,r),o}(t),...e.sdkProcessingMetadata};let r=tj(tP(t)).description;r&&!e.transaction&&"transaction"===e.type&&(e.transaction=r)}(e,n),e.fingerprint=e.fingerprint?Array.isArray(e.fingerprint)?e.fingerprint:[e.fingerprint]:[],r&&(e.fingerprint=e.fingerprint.concat(r)),e.fingerprint&&!e.fingerprint.length&&delete e.fingerprint,function(e,t){let r=[...e.breadcrumbs||[],...t];e.breadcrumbs=r.length?r:void 0}(e,i),e.sdkProcessingMetadata={...e.sdkProcessingMetadata,...a}}(d,m),(function e(t,r,n,i=0){return new tc((a,s)=>{let o=t[i];if(null===r||"function"!=typeof o)a(r);else{let l=o({...r},n);eh&&o.id&&null===l&&e_.log(`Event processor "${o.id}" dropped event`),eW(l)?l.then(r=>e(t,r,n,i+1).then(a)).then(null,s):e(t,l,n,i+1).then(a).then(null,s)}})})([...f,...m.eventProcessors],d,r).then(e=>(e&&function(e){let t={};try{e.exception.values.forEach(e=>{e.stacktrace.frames.forEach(e=>{e.debug_id&&(e.abs_path?t[e.abs_path]=e.debug_id:e.filename&&(t[e.filename]=e.debug_id),delete e.debug_id)})})}catch(e){}if(0===Object.keys(t).length)return;e.debug_meta=e.debug_meta||{},e.debug_meta.images=e.debug_meta.images||[];let r=e.debug_meta.images;Object.entries(t).forEach(([e,t])=>{r.push({type:"sourcemap",code_file:e,debug_id:t})})}(e),"number"==typeof c&&c>0?function(e,t,r){if(!e)return null;let n={...e,...e.breadcrumbs&&{breadcrumbs:e.breadcrumbs.map(e=>({...e,...e.data&&{data:tF(e.data,t,r)}}))},...e.user&&{user:tF(e.user,t,r)},...e.contexts&&{contexts:tF(e.contexts,t,r)},...e.extra&&{extra:tF(e.extra,t,r)}};return e.contexts&&e.contexts.trace&&n.contexts&&(n.contexts.trace=e.contexts.trace,e.contexts.trace.data&&(n.contexts.trace.data=tF(e.contexts.trace.data,t,r))),e.spans&&(n.spans=e.spans.map(e=>({...e,...e.data&&{data:tF(e.data,t,r)}}))),e.contexts&&e.contexts.flags&&n.contexts&&(n.contexts.flags=tF(e.contexts.flags,3,r)),n}(e,c,u):e))})(o,e,t,r,this,s).then(e=>(null===e||(e.contexts={trace:function(e){let{traceId:t,spanId:r,parentSpanId:n}=e.getPropagationContext();return e9({trace_id:t,span_id:r,parent_span_id:n})}(r),...e.contexts},e.sdkProcessingMetadata={dynamicSamplingContext:function(e,t){let r=t.getPropagationContext();return r.dsc||tN(r.traceId,e)}(this,r),...e.sdkProcessingMetadata}),e))}_captureEvent(e,t={},r){return this._processEvent(e,t,r).then(e=>e.event_id,e=>{eh&&(e instanceof t1&&"log"===e.logLevel?e_.log(e.message):e_.warn(e))})}_processEvent(e,t,r){let n=this.getOptions(),{sampleRate:i}=n,a=t5(e),s=t4(e),o=e.type||"error",l=`before send for type \`${o}\``,c=void 0===i?void 0:function(e){if("boolean"==typeof e)return Number(e);let t="string"==typeof e?parseFloat(e):e;if(!("number"!=typeof t||isNaN(t)||t<0||t>1))return t;eh&&e_.warn(`[Tracing] Given sample rate is invalid. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(e)} of type ${JSON.stringify(typeof e)}.`)}(i);if(s&&"number"==typeof c&&Math.random()>c)return this.recordDroppedEvent("sample_rate","error",e),tl(new t1(`Discarding event because it's not included in the random sample (sampling rate = ${i})`,"log"));let u="replay_event"===o?"replay":o,d=(e.sdkProcessingMetadata||{}).capturedSpanIsolationScope;return this._prepareEvent(e,t,r,d).then(r=>{if(null===r)throw this.recordDroppedEvent("event_processor",u,e),new t1("An event processor returned `null`, will not send event.","log");return t.data&&!0===t.data.__sentry__?r:function(e,t){let r=`${t} must return \`null\` or a valid event.`;if(eW(e))return e.then(e=>{if(!eY(e)&&null!==e)throw new t1(r);return e},e=>{throw new t1(`${t} rejected with ${e}`)});if(!eY(e)&&null!==e)throw new t1(r);return e}(function(e,t,r,n){let{beforeSend:i,beforeSendTransaction:a,beforeSendSpan:s}=t;if(t4(r)&&i)return i(r,n);if(t5(r)){if(r.spans&&s){let t=[];for(let n of r.spans){let r=s(n);r?t.push(r):(tM||(eb(()=>{console.warn("[Sentry] Deprecation warning: Returning null from `beforeSendSpan` will be disallowed from SDK version 9.0.0 onwards. The callback will only support mutating spans. To drop certain spans, configure the respective integrations directly.")}),tM=!0),e.recordDroppedEvent("before_send","span"))}r.spans=t}if(a){if(r.spans){let e=r.spans.length;r.sdkProcessingMetadata={...r.sdkProcessingMetadata,spanCountBeforeProcessing:e}}return a(r,n)}}return r}(this,n,r,t),l)}).then(n=>{if(null===n){if(this.recordDroppedEvent("before_send",u,e),a){let t=1+(e.spans||[]).length;this.recordDroppedEvent("before_send","span",t)}throw new t1(`${l} returned \`null\`, will not send event.`,"log")}let i=r&&r.getSession();if(!a&&i&&this._updateSessionFromEvent(i,n),a){let e=(n.sdkProcessingMetadata&&n.sdkProcessingMetadata.spanCountBeforeProcessing||0)-(n.spans?n.spans.length:0);e>0&&this.recordDroppedEvent("before_send","span",e)}let s=n.transaction_info;return a&&s&&n.transaction!==e.transaction&&(n.transaction_info={...s,source:"custom"}),this.sendEvent(n,t),n}).then(null,e=>{if(e instanceof t1)throw e;throw this.captureException(e,{data:{__sentry__:!0},originalException:e}),new t1(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${e}`)})}_process(e){this._numProcessing++,e.then(e=>(this._numProcessing--,e),e=>(this._numProcessing--,e))}_clearOutcomes(){let e=this._outcomes;return this._outcomes={},Object.entries(e).map(([e,t])=>{let[r,n]=e.split(":");return{reason:r,category:n,quantity:t}})}_flushOutcomes(){var e,t;eh&&e_.log("Flushing outcomes...");let r=this._clearOutcomes();if(0===r.length)return void(eh&&e_.log("No outcomes to send"));if(!this._dsn)return void(eh&&e_.log("No dsn provided, will not send outcomes"));eh&&e_.log("Sending outcomes:",r);let n=tq((e=this._options.tunnel&&tD(this._dsn))?{dsn:e}:{},[[{type:"client_report"},{timestamp:t||e7(),discarded_events:r}]]);this.sendEnvelope(n)}}function t4(e){return void 0===e.type}function t5(e){return"transaction"===e.type}function t8(e,t){if("event"===t||"transaction"===t)return Array.isArray(e)?e[1]:void 0}function t6(e,t){let r=tC(),n=tE();if(!r)return;let{beforeBreadcrumb:i=null,maxBreadcrumbs:a=100}=r.getOptions();if(a<=0)return;let s={timestamp:e7(),...e},o=i?eb(()=>i(s,t)):s;null!==o&&(r.emit&&r.emit("beforeAddBreadcrumb",o,t),n.addBreadcrumb(o,a))}let t9=new WeakMap,t7=()=>({name:"FunctionToString",setupOnce(){s=Function.prototype.toString;try{Function.prototype.toString=function(...e){let t=e4(this),r=t9.has(tC())&&void 0!==t?t:this;return s.apply(r,e)}}catch(e){}},setup(e){t9.set(e,!0)}}),re=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,"undefined is not an object (evaluating 'a.L')",'can\'t redefine non-configurable property "solana"',"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/],rt=(e={})=>({name:"InboundFilters",processEvent:(t,r,n)=>!function(e,t){var r;return t.ignoreInternal&&function(e){try{return"SentryError"===e.exception.values[0].type}catch(e){}return!1}(e)?(eh&&e_.warn(`Event dropped due to being internal Sentry Error.
Event: ${tn(e)}`),!0):(r=t.ignoreErrors,!e.type&&r&&r.length&&(function(e){let t;let r=[];e.message&&r.push(e.message);try{t=e.exception.values[e.exception.values.length-1]}catch(e){}return t&&t.value&&(r.push(t.value),t.type&&r.push(`${t.type}: ${t.value}`)),r})(e).some(e=>e0(e,r)))?(eh&&e_.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${tn(e)}`),!0):e.type||!e.exception||!e.exception.values||0===e.exception.values.length||e.message||e.exception.values.some(e=>e.stacktrace||e.type&&"Error"!==e.type||e.value)?!function(e,t){if("transaction"!==e.type||!t||!t.length)return!1;let r=e.transaction;return!!r&&e0(r,t)}(e,t.ignoreTransactions)?!function(e,t){if(!t||!t.length)return!1;let r=rr(e);return!!r&&e0(r,t)}(e,t.denyUrls)?!function(e,t){if(!t||!t.length)return!0;let r=rr(e);return!r||e0(r,t)}(e,t.allowUrls)&&(eh&&e_.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${tn(e)}.
Url: ${rr(e)}`),!0):(eh&&e_.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${tn(e)}.
Url: ${rr(e)}`),!0):(eh&&e_.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${tn(e)}`),!0):(eh&&e_.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${tn(e)}`),!0)}(t,function(e={},t={}){return{allowUrls:[...e.allowUrls||[],...t.allowUrls||[]],denyUrls:[...e.denyUrls||[],...t.denyUrls||[]],ignoreErrors:[...e.ignoreErrors||[],...t.ignoreErrors||[],...e.disableErrorDefaults?[]:re],ignoreTransactions:[...e.ignoreTransactions||[],...t.ignoreTransactions||[]],ignoreInternal:void 0===e.ignoreInternal||e.ignoreInternal}}(e,n.getOptions()))?t:null});function rr(e){try{let t;try{t=e.exception.values[0].stacktrace.frames}catch(e){}return t?function(e=[]){for(let t=e.length-1;t>=0;t--){let r=e[t];if(r&&"<anonymous>"!==r.filename&&"[native code]"!==r.filename)return r.filename||null}return null}(t):null}catch(t){return eh&&e_.error(`Cannot extract url for event ${tn(e)}`),null}}function rn(e,t){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,..."AggregateError"===e.type&&{is_exception_group:!0},exception_id:t}}function ri(e,t,r,n){e.mechanism=e.mechanism||{type:"generic",handled:!0},e.mechanism={...e.mechanism,type:"chained",source:t,exception_id:r,parent_id:n}}function ra(e){if(!e)return{};let t=e.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!t)return{};let r=t[6]||"",n=t[8]||"";return{host:t[4],path:t[5],protocol:t[2],search:r,hash:n,relative:t[5]+r+n}}function rs(){"console"in ef&&ev.forEach(function(e){e in ef.console&&e1(ef.console,e,function(t){return ey[e]=t,function(...t){ej("console",{args:t,level:e});let r=ey[e];r&&r.apply(ef.console,t)}})})}let ro=()=>{let e;return{name:"Dedupe",processEvent(t){if(t.type)return t;try{var r;if((r=e)&&(function(e,t){let r=e.message,n=t.message;return!!((r||n)&&(!r||n)&&(r||!n)&&r===n&&rc(e,t)&&rl(e,t))}(t,r)||function(e,t){let r=ru(t),n=ru(e);return!!(r&&n&&r.type===n.type&&r.value===n.value&&rc(e,t)&&rl(e,t))}(t,r)))return eh&&e_.warn("Event dropped due to being a duplicate of previously captured event."),null}catch(e){}return e=t}}};function rl(e,t){let r=eA(e),n=eA(t);if(!r&&!n)return!0;if(r&&!n||!r&&n||n.length!==r.length)return!1;for(let e=0;e<n.length;e++){let t=n[e],i=r[e];if(t.filename!==i.filename||t.lineno!==i.lineno||t.colno!==i.colno||t.function!==i.function)return!1}return!0}function rc(e,t){let r=e.fingerprint,n=t.fingerprint;if(!r&&!n)return!0;if(r&&!n||!r&&n)return!1;try{return r.join("")===n.join("")}catch(e){return!1}}function ru(e){return e.exception&&e.exception.values&&e.exception.values[0]}function rd(e){return void 0===e?void 0:e>=400&&e<500?"warning":e>=500?"error":void 0}function rh(e){return e&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(e.toString())}function rp(e,t){return!!e&&"object"==typeof e&&!!e[t]}function rf(e){return"string"==typeof e?e:e?rp(e,"url")?e.url:e.toString?e.toString():"":""}let rm=0;function rg(e,t={}){if("function"!=typeof e)return e;try{let t=e.__sentry_wrapped__;if(t)return"function"==typeof t?t:e;if(e4(e))return e}catch(t){return e}let r=function(...r){try{let n=r.map(e=>rg(e,t));return e.apply(this,n)}catch(e){throw rm++,setTimeout(()=>{rm--}),function(...e){let t=tw(eD());if(2===e.length){let[r,n]=e;return r?t.withSetScope(r,n):t.withScope(n)}t.withScope(e[0])}(n=>{var i;n.addEventProcessor(e=>(t.mechanism&&(ti(e,void 0,void 0),ta(e,t.mechanism)),e.extra={...e.extra,arguments:r},e)),tk().captureException(e,function(e){if(e)return e instanceof tg||"function"==typeof e||Object.keys(e).some(e=>tY.includes(e))?{captureContext:e}:e}(i))}),e}};try{for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&(r[t]=e[t])}catch(e){}e3(r,e),e2(e,"__sentry_wrapped__",r);try{Object.getOwnPropertyDescriptor(r,"name").configurable&&Object.defineProperty(r,"name",{get:()=>e.name})}catch(e){}return r}let rv="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__;function ry(e,t){let r=r_(e,t),n={type:function(e){let t=e&&e.name;return!t&&rw(e)?e.message&&Array.isArray(e.message)&&2==e.message.length?e.message[0]:"WebAssembly.Exception":t}(t),value:function(e){let t=e&&e.message;return t?t.error&&"string"==typeof t.error.message?t.error.message:rw(e)&&Array.isArray(e.message)&&2==e.message.length?e.message[1]:t:"No error message"}(t)};return r.length&&(n.stacktrace={frames:r}),void 0===n.type&&""===n.value&&(n.value="Unrecoverable error caught"),n}function rb(e,t){return{exception:{values:[ry(e,t)]}}}function r_(e,t){let r=t.stacktrace||t.stack||"",n=t&&rS.test(t.message)?1:0,i="number"==typeof t.framesToPop?t.framesToPop:0;try{return e(r,n,i)}catch(e){}return[]}let rS=/Minified React error #\d+;/i;function rw(e){return"undefined"!=typeof WebAssembly&&void 0!==WebAssembly.Exception&&e instanceof WebAssembly.Exception}function rk(e,t,r,n,i){let a;if(eU(t)&&t.error)return rb(e,t.error);if(e$(t)||eV(t,"DOMException")){if("stack"in t)a=rb(e,t);else{let i=t.name||(e$(t)?"DOMError":"DOMException"),s=t.message?`${i}: ${t.message}`:i;ti(a=rE(e,s,r,n),s)}return"code"in t&&(a.tags={...a.tags,"DOMException.code":`${t.code}`}),a}return eq(t)?rb(e,t):(eY(t)||ez(t)?ta(a=function(e,t,r,n){let i=tC(),a=i&&i.getOptions().normalizeDepth,s=function(e){for(let t in e)if(Object.prototype.hasOwnProperty.call(e,t)){let r=e[t];if(r instanceof Error)return r}}(t),o={__serialized__:function e(t,r=3,n=102400){let i=tF(t,r);return~-encodeURI(JSON.stringify(i)).split(/%..|./).length>n?e(t,r-1,n):i}(t,a)};if(s)return{exception:{values:[ry(e,s)]},extra:o};let l={exception:{values:[{type:ez(t)?t.constructor.name:n?"UnhandledRejection":"Error",value:function(e,{isUnhandledRejection:t}){let r=function(e,t=40){let r=Object.keys(e5(e));r.sort();let n=r[0];if(!n)return"[object has no keys]";if(n.length>=t)return eX(n,t);for(let e=r.length;e>0;e--){let n=r.slice(0,e).join(", ");if(!(n.length>t))return e===r.length?n:eX(n,t)}return""}(e),n=t?"promise rejection":"exception";return eU(e)?`Event \`ErrorEvent\` captured as ${n} with message \`${e.message}\``:ez(e)?`Event \`${function(e){try{let t=Object.getPrototypeOf(e);return t?t.constructor.name:void 0}catch(e){}}(e)}\` (type=${e.type}) captured as ${n}`:`Object captured as ${n} with keys: ${r}`}(t,{isUnhandledRejection:n})}]},extra:o};if(r){let t=r_(e,r);t.length&&(l.exception.values[0].stacktrace={frames:t})}return l}(e,t,r,i),{synthetic:!0}):(ti(a=rE(e,t,r,n),`${t}`,void 0),ta(a,{synthetic:!0})),a)}function rE(e,t,r,n){let i={};if(n&&r){let n=r_(e,r);n.length&&(i.exception={values:[{value:t,stacktrace:{frames:n}}]}),ta(i,{synthetic:!0})}if(eJ(t)){let{__sentry_template_string__:e,__sentry_template_values__:r}=t;return i.logentry={message:e,params:r},i}return i.message=t,i}class rC extends t3{constructor(e){let t={parentSpanIsAlwaysRootSpan:!0,...e};(function(e,t,r=[t],n="npm"){let i=e._metadata||{};i.sdk||(i.sdk={name:`sentry.javascript.${t}`,packages:r.map(e=>({name:`${n}:@sentry/${e}`,version:ep})),version:ep}),e._metadata=i})(t,"browser",["browser"],ef.SENTRY_SDK_SOURCE||"npm"),super(t),t.sendClientReports&&ef.document&&ef.document.addEventListener("visibilitychange",()=>{"hidden"===ef.document.visibilityState&&this._flushOutcomes()})}eventFromException(e,t){return function(e,t,r,n){let i=rk(e,t,r&&r.syntheticException||void 0,n);return ta(i),i.level="error",r&&r.event_id&&(i.event_id=r.event_id),to(i)}(this._options.stackParser,e,t,this._options.attachStacktrace)}eventFromMessage(e,t="info",r){return function(e,t,r="info",n,i){let a=rE(e,t,n&&n.syntheticException||void 0,i);return a.level=r,n&&n.event_id&&(a.event_id=n.event_id),to(a)}(this._options.stackParser,e,t,r,this._options.attachStacktrace)}captureUserFeedback(e){if(!this._isEnabled())return void(rv&&e_.warn("SDK not enabled, will not capture user feedback."));let t=function(e,{metadata:t,tunnel:r,dsn:n}){return tq({event_id:e.event_id,sent_at:(new Date).toISOString(),...t&&t.sdk&&{sdk:{name:t.sdk.name,version:t.sdk.version}},...!!r&&!!n&&{dsn:tD(n)}},[[{type:"user_report"},e]])}(e,{metadata:this.getSdkMetadata(),dsn:this.getDsn(),tunnel:this.getOptions().tunnel});this.sendEnvelope(t)}_prepareEvent(e,t,r){return e.platform=e.platform||"javascript",super._prepareEvent(e,t,r)}}let rA="undefined"==typeof __SENTRY_DEBUG__||__SENTRY_DEBUG__;function rT(){if(!ef.document)return;let e=ej.bind(null,"dom"),t=rM(e,!0);ef.document.addEventListener("click",t,!1),ef.document.addEventListener("keypress",t,!1),["EventTarget","Node"].forEach(t=>{let r=ef[t],n=r&&r.prototype;n&&n.hasOwnProperty&&n.hasOwnProperty("addEventListener")&&(e1(n,"addEventListener",function(t){return function(r,n,i){if("click"===r||"keypress"==r)try{let n=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},a=n[r]=n[r]||{refCount:0};if(!a.handler){let n=rM(e);a.handler=n,t.call(this,r,n,i)}a.refCount++}catch(e){}return t.call(this,r,n,i)}}),e1(n,"removeEventListener",function(e){return function(t,r,n){if("click"===t||"keypress"==t)try{let r=this.__sentry_instrumentation_handlers__||{},i=r[t];i&&(i.refCount--,i.refCount<=0&&(e.call(this,t,i.handler,n),i.handler=void 0,delete r[t]),0===Object.keys(r).length&&delete this.__sentry_instrumentation_handlers__)}catch(e){}return e.call(this,t,r,n)}}))})}function rM(e,t=!1){return r=>{if(!r||r._sentryCaptured)return;let n=function(e){try{return e.target}catch(e){return null}}(r);if("keypress"===r.type&&(!n||!n.tagName||"INPUT"!==n.tagName&&"TEXTAREA"!==n.tagName&&!n.isContentEditable))return;e2(r,"_sentryCaptured",!0),n&&!n._sentryId&&e2(n,"_sentryId",tt());let i="keypress"===r.type?"input":r.type;!function(e){if(e.type!==l)return!1;try{if(!e.target||e.target._sentryId!==c)return!1}catch(e){}return!0}(r)&&(e({event:r,name:i,global:t}),l=r.type,c=n?n._sentryId:void 0),clearTimeout(o),o=ef.setTimeout(()=>{c=void 0,l=void 0},1e3)}}function rx(e){let t="history";ex(t,e),eO(t,rO)}function rO(){if(!function(){let e=ef.chrome,t=e&&e.app&&e.app.runtime,r="history"in ef&&!!ef.history.pushState&&!!ef.history.replaceState;return!t&&r}())return;let e=ef.onpopstate;function t(e){return function(...t){let r=t.length>2?t[2]:void 0;if(r){let e=u,t=String(r);u=t,ej("history",{from:e,to:t})}return e.apply(this,t)}}ef.onpopstate=function(...t){let r=ef.location.href,n=u;if(u=r,ej("history",{from:n,to:r}),e)try{return e.apply(this,t)}catch(e){}},e1(ef.history,"pushState",t),e1(ef.history,"replaceState",t)}let rj={},rP="__sentry_xhr_v3__";function rI(){if(!ef.XMLHttpRequest)return;let e=XMLHttpRequest.prototype;e.open=new Proxy(e.open,{apply(e,t,r){let n=Error(),i=1e3*te(),a=eB(r[0])?r[0].toUpperCase():void 0,s=function(e){if(eB(e))return e;try{return e.toString()}catch(e){}}(r[1]);if(!a||!s)return e.apply(t,r);t[rP]={method:a,url:s,request_headers:{}},"POST"===a&&s.match(/sentry_key/)&&(t.__sentry_own_request__=!0);let o=()=>{let e=t[rP];if(e&&4===t.readyState){try{e.status_code=t.status}catch(e){}ej("xhr",{endTimestamp:1e3*te(),startTimestamp:i,xhr:t,virtualError:n})}};return"onreadystatechange"in t&&"function"==typeof t.onreadystatechange?t.onreadystatechange=new Proxy(t.onreadystatechange,{apply:(e,t,r)=>(o(),e.apply(t,r))}):t.addEventListener("readystatechange",o),t.setRequestHeader=new Proxy(t.setRequestHeader,{apply(e,t,r){let[n,i]=r,a=t[rP];return a&&eB(n)&&eB(i)&&(a.request_headers[n.toLowerCase()]=i),e.apply(t,r)}}),e.apply(t,r)}}),e.send=new Proxy(e.send,{apply(e,t,r){let n=t[rP];return n&&(void 0!==r[0]&&(n.body=r[0]),ej("xhr",{startTimestamp:1e3*te(),xhr:t})),e.apply(t,r)}})}function rN(e,t=function(e){let t=rj[e];if(t)return t;let r=ef[e];if(rh(r))return rj[e]=r.bind(ef);let n=ef.document;if(n&&"function"==typeof n.createElement)try{let t=n.createElement("iframe");t.hidden=!0,n.head.appendChild(t);let i=t.contentWindow;i&&i[e]&&(r=i[e]),n.head.removeChild(t)}catch(t){rA&&e_.warn(`Could not create sandbox iframe for ${e} check, bailing to window.${e}: `,t)}return r?rj[e]=r.bind(ef):r}("fetch")){let r=0,n=0;return function(e,t,r=function(e){let t=[];function r(e){return t.splice(t.indexOf(e),1)[0]||Promise.resolve(void 0)}return{$:t,add:function(n){if(!(void 0===e||t.length<e))return tl(new t1("Not adding Promise because buffer limit was reached."));let i=n();return -1===t.indexOf(i)&&t.push(i),i.then(()=>r(i)).then(null,()=>r(i).then(null,()=>{})),i},drain:function(e){return new tc((r,n)=>{let i=t.length;if(!i)return r(!0);let a=setTimeout(()=>{e&&e>0&&r(!1)},e);t.forEach(e=>{to(e).then(()=>{--i||(clearTimeout(a),r(!0))},n)})})}}}(e.bufferSize||64)){let n={};return{send:function(i){let a=[];if(tV(i,(t,r)=>{let i=t$[r];if(function(e,t,r=Date.now()){return(e[t]||e.all||0)>r}(n,i)){let n=t8(t,r);e.recordDroppedEvent("ratelimit_backoff",i,n)}else a.push(t)}),0===a.length)return to({});let s=tq(i[0],a),o=t=>{tV(s,(r,n)=>{let i=t8(r,n);e.recordDroppedEvent(t,t$[n],i)})};return r.add(()=>t({body:function(e){let[t,r]=e,n=JSON.stringify(t);function i(e){"string"==typeof n?n="string"==typeof e?n+e:[tU(n),e]:n.push("string"==typeof e?tU(e):e)}for(let e of r){let[t,r]=e;if(i(`
${JSON.stringify(t)}
`),"string"==typeof r||r instanceof Uint8Array)i(r);else{let e;try{e=JSON.stringify(r)}catch(t){e=JSON.stringify(tF(r))}i(e)}}return"string"==typeof n?n:function(e){let t=new Uint8Array(e.reduce((e,t)=>e+t.length,0)),r=0;for(let n of e)t.set(n,r),r+=n.length;return t}(n)}(s)}).then(e=>(void 0!==e.statusCode&&(e.statusCode<200||e.statusCode>=300)&&eh&&e_.warn(`Sentry responded with status code ${e.statusCode} to sent event.`),n=function(e,{statusCode:t,headers:r},n=Date.now()){let i={...e},a=r&&r["x-sentry-rate-limits"],s=r&&r["retry-after"];if(a)for(let e of a.trim().split(",")){let[t,r,,,a]=e.split(":",5),s=parseInt(t,10),o=1e3*(isNaN(s)?60:s);if(r)for(let e of r.split(";"))"metric_bucket"===e&&a&&!a.split(";").includes("custom")||(i[e]=n+o);else i.all=n+o}else s?i.all=n+function(e,t=Date.now()){let r=parseInt(`${e}`,10);if(!isNaN(r))return 1e3*r;let n=Date.parse(`${e}`);return isNaN(n)?6e4:n-t}(s,n):429===t&&(i.all=n+6e4);return i}(n,e),e),e=>{throw o("network_error"),e})).then(e=>e,e=>{if(e instanceof t1)return eh&&e_.error("Skipped sending event because buffer is full."),o("queue_overflow"),to({});throw e})},flush:e=>r.drain(e)}}(e,function(i){let a=i.body.length;r+=a,n++;let s={body:i.body,method:"POST",referrerPolicy:"origin",headers:e.headers,keepalive:r<=6e4&&n<15,...e.fetchOptions};if(!t)return rj.fetch=void 0,tl("No fetch implementation available");try{return t(e.url,s).then(e=>(r-=a,n--,{statusCode:e.status,headers:{"x-sentry-rate-limits":e.headers.get("X-Sentry-Rate-Limits"),"retry-after":e.headers.get("Retry-After")}}))}catch(e){return rj.fetch=void 0,r-=a,n--,tl(e)}})}function rL(e,t,r,n){let i={filename:e,function:"<anonymous>"===t?"?":t,in_app:!0};return void 0!==r&&(i.lineno=r),void 0!==n&&(i.colno=n),i}let rD=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,rR=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,rF=/\((\S*)(?::(\d+))(?::(\d+))\)/,rq=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,rV=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,rU=function(...e){let t=e.sort((e,t)=>e[0]-t[0]).map(e=>e[1]);return(e,r=0,n=0)=>{let i=[],a=e.split("\n");for(let e=r;e<a.length;e++){let r=a[e];if(r.length>1024)continue;let s=eS.test(r)?r.replace(eS,"$1"):r;if(!s.match(/\S*Error: /)){for(let e of t){let t=e(s);if(t){i.push(t);break}}if(i.length>=50+n)break}}return function(e){if(!e.length)return[];let t=Array.from(e);return/sentryWrapped/.test(ek(t).function||"")&&t.pop(),t.reverse(),ew.test(ek(t).function||"")&&(t.pop(),ew.test(ek(t).function||"")&&t.pop()),t.slice(0,50).map(e=>({...e,filename:e.filename||ek(t).filename,function:e.function||"?"}))}(i.slice(n))}}([30,e=>{let t=rD.exec(e);if(t){let[,e,r,n]=t;return rL(e,"?",+r,+n)}let r=rR.exec(e);if(r){if(r[2]&&0===r[2].indexOf("eval")){let e=rF.exec(r[2]);e&&(r[2]=e[1],r[3]=e[2],r[4]=e[3])}let[e,t]=r$(r[1]||"?",r[2]);return rL(t,e,r[3]?+r[3]:void 0,r[4]?+r[4]:void 0)}}],[50,e=>{let t=rq.exec(e);if(t){if(t[3]&&t[3].indexOf(" > eval")>-1){let e=rV.exec(t[3]);e&&(t[1]=t[1]||"eval",t[3]=e[1],t[4]=e[2],t[5]="")}let e=t[3],r=t[1]||"?";return[r,e]=r$(r,e),rL(e,r,t[4]?+t[4]:void 0,t[5]?+t[5]:void 0)}}]),r$=(e,t)=>{let r=-1!==e.indexOf("safari-extension"),n=-1!==e.indexOf("safari-web-extension");return r||n?[-1!==e.indexOf("@")?e.split("@")[0]:"?",r?`safari-extension:${t}`:`safari-web-extension:${t}`]:[e,t]},rB=(e={})=>{let t={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...e};return{name:"Breadcrumbs",setup(e){var r;t.console&&function(e){let t="console";ex(t,e),eO(t,rs)}(function(t){var r;if(tC()!==e)return;let n={category:"console",data:{arguments:t.args,logger:"console"},level:"warn"===(r=t.level)?"warning":["fatal","error","warning","log","info","debug"].includes(r)?r:"log",message:eZ(t.args," ")};if("assert"===t.level){if(!1!==t.args[0])return;n.message=`Assertion failed: ${eZ(t.args.slice(1)," ")||"console.assert"}`,n.data.arguments=t.args.slice(1)}t6(n,{input:t.args,level:t.level})}),t.dom&&(r=t.dom,ex("dom",function(t){if(tC()!==e)return;let n,i,a="object"==typeof r?r.serializeAttribute:void 0,s="object"==typeof r&&"number"==typeof r.maxStringLength?r.maxStringLength:void 0;s&&s>1024&&(rv&&e_.warn(`\`dom.maxStringLength\` cannot exceed 1024, but a value of ${s} was configured. Sentry will use 1024 instead.`),s=1024),"string"==typeof a&&(a=[a]);try{let e=t.event,r=e&&e.target?e.target:e;n=eK(r,{keyAttrs:a,maxStringLength:s}),i=function(e){if(!ef.HTMLElement)return null;let t=e;for(let e=0;e<5&&t;e++){if(t instanceof HTMLElement){if(t.dataset.sentryComponent)return t.dataset.sentryComponent;if(t.dataset.sentryElement)return t.dataset.sentryElement}t=t.parentNode}return null}(r)}catch(e){n="<unknown>"}if(0===n.length)return;let o={category:`ui.${t.name}`,message:n};i&&(o.data={"ui.component_name":i}),t6(o,{event:t.event,name:t.name,global:t.global})}),eO("dom",rT)),t.xhr&&(ex("xhr",function(t){if(tC()!==e)return;let{startTimestamp:r,endTimestamp:n}=t,i=t.xhr[rP];if(!r||!n||!i)return;let{method:a,url:s,status_code:o,body:l}=i,c={xhr:t.xhr,input:l,startTimestamp:r,endTimestamp:n};t6({category:"xhr",data:{method:a,url:s,status_code:o},type:"http",level:rd(o)},c)}),eO("xhr",rI)),t.fetch&&function(e,t){let r="fetch";ex(r,e),eO(r,()=>(function(e,t=!1){(!t||function(){if("string"==typeof EdgeRuntime)return!0;if(!function(){if(!("fetch"in ef))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch(e){return!1}}())return!1;if(rh(ef.fetch))return!0;let e=!1,t=ef.document;if(t&&"function"==typeof t.createElement)try{let r=t.createElement("iframe");r.hidden=!0,t.head.appendChild(r),r.contentWindow&&r.contentWindow.fetch&&(e=rh(r.contentWindow.fetch)),t.head.removeChild(r)}catch(e){eg&&e_.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",e)}return e}())&&e1(ef,"fetch",function(t){return function(...r){let n=Error(),{method:i,url:a}=function(e){if(0===e.length)return{method:"GET",url:""};if(2===e.length){let[t,r]=e;return{url:rf(t),method:rp(r,"method")?String(r.method).toUpperCase():"GET"}}let t=e[0];return{url:rf(t),method:rp(t,"method")?String(t.method).toUpperCase():"GET"}}(r),s={args:r,fetchData:{method:i,url:a},startTimestamp:1e3*te(),virtualError:n};return e||ej("fetch",{...s}),t.apply(ef,r).then(async t=>(e?e(t):ej("fetch",{...s,endTimestamp:1e3*te(),response:t}),t),e=>{throw ej("fetch",{...s,endTimestamp:1e3*te(),error:e}),eq(e)&&void 0===e.stack&&(e.stack=n.stack,e2(e,"framesToPop",1)),e})}})})(void 0,void 0))}(function(t){if(tC()!==e)return;let{startTimestamp:r,endTimestamp:n}=t;if(n&&(!t.fetchData.url.match(/sentry_key/)||"POST"!==t.fetchData.method)){if(t.error)t6({category:"fetch",data:t.fetchData,level:"error",type:"http"},{data:t.error,input:t.args,startTimestamp:r,endTimestamp:n});else{let e=t.response,i={...t.fetchData,status_code:e&&e.status},a={input:t.args,response:e,startTimestamp:r,endTimestamp:n};t6({category:"fetch",data:i,type:"http",level:rd(i.status_code)},a)}}}),t.history&&rx(function(t){if(tC()!==e)return;let r=t.from,n=t.to,i=ra(ef.location.href),a=r?ra(r):void 0,s=ra(n);a&&a.path||(a=i),i.protocol===s.protocol&&i.host===s.host&&(n=s.relative),i.protocol===a.protocol&&i.host===a.host&&(r=a.relative),t6({category:"navigation",data:{from:r,to:n}})}),t.sentry&&e.on("beforeSendEvent",function(t){tC()===e&&t6({category:"sentry."+("transaction"===t.type?"transaction":"event"),event_id:t.event_id,level:t.level,message:tn(t)},{event:t})})}}},rJ=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],rG=(e={})=>{let t={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...e};return{name:"BrowserApiErrors",setupOnce(){t.setTimeout&&e1(ef,"setTimeout",rY),t.setInterval&&e1(ef,"setInterval",rY),t.requestAnimationFrame&&e1(ef,"requestAnimationFrame",rz),t.XMLHttpRequest&&"XMLHttpRequest"in ef&&e1(XMLHttpRequest.prototype,"send",rW);let e=t.eventTarget;e&&(Array.isArray(e)?e:rJ).forEach(rQ)}}};function rY(e){return function(...t){let r=t[0];return t[0]=rg(r,{mechanism:{data:{function:eC(e)},handled:!1,type:"instrument"}}),e.apply(this,t)}}function rz(e){return function(t){return e.apply(this,[rg(t,{mechanism:{data:{function:"requestAnimationFrame",handler:eC(e)},handled:!1,type:"instrument"}})])}}function rW(e){return function(...t){let r=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(e=>{e in r&&"function"==typeof r[e]&&e1(r,e,function(t){let r={mechanism:{data:{function:e,handler:eC(t)},handled:!1,type:"instrument"}},n=e4(t);return n&&(r.mechanism.data.handler=eC(n)),rg(t,r)})}),e.apply(this,t)}}function rQ(e){let t=ef[e],r=t&&t.prototype;r&&r.hasOwnProperty&&r.hasOwnProperty("addEventListener")&&(e1(r,"addEventListener",function(t){return function(r,n,i){try{"function"==typeof n.handleEvent&&(n.handleEvent=rg(n.handleEvent,{mechanism:{data:{function:"handleEvent",handler:eC(n),target:e},handled:!1,type:"instrument"}}))}catch(e){}return t.apply(this,[r,rg(n,{mechanism:{data:{function:"addEventListener",handler:eC(n),target:e},handled:!1,type:"instrument"}}),i])}}),e1(r,"removeEventListener",function(e){return function(t,r,n){try{let i=r.__sentry_wrapped__;i&&e.call(this,t,i,n)}catch(e){}return e.call(this,t,r,n)}}))}let rH=()=>({name:"BrowserSession",setupOnce(){void 0!==ef.document?(tW({ignoreDuration:!0}),tK(),rx(({from:e,to:t})=>{void 0!==e&&e!==t&&(tW({ignoreDuration:!0}),tK())})):rv&&e_.warn("Using the `browserSessionIntegration` in non-browser environments is not supported.")}}),rK=(e={})=>{let t={onerror:!0,onunhandledrejection:!0,...e};return{name:"GlobalHandlers",setupOnce(){Error.stackTraceLimit=50},setup(e){t.onerror&&(function(e){let t="error";ex(t,e),eO(t,eI)}(t=>{let{stackParser:r,attachStacktrace:n}=rZ();if(tC()!==e||rm>0)return;let{msg:i,url:a,line:s,column:o,error:l}=t,c=function(e,t,r,n){let i=e.exception=e.exception||{},a=i.values=i.values||[],s=a[0]=a[0]||{},o=s.stacktrace=s.stacktrace||{},l=o.frames=o.frames||[],c=eB(t)&&t.length>0?t:function(){try{return ef.document.location.href}catch(e){return""}}();return 0===l.length&&l.push({colno:n,filename:c,function:"?",in_app:!0,lineno:r}),e}(rk(r,l||i,void 0,n,!1),a,s,o);c.level="error",tz(c,{originalException:l,mechanism:{handled:!1,type:"onerror"}})}),rX("onerror")),t.onunhandledrejection&&(function(e){let t="unhandledrejection";ex(t,e),eO(t,eL)}(t=>{let{stackParser:r,attachStacktrace:n}=rZ();if(tC()!==e||rm>0)return;let i=function(e){if(eG(e))return e;try{if("reason"in e)return e.reason;if("detail"in e&&"reason"in e.detail)return e.detail.reason}catch(e){}return e}(t),a=eG(i)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(i)}`}]}}:rk(r,i,void 0,n,!0);a.level="error",tz(a,{originalException:i,mechanism:{handled:!1,type:"onunhandledrejection"}})}),rX("onunhandledrejection"))}}};function rX(e){rv&&e_.log(`Global Handler attached: ${e}`)}function rZ(){let e=tC();return e&&e.getOptions()||{stackParser:()=>[],attachStacktrace:!1}}let r0=()=>({name:"HttpContext",preprocessEvent(e){if(!ef.navigator&&!ef.location&&!ef.document)return;let t=e.request&&e.request.url||ef.location&&ef.location.href,{referrer:r}=ef.document||{},{userAgent:n}=ef.navigator||{},i={...e.request&&e.request.headers,...r&&{Referer:r},...n&&{"User-Agent":n}},a={...e.request,...t&&{url:t},headers:i};e.request=a}}),r1=(e={})=>{let t=e.limit||5,r=e.key||"cause";return{name:"LinkedErrors",preprocessEvent(e,n,i){let a=i.getOptions();!function(e,t,r=250,n,i,a,s){if(!(a.exception&&a.exception.values&&s&&eQ(s.originalException,Error)))return;let o=a.exception.values.length>0?a.exception.values[a.exception.values.length-1]:void 0;o&&(a.exception.values=(function e(t,r,n,i,a,s,o,l){if(s.length>=n+1)return s;let c=[...s];if(eQ(i[a],Error)){rn(o,l);let s=t(r,i[a]),u=c.length;ri(s,a,u,l),c=e(t,r,n,i[a],a,[s,...c],s,u)}return Array.isArray(i.errors)&&i.errors.forEach((i,s)=>{if(eQ(i,Error)){rn(o,l);let u=t(r,i),d=c.length;ri(u,`errors[${s}]`,d,l),c=e(t,r,n,i,a,[u,...c],u,d)}}),c})(e,t,i,s.originalException,n,a.exception.values,o,0).map(e=>(e.value&&(e.value=eX(e.value,r)),e)))}(ry,a.stackParser,a.maxValueLength,r,t,e,n)}}};var r2="new",r3="loading",r4="loaded",r5="joining-meeting",r8="joined-meeting",r6="left-meeting",r9="error",r7="blocked",ne="off",nt="sendable",nr="loading",nn="interrupted",ni="playable",na="unknown",ns="full",no="lobby",nl="none",nc="base",nu="*",nd="ejected",nh="nbf-room",np="nbf-token",nf="exp-room",nm="exp-token",ng="no-room",nv="meeting-full",ny="end-of-life",nb="not-allowed",n_="connection-error",nS="cam-in-use",nw="mic-in-use",nk="cam-mic-in-use",nE="permissions",nC="undefined-mediadevices",nA="not-found",nT="constraints",nM="unknown",nx="iframe-ready-for-launch-config",nO="iframe-launch-config",nj="theme-updated",nP="loading",nI="load-attempt-failed",nN="loaded",nL="started-camera",nD="camera-error",nR="joining-meeting",nF="joined-meeting",nq="left-meeting",nV="participant-joined",nU="participant-updated",n$="participant-left",nB="participant-counts-updated",nJ="access-state-updated",nG="meeting-session-summary-updated",nY="meeting-session-state-updated",nz="meeting-session-data-error",nW="waiting-participant-added",nQ="waiting-participant-updated",nH="waiting-participant-removed",nK="track-started",nX="track-stopped",nZ="transcription-started",n0="transcription-stopped",n1="transcription-error",n2="recording-started",n3="recording-stopped",n4="recording-stats",n5="recording-error",n8="recording-upload-completed",n6="recording-data",n9="app-message",n7="transcription-message",ie="remote-media-player-started",it="remote-media-player-updated",ir="remote-media-player-stopped",ii="local-screen-share-started",ia="local-screen-share-stopped",is="local-screen-share-canceled",io="active-speaker-change",il="active-speaker-mode-change",ic="network-quality-change",iu="network-connection",id="cpu-load-change",ih="face-counts-updated",ip="fullscreen",im="exited-fullscreen",ig="live-streaming-started",iv="live-streaming-updated",iy="live-streaming-stopped",ib="live-streaming-error",i_="lang-updated",iS="receive-settings-updated",iw="input-settings-updated",ik="nonfatal-error",iE="error",iC="iframe-call-message",iA="local-screen-start",iT="daily-method-update-live-streaming-endpoints",iM="transmit-log",ix="daily-custom-track",iO={NONE:"none",BGBLUR:"background-blur",BGIMAGE:"background-image",FACE_DETECTION:"face-detection"},ij={NONE:"none",NOISE_CANCELLATION:"noise-cancellation"},iP={PLAY:"play",PAUSE:"pause"},iI=["jpg","png","jpeg"],iN="sip-call-transfer";function iL(){return!iD()&&"undefined"!=typeof window&&window.navigator&&window.navigator.userAgent?window.navigator.userAgent:""}function iD(){return"undefined"!=typeof navigator&&navigator.product&&"ReactNative"===navigator.product}function iR(){return navigator&&navigator.mediaDevices&&navigator.mediaDevices.getUserMedia}function iF(){if(iD()||!document)return!1;var e=document.createElement("iframe");return!!e.requestFullscreen||!!e.webkitRequestFullscreen}var iq="none",iV=function(){try{var e,t=document.createElement("canvas"),r=!1;(e=t.getContext("webgl2",{failIfMajorPerformanceCaveat:!0}))||(r=!0,e=t.getContext("webgl2"));var n=null!=e;return t.remove(),n?r?"software":"hardware":iq}catch(e){return iq}}();function iU(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return!iD()&&iV!==iq&&(e?!iG()&&["Chrome","Firefox"].includes(iY()):function(){if(iG())return!1;var e=iY();if("Safari"===e){var t=iK();if(t.major<15||15===t.major&&t.minor<4)return!1}return"Chrome"===e?iW().major>=77:"Firefox"===e?iX().major>=97:["Chrome","Firefox","Safari"].includes(e)}())}function i$(){if(iD()||iJ()||"undefined"==typeof AudioWorkletNode)return!1;switch(iY()){case"Chrome":case"Firefox":return!0;case"Safari":var e=iz();return e.major>17||17===e.major&&e.minor>=4}return!1}function iB(){return iR()&&!function(){var e,t=iY();if(!iL())return!0;switch(t){case"Chrome":return(e=iW()).major&&e.major>0&&e.major<75;case"Firefox":return(e=iX()).major<91;case"Safari":return(e=iK()).major<13||13===e.major&&e.minor<1;default:return!0}}()}function iJ(){return iL().match(/Linux; Android/)}function iG(){var e,t=iL(),r=t.match(/Mac/)&&(!iD()&&"undefined"!=typeof window&&null!==(e=window)&&void 0!==e&&null!==(e=e.navigator)&&void 0!==e&&e.maxTouchPoints?window.navigator.maxTouchPoints:0)>=5;return!!(t.match(/Mobi/)||t.match(/Android/)||r)||!!iL().match(/DailyAnd\//)||void 0}function iY(){if("undefined"!=typeof window){var e=iL();return iQ()?"Safari":e.indexOf("Edge")>-1?"Edge":e.match(/Chrome\//)?"Chrome":e.indexOf("Safari")>-1||iH()?"Safari":e.indexOf("Firefox")>-1?"Firefox":e.indexOf("MSIE")>-1||e.indexOf(".NET")>-1?"IE":"Unknown Browser"}}function iz(){switch(iY()){case"Chrome":return iW();case"Safari":return iK();case"Firefox":return iX();case"Edge":return function(){var e=0,t=0;if("undefined"!=typeof window){var r=iL().match(/Edge\/(\d+).(\d+)/);if(r)try{e=parseInt(r[1]),t=parseInt(r[2])}catch(e){}}return{major:e,minor:t}}()}}function iW(){var e=0,t=0,r=0,n=0,i=!1;if("undefined"!=typeof window){var a=iL(),s=a.match(/Chrome\/(\d+).(\d+).(\d+).(\d+)/);if(s)try{e=parseInt(s[1]),t=parseInt(s[2]),r=parseInt(s[3]),n=parseInt(s[4]),i=a.indexOf("OPR/")>-1}catch(e){}}return{major:e,minor:t,build:r,patch:n,opera:i}}function iQ(){return!!iL().match(/iPad|iPhone|iPod/i)&&iR()}function iH(){return iL().indexOf("AppleWebKit/605.1.15")>-1}function iK(){var e=0,t=0,r=0;if("undefined"!=typeof window){var n=iL().match(/Version\/(\d+).(\d+)(.(\d+))?/);if(n)try{e=parseInt(n[1]),t=parseInt(n[2]),r=parseInt(n[4])}catch(e){}else(iQ()||iH())&&(e=14,t=0,r=3)}return{major:e,minor:t,point:r}}function iX(){var e=0,t=0;if("undefined"!=typeof window){var r=iL().match(/Firefox\/(\d+).(\d+)/);if(r)try{e=parseInt(r[1]),t=parseInt(r[2])}catch(e){}}return{major:e,minor:t}}var iZ=g(function e(){h(this,e)},[{key:"addListenerForMessagesFromCallMachine",value:function(e,t,r){el()}},{key:"addListenerForMessagesFromDailyJs",value:function(e,t,r){el()}},{key:"sendMessageToCallMachine",value:function(e,t,r,n){el()}},{key:"sendMessageToDailyJs",value:function(e,t){el()}},{key:"removeListener",value:function(e){el()}}]);function i0(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i1(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i0(Object(r),!0).forEach(function(t){S(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i0(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function i2(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(i2=function(){return!!e})()}var i3=function(){function e(){var t,r,n;return h(this,e),r=y(r=e),(t=v(this,i2()?Reflect.construct(r,n||[],y(this).constructor):r.apply(this,n)))._wrappedListeners={},t._messageCallbacks={},t}return _(e,iZ),g(e,[{key:"addListenerForMessagesFromCallMachine",value:function(e,t,r){var n=this,i=function(i){if(i.data&&"iframe-call-message"===i.data.what&&(!i.data.callClientId||i.data.callClientId===t)&&(!i.data.from||"module"!==i.data.from)){var a=i1({},i.data);if(delete a.from,a.callbackStamp&&n._messageCallbacks[a.callbackStamp]){var s=a.callbackStamp;n._messageCallbacks[s].call(r,a),delete n._messageCallbacks[s]}delete a.what,delete a.callbackStamp,e.call(r,a)}};this._wrappedListeners[e]=i,window.addEventListener("message",i)}},{key:"addListenerForMessagesFromDailyJs",value:function(e,t,r){var n=function(n){var i;if(!(!n.data||n.data.what!==iC||!n.data.action||n.data.from&&"module"!==n.data.from||n.data.callClientId&&t&&n.data.callClientId!==t||null!=n&&null!==(i=n.data)&&void 0!==i&&i.callFrameId)){var a=n.data;e.call(r,a)}};this._wrappedListeners[e]=n,window.addEventListener("message",n)}},{key:"sendMessageToCallMachine",value:function(e,t,r,n){if(!r)throw Error("undefined callClientId. Are you trying to use a DailyCall instance previously destroyed?");var i=i1({},e);if(i.what=iC,i.from="module",i.callClientId=r,t){var a=eo();this._messageCallbacks[a]=t,i.callbackStamp=a}var s=n?n.contentWindow:window,o=this._callMachineTargetOrigin(n);o&&s.postMessage(i,o)}},{key:"sendMessageToDailyJs",value:function(e,t){e.what=iC,e.callClientId=t,e.from="embedded",window.postMessage(e,this._targetOriginFromWindowLocation())}},{key:"removeListener",value:function(e){var t=this._wrappedListeners[e];t&&(window.removeEventListener("message",t),delete this._wrappedListeners[e])}},{key:"forwardPackagedMessageToCallMachine",value:function(e,t,r){var n=i1({},e);n.callClientId=r;var i=t?t.contentWindow:window,a=this._callMachineTargetOrigin(t);a&&i.postMessage(n,a)}},{key:"addListenerForPackagedMessagesFromCallMachine",value:function(e,t){var r=function(r){!r.data||"iframe-call-message"!==r.data.what||r.data.callClientId&&r.data.callClientId!==t||r.data.from&&"module"===r.data.from||e(r.data)};return this._wrappedListeners[e]=r,window.addEventListener("message",r),e}},{key:"removeListenerForPackagedMessagesFromCallMachine",value:function(e){var t=this._wrappedListeners[e];t&&(window.removeEventListener("message",t),delete this._wrappedListeners[e])}},{key:"_callMachineTargetOrigin",value:function(e){return e?e.src?new URL(e.src).origin:void 0:this._targetOriginFromWindowLocation()}},{key:"_targetOriginFromWindowLocation",value:function(){return"file:"===window.location.protocol?"*":window.location.origin}}])}();function i4(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function i5(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(i5=function(){return!!e})()}var i8=function(){function e(){var t,r,n;return h(this,e),r=y(r=e),t=v(this,i5()?Reflect.construct(r,n||[],y(this).constructor):r.apply(this,n)),global.callMachineToDailyJsEmitter=global.callMachineToDailyJsEmitter||new B.EventEmitter,global.dailyJsToCallMachineEmitter=global.dailyJsToCallMachineEmitter||new B.EventEmitter,t._wrappedListeners={},t._messageCallbacks={},t}return _(e,iZ),g(e,[{key:"addListenerForMessagesFromCallMachine",value:function(e,t,r){this._addListener(e,global.callMachineToDailyJsEmitter,t,r,"received call machine message")}},{key:"addListenerForMessagesFromDailyJs",value:function(e,t,r){this._addListener(e,global.dailyJsToCallMachineEmitter,t,r,"received daily-js message")}},{key:"sendMessageToCallMachine",value:function(e,t,r){this._sendMessage(e,global.dailyJsToCallMachineEmitter,r,t,"sending message to call machine")}},{key:"sendMessageToDailyJs",value:function(e,t){this._sendMessage(e,global.callMachineToDailyJsEmitter,t,null,"sending message to daily-js")}},{key:"removeListener",value:function(e){var t=this._wrappedListeners[e];t&&(global.callMachineToDailyJsEmitter.removeListener("message",t),global.dailyJsToCallMachineEmitter.removeListener("message",t),delete this._wrappedListeners[e])}},{key:"_addListener",value:function(e,t,r,n,i){var a=this,s=function(t){if(t.callClientId===r){if(t.callbackStamp&&a._messageCallbacks[t.callbackStamp]){var i=t.callbackStamp;a._messageCallbacks[i].call(n,t),delete a._messageCallbacks[i]}e.call(n,t)}};this._wrappedListeners[e]=s,t.addListener("message",s)}},{key:"_sendMessage",value:function(e,t,r,n,i){var a=function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?i4(Object(r),!0).forEach(function(t){S(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):i4(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}({},e);if(a.callClientId=r,n){var s=eo();this._messageCallbacks[s]=n,a.callbackStamp=s}t.emit("message",a)}}])}(),i6="replace",i9="shallow-merge",i7=[i6,i9],ae=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.data,n=t.mergeStrategy,i=void 0===n?i6:n;h(this,e),e._validateMergeStrategy(i),e._validateData(r,i),this.mergeStrategy=i,this.data=r}return g(e,[{key:"isNoOp",value:function(){return e.isNoOpUpdate(this.data,this.mergeStrategy)}}],[{key:"isNoOpUpdate",value:function(e,t){return 0===Object.keys(e).length&&t===i9}},{key:"_validateMergeStrategy",value:function(e){if(!i7.includes(e))throw Error("Unrecognized mergeStrategy provided. Options are: [".concat(i7,"]"))}},{key:"_validateData",value:function(e,t){if(!function(e){if(null==e||"object"!==p(e))return!1;var t=Object.getPrototypeOf(e);return null==t||t===Object.prototype}(e))throw Error("Meeting session data must be a plain (map-like) object");try{if(r=JSON.stringify(e),t===i6){var r,n=JSON.parse(r);z(n,e)||console.warn("The meeting session data provided will be modified when serialized.",n,e)}else if(t===i9){for(var i in e)if(Object.hasOwnProperty.call(e,i)&&void 0!==e[i]){var a=JSON.parse(JSON.stringify(e[i]));z(e[i],a)||console.warn("At least one key in the meeting session data provided will be modified when serialized.",a,e[i])}}}catch(e){throw Error("Meeting session data must be serializable to JSON: ".concat(e))}if(r.length>102400)throw Error("Meeting session data is too large (".concat(r.length," characters). Maximum size suppported is ").concat(102400,"."))}}])}();function at(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(at=function(){return!!e})()}function ar(e){var t="function"==typeof Map?new Map:void 0;return(ar=function(e){if(null===e||!function(e){try{return -1!==Function.toString.call(e).indexOf("[native code]")}catch(t){return"function"==typeof e}}(e))return e;if("function"!=typeof e)throw TypeError("Super expression must either be null or a function");if(void 0!==t){if(t.has(e))return t.get(e);t.set(e,r)}function r(){return function(e,t,r){if(at())return Reflect.construct.apply(null,arguments);var n=[null];n.push.apply(n,t);var i=new(e.bind.apply(e,n));return r&&b(i,r.prototype),i}(e,arguments,y(this).constructor)}return r.prototype=Object.create(e.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),b(r,e)})(e)}function an(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(an=function(){return!!e})()}function ai(e){var t,r=null===(t=window._daily)||void 0===t?void 0:t.pendings;if(r){var n=r.indexOf(e);-1!==n&&r.splice(n,1)}}var aa=g(function e(t){h(this,e),this._currentLoad=null,this._callClientId=t},[{key:"load",value:function(){var e,t=this,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0,i=arguments.length>2?arguments[2]:void 0;if(this.loaded)return window._daily.instances[this._callClientId].callMachine.reset(),void n(!0);e=this._callClientId,window._daily.pendings.push(e),this._currentLoad&&this._currentLoad.cancel(),this._currentLoad=new as(r,function(){n(!1)},function(e,r){r||ai(t._callClientId),i(e,r)}),this._currentLoad.start()}},{key:"cancel",value:function(){this._currentLoad&&this._currentLoad.cancel(),ai(this._callClientId)}},{key:"loaded",get:function(){return this._currentLoad&&this._currentLoad.succeeded}}]),as=g(function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length>1?arguments[1]:void 0,n=arguments.length>2?arguments[2]:void 0;h(this,e),this._attemptsRemaining=3,this._currentAttempt=null,this._dailyConfig=t,this._successCallback=r,this._failureCallback=n},[{key:"start",value:function(){var e=this;if(!this._currentAttempt){var t=function(r){e._currentAttempt.cancelled||(e._attemptsRemaining--,e._failureCallback(r,e._attemptsRemaining>0),e._attemptsRemaining<=0||setTimeout(function(){e._currentAttempt.cancelled||(e._currentAttempt=new al(e._dailyConfig,e._successCallback,t),e._currentAttempt.start())},3e3))};this._currentAttempt=new al(this._dailyConfig,this._successCallback,t),this._currentAttempt.start()}}},{key:"cancel",value:function(){this._currentAttempt&&this._currentAttempt.cancel()}},{key:"cancelled",get:function(){return this._currentAttempt&&this._currentAttempt.cancelled}},{key:"succeeded",get:function(){return this._currentAttempt&&this._currentAttempt.succeeded}}]),ao=function(){function e(){var t,r;return h(this,e),r=arguments,t=y(t=e),v(this,an()?Reflect.construct(t,r||[],y(this).constructor):t.apply(this,r))}return _(e,ar(Error)),g(e)}(),al=function(){var e;return g(function e(t,r,n){h(this,e),this._loadAttemptImpl=iD()||!t.avoidEval?new ac(t,r,n):new au(t,r,n)},[{key:"start",value:(e=k(function*(){return this._loadAttemptImpl.start()}),function(){return e.apply(this,arguments)})},{key:"cancel",value:function(){this._loadAttemptImpl.cancel()}},{key:"cancelled",get:function(){return this._loadAttemptImpl.cancelled}},{key:"succeeded",get:function(){return this._loadAttemptImpl.succeeded}}])}(),ac=function(){var e,t,r,n;return g(function e(t,r,n){h(this,e),this.cancelled=!1,this.succeeded=!1,this._networkTimedOut=!1,this._networkTimeout=null,this._iosCache="undefined"!=typeof iOSCallObjectBundleCache&&iOSCallObjectBundleCache,this._refetchHeaders=null,this._dailyConfig=t,this._successCallback=r,this._failureCallback=n},[{key:"start",value:(n=k(function*(){var e=eu(this._dailyConfig);(yield this._tryLoadFromIOSCache(e))||this._loadFromNetwork(e)}),function(){return n.apply(this,arguments)})},{key:"cancel",value:function(){clearTimeout(this._networkTimeout),this.cancelled=!0}},{key:"_tryLoadFromIOSCache",value:(r=k(function*(e){if(!this._iosCache)return!1;try{var t=yield this._iosCache.get(e);return!!this.cancelled||!!t&&(t.code?(Function('"use strict";'+t.code)(),this.succeeded=!0,this._successCallback(),!0):(this._refetchHeaders=t.refetchHeaders,!1))}catch(e){return!1}}),function(e){return r.apply(this,arguments)})},{key:"_loadFromNetwork",value:(t=k(function*(e){var t=this;this._networkTimeout=setTimeout(function(){t._networkTimedOut=!0,t._failureCallback({msg:"Timed out (>".concat(2e4," ms) when loading call object bundle ").concat(e),type:"timeout"})},2e4);try{var r=this._refetchHeaders?{headers:this._refetchHeaders}:{},n=yield fetch(e,r);if(clearTimeout(this._networkTimeout),this.cancelled||this._networkTimedOut)throw new ao;var i=yield this._getBundleCodeFromResponse(e,n);if(this.cancelled)throw new ao;Function('"use strict";'+i)(),this._iosCache&&this._iosCache.set(e,i,n.headers),this.succeeded=!0,this._successCallback()}catch(t){if(clearTimeout(this._networkTimeout),t instanceof ao||this.cancelled||this._networkTimedOut)return;this._failureCallback({msg:"Failed to load call object bundle ".concat(e,": ").concat(t),type:t.message})}}),function(e){return t.apply(this,arguments)})},{key:"_getBundleCodeFromResponse",value:(e=k(function*(e,t){if(t.ok)return yield t.text();if(this._iosCache&&304===t.status)return(yield this._iosCache.renew(e,t.headers)).code;throw Error("Received ".concat(t.status," response"))}),function(t,r){return e.apply(this,arguments)})}])}(),au=g(function e(t,r,n){h(this,e),this.cancelled=!1,this.succeeded=!1,this._dailyConfig=t,this._successCallback=r,this._failureCallback=n,this._attemptId=eo(),this._networkTimeout=null,this._scriptElement=null},[{key:"start",value:function(){window._dailyCallMachineLoadWaitlist||(window._dailyCallMachineLoadWaitlist=new Set);var e=eu(this._dailyConfig);"object"===("undefined"==typeof document?"undefined":p(document))?this._startLoading(e):this._failureCallback({msg:"Call object bundle must be loaded in a DOM/web context",type:"missing context"})}},{key:"cancel",value:function(){this._stopLoading(),this.cancelled=!0}},{key:"_startLoading",value:function(e){var t=this;this._signUpForCallMachineLoadWaitlist(),this._networkTimeout=setTimeout(function(){t._stopLoading(),t._failureCallback({msg:"Timed out (>".concat(2e4," ms) when loading call object bundle ").concat(e),type:"timeout"})},2e4);var r=document.getElementsByTagName("head")[0],n=document.createElement("script");this._scriptElement=n,n.onload=function(){t._stopLoading(),t.succeeded=!0,t._successCallback()},n.onerror=function(e){t._stopLoading(),t._failureCallback({msg:"Failed to load call object bundle ".concat(e.target.src),type:e.message})},n.src=e,r.appendChild(n)}},{key:"_stopLoading",value:function(){this._withdrawFromCallMachineLoadWaitlist(),clearTimeout(this._networkTimeout),this._scriptElement&&(this._scriptElement.onload=null,this._scriptElement.onerror=null)}},{key:"_signUpForCallMachineLoadWaitlist",value:function(){window._dailyCallMachineLoadWaitlist.add(this._attemptId)}},{key:"_withdrawFromCallMachineLoadWaitlist",value:function(){window._dailyCallMachineLoadWaitlist.delete(this._attemptId)}}]),ad=function(e,t,r){return!0===ap(e.local,t,r)},ah=function(e,t,r,n){var i=af(e,t,r,n);return i&&i.pendingTrack},ap=function(e,t,r){if(!e)return!1;var n=function(e){switch(e){case"avatar":return!0;case"staged":return e;default:return!!e}},i=e.public.subscribedTracks;return i&&i[t]?n(-1===["cam-audio","cam-video","screen-video","screen-audio","rmpAudio","rmpVideo"].indexOf(r)&&i[t].custom?[!0,"staged"].includes(i[t].custom)?i[t].custom:i[t].custom[r]:i[t][r]):!i||n(i.ALL)},af=function(e,t,r,n){var i=Object.values(e.streams||{}).filter(function(e){return e.participantId===t&&e.type===r&&e.pendingTrack&&e.pendingTrack.kind===n}).sort(function(e,t){return new Date(t.starttime)-new Date(e.starttime)});return i&&i[0]},am=function(e,t){var r=e.local.public.customTracks;if(r&&r[t])return r[t].track};function ag(e,t){for(var r=t.getState(),n=0,i=["cam","screen"];n<i.length;n++)for(var a=i[n],s=0,o=["video","audio"];s<o.length;s++){var l=o[s],c="cam"===a?l:"screen".concat(l.charAt(0).toUpperCase()+l.slice(1)),u=e.tracks[c];if(u){var d=e.local?r.local.streams&&r.local.streams[a]&&r.local.streams[a].stream&&r.local.streams[a].stream["get".concat("video"===l?"Video":"Audio","Tracks")]()[0]:ah(r,e.session_id,a,l);"playable"===u.state&&(u.track=d),u.persistentTrack=d}}}function av(e,t){try{var r,n=t.getState();for(var i in e.tracks){if(r=i,!["video","audio","screenVideo","screenAudio"].includes(r)){var a=e.tracks[i].kind;if(a){var s=e.tracks[i];if(s){var o=e.local?am(n,i):ah(n,e.session_id,i,a);"playable"===s.state&&(e.tracks[i].track=o),s.persistentTrack=o}}else console.error("unknown type for custom track")}}}catch(e){console.error(e)}}function ay(e,t,r){var n=r.getState();if(e.local){if(e.audio)try{e.audioTrack=n.local.streams.cam.stream.getAudioTracks()[0],e.audioTrack||(e.audio=!1)}catch(e){}if(e.video)try{e.videoTrack=n.local.streams.cam.stream.getVideoTracks()[0],e.videoTrack||(e.video=!1)}catch(e){}if(e.screen)try{e.screenVideoTrack=n.local.streams.screen.stream.getVideoTracks()[0],e.screenAudioTrack=n.local.streams.screen.stream.getAudioTracks()[0],e.screenVideoTrack||e.screenAudioTrack||(e.screen=!1)}catch(e){}}else{var i=!0;try{var a=n.participants[e.session_id];a&&a.public&&a.public.rtcType&&"peer-to-peer"===a.public.rtcType.impl&&a.private&&!["connected","completed"].includes(a.private.peeringState)&&(i=!1)}catch(e){console.error(e)}if(!i)return e.audio=!1,e.audioTrack=!1,e.video=!1,e.videoTrack=!1,e.screen=!1,void(e.screenTrack=!1);try{if(n.streams,e.audio&&ad(n,e.session_id,"cam-audio")){var s=ah(n,e.session_id,"cam","audio");s&&(t&&t.audioTrack&&t.audioTrack.id===s.id?e.audioTrack=s:s.muted||(e.audioTrack=s)),e.audioTrack||(e.audio=!1)}if(e.video&&ad(n,e.session_id,"cam-video")){var o=ah(n,e.session_id,"cam","video");o&&(t&&t.videoTrack&&t.videoTrack.id===o.id?e.videoTrack=o:o.muted||(e.videoTrack=o)),e.videoTrack||(e.video=!1)}if(e.screen&&ad(n,e.session_id,"screen-audio")){var l=ah(n,e.session_id,"screen","audio");l&&(t&&t.screenAudioTrack&&t.screenAudioTrack.id===l.id?e.screenAudioTrack=l:l.muted||(e.screenAudioTrack=l))}if(e.screen&&ad(n,e.session_id,"screen-video")){var c=ah(n,e.session_id,"screen","video");c&&(t&&t.screenVideoTrack&&t.screenVideoTrack.id===c.id?e.screenVideoTrack=c:c.muted||(e.screenVideoTrack=c))}e.screenVideoTrack||e.screenAudioTrack||(e.screen=!1)}catch(e){console.error("unexpected error matching up tracks",e)}}}function ab(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var a_=new Map,aS=null;function aw(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var ak=new Map,aE=null;function aC(){var e;return iD()||void 0!==(null===(e=navigator.mediaDevices)||void 0===e?void 0:e.ondevicechange)}var aA=new Set;function aT(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aM(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aT(Object(r),!0).forEach(function(t){S(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aT(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}var ax=Object.freeze({VIDEO:"video",AUDIO:"audio",SCREEN_VIDEO:"screenVideo",SCREEN_AUDIO:"screenAudio",CUSTOM_VIDEO:"customVideo",CUSTOM_AUDIO:"customAudio"}),aO=Object.freeze({PARTICIPANTS:"participants",STREAMING:"streaming",TRANSCRIPTION:"transcription"}),aj=Object.values(ax),aP=["v","a","sv","sa","cv","ca"];Object.freeze(aj.reduce(function(e,t,r){return e[t]=aP[r],e},{})),Object.freeze(aP.reduce(function(e,t,r){return e[t]=aj[r],e},{}));var aI=[ax.VIDEO,ax.AUDIO,ax.SCREEN_VIDEO,ax.SCREEN_AUDIO],aN=Object.values(aO),aL=["p","s","t"];Object.freeze(aN.reduce(function(e,t,r){return e[t]=aL[r],e},{})),Object.freeze(aL.reduce(function(e,t,r){return e[t]=aN[r],e},{}));var aD=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.base,n=t.byUserId,i=t.byParticipantId;h(this,e),this.base=r,this.byUserId=n,this.byParticipantId=i}return g(e,[{key:"clone",value:function(){var t=new e;if(this.base instanceof aR?t.base=this.base.clone():t.base=this.base,void 0!==this.byUserId)for(var r in t.byUserId={},this.byUserId){var n=this.byUserId[r];t.byUserId[r]=n instanceof aR?n.clone():n}if(void 0!==this.byParticipantId)for(var i in t.byParticipantId={},this.byParticipantId){var a=this.byParticipantId[i];t.byParticipantId[i]=a instanceof aR?a.clone():a}return t}},{key:"toJSONObject",value:function(){var e={};if("boolean"==typeof this.base?e.base=this.base:this.base instanceof aR&&(e.base=this.base.toJSONObject()),void 0!==this.byUserId)for(var t in e.byUserId={},this.byUserId){var r=this.byUserId[t];e.byUserId[t]=r instanceof aR?r.toJSONObject():r}if(void 0!==this.byParticipantId)for(var n in e.byParticipantId={},this.byParticipantId){var i=this.byParticipantId[n];e.byParticipantId[n]=i instanceof aR?i.toJSONObject():i}return e}},{key:"toMinifiedJSONObject",value:function(){var e={};if(void 0!==this.base&&("boolean"==typeof this.base?e.b=this.base:e.b=this.base.toMinifiedJSONObject()),void 0!==this.byUserId)for(var t in e.u={},this.byUserId){var r=this.byUserId[t];e.u[t]="boolean"==typeof r?r:r.toMinifiedJSONObject()}if(void 0!==this.byParticipantId)for(var n in e.p={},this.byParticipantId){var i=this.byParticipantId[n];e.p[n]="boolean"==typeof i?i:i.toMinifiedJSONObject()}return e}},{key:"normalize",value:function(){return this.base instanceof aR&&(this.base=this.base.normalize()),this.byUserId&&(this.byUserId=Object.fromEntries(Object.entries(this.byUserId).map(function(e){var t=C(e,2),r=t[0],n=t[1];return[r,n instanceof aR?n.normalize():n]}))),this.byParticipantId&&(this.byParticipantId=Object.fromEntries(Object.entries(this.byParticipantId).map(function(e){var t=C(e,2),r=t[0],n=t[1];return[r,n instanceof aR?n.normalize():n]}))),this}}],[{key:"fromJSONObject",value:function(t){var r,n,i;if(void 0!==t.base&&(r="boolean"==typeof t.base?t.base:aR.fromJSONObject(t.base)),void 0!==t.byUserId)for(var a in n={},t.byUserId){var s=t.byUserId[a];n[a]="boolean"==typeof s?s:aR.fromJSONObject(s)}if(void 0!==t.byParticipantId)for(var o in i={},t.byParticipantId){var l=t.byParticipantId[o];i[o]="boolean"==typeof l?l:aR.fromJSONObject(l)}return new e({base:r,byUserId:n,byParticipantId:i})}},{key:"fromMinifiedJSONObject",value:function(t){var r,n,i;if(void 0!==t.b&&(r="boolean"==typeof t.b?t.b:aR.fromMinifiedJSONObject(t.b)),void 0!==t.u)for(var a in n={},t.u){var s=t.u[a];n[a]="boolean"==typeof s?s:aR.fromMinifiedJSONObject(s)}if(void 0!==t.p)for(var o in i={},t.p){var l=t.p[o];i[o]="boolean"==typeof l?l:aR.fromMinifiedJSONObject(l)}return new e({base:r,byUserId:n,byParticipantId:i})}},{key:"validateJSONObject",value:function(e){if("object"!==p(e))return[!1,"canReceive must be an object"];for(var t=["base","byUserId","byParticipantId"],r=0,n=Object.keys(e);r<n.length;r++){var i=n[r];if(!t.includes(i))return[!1,"canReceive can only contain keys (".concat(t.join(", "),")")];if("base"===i){var a=C(aR.validateJSONObject(e.base,!0),2),s=a[0],o=a[1];if(!s)return[!1,o]}else{if("object"!==p(e[i]))return[!1,"invalid (non-object) value for field '".concat(i,"' in canReceive")];for(var l=0,c=Object.values(e[i]);l<c.length;l++){var u=c[l],d=C(aR.validateJSONObject(u),2),h=d[0],f=d[1];if(!h)return[!1,f]}}}return[!0]}}])}(),aR=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.video,n=t.audio,i=t.screenVideo,a=t.screenAudio,s=t.customVideo,o=t.customAudio;h(this,e),this.video=r,this.audio=n,this.screenVideo=i,this.screenAudio=a,this.customVideo=s,this.customAudio=o}return g(e,[{key:"clone",value:function(){var t=new e;return void 0!==this.video&&(t.video=this.video),void 0!==this.audio&&(t.audio=this.audio),void 0!==this.screenVideo&&(t.screenVideo=this.screenVideo),void 0!==this.screenAudio&&(t.screenAudio=this.screenAudio),void 0!==this.customVideo&&(t.customVideo=aM({},this.customVideo)),void 0!==this.customAudio&&(t.customAudio=aM({},this.customAudio)),t}},{key:"toJSONObject",value:function(){var e={};return void 0!==this.video&&(e.video=this.video),void 0!==this.audio&&(e.audio=this.audio),void 0!==this.screenVideo&&(e.screenVideo=this.screenVideo),void 0!==this.screenAudio&&(e.screenAudio=this.screenAudio),void 0!==this.customVideo&&(e.customVideo=aM({},this.customVideo)),void 0!==this.customAudio&&(e.customAudio=aM({},this.customAudio)),e}},{key:"toMinifiedJSONObject",value:function(){var e={};return void 0!==this.video&&(e.v=this.video),void 0!==this.audio&&(e.a=this.audio),void 0!==this.screenVideo&&(e.sv=this.screenVideo),void 0!==this.screenAudio&&(e.sa=this.screenAudio),void 0!==this.customVideo&&(e.cv=aM({},this.customVideo)),void 0!==this.customAudio&&(e.ca=aM({},this.customAudio)),e}},{key:"normalize",value:function(){function e(e,t){return e&&1===Object.keys(e).length&&e["*"]===t}return!(!0!==this.video||!0!==this.audio||!0!==this.screenVideo||!0!==this.screenAudio||!e(this.customVideo,!0)||!e(this.customAudio,!0))||(!1!==this.video||!1!==this.audio||!1!==this.screenVideo||!1!==this.screenAudio||!e(this.customVideo,!1)||!e(this.customAudio,!1))&&this}}],[{key:"fromBoolean",value:function(t){return new e({video:t,audio:t,screenVideo:t,screenAudio:t,customVideo:{"*":t},customAudio:{"*":t}})}},{key:"fromJSONObject",value:function(t){return new e({video:t.video,audio:t.audio,screenVideo:t.screenVideo,screenAudio:t.screenAudio,customVideo:void 0!==t.customVideo?aM({},t.customVideo):void 0,customAudio:void 0!==t.customAudio?aM({},t.customAudio):void 0})}},{key:"fromMinifiedJSONObject",value:function(t){return new e({video:t.v,audio:t.a,screenVideo:t.sv,screenAudio:t.sa,customVideo:t.cv,customAudio:t.ca})}},{key:"validateJSONObject",value:function(e,t){if("boolean"==typeof e)return[!0];if("object"!==p(e))return[!1,"invalid (non-object, non-boolean) value in canReceive"];for(var r=Object.keys(e),n=0;n<r.length;n++){var i=r[n];if(!aj.includes(i))return[!1,"invalid media type '".concat(i,"' in canReceive")];if(aI.includes(i)){if("boolean"!=typeof e[i])return[!1,"invalid (non-boolean) value for media type '".concat(i,"' in canReceive")]}else{if("object"!==p(e[i]))return[!1,"invalid (non-object) value for media type '".concat(i,"' in canReceive")];for(var a=0,s=Object.values(e[i]);a<s.length;a++)if("boolean"!=typeof s[a])return[!1,"invalid (non-boolean) value for entry within '".concat(i,"' in canReceive")];if(t&&void 0===e[i]["*"])return[!1,'canReceive "base" permission must specify "*" as an entry within \''.concat(i,"'")]}}return t&&r.length!==aj.length?[!1,'canReceive "base" permission must specify all media types: '.concat(aj.join(", ")," (or be set to a boolean shorthand)")]:[!0]}}])}(),aF=["result"],aq=["preserveIframe"];function aV(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}function aU(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?aV(Object(r),!0).forEach(function(t){S(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):aV(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function a$(){try{var e=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(e){}return(a$=function(){return!!e})()}function aB(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return aJ(e,void 0);var r=({}).toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?aJ(e,void 0):void 0}}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,o=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return s=e.done,e},e:function(e){o=!0,a=e},f:function(){try{s||null==r.return||r.return()}finally{if(o)throw a}}}}function aJ(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=Array(t);r<t;r++)n[r]=e[r];return n}var aG={},aY="video",az=iD()?{data:{}}:{data:{},topology:"none"},aW={present:0,hidden:0},aQ={maxBitrate:{min:1e5,max:25e5},maxFramerate:{min:1,max:30},scaleResolutionDownBy:{min:1,max:8}},aH=Object.keys(aQ),aK=["state","volume","simulcastEncodings"],aX={androidInCallNotification:{title:"string",subtitle:"string",iconName:"string",disableForCustomOverride:"boolean"},disableAutoDeviceManagement:{audio:"boolean",video:"boolean"}},aZ={id:{iconPath:"string",iconPathDarkMode:"string",label:"string",tooltip:"string",visualState:"'default' | 'sidebar-open' | 'active'"}},a0={id:{allow:"string",controlledBy:"'*' | 'owners' | string[]",csp:"string",iconURL:"string",label:"string",loading:"'eager' | 'lazy'",location:"'main' | 'sidebar'",name:"string",referrerPolicy:"string",sandbox:"string",src:"string",srcdoc:"string",shared:"string[] | 'owners' | boolean"}},a1={customIntegrations:{validate:sp,help:sd()},customTrayButtons:{validate:sh,help:"customTrayButtons should be a dictionary of the type ".concat(JSON.stringify(aZ))},url:{validate:function(e){return"string"==typeof e},help:"url should be a string"},baseUrl:{validate:function(e){return"string"==typeof e},help:"baseUrl should be a string"},token:{validate:function(e){return"string"==typeof e},help:"token should be a string",queryString:"t"},dailyConfig:{validate:function(e,t){try{return t.validateDailyConfig(e),!0}catch(e){console.error("Failed to validate dailyConfig",e)}return!1},help:"Unsupported dailyConfig. Check error logs for detailed info."},reactNativeConfig:{validate:function(e){return function e(t,r){if(void 0===r)return!1;switch(p(r)){case"string":return p(t)===r;case"object":if("object"!==p(t))return!1;for(var n in t)if(!e(t[n],r[n]))return!1;return!0;default:return!1}}(e,aX)},help:"reactNativeConfig should look like ".concat(JSON.stringify(aX),", all fields optional")},lang:{validate:function(e){return["da","de","en-us","en","es","fi","fr","it","jp","ka","nl","no","pl","pt","pt-BR","ru","sv","tr","user"].includes(e)},help:"language not supported. Options are: da, de, en-us, en, es, fi, fr, it, jp, ka, nl, no, pl, pt, pt-BR, ru, sv, tr, user"},userName:!0,userData:{validate:function(e){try{return si(e),!0}catch(e){return console.error(e),!1}},help:"invalid userData type provided"},startVideoOff:!0,startAudioOff:!0,allowLocalVideo:!0,allowLocalAudio:!0,activeSpeakerMode:!0,showLeaveButton:!0,showLocalVideo:!0,showParticipantsBar:!0,showFullscreenButton:!0,showUserNameChangeUI:!0,iframeStyle:!0,customLayout:!0,cssFile:!0,cssText:!0,bodyClass:!0,videoSource:{validate:function(e,t){var r;if("boolean"==typeof e)return t._preloadCache.allowLocalVideo=e,!0;if(e instanceof MediaStreamTrack)t._sharedTracks.videoTrack=e,r={customTrack:ix};else{if(delete t._sharedTracks.videoTrack,"string"!=typeof e)return console.error("videoSource must be a MediaStreamTrack, boolean, or a string"),!1;r={deviceId:e}}return t._updatePreloadCacheInputSettings({video:{settings:r}},!1),!0}},audioSource:{validate:function(e,t){var r;if("boolean"==typeof e)return t._preloadCache.allowLocalAudio=e,!0;if(e instanceof MediaStreamTrack)t._sharedTracks.audioTrack=e,r={customTrack:ix};else{if(delete t._sharedTracks.audioTrack,"string"!=typeof e)return console.error("audioSource must be a MediaStreamTrack, boolean, or a string"),!1;r={deviceId:e}}return t._updatePreloadCacheInputSettings({audio:{settings:r}},!1),!0}},subscribeToTracksAutomatically:{validate:function(e,t){return t._preloadCache.subscribeToTracksAutomatically=e,!0}},theme:{validate:function(e){var t=["accent","accentText","background","backgroundAccent","baseText","border","mainAreaBg","mainAreaBgAccent","mainAreaText","supportiveText"],r=function(e){for(var r=0,n=Object.keys(e);r<n.length;r++){var i=n[r];if(!t.includes(i))return console.error('unsupported color "'.concat(i,'". Valid colors: ').concat(t.join(", "))),!1;if(!e[i].match(/^#[0-9a-f]{6}|#[0-9a-f]{3}$/i))return console.error("".concat(i,' theme color should be provided in valid hex color format. Received: "').concat(e[i],'"')),!1}return!0};return"object"===p(e)&&("light"in e&&"dark"in e||"colors"in e)?"light"in e&&"dark"in e?"colors"in e.light?"colors"in e.dark?r(e.light.colors)&&r(e.dark.colors):(console.error('Dark theme is missing "colors" property.',e),!1):(console.error('Light theme is missing "colors" property.',e),!1):r(e.colors):(console.error('Theme must contain either both "light" and "dark" properties, or "colors".',e),!1)},help:"unsupported theme configuration. Check error logs for detailed info."},layoutConfig:{validate:function(e){if("grid"in e){var t=e.grid;if("maxTilesPerPage"in t){if(!Number.isInteger(t.maxTilesPerPage))return console.error("grid.maxTilesPerPage should be an integer. You passed ".concat(t.maxTilesPerPage,".")),!1;if(t.maxTilesPerPage>49)return console.error("grid.maxTilesPerPage can't be larger than 49 without sacrificing browser performance. Please contact us at https://www.daily.co/contact to talk about your use case."),!1}if("minTilesPerPage"in t){if(!Number.isInteger(t.minTilesPerPage))return console.error("grid.minTilesPerPage should be an integer. You passed ".concat(t.minTilesPerPage,".")),!1;if(t.minTilesPerPage<1)return console.error("grid.minTilesPerPage can't be lower than 1."),!1;if("maxTilesPerPage"in t&&t.minTilesPerPage>t.maxTilesPerPage)return console.error("grid.minTilesPerPage can't be higher than grid.maxTilesPerPage."),!1}}return!0},help:"unsupported layoutConfig. Check error logs for detailed info."},receiveSettings:{validate:function(e){return sa(e,{allowAllParticipantsKey:!1})},help:su({allowAllParticipantsKey:!1})},sendSettings:{validate:function(e,t){return!!function(e,t){try{return t.validateUpdateSendSettings(e),!0}catch(e){return console.error("Failed to validate send settings",e),!1}}(e,t)&&(t._preloadCache.sendSettings=e,!0)},help:"Invalid sendSettings provided. Check error logs for detailed info."},inputSettings:{validate:function(e,t){var r;return!!ss(e)&&(t._inputSettings||(t._inputSettings={}),so(e,null===(r=t.properties)||void 0===r?void 0:r.dailyConfig,t._sharedTracks),t._updatePreloadCacheInputSettings(e,!0),!0)},help:sc()},layout:{validate:function(e){return"custom-v1"===e||"browser"===e||"none"===e},help:'layout may only be set to "custom-v1"',queryString:"layout"},emb:{queryString:"emb"},embHref:{queryString:"embHref"},dailyJsVersion:{queryString:"dailyJsVersion"},proxy:{queryString:"proxy"},strictMode:!0,allowMultipleCallInstances:!0},a2={styles:{validate:function(e){for(var t in e)if("cam"!==t&&"screen"!==t)return!1;if(e.cam){for(var r in e.cam)if("div"!==r&&"video"!==r)return!1}if(e.screen){for(var n in e.screen)if("div"!==n&&"video"!==n)return!1}return!0},help:"styles format should be a subset of: { cam: {div: {}, video: {}}, screen: {div: {}, video: {}} }"},setSubscribedTracks:{validate:function(e,t){if(t._preloadCache.subscribeToTracksAutomatically)return!1;var r=[!0,!1,"staged"];if(r.includes(e)||!iD()&&"avatar"===e)return!0;var n=["audio","video","screenAudio","screenVideo","rmpAudio","rmpVideo"],i=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];for(var a in e)if("custom"===a){if(!r.includes(e[a])&&!i(e[a],!0))return!1}else{var s=!t&&!n.includes(a),o=!r.includes(e[a]);if(s||o)return!1}return!0};return i(e)},help:"setSubscribedTracks cannot be used when setSubscribeToTracksAutomatically is enabled, and should be of the form: "+"true".concat(iD()?"":" | 'avatar'"," | false | 'staged' | { [audio: true|false|'staged'], [video: true|false|'staged'], [screenAudio: true|false|'staged'], [screenVideo: true|false|'staged'] }")},setAudio:!0,setVideo:!0,setScreenShare:{validate:function(e){return!1===e},help:"setScreenShare must be false, as it's only meant for stopping remote participants' screen shares"},eject:!0,updatePermissions:{validate:function(e){for(var t=0,r=Object.entries(e);t<r.length;t++){var n=C(r[t],2),i=n[0],a=n[1];switch(i){case"hasPresence":if("boolean"!=typeof a)return!1;break;case"canSend":if(a instanceof Set||a instanceof Array||Array.isArray(a)){var s,o=["video","audio","screenVideo","screenAudio","customVideo","customAudio"],l=aB(a);try{for(l.s();!(s=l.n()).done;){var c=s.value;if(!o.includes(c))return!1}}catch(e){l.e(e)}finally{l.f()}}else if("boolean"!=typeof a)return!1;(a instanceof Array||Array.isArray(a))&&(e.canSend=new Set(a));break;case"canReceive":var u=C(aD.validateJSONObject(a),2),d=u[0],h=u[1];if(!d)return console.error(h),!1;break;case"canAdmin":if(a instanceof Set||a instanceof Array||Array.isArray(a)){var p,f=["participants","streaming","transcription"],m=aB(a);try{for(m.s();!(p=m.n()).done;){var g=p.value;if(!f.includes(g))return!1}}catch(e){m.e(e)}finally{m.f()}}else if("boolean"!=typeof a)return!1;(a instanceof Array||Array.isArray(a))&&(e.canAdmin=new Set(a));break;default:return!1}}return!0},help:"updatePermissions can take hasPresence, canSend, canReceive, and canAdmin permissions. hasPresence must be a boolean. canSend can be a boolean or an Array or Set of media types (video, audio, screenVideo, screenAudio, customVideo, customAudio). canReceive must be an object specifying base, byUserId, and/or byParticipantId fields (see documentation for more details). canAdmin can be a boolean or an Array or Set of admin types (participants, streaming, transcription)."}};Promise.any||(Promise.any=function(){var e=k(function*(e){return new Promise(function(t,r){var n=[];e.forEach(function(i){return Promise.resolve(i).then(function(e){t(e)}).catch(function(t){n.push(t),n.length===e.length&&r(n)})})})});return function(t){return e.apply(this,arguments)}}());var a3=function(){var e,t,r,n,i,a,s,o,l,c,u,f,m,b,w,E,A,T,M,x,O,j,P,I,N,L,D,R,F,q,V,U,$,B,G,Y,W,Q;function H(e){var t,r,n,i,a,s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(h(this,H),i=y(i=H),S(n=v(this,a$()?Reflect.construct(i,a||[],y(this).constructor):i.apply(this,a)),"startListeningForDeviceChanges",function(){var e;e=n.handleDeviceChange,aC()?a_.has(e)||(a_.set(e,{}),navigator.mediaDevices.enumerateDevices().then(function(t){var r;a_.has(e)&&(a_.get(e).lastDevicesString=JSON.stringify(t),aS||(r=k(function*(){var e,t=yield navigator.mediaDevices.enumerateDevices(),r=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return ab(e,void 0);var r=({}).toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ab(e,void 0):void 0}}(e))){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,o=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return s=e.done,e},e:function(e){o=!0,a=e},f:function(){try{s||null==r.return||r.return()}finally{if(o)throw a}}}}(a_.keys());try{for(r.s();!(e=r.n()).done;){var n=e.value,i=JSON.stringify(t);i!==a_.get(n).lastDevicesString&&(a_.get(n).lastDevicesString=i,n(t))}}catch(e){r.e(e)}finally{r.f()}}),aS=function(){return r.apply(this,arguments)},navigator.mediaDevices.addEventListener("devicechange",aS)))}).catch(function(){})):ak.has(e)||(ak.set(e,{}),navigator.mediaDevices.enumerateDevices().then(function(t){ak.has(e)&&(ak.get(e).lastDevicesString=JSON.stringify(t),aE||(aE=setInterval(k(function*(){var e,t=yield navigator.mediaDevices.enumerateDevices(),r=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(e){if("string"==typeof e)return aw(e,void 0);var r=({}).toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?aw(e,void 0):void 0}}(e))){r&&(e=r);var n=0,i=function(){};return{s:i,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:i}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,o=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return s=e.done,e},e:function(e){o=!0,a=e},f:function(){try{s||null==r.return||r.return()}finally{if(o)throw a}}}}(ak.keys());try{for(r.s();!(e=r.n()).done;){var n=e.value,i=JSON.stringify(t);i!==ak.get(n).lastDevicesString&&(ak.get(n).lastDevicesString=i,n(t))}}catch(e){r.e(e)}finally{r.f()}}),3e3)))}))}),S(n,"stopListeningForDeviceChanges",function(){var e;e=n.handleDeviceChange,aC()?a_.has(e)&&(a_.delete(e),0===a_.size&&aS&&(navigator.mediaDevices.removeEventListener("devicechange",aS),aS=null)):ak.has(e)&&(ak.delete(e),0===ak.size&&aE&&(clearInterval(aE),aE=null))}),S(n,"handleDeviceChange",function(e){e=e.map(function(e){return JSON.parse(JSON.stringify(e))}),n.emitDailyJSEvent({action:"available-devices-updated",availableDevices:e})}),S(n,"handleNativeAppStateChange",(t=k(function*(e){if("destroyed"===e)return console.warn("App has been destroyed before leaving the meeting. Cleaning up all the resources!"),void(yield n.destroy());n.disableReactNativeAutoDeviceManagement("video")||("active"===e?n.camUnmutedBeforeLosingNativeActiveState&&n.setLocalVideo(!0):(n.camUnmutedBeforeLosingNativeActiveState=n.localVideo(),n.camUnmutedBeforeLosingNativeActiveState&&n.setLocalVideo(!1)))}),function(e){return t.apply(this,arguments)})),S(n,"handleNativeAudioFocusChange",function(e){n.disableReactNativeAutoDeviceManagement("audio")||(n._hasNativeAudioFocus=e,n.toggleParticipantAudioBasedOnNativeAudioFocus(),n._hasNativeAudioFocus?n.micUnmutedBeforeLosingNativeAudioFocus&&n.setLocalAudio(!0):(n.micUnmutedBeforeLosingNativeAudioFocus=n.localAudio(),n.setLocalAudio(!1)))}),S(n,"handleNativeSystemScreenCaptureStop",function(){n.stopScreenShare()}),n.strictMode=void 0===s.strictMode||s.strictMode,n.allowMultipleCallInstances=null!==(r=s.allowMultipleCallInstances)&&void 0!==r&&r,Object.keys(aG).length&&(n._logDuplicateInstanceAttempt(),!n.allowMultipleCallInstances)){if(n.strictMode)throw Error("Duplicate DailyIframe instances are not allowed");console.warn("Using strictMode: false to allow multiple call instances is now deprecated. Set `allowMultipleCallInstances: true`")}if(window._daily||(window._daily={pendings:[],instances:{}}),n.callClientId=eo(),aG[n.callClientId]=n,window._daily.instances[n.callClientId]={},n._sharedTracks={},window._daily.instances[n.callClientId].tracks=n._sharedTracks,s.dailyJsVersion=H.version(),n._iframe=e,n._callObjectMode="none"===s.layout&&!n._iframe,n._preloadCache={subscribeToTracksAutomatically:!0,outputDeviceId:null,inputSettings:null,sendSettings:null,videoTrackForNetworkConnectivityTest:null,videoTrackForConnectionQualityTest:null},void 0!==s.showLocalVideo?n._callObjectMode?console.error("showLocalVideo is not available in call object mode"):n._showLocalVideo=!!s.showLocalVideo:n._showLocalVideo=!0,void 0!==s.showParticipantsBar?n._callObjectMode?console.error("showParticipantsBar is not available in call object mode"):n._showParticipantsBar=!!s.showParticipantsBar:n._showParticipantsBar=!0,void 0!==s.customIntegrations?n._callObjectMode?console.error("customIntegrations is not available in call object mode"):n._customIntegrations=s.customIntegrations:n._customIntegrations={},void 0!==s.customTrayButtons?n._callObjectMode?console.error("customTrayButtons is not available in call object mode"):n._customTrayButtons=s.customTrayButtons:n._customTrayButtons={},void 0!==s.activeSpeakerMode?n._callObjectMode?console.error("activeSpeakerMode is not available in call object mode"):n._activeSpeakerMode=!!s.activeSpeakerMode:n._activeSpeakerMode=!1,s.receiveSettings?n._callObjectMode?n._receiveSettings=s.receiveSettings:console.error("receiveSettings is only available in call object mode"):n._receiveSettings={},n.validateProperties(s),n.properties=aU({},s),n._inputSettings||(n._inputSettings={}),n._callObjectLoader=n._callObjectMode?new aa(n.callClientId):null,n._callState=r2,n._isPreparingToJoin=!1,n._accessState={access:na},n._meetingSessionSummary={},n._finalSummaryOfPrevSession={},n._meetingSessionState=sv(az,n._callObjectMode),n._nativeInCallAudioMode=aY,n._participants={},n._isScreenSharing=!1,n._participantCounts=aW,n._rmpPlayerState={},n._waitingParticipants={},n._network={threshold:"good",quality:100,networkState:"unknown",stats:{}},n._activeSpeaker={},n._localAudioLevel=0,n._isLocalAudioLevelObserverRunning=!1,n._remoteParticipantsAudioLevel={},n._isRemoteParticipantsAudioLevelObserverRunning=!1,n._maxAppMessageSize=4096,n._messageChannel=iD()?new i8:new i3,n._iframe&&(n._iframe.requestFullscreen?n._iframe.addEventListener("fullscreenchange",function(){document.fullscreenElement===n._iframe?(n.emitDailyJSEvent({action:ip}),n.sendMessageToCallMachine({action:ip})):(n.emitDailyJSEvent({action:im}),n.sendMessageToCallMachine({action:im}))}):n._iframe.webkitRequestFullscreen&&n._iframe.addEventListener("webkitfullscreenchange",function(){document.webkitFullscreenElement===n._iframe?(n.emitDailyJSEvent({action:ip}),n.sendMessageToCallMachine({action:ip})):(n.emitDailyJSEvent({action:im}),n.sendMessageToCallMachine({action:im}))})),iD()){var o=n.nativeUtils();o.addAudioFocusChangeListener&&o.removeAudioFocusChangeListener&&o.addAppStateChangeListener&&o.removeAppStateChangeListener&&o.addSystemScreenCaptureStopListener&&o.removeSystemScreenCaptureStopListener||console.warn("expected (add|remove)(AudioFocusChange|AppActiveStateChange|SystemScreenCaptureStop)Listener to be available in React Native"),n._hasNativeAudioFocus=!0,o.addAudioFocusChangeListener(n.handleNativeAudioFocusChange),o.addAppStateChangeListener(n.handleNativeAppStateChange),o.addSystemScreenCaptureStopListener(n.handleNativeSystemScreenCaptureStop)}return n._callObjectMode&&n.startListeningForDeviceChanges(),n._messageChannel.addListenerForMessagesFromCallMachine(n.handleMessageFromCallMachine,n.callClientId,n),n}return _(H,J),g(H,[{key:"destroy",value:(Q=k(function*(){try{yield this.leave()}catch(e){}var e,t=this._iframe;if(t){var r=t.parentElement;r&&r.removeChild(t)}if(this._messageChannel.removeListener(this.handleMessageFromCallMachine),iD()){var n=this.nativeUtils();n.removeAudioFocusChangeListener(this.handleNativeAudioFocusChange),n.removeAppStateChangeListener(this.handleNativeAppStateChange),n.removeSystemScreenCaptureStopListener(this.handleNativeSystemScreenCaptureStop)}this._callObjectMode&&this.stopListeningForDeviceChanges(),this.resetMeetingDependentVars(),this._destroyed=!0,this.emitDailyJSEvent({action:"call-instance-destroyed"}),delete aG[this.callClientId],(null===(e=window)||void 0===e||null===(e=e._daily)||void 0===e?void 0:e.instances)&&delete window._daily.instances[this.callClientId],this.strictMode&&(this.callClientId=void 0)}),function(){return Q.apply(this,arguments)})},{key:"isDestroyed",value:function(){return!!this._destroyed}},{key:"loadCss",value:function(e){var t=e.bodyClass,r=e.cssFile,n=e.cssText;return sr(),this.sendMessageToCallMachine({action:"load-css",cssFile:this.absoluteUrl(r),bodyClass:t,cssText:n}),this}},{key:"iframe",value:function(){return sr(),this._iframe}},{key:"meetingState",value:function(){return this._callState}},{key:"accessState",value:function(){return se(this._callObjectMode,"accessState()"),this._accessState}},{key:"participants",value:function(){return this._participants}},{key:"participantCounts",value:function(){return this._participantCounts}},{key:"waitingParticipants",value:function(){return se(this._callObjectMode,"waitingParticipants()"),this._waitingParticipants}},{key:"validateParticipantProperties",value:function(e,t){for(var r in t){if(!a2[r])throw Error("unrecognized updateParticipant property ".concat(r));if(a2[r].validate&&!a2[r].validate(t[r],this,this._participants[e]))throw Error(a2[r].help)}}},{key:"updateParticipant",value:function(e,t){return this._participants.local&&this._participants.local.session_id===e&&(e="local"),e&&t&&(this.validateParticipantProperties(e,t),this.sendMessageToCallMachine({action:"update-participant",id:e,properties:t})),this}},{key:"updateParticipants",value:function(e){var t=this._participants.local&&this._participants.local.session_id;for(var r in e)r===t&&(r="local"),r&&e[r]&&this.validateParticipantProperties(r,e[r]);return this.sendMessageToCallMachine({action:"update-participants",participants:e}),this}},{key:"updateWaitingParticipant",value:(W=k(function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(se(this._callObjectMode,"updateWaitingParticipant()"),a5(this._callState,"updateWaitingParticipant()"),"string"!=typeof t||"object"!==p(r))throw Error("updateWaitingParticipant() must take an id string and a updates object");return new Promise(function(n,i){e.sendMessageToCallMachine({action:"daily-method-update-waiting-participant",id:t,updates:r},function(e){e.error&&i(e.error),e.id||i(Error("unknown error in updateWaitingParticipant()")),n({id:e.id})})})}),function(){return W.apply(this,arguments)})},{key:"updateWaitingParticipants",value:(Y=k(function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(se(this._callObjectMode,"updateWaitingParticipants()"),a5(this._callState,"updateWaitingParticipants()"),"object"!==p(t))throw Error("updateWaitingParticipants() must take a mapping between ids and update objects");return new Promise(function(r,n){e.sendMessageToCallMachine({action:"daily-method-update-waiting-participants",updatesById:t},function(e){e.error&&n(e.error),e.ids||n(Error("unknown error in updateWaitingParticipants()")),r({ids:e.ids})})})}),function(){return Y.apply(this,arguments)})},{key:"requestAccess",value:(G=k(function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=t.access,n=void 0===r?{level:ns}:r,i=t.name,a=void 0===i?"":i;return se(this._callObjectMode,"requestAccess()"),a5(this._callState,"requestAccess()"),new Promise(function(t,r){e.sendMessageToCallMachine({action:"daily-method-request-access",access:n,name:a},function(e){e.error&&r(e.error),e.access||r(Error("unknown error in requestAccess()")),t({access:e.access,granted:e.granted})})})}),function(){return G.apply(this,arguments)})},{key:"localAudio",value:function(){return this._participants.local?!["blocked","off"].includes(this._participants.local.tracks.audio.state):null}},{key:"localVideo",value:function(){return this._participants.local?!["blocked","off"].includes(this._participants.local.tracks.video.state):null}},{key:"setLocalAudio",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return"forceDiscardTrack"in t&&(iD()?(console.warn("forceDiscardTrack option not supported in React Native; ignoring"),t={}):e&&(console.warn("forceDiscardTrack option only supported when calling setLocalAudio(false); ignoring"),t={})),this.sendMessageToCallMachine({action:"local-audio",state:e,options:t}),this}},{key:"localScreenAudio",value:function(){return this._participants.local?!["blocked","off"].includes(this._participants.local.tracks.screenAudio.state):null}},{key:"localScreenVideo",value:function(){return this._participants.local?!["blocked","off"].includes(this._participants.local.tracks.screenVideo.state):null}},{key:"updateScreenShare",value:function(e){if(this._isScreenSharing)return this.sendMessageToCallMachine({action:"local-screen-update",options:e}),this;console.warn("There is no screen share in progress. Try calling startScreenShare first.")}},{key:"setLocalVideo",value:function(e){return this.sendMessageToCallMachine({action:"local-video",state:e}),this}},{key:"_setAllowLocalAudio",value:function(e){if(this._preloadCache.allowLocalAudio=e,this._callMachineInitialized)return this.sendMessageToCallMachine({action:"set-allow-local-audio",state:e}),this}},{key:"_setAllowLocalVideo",value:function(e){if(this._preloadCache.allowLocalVideo=e,this._callMachineInitialized)return this.sendMessageToCallMachine({action:"set-allow-local-video",state:e}),this}},{key:"getReceiveSettings",value:(B=k(function*(e){var t=this,r=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).showInheritedValues,n=void 0!==r&&r;if(se(this._callObjectMode,"getReceiveSettings()"),!this._callMachineInitialized)return this._receiveSettings;switch(p(e)){case"string":return new Promise(function(r){t.sendMessageToCallMachine({action:"get-single-participant-receive-settings",id:e,showInheritedValues:n},function(e){r(e.receiveSettings)})});case"undefined":return this._receiveSettings;default:throw Error('first argument to getReceiveSettings() must be a participant id (or "base"), or there should be no arguments')}}),function(e){return B.apply(this,arguments)})},{key:"updateReceiveSettings",value:($=k(function*(e){var t=this;if(se(this._callObjectMode,"updateReceiveSettings()"),!sa(e,{allowAllParticipantsKey:!0}))throw Error(su({allowAllParticipantsKey:!0}));return a5(this._callState,"updateReceiveSettings()","To specify receive settings earlier, use the receiveSettings config property."),new Promise(function(r){t.sendMessageToCallMachine({action:"update-receive-settings",receiveSettings:e},function(e){r({receiveSettings:e.receiveSettings})})})}),function(e){return $.apply(this,arguments)})},{key:"_prepInputSettingsForSharing",value:function(e,t){if(e){var r={};if(e.audio){e.audio.settings&&(!Object.keys(e.audio.settings).length&&t||(r.audio={settings:aU({},e.audio.settings)})),t&&null!==(n=r.audio)&&void 0!==n&&null!==(n=n.settings)&&void 0!==n&&n.customTrack&&(r.audio.settings={customTrack:this._sharedTracks.audioTrack});var n,i,a,s="none"===(null===(i=e.audio.processor)||void 0===i?void 0:i.type)&&(null===(a=e.audio.processor)||void 0===a?void 0:a._isDefaultWhenNone);if(e.audio.processor&&!s){var o=aU({},e.audio.processor);delete o._isDefaultWhenNone,r.audio=aU(aU({},r.audio),{},{processor:o})}}if(e.video){e.video.settings&&(!Object.keys(e.video.settings).length&&t||(r.video={settings:aU({},e.video.settings)})),t&&null!==(l=r.video)&&void 0!==l&&null!==(l=l.settings)&&void 0!==l&&l.customTrack&&(r.video.settings={customTrack:this._sharedTracks.videoTrack});var l,c,u,d="none"===(null===(c=e.video.processor)||void 0===c?void 0:c.type)&&(null===(u=e.video.processor)||void 0===u?void 0:u._isDefaultWhenNone);if(e.video.processor&&!d){var h=aU({},e.video.processor);delete h._isDefaultWhenNone,r.video=aU(aU({},r.video),{},{processor:h})}}return r}}},{key:"getInputSettings",value:function(){var e=this;return sr(),new Promise(function(t){t(e._getInputSettings())})}},{key:"_getInputSettings",value:function(){var e,t,r,n,i,a,s={processor:{type:"none",_isDefaultWhenNone:!0}};this._inputSettings?(e=(null===(r=this._inputSettings)||void 0===r?void 0:r.video)||s,t=(null===(n=this._inputSettings)||void 0===n?void 0:n.audio)||s):(e=(null===(i=this._preloadCache)||void 0===i||null===(i=i.inputSettings)||void 0===i?void 0:i.video)||s,t=(null===(a=this._preloadCache)||void 0===a||null===(a=a.inputSettings)||void 0===a?void 0:a.audio)||s);var o={audio:t,video:e};return this._prepInputSettingsForSharing(o,!0)}},{key:"_updatePreloadCacheInputSettings",value:function(e,t){var r,n,i,a,s,o,l=this._inputSettings||{},c={};e.video?((c.video={},e.video.settings)?(c.video.settings={},t||e.video.settings.customTrack||null===(i=l.video)||void 0===i||!i.settings?c.video.settings=e.video.settings:c.video.settings=aU(aU({},l.video.settings),e.video.settings),Object.keys(c.video.settings).length||delete c.video.settings):null!==(r=l.video)&&void 0!==r&&r.settings&&(c.video.settings=l.video.settings),e.video.processor?c.video.processor=e.video.processor:null!==(n=l.video)&&void 0!==n&&n.processor&&(c.video.processor=l.video.processor)):l.video&&(c.video=l.video),e.audio?((c.audio={},e.audio.settings)?(c.audio.settings={},t||e.audio.settings.customTrack||null===(o=l.audio)||void 0===o||!o.settings?c.audio.settings=e.audio.settings:c.audio.settings=aU(aU({},l.audio.settings),e.audio.settings),Object.keys(c.audio.settings).length||delete c.audio.settings):null!==(a=l.audio)&&void 0!==a&&a.settings&&(c.audio.settings=l.audio.settings),e.audio.processor?c.audio.processor=e.audio.processor:null!==(s=l.audio)&&void 0!==s&&s.processor&&(c.audio.processor=l.audio.processor)):l.audio&&(c.audio=l.audio),this._maybeUpdateInputSettings(c)}},{key:"_devicesFromInputSettings",value:function(e){var t,r,n=(null==e||null===(t=e.video)||void 0===t||null===(t=t.settings)||void 0===t?void 0:t.deviceId)||null,i=(null==e||null===(r=e.audio)||void 0===r||null===(r=r.settings)||void 0===r?void 0:r.deviceId)||null,a=this._preloadCache.outputDeviceId||null;return{camera:n?{deviceId:n}:{},mic:i?{deviceId:i}:{},speaker:a?{deviceId:a}:{}}}},{key:"updateInputSettings",value:(U=k(function*(e){var t=this;return sr(),ss(e)?e.video||e.audio?(so(e,this.properties.dailyConfig,this._sharedTracks),this._callObjectMode&&!this._callMachineInitialized?(this._updatePreloadCacheInputSettings(e,!0),this._getInputSettings()):new Promise(function(r,n){t.sendMessageToCallMachine({action:"update-input-settings",inputSettings:e},function(i){if(i.error)n(i.error);else{if(i.returnPreloadCache)return t._updatePreloadCacheInputSettings(e,!0),void r(t._getInputSettings());t._maybeUpdateInputSettings(i.inputSettings),r(t._prepInputSettingsForSharing(i.inputSettings,!0))}})})):this._getInputSettings():(console.error(sc()),Promise.reject(sc()))}),function(e){return U.apply(this,arguments)})},{key:"setBandwidth",value:function(e){var t=e.kbs,r=e.trackConstraints;if(sr(),this._callMachineInitialized)return this.sendMessageToCallMachine({action:"set-bandwidth",kbs:t,trackConstraints:r}),this}},{key:"getDailyLang",value:function(){var e=this;if(sr(),this._callMachineInitialized)return new Promise(function(t){e.sendMessageToCallMachine({action:"get-daily-lang"},function(e){delete e.action,delete e.callbackStamp,t(e)})})}},{key:"setDailyLang",value:function(e){return sr(),this.sendMessageToCallMachine({action:"set-daily-lang",lang:e}),this}},{key:"setProxyUrl",value:function(e){return this.sendMessageToCallMachine({action:"set-proxy-url",proxyUrl:e}),this}},{key:"setIceConfig",value:function(e){return this.sendMessageToCallMachine({action:"set-ice-config",iceConfig:e}),this}},{key:"meetingSessionSummary",value:function(){return[r6,r9].includes(this._callState)?this._finalSummaryOfPrevSession:this._meetingSessionSummary}},{key:"getMeetingSession",value:(V=k(function*(){var e=this;return console.warn("getMeetingSession() is deprecated: use meetingSessionSummary(), which will return immediately"),a5(this._callState,"getMeetingSession()"),new Promise(function(t){e.sendMessageToCallMachine({action:"get-meeting-session"},function(e){delete e.action,delete e.callbackStamp,t(e)})})}),function(){return V.apply(this,arguments)})},{key:"meetingSessionState",value:function(){return a5(this._callState,"meetingSessionState"),this._meetingSessionState}},{key:"setMeetingSessionData",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"replace";se(this._callObjectMode,"setMeetingSessionData()"),a5(this._callState,"setMeetingSessionData");try{new ae({data:e,mergeStrategy:t})}catch(e){throw console.error(e),e}try{this.sendMessageToCallMachine({action:"set-session-data",data:e,mergeStrategy:t})}catch(e){throw Error("Error setting meeting session data: ".concat(e))}}},{key:"setUserName",value:function(e,t){var r=this;return this.properties.userName=e,new Promise(function(n){r.sendMessageToCallMachine({action:"set-user-name",name:null!=e?e:"",thisMeetingOnly:iD()||!!t&&!!t.thisMeetingOnly},function(e){delete e.action,delete e.callbackStamp,n(e)})})}},{key:"setUserData",value:(q=k(function*(e){var t=this;try{si(e)}catch(e){throw console.error(e),e}if(this.properties.userData=e,this._callMachineInitialized)return new Promise(function(r){try{t.sendMessageToCallMachine({action:"set-user-data",userData:e},function(e){delete e.action,delete e.callbackStamp,r(e)})}catch(e){throw Error("Error setting user data: ".concat(e))}})}),function(e){return q.apply(this,arguments)})},{key:"validateAudioLevelInterval",value:function(e){if(e&&(e<100||"number"!=typeof e))throw Error("The interval must be a number greater than or equal to 100 milliseconds.")}},{key:"startLocalAudioLevelObserver",value:function(e){var t=this;if("undefined"==typeof AudioWorkletNode&&!iD())throw Error("startLocalAudioLevelObserver() is not supported on this browser");if(this.validateAudioLevelInterval(e),this._callMachineInitialized)return this._isLocalAudioLevelObserverRunning=!0,new Promise(function(r,n){t.sendMessageToCallMachine({action:"start-local-audio-level-observer",interval:e},function(e){t._isLocalAudioLevelObserverRunning=!e.error,e.error?n({error:e.error}):r()})});this._preloadCache.localAudioLevelObserver={enabled:!0,interval:e}}},{key:"isLocalAudioLevelObserverRunning",value:function(){return this._isLocalAudioLevelObserverRunning}},{key:"stopLocalAudioLevelObserver",value:function(){this._preloadCache.localAudioLevelObserver=null,this._localAudioLevel=0,this._isLocalAudioLevelObserverRunning=!1,this.sendMessageToCallMachine({action:"stop-local-audio-level-observer"})}},{key:"startRemoteParticipantsAudioLevelObserver",value:function(e){var t=this;if(this.validateAudioLevelInterval(e),this._callMachineInitialized)return this._isRemoteParticipantsAudioLevelObserverRunning=!0,new Promise(function(r,n){t.sendMessageToCallMachine({action:"start-remote-participants-audio-level-observer",interval:e},function(e){t._isRemoteParticipantsAudioLevelObserverRunning=!e.error,e.error?n({error:e.error}):r()})});this._preloadCache.remoteParticipantsAudioLevelObserver={enabled:!0,interval:e}}},{key:"isRemoteParticipantsAudioLevelObserverRunning",value:function(){return this._isRemoteParticipantsAudioLevelObserverRunning}},{key:"stopRemoteParticipantsAudioLevelObserver",value:function(){this._preloadCache.remoteParticipantsAudioLevelObserver=null,this._remoteParticipantsAudioLevel={},this._isRemoteParticipantsAudioLevelObserverRunning=!1,this.sendMessageToCallMachine({action:"stop-remote-participants-audio-level-observer"})}},{key:"startCamera",value:(F=k(function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(se(this._callObjectMode,"startCamera()"),a6(this._callState,this._isPreparingToJoin,"startCamera()","Did you mean to use setLocalAudio() and/or setLocalVideo() instead?"),this.needsLoad())try{yield this.load(t)}catch(e){return Promise.reject(e)}else{if(this._didPreAuth){if(t.url&&t.url!==this.properties.url)return console.error("url in startCamera() is different than the one used in preAuth()"),Promise.reject();if(t.token&&t.token!==this.properties.token)return console.error("token in startCamera() is different than the one used in preAuth()"),Promise.reject()}this.validateProperties(t),this.properties=aU(aU({},this.properties),t)}return new Promise(function(t){e._preloadCache.inputSettings=e._prepInputSettingsForSharing(e._inputSettings,!1),e.sendMessageToCallMachine({action:"start-camera",properties:a4(e.properties,e.callClientId),preloadCache:a4(e._preloadCache,e.callClientId)},function(e){t({camera:e.camera,mic:e.mic,speaker:e.speaker})})})}),function(){return F.apply(this,arguments)})},{key:"validateCustomTrack",value:function(e,t,r){if(r&&r.length>50)throw Error("Custom track `trackName` must not be more than 50 characters");if(t&&"music"!==t&&"speech"!==t&&!(t instanceof Object))throw Error("Custom track `mode` must be either `music` | `speech` | `DailyMicAudioModeSettings` or `undefined`");if(r&&["cam-audio","cam-video","screen-video","screen-audio","rmpAudio","rmpVideo","customVideoDefaults"].includes(r))throw Error("Custom track `trackName` must not match a track name already used by daily: cam-audio, cam-video, customVideoDefaults, screen-video, screen-audio, rmpAudio, rmpVideo");if(!(e instanceof MediaStreamTrack))throw Error("Custom tracks provided must be instances of MediaStreamTrack")}},{key:"startCustomTrack",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{track:track,mode:mode,trackName:trackName};return sr(),a5(this._callState,"startCustomTrack()"),this.validateCustomTrack(t.track,t.mode,t.trackName),new Promise(function(r,n){e._sharedTracks.customTrack=t.track,t.track=ix,e.sendMessageToCallMachine({action:"start-custom-track",properties:t},function(e){e.error?n({error:e.error}):r(e.mediaTag)})})}},{key:"stopCustomTrack",value:function(e){var t=this;return sr(),a5(this._callState,"stopCustomTrack()"),new Promise(function(r){t.sendMessageToCallMachine({action:"stop-custom-track",mediaTag:e},function(e){r(e.mediaTag)})})}},{key:"setCamera",value:function(e){var t=this;return sn(),a9(this._callMachineInitialized,"setCamera()"),new Promise(function(r){t.sendMessageToCallMachine({action:"set-camera",cameraDeviceId:e},function(e){r({device:e.device})})})}},{key:"setAudioDevice",value:(R=k(function*(e){return sn(),this.nativeUtils().setAudioDevice(e),{deviceId:yield this.nativeUtils().getAudioDevice()}}),function(e){return R.apply(this,arguments)})},{key:"cycleCamera",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new Promise(function(r){e.sendMessageToCallMachine({action:"cycle-camera",properties:t},function(e){r({device:e.device})})})}},{key:"cycleMic",value:function(){var e=this;return sr(),new Promise(function(t){e.sendMessageToCallMachine({action:"cycle-mic"},function(e){t({device:e.device})})})}},{key:"getCameraFacingMode",value:function(){var e=this;return sn(),new Promise(function(t){e.sendMessageToCallMachine({action:"get-camera-facing-mode"},function(e){t(e.facingMode)})})}},{key:"setInputDevicesAsync",value:(D=k(function*(e){var t=this,r=e.audioDeviceId,n=e.videoDeviceId,i=e.audioSource,a=e.videoSource;if(sr(),void 0!==i&&(r=i),void 0!==a&&(n=a),"boolean"==typeof r&&(this._setAllowLocalAudio(r),r=void 0),"boolean"==typeof n&&(this._setAllowLocalVideo(n),n=void 0),!r&&!n)return yield this.getInputDevices();var s={};return r&&(r instanceof MediaStreamTrack?(this._sharedTracks.audioTrack=r,s.audio={settings:{customTrack:r=ix}}):(delete this._sharedTracks.audioTrack,s.audio={settings:{deviceId:r}})),n&&(n instanceof MediaStreamTrack?(this._sharedTracks.videoTrack=n,s.video={settings:{customTrack:n=ix}}):(delete this._sharedTracks.videoTrack,s.video={settings:{deviceId:n}})),this._callObjectMode&&this.needsLoad()?(this._updatePreloadCacheInputSettings(s,!1),this._devicesFromInputSettings(this._inputSettings)):new Promise(function(e){t.sendMessageToCallMachine({action:"set-input-devices",audioDeviceId:r,videoDeviceId:n},function(r){if(delete r.action,delete r.callbackStamp,r.returnPreloadCache)return t._updatePreloadCacheInputSettings(s,!1),void e(t._devicesFromInputSettings(t._inputSettings));e(r)})})}),function(e){return D.apply(this,arguments)})},{key:"setOutputDeviceAsync",value:(L=k(function*(e){var t=this,r=e.outputDeviceId;return sr(),r&&(this._preloadCache.outputDeviceId=r),this._callObjectMode&&this.needsLoad()?this._devicesFromInputSettings(this._inputSettings):new Promise(function(e){t.sendMessageToCallMachine({action:"set-output-device",outputDeviceId:r},function(r){delete r.action,delete r.callbackStamp,r.returnPreloadCache?e(t._devicesFromInputSettings(t._inputSettings)):e(r)})})}),function(e){return L.apply(this,arguments)})},{key:"getInputDevices",value:(N=k(function*(){var e=this;return this._callObjectMode&&this.needsLoad()?this._devicesFromInputSettings(this._inputSettings):new Promise(function(t){e.sendMessageToCallMachine({action:"get-input-devices"},function(r){r.returnPreloadCache?t(e._devicesFromInputSettings(e._inputSettings)):t({camera:r.camera,mic:r.mic,speaker:r.speaker})})})}),function(){return N.apply(this,arguments)})},{key:"nativeInCallAudioMode",value:function(){return sn(),this._nativeInCallAudioMode}},{key:"setNativeInCallAudioMode",value:function(e){if(sn(),[aY,"voice"].includes(e)){if(e!==this._nativeInCallAudioMode)return this._nativeInCallAudioMode=e,!this.disableReactNativeAutoDeviceManagement("audio")&&a8(this._callState,this._isPreparingToJoin)&&this.nativeUtils().setAudioMode(this._nativeInCallAudioMode),this}else console.error("invalid in-call audio mode specified: ",e)}},{key:"preAuth",value:(I=k(function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(se(this._callObjectMode,"preAuth()"),a6(this._callState,this._isPreparingToJoin,"preAuth()"),this.needsLoad()&&(yield this.load(t)),!t.url)throw Error("preAuth() requires at least a url to be provided");return this.validateProperties(t),this.properties=aU(aU({},this.properties),t),new Promise(function(t,r){e._preloadCache.inputSettings=e._prepInputSettingsForSharing(e._inputSettings,!1),e.sendMessageToCallMachine({action:"daily-method-preauth",properties:a4(e.properties,e.callClientId),preloadCache:a4(e._preloadCache,e.callClientId)},function(n){return n.error?r(n.error):n.access?(e._didPreAuth=!0,void t({access:n.access})):r(Error("unknown error in preAuth()"))})})}),function(){return I.apply(this,arguments)})},{key:"load",value:(P=k(function*(e){var t=this;if(this.needsLoad()){if(this._destroyed&&(this._logUseAfterDestroy(),this.strictMode))throw Error("Use after destroy");if(e&&(this.validateProperties(e),this.properties=aU(aU({},this.properties),e)),!this._callObjectMode&&!this.properties.url)throw Error("can't load iframe meeting because url property isn't set");return this._updateCallState(r3),this.emitDailyJSEvent({action:nP}),this._callObjectMode?new Promise(function(e,r){t._callObjectLoader.cancel();var n=Date.now();t._callObjectLoader.load(t.properties.dailyConfig,function(r){t._bundleLoadTime=r?"no-op":Date.now()-n,t._updateCallState(r4),r&&t.emitDailyJSEvent({action:nN}),e()},function(e,n){if(t.emitDailyJSEvent({action:nI}),!n){t._updateCallState(r9),t.resetMeetingDependentVars();var i={action:iE,errorMsg:e.msg,error:{type:"connection-error",msg:"Failed to load call object bundle.",details:{on:"load",sourceError:e,bundleUrl:eu(t.properties.dailyConfig)}}};t._maybeSendToSentry(i),t.emitDailyJSEvent(i),r(e.msg)}})}):(this._iframe.src=ec(this.assembleMeetingUrl(),this.properties.dailyConfig),new Promise(function(e,r){t._loadedCallback=function(n){t._callState!==r9?(t._updateCallState(r4),(t.properties.cssFile||t.properties.cssText)&&t.loadCss(t.properties),e()):r(n)}}))}}),function(e){return P.apply(this,arguments)})},{key:"join",value:(j=k(function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this._testCallInProgress&&this.stopTestCallQuality();var r=!1;if(this.needsLoad()){this.updateIsPreparingToJoin(!0);try{yield this.load(t)}catch(e){return this.updateIsPreparingToJoin(!1),Promise.reject(e)}}else{if(r=!(!this.properties.cssFile&&!this.properties.cssText),this._didPreAuth){if(t.url&&t.url!==this.properties.url)return console.error("url in join() is different than the one used in preAuth()"),this.updateIsPreparingToJoin(!1),Promise.reject();if(t.token&&t.token!==this.properties.token)return console.error("token in join() is different than the one used in preAuth()"),this.updateIsPreparingToJoin(!1),Promise.reject()}if(t.url&&!this._callObjectMode&&t.url&&t.url!==this.properties.url)return console.error("url in join() is different than the one used in load() (".concat(this.properties.url," -> ").concat(t.url,")")),this.updateIsPreparingToJoin(!1),Promise.reject();this.validateProperties(t),this.properties=aU(aU({},this.properties),t)}return void 0!==t.showLocalVideo&&(this._callObjectMode?console.error("showLocalVideo is not available in callObject mode"):this._showLocalVideo=!!t.showLocalVideo),void 0!==t.showParticipantsBar&&(this._callObjectMode?console.error("showParticipantsBar is not available in callObject mode"):this._showParticipantsBar=!!t.showParticipantsBar),this._callState===r8||this._callState===r5?(console.warn("already joined meeting, call leave() before joining again"),void this.updateIsPreparingToJoin(!1)):(this._updateCallState(r5,!1),this.emitDailyJSEvent({action:nR}),this._preloadCache.inputSettings=this._prepInputSettingsForSharing(this._inputSettings||{},!1),this.sendMessageToCallMachine({action:"join-meeting",properties:a4(this.properties,this.callClientId),preloadCache:a4(this._preloadCache,this.callClientId)}),new Promise(function(t,n){e._joinedCallback=function(i,a){if(e._callState!==r9){if(e._updateCallState(r8),i)for(var s in i){if(e._callObjectMode){var o=e._callMachine().store;ag(i[s],o),av(i[s],o),ay(i[s],e._participants[s],o)}e._participants[s]=aU({},i[s]),e.toggleParticipantAudioBasedOnNativeAudioFocus()}r&&e.loadCss(e.properties),t(i)}else n(a)}}))}),function(){return j.apply(this,arguments)})},{key:"leave",value:(O=k(function*(){var e=this;return this._testCallInProgress&&this.stopTestCallQuality(),new Promise(function(t){e._callState===r6||e._callState===r9?t():e._callObjectLoader&&!e._callObjectLoader.loaded?(e._callObjectLoader.cancel(),e._updateCallState(r6),e.resetMeetingDependentVars(),e.emitDailyJSEvent({action:r6}),t()):(e._resolveLeave=t,e.sendMessageToCallMachine({action:"leave-meeting"}))})}),function(){return O.apply(this,arguments)})},{key:"startScreenShare",value:(x=k(function*(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(a9(this._callMachineInitialized,"startScreenShare()"),t.screenVideoSendSettings&&this._validateVideoSendSettings("screenVideo",t.screenVideoSendSettings),t.mediaStream&&(this._sharedTracks.screenMediaStream=t.mediaStream,t.mediaStream=ix),"undefined"!=typeof DailyNativeUtils&&void 0!==DailyNativeUtils.isIOS&&DailyNativeUtils.isIOS){var r=this.nativeUtils();if(yield r.isScreenBeingCaptured())return void this.emitDailyJSEvent({action:ik,type:"screen-share-error",errorMsg:"Could not start the screen sharing. The screen is already been captured!"});r.setSystemScreenCaptureStartCallback(function(){r.setSystemScreenCaptureStartCallback(null),e.sendMessageToCallMachine({action:iA,captureOptions:t})}),r.presentSystemScreenCapturePrompt()}else this.sendMessageToCallMachine({action:iA,captureOptions:t})}),function(){return x.apply(this,arguments)})},{key:"stopScreenShare",value:function(){a9(this._callMachineInitialized,"stopScreenShare()"),this.sendMessageToCallMachine({action:"local-screen-stop"})}},{key:"startRecording",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=e.type;if(t&&"cloud"!==t&&"raw-tracks"!==t&&"local"!==t)throw Error("invalid type: ".concat(t,", allowed values 'cloud', 'raw-tracks', or 'local'"));this.sendMessageToCallMachine(aU({action:"local-recording-start"},e))}},{key:"updateRecording",value:function(e){var t=e.layout,r=e.instanceId;this.sendMessageToCallMachine({action:"daily-method-update-recording",layout:void 0===t?{preset:"default"}:t,instanceId:r})}},{key:"stopRecording",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.sendMessageToCallMachine(aU({action:"local-recording-stop"},e))}},{key:"startLiveStreaming",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.sendMessageToCallMachine(aU({action:"daily-method-start-live-streaming"},e))}},{key:"updateLiveStreaming",value:function(e){var t=e.layout,r=e.instanceId;this.sendMessageToCallMachine({action:"daily-method-update-live-streaming",layout:void 0===t?{preset:"default"}:t,instanceId:r})}},{key:"addLiveStreamingEndpoints",value:function(e){var t=e.endpoints,r=e.instanceId;this.sendMessageToCallMachine({action:iT,endpointsOp:"add-endpoints",endpoints:t,instanceId:r})}},{key:"removeLiveStreamingEndpoints",value:function(e){var t=e.endpoints,r=e.instanceId;this.sendMessageToCallMachine({action:iT,endpointsOp:"remove-endpoints",endpoints:t,instanceId:r})}},{key:"stopLiveStreaming",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.sendMessageToCallMachine(aU({action:"daily-method-stop-live-streaming"},e))}},{key:"validateDailyConfig",value:function(e){e.camSimulcastEncodings&&(console.warn("camSimulcastEncodings is deprecated. Use sendSettings, found in DailyCallOptions, to provide camera simulcast settings."),this.validateSimulcastEncodings(e.camSimulcastEncodings)),e.screenSimulcastEncodings&&console.warn("screenSimulcastEncodings is deprecated. Use sendSettings, found in DailyCallOptions, to provide screen simulcast settings."),iJ()&&e.noAutoDefaultDeviceChange&&console.warn("noAutoDefaultDeviceChange is not supported on Android, and will be ignored.")}},{key:"validateSimulcastEncodings",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(e){if(!(e instanceof Array||Array.isArray(e)))throw Error("encodings must be an Array");if(!sg(e.length,1,3))throw Error("encodings must be an Array with between 1 to ".concat(3," layers"));for(var n=0;n<e.length;n++){var i=e[n];for(var a in this._validateEncodingLayerHasValidProperties(i),i)if(aH.includes(a)){if("number"!=typeof i[a])throw Error("".concat(a," must be a number"));if(t){var s=t[a],o=s.min,l=s.max;if(!sg(i[a],o,l))throw Error("".concat(a," value not in range. valid range: ").concat(o," to ").concat(l))}}else if(!["active","scalabilityMode"].includes(a))throw Error("Invalid key ".concat(a,", valid keys are:")+Object.values(aH));if(r&&!i.hasOwnProperty("maxBitrate"))throw Error("maxBitrate is not specified")}}}},{key:"startRemoteMediaPlayer",value:(M=k(function*(e){var t=this,r=e.url,n=e.settings,i=void 0===n?{state:iP.PLAY}:n;try{(function(e){if("string"!=typeof e)throw Error('url parameter must be "string" type')})(r),sm(i),function(e){for(var t in e)if(!aK.includes(t))throw Error("Invalid key ".concat(t,", valid keys are: ").concat(aK));e.simulcastEncodings&&this.validateSimulcastEncodings(e.simulcastEncodings,aQ,!0)}(i)}catch(e){throw console.error("invalid argument Error: ".concat(e)),console.error('startRemoteMediaPlayer arguments must be of the form:\n  { url: "playback url",\n  settings?:\n  {state: "play"|"pause", simulcastEncodings?: [{}] } }'),e}return new Promise(function(e,n){t.sendMessageToCallMachine({action:"daily-method-start-remote-media-player",url:r,settings:i},function(t){t.error?n({error:t.error,errorMsg:t.errorMsg}):e({session_id:t.session_id,remoteMediaPlayerState:{state:t.state,settings:t.settings}})})})}),function(e){return M.apply(this,arguments)})},{key:"stopRemoteMediaPlayer",value:(T=k(function*(e){var t=this;if("string"!=typeof e)throw Error(" remotePlayerID must be of type string");return new Promise(function(r,n){t.sendMessageToCallMachine({action:"daily-method-stop-remote-media-player",session_id:e},function(e){e.error?n({error:e.error,errorMsg:e.errorMsg}):r()})})}),function(e){return T.apply(this,arguments)})},{key:"updateRemoteMediaPlayer",value:(A=k(function*(e){var t=this,r=e.session_id,n=e.settings;try{sm(n)}catch(e){throw console.error("invalid argument Error: ".concat(e)),console.error('updateRemoteMediaPlayer arguments must be of the form:\n  session_id: "participant session",\n  { settings?: {state: "play"|"pause"} }'),e}return new Promise(function(e,i){t.sendMessageToCallMachine({action:"daily-method-update-remote-media-player",session_id:r,settings:n},function(t){t.error?i({error:t.error,errorMsg:t.errorMsg}):e({session_id:t.session_id,remoteMediaPlayerState:{state:t.state,settings:t.settings}})})})}),function(e){return A.apply(this,arguments)})},{key:"startTranscription",value:function(e){a5(this._callState,"startTranscription()"),this.sendMessageToCallMachine(aU({action:"daily-method-start-transcription"},e))}},{key:"updateTranscription",value:function(e){if(a5(this._callState,"updateTranscription()"),!e)throw Error("updateTranscription Error: options is mandatory");if("object"!==p(e))throw Error("updateTranscription Error: options must be object type");if(e.participants&&!Array.isArray(e.participants))throw Error("updateTranscription Error: participants must be an array");this.sendMessageToCallMachine(aU({action:"daily-method-update-transcription"},e))}},{key:"stopTranscription",value:function(e){if(a5(this._callState,"stopTranscription()"),e&&"object"!==p(e))throw Error("stopTranscription Error: options must be object type");if(e&&!e.instanceId)throw Error('"instanceId" not provided');this.sendMessageToCallMachine(aU({action:"daily-method-stop-transcription"},e))}},{key:"startDialOut",value:(E=k(function*(e){var t=this;a5(this._callState,"startDialOut()");var r=function(e){if(e){if(!Array.isArray(e))throw Error("Error starting dial out: audio codec must be an array");if(e.length<=0)throw Error("Error starting dial out: audio codec array specified but empty");e.forEach(function(e){if("string"!=typeof e)throw Error("Error starting dial out: audio codec must be a string");if("OPUS"!==e&&"PCMU"!==e&&"PCMA"!==e&&"G722"!==e)throw Error("Error starting dial out: audio codec must be one of OPUS, PCMU, PCMA, G722")})}};if(!e.sipUri&&!e.phoneNumber)throw Error("Error starting dial out: either a sip uri or phone number must be provided");if(e.sipUri&&e.phoneNumber)throw Error("Error starting dial out: only one of sip uri or phone number must be provided");if(e.sipUri){if("string"!=typeof e.sipUri)throw Error("Error starting dial out: sipUri must be a string");if(!e.sipUri.startsWith("sip:"))throw Error("Error starting dial out: Invalid SIP URI, must start with 'sip:'");if(e.video&&"boolean"!=typeof e.video)throw Error("Error starting dial out: video must be a boolean value");!function(e){if(e&&(r(e.audio),e.video)){if(!Array.isArray(e.video))throw Error("Error starting dial out: video codec must be an array");if(e.video.length<=0)throw Error("Error starting dial out: video codec array specified but empty");e.video.forEach(function(e){if("string"!=typeof e)throw Error("Error starting dial out: video codec must be a string");if("H264"!==e&&"VP8"!==e)throw Error("Error starting dial out: video codec must be H264 or VP8")})}}(e.codecs)}if(e.phoneNumber){if("string"!=typeof e.phoneNumber)throw Error("Error starting dial out: phoneNumber must be a string");if(!/^\+\d{1,}$/.test(e.phoneNumber))throw Error("Error starting dial out: Invalid phone number, must be valid phone number as per E.164");e.codecs&&r(e.codecs.audio)}if(e.callerId){if("string"!=typeof e.callerId)throw Error("Error starting dial out: callerId must be a string");if(e.sipUri)throw Error("Error starting dial out: callerId not allowed with sipUri")}if(e.displayName){if("string"!=typeof e.displayName)throw Error("Error starting dial out: displayName must be a string");if(e.displayName.length>=200)throw Error("Error starting dial out: displayName length must be less than 200")}if(e.userId){if("string"!=typeof e.userId)throw Error("Error starting dial out: userId must be a string");if(e.userId.length>36)throw Error("Error starting dial out: userId length must be less than or equal to 36")}return new Promise(function(r,n){t.sendMessageToCallMachine(aU({action:"dialout-start"},e),function(e){e.error?n(e.error):r(e)})})}),function(e){return E.apply(this,arguments)})},{key:"stopDialOut",value:function(e){var t=this;return a5(this._callState,"stopDialOut()"),new Promise(function(r,n){t.sendMessageToCallMachine(aU({action:"dialout-stop"},e),function(e){e.error?n(e.error):r(e)})})}},{key:"sipCallTransfer",value:(w=k(function*(e){var t=this;if(a5(this._callState,"sipCallTransfer()"),!e)throw Error("sipCallTransfer() requires a sessionId and toEndPoint");return e.useSipRefer=!1,sf(e,"sipCallTransfer"),new Promise(function(r,n){t.sendMessageToCallMachine(aU({action:iN},e),function(e){e.error?n(e.error):r(e)})})}),function(e){return w.apply(this,arguments)})},{key:"sipRefer",value:(b=k(function*(e){var t=this;if(a5(this._callState,"sipRefer()"),!e)throw Error("sessionId and toEndPoint are mandatory parameter");return e.useSipRefer=!0,sf(e,"sipRefer"),new Promise(function(r,n){t.sendMessageToCallMachine(aU({action:iN},e),function(e){e.error?n(e.error):r(e)})})}),function(e){return b.apply(this,arguments)})},{key:"sendDTMF",value:(m=k(function*(e){var t=this;return a5(this._callState,"sendDTMF()"),function(e){var t=e.sessionId,r=e.tones;if(!t||!r)throw Error("sessionId and tones are mandatory parameter");if("string"!=typeof t||"string"!=typeof r)throw Error("sessionId and tones should be of string type");if(r.length>20)throw Error("tones string must be upto 20 characters");var n=r.match(/[^0-9A-D*#]/g);if(n&&n[0])throw Error("".concat(n[0]," is not valid DTMF tone"))}(e),new Promise(function(r,n){t.sendMessageToCallMachine(aU({action:"send-dtmf"},e),function(e){e.error?n(e.error):r(e)})})}),function(e){return m.apply(this,arguments)})},{key:"getNetworkStats",value:function(){var e=this;return this._callState!==r8?Promise.resolve(aU({stats:{latest:{}}},this._network)):new Promise(function(t){e.sendMessageToCallMachine({action:"get-calc-stats"},function(r){t(aU(aU({},e._network),{},{stats:r.stats}))})})}},{key:"testWebsocketConnectivity",value:(f=k(function*(){var e=this;if(a7(this._testCallInProgress,"testWebsocketConnectivity()"),this.needsLoad())try{yield this.load()}catch(e){return Promise.reject(e)}return new Promise(function(t,r){e.sendMessageToCallMachine({action:"test-websocket-connectivity"},function(e){e.error?r(e.error):t(e.results)})})}),function(){return f.apply(this,arguments)})},{key:"abortTestWebsocketConnectivity",value:function(){this.sendMessageToCallMachine({action:"abort-test-websocket-connectivity"})}},{key:"_validateVideoTrackForNetworkTests",value:function(e){return e?e instanceof MediaStreamTrack?!!(e&&"live"===e.readyState&&(!e.muted||aA.has(e.id)))||(console.error("Video track is not playable. This test needs a live video track."),!1):(console.error("Video track needs to be of type `MediaStreamTrack`."),!1):(console.error("Missing video track. You must provide a video track in order to run this test."),!1)}},{key:"testCallQuality",value:(u=k(function*(){var e=this;sr(),se(this._callObjectMode,"testCallQuality()"),a9(this._callMachineInitialized,"testCallQuality()",null,!0),a6(this._callState,this._isPreparingToJoin,"testCallQuality()");var t=this._testCallAlreadyInProgress,r=function(r){t||(e._testCallInProgress=r)};if(r(!0),this.needsLoad())try{var n=this._callState;yield this.load(),this._callState=n}catch(e){return r(!1),Promise.reject(e)}return new Promise(function(t){e.sendMessageToCallMachine({action:"test-call-quality",dailyJsVersion:e.properties.dailyJsVersion},function(n){var i=n.results,a=i.result,s=d(i,aF);if("failed"===a){var o,l=aU({},s);null!==(o=s.error)&&void 0!==o&&o.details?(s.error.details=JSON.parse(s.error.details),l.error=aU(aU({},l.error),{},{details:aU({},l.error.details)}),l.error.details.duringTest="testCallQuality"):(l.error=l.error?aU({},l.error):{},l.error.details={duringTest:"testCallQuality"}),e._maybeSendToSentry(l)}r(!1),t(aU({result:a},s))})})}),function(){return u.apply(this,arguments)})},{key:"stopTestCallQuality",value:function(){this.sendMessageToCallMachine({action:"stop-test-call-quality"})}},{key:"testConnectionQuality",value:(c=k(function*(e){iD()?(console.warn("testConnectionQuality() is deprecated: use testPeerToPeerCallQuality() instead"),t=yield this.testPeerToPeerCallQuality(e)):(console.warn("testConnectionQuality() is deprecated: use testCallQuality() instead"),t=yield this.testCallQuality());var t,r={result:t.result,secondsElapsed:t.secondsElapsed};return t.data&&(r.data={maxRTT:t.data.maxRoundTripTime,packetLoss:t.data.avgRecvPacketLoss}),r}),function(e){return c.apply(this,arguments)})},{key:"testPeerToPeerCallQuality",value:(l=k(function*(e){var t=this;if(a7(this._testCallInProgress,"testPeerToPeerCallQuality()"),this.needsLoad())try{yield this.load()}catch(e){return Promise.reject(e)}var r=e.videoTrack,n=e.duration;if(!this._validateVideoTrackForNetworkTests(r))throw Error("Video track error");return this._sharedTracks.videoTrackForConnectionQualityTest=r,new Promise(function(e,r){t.sendMessageToCallMachine({action:"test-p2p-call-quality",duration:n},function(t){t.error?r(t.error):e(t.results)})})}),function(e){return l.apply(this,arguments)})},{key:"stopTestConnectionQuality",value:function(){iD()?(console.warn("stopTestConnectionQuality() is deprecated: use testPeerToPeerCallQuality() and stopTestPeerToPeerCallQuality() instead"),this.stopTestPeerToPeerCallQuality()):(console.warn("stopTestConnectionQuality() is deprecated: use testCallQuality() and stopTestCallQuality() instead"),this.stopTestCallQuality())}},{key:"stopTestPeerToPeerCallQuality",value:function(){this.sendMessageToCallMachine({action:"stop-test-p2p-call-quality"})}},{key:"testNetworkConnectivity",value:(o=k(function*(e){var t=this;if(a7(this._testCallInProgress,"testNetworkConnectivity()"),this.needsLoad())try{yield this.load()}catch(e){return Promise.reject(e)}if(!this._validateVideoTrackForNetworkTests(e))throw Error("Video track error");return this._sharedTracks.videoTrackForNetworkConnectivityTest=e,new Promise(function(e,r){t.sendMessageToCallMachine({action:"test-network-connectivity"},function(t){t.error?r(t.error):e(t.results)})})}),function(e){return o.apply(this,arguments)})},{key:"abortTestNetworkConnectivity",value:function(){this.sendMessageToCallMachine({action:"abort-test-network-connectivity"})}},{key:"getCpuLoadStats",value:function(){var e=this;return new Promise(function(t){e._callState===r8?e.sendMessageToCallMachine({action:"get-cpu-load-stats"},function(e){t(e.cpuStats)}):t({cpuLoadState:void 0,cpuLoadStateReason:void 0,stats:{}})})}},{key:"_validateEncodingLayerHasValidProperties",value:function(e){var t;if(!((null===(t=Object.keys(e))||void 0===t?void 0:t.length)>0))throw Error("Empty encoding is not allowed. At least one of these valid keys should be specified:"+Object.values(aH))}},{key:"_validateVideoSendSettings",value:function(e,t){var r="screenVideo"===e?["default-screen-video","detail-optimized","motion-optimized","motion-and-detail-balanced"]:["default-video","bandwidth-optimized","bandwidth-and-quality-balanced","quality-optimized","adaptive-2-layers","adaptive-3-layers"],n="Video send settings should be either an object or one of the supported presets: ".concat(r.join());if("string"==typeof t){if(!r.includes(t))throw Error(n)}else{if("object"!==p(t))throw Error(n);if(!t.maxQuality&&!t.encodings&&void 0===t.allowAdaptiveLayers)throw Error("Video send settings must contain at least maxQuality, allowAdaptiveLayers or encodings attribute");if(t.maxQuality&&-1===["low","medium","high"].indexOf(t.maxQuality))throw Error("maxQuality must be either low, medium or high");if(t.encodings){var i=!1;switch(Object.keys(t.encodings).length){case 1:i=!t.encodings.low;break;case 2:i=!t.encodings.low||!t.encodings.medium;break;case 3:i=!t.encodings.low||!t.encodings.medium||!t.encodings.high;break;default:i=!0}if(i)throw Error("Encodings must be defined as: low, low and medium, or low, medium and high.");t.encodings.low&&this._validateEncodingLayerHasValidProperties(t.encodings.low),t.encodings.medium&&this._validateEncodingLayerHasValidProperties(t.encodings.medium),t.encodings.high&&this._validateEncodingLayerHasValidProperties(t.encodings.high)}}}},{key:"validateUpdateSendSettings",value:function(e){var t=this;if(!e||0===Object.keys(e).length)throw Error("Send settings must contain at least information for one track!");Object.entries(e).forEach(function(e){var r=C(e,2),n=r[0],i=r[1];t._validateVideoSendSettings(n,i)})}},{key:"updateSendSettings",value:function(e){var t=this;return this.validateUpdateSendSettings(e),this.needsLoad()?(this._preloadCache.sendSettings=e,{sendSettings:this._preloadCache.sendSettings}):new Promise(function(r,n){t.sendMessageToCallMachine({action:"update-send-settings",sendSettings:e},function(e){e.error?n(e.error):r(e.sendSettings)})})}},{key:"getSendSettings",value:function(){return this._sendSettings||this._preloadCache.sendSettings}},{key:"getLocalAudioLevel",value:function(){return this._localAudioLevel}},{key:"getRemoteParticipantsAudioLevel",value:function(){return this._remoteParticipantsAudioLevel}},{key:"getActiveSpeaker",value:function(){return sr(),this._activeSpeaker}},{key:"setActiveSpeakerMode",value:function(e){return sr(),this.sendMessageToCallMachine({action:"set-active-speaker-mode",enabled:e}),this}},{key:"activeSpeakerMode",value:function(){return sr(),this._activeSpeakerMode}},{key:"subscribeToTracksAutomatically",value:function(){return this._preloadCache.subscribeToTracksAutomatically}},{key:"setSubscribeToTracksAutomatically",value:function(e){return a5(this._callState,"setSubscribeToTracksAutomatically()","Use the subscribeToTracksAutomatically configuration property."),this._preloadCache.subscribeToTracksAutomatically=e,this.sendMessageToCallMachine({action:"daily-method-subscribe-to-tracks-automatically",enabled:e}),this}},{key:"enumerateDevices",value:(s=k(function*(){var e=this;if(this._callObjectMode){var t=yield navigator.mediaDevices.enumerateDevices();return"Firefox"===iY()&&iz().major>115&&iz().major<123&&(t=t.filter(function(e){return"audiooutput"!==e.kind})),{devices:t.map(function(e){var t=JSON.parse(JSON.stringify(e));if(!iD()&&"videoinput"===e.kind&&e.getCapabilities){var r,n=e.getCapabilities();t.facing=(null==n||null===(r=n.facingMode)||void 0===r?void 0:r.length)>=1?n.facingMode[0]:void 0}return t})}}return new Promise(function(t){e.sendMessageToCallMachine({action:"enumerate-devices"},function(e){t({devices:e.devices})})})}),function(){return s.apply(this,arguments)})},{key:"sendAppMessage",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"*";if(a5(this._callState,"sendAppMessage()"),JSON.stringify(e).length>this._maxAppMessageSize)throw Error("Message data too large. Max size is "+this._maxAppMessageSize);return this.sendMessageToCallMachine({action:"app-msg",data:e,to:t}),this}},{key:"addFakeParticipant",value:function(e){return sr(),a5(this._callState,"addFakeParticipant()"),this.sendMessageToCallMachine(aU({action:"add-fake-participant"},e)),this}},{key:"setShowNamesMode",value:function(e){return st(this._callObjectMode,"setShowNamesMode()"),sr(),e&&"always"!==e&&"never"!==e?console.error('setShowNamesMode argument should be "always", "never", or false'):this.sendMessageToCallMachine({action:"set-show-names",mode:e}),this}},{key:"setShowLocalVideo",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return st(this._callObjectMode,"setShowLocalVideo()"),sr(),a5(this._callState,"setShowLocalVideo()"),"boolean"!=typeof e?console.error("setShowLocalVideo only accepts a boolean value"):(this.sendMessageToCallMachine({action:"set-show-local-video",show:e}),this._showLocalVideo=e),this}},{key:"showLocalVideo",value:function(){return st(this._callObjectMode,"showLocalVideo()"),sr(),this._showLocalVideo}},{key:"setShowParticipantsBar",value:function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];return st(this._callObjectMode,"setShowParticipantsBar()"),sr(),a5(this._callState,"setShowParticipantsBar()"),"boolean"!=typeof e?console.error("setShowParticipantsBar only accepts a boolean value"):(this.sendMessageToCallMachine({action:"set-show-participants-bar",show:e}),this._showParticipantsBar=e),this}},{key:"showParticipantsBar",value:function(){return st(this._callObjectMode,"showParticipantsBar()"),sr(),this._showParticipantsBar}},{key:"customIntegrations",value:function(){return sr(),st(this._callObjectMode,"customIntegrations()"),this._customIntegrations}},{key:"setCustomIntegrations",value:function(e){return sr(),st(this._callObjectMode,"setCustomIntegrations()"),a5(this._callState,"setCustomIntegrations()"),sp(e)&&(this.sendMessageToCallMachine({action:"set-custom-integrations",integrations:e}),this._customIntegrations=e),this}},{key:"startCustomIntegrations",value:function(e){var t=this;if(sr(),st(this._callObjectMode,"startCustomIntegrations()"),a5(this._callState,"startCustomIntegrations()"),Array.isArray(e)&&e.some(function(e){return"string"!=typeof e})||!Array.isArray(e)&&"string"!=typeof e)return console.error("startCustomIntegrations() only accepts string | string[]"),this;var r="string"==typeof e?[e]:e,n=r.filter(function(e){return!(e in t._customIntegrations)});return n.length?console.error("Can't find custom integration(s): \"".concat(n.join(", "),'"')):this.sendMessageToCallMachine({action:"start-custom-integrations",ids:r}),this}},{key:"stopCustomIntegrations",value:function(e){var t=this;if(sr(),st(this._callObjectMode,"stopCustomIntegrations()"),a5(this._callState,"stopCustomIntegrations()"),Array.isArray(e)&&e.some(function(e){return"string"!=typeof e})||!Array.isArray(e)&&"string"!=typeof e)return console.error("stopCustomIntegrations() only accepts string | string[]"),this;var r="string"==typeof e?[e]:e,n=r.filter(function(e){return!(e in t._customIntegrations)});return n.length?console.error("Can't find custom integration(s): \"".concat(n.join(", "),'"')):this.sendMessageToCallMachine({action:"stop-custom-integrations",ids:r}),this}},{key:"customTrayButtons",value:function(){return st(this._callObjectMode,"customTrayButtons()"),sr(),this._customTrayButtons}},{key:"updateCustomTrayButtons",value:function(e){return st(this._callObjectMode,"updateCustomTrayButtons()"),sr(),a5(this._callState,"updateCustomTrayButtons()"),sh(e)?(this.sendMessageToCallMachine({action:"update-custom-tray-buttons",btns:e}),this._customTrayButtons=e):console.error("updateCustomTrayButtons only accepts a dictionary of the type ".concat(JSON.stringify(aZ))),this}},{key:"theme",value:function(){return st(this._callObjectMode,"theme()"),this.properties.theme}},{key:"setTheme",value:function(e){var t=this;return st(this._callObjectMode,"setTheme()"),new Promise(function(r,n){try{t.validateProperties({theme:e}),t.properties.theme=aU({},e),t.sendMessageToCallMachine({action:"set-theme",theme:t.properties.theme});try{t.emitDailyJSEvent({action:nj,theme:t.properties.theme})}catch(e){console.log("could not emit 'theme-updated'",e)}r(t.properties.theme)}catch(e){n(e)}})}},{key:"requestFullscreen",value:(a=k(function*(){if(sr(),this._iframe&&!document.fullscreenElement&&iF())try{(yield this._iframe.requestFullscreen)?this._iframe.requestFullscreen():this._iframe.webkitRequestFullscreen()}catch(e){console.log("could not make video call fullscreen",e)}}),function(){return a.apply(this,arguments)})},{key:"exitFullscreen",value:function(){sr(),document.fullscreenElement?document.exitFullscreen():document.webkitFullscreenElement&&document.webkitExitFullscreen()}},{key:"getSidebarView",value:(i=k(function*(){var e=this;return this._callObjectMode?(console.error("getSidebarView is not available in callObject mode"),Promise.resolve(null)):new Promise(function(t){e.sendMessageToCallMachine({action:"get-sidebar-view"},function(e){t(e.view)})})}),function(){return i.apply(this,arguments)})},{key:"setSidebarView",value:function(e){return this._callObjectMode?console.error("setSidebarView is not available in callObject mode"):this.sendMessageToCallMachine({action:"set-sidebar-view",view:e}),this}},{key:"room",value:(n=k(function*(){var e=this,t=(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{}).includeRoomConfigDefaults,r=void 0===t||t;return this._accessState.access===na||this.needsLoad()?this.properties.url?{roomUrlPendingJoin:this.properties.url}:null:new Promise(function(t){e.sendMessageToCallMachine({action:"lib-room-info",includeRoomConfigDefaults:r},function(e){delete e.action,delete e.callbackStamp,t(e)})})}),function(){return n.apply(this,arguments)})},{key:"geo",value:(r=k(function*(){try{var e=yield fetch("https://gs.daily.co/_ks_/x-swsl/:");return{current:(yield e.json()).geo}}catch(e){return console.error("geo lookup failed",e),{current:""}}}),function(){return r.apply(this,arguments)})},{key:"setNetworkTopology",value:(t=k(function*(e){var t=this;return sr(),a5(this._callState,"setNetworkTopology()"),new Promise(function(r,n){t.sendMessageToCallMachine({action:"set-network-topology",opts:e},function(e){e.error?n({error:e.error}):r({workerId:e.workerId})})})}),function(e){return t.apply(this,arguments)})},{key:"getNetworkTopology",value:(e=k(function*(){var e=this;return new Promise(function(t,r){e.needsLoad()&&t({topology:"none"}),e.sendMessageToCallMachine({action:"get-network-topology"},function(e){e.error?r({error:e.error}):t({topology:e.topology})})})}),function(){return e.apply(this,arguments)})},{key:"setPlayNewParticipantSound",value:function(e){if(sr(),"number"!=typeof e&&!0!==e&&!1!==e)throw Error("argument to setShouldPlayNewParticipantSound should be true, false, or a number, but is ".concat(e));this.sendMessageToCallMachine({action:"daily-method-set-play-ding",arg:e})}},{key:"on",value:function(e,t){return J.prototype.on.call(this,e,t)}},{key:"once",value:function(e,t){return J.prototype.once.call(this,e,t)}},{key:"off",value:function(e,t){return J.prototype.off.call(this,e,t)}},{key:"validateProperties",value:function(e){var t,r;if(null!=e&&null!==(t=e.dailyConfig)&&void 0!==t&&t.userMediaAudioConstraints){iD()||console.warn("userMediaAudioConstraints is deprecated. You can override constraints with inputSettings.audio.settings, found in DailyCallOptions.");var n,i,a=e.inputSettings||{};a.audio=(null===(n=e.inputSettings)||void 0===n?void 0:n.audio)||{},a.audio.settings=(null===(i=e.inputSettings)||void 0===i||null===(i=i.audio)||void 0===i?void 0:i.settings)||{},a.audio.settings=aU(aU({},a.audio.settings),e.dailyConfig.userMediaAudioConstraints),e.inputSettings=a,delete e.dailyConfig.userMediaAudioConstraints}if(null!=e&&null!==(r=e.dailyConfig)&&void 0!==r&&r.userMediaVideoConstraints){iD()||console.warn("userMediaVideoConstraints is deprecated. You can override constraints with inputSettings.video.settings, found in DailyCallOptions.");var s,o,l=e.inputSettings||{};l.video=(null===(s=e.inputSettings)||void 0===s?void 0:s.video)||{},l.video.settings=(null===(o=e.inputSettings)||void 0===o||null===(o=o.video)||void 0===o?void 0:o.settings)||{},l.video.settings=aU(aU({},l.video.settings),e.dailyConfig.userMediaVideoConstraints),e.inputSettings=l,delete e.dailyConfig.userMediaVideoConstraints}for(var c in e){if(!a1[c])throw Error("unrecognized property '".concat(c,"'"));if(a1[c].validate&&!a1[c].validate(e[c],this))throw Error("property '".concat(c,"': ").concat(a1[c].help))}}},{key:"assembleMeetingUrl",value:function(){var e,t,r=aU(aU({},this.properties),{},{emb:this.callClientId,embHref:encodeURIComponent(window.location.href),proxy:null!==(e=this.properties.dailyConfig)&&void 0!==e&&e.proxyUrl?encodeURIComponent(null===(t=this.properties.dailyConfig)||void 0===t?void 0:t.proxyUrl):void 0}),n=r.url.match(/\?/)?"&":"?";return r.url+n+Object.keys(a1).filter(function(e){return a1[e].queryString&&void 0!==r[e]}).map(function(e){return"".concat(a1[e].queryString,"=").concat(r[e])}).join("&")}},{key:"needsLoad",value:function(){return[r2,r3,r6,r9].includes(this._callState)}},{key:"sendMessageToCallMachine",value:function(e,t){if(this._destroyed&&(this._logUseAfterDestroy(),this.strictMode))throw Error("Use after destroy");this._messageChannel.sendMessageToCallMachine(e,t,this.callClientId,this._iframe)}},{key:"forwardPackagedMessageToCallMachine",value:function(e){this._messageChannel.forwardPackagedMessageToCallMachine(e,this._iframe,this.callClientId)}},{key:"addListenerForPackagedMessagesFromCallMachine",value:function(e){return this._messageChannel.addListenerForPackagedMessagesFromCallMachine(e,this.callClientId)}},{key:"removeListenerForPackagedMessagesFromCallMachine",value:function(e){this._messageChannel.removeListenerForPackagedMessagesFromCallMachine(e)}},{key:"handleMessageFromCallMachine",value:function(e){switch(e.action){case nx:this.sendMessageToCallMachine(aU({action:nO},this.properties));break;case"call-machine-initialized":this._callMachineInitialized=!0;var t={action:iM,level:"log",code:1011,stats:{event:"bundle load",time:"no-op"===this._bundleLoadTime?0:this._bundleLoadTime,preLoaded:"no-op"===this._bundleLoadTime,url:eu(this.properties.dailyConfig)}};this.sendMessageToCallMachine(t),this._delayDuplicateInstanceLog&&this._logDuplicateInstanceAttempt();break;case nN:this._loadedCallback&&(this._loadedCallback(),this._loadedCallback=null),this.emitDailyJSEvent(e);break;case nF:var r,n=aU({},e);delete n.internal,this._maxAppMessageSize=(null===(r=e.internal)||void 0===r?void 0:r._maxAppMessageSize)||4096,this._joinedCallback&&(this._joinedCallback(e.participants),this._joinedCallback=null),this.emitDailyJSEvent(n);break;case nV:case nU:if(this._callState===r6)return;if(e.participant&&e.participant.session_id){var i=e.participant.local?"local":e.participant.session_id;if(this._callObjectMode){var a=this._callMachine().store;ag(e.participant,a),av(e.participant,a),ay(e.participant,this._participants[i],a)}try{this.maybeParticipantTracksStopped(this._participants[i],e.participant),this.maybeParticipantTracksStarted(this._participants[i],e.participant),this.maybeEventRecordingStopped(this._participants[i],e.participant),this.maybeEventRecordingStarted(this._participants[i],e.participant)}catch(e){console.error("track events error",e)}this.compareEqualForParticipantUpdateEvent(e.participant,this._participants[i])||(this._participants[i]=aU({},e.participant),this.toggleParticipantAudioBasedOnNativeAudioFocus(),this.emitDailyJSEvent(e))}break;case n$:if(e.participant&&e.participant.session_id){var s=this._participants[e.participant.session_id];s&&this.maybeParticipantTracksStopped(s,null),delete this._participants[e.participant.session_id],this.emitDailyJSEvent(e)}break;case nB:z(this._participantCounts,e.participantCounts)||(this._participantCounts=e.participantCounts,this.emitDailyJSEvent(e));break;case nJ:var o={access:e.access};e.awaitingAccess&&(o.awaitingAccess=e.awaitingAccess),z(this._accessState,o)||(this._accessState=o,this.emitDailyJSEvent(e));break;case nG:if(e.meetingSession){this._meetingSessionSummary=e.meetingSession,this.emitDailyJSEvent(e);var l=aU(aU({},e),{},{action:"meeting-session-updated"});this.emitDailyJSEvent(l)}break;case iE:this._iframe&&!e.preserveIframe&&(this._iframe.src=""),this._updateCallState(r9),this.resetMeetingDependentVars(),this._loadedCallback&&(this._loadedCallback(e.errorMsg),this._loadedCallback=null),e.preserveIframe;var c,u=d(e,aq);null!=u&&null!==(c=u.error)&&void 0!==c&&c.details&&(u.error.details=JSON.parse(u.error.details)),this._maybeSendToSentry(e),this._joinedCallback&&(this._joinedCallback(null,u),this._joinedCallback=null),this.emitDailyJSEvent(u);break;case nq:this._callState!==r9&&this._updateCallState(r6),this.resetMeetingDependentVars(),this._resolveLeave&&(this._resolveLeave(),this._resolveLeave=null),this.emitDailyJSEvent(e);break;case"selected-devices-updated":e.devices&&this.emitDailyJSEvent(e);break;case ic:var h=e.state,p=e.threshold,f=e.quality,m=h.state,g=h.reasons;m===this._network.networkState&&z(g,this._network.networkStateReasons)&&p===this._network.threshold&&f===this._network.quality||(this._network.networkState=m,this._network.networkStateReasons=g,this._network.quality=f,this._network.threshold=p,e.networkState=m,g.length&&(e.networkStateReasons=g),delete e.state,this.emitDailyJSEvent(e));break;case id:e&&e.cpuLoadState&&this.emitDailyJSEvent(e);break;case ih:e&&void 0!==e.faceCounts&&this.emitDailyJSEvent(e);break;case io:var v=e.activeSpeaker;this._activeSpeaker.peerId!==v.peerId&&(this._activeSpeaker.peerId=v.peerId,this.emitDailyJSEvent({action:e.action,activeSpeaker:this._activeSpeaker}));break;case"show-local-video-changed":if(this._callObjectMode)return;var y=e.show;this._showLocalVideo=y,this.emitDailyJSEvent({action:e.action,show:y});break;case il:var b=e.enabled;this._activeSpeakerMode!==b&&(this._activeSpeakerMode=b,this.emitDailyJSEvent({action:e.action,enabled:this._activeSpeakerMode}));break;case nW:case nQ:case nH:this._waitingParticipants=e.allWaitingParticipants,this.emitDailyJSEvent({action:e.action,participant:e.participant});break;case iS:z(this._receiveSettings,e.receiveSettings)||(this._receiveSettings=e.receiveSettings,this.emitDailyJSEvent({action:e.action,receiveSettings:e.receiveSettings}));break;case iw:this._maybeUpdateInputSettings(e.inputSettings);break;case"send-settings-updated":z(this._sendSettings,e.sendSettings)||(this._sendSettings=e.sendSettings,this._preloadCache.sendSettings=null,this.emitDailyJSEvent({action:e.action,sendSettings:e.sendSettings}));break;case"local-audio-level":this._localAudioLevel=e.audioLevel,this._preloadCache.localAudioLevelObserver=null,this.emitDailyJSEvent(e);break;case"remote-participants-audio-level":this._remoteParticipantsAudioLevel=e.participantsAudioLevel,this._preloadCache.remoteParticipantsAudioLevelObserver=null,this.emitDailyJSEvent(e);break;case ie:var _=e.session_id;this._rmpPlayerState[_]=e.playerState,this.emitDailyJSEvent(e);break;case ir:delete this._rmpPlayerState[e.session_id],this.emitDailyJSEvent(e);break;case it:var S=e.session_id,w=this._rmpPlayerState[S];w&&this.compareEqualForRMPUpdateEvent(w,e.remoteMediaPlayerState)||(this._rmpPlayerState[S]=e.remoteMediaPlayerState,this.emitDailyJSEvent(e));break;case"custom-button-click":case"sidebar-view-changed":this.emitDailyJSEvent(e);break;case nY:var k=this._meetingSessionState.topology!==(e.meetingSessionState&&e.meetingSessionState.topology);this._meetingSessionState=sv(e.meetingSessionState,this._callObjectMode),(this._callObjectMode||k)&&this.emitDailyJSEvent(e);break;case ii:this._isScreenSharing=!0,this.emitDailyJSEvent(e);break;case ia:case is:this._isScreenSharing=!1,this.emitDailyJSEvent(e);break;case n2:case n3:case n4:case n5:case n8:case nZ:case n0:case n1:case nL:case nD:case n9:case n7:case"test-completed":case iu:case n6:case ig:case iv:case iy:case ib:case ik:case i_:case"dialin-ready":case"dialin-connected":case"dialin-error":case"dialin-stopped":case"dialin-warning":case"dialout-connected":case"dialout-answered":case"dialout-error":case"dialout-stopped":case"dialout-warning":this.emitDailyJSEvent(e);break;case"request-fullscreen":this.requestFullscreen();break;case"request-exit-fullscreen":this.exitFullscreen()}}},{key:"maybeEventRecordingStopped",value:function(e,t){var r="record";e&&(t.local||!1!==t[r]||e[r]===t[r]||this.emitDailyJSEvent({action:n3}))}},{key:"maybeEventRecordingStarted",value:function(e,t){var r="record";e&&(t.local||!0!==t[r]||e[r]===t[r]||this.emitDailyJSEvent({action:n2}))}},{key:"_trackStatePlayable",value:function(e){return!(!e||e.state!==ni)}},{key:"_trackChanged",value:function(e,t){return(null==e?void 0:e.id)!==(null==t?void 0:t.id)}},{key:"maybeEventTrackStopped",value:function(e,t,r){var n,i,a=null!==(n=null==t?void 0:t.tracks[e])&&void 0!==n?n:null,s=null!==(i=null==r?void 0:r.tracks[e])&&void 0!==i?i:null,o=null==a?void 0:a.track;if(o){var l=this._trackStatePlayable(a),c=this._trackStatePlayable(s),u=this._trackChanged(o,null==s?void 0:s.track);l&&(c&&!u||this.emitDailyJSEvent({action:nX,track:o,participant:null!=r?r:t,type:e}))}}},{key:"maybeEventTrackStarted",value:function(e,t,r){var n,i,a=null!==(n=null==t?void 0:t.tracks[e])&&void 0!==n?n:null,s=null!==(i=null==r?void 0:r.tracks[e])&&void 0!==i?i:null,o=null==s?void 0:s.track;if(o){var l=this._trackStatePlayable(a),c=this._trackStatePlayable(s),u=this._trackChanged(null==a?void 0:a.track,o);c&&(l&&!u||this.emitDailyJSEvent({action:nK,track:o,participant:r,type:e}))}}},{key:"maybeParticipantTracksStopped",value:function(e,t){if(e)for(var r in e.tracks)this.maybeEventTrackStopped(r,e,t)}},{key:"maybeParticipantTracksStarted",value:function(e,t){if(t)for(var r in t.tracks)this.maybeEventTrackStarted(r,e,t)}},{key:"compareEqualForRMPUpdateEvent",value:function(e,t){var r,n;return e.state===t.state&&(null===(r=e.settings)||void 0===r?void 0:r.volume)===(null===(n=t.settings)||void 0===n?void 0:n.volume)}},{key:"emitDailyJSEvent",value:function(e){try{e.callClientId=this.callClientId,this.emit(e.action,e)}catch(t){console.log("could not emit",e,t)}}},{key:"compareEqualForParticipantUpdateEvent",value:function(e,t){return!!z(e,t)&&(!e.videoTrack||!t.videoTrack||e.videoTrack.id===t.videoTrack.id&&e.videoTrack.muted===t.videoTrack.muted&&e.videoTrack.enabled===t.videoTrack.enabled)&&(!e.audioTrack||!t.audioTrack||e.audioTrack.id===t.audioTrack.id&&e.audioTrack.muted===t.audioTrack.muted&&e.audioTrack.enabled===t.audioTrack.enabled)}},{key:"nativeUtils",value:function(){return iD()?"undefined"==typeof DailyNativeUtils?(console.warn("in React Native, DailyNativeUtils is expected to be available"),null):DailyNativeUtils:null}},{key:"updateIsPreparingToJoin",value:function(e){this._updateCallState(this._callState,e)}},{key:"_updateCallState",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._isPreparingToJoin;if(e!==this._callState||t!==this._isPreparingToJoin){var r=this._callState,n=this._isPreparingToJoin;this._callState=e,this._isPreparingToJoin=t;var i=this._callState===r8;this.updateShowAndroidOngoingMeetingNotification(i);var a=a8(r,n),s=a8(this._callState,this._isPreparingToJoin);a!==s&&(this.updateKeepDeviceAwake(s),this.updateDeviceAudioMode(s),this.updateNoOpRecordingEnsuringBackgroundContinuity(s))}}},{key:"resetMeetingDependentVars",value:function(){this._participants={},this._participantCounts=aW,this._waitingParticipants={},this._activeSpeaker={},this._activeSpeakerMode=!1,this._didPreAuth=!1,this._accessState={access:na},this._finalSummaryOfPrevSession=this._meetingSessionSummary,this._meetingSessionSummary={},this._meetingSessionState=sv(az,this._callObjectMode),this._isScreenSharing=!1,this._receiveSettings={},this._inputSettings=void 0,this._sendSettings={},this._localAudioLevel=0,this._isLocalAudioLevelObserverRunning=!1,this._remoteParticipantsAudioLevel={},this._isRemoteParticipantsAudioLevelObserverRunning=!1,this._maxAppMessageSize=4096,this._callMachineInitialized=!1,this._bundleLoadTime=void 0,this._preloadCache}},{key:"updateKeepDeviceAwake",value:function(e){iD()&&this.nativeUtils().setKeepDeviceAwake(e,this.callClientId)}},{key:"updateDeviceAudioMode",value:function(e){if(iD()&&!this.disableReactNativeAutoDeviceManagement("audio")){var t=e?this._nativeInCallAudioMode:"idle";this.nativeUtils().setAudioMode(t)}}},{key:"updateShowAndroidOngoingMeetingNotification",value:function(e){if(iD()&&this.nativeUtils().setShowOngoingMeetingNotification){var t,r,n,i;if(this.properties.reactNativeConfig&&this.properties.reactNativeConfig.androidInCallNotification){var a=this.properties.reactNativeConfig.androidInCallNotification;t=a.title,r=a.subtitle,n=a.iconName,i=a.disableForCustomOverride}i&&(e=!1),this.nativeUtils().setShowOngoingMeetingNotification(e,t,r,n,this.callClientId)}}},{key:"updateNoOpRecordingEnsuringBackgroundContinuity",value:function(e){iD()&&this.nativeUtils().enableNoOpRecordingEnsuringBackgroundContinuity&&this.nativeUtils().enableNoOpRecordingEnsuringBackgroundContinuity(e)}},{key:"toggleParticipantAudioBasedOnNativeAudioFocus",value:function(){var e;if(iD()){var t=null===(e=this._callMachine())||void 0===e||null===(e=e.store)||void 0===e?void 0:e.getState();for(var r in null==t?void 0:t.streams){var n=t.streams[r];n&&n.pendingTrack&&"audio"===n.pendingTrack.kind&&(n.pendingTrack.enabled=this._hasNativeAudioFocus)}}}},{key:"disableReactNativeAutoDeviceManagement",value:function(e){return this.properties.reactNativeConfig&&this.properties.reactNativeConfig.disableAutoDeviceManagement&&this.properties.reactNativeConfig.disableAutoDeviceManagement[e]}},{key:"absoluteUrl",value:function(e){if(void 0!==e){var t=document.createElement("a");return t.href=e,t.href}}},{key:"sayHello",value:function(){var e="hello, world.";return console.log(e),e}},{key:"_logUseAfterDestroy",value:function(){var e=Object.values(aG)[0];if(this.needsLoad()){if(e&&!e.needsLoad()){var t={action:iM,level:"error",code:this.strictMode?9995:9997};e.sendMessageToCallMachine(t)}else this.strictMode||console.error("You are are attempting to use a call instance that was previously destroyed, which is unsupported. Please remove `strictMode: false` from your constructor properties to enable strict mode to track down and fix this unsupported usage.")}else{var r={action:iM,level:"error",code:this.strictMode?9995:9997};this._messageChannel.sendMessageToCallMachine(r,null,this.callClientId,this._iframe)}}},{key:"_logDuplicateInstanceAttempt",value:function(){for(var e=0,t=Object.values(aG);e<t.length;e++){var r=t[e];r._callMachineInitialized?(r.sendMessageToCallMachine({action:iM,level:"warn",code:this.allowMultipleCallInstances?9993:9992}),r._delayDuplicateInstanceLog=!1):r._delayDuplicateInstanceLog=!0}}},{key:"_maybeSendToSentry",value:function(e){if(!(null!==(n=e.error)&&void 0!==n&&n.type&&(![n_,ny,ng].includes(e.error.type)||e.error.type===ng&&e.error.msg.includes("deleted")))){var t=null!==(i=this.properties)&&void 0!==i&&i.url?new URL(this.properties.url):void 0,r="production";t&&t.host.includes(".staging.daily")&&(r="staging");var n,i,a,s,o,l,c,u,d,h=new rC({dsn:"https://<EMAIL>/168844",transport:rN,stackParser:rU,integrations:(function(e){let t=[rt(),t7(),rG(),rB(),rK(),r1(),ro(),r0()];return!1!==e.autoSessionTracking&&t.push(rH()),t})({}).filter(function(e){return!["BrowserApiErrors","Breadcrumbs","GlobalHandlers"].includes(e.name)}),environment:r}),p=new tg;if(p.setClient(h),h.init(),this.session_id&&p.setExtra("sessionId",this.session_id),this.properties){var f=aU({},this.properties);f.userName=f.userName?"[Filtered]":void 0,f.userData=f.userData?"[Filtered]":void 0,f.token=f.token?"[Filtered]":void 0,p.setExtra("properties",f)}if(t){var m=t.searchParams.get("domain");if(!m){var g=t.host.match(/(.*?)\./);m=g&&g[1]||""}m&&p.setTag("domain",m)}e.error&&(p.setTag("fatalErrorType",e.error.type),p.setExtra("errorDetails",e.error.details),(null===(o=e.error.details)||void 0===o?void 0:o.uri)&&p.setTag("serverAddress",e.error.details.uri),(null===(l=e.error.details)||void 0===l?void 0:l.workerGroup)&&p.setTag("workerGroup",e.error.details.workerGroup),(null===(c=e.error.details)||void 0===c?void 0:c.geoGroup)&&p.setTag("geoGroup",e.error.details.geoGroup),(null===(u=e.error.details)||void 0===u?void 0:u.on)&&p.setTag("connectionAttempt",e.error.details.on),null!==(d=e.error.details)&&void 0!==d&&d.bundleUrl&&(p.setTag("bundleUrl",e.error.details.bundleUrl),p.setTag("bundleError",e.error.details.sourceError.type))),p.setTags({callMode:this._callObjectMode?iD()?"reactNative":null!==(a=this.properties)&&void 0!==a&&null!==(a=a.dailyConfig)&&void 0!==a&&null!==(a=a.callMode)&&void 0!==a&&a.includes("prebuilt")?this.properties.dailyConfig.callMode:"custom":"prebuilt-frame",version:H.version()});var v=(null===(s=e.error)||void 0===s?void 0:s.msg)||e.errorMsg;p.captureException(Error(v))}}},{key:"_callMachine",value:function(){var e;return null===(e=window._daily)||void 0===e||null===(e=e.instances)||void 0===e||null===(e=e[this.callClientId])||void 0===e?void 0:e.callMachine}},{key:"_maybeUpdateInputSettings",value:function(e){if(!z(this._inputSettings,e)){var t=this._getInputSettings();this._inputSettings=e;var r=this._getInputSettings();z(t,r)||this.emitDailyJSEvent({action:iw,inputSettings:r})}}}],[{key:"supportedBrowser",value:function(){if(iD())return{supported:!0,mobile:!0,name:"React Native",version:null,supportsScreenShare:!0,supportsSfu:!0,supportsVideoProcessing:!1,supportsAudioProcessing:!1};var e=es.getParser(iL());return{supported:!!iB(),mobile:"mobile"===e.getPlatformType(),name:e.getBrowserName(),version:e.getBrowserVersion(),supportsFullscreen:!!iF(),supportsScreenShare:!!(navigator&&navigator.mediaDevices&&navigator.mediaDevices.getDisplayMedia&&(function(e,t){if(!e||!t)return!0;switch(e){case"Chrome":return t.major>=75;case"Safari":return RTCRtpTransceiver.prototype.hasOwnProperty("currentDirection")&&(13!==t.major||0!==t.minor||0!==t.point);case"Firefox":return t.major>=67}return!0}(iY(),iz())||iD())),supportsSfu:!!iB(),supportsVideoProcessing:iU(),supportsAudioProcessing:i$()}}},{key:"version",value:function(){return"0.79.0"}},{key:"createCallObject",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return e.layout="none",new H(null,e)}},{key:"wrap",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(sr(),!e||!e.contentWindow||"string"!=typeof e.src)throw Error("DailyIframe::Wrap needs an iframe-like first argument");return t.layout||(t.customLayout?t.layout="custom-v1":t.layout="browser"),new H(e,t)}},{key:"createFrame",value:function(e,t){sr(),e&&t?(r=e,n=t):e&&e.append?(r=e,n={}):(r=document.body,n=e||{});var r,n,i=n.iframeStyle;i||(i=r===document.body?{position:"fixed",border:"1px solid black",backgroundColor:"white",width:"375px",height:"450px",right:"1em",bottom:"1em"}:{border:0,width:"100%",height:"100%"});var a=document.createElement("iframe");window.navigator&&window.navigator.userAgent.match(/Chrome\/61\./)?a.allow="microphone, camera":a.allow="microphone; camera; autoplay; display-capture; screen-wake-lock",a.style.visibility="hidden",r.appendChild(a),a.style.visibility=null,Object.keys(i).forEach(function(e){return a.style[e]=i[e]}),n.layout||(n.customLayout?n.layout="custom-v1":n.layout="browser");try{return new H(a,n)}catch(e){throw r.removeChild(a),e}}},{key:"createTransparentFrame",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};sr();var t=document.createElement("iframe");return t.allow="microphone; camera; autoplay",t.style.cssText="\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      border: 0;\n      pointer-events: none;\n    ",document.body.appendChild(t),e.layout||(e.layout="custom-v1"),H.wrap(t,e)}},{key:"getCallInstance",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;return e?aG[e]:Object.values(aG)[0]}}])}();function a4(e,t){var r={};for(var n in e)if(e[n]instanceof MediaStreamTrack)console.warn("MediaStreamTrack found in props or cache.",n),r[n]=ix;else if("dailyConfig"===n){if(e[n].modifyLocalSdpHook){var i=window._daily.instances[t].customCallbacks||{};i.modifyLocalSdpHook=e[n].modifyLocalSdpHook,window._daily.instances[t].customCallbacks=i,delete e[n].modifyLocalSdpHook}if(e[n].modifyRemoteSdpHook){var a=window._daily.instances[t].customCallbacks||{};a.modifyRemoteSdpHook=e[n].modifyRemoteSdpHook,window._daily.instances[t].customCallbacks=a,delete e[n].modifyRemoteSdpHook}r[n]=e[n]}else r[n]=e[n];return r}function a5(e){var t=arguments.length>2?arguments[2]:void 0;if(e!==r8){var r="".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"This daily-js method"," only supported after join.");throw t&&(r+=" ".concat(t)),console.error(r),Error(r)}}function a8(e,t){return[r5,r8].includes(e)||t}function a6(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"This daily-js method",n=arguments.length>3?arguments[3]:void 0;if(a8(e,t)){var i="".concat(r," not supported after joining a meeting.");throw n&&(i+=" ".concat(n)),console.error(i),Error(i)}}function a9(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"This daily-js method",r=arguments.length>2?arguments[2]:void 0;if(!e){var n="".concat(t,arguments.length>3&&void 0!==arguments[3]&&arguments[3]?" requires preAuth() or startCamera() to initialize call state.":" requires preAuth(), startCamera(), or join() to initialize call state.");throw r&&(n+=" ".concat(r)),console.error(n),Error(n)}}function a7(e){if(e){var t="A pre-call quality test is in progress. Please try ".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"This daily-js method"," again once testing has completed. Use stopTestCallQuality() to end it early.");throw console.error(t),Error(t)}}function se(e){if(!e){var t="".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"This daily-js method"," is only supported on custom callObject instances");throw console.error(t),Error(t)}}function st(e){if(e){var t="".concat(arguments.length>1&&void 0!==arguments[1]?arguments[1]:"This daily-js method"," is only supported as part of Daily's Prebuilt");throw console.error(t),Error(t)}}function sr(){if(iD())throw Error("This daily-js method is not currently supported in React Native")}function sn(){if(!iD())throw Error("This daily-js method is only supported in React Native")}function si(e){var t;if(void 0===e)return!0;if("string"==typeof e)t=e;else try{t=JSON.stringify(e),z(JSON.parse(t),e)||console.warn("The userData provided will be modified when serialized.")}catch(e){throw Error("userData must be serializable to JSON: ".concat(e))}if(t.length>4096)throw Error("userData is too large (".concat(t.length," characters). Maximum size suppported is ").concat(4096,"."));return!0}function sa(e,t){for(var r=t.allowAllParticipantsKey,n=function(e){return!!(void 0===e.layer||Number.isInteger(e.layer)&&e.layer>=0||"inherit"===e.layer)},i=0,a=Object.entries(e);i<a.length;i++){var s=C(a[i],2),o=s[0],l=s[1];if(!function(e){var t=["local"];return r||t.push("*"),e&&!t.includes(e)}(o)||!(l&&!(l.video&&!n(l.video))&&!(l.screenVideo&&!n(l.screenVideo))))return!1}return!0}function ss(e){if("object"!==p(e))return!1;for(var t=0,r=Object.entries(e);t<r.length;t++){var n=C(r[t],2),i=n[0],a=n[1];switch(i){case"video":if("object"!==p(a))return!1;for(var s=0,o=Object.entries(a);s<o.length;s++){var l=C(o[s],2),c=l[0],u=l[1];switch(c){case"processor":if(!function(e){if(iD())return console.warn("Video processing is not yet supported in React Native"),!1;var t,r=["type","config"];return!!e&&"object"===p(e)&&!!("string"==typeof(t=e.type)&&(Object.values(iO).includes(t)||(console.error("inputSettings video processor type invalid"),0)))&&(!e.config||"object"===p(e.config)&&!!function(e,t){var r,n=Object.keys(t);if(0===n.length)return!0;var i="invalid object in inputSettings -> video -> processor -> config";switch(e){case iO.BGBLUR:return n.length>1||"strength"!==n[0]?(console.error(i),!1):!("number"!=typeof t.strength||t.strength<=0||t.strength>1||isNaN(t.strength))||(console.error("".concat(i,"; expected: {0 < strength <= 1}, got: ").concat(t.strength)),!1);case iO.BGIMAGE:return!(void 0!==t.source&&!("default"===t.source?(t.type="default",!0):t.source instanceof ArrayBuffer||(ed(t.source)?(t.type="url",!!function(e){var t=new URL(e),r=t.pathname;if("data:"===t.protocol)try{var n=r.substring(r.indexOf(":")+1,r.indexOf(";")).split("/")[1];return iI.includes(n)}catch(e){return console.error("failed to deduce blob content type",e),!1}var i=r.split(".").at(-1).toLowerCase().trim();return iI.includes(i)}(t.source)||(console.error("invalid image type; supported types: [".concat(iI.join(", "),"]")),!1)):isNaN(r=Number(t.source))||!Number.isInteger(r)||r<=0||r>10?(console.error("invalid image selection; must be an int, > 0, <= ".concat(10)),!1):(t.type="daily-preselect",!0))));default:return!0}}(e.type,e.config))&&(Object.keys(e).filter(function(e){return!r.includes(e)}).forEach(function(t){console.warn("invalid key inputSettings -> video -> processor : ".concat(t)),delete e[t]}),!0)}(u))return!1;break;case"settings":if(!sl(u))return!1;break;default:return!1}}break;case"audio":if("object"!==p(a))return!1;for(var d=0,h=Object.entries(a);d<h.length;d++){var f=C(h[d],2),m=f[0],g=f[1];switch(m){case"processor":if(!function(e){if(iD())return console.warn("Video processing is not yet supported in React Native"),!1;var t,r=["type"];return!!e&&"object"===p(e)&&(Object.keys(e).filter(function(e){return!r.includes(e)}).forEach(function(t){console.warn("invalid key inputSettings -> audio -> processor : ".concat(t)),delete e[t]}),!!("string"==typeof(t=e.type)&&(Object.values(ij).includes(t)||(console.error("inputSettings audio processor type invalid"),0))))}(g))return!1;break;case"settings":if(!sl(g))return!1;break;default:return!1}}break;default:return!1}}return!0}function so(e,t,r){var n,i=[];e.video&&e.video.processor&&(iU(null!==(n=null==t?void 0:t.useLegacyVideoProcessor)&&void 0!==n&&n)||(e.video.settings?delete e.video.processor:delete e.video,i.push("video"))),e.audio&&e.audio.processor&&(i$()||(e.audio.settings?delete e.audio.processor:delete e.audio,i.push("audio"))),i.length>0&&console.error("Ignoring settings for browser- or platform-unsupported input processor(s): ".concat(i.join(", "))),e.audio&&e.audio.settings&&(e.audio.settings.customTrack?(r.audioTrack=e.audio.settings.customTrack,e.audio.settings={customTrack:ix}):delete r.audioTrack),e.video&&e.video.settings&&(e.video.settings.customTrack?(r.videoTrack=e.video.settings.customTrack,e.video.settings={customTrack:ix}):delete r.videoTrack)}function sl(e){return"object"===p(e)&&(!e.customTrack||e.customTrack instanceof MediaStreamTrack)}function sc(){var e=Object.values(iO).join(" | "),t=Object.values(ij).join(" | ");return"inputSettings must be of the form: { video?: { processor?: { type: [ ".concat(e," ], config?: {} } }, audio?: { processor: {type: [ ").concat(t," ] } } }")}function su(e){var t=e.allowAllParticipantsKey;return"receiveSettings must be of the form { [<remote participant id> | ".concat(nc).concat(t?' | "'.concat("*",'"'):"","]: ")+'{ [video: [{ layer: [<non-negative integer> | "inherit"] } | "inherit"]], [screenVideo: [{ layer: [<non-negative integer> | "inherit"] } | "inherit"]] }}}'}function sd(){return"customIntegrations should be an object of type ".concat(JSON.stringify(a0),".")}function sh(e){if(e&&"object"!==p(e)||Array.isArray(e))return console.error("customTrayButtons should be an Object of the type ".concat(JSON.stringify(aZ),".")),!1;if(e)for(var t=0,r=Object.entries(e);t<r.length;t++)for(var n=C(r[t],1)[0],i=0,a=Object.entries(e[n]);i<a.length;i++){var s=C(a[i],2),o=s[0],l=s[1],c=aZ.id[o];if(!c)return console.error("customTrayButton does not support key ".concat(o)),!1;switch(o){case"iconPath":case"iconPathDarkMode":if(!ed(l))return console.error("customTrayButton ".concat(o," should be a url.")),!1;break;case"visualState":if(!["default","sidebar-open","active"].includes(l))return console.error("customTrayButton ".concat(o," should be ").concat(c,". Got: ").concat(l)),!1;break;default:if(p(l)!==c)return console.error("customTrayButton ".concat(o," should be a ").concat(c,".")),!1}}return!0}function sp(e){if(!e||e&&"object"!==p(e)||Array.isArray(e))return console.error(sd()),!1;for(var t=function(e){return"".concat(e," should be ").concat(a0.id[e])},r=function(e,t){return console.error("customIntegration ".concat(e,": ").concat(t))},n=0,i=Object.entries(e);n<i.length;n++){var a=C(i[n],1)[0];if(!("label"in e[a]))return r(a,"label is required"),!1;if(!("location"in e[a]))return r(a,"location is required"),!1;if(!("src"in e[a])&&!("srcdoc"in e[a]))return r(a,"src or srcdoc is required"),!1;for(var s=0,o=Object.entries(e[a]);s<o.length;s++){var l=C(o[s],2),c=l[0],u=l[1];switch(c){case"allow":case"csp":case"name":case"referrerPolicy":case"sandbox":if("string"!=typeof u)return r(a,t(c)),!1;break;case"iconURL":if(!ed(u))return r(a,"".concat(c," should be a url")),!1;break;case"src":if("srcdoc"in e[a])return r(a,"cannot have both src and srcdoc"),!1;if(!ed(u))return r(a,'src "'.concat(u,'" is not a valid URL')),!1;break;case"srcdoc":if("src"in e[a])return r(a,"cannot have both src and srcdoc"),!1;if("string"!=typeof u)return r(a,t(c)),!1;break;case"location":if(!["main","sidebar"].includes(u))return r(a,t(c)),!1;break;case"controlledBy":if("*"!==u&&"owners"!==u&&(!Array.isArray(u)||u.some(function(e){return"string"!=typeof e})))return r(a,t(c)),!1;break;case"shared":if((!Array.isArray(u)||u.some(function(e){return"string"!=typeof e}))&&"owners"!==u&&"boolean"!=typeof u)return r(a,t(c)),!1;break;default:if(!a0.id[c])return console.error("customIntegration does not support key ".concat(c)),!1}}}return!0}function sf(e,t){var r=e.sessionId,n=e.toEndPoint,i=e.callerId,a=e.useSipRefer;if(!r||!n)throw Error("".concat(t,"() requires a sessionId and toEndPoint"));if("string"!=typeof r||"string"!=typeof n)throw Error("Invalid paramater: sessionId and toEndPoint must be of type string");if(a&&!n.startsWith("sip:"))throw Error('"toEndPoint" must be a "sip" address');if(!n.startsWith("sip:")&&!n.startsWith("+"))throw Error("toEndPoint: ".concat(n,' must starts with either "sip:" or "+"'));if(i&&"string"!=typeof i)throw Error("callerId must be of type string");if(i&&!n.startsWith("+"))throw Error("callerId is only valid when transferring to a PSTN number")}function sm(e){if("object"!==p(e))throw Error('RemoteMediaPlayerSettings: must be "object" type');if(e.state&&!Object.values(iP).includes(e.state))throw Error("Invalid value for RemoteMediaPlayerSettings.state, valid values are: "+JSON.stringify(iP));if(e.volume){if("number"!=typeof e.volume)throw Error('RemoteMediaPlayerSettings.volume: must be "number" type');if(e.volume<0||e.volume>2)throw Error("RemoteMediaPlayerSettings.volume: must be between 0.0 - 2.0")}}function sg(e,t,r){return!("number"!=typeof e||e<t||e>r)}function sv(e,t){return e&&!t&&delete e.data,e}},41663:(e,t,r)=>{Promise.resolve().then(r.bind(r,84248))},45734:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>h,tree:()=>c});var n=r(65239),i=r(48088),a=r(88170),s=r.n(a),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["(workspace)",{children:["agents",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,93448)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,50184)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/(workspace)/agents/page",pathname:"/agents",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},53053:(e,t)=>{"use strict";var r;Object.defineProperty(t,"__esModule",{value:!0}),t.Api=t.HttpClient=t.ContentType=void 0,function(e){e.Json="application/json",e.FormData="multipart/form-data",e.UrlEncoded="application/x-www-form-urlencoded",e.Text="text/plain"}(r||(t.ContentType=r={}));class n{baseUrl="https://api.vapi.ai";securityData=null;securityWorker;abortControllers=new Map;customFetch=(...e)=>fetch(...e);baseApiParams={credentials:"same-origin",headers:{},redirect:"follow",referrerPolicy:"no-referrer"};constructor(e={}){Object.assign(this,e)}setSecurityData=e=>{this.securityData=e};encodeQueryParam(e,t){let r=encodeURIComponent(e);return`${r}=${encodeURIComponent("number"==typeof t?t:`${t}`)}`}addQueryParam(e,t){return this.encodeQueryParam(t,e[t])}addArrayQueryParam(e,t){return e[t].map(e=>this.encodeQueryParam(t,e)).join("&")}toQueryString(e){let t=e||{};return Object.keys(t).filter(e=>void 0!==t[e]).map(e=>Array.isArray(t[e])?this.addArrayQueryParam(t,e):this.addQueryParam(t,e)).join("&")}addQueryParams(e){let t=this.toQueryString(e);return t?`?${t}`:""}contentFormatters={[r.Json]:e=>null!==e&&("object"==typeof e||"string"==typeof e)?JSON.stringify(e):e,[r.Text]:e=>null!==e&&"string"!=typeof e?JSON.stringify(e):e,[r.FormData]:e=>Object.keys(e||{}).reduce((t,r)=>{let n=e[r];return t.append(r,n instanceof Blob?n:"object"==typeof n&&null!==n?JSON.stringify(n):`${n}`),t},new FormData),[r.UrlEncoded]:e=>this.toQueryString(e)};mergeRequestParams(e,t){return{...this.baseApiParams,...e,...t||{},headers:{...this.baseApiParams.headers||{},...e.headers||{},...t&&t.headers||{}}}}createAbortSignal=e=>{if(this.abortControllers.has(e)){let t=this.abortControllers.get(e);return t?t.signal:void 0}let t=new AbortController;return this.abortControllers.set(e,t),t.signal};abortRequest=e=>{let t=this.abortControllers.get(e);t&&(t.abort(),this.abortControllers.delete(e))};request=async({body:e,secure:t,path:n,type:i,query:a,format:s,baseUrl:o,cancelToken:l,...c})=>{let u=("boolean"==typeof t?t:this.baseApiParams.secure)&&this.securityWorker&&await this.securityWorker(this.securityData)||{},d=this.mergeRequestParams(c,u),h=a&&this.toQueryString(a),p=this.contentFormatters[i||r.Json],f=s||d.format;return this.customFetch(`${o||this.baseUrl||""}${n}${h?`?${h}`:""}`,{...d,headers:{...d.headers||{},...i&&i!==r.FormData?{"Content-Type":i}:{}},signal:(l?this.createAbortSignal(l):d.signal)||null,body:null==e?null:p(e)}).then(async e=>{e.data=null,e.error=null;let t=f?await e[f]().then(t=>(e.ok?e.data=t:e.error=t,e)).catch(t=>(e.error=t,e)):e;if(l&&this.abortControllers.delete(l),!e.ok)throw t;return t})}}t.HttpClient=n;class i extends n{call={callControllerCreate:(e,t={})=>this.request({path:"/call",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),callControllerFindAll:(e,t={})=>this.request({path:"/call",method:"GET",query:e,secure:!0,format:"json",...t}),callControllerFindOne:(e,t={})=>this.request({path:`/call/${e}`,method:"GET",secure:!0,format:"json",...t}),callControllerUpdate:(e,t,n={})=>this.request({path:`/call/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...n}),callControllerDeleteCallData:(e,t={})=>this.request({path:`/call/${e}`,method:"DELETE",secure:!0,format:"json",...t}),callControllerCreatePhoneCall:(e,t={})=>this.request({path:"/call/phone",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),callControllerCreateWebCall:(e,t={})=>this.request({path:"/call/web",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t})};v2={callControllerExportCalls:(e,t={})=>this.request({path:"/v2/call/export",method:"GET",query:e,secure:!0,...t}),callControllerFindAllPaginated:(e,t={})=>this.request({path:"/v2/call",method:"GET",query:e,secure:!0,format:"json",...t}),callControllerFindAllMetadataPaginated:(e,t={})=>this.request({path:"/v2/call/metadata",method:"GET",query:e,secure:!0,format:"json",...t}),assistantControllerFindAllPaginated:(e,t={})=>this.request({path:"/v2/assistant",method:"GET",query:e,secure:!0,format:"json",...t}),phoneNumberControllerFindAllPaginated:(e,t={})=>this.request({path:"/v2/phone-number",method:"GET",query:e,secure:!0,format:"json",...t})};chat={chatControllerListChats:(e,t={})=>this.request({path:"/chat",method:"GET",query:e,secure:!0,format:"json",...t}),chatControllerCreateChat:(e,t={})=>this.request({path:"/chat",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),chatControllerGetChat:(e,t={})=>this.request({path:`/chat/${e}`,method:"GET",secure:!0,format:"json",...t}),chatControllerDeleteChat:(e,t={})=>this.request({path:`/chat/${e}`,method:"DELETE",secure:!0,format:"json",...t}),chatControllerCreateOpenAiChat:(e,t={})=>this.request({path:"/chat/responses",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t})};session={sessionControllerCreate:(e,t={})=>this.request({path:"/session",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),sessionControllerFindAllPaginated:(e,t={})=>this.request({path:"/session",method:"GET",query:e,secure:!0,format:"json",...t}),sessionControllerFindOne:(e,t={})=>this.request({path:`/session/${e}`,method:"GET",secure:!0,format:"json",...t}),sessionControllerUpdate:(e,t,n={})=>this.request({path:`/session/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...n}),sessionControllerRemove:(e,t={})=>this.request({path:`/session/${e}`,method:"DELETE",secure:!0,format:"json",...t})};assistant={assistantControllerCreate:(e,t={})=>this.request({path:"/assistant",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),assistantControllerFindAll:(e,t={})=>this.request({path:"/assistant",method:"GET",query:e,secure:!0,format:"json",...t}),assistantControllerFindOne:(e,t={})=>this.request({path:`/assistant/${e}`,method:"GET",secure:!0,format:"json",...t}),assistantControllerUpdate:(e,t,n={})=>this.request({path:`/assistant/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...n}),assistantControllerReplace:(e,t,n={})=>this.request({path:`/assistant/${e}`,method:"PUT",body:t,secure:!0,type:r.Json,format:"json",...n}),assistantControllerRemove:(e,t={})=>this.request({path:`/assistant/${e}`,method:"DELETE",secure:!0,format:"json",...t}),assistantControllerFindVersions:(e,t,r={})=>this.request({path:`/assistant/${e}/version`,method:"GET",query:t,secure:!0,format:"json",...r})};phoneNumber={phoneNumberControllerImportTwilio:(e,t={})=>this.request({path:"/phone-number/import/twilio",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),phoneNumberControllerImportVonage:(e,t={})=>this.request({path:"/phone-number/import/vonage",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),phoneNumberControllerCreate:(e,t={})=>this.request({path:"/phone-number",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),phoneNumberControllerFindAll:(e,t={})=>this.request({path:"/phone-number",method:"GET",query:e,secure:!0,format:"json",...t}),phoneNumberControllerFindOne:(e,t={})=>this.request({path:`/phone-number/${e}`,method:"GET",secure:!0,format:"json",...t}),phoneNumberControllerUpdate:(e,t,n={})=>this.request({path:`/phone-number/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...n}),phoneNumberControllerRemove:(e,t={})=>this.request({path:`/phone-number/${e}`,method:"DELETE",secure:!0,format:"json",...t})};tool={toolControllerCreate:(e,t={})=>this.request({path:"/tool",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),toolControllerFindAll:(e,t={})=>this.request({path:"/tool",method:"GET",query:e,secure:!0,format:"json",...t}),toolControllerFindOne:(e,t={})=>this.request({path:`/tool/${e}`,method:"GET",secure:!0,format:"json",...t}),toolControllerUpdate:(e,t,n={})=>this.request({path:`/tool/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...n}),toolControllerRemove:(e,t={})=>this.request({path:`/tool/${e}`,method:"DELETE",secure:!0,format:"json",...t})};file={fileControllerCreateDeprecated:(e,t={})=>this.request({path:"/file/upload",method:"POST",body:e,secure:!0,type:r.FormData,format:"json",...t}),fileControllerCreate:(e,t={})=>this.request({path:"/file",method:"POST",body:e,secure:!0,type:r.FormData,format:"json",...t}),fileControllerFindAll:(e={})=>this.request({path:"/file",method:"GET",secure:!0,format:"json",...e}),fileControllerFindOne:(e,t={})=>this.request({path:`/file/${e}`,method:"GET",secure:!0,format:"json",...t}),fileControllerUpdate:(e,t,n={})=>this.request({path:`/file/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...n}),fileControllerRemove:(e,t={})=>this.request({path:`/file/${e}`,method:"DELETE",secure:!0,format:"json",...t})};knowledgeBase={knowledgeBaseControllerCreate:(e,t={})=>this.request({path:"/knowledge-base",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),knowledgeBaseControllerFindAll:(e,t={})=>this.request({path:"/knowledge-base",method:"GET",query:e,secure:!0,format:"json",...t}),knowledgeBaseControllerFindOne:(e,t={})=>this.request({path:`/knowledge-base/${e}`,method:"GET",secure:!0,format:"json",...t}),knowledgeBaseControllerUpdate:(e,t,n={})=>this.request({path:`/knowledge-base/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...n}),knowledgeBaseControllerRemove:(e,t={})=>this.request({path:`/knowledge-base/${e}`,method:"DELETE",secure:!0,format:"json",...t})};workflow={workflowControllerFindAll:(e={})=>this.request({path:"/workflow",method:"GET",secure:!0,format:"json",...e}),workflowControllerCreate:(e,t={})=>this.request({path:"/workflow",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),workflowControllerFindOne:(e,t={})=>this.request({path:`/workflow/${e}`,method:"GET",secure:!0,format:"json",...t}),workflowControllerDelete:(e,t={})=>this.request({path:`/workflow/${e}`,method:"DELETE",secure:!0,format:"json",...t}),workflowControllerUpdate:(e,t,n={})=>this.request({path:`/workflow/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...n})};squad={squadControllerCreate:(e,t={})=>this.request({path:"/squad",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),squadControllerFindAll:(e,t={})=>this.request({path:"/squad",method:"GET",query:e,secure:!0,format:"json",...t}),squadControllerFindOne:(e,t={})=>this.request({path:`/squad/${e}`,method:"GET",secure:!0,format:"json",...t}),squadControllerUpdate:(e,t,n={})=>this.request({path:`/squad/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...n}),squadControllerRemove:(e,t={})=>this.request({path:`/squad/${e}`,method:"DELETE",secure:!0,format:"json",...t})};testSuite={testSuiteControllerFindAllPaginated:(e,t={})=>this.request({path:"/test-suite",method:"GET",query:e,secure:!0,format:"json",...t}),testSuiteControllerCreate:(e,t={})=>this.request({path:"/test-suite",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),testSuiteControllerFindOne:(e,t={})=>this.request({path:`/test-suite/${e}`,method:"GET",secure:!0,format:"json",...t}),testSuiteControllerUpdate:(e,t,n={})=>this.request({path:`/test-suite/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...n}),testSuiteControllerRemove:(e,t={})=>this.request({path:`/test-suite/${e}`,method:"DELETE",secure:!0,format:"json",...t}),testSuiteTestControllerFindAllPaginated:(e,t,r={})=>this.request({path:`/test-suite/${e}/test`,method:"GET",query:t,secure:!0,format:"json",...r}),testSuiteTestControllerCreate:(e,t,n={})=>this.request({path:`/test-suite/${e}/test`,method:"POST",body:t,secure:!0,type:r.Json,format:"json",...n}),testSuiteTestControllerFindOne:(e,t,r={})=>this.request({path:`/test-suite/${e}/test/${t}`,method:"GET",secure:!0,format:"json",...r}),testSuiteTestControllerUpdate:(e,t,n,i={})=>this.request({path:`/test-suite/${e}/test/${t}`,method:"PATCH",body:n,secure:!0,type:r.Json,format:"json",...i}),testSuiteTestControllerRemove:(e,t,r={})=>this.request({path:`/test-suite/${e}/test/${t}`,method:"DELETE",secure:!0,format:"json",...r}),testSuiteRunControllerFindAllPaginated:(e,t,r={})=>this.request({path:`/test-suite/${e}/run`,method:"GET",query:t,secure:!0,format:"json",...r}),testSuiteRunControllerCreate:(e,t,n={})=>this.request({path:`/test-suite/${e}/run`,method:"POST",body:t,secure:!0,type:r.Json,format:"json",...n}),testSuiteRunControllerFindOne:(e,t,r={})=>this.request({path:`/test-suite/${e}/run/${t}`,method:"GET",secure:!0,format:"json",...r}),testSuiteRunControllerUpdate:(e,t,n,i={})=>this.request({path:`/test-suite/${e}/run/${t}`,method:"PATCH",body:n,secure:!0,type:r.Json,format:"json",...i}),testSuiteRunControllerRemove:(e,t,r={})=>this.request({path:`/test-suite/${e}/run/${t}`,method:"DELETE",secure:!0,format:"json",...r})};metrics={analyticsControllerFindAllDeprecated:(e,t={})=>this.request({path:"/metrics",method:"GET",query:e,secure:!0,format:"json",...t})};analytics={analyticsControllerQuery:(e,t={})=>this.request({path:"/analytics",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t})};log={loggingControllerCallLogsQuery:(e,t={})=>this.request({path:"/log",method:"GET",query:e,secure:!0,format:"json",...t}),loggingControllerCallLogsDeleteQuery:(e,t={})=>this.request({path:"/log",method:"DELETE",query:e,secure:!0,...t})};logs={loggingControllerLogsQuery:(e,t={})=>this.request({path:"/logs",method:"GET",query:e,secure:!0,format:"json",...t}),loggingControllerLogsDeleteQuery:(e,t={})=>this.request({path:"/logs",method:"DELETE",query:e,secure:!0,...t})};org={orgControllerCreate:(e,t={})=>this.request({path:"/org",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),orgControllerFindAll:(e={})=>this.request({path:"/org",method:"GET",secure:!0,format:"json",...e}),orgControllerFindOne:(e,t={})=>this.request({path:`/org/${e}`,method:"GET",secure:!0,format:"json",...t}),orgControllerUpdate:(e,t,n={})=>this.request({path:`/org/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...n}),orgControllerDeleteOrg:(e,t={})=>this.request({path:`/org/${e}`,method:"DELETE",secure:!0,...t}),orgControllerFindAllUsers:(e,t={})=>this.request({path:`/org/${e}/user`,method:"GET",secure:!0,format:"json",...t}),orgControllerOrgLeave:(e,t={})=>this.request({path:`/org/${e}/leave`,method:"DELETE",secure:!0,...t}),orgControllerOrgRemoveUser:(e,t,r={})=>this.request({path:`/org/${e}/member/${t}/leave`,method:"DELETE",secure:!0,...r}),orgControllerUsersInvite:(e,t,n={})=>this.request({path:`/org/${e}/invite`,method:"POST",body:t,secure:!0,type:r.Json,...n}),orgControllerUserUpdate:(e,t,n={})=>this.request({path:`/org/${e}/role`,method:"PATCH",body:t,secure:!0,type:r.Json,...n}),orgControllerOrgToken:(e,t={})=>this.request({path:`/org/${e}/auth`,method:"GET",secure:!0,format:"json",...t})};token={tokenControllerCreate:(e,t={})=>this.request({path:"/token",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),tokenControllerFindAll:(e,t={})=>this.request({path:"/token",method:"GET",query:e,secure:!0,format:"json",...t}),tokenControllerFindOne:(e,t={})=>this.request({path:`/token/${e}`,method:"GET",secure:!0,format:"json",...t}),tokenControllerUpdate:(e,t,n={})=>this.request({path:`/token/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...n}),tokenControllerRemove:(e,t={})=>this.request({path:`/token/${e}`,method:"DELETE",secure:!0,format:"json",...t})};credential={credentialControllerCreate:(e,t={})=>this.request({path:"/credential",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),credentialControllerFindAll:(e,t={})=>this.request({path:"/credential",method:"GET",query:e,secure:!0,format:"json",...t}),credentialControllerFindOne:(e,t={})=>this.request({path:`/credential/${e}`,method:"GET",secure:!0,format:"json",...t}),credentialControllerUpdate:(e,t,n={})=>this.request({path:`/credential/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...n}),credentialControllerRemove:(e,t={})=>this.request({path:`/credential/${e}`,method:"DELETE",secure:!0,format:"json",...t}),credentialControllerGenerateSession:(e,t={})=>this.request({path:"/credential/session",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),credentialControllerHandleWebhook:(e,t={})=>this.request({path:"/credential/webhook",method:"POST",body:e,type:r.Json,...t}),credentialControllerTriggerCredentialAction:(e,t={})=>this.request({path:"/credential/trigger",method:"POST",body:e,secure:!0,type:r.Json,...t})};template={templateControllerCreate:(e,t={})=>this.request({path:"/template",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),templateControllerFindAll:(e,t={})=>this.request({path:"/template",method:"GET",query:e,secure:!0,format:"json",...t}),templateControllerFindAllPinned:(e={})=>this.request({path:"/template/pinned",method:"GET",secure:!0,format:"json",...e}),templateControllerFindOne:(e,t={})=>this.request({path:`/template/${e}`,method:"GET",secure:!0,format:"json",...t}),templateControllerUpdate:(e,t,n={})=>this.request({path:`/template/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...n}),templateControllerRemove:(e,t={})=>this.request({path:`/template/${e}`,method:"DELETE",secure:!0,format:"json",...t})};voiceLibrary={voiceLibraryControllerVoiceGetByProvider:(e,t,r={})=>this.request({path:`/voice-library/${e}`,method:"GET",query:t,secure:!0,format:"json",...r}),voiceLibraryControllerVoiceGetAccentsByProvider:(e,t={})=>this.request({path:`/voice-library/${e}/accents`,method:"GET",secure:!0,format:"json",...t}),voiceLibraryControllerVoiceLibrarySyncByProvider:(e,t={})=>this.request({path:`/voice-library/sync/${e}`,method:"POST",secure:!0,format:"json",...t}),voiceLibraryControllerVoiceLibrarySyncDefaultVoices:(e,t={})=>this.request({path:"/voice-library/sync",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),voiceLibraryControllerVoiceLibraryCreateSesameVoice:(e,t={})=>this.request({path:"/voice-library/create-sesame-voice",method:"POST",body:e,secure:!0,type:r.Json,...t})};provider={providerControllerGetWorkflows:(e,t,r={})=>this.request({path:`/${e}/workflows`,method:"GET",query:t,secure:!0,format:"json",...r}),providerControllerGetWorkflowTriggerHook:(e,t,r={})=>this.request({path:`/${e}/workflows/${t}/hooks`,method:"GET",secure:!0,format:"json",...r}),providerControllerGetLocations:(e,t={})=>this.request({path:`/${e}/locations`,method:"GET",secure:!0,format:"json",...t}),voiceProviderControllerSearchVoices:(e,t,r={})=>this.request({path:`/${e}/voices/search`,method:"GET",query:t,secure:!0,format:"json",...r}),voiceProviderControllerSearchVoice:(e,t,r={})=>this.request({path:`/${e}/voice/search`,method:"GET",query:t,secure:!0,format:"json",...r}),voiceProviderControllerAddVoices:(e,t,n={})=>this.request({path:`/${e}/voices/add`,method:"POST",body:t,secure:!0,type:r.Json,format:"json",...n}),voiceProviderControllerAddVoice:(e,t,n={})=>this.request({path:`/${e}/voice/add`,method:"POST",body:t,secure:!0,type:r.Json,format:"json",...n})};v11Labs={voiceProviderControllerCloneVoices:(e,t={})=>this.request({path:"/11labs/voice/clone",method:"POST",body:e,secure:!0,type:r.FormData,...t})}}t.Api=i},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57175:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},62010:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.client=void 0,t.client=new(r(53053)).Api({baseUrl:"https://api.vapi.ai",baseApiParams:{secure:!0},securityWorker:async e=>{if(e)return{headers:{Authorization:`Bearer ${e}`}}}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69024:(e,t,r)=>{"use strict";r.d(t,{b:()=>o,s:()=>s});var n=r(43210),i=r(14163),a=r(60687),s=n.forwardRef((e,t)=>(0,a.jsx)(i.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));s.displayName="VisuallyHidden";var o=s},74075:e=>{"use strict";e.exports=require("zlib")},76104:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n={src:"/_next/static/media/Binghatti-Lisa.85c81ecb.jpeg",height:1586,width:1586,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/2wBDAQoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/wgARCAAIAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAX/xAAUAQEAAAAAAAAAAAAAAAAAAAAC/9oADAMBAAIQAxAAAACeA//EABsQAAEFAQEAAAAAAAAAAAAAAAECAwQREwAi/9oACAEBAAE/AG6e3khuoyZAbQNDWYR6SO//xAAVEQEBAAAAAAAAAAAAAAAAAAABAP/aAAgBAgEBPwAL/8QAFhEAAwAAAAAAAAAAAAAAAAAAAAFB/9oACAEDAQE/AHD/2Q==",blurWidth:8,blurHeight:8}},76242:(e,t,r)=>{"use strict";r.d(t,{Bc:()=>s,ZI:()=>c,k$:()=>l,m_:()=>o});var n=r(60687);r(43210);var i=r(9989),a=r(4780);function s({delayDuration:e=0,...t}){return(0,n.jsx)(i.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...t})}function o({...e}){return(0,n.jsx)(s,{children:(0,n.jsx)(i.bL,{"data-slot":"tooltip",...e})})}function l({...e}){return(0,n.jsx)(i.l9,{"data-slot":"tooltip-trigger",...e})}function c({className:e,sideOffset:t=0,children:r,...s}){return(0,n.jsx)(i.ZL,{children:(0,n.jsxs)(i.UC,{"data-slot":"tooltip-content",sideOffset:t,className:(0,a.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit rounded-md px-3 py-1.5 text-xs text-balance",e),...s,children:[r,(0,n.jsx)(i.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},78615:(e,t,r)=>{Promise.resolve().then(r.bind(r,29014))},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},81904:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},83997:e=>{"use strict";e.exports=require("tty")},84248:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\app\\\\(workspace)\\\\agents\\\\AgentsContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\AgentsContent.tsx","default")},87979:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(43210);function i(){let[e,t]=(0,n.useState)(null),[r,i]=(0,n.useState)(!0),[a,s]=(0,n.useState)(null),o=e?.role||null;return{user:e,userRole:o,authIsLoading:r,authError:a}}},91645:e=>{"use strict";e.exports=require("net")},93448:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(37413),i=r(84248);function a(){return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(i.default,{})})}},93500:(e,t,r)=>{"use strict";r.d(t,{$v:()=>f,EO:()=>u,Lt:()=>o,Rx:()=>m,Zr:()=>g,ck:()=>h,r7:()=>p,wd:()=>d});var n=r(60687);r(43210);var i=r(97895),a=r(4780),s=r(29523);function o({...e}){return(0,n.jsx)(i.bL,{"data-slot":"alert-dialog",...e})}function l({...e}){return(0,n.jsx)(i.ZL,{"data-slot":"alert-dialog-portal",...e})}function c({className:e,...t}){return(0,n.jsx)(i.hJ,{"data-slot":"alert-dialog-overlay",className:(0,a.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-650 bg-black/80",e),...t})}function u({className:e,...t}){return(0,n.jsxs)(l,{children:[(0,n.jsx)(c,{}),(0,n.jsx)(i.UC,{"data-slot":"alert-dialog-content",className:(0,a.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-650 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...t})]})}function d({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,a.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function h({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,a.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function p({className:e,...t}){return(0,n.jsx)(i.hE,{"data-slot":"alert-dialog-title",className:(0,a.cn)("text-lg font-semibold",e),...t})}function f({className:e,...t}){return(0,n.jsx)(i.VY,{"data-slot":"alert-dialog-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function m({className:e,...t}){return(0,n.jsx)(i.rc,{className:(0,a.cn)((0,s.r)(),e),...t})}function g({className:e,...t}){return(0,n.jsx)(i.ZD,{className:(0,a.cn)((0,s.r)({variant:"outline"}),e),...t})}},94735:e=>{"use strict";e.exports=require("events")},95534:(e,t,r)=>{"use strict";r.d(t,{G:()=>m});var n=r(60687),i=r(43210),a=r(29523),s=r(63503),o=r(89667),l=r(41862),c=r(48340),u=r(89757),d=r(97461),h=r.n(d),p=r(52581);r(90895);var f=r(4845);function m({isOpen:e,onClose:t,agent:r}){let[d,m]=(0,i.useState)({name:"",phoneNumber:""}),[g,v]=(0,i.useState)(!1),[y,b]=(0,i.useState)(null),_=async()=>{if(r){v(!0),b(null);try{let e=d.phoneNumber.replace(/\s+/g,""),n=e.startsWith("+")?e:`+${e}`,i=!1;try{await (0,u.SQ)({contactName:d.name,phoneNumber:n,campaigns:[]})}catch(e){if(e.message.includes("Conflict")||e.message.includes("already exists"))i=!0;else throw e}let a=(0,f.s)(n)||"",s=[{Name:d.name,MobileNumber:n}];await (0,u.G6)(r.id,s,a),p.o.success(i?"Call initiated with existing contact":"Contact created and call initiated"),t()}catch(e){console.error("Call error:",e),e.message.includes("already exists")?b("Contact already exists with this name and phone number"):b(e instanceof Error?e.message:"Failed to initiate call"),p.o.error("Failed to process request")}finally{v(!1)}}};return(0,n.jsx)(s.lG,{open:e,onOpenChange:e=>{e||(m({name:"",phoneNumber:""}),b(null)),t()},children:(0,n.jsxs)(s.Cf,{className:"sm:max-w-[425px]",children:[(0,n.jsxs)(s.c7,{children:[(0,n.jsx)(s.L3,{children:"Start Phone Call"}),(0,n.jsxs)(s.rr,{children:["Enter your details to start a call with",(0,n.jsx)("span",{className:"font-bold ml-1",children:r?.name})]})]}),(0,n.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"name",className:"text-sm font-medium",children:"Your Name"}),(0,n.jsx)(o.p,{id:"name",placeholder:"Enter your name",className:"mt-2",value:d.name,onChange:e=>m(t=>({...t,name:e.target.value}))})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("label",{htmlFor:"phone",className:"text-sm font-medium",children:"Phone Number"}),(0,n.jsx)(h(),{country:(0,f.u)(),value:d.phoneNumber,onChange:e=>m(t=>({...t,phoneNumber:`+${e}`})),containerClass:"mt-2",inputClass:"!w-full !h-10 !pl-[48px] !rounded-md !border !border-input !bg-background !px-3 !py-2 !text-sm !ring-offset-background file:!border-0 file:!bg-transparent file:!text-sm file:!font-medium placeholder:!text-muted-foreground focus-visible:!outline-none focus-visible:!ring-2 focus-visible:!ring-ring focus-visible:!ring-offset-2 disabled:!cursor-not-allowed disabled:!opacity-50",buttonClass:"!border-r-0 !bg-transparent !border !border-input",dropdownClass:"!bg-background !border !border-input",specialLabel:""})]}),y&&(0,n.jsx)("div",{className:"text-sm text-red-500 bg-red-50 dark:bg-red-900/20 p-3 rounded-md",children:y})]}),(0,n.jsxs)(s.Es,{children:[(0,n.jsx)(a.$,{variant:"outline",onClick:t,children:"Cancel"}),(0,n.jsx)(a.$,{onClick:_,disabled:g||!d.name||!d.phoneNumber,className:"bg-green-600 hover:bg-green-700 text-white",children:g?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(l.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Initiating..."]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(c.A,{className:"mr-2 h-4 w-4"}),"Start Call"]})})]})]})})}},96474:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96834:(e,t,r)=>{"use strict";r.d(t,{E:()=>l});var n=r(60687);r(43210);var i=r(8730),a=r(24224),s=r(4780);let o=(0,a.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,asChild:r=!1,...a}){let l=r?i.DX:"span";return(0,n.jsx)(l,{"data-slot":"badge",className:(0,s.cn)(o({variant:t}),e),...a})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[287,9176,7674,5814,598,5188,9677,6913,1476,4772,2093],()=>r(45734));module.exports=n})();