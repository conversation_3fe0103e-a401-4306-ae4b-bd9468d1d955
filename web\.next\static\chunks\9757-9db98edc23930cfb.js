"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9757],{1243:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Triangle<PERSON>lert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},6654:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return l}});let r=n(12115);function l(e,t){let n=(0,r.useRef)(null),l=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=l.current;t&&(l.current=null,t())}else e&&(n.current=i(e,r)),t&&(l.current=i(t,r))},[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},12324:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Columns2",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M12 3v18",key:"108xh3"}]])},13717:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},17580:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},17649:(e,t,n)=>{n.d(t,{UC:()=>O,VY:()=>I,ZD:()=>z,ZL:()=>L,bL:()=>S,hE:()=>j,hJ:()=>T,rc:()=>N});var r=n(12115),l=n(46081),i=n(6101),a=n(15452),o=n(85185),u=n(99708),s=n(95155),c="AlertDialog",[d,h]=(0,l.A)(c,[a.Hs]),f=(0,a.Hs)(),p=e=>{let{__scopeAlertDialog:t,...n}=e,r=f(t);return(0,s.jsx)(a.bL,{...r,...n,modal:!0})};p.displayName=c,r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,l=f(n);return(0,s.jsx)(a.l9,{...l,...r,ref:t})}).displayName="AlertDialogTrigger";var g=e=>{let{__scopeAlertDialog:t,...n}=e,r=f(t);return(0,s.jsx)(a.ZL,{...r,...n})};g.displayName="AlertDialogPortal";var v=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,l=f(n);return(0,s.jsx)(a.hJ,{...l,...r,ref:t})});v.displayName="AlertDialogOverlay";var y="AlertDialogContent",[b,m]=d(y),w=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,children:l,...c}=e,d=f(n),h=r.useRef(null),p=(0,i.s)(t,h),g=r.useRef(null);return(0,s.jsx)(a.G$,{contentName:y,titleName:x,docsSlug:"alert-dialog",children:(0,s.jsx)(b,{scope:n,cancelRef:g,children:(0,s.jsxs)(a.UC,{role:"alertdialog",...d,...c,ref:p,onOpenAutoFocus:(0,o.m)(c.onOpenAutoFocus,e=>{var t;e.preventDefault(),null===(t=g.current)||void 0===t||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,s.jsx)(u.xV,{children:l}),(0,s.jsx)(R,{contentRef:h})]})})})});w.displayName=y;var x="AlertDialogTitle",k=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,l=f(n);return(0,s.jsx)(a.hE,{...l,...r,ref:t})});k.displayName=x;var M="AlertDialogDescription",D=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,l=f(n);return(0,s.jsx)(a.VY,{...l,...r,ref:t})});D.displayName=M;var E=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,l=f(n);return(0,s.jsx)(a.bm,{...l,...r,ref:t})});E.displayName="AlertDialogAction";var C="AlertDialogCancel",A=r.forwardRef((e,t)=>{let{__scopeAlertDialog:n,...r}=e,{cancelRef:l}=m(C,n),o=f(n),u=(0,i.s)(t,l);return(0,s.jsx)(a.bm,{...o,...r,ref:u})});A.displayName=C;var R=e=>{let{contentRef:t}=e,n="`".concat(y,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(y,"` by passing a `").concat(M,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(y,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return r.useEffect(()=>{var e;document.getElementById(null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby"))||console.warn(n)},[n,t]),null},S=p,L=g,T=v,O=w,N=E,z=A,j=k,I=D},19420:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},23837:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("FileDown",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 18v-6",key:"17g6i2"}],["path",{d:"m9 15 3 3 3-3",key:"1npd3o"}]])},29676:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]])},29869:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},40133:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]])},40646:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},44020:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},47924:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},48021:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("GripVertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]])},50402:(e,t,n)=>{n.d(t,{JR:()=>M,_G:()=>c,be:()=>a,gB:()=>f,gl:()=>w});var r=n(12115),l=n(75143),i=n(78266);function a(e,t,n){let r=e.slice();return r.splice(n<0?r.length+n:n,0,r.splice(t,1)[0]),r}function o(e){return null!==e&&e>=0}let u=e=>{let{rects:t,activeIndex:n,overIndex:r,index:l}=e,i=a(t,r,n),o=t[l],u=i[l];return u&&o?{x:u.left-o.left,y:u.top-o.top,scaleX:u.width/o.width,scaleY:u.height/o.height}:null},s={scaleX:1,scaleY:1},c=e=>{var t;let{activeIndex:n,activeNodeRect:r,index:l,rects:i,overIndex:a}=e,o=null!=(t=i[n])?t:r;if(!o)return null;if(l===n){let e=i[a];return e?{x:0,y:n<a?e.top+e.height-(o.top+o.height):e.top-o.top,...s}:null}let u=function(e,t,n){let r=e[t],l=e[t-1],i=e[t+1];return r?n<t?l?r.top-(l.top+l.height):i?i.top-(r.top+r.height):0:i?i.top-(r.top+r.height):l?r.top-(l.top+l.height):0:0}(i,l,n);return l>n&&l<=a?{x:0,y:-o.height-u,...s}:l<n&&l>=a?{x:0,y:o.height+u,...s}:{x:0,y:0,...s}},d="Sortable",h=r.createContext({activeIndex:-1,containerId:d,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:u,disabled:{draggable:!1,droppable:!1}});function f(e){let{children:t,id:n,items:a,strategy:o=u,disabled:s=!1}=e,{active:c,dragOverlay:f,droppableRects:p,over:g,measureDroppableContainers:v}=(0,l.fF)(),y=(0,i.YG)(d,n),b=null!==f.rect,m=(0,r.useMemo)(()=>a.map(e=>"object"==typeof e&&"id"in e?e.id:e),[a]),w=null!=c,x=c?m.indexOf(c.id):-1,k=g?m.indexOf(g.id):-1,M=(0,r.useRef)(m),D=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}(m,M.current),E=-1!==k&&-1===x||D,C="boolean"==typeof s?{draggable:s,droppable:s}:s;(0,i.Es)(()=>{D&&w&&v(m)},[D,m,w,v]),(0,r.useEffect)(()=>{M.current=m},[m]);let A=(0,r.useMemo)(()=>({activeIndex:x,containerId:y,disabled:C,disableTransforms:E,items:m,overIndex:k,useDragOverlay:b,sortedRects:m.reduce((e,t,n)=>{let r=p.get(t);return r&&(e[n]=r),e},Array(m.length)),strategy:o}),[x,y,C.draggable,C.droppable,E,m,k,p,b,o]);return r.createElement(h.Provider,{value:A},t)}let p=e=>{let{id:t,items:n,activeIndex:r,overIndex:l}=e;return a(n,r,l).indexOf(t)},g=e=>{let{containerId:t,isSorting:n,wasDragging:r,index:l,items:i,newIndex:a,previousItems:o,previousContainerId:u,transition:s}=e;return!!s&&!!r&&(o===i||l!==a)&&(!!n||a!==l&&t===u)},v={duration:200,easing:"ease"},y="transform",b=i.Ks.Transition.toString({property:y,duration:0,easing:"linear"}),m={roleDescription:"sortable"};function w(e){var t,n,a,u;let{animateLayoutChanges:s=g,attributes:c,disabled:d,data:f,getNewIndex:w=p,id:x,strategy:k,resizeObserverConfig:M,transition:D=v}=e,{items:E,containerId:C,activeIndex:A,disabled:R,disableTransforms:S,sortedRects:L,overIndex:T,useDragOverlay:O,strategy:N}=(0,r.useContext)(h),z=(t=d,n=R,"boolean"==typeof t?{draggable:t,droppable:!1}:{draggable:null!=(a=null==t?void 0:t.draggable)?a:n.draggable,droppable:null!=(u=null==t?void 0:t.droppable)?u:n.droppable}),j=E.indexOf(x),I=(0,r.useMemo)(()=>({sortable:{containerId:C,index:j,items:E},...f}),[C,f,j,E]),P=(0,r.useMemo)(()=>E.slice(E.indexOf(x)),[E,x]),{rect:Y,node:F,isOver:B,setNodeRef:W}=(0,l.zM)({id:x,data:I,disabled:z.droppable,resizeObserverConfig:{updateMeasurementsFor:P,...M}}),{active:q,activatorEvent:K,activeNodeRect:U,attributes:H,setNodeRef:_,listeners:V,isDragging:G,over:X,setActivatorNodeRef:Z,transform:J}=(0,l.PM)({id:x,data:I,attributes:{...m,...c},disabled:z.draggable}),Q=(0,i.jn)(W,_),$=!!q,ee=$&&!S&&o(A)&&o(T),et=!O&&G,en=et&&ee?J:null,er=ee?null!=en?en:(null!=k?k:N)({rects:L,activeNodeRect:U,activeIndex:A,overIndex:T,index:j}):null,el=o(A)&&o(T)?w({id:x,items:E,activeIndex:A,overIndex:T}):j,ei=null==q?void 0:q.id,ea=(0,r.useRef)({activeId:ei,items:E,newIndex:el,containerId:C}),eo=E!==ea.current.items,eu=s({active:q,containerId:C,isDragging:G,isSorting:$,id:x,index:j,items:E,newIndex:ea.current.newIndex,previousItems:ea.current.items,previousContainerId:ea.current.containerId,transition:D,wasDragging:null!=ea.current.activeId}),es=function(e){let{disabled:t,index:n,node:a,rect:o}=e,[u,s]=(0,r.useState)(null),c=(0,r.useRef)(n);return(0,i.Es)(()=>{if(!t&&n!==c.current&&a.current){let e=o.current;if(e){let t=(0,l.Sj)(a.current,{ignoreTransform:!0}),n={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(n.x||n.y)&&s(n)}}n!==c.current&&(c.current=n)},[t,n,a,o]),(0,r.useEffect)(()=>{u&&s(null)},[u]),u}({disabled:!eu,index:j,node:F,rect:Y});return(0,r.useEffect)(()=>{$&&ea.current.newIndex!==el&&(ea.current.newIndex=el),C!==ea.current.containerId&&(ea.current.containerId=C),E!==ea.current.items&&(ea.current.items=E)},[$,el,C,E]),(0,r.useEffect)(()=>{if(ei===ea.current.activeId)return;if(null!=ei&&null==ea.current.activeId){ea.current.activeId=ei;return}let e=setTimeout(()=>{ea.current.activeId=ei},50);return()=>clearTimeout(e)},[ei]),{active:q,activeIndex:A,attributes:H,data:I,rect:Y,index:j,newIndex:el,items:E,isOver:B,isSorting:$,isDragging:G,listeners:V,node:F,overIndex:T,over:X,setNodeRef:Q,setActivatorNodeRef:Z,setDroppableNodeRef:W,setDraggableNodeRef:_,transform:null!=es?es:er,transition:es||eo&&ea.current.newIndex===j?b:(!et||(0,i.kx)(K))&&D&&($||eu)?i.Ks.Transition.toString({...D,property:y}):void 0}}function x(e){if(!e)return!1;let t=e.data.current;return!!t&&"sortable"in t&&"object"==typeof t.sortable&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable}let k=[l.vL.Down,l.vL.Right,l.vL.Up,l.vL.Left],M=(e,t)=>{let{context:{active:n,collisionRect:r,droppableRects:a,droppableContainers:o,over:u,scrollableAncestors:s}}=t;if(k.includes(e.code)){if(e.preventDefault(),!n||!r)return;let t=[];o.getEnabled().forEach(n=>{if(!n||null!=n&&n.disabled)return;let i=a.get(n.id);if(i)switch(e.code){case l.vL.Down:r.top<i.top&&t.push(n);break;case l.vL.Up:r.top>i.top&&t.push(n);break;case l.vL.Left:r.left>i.left&&t.push(n);break;case l.vL.Right:r.left<i.left&&t.push(n)}});let c=(0,l.y$)({active:n,collisionRect:r,droppableRects:a,droppableContainers:t,pointerCoordinates:null}),d=(0,l.Vy)(c,"id");if(d===(null==u?void 0:u.id)&&c.length>1&&(d=c[1].id),null!=d){let e=o.get(n.id),t=o.get(d),u=t?a.get(t.id):null,c=null==t?void 0:t.node.current;if(c&&u&&e&&t){let n=(0,l.sl)(c).some((e,t)=>s[t]!==e),a=D(e,t),o=function(e,t){return!!(x(e)&&x(t)&&D(e,t))&&e.data.current.sortable.index<t.data.current.sortable.index}(e,t),d=n||!a?{x:0,y:0}:{x:o?r.width-u.width:0,y:o?r.height-u.height:0},h={x:u.left,y:u.top};return d.x&&d.y?h:(0,i.Re)(h,d)}}}};function D(e,t){return!!(x(e)&&x(t))&&e.data.current.sortable.containerId===t.data.current.sortable.containerId}},51154:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},57434:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},62525:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},64261:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},69074:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71007:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},75143:(e,t,n)=>{let r;n.d(t,{Mp:()=>eY,vL:()=>o,uN:()=>ea,AN:()=>ed,fp:()=>z,y$:()=>j,Sj:()=>B,Vy:()=>O,sl:()=>q,fF:()=>eq,PM:()=>eW,zM:()=>eU,MS:()=>E,FR:()=>C});var l,i,a,o,u,s,c,d,h,f,p=n(12115),g=n(47650),v=n(78266);let y={display:"none"};function b(e){let{id:t,value:n}=e;return p.createElement("div",{id:t,style:y},n)}function m(e){let{id:t,announcement:n,ariaLiveType:r="assertive"}=e;return p.createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":r,"aria-atomic":!0},n)}let w=(0,p.createContext)(null),x={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},k={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was moved over droppable area "+n.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:n}=e;return n?"Draggable item "+t.id+" was dropped over droppable area "+n.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function M(e){let{announcements:t=k,container:n,hiddenTextDescribedById:r,screenReaderInstructions:l=x}=e,{announce:i,announcement:a}=function(){let[e,t]=(0,p.useState)("");return{announce:(0,p.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),o=(0,v.YG)("DndLiveRegion"),[u,s]=(0,p.useState)(!1);if((0,p.useEffect)(()=>{s(!0)},[]),!function(e){let t=(0,p.useContext)(w);(0,p.useEffect)(()=>{if(!t)throw Error("useDndMonitor must be used within a children of <DndContext>");return t(e)},[e,t])}((0,p.useMemo)(()=>({onDragStart(e){let{active:n}=e;i(t.onDragStart({active:n}))},onDragMove(e){let{active:n,over:r}=e;t.onDragMove&&i(t.onDragMove({active:n,over:r}))},onDragOver(e){let{active:n,over:r}=e;i(t.onDragOver({active:n,over:r}))},onDragEnd(e){let{active:n,over:r}=e;i(t.onDragEnd({active:n,over:r}))},onDragCancel(e){let{active:n,over:r}=e;i(t.onDragCancel({active:n,over:r}))}}),[i,t])),!u)return null;let c=p.createElement(p.Fragment,null,p.createElement(b,{id:r,value:l.draggable}),p.createElement(m,{id:o,announcement:a}));return n?(0,g.createPortal)(c,n):c}function D(){}function E(e,t){return(0,p.useMemo)(()=>({sensor:e,options:null!=t?t:{}}),[e,t])}function C(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,p.useMemo)(()=>[...t].filter(e=>null!=e),[...t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(l||(l={}));let A=Object.freeze({x:0,y:0});function R(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function S(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return n-r}function L(e,t){let{data:{value:n}}=e,{data:{value:r}}=t;return r-n}function T(e){let{left:t,top:n,height:r,width:l}=e;return[{x:t,y:n},{x:t+l,y:n},{x:t,y:n+r},{x:t+l,y:n+r}]}function O(e,t){if(!e||0===e.length)return null;let[n]=e;return t?n[t]:n}function N(e,t,n){return void 0===t&&(t=e.left),void 0===n&&(n=e.top),{x:t+.5*e.width,y:n+.5*e.height}}let z=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=N(t,t.left,t.top),i=[];for(let e of r){let{id:t}=e,r=n.get(t);if(r){let n=R(N(r),l);i.push({id:t,data:{droppableContainer:e,value:n}})}}return i.sort(S)},j=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=T(t),i=[];for(let e of r){let{id:t}=e,r=n.get(t);if(r){let n=T(r),a=Number((l.reduce((e,t,r)=>e+R(n[r],t),0)/4).toFixed(4));i.push({id:t,data:{droppableContainer:e,value:a}})}}return i.sort(S)},I=e=>{let{collisionRect:t,droppableRects:n,droppableContainers:r}=e,l=[];for(let e of r){let{id:r}=e,i=n.get(r);if(i){let n=function(e,t){let n=Math.max(t.top,e.top),r=Math.max(t.left,e.left),l=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height);if(r<l&&n<i){let a=t.width*t.height,o=e.width*e.height,u=(l-r)*(i-n);return Number((u/(a+o-u)).toFixed(4))}return 0}(i,t);n>0&&l.push({id:r,data:{droppableContainer:e,value:n}})}}return l.sort(L)};function P(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:A}let Y=function(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>({...t,top:t.top+e*n.y,bottom:t.bottom+e*n.y,left:t.left+e*n.x,right:t.right+e*n.x}),{...t})}}(1),F={ignoreTransform:!1};function B(e,t){void 0===t&&(t=F);let n=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:r}=(0,v.zk)(e).getComputedStyle(e);t&&(n=function(e,t,n){let r=function(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}(t);if(!r)return e;let{scaleX:l,scaleY:i,x:a,y:o}=r,u=e.left-a-(1-l)*parseFloat(n),s=e.top-o-(1-i)*parseFloat(n.slice(n.indexOf(" ")+1)),c=l?e.width/l:e.width,d=i?e.height/i:e.height;return{width:c,height:d,top:s,right:u+c,bottom:s+d,left:u}}(n,t,r))}let{top:r,left:l,width:i,height:a,bottom:o,right:u}=n;return{top:r,left:l,width:i,height:a,bottom:o,right:u}}function W(e){return B(e,{ignoreTransform:!0})}function q(e,t){let n=[];return e?function r(l){var i;if(null!=t&&n.length>=t||!l)return n;if((0,v.wz)(l)&&null!=l.scrollingElement&&!n.includes(l.scrollingElement))return n.push(l.scrollingElement),n;if(!(0,v.sb)(l)||(0,v.xZ)(l)||n.includes(l))return n;let a=(0,v.zk)(e).getComputedStyle(l);return(l!==e&&function(e,t){void 0===t&&(t=(0,v.zk)(e).getComputedStyle(e));let n=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let r=t[e];return"string"==typeof r&&n.test(r)})}(l,a)&&n.push(l),void 0===(i=a)&&(i=(0,v.zk)(l).getComputedStyle(l)),"fixed"===i.position)?n:r(l.parentNode)}(e):n}function K(e){let[t]=q(e,1);return null!=t?t:null}function U(e){return v.Sw&&e?(0,v.l6)(e)?e:(0,v.Ll)(e)?(0,v.wz)(e)||e===(0,v.TW)(e).scrollingElement?window:(0,v.sb)(e)?e:null:null:null}function H(e){return(0,v.l6)(e)?e.scrollX:e.scrollLeft}function _(e){return(0,v.l6)(e)?e.scrollY:e.scrollTop}function V(e){return{x:H(e),y:_(e)}}function G(e){return!!v.Sw&&!!e&&e===document.scrollingElement}function X(e){let t={x:0,y:0},n=G(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-n.width,y:e.scrollHeight-n.height},l=e.scrollTop<=t.y,i=e.scrollLeft<=t.x;return{isTop:l,isLeft:i,isBottom:e.scrollTop>=r.y,isRight:e.scrollLeft>=r.x,maxScroll:r,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(i||(i={}));let Z={x:.2,y:.2};function J(e){return e.reduce((e,t)=>(0,v.WQ)(e,V(t)),A)}let Q=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+H(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+_(t),0)}]];class ${constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let n=q(t),r=J(n);for(let[t,l,i]of(this.rect={...e},this.width=e.width,this.height=e.height,Q))for(let e of l)Object.defineProperty(this,e,{get:()=>{let l=i(n),a=r[t]-l;return this.rect[e]+a},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class ee{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,n){var r;null==(r=this.target)||r.addEventListener(e,t,n),this.listeners.push([e,t,n])}}function et(e,t){let n=Math.abs(e.x),r=Math.abs(e.y);return"number"==typeof t?Math.sqrt(n**2+r**2)>t:"x"in t&&"y"in t?n>t.x&&r>t.y:"x"in t?n>t.x:"y"in t&&r>t.y}function en(e){e.preventDefault()}function er(e){e.stopPropagation()}!function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"}(a||(a={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}(o||(o={}));let el={start:[o.Space,o.Enter],cancel:[o.Esc],end:[o.Space,o.Enter,o.Tab]},ei=(e,t)=>{let{currentCoordinates:n}=t;switch(e.code){case o.Right:return{...n,x:n.x+25};case o.Left:return{...n,x:n.x-25};case o.Down:return{...n,y:n.y+25};case o.Up:return{...n,y:n.y-25}}};class ea{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new ee((0,v.TW)(t)),this.windowListeners=new ee((0,v.zk)(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(a.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,n=e.node.current;n&&function(e,t){if(void 0===t&&(t=B),!e)return;let{top:n,left:r,bottom:l,right:i}=t(e);K(e)&&(l<=0||i<=0||n>=window.innerHeight||r>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}(n),t(A)}handleKeyDown(e){if((0,v.kx)(e)){let{active:t,context:n,options:r}=this.props,{keyboardCodes:l=el,coordinateGetter:i=ei,scrollBehavior:a="smooth"}=r,{code:u}=e;if(l.end.includes(u)){this.handleEnd(e);return}if(l.cancel.includes(u)){this.handleCancel(e);return}let{collisionRect:s}=n.current,c=s?{x:s.left,y:s.top}:A;this.referenceCoordinates||(this.referenceCoordinates=c);let d=i(e,{active:t,context:n.current,currentCoordinates:c});if(d){let t=(0,v.Re)(d,c),r={x:0,y:0},{scrollableAncestors:l}=n.current;for(let n of l){let l=e.code,{isTop:i,isRight:u,isLeft:s,isBottom:c,maxScroll:h,minScroll:f}=X(n),p=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:n,right:r,bottom:l}=e.getBoundingClientRect();return{top:t,left:n,right:r,bottom:l,width:e.clientWidth,height:e.clientHeight}}(n),g={x:Math.min(l===o.Right?p.right-p.width/2:p.right,Math.max(l===o.Right?p.left:p.left+p.width/2,d.x)),y:Math.min(l===o.Down?p.bottom-p.height/2:p.bottom,Math.max(l===o.Down?p.top:p.top+p.height/2,d.y))},v=l===o.Right&&!u||l===o.Left&&!s,y=l===o.Down&&!c||l===o.Up&&!i;if(v&&g.x!==d.x){let e=n.scrollLeft+t.x,i=l===o.Right&&e<=h.x||l===o.Left&&e>=f.x;if(i&&!t.y){n.scrollTo({left:e,behavior:a});return}i?r.x=n.scrollLeft-e:r.x=l===o.Right?n.scrollLeft-h.x:n.scrollLeft-f.x,r.x&&n.scrollBy({left:-r.x,behavior:a});break}if(y&&g.y!==d.y){let e=n.scrollTop+t.y,i=l===o.Down&&e<=h.y||l===o.Up&&e>=f.y;if(i&&!t.x){n.scrollTo({top:e,behavior:a});return}i?r.y=n.scrollTop-e:r.y=l===o.Down?n.scrollTop-h.y:n.scrollTop-f.y,r.y&&n.scrollBy({top:-r.y,behavior:a});break}}this.handleMove(e,(0,v.WQ)((0,v.Re)(d,this.referenceCoordinates),r))}}}handleMove(e,t){let{onMove:n}=this.props;e.preventDefault(),n(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function eo(e){return!!(e&&"distance"in e)}function eu(e){return!!(e&&"delay"in e)}ea.activators=[{eventName:"onKeyDown",handler:(e,t,n)=>{let{keyboardCodes:r=el,onActivation:l}=t,{active:i}=n,{code:a}=e.nativeEvent;if(r.start.includes(a)){let t=i.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==l||l({event:e.nativeEvent}),!0)}return!1}}];class es{constructor(e,t,n){var r;void 0===n&&(n=function(e){let{EventTarget:t}=(0,v.zk)(e);return e instanceof t?e:(0,v.TW)(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:l}=e,{target:i}=l;this.props=e,this.events=t,this.document=(0,v.TW)(i),this.documentListeners=new ee(this.document),this.listeners=new ee(n),this.windowListeners=new ee((0,v.zk)(i)),this.initialCoordinates=null!=(r=(0,v.e_)(l))?r:A,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:n}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(a.Resize,this.handleCancel),this.windowListeners.add(a.DragStart,en),this.windowListeners.add(a.VisibilityChange,this.handleCancel),this.windowListeners.add(a.ContextMenu,en),this.documentListeners.add(a.Keydown,this.handleKeydown),t){if(null!=n&&n({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(eu(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(eo(t)){this.handlePending(t);return}}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){let{active:n,onPending:r}=this.props;r(n,e,this.initialCoordinates,t)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(a.Click,er,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(a.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:n,initialCoordinates:r,props:l}=this,{onMove:i,options:{activationConstraint:a}}=l;if(!r)return;let o=null!=(t=(0,v.e_)(e))?t:A,u=(0,v.Re)(r,o);if(!n&&a){if(eo(a)){if(null!=a.tolerance&&et(u,a.tolerance))return this.handleCancel();if(et(u,a.distance))return this.handleStart()}return eu(a)&&et(u,a.tolerance)?this.handleCancel():void this.handlePending(a,u)}e.cancelable&&e.preventDefault(),i(o)}handleEnd(){let{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){let{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===o.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let ec={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class ed extends es{constructor(e){let{event:t}=e;super(e,ec,(0,v.TW)(t.target))}}ed.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return!!n.isPrimary&&0===n.button&&(null==r||r({event:n}),!0)}}];let eh={move:{name:"mousemove"},end:{name:"mouseup"}};!function(e){e[e.RightClick=2]="RightClick"}(u||(u={}));class ef extends es{constructor(e){super(e,eh,(0,v.TW)(e.event.target))}}ef.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t;return n.button!==u.RightClick&&(null==r||r({event:n}),!0)}}];let ep={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class eg extends es{constructor(e){super(e,ep)}static setup(){return window.addEventListener(ep.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(ep.move.name,e)};function e(){}}}eg.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:n}=e,{onActivation:r}=t,{touches:l}=n;return!(l.length>1)&&(null==r||r({event:n}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(s||(s={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(c||(c={}));let ev={x:{[i.Backward]:!1,[i.Forward]:!1},y:{[i.Backward]:!1,[i.Forward]:!1}};!function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"}(d||(d={})),(h||(h={})).Optimized="optimized";let ey=new Map;function eb(e,t){return(0,v.KG)(n=>e?n||("function"==typeof t?t(e):e):null,[t,e])}function em(e){let{callback:t,disabled:n}=e,r=(0,v._q)(t),l=(0,p.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(r)},[n]);return(0,p.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}function ew(e){return new $(B(e),e)}function ex(e,t,n){void 0===t&&(t=ew);let[r,l]=(0,p.useState)(null);function i(){l(r=>{if(!e)return null;if(!1===e.isConnected){var l;return null!=(l=null!=r?r:n)?l:null}let i=t(e);return JSON.stringify(r)===JSON.stringify(i)?r:i})}let a=function(e){let{callback:t,disabled:n}=e,r=(0,v._q)(t),l=(0,p.useMemo)(()=>{if(n||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(r)},[r,n]);return(0,p.useEffect)(()=>()=>null==l?void 0:l.disconnect(),[l]),l}({callback(t){if(e)for(let n of t){let{type:t,target:r}=n;if("childList"===t&&r instanceof HTMLElement&&r.contains(e)){i();break}}}}),o=em({callback:i});return(0,v.Es)(()=>{i(),e?(null==o||o.observe(e),null==a||a.observe(document.body,{childList:!0,subtree:!0})):(null==o||o.disconnect(),null==a||a.disconnect())},[e]),r}let ek=[];function eM(e,t){void 0===t&&(t=[]);let n=(0,p.useRef)(null);return(0,p.useEffect)(()=>{n.current=null},t),(0,p.useEffect)(()=>{let t=e!==A;t&&!n.current&&(n.current=e),!t&&n.current&&(n.current=null)},[e]),n.current?(0,v.Re)(e,n.current):A}function eD(e){return(0,p.useMemo)(()=>e?function(e){let t=e.innerWidth,n=e.innerHeight;return{top:0,left:0,right:t,bottom:n,width:t,height:n}}(e):null,[e])}let eE=[],eC=[{sensor:ed,options:{}},{sensor:ea,options:{}}],eA={current:{}},eR={draggable:{measure:W},droppable:{measure:W,strategy:d.WhileDragging,frequency:h.Optimized},dragOverlay:{measure:B}};class eS extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,n;return null!=(t=null==(n=this.get(e))?void 0:n.node.current)?t:void 0}}let eL={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new eS,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:D},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:eR,measureDroppableContainers:D,windowRect:null,measuringScheduled:!1},eT={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:D,draggableNodes:new Map,over:null,measureDroppableContainers:D},eO=(0,p.createContext)(eT),eN=(0,p.createContext)(eL);function ez(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new eS}}}function ej(e,t){switch(t.type){case l.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case l.DragMove:if(null==e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case l.DragEnd:case l.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case l.RegisterDroppable:{let{element:n}=t,{id:r}=n,l=new eS(e.droppable.containers);return l.set(r,n),{...e,droppable:{...e.droppable,containers:l}}}case l.SetDroppableDisabled:{let{id:n,key:r,disabled:l}=t,i=e.droppable.containers.get(n);if(!i||r!==i.key)return e;let a=new eS(e.droppable.containers);return a.set(n,{...i,disabled:l}),{...e,droppable:{...e.droppable,containers:a}}}case l.UnregisterDroppable:{let{id:n,key:r}=t,l=e.droppable.containers.get(n);if(!l||r!==l.key)return e;let i=new eS(e.droppable.containers);return i.delete(n),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function eI(e){let{disabled:t}=e,{active:n,activatorEvent:r,draggableNodes:l}=(0,p.useContext)(eO),i=(0,v.ZC)(r),a=(0,v.ZC)(null==n?void 0:n.id);return(0,p.useEffect)(()=>{if(!t&&!r&&i&&null!=a){if(!(0,v.kx)(i)||document.activeElement===i.target)return;let e=l.get(a);if(!e)return;let{activatorNode:t,node:n}=e;if(t.current||n.current)requestAnimationFrame(()=>{for(let e of[t.current,n.current]){if(!e)continue;let t=(0,v.ag)(e);if(t){t.focus();break}}})}},[r,t,l,a,i]),null}let eP=(0,p.createContext)({...A,scaleX:1,scaleY:1});!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(f||(f={}));let eY=(0,p.memo)(function(e){var t,n,r,a,o,u;let{id:h,accessibility:y,autoScroll:b=!0,children:m,sensors:x=eC,collisionDetection:k=I,measuring:D,modifiers:E,...C}=e,[R,S]=(0,p.useReducer)(ej,void 0,ez),[L,T]=function(){let[e]=(0,p.useState)(()=>new Set),t=(0,p.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,p.useCallback)(t=>{let{type:n,event:r}=t;e.forEach(e=>{var t;return null==(t=e[n])?void 0:t.call(e,r)})},[e]),t]}(),[N,z]=(0,p.useState)(f.Uninitialized),j=N===f.Initialized,{draggable:{active:F,nodes:W,translate:H},droppable:{containers:_}}=R,Q=null!=F?W.get(F):null,ee=(0,p.useRef)({initial:null,translated:null}),et=(0,p.useMemo)(()=>{var e;return null!=F?{id:F,data:null!=(e=null==Q?void 0:Q.data)?e:eA,rect:ee}:null},[F,Q]),en=(0,p.useRef)(null),[er,el]=(0,p.useState)(null),[ei,ea]=(0,p.useState)(null),eo=(0,v.YN)(C,Object.values(C)),eu=(0,v.YG)("DndDescribedBy",h),es=(0,p.useMemo)(()=>_.getEnabled(),[_]),ec=(0,p.useMemo)(()=>({draggable:{...eR.draggable,...null==D?void 0:D.draggable},droppable:{...eR.droppable,...null==D?void 0:D.droppable},dragOverlay:{...eR.dragOverlay,...null==D?void 0:D.dragOverlay}}),[null==D?void 0:D.draggable,null==D?void 0:D.droppable,null==D?void 0:D.dragOverlay]),{droppableRects:ed,measureDroppableContainers:eh,measuringScheduled:ef}=function(e,t){let{dragging:n,dependencies:r,config:l}=t,[i,a]=(0,p.useState)(null),{frequency:o,measure:u,strategy:s}=l,c=(0,p.useRef)(e),h=function(){switch(s){case d.Always:return!1;case d.BeforeDragging:return n;default:return!n}}(),f=(0,v.YN)(h),g=(0,p.useCallback)(function(e){void 0===e&&(e=[]),!f.current&&a(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[f]),y=(0,p.useRef)(null),b=(0,v.KG)(t=>{if(h&&!n)return ey;if(!t||t===ey||c.current!==e||null!=i){let t=new Map;for(let n of e){if(!n)continue;if(i&&i.length>0&&!i.includes(n.id)&&n.rect.current){t.set(n.id,n.rect.current);continue}let e=n.node.current,r=e?new $(u(e),e):null;n.rect.current=r,r&&t.set(n.id,r)}return t}return t},[e,i,n,h,u]);return(0,p.useEffect)(()=>{c.current=e},[e]),(0,p.useEffect)(()=>{!h&&g()},[n,h]),(0,p.useEffect)(()=>{i&&i.length>0&&a(null)},[JSON.stringify(i)]),(0,p.useEffect)(()=>{!h&&"number"==typeof o&&null===y.current&&(y.current=setTimeout(()=>{g(),y.current=null},o))},[o,h,g,...r]),{droppableRects:b,measureDroppableContainers:g,measuringScheduled:null!=i}}(es,{dragging:j,dependencies:[H.x,H.y],config:ec.droppable}),ep=function(e,t){let n=null!=t?e.get(t):void 0,r=n?n.node.current:null;return(0,v.KG)(e=>{var n;return null==t?null:null!=(n=null!=r?r:e)?n:null},[r,t])}(W,F),eg=(0,p.useMemo)(()=>ei?(0,v.e_)(ei):null,[ei]),ew=function(){let e=(null==er?void 0:er.autoScrollEnabled)===!1,t="object"==typeof b?!1===b.enabled:!1===b,n=j&&!e&&!t;return"object"==typeof b?{...b,enabled:n}:{enabled:n}}(),eS=eb(ep,ec.draggable.measure);!function(e){let{activeNode:t,measure:n,initialRect:r,config:l=!0}=e,i=(0,p.useRef)(!1),{x:a,y:o}="boolean"==typeof l?{x:l,y:l}:l;(0,v.Es)(()=>{if(!a&&!o||!t){i.current=!1;return}if(i.current||!r)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let l=P(n(e),r);if(a||(l.x=0),o||(l.y=0),i.current=!0,Math.abs(l.x)>0||Math.abs(l.y)>0){let t=K(e);t&&t.scrollBy({top:l.y,left:l.x})}},[t,a,o,r,n])}({activeNode:null!=F?W.get(F):null,config:ew.layoutShiftCompensation,initialRect:eS,measure:ec.draggable.measure});let eL=ex(ep,ec.draggable.measure,eS),eT=ex(ep?ep.parentElement:null),eY=(0,p.useRef)({activatorEvent:null,active:null,activeNode:ep,collisionRect:null,collisions:null,droppableRects:ed,draggableNodes:W,draggingNode:null,draggingNodeRect:null,droppableContainers:_,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),eF=_.getNodeFor(null==(t=eY.current.over)?void 0:t.id),eB=function(e){let{measure:t}=e,[n,r]=(0,p.useState)(null),l=em({callback:(0,p.useCallback)(e=>{for(let{target:n}of e)if((0,v.sb)(n)){r(e=>{let r=t(n);return e?{...e,width:r.width,height:r.height}:r});break}},[t])}),i=(0,p.useCallback)(e=>{let n=function(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return(0,v.sb)(t)?t:e}(e);null==l||l.disconnect(),n&&(null==l||l.observe(n)),r(n?t(n):null)},[t,l]),[a,o]=(0,v.lk)(i);return(0,p.useMemo)(()=>({nodeRef:a,rect:n,setRef:o}),[n,a,o])}({measure:ec.dragOverlay.measure}),eW=null!=(n=eB.nodeRef.current)?n:ep,eq=j?null!=(r=eB.rect)?r:eL:null,eK=!!(eB.nodeRef.current&&eB.rect),eU=function(e){let t=eb(e);return P(e,t)}(eK?null:eL),eH=eD(eW?(0,v.zk)(eW):null),e_=function(e){let t=(0,p.useRef)(e),n=(0,v.KG)(n=>e?n&&n!==ek&&e&&t.current&&e.parentNode===t.current.parentNode?n:q(e):ek,[e]);return(0,p.useEffect)(()=>{t.current=e},[e]),n}(j?null!=eF?eF:ep:null),eV=function(e,t){void 0===t&&(t=B);let[n]=e,r=eD(n?(0,v.zk)(n):null),[l,i]=(0,p.useState)(eE);function a(){i(()=>e.length?e.map(e=>G(e)?r:new $(t(e),e)):eE)}let o=em({callback:a});return(0,v.Es)(()=>{null==o||o.disconnect(),a(),e.forEach(e=>null==o?void 0:o.observe(e))},[e]),l}(e_),eG=function(e,t){let{transform:n,...r}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...r}),n):n}(E,{transform:{x:H.x-eU.x,y:H.y-eU.y,scaleX:1,scaleY:1},activatorEvent:ei,active:et,activeNodeRect:eL,containerNodeRect:eT,draggingNodeRect:eq,over:eY.current.over,overlayNodeRect:eB.rect,scrollableAncestors:e_,scrollableAncestorRects:eV,windowRect:eH}),eX=eg?(0,v.WQ)(eg,H):null,eZ=function(e){let[t,n]=(0,p.useState)(null),r=(0,p.useRef)(e),l=(0,p.useCallback)(e=>{let t=U(e.target);t&&n(e=>e?(e.set(t,V(t)),new Map(e)):null)},[]);return(0,p.useEffect)(()=>{let t=r.current;if(e!==t){i(t);let a=e.map(e=>{let t=U(e);return t?(t.addEventListener("scroll",l,{passive:!0}),[t,V(t)]):null}).filter(e=>null!=e);n(a.length?new Map(a):null),r.current=e}return()=>{i(e),i(t)};function i(e){e.forEach(e=>{let t=U(e);null==t||t.removeEventListener("scroll",l)})}},[l,e]),(0,p.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>(0,v.WQ)(e,t),A):J(e):A,[e,t])}(e_),eJ=eM(eZ),eQ=eM(eZ,[eL]),e$=(0,v.WQ)(eG,eJ),e0=eq?Y(eq,eG):null,e1=et&&e0?k({active:et,collisionRect:e0,droppableRects:ed,droppableContainers:es,pointerCoordinates:eX}):null,e2=O(e1,"id"),[e4,e9]=(0,p.useState)(null),e7=(o=eK?eG:(0,v.WQ)(eG,eQ),u=null!=(a=null==e4?void 0:e4.rect)?a:null,{...o,scaleX:u&&eL?u.width/eL.width:1,scaleY:u&&eL?u.height/eL.height:1}),e6=(0,p.useRef)(null),e5=(0,p.useCallback)((e,t)=>{let{sensor:n,options:r}=t;if(null==en.current)return;let i=W.get(en.current);if(!i)return;let a=e.nativeEvent,o=new n({active:en.current,activeNode:i,event:a,options:r,context:eY,onAbort(e){if(!W.get(e))return;let{onDragAbort:t}=eo.current,n={id:e};null==t||t(n),L({type:"onDragAbort",event:n})},onPending(e,t,n,r){if(!W.get(e))return;let{onDragPending:l}=eo.current,i={id:e,constraint:t,initialCoordinates:n,offset:r};null==l||l(i),L({type:"onDragPending",event:i})},onStart(e){let t=en.current;if(null==t)return;let n=W.get(t);if(!n)return;let{onDragStart:r}=eo.current,i={activatorEvent:a,active:{id:t,data:n.data,rect:ee}};(0,g.unstable_batchedUpdates)(()=>{null==r||r(i),z(f.Initializing),S({type:l.DragStart,initialCoordinates:e,active:t}),L({type:"onDragStart",event:i}),el(e6.current),ea(a)})},onMove(e){S({type:l.DragMove,coordinates:e})},onEnd:u(l.DragEnd),onCancel:u(l.DragCancel)});function u(e){return async function(){let{active:t,collisions:n,over:r,scrollAdjustedTranslate:i}=eY.current,o=null;if(t&&i){let{cancelDrop:u}=eo.current;o={activatorEvent:a,active:t,collisions:n,delta:i,over:r},e===l.DragEnd&&"function"==typeof u&&await Promise.resolve(u(o))&&(e=l.DragCancel)}en.current=null,(0,g.unstable_batchedUpdates)(()=>{S({type:e}),z(f.Uninitialized),e9(null),el(null),ea(null),e6.current=null;let t=e===l.DragEnd?"onDragEnd":"onDragCancel";if(o){let e=eo.current[t];null==e||e(o),L({type:t,event:o})}})}}e6.current=o},[W]),e3=(0,p.useCallback)((e,t)=>(n,r)=>{let l=n.nativeEvent,i=W.get(r);null===en.current&&i&&!l.dndKit&&!l.defaultPrevented&&!0===e(n,t.options,{active:i})&&(l.dndKit={capturedBy:t.sensor},en.current=r,e5(n,t))},[W,e5]),e8=(0,p.useMemo)(()=>x.reduce((e,t)=>{let{sensor:n}=t;return[...e,...n.activators.map(e=>({eventName:e.eventName,handler:e3(e.handler,t)}))]},[]),[x,e3]);(0,p.useEffect)(()=>{if(!v.Sw)return;let e=x.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},x.map(e=>{let{sensor:t}=e;return t})),(0,v.Es)(()=>{eL&&N===f.Initializing&&z(f.Initialized)},[eL,N]),(0,p.useEffect)(()=>{let{onDragMove:e}=eo.current,{active:t,activatorEvent:n,collisions:r,over:l}=eY.current;if(!t||!n)return;let i={active:t,activatorEvent:n,collisions:r,delta:{x:e$.x,y:e$.y},over:l};(0,g.unstable_batchedUpdates)(()=>{null==e||e(i),L({type:"onDragMove",event:i})})},[e$.x,e$.y]),(0,p.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:n,droppableContainers:r,scrollAdjustedTranslate:l}=eY.current;if(!e||null==en.current||!t||!l)return;let{onDragOver:i}=eo.current,a=r.get(e2),o=a&&a.rect.current?{id:a.id,rect:a.rect.current,data:a.data,disabled:a.disabled}:null,u={active:e,activatorEvent:t,collisions:n,delta:{x:l.x,y:l.y},over:o};(0,g.unstable_batchedUpdates)(()=>{e9(o),null==i||i(u),L({type:"onDragOver",event:u})})},[e2]),(0,v.Es)(()=>{eY.current={activatorEvent:ei,active:et,activeNode:ep,collisionRect:e0,collisions:e1,droppableRects:ed,draggableNodes:W,draggingNode:eW,draggingNodeRect:eq,droppableContainers:_,over:e4,scrollableAncestors:e_,scrollAdjustedTranslate:e$},ee.current={initial:eq,translated:e0}},[et,ep,e1,e0,W,eW,eq,ed,_,e4,e_,e$]),function(e){let{acceleration:t,activator:n=s.Pointer,canScroll:r,draggingRect:l,enabled:a,interval:o=5,order:u=c.TreeOrder,pointerCoordinates:d,scrollableAncestors:h,scrollableAncestorRects:f,delta:g,threshold:y}=e,b=function(e){let{delta:t,disabled:n}=e,r=(0,v.ZC)(t);return(0,v.KG)(e=>{if(n||!r||!e)return ev;let l={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[i.Backward]:e.x[i.Backward]||-1===l.x,[i.Forward]:e.x[i.Forward]||1===l.x},y:{[i.Backward]:e.y[i.Backward]||-1===l.y,[i.Forward]:e.y[i.Forward]||1===l.y}}},[n,t,r])}({delta:g,disabled:!a}),[m,w]=(0,v.$$)(),x=(0,p.useRef)({x:0,y:0}),k=(0,p.useRef)({x:0,y:0}),M=(0,p.useMemo)(()=>{switch(n){case s.Pointer:return d?{top:d.y,bottom:d.y,left:d.x,right:d.x}:null;case s.DraggableRect:return l}},[n,l,d]),D=(0,p.useRef)(null),E=(0,p.useCallback)(()=>{let e=D.current;if(!e)return;let t=x.current.x*k.current.x,n=x.current.y*k.current.y;e.scrollBy(t,n)},[]),C=(0,p.useMemo)(()=>u===c.TreeOrder?[...h].reverse():h,[u,h]);(0,p.useEffect)(()=>{if(!a||!h.length||!M){w();return}for(let e of C){if((null==r?void 0:r(e))===!1)continue;let n=f[h.indexOf(e)];if(!n)continue;let{direction:l,speed:a}=function(e,t,n,r,l){let{top:a,left:o,right:u,bottom:s}=n;void 0===r&&(r=10),void 0===l&&(l=Z);let{isTop:c,isBottom:d,isLeft:h,isRight:f}=X(e),p={x:0,y:0},g={x:0,y:0},v={height:t.height*l.y,width:t.width*l.x};return!c&&a<=t.top+v.height?(p.y=i.Backward,g.y=r*Math.abs((t.top+v.height-a)/v.height)):!d&&s>=t.bottom-v.height&&(p.y=i.Forward,g.y=r*Math.abs((t.bottom-v.height-s)/v.height)),!f&&u>=t.right-v.width?(p.x=i.Forward,g.x=r*Math.abs((t.right-v.width-u)/v.width)):!h&&o<=t.left+v.width&&(p.x=i.Backward,g.x=r*Math.abs((t.left+v.width-o)/v.width)),{direction:p,speed:g}}(e,n,M,t,y);for(let e of["x","y"])b[e][l[e]]||(a[e]=0,l[e]=0);if(a.x>0||a.y>0){w(),D.current=e,m(E,o),x.current=a,k.current=l;return}}x.current={x:0,y:0},k.current={x:0,y:0},w()},[t,E,r,w,a,o,JSON.stringify(M),JSON.stringify(b),m,h,C,f,JSON.stringify(y)])}({...ew,delta:H,draggingRect:e0,pointerCoordinates:eX,scrollableAncestors:e_,scrollableAncestorRects:eV});let te=(0,p.useMemo)(()=>({active:et,activeNode:ep,activeNodeRect:eL,activatorEvent:ei,collisions:e1,containerNodeRect:eT,dragOverlay:eB,draggableNodes:W,droppableContainers:_,droppableRects:ed,over:e4,measureDroppableContainers:eh,scrollableAncestors:e_,scrollableAncestorRects:eV,measuringConfiguration:ec,measuringScheduled:ef,windowRect:eH}),[et,ep,eL,ei,e1,eT,eB,W,_,ed,e4,eh,e_,eV,ec,ef,eH]),tt=(0,p.useMemo)(()=>({activatorEvent:ei,activators:e8,active:et,activeNodeRect:eL,ariaDescribedById:{draggable:eu},dispatch:S,draggableNodes:W,over:e4,measureDroppableContainers:eh}),[ei,e8,et,eL,S,eu,W,e4,eh]);return p.createElement(w.Provider,{value:T},p.createElement(eO.Provider,{value:tt},p.createElement(eN.Provider,{value:te},p.createElement(eP.Provider,{value:e7},m)),p.createElement(eI,{disabled:(null==y?void 0:y.restoreFocus)===!1})),p.createElement(M,{...y,hiddenTextDescribedById:eu}))}),eF=(0,p.createContext)(null),eB="button";function eW(e){let{id:t,data:n,disabled:r=!1,attributes:l}=e,i=(0,v.YG)("Draggable"),{activators:a,activatorEvent:o,active:u,activeNodeRect:s,ariaDescribedById:c,draggableNodes:d,over:h}=(0,p.useContext)(eO),{role:f=eB,roleDescription:g="draggable",tabIndex:y=0}=null!=l?l:{},b=(null==u?void 0:u.id)===t,m=(0,p.useContext)(b?eP:eF),[w,x]=(0,v.lk)(),[k,M]=(0,v.lk)(),D=(0,p.useMemo)(()=>a.reduce((e,n)=>{let{eventName:r,handler:l}=n;return e[r]=e=>{l(e,t)},e},{}),[a,t]),E=(0,v.YN)(n);return(0,v.Es)(()=>(d.set(t,{id:t,key:i,node:w,activatorNode:k,data:E}),()=>{let e=d.get(t);e&&e.key===i&&d.delete(t)}),[d,t]),{active:u,activatorEvent:o,activeNodeRect:s,attributes:(0,p.useMemo)(()=>({role:f,tabIndex:y,"aria-disabled":r,"aria-pressed":!!b&&f===eB||void 0,"aria-roledescription":g,"aria-describedby":c.draggable}),[r,f,y,b,g,c.draggable]),isDragging:b,listeners:r?void 0:D,node:w,over:h,setNodeRef:x,setActivatorNodeRef:M,transform:m}}function eq(){return(0,p.useContext)(eN)}let eK={timeout:25};function eU(e){let{data:t,disabled:n=!1,id:r,resizeObserverConfig:i}=e,a=(0,v.YG)("Droppable"),{active:o,dispatch:u,over:s,measureDroppableContainers:c}=(0,p.useContext)(eO),d=(0,p.useRef)({disabled:n}),h=(0,p.useRef)(!1),f=(0,p.useRef)(null),g=(0,p.useRef)(null),{disabled:y,updateMeasurementsFor:b,timeout:m}={...eK,...i},w=(0,v.YN)(null!=b?b:r),x=em({callback:(0,p.useCallback)(()=>{if(!h.current){h.current=!0;return}null!=g.current&&clearTimeout(g.current),g.current=setTimeout(()=>{c(Array.isArray(w.current)?w.current:[w.current]),g.current=null},m)},[m]),disabled:y||!o}),k=(0,p.useCallback)((e,t)=>{x&&(t&&(x.unobserve(t),h.current=!1),e&&x.observe(e))},[x]),[M,D]=(0,v.lk)(k),E=(0,v.YN)(t);return(0,p.useEffect)(()=>{x&&M.current&&(x.disconnect(),h.current=!1,x.observe(M.current))},[M,x]),(0,p.useEffect)(()=>(u({type:l.RegisterDroppable,element:{id:r,key:a,disabled:n,node:M,rect:f,data:E}}),()=>u({type:l.UnregisterDroppable,key:a,id:r})),[r]),(0,p.useEffect)(()=>{n!==d.current.disabled&&(u({type:l.SetDroppableDisabled,id:r,key:a,disabled:n}),d.current.disabled=n)},[r,a,n,u]),{active:o,rect:f,isOver:(null==s?void 0:s.id)===r,node:M,over:s,setNodeRef:D}}r={styles:{active:{opacity:"0"}}},e=>{let{active:t,dragOverlay:n}=e,l={},{styles:i,className:a}=r;if(null!=i&&i.active)for(let[e,n]of Object.entries(i.active))void 0!==n&&(l[e]=t.node.style.getPropertyValue(e),t.node.style.setProperty(e,n));if(null!=i&&i.dragOverlay)for(let[e,t]of Object.entries(i.dragOverlay))void 0!==t&&n.node.style.setProperty(e,t);return null!=a&&a.active&&t.node.classList.add(a.active),null!=a&&a.dragOverlay&&n.node.classList.add(a.dragOverlay),function(){for(let[e,n]of Object.entries(l))t.node.style.setProperty(e,n);null!=a&&a.active&&t.node.classList.remove(a.active)}}},78266:(e,t,n)=>{n.d(t,{$$:()=>g,Es:()=>f,KG:()=>y,Ks:()=>A,Ll:()=>o,Re:()=>D,Sw:()=>i,TW:()=>h,WQ:()=>M,YG:()=>x,YN:()=>v,ZC:()=>m,_q:()=>p,ag:()=>S,e_:()=>C,jn:()=>l,kx:()=>E,l6:()=>a,lk:()=>b,sb:()=>c,wz:()=>s,xZ:()=>d,zk:()=>u});var r=n(12115);function l(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.useMemo)(()=>e=>{t.forEach(t=>t(e))},t)}let i="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function a(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function o(e){return"nodeType"in e}function u(e){var t,n;return e?a(e)?e:o(e)&&null!=(t=null==(n=e.ownerDocument)?void 0:n.defaultView)?t:window:window}function s(e){let{Document:t}=u(e);return e instanceof t}function c(e){return!a(e)&&e instanceof u(e).HTMLElement}function d(e){return e instanceof u(e).SVGElement}function h(e){return e?a(e)?e.document:o(e)?s(e)?e:c(e)||d(e)?e.ownerDocument:document:document:document}let f=i?r.useLayoutEffect:r.useEffect;function p(e){let t=(0,r.useRef)(e);return f(()=>{t.current=e}),(0,r.useCallback)(function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current(...n)},[])}function g(){let e=(0,r.useRef)(null);return[(0,r.useCallback)((t,n)=>{e.current=setInterval(t,n)},[]),(0,r.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}function v(e,t){void 0===t&&(t=[e]);let n=(0,r.useRef)(e);return f(()=>{n.current!==e&&(n.current=e)},t),n}function y(e,t){let n=(0,r.useRef)();return(0,r.useMemo)(()=>{let t=e(n.current);return n.current=t,t},[...t])}function b(e){let t=p(e),n=(0,r.useRef)(null),l=(0,r.useCallback)(e=>{e!==n.current&&(null==t||t(e,n.current)),n.current=e},[]);return[n,l]}function m(e){let t=(0,r.useRef)();return(0,r.useEffect)(()=>{t.current=e},[e]),t.current}let w={};function x(e,t){return(0,r.useMemo)(()=>{if(t)return t;let n=null==w[e]?0:w[e]+1;return w[e]=n,e+"-"+n},[e,t])}function k(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),l=1;l<n;l++)r[l-1]=arguments[l];return r.reduce((t,n)=>{for(let[r,l]of Object.entries(n)){let n=t[r];null!=n&&(t[r]=n+e*l)}return t},{...t})}}let M=k(1),D=k(-1);function E(e){if(!e)return!1;let{KeyboardEvent:t}=u(e.target);return t&&e instanceof t}function C(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=u(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:n}=e.touches[0];return{x:t,y:n}}if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:n}=e.changedTouches[0];return{x:t,y:n}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let A=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:n}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(n?Math.round(n):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:n}=e;return"scaleX("+t+") scaleY("+n+")"}},Transform:{toString(e){if(e)return[A.Translate.toString(e),A.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:n,easing:r}=e;return t+" "+n+"ms "+r}}}),R="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function S(e){return e.matches(R)?e:e.querySelector(R)}},78749:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},84616:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85339:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},92138:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},92657:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])}}]);