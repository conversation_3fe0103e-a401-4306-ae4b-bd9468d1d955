(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1400],{14098:(t,e,o)=>{"use strict";o.r(e),o.d(e,{default:()=>s});var a=o(95155),r=o(12115),c=o(35695),n=o(25561);function s(){let t=(0,c.useParams)(),e=null==t?void 0:t.contactName,o=(0,c.useRouter)();return(0,r.useEffect)(()=>{(async()=>{try{let t=(await (0,n.oe)()).find(t=>t.contactName.toLowerCase()===decodeURIComponent(e).toLowerCase());t?o.push("/contacts/edit/".concat(encodeURIComponent(t.contactName),"/").concat(encodeURIComponent(t._id))):o.push("/contacts")}catch(t){console.error("Error redirecting:",t),o.push("/contacts")}})()},[e,o]),(0,a.jsx)("div",{className:"flex items-center justify-center h-screen",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Redirecting..."}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Please wait while we redirect you to the contact edit page."})]})})}},25561:(t,e,o)=>{"use strict";o.d(e,{G6:()=>h,MO:()=>i,SQ:()=>s,TX:()=>l,oe:()=>r,oz:()=>u,to:()=>c,vY:()=>n});let a="http://localhost:4000";async function r(){try{let t=localStorage.getItem("access_token");if(!t)return console.error("No access token available"),[];let e=await fetch("".concat(a,"/api/contacts"),{headers:{Authorization:"Bearer ".concat(t)}});if(!e.ok)throw Error("Failed to fetch contacts");return await e.json()}catch(t){throw console.error("Error fetching contacts:",t),t}}async function c(){try{let t=localStorage.getItem("access_token");if(!t)return console.error("No access token available"),[];let e=await fetch("".concat(a,"/api/campaigns"),{headers:{Authorization:"Bearer ".concat(t)}});if(!e.ok)throw Error("Failed to fetch contacts");return await e.json()}catch(t){throw console.error("Error fetching contacts:",t),t}}async function n(t){try{let e=localStorage.getItem("access_token");if(!e)throw console.error("No access token available"),Error("No access token available");let o=await fetch("".concat(a,"/api/contacts"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify(t)});if(!o.ok)throw Error("Failed to create contact");return await o.json()}catch(t){throw console.error("Error creating contact:",t),t}}async function s(t){let e=localStorage.getItem("access_token");if(!e)throw Error("No access token available");let o=await fetch("".concat("http://localhost:4000","/api/contacts"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(e)},body:JSON.stringify(t)});if(!o.ok)throw Error((await o.json()).error||"Failed to create contact: ".concat(o.statusText));return o.json()}async function i(t){try{let e=localStorage.getItem("access_token");if(!e)throw console.error("No access token available"),Error("No access token available");if(!(await fetch("".concat(a,"/api/contacts/").concat(t),{method:"DELETE",headers:{Authorization:"Bearer ".concat(e)}})).ok)throw Error("Failed to delete contact")}catch(t){throw console.error("Error deleting contact:",t),t}}async function l(){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");let e=await fetch("".concat(a,"/api/contacts/import-contacts"),{headers:{Authorization:"Bearer ".concat(t)}});if(!e.ok)throw Error("Failed to import contacts");await e.json()}catch(t){throw console.error("Error importing contacts:",t),t}}async function h(t,e,o){try{let r=localStorage.getItem("access_token");if(!r)throw console.error("No access token available"),Error("No access token available");let c=await fetch("".concat(a,"/api/vapi/call-contacts"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r)},body:JSON.stringify({agentId:t,contacts:e,region:o})});if(402===c.status){let t=await c.json();throw Error(t.error||"Insufficient credits to make this call. Please add funds to your account.")}if(!c.ok)throw Error("Failed to start call");return await c.json()}catch(t){throw console.error("Error calling contact(s):",t),t}}async function u(t,e,o,r){try{let c=localStorage.getItem("access_token");if(!c)throw console.error("No access token available"),Error("No access token available");let n=await fetch("".concat(a,"/api/scheduled-call"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(c)},body:JSON.stringify({agentId:t,contacts:e,scheduledTime:o,region:r})});if(402===n.status){let t=await n.json();throw Error(t.error||"Insufficient credits to schedule this call. Please add funds to your account.")}if(!n.ok)throw Error("Failed to schedule call");return await n.json()}catch(t){throw console.error("Error scheduling call:",t),t}}},35695:(t,e,o)=>{"use strict";var a=o(18999);o.o(a,"useParams")&&o.d(e,{useParams:function(){return a.useParams}}),o.o(a,"usePathname")&&o.d(e,{usePathname:function(){return a.usePathname}}),o.o(a,"useRouter")&&o.d(e,{useRouter:function(){return a.useRouter}})},87696:(t,e,o)=>{Promise.resolve().then(o.bind(o,14098))}},t=>{var e=e=>t(t.s=e);t.O(0,[8441,1684,7358],()=>e(87696)),_N_E=t.O()}]);