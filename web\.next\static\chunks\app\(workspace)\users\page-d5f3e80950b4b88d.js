(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6878],{7524:(e,t,a)=>{"use strict";a.d(t,{LB:()=>d,TK:()=>o,hG:()=>i,hU:()=>s,lo:()=>n,mP:()=>u,nu:()=>l,s2:()=>c});let r="http://localhost:4000";async function s(){try{let e=localStorage.getItem("access_token");if(!e)throw console.error("No access token available"),Error("No access token available");let t=await fetch("".concat(r,"/api/users"),{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error("Failed to fetch users");return await t.json()}catch(e){throw console.error("Error fetching users:",e),e}}let n=async()=>{let e=localStorage.getItem("access_token");if(!e)throw Error("No access token available");let t=await fetch("".concat(r,"/api/users"),{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch users");return t.json()};async function l(e){try{if(!localStorage.getItem("access_token"))throw console.error("No access token available"),Error("No access token available");let t=await fetch("".concat(r,"/api/users/register"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.text();throw Error(e||"Failed to add user")}return}catch(e){throw console.error("Error adding user:",e),e}}async function o(e,t){try{let a=localStorage.getItem("access_token");if(!a)throw console.error("No access token available"),Error("No access token available");let s=await fetch("".concat(r,"/api/users/").concat(e),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)},body:JSON.stringify(t)});if(!s.ok)throw Error("Failed to update user");return await s.json()}catch(e){throw console.error("Error updating user:",e),e}}async function i(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");if(!(await fetch("".concat(r,"/api/users/").concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(t)}})).ok)throw Error("Failed to delete user")}catch(e){throw console.error("Error deleting user:",e),e}}async function d(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");let a=await fetch("".concat(r,"/api/users/approve/").concat(e),{method:"PATCH",headers:{Authorization:"Bearer ".concat(t)}});if(!a.ok)throw Error("Failed to approve user");return await a.json()}catch(e){throw console.error("Error approving user:",e),e}}async function c(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");let a=await fetch("".concat(r,"/api/users/").concat(e),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify({isApproved:!1})});if(!a.ok)throw Error("Failed to revoke user access");return await a.json()}catch(e){throw console.error("Error revoking user access:",e),e}}async function u(){try{let e=localStorage.getItem("access_token");if(!e)throw console.error("No access token available"),Error("No access token available");let t=await fetch("".concat(r,"/api/users/me/credits"),{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error("Failed to fetch user credits");let a=await t.json();return{credits:a.credits||0,minutes:a.minutes||0,freeCreditsRemaining:a.freeCreditsRemaining||0,paidCredits:a.paidCredits||0,totalAvailable:a.totalAvailable||0,usingFreeCredits:a.usingFreeCredits||!1,freeMinutesRemaining:a.freeMinutesRemaining||0,paidMinutes:a.paidMinutes||0,totalMinutesAvailable:a.totalMinutesAvailable||0,callPricePerMinute:a.callPricePerMinute||.1,monthlyResetDate:a.monthlyResetDate||1,monthlyAllowance:a.monthlyAllowance||0,minimumCreditsThreshold:a.minimumCreditsThreshold||1}}catch(e){return console.error("Error fetching user credits:",e),{credits:0,minutes:0,freeCreditsRemaining:0,paidCredits:0,totalAvailable:0,usingFreeCredits:!1,freeMinutesRemaining:0,paidMinutes:0,totalMinutesAvailable:0,callPricePerMinute:.1,monthlyResetDate:1,monthlyAllowance:0,minimumCreditsThreshold:1}}}},17649:(e,t,a)=>{"use strict";a.d(t,{UC:()=>M,VY:()=>U,ZD:()=>L,ZL:()=>S,bL:()=>D,hE:()=>R,hJ:()=>z,rc:()=>_});var r=a(12115),s=a(46081),n=a(6101),l=a(15452),o=a(85185),i=a(99708),d=a(95155),c="AlertDialog",[u,m]=(0,s.A)(c,[l.Hs]),h=(0,l.Hs)(),x=e=>{let{__scopeAlertDialog:t,...a}=e,r=h(t);return(0,d.jsx)(l.bL,{...r,...a,modal:!0})};x.displayName=c,r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,s=h(a);return(0,d.jsx)(l.l9,{...s,...r,ref:t})}).displayName="AlertDialogTrigger";var g=e=>{let{__scopeAlertDialog:t,...a}=e,r=h(t);return(0,d.jsx)(l.ZL,{...r,...a})};g.displayName="AlertDialogPortal";var f=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,s=h(a);return(0,d.jsx)(l.hJ,{...s,...r,ref:t})});f.displayName="AlertDialogOverlay";var p="AlertDialogContent",[v,b]=u(p),j=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,children:s,...c}=e,u=h(a),m=r.useRef(null),x=(0,n.s)(t,m),g=r.useRef(null);return(0,d.jsx)(l.G$,{contentName:p,titleName:y,docsSlug:"alert-dialog",children:(0,d.jsx)(v,{scope:a,cancelRef:g,children:(0,d.jsxs)(l.UC,{role:"alertdialog",...u,...c,ref:x,onOpenAutoFocus:(0,o.m)(c.onOpenAutoFocus,e=>{var t;e.preventDefault(),null===(t=g.current)||void 0===t||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,d.jsx)(i.xV,{children:s}),(0,d.jsx)(F,{contentRef:m})]})})})});j.displayName=p;var y="AlertDialogTitle",w=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,s=h(a);return(0,d.jsx)(l.hE,{...s,...r,ref:t})});w.displayName=y;var N="AlertDialogDescription",k=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,s=h(a);return(0,d.jsx)(l.VY,{...s,...r,ref:t})});k.displayName=N;var A=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,s=h(a);return(0,d.jsx)(l.bm,{...s,...r,ref:t})});A.displayName="AlertDialogAction";var C="AlertDialogCancel",E=r.forwardRef((e,t)=>{let{__scopeAlertDialog:a,...r}=e,{cancelRef:s}=b(C,a),o=h(a),i=(0,n.s)(t,s);return(0,d.jsx)(l.bm,{...o,...r,ref:i})});E.displayName=C;var F=e=>{let{contentRef:t}=e,a="`".concat(p,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(p,"` by passing a `").concat(N,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(p,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return r.useEffect(()=>{var e;document.getElementById(null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby"))||console.warn(a)},[a,t]),null},D=x,S=g,z=f,M=j,_=A,L=E,R=w,U=k},26126:(e,t,a)=>{"use strict";a.d(t,{E:()=>i});var r=a(95155);a(12115);var s=a(99708),n=a(74466),l=a(59434);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:a,asChild:n=!1,...i}=e,d=n?s.DX:"span";return(0,r.jsx)(d,{"data-slot":"badge",className:(0,l.cn)(o({variant:a}),t),...i})}},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>i,r:()=>o});var r=a(95155);a(12115);var s=a(99708),n=a(74466),l=a(59434);let o=(0,n.F)("inline-flex items-center cursor-pointer justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:a,size:n,asChild:i=!1,...d}=e,c=i?s.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,l.cn)(o({variant:a,size:n,className:t})),...d})}},40968:(e,t,a)=>{"use strict";a.d(t,{b:()=>o});var r=a(12115),s=a(63655),n=a(95155),l=r.forwardRef((e,t)=>(0,n.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null===(a=e.onMouseDown)||void 0===a||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var o=l},47924:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},51154:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>m,Es:()=>x,HM:()=>c,L3:()=>g,c7:()=>h,lG:()=>o,rr:()=>f,zM:()=>i});var r=a(95155);a(12115);var s=a(15452),n=a(54416),l=a(59434);function o(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"dialog",...t})}function i(e){let{...t}=e;return(0,r.jsx)(s.l9,{"data-slot":"dialog-trigger",...t})}function d(e){let{...t}=e;return(0,r.jsx)(s.ZL,{"data-slot":"dialog-portal",...t})}function c(e){let{...t}=e;return(0,r.jsx)(s.bm,{"data-slot":"dialog-close",...t})}function u(e){let{className:t,...a}=e;return(0,r.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-650 bg-black/50",t),...a})}function m(e){let{className:t,children:a,...o}=e;return(0,r.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,r.jsx)(u,{}),(0,r.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-650 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...o,children:[a,(0,r.jsxs)(s.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(n.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function h(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function x(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",t),...a})}function f(e){let{className:t,...a}=e;return(0,r.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",t),...a})}},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n,v:()=>l});var r=a(52596),s=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}function l(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var r=a(95155);a(12115);var s=a(59434);function n(e){let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},62525:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},84616:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>l});var r=a(95155);a(12115);var s=a(40968),n=a(59434);function l(e){let{className:t,...a}=e;return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},85127:(e,t,a)=>{"use strict";a.d(t,{A0:()=>l,BF:()=>o,Hj:()=>i,XI:()=>n,nA:()=>c,nd:()=>d});var r=a(95155);a(12115);var s=a(59434);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",t),...a})})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-muted-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}},86046:(e,t,a)=>{"use strict";a.d(t,{default:()=>w});var r=a(95155),s=a(12115),n=a(30285),l=a(85127),o=a(54165),i=a(62523),d=a(85057),c=a(26126),u=a(84616),m=a(47924),h=a(51154),x=a(19946);let g=(0,x.A)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),f=(0,x.A)("ShieldAlert",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M12 16h.01",key:"1drbdi"}]]);var p=a(89917),v=a(62525),b=a(56671),j=a(90010),y=a(7524);function w(){let[e,t]=(0,s.useState)([]),[a,x]=(0,s.useState)(!0),[w,N]=(0,s.useState)(!1),[k,A]=(0,s.useState)(!1),[C,E]=(0,s.useState)(!1),[F,D]=(0,s.useState)(!1),[S,z]=(0,s.useState)(null),[M,_]=(0,s.useState)(""),[L,R]=(0,s.useState)({fullName:"",email:"",password:""}),[U,P]=(0,s.useState)(null);(0,s.useEffect)(()=>{T()},[]);let T=async()=>{x(!0);try{let e=await (0,y.hU)();t(e)}catch(e){console.error("Error loading users:",e),b.o.error("Failed to load users")}finally{x(!1)}},O=e=>{let{name:t,value:a}=e.target;R(e=>({...e,[t]:a}))},$=async e=>{e.preventDefault(),P("add");try{let e={fullName:L.fullName,email:L.email,password:L.password};R({fullName:"",email:"",password:""}),N(!1),await (0,y.nu)(e),await T()}catch(e){console.error("Error adding user:",e)}finally{P(null)}},I=async e=>{if(e.preventDefault(),S){P("edit");try{let e={};L.fullName&&(e.fullName=L.fullName),L.email&&(e.email=L.email),L.password&&(e.password=L.password),await (0,y.TK)(S._id,e),b.o.success("User updated successfully"),A(!1),R({fullName:"",email:"",password:""}),T()}catch(e){console.error("Error updating user:",e),b.o.error("Failed to update user")}finally{P(null)}}},J=async()=>{if(S){P("delete");try{await (0,y.hG)(S._id),b.o.success("User deleted successfully"),E(!1),T()}catch(e){console.error("Error deleting user:",e),b.o.error("Failed to delete user")}finally{P(null)}}},B=async()=>{if(!S)return;let e=!S.isApproved;P("access");try{e?await (0,y.LB)(S._id):await (0,y.s2)(S._id),b.o.success("User ".concat(e?"access granted":"access revoked"," successfully")),D(!1),T()}catch(e){console.error("Error updating user status:",e),b.o.error("Failed to update user status")}finally{P(null)}},H=e=>{let t=new Date(e);return isNaN(t.getTime())?"N/A":t.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})},V=e.filter(e=>e.fullName.toLowerCase().includes(M.toLowerCase())||e.email.toLowerCase().includes(M.toLowerCase()));return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold",children:"Users Management"}),(0,r.jsxs)(n.$,{className:"bg-primary hover:bg-primary/90 flex items-center gap-2",onClick:()=>N(!0),children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),"Add User"]})]}),(0,r.jsx)("div",{className:"flex flex-col sm:flex-row gap-2 mb-6",children:(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,r.jsx)(i.p,{placeholder:"Search users...",className:"pl-10",value:M,onChange:e=>_(e.target.value)})]})}),(0,r.jsx)("div",{className:"bg-card rounded-lg border shadow-sm overflow-hidden",children:(0,r.jsxs)(l.XI,{children:[(0,r.jsx)(l.A0,{children:(0,r.jsxs)(l.Hj,{children:[(0,r.jsx)(l.nd,{children:"Name"}),(0,r.jsx)(l.nd,{children:"Email"}),(0,r.jsx)(l.nd,{children:"Date Created"}),(0,r.jsx)(l.nd,{children:"Status"}),(0,r.jsx)(l.nd,{className:"text-right",children:"Actions"})]})}),(0,r.jsx)(l.BF,{children:a?(0,r.jsx)(l.Hj,{children:(0,r.jsx)(l.nA,{colSpan:5,className:"h-64 text-center",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,r.jsx)(h.A,{className:"h-8 w-8 animate-spin text-primary mb-2"}),(0,r.jsx)("p",{className:"text-lg font-medium text-gray-600 dark:text-gray-300",children:"Loading users..."})]})})}):V.length>0?V.map(e=>(0,r.jsxs)(l.Hj,{children:[(0,r.jsx)(l.nA,{className:"font-medium",children:e.fullName}),(0,r.jsx)(l.nA,{children:e.email}),(0,r.jsx)(l.nA,{children:H(e.createdAt)}),(0,r.jsx)(l.nA,{children:e.isApproved?(0,r.jsxs)(c.E,{variant:"outline",className:"bg-green-50 text-green-700 border-green-200 hover:bg-green-50",children:[(0,r.jsx)(g,{className:"h-3 w-3 mr-1"}),"Approved"]}):(0,r.jsxs)(c.E,{variant:"outline",className:"bg-red-50 text-red-700 border-red-200 hover:bg-red-50",children:[(0,r.jsx)(f,{className:"h-3 w-3 mr-1"}),"Denied"]})}),(0,r.jsx)(l.nA,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,r.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>{z(e),D(!0)},className:e.isApproved?"text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700":"text-green-600 border-green-200 hover:bg-green-50 hover:text-green-700",disabled:U===e._id,children:e.isApproved?"Revoke Access":"Grant Access"}),(0,r.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{z(e),R({fullName:"",email:"",password:""}),A(!0)},className:"text-gray-600 border-gray-200 hover:bg-gray-50 hover:text-gray-700",children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-1"}),"Edit"]}),(0,r.jsx)(n.$,{variant:"outline",size:"sm",className:"text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700",onClick:()=>{z(e),E(!0)},children:(0,r.jsx)(v.A,{className:"h-4 w-4 mr-1"})})]})})]},e._id)):(0,r.jsx)(l.Hj,{children:(0,r.jsx)(l.nA,{colSpan:5,className:"h-64 text-center",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,r.jsx)("p",{className:"text-lg font-medium text-gray-600 dark:text-gray-300 mb-2",children:"No users found"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground max-w-md text-center mb-4",children:M?"No users match your search criteria":"Create users to manage access to the platform"}),(0,r.jsxs)(n.$,{variant:"outline",onClick:()=>N(!0),children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Add your first user"]})]})})})})]})}),(0,r.jsx)(o.lG,{open:w,onOpenChange:N,children:(0,r.jsxs)(o.Cf,{className:"sm:max-w-[425px]",children:[(0,r.jsx)(o.c7,{children:(0,r.jsx)(o.L3,{children:"Add New User"})}),(0,r.jsxs)("form",{onSubmit:$,children:[(0,r.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(d.J,{htmlFor:"fullName",children:"Full Name"}),(0,r.jsx)(i.p,{id:"fullName",name:"fullName",value:L.fullName,onChange:O,required:!0})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(d.J,{htmlFor:"email",children:"Email"}),(0,r.jsx)(i.p,{id:"email",name:"email",type:"email",value:L.email,onChange:O,required:!0})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(d.J,{htmlFor:"password",children:"Password"}),(0,r.jsx)(i.p,{id:"password",name:"password",type:"password",value:L.password,onChange:O,required:!0})]})]}),(0,r.jsxs)(o.Es,{className:"flex flex-col sm:flex-row gap-2",children:[(0,r.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>N(!1),disabled:!!U,children:"Cancel"}),(0,r.jsx)(n.$,{type:"submit",disabled:!!U,className:"bg-primary hover:bg-primary/90",children:"add"===U?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Adding..."]}):"Add User"})]})]})]})}),(0,r.jsx)(o.lG,{open:k,onOpenChange:A,children:(0,r.jsxs)(o.Cf,{className:"sm:max-w-[425px]",children:[(0,r.jsx)(o.c7,{children:(0,r.jsx)(o.L3,{children:"Edit User"})}),(0,r.jsxs)("form",{onSubmit:I,children:[(0,r.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(d.J,{htmlFor:"edit-fullName",children:"Full Name"}),(0,r.jsx)(i.p,{id:"edit-fullName",name:"fullName",value:L.fullName,onChange:O,placeholder:null==S?void 0:S.fullName})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(d.J,{htmlFor:"edit-email",children:"Email"}),(0,r.jsx)(i.p,{id:"edit-email",name:"email",type:"email",value:L.email,onChange:O,placeholder:null==S?void 0:S.email})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(d.J,{htmlFor:"edit-password",children:"New Password (leave blank to keep current)"}),(0,r.jsx)(i.p,{id:"edit-password",name:"password",type:"password",value:L.password,onChange:O})]})]}),(0,r.jsxs)(o.Es,{children:[(0,r.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>A(!1),disabled:!!U,children:"Cancel"}),(0,r.jsx)(n.$,{type:"submit",disabled:!!U,className:"bg-blue-600 hover:bg-blue-700 text-white",children:"edit"===U?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Updating..."]}):"Update User"})]})]})]})}),(0,r.jsx)(j.Lt,{open:C,onOpenChange:E,children:(0,r.jsxs)(j.EO,{children:[(0,r.jsxs)(j.wd,{children:[(0,r.jsx)(j.r7,{children:"Delete User"}),(0,r.jsxs)(j.$v,{children:["Are you sure you want to delete user ",(0,r.jsx)("strong",{children:null==S?void 0:S.fullName}),"? This action cannot be undone."]})]}),(0,r.jsxs)(j.ck,{children:[(0,r.jsx)(j.Zr,{disabled:!!U,children:"Cancel"}),(0,r.jsx)(j.Rx,{className:"bg-red-600 hover:bg-red-700 text-white",onClick:e=>{e.preventDefault(),J()},disabled:!!U,children:"delete"===U?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Deleting..."]}):"Delete User"})]})]})}),(0,r.jsx)(j.Lt,{open:F,onOpenChange:D,children:(0,r.jsxs)(j.EO,{children:[(0,r.jsxs)(j.wd,{children:[(0,r.jsx)(j.r7,{children:(null==S?void 0:S.isApproved)?"Revoke Access":"Grant Access"}),(0,r.jsx)(j.$v,{children:(null==S?void 0:S.isApproved)?(0,r.jsxs)(r.Fragment,{children:["Are you sure you want to ",(0,r.jsx)("strong",{children:"revoke access"})," for ",null==S?void 0:S.fullName,"? They will no longer be able to log in until access is restored."]}):(0,r.jsxs)(r.Fragment,{children:["Are you sure you want to ",(0,r.jsx)("strong",{children:"grant access"})," to ",null==S?void 0:S.fullName,"? This will allow them to log in to the platform."]})})]}),(0,r.jsxs)(j.ck,{children:[(0,r.jsx)(j.Zr,{disabled:"access"===U,children:"Cancel"}),(0,r.jsx)(j.Rx,{className:(null==S?void 0:S.isApproved)?"bg-black hover:bg-gray-800 text-white":"bg-green-600 hover:bg-green-700 text-white",onClick:e=>{e.preventDefault(),B()},disabled:"access"===U,children:"access"===U?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):(null==S?void 0:S.isApproved)?"Revoke Access":"Grant Access"})]})]})})]})}},89917:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});let r=(0,a(19946).A)("Pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},90010:(e,t,a)=>{"use strict";a.d(t,{$v:()=>x,EO:()=>c,Lt:()=>o,Rx:()=>g,Zr:()=>f,ck:()=>m,r7:()=>h,wd:()=>u});var r=a(95155);a(12115);var s=a(17649),n=a(59434),l=a(30285);function o(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"alert-dialog",...t})}function i(e){let{...t}=e;return(0,r.jsx)(s.ZL,{"data-slot":"alert-dialog-portal",...t})}function d(e){let{className:t,...a}=e;return(0,r.jsx)(s.hJ,{"data-slot":"alert-dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-650 bg-black/80",t),...a})}function c(e){let{className:t,...a}=e;return(0,r.jsxs)(i,{children:[(0,r.jsx)(d,{}),(0,r.jsx)(s.UC,{"data-slot":"alert-dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-650 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...a})]})}function u(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function m(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function h(e){let{className:t,...a}=e;return(0,r.jsx)(s.hE,{"data-slot":"alert-dialog-title",className:(0,n.cn)("text-lg font-semibold",t),...a})}function x(e){let{className:t,...a}=e;return(0,r.jsx)(s.VY,{"data-slot":"alert-dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(s.rc,{className:(0,n.cn)((0,l.r)(),t),...a})}function f(e){let{className:t,...a}=e;return(0,r.jsx)(s.ZD,{className:(0,n.cn)((0,l.r)({variant:"outline"}),t),...a})}},93829:(e,t,a)=>{Promise.resolve().then(a.bind(a,86046))}},e=>{var t=t=>e(e.s=t);e.O(0,[4201,4341,1071,6671,8441,1684,7358],()=>t(93829)),_N_E=e.O()}]);