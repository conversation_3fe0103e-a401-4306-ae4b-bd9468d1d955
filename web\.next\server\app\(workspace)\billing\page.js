(()=>{var e={};e.id=3793,e.ids=[3793],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},6211:(e,t,r)=>{"use strict";r.d(t,{A0:()=>i,BF:()=>o,Hj:()=>l,XI:()=>n,nA:()=>d,nd:()=>c});var s=r(60687);r(43210);var a=r(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...t})})}function i({className:e,...t}){return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...t})}function o({className:e,...t}){return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t})}function l({className:e,...t}){return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function c({className:e,...t}){return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-muted-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function d({className:e,...t}){return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},10022:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},14719:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>p,gC:()=>m,l6:()=>c,yv:()=>d});var s=r(60687);r(43210);var a=r(22670),n=r(78272),i=r(13964),o=r(3589),l=r(4780);function c({...e}){return(0,s.jsx)(a.bL,{"data-slot":"select",...e})}function d({...e}){return(0,s.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,children:t,...r}){return(0,s.jsxs)(a.l9,{"data-slot":"select-trigger",className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...r,children:[t,(0,s.jsx)(a.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function m({className:e,children:t,position:r="popper",...n}){return(0,s.jsx)(a.ZL,{children:(0,s.jsxs)(a.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...n,children:[(0,s.jsx)(h,{}),(0,s.jsx)(a.LM,{className:(0,l.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,s.jsx)(f,{})]})})}function p({className:e,children:t,...r}){return(0,s.jsxs)(a.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(a.VF,{children:(0,s.jsx)(i.A,{className:"size-4"})})}),(0,s.jsx)(a.p4,{children:t})]})}function h({className:e,...t}){return(0,s.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(o.A,{className:"size-4"})})}function f({className:e,...t}){return(0,s.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,s.jsx)(n.A,{className:"size-4"})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20577:(e,t,r)=>{Promise.resolve().then(r.bind(r,20862))},20862:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\app\\\\(workspace)\\\\billing\\\\BillingContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\billing\\BillingContent.tsx","default")},21820:e=>{"use strict";e.exports=require("os")},26134:(e,t,r)=>{"use strict";r.d(t,{G$:()=>G,Hs:()=>j,UC:()=>et,VY:()=>es,ZL:()=>Q,bL:()=>X,bm:()=>ea,hE:()=>er,hJ:()=>ee,l9:()=>K,lG:()=>k});var s=r(43210),a=r(70569),n=r(98599),i=r(11273),o=r(96963),l=r(65551),c=r(31355),d=r(32547),u=r(25028),m=r(46059),p=r(14163),h=r(1359),f=r(42247),x=r(63376),g=r(8730),y=r(60687),v="Dialog",[b,j]=(0,i.A)(v),[w,N]=b(v),k=e=>{let{__scopeDialog:t,children:r,open:a,defaultOpen:n,onOpenChange:i,modal:c=!0}=e,d=s.useRef(null),u=s.useRef(null),[m=!1,p]=(0,l.i)({prop:a,defaultProp:n,onChange:i});return(0,y.jsx)(w,{scope:t,triggerRef:d,contentRef:u,contentId:(0,o.B)(),titleId:(0,o.B)(),descriptionId:(0,o.B)(),open:m,onOpenChange:p,onOpenToggle:s.useCallback(()=>p(e=>!e),[p]),modal:c,children:r})};k.displayName=v;var C="DialogTrigger",S=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,i=N(C,r),o=(0,n.s)(t,i.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":U(i.open),...s,ref:o,onClick:(0,a.m)(e.onClick,i.onOpenToggle)})});S.displayName=C;var A="DialogPortal",[_,E]=b(A,{forceMount:void 0}),P=e=>{let{__scopeDialog:t,forceMount:r,children:a,container:n}=e,i=N(A,t);return(0,y.jsx)(_,{scope:t,forceMount:r,children:s.Children.map(a,e=>(0,y.jsx)(m.C,{present:r||i.open,children:(0,y.jsx)(u.Z,{asChild:!0,container:n,children:e})}))})};P.displayName=A;var z="DialogOverlay",R=s.forwardRef((e,t)=>{let r=E(z,e.__scopeDialog),{forceMount:s=r.forceMount,...a}=e,n=N(z,e.__scopeDialog);return n.modal?(0,y.jsx)(m.C,{present:s||n.open,children:(0,y.jsx)(M,{...a,ref:t})}):null});R.displayName=z;var M=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,a=N(z,r);return(0,y.jsx)(f.A,{as:g.DX,allowPinchZoom:!0,shards:[a.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":U(a.open),...s,ref:t,style:{pointerEvents:"auto",...s.style}})})}),F="DialogContent",O=s.forwardRef((e,t)=>{let r=E(F,e.__scopeDialog),{forceMount:s=r.forceMount,...a}=e,n=N(F,e.__scopeDialog);return(0,y.jsx)(m.C,{present:s||n.open,children:n.modal?(0,y.jsx)(D,{...a,ref:t}):(0,y.jsx)($,{...a,ref:t})})});O.displayName=F;var D=s.forwardRef((e,t)=>{let r=N(F,e.__scopeDialog),i=s.useRef(null),o=(0,n.s)(t,r.contentRef,i);return s.useEffect(()=>{let e=i.current;if(e)return(0,x.Eq)(e)},[]),(0,y.jsx)(I,{...e,ref:o,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault())})}),$=s.forwardRef((e,t)=>{let r=N(F,e.__scopeDialog),a=s.useRef(!1),n=s.useRef(!1);return(0,y.jsx)(I,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(a.current||r.triggerRef.current?.focus(),t.preventDefault()),a.current=!1,n.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(a.current=!0,"pointerdown"!==t.detail.originalEvent.type||(n.current=!0));let s=t.target;r.triggerRef.current?.contains(s)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&n.current&&t.preventDefault()}})}),I=s.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:a,onOpenAutoFocus:i,onCloseAutoFocus:o,...l}=e,u=N(F,r),m=s.useRef(null),p=(0,n.s)(t,m);return(0,h.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(d.n,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:i,onUnmountAutoFocus:o,children:(0,y.jsx)(c.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":U(u.open),...l,ref:p,onDismiss:()=>u.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(Y,{titleId:u.titleId}),(0,y.jsx)(J,{contentRef:m,descriptionId:u.descriptionId})]})]})}),T="DialogTitle",B=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,a=N(T,r);return(0,y.jsx)(p.sG.h2,{id:a.titleId,...s,ref:t})});B.displayName=T;var q="DialogDescription",L=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,a=N(q,r);return(0,y.jsx)(p.sG.p,{id:a.descriptionId,...s,ref:t})});L.displayName=q;var H="DialogClose",W=s.forwardRef((e,t)=>{let{__scopeDialog:r,...s}=e,n=N(H,r);return(0,y.jsx)(p.sG.button,{type:"button",...s,ref:t,onClick:(0,a.m)(e.onClick,()=>n.onOpenChange(!1))})});function U(e){return e?"open":"closed"}W.displayName=H;var V="DialogTitleWarning",[G,Z]=(0,i.q)(V,{contentName:F,titleName:T,docsSlug:"dialog"}),Y=({titleId:e})=>{let t=Z(V),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return s.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},J=({contentRef:e,descriptionId:t})=>{let r=Z("DialogDescriptionWarning"),a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return s.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(a)},[a,e,t]),null},X=k,K=S,Q=P,ee=R,et=O,er=B,es=L,ea=W},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34452:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},34631:e=>{"use strict";e.exports=require("tls")},44493:(e,t,r)=>{"use strict";r.d(t,{BT:()=>l,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>d});var s=r(60687);r(43210);var a=r(4780);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("flex flex-col gap-1.5 px-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6",e),...t})}},50812:(e,t,r)=>{"use strict";r.d(t,{C:()=>l,z:()=>o});var s=r(60687);r(43210);var a=r(14555),n=r(65822),i=r(4780);function o({className:e,...t}){return(0,s.jsx)(a.bL,{"data-slot":"radio-group",className:(0,i.cn)("grid gap-3",e),...t})}function l({className:e,...t}){return(0,s.jsx)(a.q7,{"data-slot":"radio-group-item",className:(0,i.cn)("border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(a.C1,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,s.jsx)(n.A,{className:"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2"})})})}},54987:(e,t,r)=>{"use strict";r.d(t,{d:()=>i});var s=r(60687);r(43210);var a=r(90270),n=r(4780);function i({className:e,...t}){return(0,s.jsx)(a.bL,{"data-slot":"switch",className:(0,n.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 inline-flex h-5 w-9 shrink-0 items-center rounded-full border-2 border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(a.zi,{"data-slot":"switch-thumb",className:(0,n.cn)("bg-background pointer-events-none block size-4 rounded-full ring-0 shadow-lg transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")})})}},55146:(e,t,r)=>{"use strict";r.d(t,{B8:()=>E,UC:()=>z,bL:()=>_,l9:()=>P});var s=r(43210),a=r(70569),n=r(11273),i=r(72942),o=r(46059),l=r(14163),c=r(43),d=r(65551),u=r(96963),m=r(60687),p="Tabs",[h,f]=(0,n.A)(p,[i.RG]),x=(0,i.RG)(),[g,y]=h(p),v=s.forwardRef((e,t)=>{let{__scopeTabs:r,value:s,onValueChange:a,defaultValue:n,orientation:i="horizontal",dir:o,activationMode:p="automatic",...h}=e,f=(0,c.jH)(o),[x,y]=(0,d.i)({prop:s,onChange:a,defaultProp:n});return(0,m.jsx)(g,{scope:r,baseId:(0,u.B)(),value:x,onValueChange:y,orientation:i,dir:f,activationMode:p,children:(0,m.jsx)(l.sG.div,{dir:f,"data-orientation":i,...h,ref:t})})});v.displayName=p;var b="TabsList",j=s.forwardRef((e,t)=>{let{__scopeTabs:r,loop:s=!0,...a}=e,n=y(b,r),o=x(r);return(0,m.jsx)(i.bL,{asChild:!0,...o,orientation:n.orientation,dir:n.dir,loop:s,children:(0,m.jsx)(l.sG.div,{role:"tablist","aria-orientation":n.orientation,...a,ref:t})})});j.displayName=b;var w="TabsTrigger",N=s.forwardRef((e,t)=>{let{__scopeTabs:r,value:s,disabled:n=!1,...o}=e,c=y(w,r),d=x(r),u=S(c.baseId,s),p=A(c.baseId,s),h=s===c.value;return(0,m.jsx)(i.q7,{asChild:!0,...d,focusable:!n,active:h,children:(0,m.jsx)(l.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":p,"data-state":h?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:u,...o,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(s)}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(s)}),onFocus:(0,a.m)(e.onFocus,()=>{let e="manual"!==c.activationMode;h||n||!e||c.onValueChange(s)})})})});N.displayName=w;var k="TabsContent",C=s.forwardRef((e,t)=>{let{__scopeTabs:r,value:a,forceMount:n,children:i,...c}=e,d=y(k,r),u=S(d.baseId,a),p=A(d.baseId,a),h=a===d.value,f=s.useRef(h);return s.useEffect(()=>{let e=requestAnimationFrame(()=>f.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,m.jsx)(o.C,{present:n||h,children:({present:r})=>(0,m.jsx)(l.sG.div,{"data-state":h?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":u,hidden:!r,id:p,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:f.current?"0s":void 0},children:r&&i})})});function S(e,t){return`${e}-trigger-${t}`}function A(e,t){return`${e}-content-${t}`}C.displayName=k;var _=v,E=j,P=N,z=C},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56397:()=>{},56896:(e,t,r)=>{"use strict";r.d(t,{S:()=>o});var s=r(60687);r(43210);var a=r(25112),n=r(13964),i=r(4780);function o({className:e,...t}){return(0,s.jsx)(a.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...t,children:(0,s.jsx)(a.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(n.A,{className:"size-3.5"})})})}},57025:(e,t,r)=>{Promise.resolve().then(r.bind(r,58562))},58562:(e,t,r)=>{"use strict";r.d(t,{default:()=>eD});var s,a=r(60687),n=r(43210),i=r(16189),o=r(44493),l=r(89667),c=r(80013),d=r(29523),u=r(85763),m=r(6211),p=r(50812),h=r(62688);let f=(0,h.A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);(0,h.A)("Banknote",[["rect",{width:"20",height:"12",x:"2",y:"6",rx:"2",key:"9lu3g6"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}],["path",{d:"M6 12h.01M18 12h.01",key:"113zkx"}]]);var x=r(28559),g=r(41862),y=r(14719),v=r(88233),b=r(99270),j=r(10022),w=r(23328),N=r(74678);let k=async()=>{let e=await fetch(`${N.H}/api/billing/payment-methods`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch payment methods");return e.json()},C=async e=>{let t=await fetch(`${N.H}/api/billing/payment-methods/${e}/default`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!t.ok)throw Error((await t.json()).message||"Failed to set default payment method");return t.json()},S=async e=>{let t=await fetch(`${N.H}/api/billing/payment-methods/${e}`,{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!t.ok)throw Error((await t.json()).message||"Failed to remove payment method");return t.json()},A=async(e=1,t=10)=>{let r=await fetch(`${N.H}/api/billing/transactions?page=${e}&limit=${t}`,{method:"GET",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!r.ok)throw Error((await r.json()).message||"Failed to fetch transaction history");return r.json()},_=async e=>{let t=await fetch(`${N.H}/api/billing/process-payment`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Failed to process payment");return t.json()};var E=r(24258),P=r(87955);function z(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(e);t&&(s=s.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,s)}return r}function R(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?z(Object(r),!0).forEach(function(t){F(e,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):z(Object(r)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))})}return e}function M(e){return(M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function F(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function O(e,t){if(null==e)return{};var r,s,a=function(e,t){if(null==e)return{};var r,s,a={},n=Object.keys(e);for(s=0;s<n.length;s++)r=n[s],t.indexOf(r)>=0||(a[r]=e[r]);return a}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(s=0;s<n.length;s++)r=n[s],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}function D(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r,s,a=e&&("undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"]);if(null!=a){var n=[],i=!0,o=!1;try{for(a=a.call(e);!(i=(r=a.next()).done)&&(n.push(r.value),!t||n.length!==t);i=!0);}catch(e){o=!0,s=e}finally{try{i||null==a.return||a.return()}finally{if(o)throw s}}return n}}(e,t)||function(e,t){if(e){if("string"==typeof e)return $(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);if("Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return $(e,t)}}(e,t)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,s=Array(t);r<t;r++)s[r]=e[r];return s}var I=function(e,t,r){var s=!!r,a=n.useRef(r);n.useEffect(function(){a.current=r},[r]),n.useEffect(function(){if(!s||!e)return function(){};var r=function(){a.current&&a.current.apply(a,arguments)};return e.on(t,r),function(){e.off(t,r)}},[s,t,e,a])},T=function(e){var t=n.useRef(e);return n.useEffect(function(){t.current=e},[e]),t.current},B=function(e){return null!==e&&"object"===M(e)},q="[object Object]",L=function e(t,r){if(!B(t)||!B(r))return t===r;var s=Array.isArray(t);if(s!==Array.isArray(r))return!1;var a=Object.prototype.toString.call(t)===q;if(a!==(Object.prototype.toString.call(r)===q))return!1;if(!a&&!s)return t===r;var n=Object.keys(t),i=Object.keys(r);if(n.length!==i.length)return!1;for(var o={},l=0;l<n.length;l+=1)o[n[l]]=!0;for(var c=0;c<i.length;c+=1)o[i[c]]=!0;var d=Object.keys(o);return d.length===n.length&&d.every(function(s){return e(t[s],r[s])})},H=function(e,t,r){return B(e)?Object.keys(e).reduce(function(s,a){var n=!B(t)||!L(e[a],t[a]);return r.includes(a)?(n&&console.warn("Unsupported prop change: options.".concat(a," is not a mutable property.")),s):n?R(R({},s||{}),{},F({},a,e[a])):s},null):null},W="Invalid prop `stripe` supplied to `Elements`. We recommend using the `loadStripe` utility from `@stripe/stripe-js`. See https://stripe.com/docs/stripe-js/react#elements-props-stripe for details.",U=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:W;if(null===e||B(e)&&"function"==typeof e.elements&&"function"==typeof e.createToken&&"function"==typeof e.createPaymentMethod&&"function"==typeof e.confirmCardPayment)return e;throw Error(t)},V=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:W;if(B(e)&&"function"==typeof e.then)return{tag:"async",stripePromise:Promise.resolve(e).then(function(e){return U(e,t)})};var r=U(e,t);return null===r?{tag:"empty"}:{tag:"sync",stripe:r}},G=function(e){e&&e._registerWrapper&&e.registerAppInfo&&(e._registerWrapper({name:"react-stripe-js",version:"3.7.0"}),e.registerAppInfo({name:"react-stripe-js",version:"3.7.0",url:"https://stripe.com/docs/stripe-js/react"}))},Z=n.createContext(null);Z.displayName="ElementsContext";var Y=function(e,t){if(!e)throw Error("Could not find Elements context; You need to wrap the part of your app that ".concat(t," in an <Elements> provider."));return e},J=function(e){var t=e.stripe,r=e.options,s=e.children,a=n.useMemo(function(){return V(t)},[t]),i=D(n.useState(function(){return{stripe:"sync"===a.tag?a.stripe:null,elements:"sync"===a.tag?a.stripe.elements(r):null}}),2),o=i[0],l=i[1];n.useEffect(function(){var e=!0,t=function(e){l(function(t){return t.stripe?t:{stripe:e,elements:e.elements(r)}})};return"async"!==a.tag||o.stripe?"sync"!==a.tag||o.stripe||t(a.stripe):a.stripePromise.then(function(r){r&&e&&t(r)}),function(){e=!1}},[a,o,r]);var c=T(t);n.useEffect(function(){null!==c&&c!==t&&console.warn("Unsupported prop change on Elements: You cannot change the `stripe` prop after setting it.")},[c,t]);var d=T(r);return n.useEffect(function(){if(o.elements){var e=H(r,d,["clientSecret","fonts"]);e&&o.elements.update(e)}},[r,d,o.elements]),n.useEffect(function(){G(o.stripe)},[o.stripe]),n.createElement(Z.Provider,{value:o},s)};J.propTypes={stripe:P.any,options:P.object};P.func.isRequired;var X=["on","session"],K=n.createContext(null);K.displayName="CheckoutSdkContext";var Q=function(e,t){if(!e)throw Error("Could not find CheckoutProvider context; You need to wrap the part of your app that ".concat(t," in an <CheckoutProvider> provider."));return e};n.createContext(null).displayName="CheckoutContext";P.any,P.shape({fetchClientSecret:P.func.isRequired,elementsOptions:P.object}).isRequired;var ee=function(e){var t=n.useContext(K),r=n.useContext(Z);if(t&&r)throw Error("You cannot wrap the part of your app that ".concat(e," in both <CheckoutProvider> and <Elements> providers."));return t?Q(t,e):Y(r,e)},et=["mode"],er=function(e,t){var r="".concat(e.charAt(0).toUpperCase()+e.slice(1),"Element"),s=t?function(e){ee("mounts <".concat(r,">"));var t=e.id,s=e.className;return n.createElement("div",{id:t,className:s})}:function(t){var s,a=t.id,i=t.className,o=t.options,l=void 0===o?{}:o,c=t.onBlur,d=t.onFocus,u=t.onReady,m=t.onChange,p=t.onEscape,h=t.onClick,f=t.onLoadError,x=t.onLoaderStart,g=t.onNetworksChange,y=t.onConfirm,v=t.onCancel,b=t.onShippingAddressChange,j=t.onShippingRateChange,w=ee("mounts <".concat(r,">")),N="elements"in w?w.elements:null,k="checkoutSdk"in w?w.checkoutSdk:null,C=D(n.useState(null),2),S=C[0],A=C[1],_=n.useRef(null),E=n.useRef(null);I(S,"blur",c),I(S,"focus",d),I(S,"escape",p),I(S,"click",h),I(S,"loaderror",f),I(S,"loaderstart",x),I(S,"networkschange",g),I(S,"confirm",y),I(S,"cancel",v),I(S,"shippingaddresschange",b),I(S,"shippingratechange",j),I(S,"change",m),u&&(s="expressCheckout"===e?u:function(){u(S)}),I(S,"ready",s),n.useLayoutEffect(function(){if(null===_.current&&null!==E.current&&(N||k)){var t=null;if(k)switch(e){case"payment":t=k.createPaymentElement(l);break;case"address":if("mode"in l){var s=l.mode,a=O(l,et);if("shipping"===s)t=k.createShippingAddressElement(a);else if("billing"===s)t=k.createBillingAddressElement(a);else throw Error("Invalid options.mode. mode must be 'billing' or 'shipping'.")}else throw Error("You must supply options.mode. mode must be 'billing' or 'shipping'.");break;case"expressCheckout":t=k.createExpressCheckoutElement(l);break;case"currencySelector":t=k.createCurrencySelectorElement();break;default:throw Error("Invalid Element type ".concat(r,". You must use either the <PaymentElement />, <AddressElement options={{mode: 'shipping'}} />, <AddressElement options={{mode: 'billing'}} />, or <ExpressCheckoutElement />."))}else N&&(t=N.create(e,l));_.current=t,A(t),t&&t.mount(E.current)}},[N,k,l]);var P=T(l);return n.useEffect(function(){if(_.current){var e=H(l,P,["paymentRequest"]);e&&"update"in _.current&&_.current.update(e)}},[l,P]),n.useLayoutEffect(function(){return function(){if(_.current&&"function"==typeof _.current.destroy)try{_.current.destroy(),_.current=null}catch(e){}}},[]),n.createElement("div",{id:a,className:i,ref:E})};return s.propTypes={id:P.string,className:P.string,onChange:P.func,onBlur:P.func,onFocus:P.func,onReady:P.func,onEscape:P.func,onClick:P.func,onLoadError:P.func,onLoaderStart:P.func,onNetworksChange:P.func,onConfirm:P.func,onCancel:P.func,onShippingAddressChange:P.func,onShippingRateChange:P.func,options:P.object},s.displayName=r,s.__elementType=e,s},es="undefined"==typeof window,ea=n.createContext(null);ea.displayName="EmbeddedCheckoutProviderContext";er("auBankAccount",es);var en=er("card",es);er("cardNumber",es),er("cardExpiry",es),er("cardCvc",es),er("fpxBank",es),er("iban",es),er("idealBank",es),er("p24Bank",es),er("epsBank",es),er("payment",es),er("expressCheckout",es),er("currencySelector",es),er("paymentRequestButton",es),er("linkAuthentication",es),er("address",es),er("shippingAddress",es),er("paymentMethodMessaging",es),er("affirmMessage",es),er("afterpayClearpayMessage",es);var ei="basil",eo="https://js.stripe.com",el="".concat(eo,"/").concat(ei,"/stripe.js"),ec=/^https:\/\/js\.stripe\.com\/v3\/?(\?.*)?$/,ed=/^https:\/\/js\.stripe\.com\/(v3|[a-z]+)\/stripe\.js(\?.*)?$/,eu=function(){for(var e=document.querySelectorAll('script[src^="'.concat(eo,'"]')),t=0;t<e.length;t++){var r,s=e[t];if(r=s.src,ec.test(r)||ed.test(r))return s}return null},em=function(e){var t=e&&!e.advancedFraudSignals?"?advancedFraudSignals=false":"",r=document.createElement("script");r.src="".concat(el).concat(t);var s=document.head||document.body;if(!s)throw Error("Expected document.body not to be null. Stripe.js requires a <body> element.");return s.appendChild(r),r},ep=function(e,t){e&&e._registerWrapper&&e._registerWrapper({name:"stripe-js",version:"7.3.0",startTime:t})},eh=null,ef=null,ex=null,eg=function(e,t,r){if(null===e)return null;var s,a=t[0].match(/^pk_test/),n=3===(s=e.version)?"v3":s;a&&n!==ei&&console.warn("Stripe.js@".concat(n," was loaded on the page, but @stripe/stripe-js@").concat("7.3.0"," expected Stripe.js@").concat(ei,". This may result in unexpected behavior. For more information, see https://docs.stripe.com/sdks/stripejs-versioning"));var i=e.apply(void 0,t);return ep(i,r),i},ey=!1,ev=function(){return s?s:s=(null!==eh?eh:(eh=new Promise(function(e,t){if("undefined"==typeof window||"undefined"==typeof document){e(null);return}if(window.Stripe,window.Stripe){e(window.Stripe);return}try{var r,s=eu();s?s&&null!==ex&&null!==ef&&(s.removeEventListener("load",ex),s.removeEventListener("error",ef),null===(r=s.parentNode)||void 0===r||r.removeChild(s),s=em(null)):s=em(null),ex=function(){window.Stripe?e(window.Stripe):t(Error("Stripe.js not available"))},ef=function(e){t(Error("Failed to load Stripe.js",{cause:e}))},s.addEventListener("load",ex),s.addEventListener("error",ef)}catch(e){t(e);return}})).catch(function(e){return eh=null,Promise.reject(e)})).catch(function(e){return s=null,Promise.reject(e)})};Promise.resolve().then(function(){return ev()}).catch(function(e){ey||console.warn(e)});let eb=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];ey=!0;var s=Date.now();return ev().then(function(e){return eg(e,t,s)})}(N.e),ej=(0,n.memo)(({children:e})=>(0,a.jsx)(J,{stripe:eb,children:e}));var ew=r(56896);let eN=(0,n.memo)(({amount:e,currency:t,description:r,onSuccess:s,onError:i,showSaveOption:o=!0})=>{var l;let u=ee("calls useStripe()").stripe,m=(l="calls useElements()",Y(n.useContext(Z),l)).elements,[p,h]=(0,n.useState)(!1),[x,y]=(0,n.useState)(!1),[v,b]=(0,n.useState)(!0),j=(0,n.useMemo)(()=>new Intl.NumberFormat("en-US",{style:"currency",currency:t}).format(e/100),[e,t]),w=async a=>{if(a.preventDefault(),!u||!m){i("Stripe has not loaded yet. Please try again.");return}h(!0);try{let a=m.getElement(en);if(!a)throw Error("Card element not found");let{error:n,paymentMethod:i}=await u.createPaymentMethod({type:"card",card:a});if(n)throw Error(n.message);if(!i)throw Error("Failed to create payment method");let o=await _({paymentMethodId:i.id,amount:e,currency:t,description:r,savePaymentMethod:x,setAsDefault:x&&v});if(o.success){a.clear();try{await new Promise(e=>setTimeout(e,500)),s()}catch(e){console.error("Error refreshing credits:",e),s()}}else if(("requires_action"===o.status||"requires_confirmation"===o.status)&&o.clientSecret){let{error:e,paymentIntent:t}=await u.confirmCardPayment(o.clientSecret);if(e)throw Error(e.message);if("succeeded"===t.status){a.clear();try{await new Promise(e=>setTimeout(e,500)),s()}catch(e){console.error("Error refreshing credits:",e),s()}}else throw Error(`Payment failed: ${t.status}`)}else if("requires_action"===o.status||"requires_confirmation"===o.status)throw Error("Authentication required but client secret is missing");else throw Error(`Payment failed: ${o.status}`)}catch(e){console.error("Payment error:",e),i(e.message||"Payment processing failed")}finally{h(!1)}};return(0,a.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"p-4 border rounded-lg bg-white dark:bg-gray-800 shadow-sm",children:[(0,a.jsx)("div",{className:"mb-3 flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(f,{className:"h-5 w-5 mr-2 text-primary"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:"Enter Card Details"})]})}),(0,a.jsx)(en,{options:{style:{base:{fontSize:"16px",color:"#424770",fontFamily:'system-ui, -apple-system, "Segoe UI", Roboto, sans-serif',"::placeholder":{color:"#aab7c4"},":-webkit-autofill":{color:"#424770"}},invalid:{color:"#9e2146",iconColor:"#9e2146"}}}})]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground flex items-center",children:[(0,a.jsx)(f,{className:"h-3 w-3 mr-1"}),"Your card information is securely processed by Stripe."]})]}),o&&(0,a.jsxs)("div",{className:"space-y-3 p-3 border rounded-md bg-primary/5 border-primary/10",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(ew.S,{id:"save-card",checked:x,onCheckedChange:e=>y(!0===e)}),(0,a.jsxs)(c.J,{htmlFor:"save-card",className:"flex items-center cursor-pointer",children:[(0,a.jsx)(f,{className:"h-4 w-4 mr-2 text-primary"}),"Save card for future payments"]})]}),x&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 ml-6",children:[(0,a.jsx)(ew.S,{id:"default-card",checked:v,onCheckedChange:e=>b(!0===e)}),(0,a.jsx)(c.J,{htmlFor:"default-card",className:"cursor-pointer",children:"Set as default payment method"})]})]})]}),(0,a.jsx)(d.$,{type:"submit",className:"w-full",disabled:!u||p||e<1e3,children:p?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):`Pay ${j}`})]})});var ek=r(15079);let eC=(0,n.memo)(({amount:e,currency:t,description:r,onSuccess:s,onError:i})=>{let[l,c]=(0,n.useState)([]),[u,m]=(0,n.useState)(null),[p,h]=(0,n.useState)(!0),[x,y]=(0,n.useState)(!1),v=(0,n.useMemo)(()=>new Intl.NumberFormat("en-US",{style:"currency",currency:t}).format(e/100),[e,t]),b=(0,n.useMemo)(()=>l.map(e=>(0,a.jsx)(ek.eb,{value:e._id,children:(0,a.jsxs)("div",{className:"flex items-center",children:["visa"===e.brand&&(0,a.jsxs)("svg",{viewBox:"0 0 32 21",className:"h-4 w-4 mr-2",children:[(0,a.jsx)("path",{d:"M26.58 21H5.42A5.42 5.42 0 0 1 0 15.58V5.42A5.42 5.42 0 0 1 5.42 0h21.16A5.42 5.42 0 0 1 32 5.42v10.16A5.42 5.42 0 0 1 26.58 21z",fill:"#fff"}),(0,a.jsx)("path",{d:"M12.322 7.583c.165-.437.352-.867.55-1.289h-1.09c-.33.39-.65.803-.957 1.234-.1-.431-.415-.845-.736-1.234h-.922c.616.452 1.013 1.014 1.013 1.716 0 .669-.352 1.289-.968 1.716h1.112c.308-.365.583-.803.781-1.274.187.471.451.909.77 1.274h1.134c-.627-.427-.968-1.047-.968-1.716a2.069 2.069 0 0 1 .28-1.427z",fill:"#00579f"}),(0,a.jsx)("path",{d:"M16.9 7.233c-.319 0-.605.075-.825.224-.209.149-.319.373-.319.634 0 .5.407.764 1.013.94l.297.09c.341.104.517.193.517.365 0 .268-.275.432-.726.432-.385 0-.671-.075-.979-.277l-.143.67c.33.164.737.254 1.112.254.374 0 .682-.075.902-.239.242-.164.374-.403.374-.7 0-.537-.44-.81-1.024-.97l-.297-.09c-.275-.074-.506-.164-.506-.343 0-.224.231-.373.594-.373.33 0 .649.09.869.194l.132-.634a2.365 2.365 0 0 0-.99-.179zm2.772 0c-.22 0-.407.03-.55.09l-.11.642c.143-.06.33-.104.55-.104.407-.15.66.224.66.642 0 .582-.308.94-.77.94-.242 0-.44-.06-.627-.164l-.121.656c.187.09.506.149.792.149.891 0 1.463-.642 1.463-1.61 0-.94-.517-1.24-1.288-1.24zm-6.578.09l-.209 1.28c-.22-.06-.44-.09-.66-.09-.56 0-1.024.149-1.024.582 0 .194.121.328.297.403.22.09.275.12.275.209 0 .134-.143.194-.341.194a1.9 1.9 0 0 1-.638-.12l-.11.626c.22.075.55.12.847.12.891 0 1.365-.373 1.365-.94 0-.224-.132-.387-.33-.477-.165-.075-.22-.09-.22-.179 0-.104.11-.164.297-.164.165 0 .374.03.55.09l.11-.634h-.209zm13.64-.09c-.33 0-.638.09-.869.373l.088-.373h-.748l-.572 2.68h.847l.33-1.582c.11-.507.396-.776.737-.776.11 0 .22.015.308.045l.165-.746a1.274 1.274 0 0 0-.286-.03zm-8.415.09l-.671 2.68h.847l.66-2.68h-.836zm-3.96 0l-.847 1.88-.341-1.88h-.902l-.781 2.68h.792l.484-1.85.396 1.85h.55l.99-1.85-.495 1.85h.803l.847-2.68h-1.497z",fill:"#00579f"})]}),"mastercard"===e.brand&&(0,a.jsxs)("svg",{viewBox:"0 0 32 21",className:"h-4 w-4 mr-2",children:[(0,a.jsx)("path",{d:"M26.58 21H5.42A5.42 5.42 0 0 1 0 15.58V5.42A5.42 5.42 0 0 1 5.42 0h21.16A5.42 5.42 0 0 1 32 5.42v10.16A5.42 5.42 0 0 1 26.58 21z",fill:"#fff"}),(0,a.jsx)("path",{d:"M23.209 7.465c0-1.196-.946-2.142-2.142-2.142h-10.12c-1.196 0-2.142.946-2.142 2.142v6.07c0 1.196.946 2.142 2.142 2.142h10.12c1.196 0 2.142-.946 2.142-2.142v-6.07z",fill:"#ff5f00"}),(0,a.jsx)("path",{d:"M12.947 10.5a4.006 4.006 0 0 1 1.535-3.16 4.006 4.006 0 1 0 0 6.32 4.006 4.006 0 0 1-1.535-3.16z",fill:"#eb001b"}),(0,a.jsx)("path",{d:"M20.994 10.5a4.006 4.006 0 0 1-6.512 3.16 4.006 4.006 0 0 0 0-6.32a4.006 4.006 0 0 1 6.512 3.16z",fill:"#f79e1b"})]}),(!e.brand||"visa"!==e.brand&&"mastercard"!==e.brand)&&(0,a.jsx)(f,{className:"h-4 w-4 mr-2"}),(0,a.jsxs)("span",{children:["•••• ",e.last4," ",e.isDefault&&(0,a.jsx)("span",{className:"text-xs text-primary ml-1",children:"(Default)"})]})]})},e._id)),[l]),j=(0,n.useMemo)(()=>{if(!u)return null;let e=l.find(e=>e._id===u);return e?(0,a.jsx)(o.Zp,{className:"border-primary/20 shadow-sm",children:(0,a.jsx)(o.Wu,{className:"p-4",children:(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsxs)("div",{className:`p-3 rounded-md ${e.isDefault?"bg-primary/10":"bg-gray-100 dark:bg-gray-800"}`,children:["visa"===e.brand&&(0,a.jsxs)("svg",{viewBox:"0 0 32 21",className:"h-5 w-5",children:[(0,a.jsx)("path",{d:"M26.58 21H5.42A5.42 5.42 0 0 1 0 15.58V5.42A5.42 5.42 0 0 1 5.42 0h21.16A5.42 5.42 0 0 1 32 5.42v10.16A5.42 5.42 0 0 1 26.58 21z",fill:"#fff"}),(0,a.jsx)("path",{d:"M12.322 7.583c.165-.437.352-.867.55-1.289h-1.09c-.33.39-.65.803-.957 1.234-.1-.431-.415-.845-.736-1.234h-.922c.616.452 1.013 1.014 1.013 1.716 0 .669-.352 1.289-.968 1.716h1.112c.308-.365.583-.803.781-1.274.187.471.451.909.77 1.274h1.134c-.627-.427-.968-1.047-.968-1.716a2.069 2.069 0 0 1 .28-1.427z",fill:"#00579f"}),(0,a.jsx)("path",{d:"M16.9 7.233c-.319 0-.605.075-.825.224-.209.149-.319.373-.319.634 0 .5.407.764 1.013.94l.297.09c.341.104.517.193.517.365 0 .268-.275.432-.726.432-.385 0-.671-.075-.979-.277l-.143.67c.33.164.737.254 1.112.254.374 0 .682-.075.902-.239.242-.164.374-.403.374-.7 0-.537-.44-.81-1.024-.97l-.297-.09c-.275-.074-.506-.164-.506-.343 0-.224.231-.373.594-.373.33 0 .649.09.869.194l.132-.634a2.365 2.365 0 0 0-.99-.179zm2.772 0c-.22 0-.407.03-.55.09l-.11.642c.143-.06.33-.104.55-.104.407-.15.66.224.66.642 0 .582-.308.94-.77.94-.242 0-.44-.06-.627-.164l-.121.656c.187.09.506.149.792.149.891 0 1.463-.642 1.463-1.61 0-.94-.517-1.24-1.288-1.24zm-6.578.09l-.209 1.28c-.22-.06-.44-.09-.66-.09-.56 0-1.024.149-1.024.582 0 .194.121.328.297.403.22.09.275.12.275.209 0 .134-.143.194-.341.194a1.9 1.9 0 0 1-.638-.12l-.11.626c.22.075.55.12.847.12.891 0 1.365-.373 1.365-.94 0-.224-.132-.387-.33-.477-.165-.075-.22-.09-.22-.179 0-.104.11-.164.297-.164.165 0 .374.03.55.09l.11-.634h-.209zm13.64-.09c-.33 0-.638.09-.869.373l.088-.373h-.748l-.572 2.68h.847l.33-1.582c.11-.507.396-.776.737-.776.11 0 .22.015.308.045l.165-.746a1.274 1.274 0 0 0-.286-.03zm-8.415.09l-.671 2.68h.847l.66-2.68h-.836zm-3.96 0l-.847 1.88-.341-1.88h-.902l-.781 2.68h.792l.484-1.85.396 1.85h.55l.99-1.85-.495 1.85h.803l.847-2.68h-1.497z",fill:"#00579f"})]}),"mastercard"===e.brand&&(0,a.jsxs)("svg",{viewBox:"0 0 32 21",className:"h-5 w-5",children:[(0,a.jsx)("path",{d:"M26.58 21H5.42A5.42 5.42 0 0 1 0 15.58V5.42A5.42 5.42 0 0 1 5.42 0h21.16A5.42 5.42 0 0 1 32 5.42v10.16A5.42 5.42 0 0 1 26.58 21z",fill:"#fff"}),(0,a.jsx)("path",{d:"M23.209 7.465c0-1.196-.946-2.142-2.142-2.142h-10.12c-1.196 0-2.142.946-2.142 2.142v6.07c0 1.196.946 2.142 2.142 2.142h10.12c1.196 0 2.142-.946 2.142-2.142v-6.07z",fill:"#ff5f00"}),(0,a.jsx)("path",{d:"M12.947 10.5a4.006 4.006 0 0 1 1.535-3.16a4.006 4.006 0 1 0 0 6.32a4.006 4.006 0 0 1-1.535-3.16z",fill:"#eb001b"}),(0,a.jsx)("path",{d:"M20.994 10.5a4.006 4.006 0 0 1-6.512 3.16a4.006 4.006 0 0 0 0-6.32a4.006 4.006 0 0 1 6.512 3.16z",fill:"#f79e1b"})]}),(!e.brand||"visa"!==e.brand&&"mastercard"!==e.brand)&&(0,a.jsx)(f,{className:`h-5 w-5 ${e.isDefault?"text-primary":""}`})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("p",{className:"font-medium",children:["•••• ",e.last4]}),e.isDefault&&(0,a.jsx)("span",{className:"ml-2 text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full",children:"Default"})]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.brand&&e.brand.charAt(0).toUpperCase()+e.brand.slice(1)," • Expires ",e.expMonth,"/",e.expYear]}),e.cardholderName&&(0,a.jsx)("p",{className:"text-sm mt-1",children:e.cardholderName})]})]})})})}):null},[u,l]);(0,n.useEffect)(()=>{(async()=>{try{h(!0);let e=await k();c(e);let t=e.find(e=>e.isDefault);t?m(t._id):e.length>0&&m(e[0]._id)}catch(e){console.error("Error fetching payment methods:",e),i("Failed to load payment methods")}finally{h(!1)}})()},[i]);let w=async()=>{if(!u){i("Please select a payment method");return}y(!0);try{let a=l.find(e=>e._id===u);if(!a)throw Error("Selected payment method not found");let n=await _({paymentMethodId:a.stripePaymentMethodId,amount:e,currency:t,description:r});if(n.success)s();else if("requires_action"===n.status||"requires_confirmation"===n.status)throw Error("This payment requires additional authentication. Please use a different payment method.");else throw Error(`Payment failed: ${n.status}`)}catch(e){console.error("Payment error:",e),i(e.message||"Payment processing failed")}finally{y(!1)}};return p?(0,a.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,a.jsx)(g.A,{className:"h-8 w-8 animate-spin text-primary"})}):0===l.length?(0,a.jsx)("div",{className:"text-center py-4",children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"No saved payment methods"})}):(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"text-sm font-medium flex items-center",children:[(0,a.jsx)(f,{className:"h-4 w-4 mr-2 text-primary"}),"Select Payment Method"]}),(0,a.jsxs)(ek.l6,{value:u||"",onValueChange:m,children:[(0,a.jsx)(ek.bq,{className:"bg-white dark:bg-gray-800",children:(0,a.jsx)(ek.yv,{placeholder:"Select a payment method"})}),(0,a.jsx)(ek.gC,{children:b})]})]}),j,(0,a.jsx)(d.$,{onClick:w,disabled:!u||x||e<1e3,className:"w-full bg-green-600 hover:bg-green-700 text-white",children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("svg",{className:"mr-2 h-4 w-4",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,a.jsx)("path",{d:"M13 9L15 11L21 5",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,a.jsx)("path",{d:"M21 12V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H16",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),"Recharge ",v]})})]})});var eS=r(54987),eA=r(52581);async function e_(e){let t=await fetch("http://localhost:4000/api/users/me/auto-recharge",{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Failed to update auto-recharge settings");return t.json()}function eE(){let[e,t]=(0,n.useState)(!0),[r,s]=(0,n.useState)(!1),[i,u]=(0,n.useState)(!1),[m,p]=(0,n.useState)(1),[h,f]=(0,n.useState)(0),x=async()=>{try{if(s(!0),m<0)throw Error("Threshold cannot be negative");await e_({autoRechargeEnabled:i,autoRechargeThreshold:m}),eA.o.success("Auto-recharge settings updated successfully")}catch(e){console.error("Failed to update auto-recharge settings:",e),eA.o.error(e.message||"Failed to update auto-recharge settings")}finally{s(!1)}};return(0,a.jsxs)(o.Zp,{children:[(0,a.jsxs)(o.aR,{children:[(0,a.jsx)(o.ZB,{className:"text-lg",children:"Auto-Recharge Settings"}),(0,a.jsx)(o.BT,{children:"Automatically add funds when your balance falls below a threshold"})]}),(0,a.jsx)(o.Wu,{children:e?(0,a.jsx)("div",{className:"flex justify-center py-6",children:(0,a.jsx)(g.A,{className:"h-6 w-6 animate-spin text-primary"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"space-y-0.5",children:[(0,a.jsx)(c.J,{children:"Enable Auto-Recharge"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Automatically add funds when your balance is low"})]}),(0,a.jsx)(eS.d,{checked:i,onCheckedChange:u})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"amount",children:"Amount to reload ($)"}),(0,a.jsx)(l.p,{id:"amount",type:"number",step:"0.01",min:"10",value:h,onChange:e=>f(parseFloat(e.target.value)),placeholder:"10.00"}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Amount to add when auto-recharge is triggered (minimum $10)"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(c.J,{htmlFor:"threshold",children:"When threshold reaches ($)"}),(0,a.jsx)(l.p,{id:"threshold",type:"number",step:"0.01",min:"0",value:m,onChange:e=>p(parseFloat(e.target.value)),placeholder:"1.00",disabled:!i}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground",children:"Auto-recharge when your balance falls below this amount"})]}),(0,a.jsx)(d.$,{onClick:x,disabled:r||!i,className:"w-full",children:r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):"Save Settings"})]})})]})}var eP=r(76180),ez=r.n(eP),eR=r(63503),eM=r(5336);function eF({open:e,onOpenChange:t,title:r,description:s,actionLabel:i,onAction:o}){let[l,c]=(0,n.useState)(!1);return(0,a.jsxs)(eR.lG,{open:e,onOpenChange:t,children:[(0,a.jsxs)(eR.Cf,{className:"sm:max-w-md",children:[(0,a.jsxs)(eR.c7,{className:"flex flex-col items-center text-center",children:[(0,a.jsx)("div",{className:"jsx-8384fac354370dcf bg-green-100 dark:bg-green-900/30 p-3 rounded-full mb-4",children:(0,a.jsx)(eM.A,{className:"h-8 w-8 text-green-600 dark:text-green-400"})}),(0,a.jsx)(eR.L3,{className:"text-xl",children:r}),(0,a.jsx)(eR.rr,{className:"pt-2 max-w-sm mx-auto",children:s})]}),(0,a.jsx)("div",{className:"jsx-8384fac354370dcf flex justify-center mt-4",children:i&&o?(0,a.jsx)(d.$,{onClick:o,children:i}):(0,a.jsx)(d.$,{onClick:()=>t(!1),children:"Close"})}),l&&(0,a.jsx)("div",{className:"jsx-8384fac354370dcf confetti-container",children:Array.from({length:50}).map((e,t)=>(0,a.jsx)("div",{style:{left:`${100*Math.random()}%`,animationDelay:`${3*Math.random()}s`,backgroundColor:`hsl(${360*Math.random()}, 100%, 50%)`},className:"jsx-8384fac354370dcf confetti"},t))})]}),(0,a.jsx)(ez(),{id:"8384fac354370dcf",children:".confetti-container{position:absolute;top:0;left:0;width:100%;height:100%;overflow:hidden;pointer-events:none;z-index:1000}.confetti{position:absolute;width:10px;height:10px;opacity:0;-webkit-transform:translatey(0)rotate(0);-moz-transform:translatey(0)rotate(0);-ms-transform:translatey(0)rotate(0);-o-transform:translatey(0)rotate(0);transform:translatey(0)rotate(0);-webkit-animation:confetti-fall 3s ease-out forwards;-moz-animation:confetti-fall 3s ease-out forwards;-o-animation:confetti-fall 3s ease-out forwards;animation:confetti-fall 3s ease-out forwards}@-webkit-keyframes confetti-fall{0%{opacity:1;-webkit-transform:translatey(-100px)rotate(0deg);transform:translatey(-100px)rotate(0deg)}100%{opacity:0;-webkit-transform:translatey(100vh)rotate(720deg);transform:translatey(100vh)rotate(720deg)}}@-moz-keyframes confetti-fall{0%{opacity:1;-moz-transform:translatey(-100px)rotate(0deg);transform:translatey(-100px)rotate(0deg)}100%{opacity:0;-moz-transform:translatey(100vh)rotate(720deg);transform:translatey(100vh)rotate(720deg)}}@-o-keyframes confetti-fall{0%{opacity:1;-o-transform:translatey(-100px)rotate(0deg);transform:translatey(-100px)rotate(0deg)}100%{opacity:0;-o-transform:translatey(100vh)rotate(720deg);transform:translatey(100vh)rotate(720deg)}}@keyframes confetti-fall{0%{opacity:1;-webkit-transform:translatey(-100px)rotate(0deg);-moz-transform:translatey(-100px)rotate(0deg);-o-transform:translatey(-100px)rotate(0deg);transform:translatey(-100px)rotate(0deg)}100%{opacity:0;-webkit-transform:translatey(100vh)rotate(720deg);-moz-transform:translatey(100vh)rotate(720deg);-o-transform:translatey(100vh)rotate(720deg);transform:translatey(100vh)rotate(720deg)}}"})]})}let eO=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});function eD(){let e=(0,i.useRouter)(),[t,r]=(0,n.useState)("add-funds"),[s,h]=(0,n.useState)("card"),[N,_]=(0,n.useState)("25"),[P,z]=(0,n.useState)(""),[R,M]=(0,n.useState)(!1),[F,O]=(0,n.useState)([]),[D,$]=(0,n.useState)([]),[I,T]=(0,n.useState)(!1),[B,q]=(0,n.useState)(""),[L,H]=(0,n.useState)(!1),[W,U]=(0,n.useState)(!1),[V,G]=(0,n.useState)({title:"",description:""}),[Z,Y]=(0,n.useState)({freeMinutesRemaining:0,paidMinutes:0,totalMinutesAvailable:0,usingFreeCredits:!1,callPricePerMinute:.1,monthlyResetDate:1,monthlyAllowance:0}),[J,X]=(0,n.useState)(100*parseInt(N)),[K,Q]=(0,n.useState)(!0),ee=async()=>{try{let e=await (0,E.mP)();return console.log("Refreshed user credits:",e),Y({freeMinutesRemaining:e.freeMinutesRemaining||0,paidMinutes:e.paidMinutes||0,totalMinutesAvailable:e.totalMinutesAvailable||0,usingFreeCredits:e.usingFreeCredits||!1,callPricePerMinute:e.callPricePerMinute||.1,monthlyResetDate:e.monthlyResetDate||1,monthlyAllowance:e.monthlyAllowance||0}),e}catch(e){return console.error("Error fetching user credits:",e),null}},et=()=>{M(!0),q("");try{if(J<1e3)throw Error("Minimum amount is $10");"paypal"===s?alert("PayPal integration would be implemented here"):"apple"===s&&alert("Apple Pay integration would be implemented here")}catch(e){console.error("Payment error:",e),q(e.message||"Payment processing failed")}finally{M(!1)}},er=async()=>{await ee(),G({title:"Payment Successful",description:`Your payment of ${new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(J/100)} has been processed successfully.`}),U(!0),"payment-history"===t&&O((await A()).transactions)};return(0,a.jsxs)(w.default,{children:[(0,a.jsx)(eF,{open:W,onOpenChange:U,title:V.title,description:V.description}),(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[(0,a.jsxs)(d.$,{variant:"ghost",size:"icon",onClick:()=>e.back(),className:"rounded-full hover:bg-gray-100 dark:hover:bg-gray-800",children:[(0,a.jsx)(x.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{className:"sr-only",children:"Back"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Billing & Payments"}),(0,a.jsx)("p",{className:"text-muted-foreground mt-1",children:"Manage your payment methods and view transaction history"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"lg:col-span-1 space-y-4",children:[(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsx)(o.ZB,{className:"text-xl",children:"Current Plan"})}),(0,a.jsxs)(o.Wu,{className:"space-y-4",children:[(0,a.jsx)("div",{className:"space-y-4",children:Z.monthlyAllowance>0?(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Monthly Allowance"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-foreground",children:[Z.monthlyAllowance.toFixed(0)," ",(0,a.jsx)("span",{className:"text-lg font-normal text-muted-foreground",children:"minutes"})]})]}),Z.paidMinutes>0&&(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-xs font-medium text-muted-foreground mb-1",children:"Balance"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full bg-emerald-500"}),(0,a.jsxs)("span",{className:"text-lg font-semibold text-emerald-600 dark:text-emerald-400",children:["$",(Z.paidMinutes*Z.callPricePerMinute).toFixed(2)]})]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["(",Z.paidMinutes.toFixed(0)," min)"]})]})]}):(0,a.jsx)("div",{className:"text-center space-y-3",children:Z.paidMinutes>0?(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground mb-2",children:"Current Balance"}),(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full bg-emerald-500"}),(0,a.jsxs)("span",{className:"text-3xl font-bold text-emerald-600 dark:text-emerald-400",children:["$",(Z.paidMinutes*Z.callPricePerMinute).toFixed(2)]})]}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground mt-1",children:[Z.paidMinutes.toFixed(0)," minutes available"]})]}):(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground mb-2",children:"Current Balance"}),(0,a.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full bg-gray-400"}),(0,a.jsx)("span",{className:"text-3xl font-bold text-gray-500 dark:text-gray-400",children:"$0.00"})]}),(0,a.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Add funds to start making calls"})]})})}),(0,a.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-emerald-700 dark:text-emerald-400",children:"Minutes Remaining"}),(0,a.jsxs)("span",{className:"font-medium text-emerald-600 dark:text-emerald-400",children:[Z.totalMinutesAvailable.toFixed(0)," min"]})]}),(0,a.jsx)("div",{className:"h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden",children:(0,a.jsx)("div",{className:"h-full bg-emerald-500 rounded-full",style:{width:`${Math.min(100,Z.totalMinutesAvailable/Math.max(Z.monthlyAllowance,1)*100)}%`}})})]}),(0,a.jsx)("div",{className:"pt-4 border-t border-gray-200 dark:border-gray-700",children:(0,a.jsx)("div",{className:"flex items-center justify-between",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Next Reset Date"}),(0,a.jsx)("p",{className:"text-sm font-semibold",children:(()=>{let e=new Date,t=e.getMonth(),r=e.getFullYear(),s=Z.monthlyResetDate||1,a=new Date(r,t,s);return e.getDate()>=s&&(a=new Date(r,t+1,s)),a.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})()})]})})})]})]}),(0,a.jsxs)(o.Zp,{children:[(0,a.jsxs)(o.aR,{className:"pb-2 flex flex-row items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(o.ZB,{className:"text-xl",children:"Payment Methods"}),(0,a.jsx)(o.BT,{children:"Manage your saved payment methods"})]}),"add-funds"===t&&(0,a.jsxs)(d.$,{variant:"outline",size:"sm",onClick:()=>H(!0),children:[(0,a.jsx)(f,{className:"h-4 w-4 mr-2"}),"Add New Card"]})]}),(0,a.jsx)(o.Wu,{className:"pt-2",children:I?(0,a.jsx)("div",{className:"flex justify-center py-6",children:(0,a.jsx)(g.A,{className:"h-6 w-6 animate-spin text-primary"})}):0===D.length?(0,a.jsxs)("div",{className:"text-center py-6",children:[(0,a.jsx)("div",{className:"bg-gray-100 dark:bg-gray-800 p-4 rounded-md inline-flex mb-3",children:(0,a.jsx)(f,{className:"h-6 w-6 text-muted-foreground"})}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"No payment methods saved"}),(0,a.jsx)(d.$,{variant:"outline",size:"sm",className:"mt-4",onClick:()=>{r("add-funds"),H(!0)},children:"Add Payment Method"})]}):(0,a.jsx)("div",{className:"space-y-3",children:D.map(e=>(0,a.jsxs)("div",{className:`flex items-center justify-between p-3 rounded-lg border ${e.isDefault?"border-primary/50 bg-primary/5":"border-border"}`,children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:`p-2 rounded-md ${e.isDefault?"bg-primary/10":"bg-gray-100 dark:bg-gray-800"}`,children:(0,a.jsx)(f,{className:`h-5 w-5 ${e.isDefault?"text-primary":""}`})}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("p",{className:"font-medium",children:["•••• ",e.last4]}),e.isDefault&&(0,a.jsx)("span",{className:"ml-2 text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full",children:"Default"})]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.brand&&e.brand.charAt(0).toUpperCase()+e.brand.slice(1)," ","• Expires ",e.expMonth,"/",e.expYear]})]})]}),(0,a.jsxs)("div",{className:"flex gap-1",children:[!e.isDefault&&(0,a.jsx)(d.$,{variant:"ghost",size:"sm",onClick:async()=>{try{await C(e._id);let t=await k();$(t)}catch(e){console.error("Error setting default payment method:",e),q("Failed to set default payment method")}},title:"Set as default",children:(0,a.jsx)(y.A,{className:"h-4 w-4"})}),(0,a.jsx)(d.$,{variant:"ghost",size:"sm",onClick:async()=>{try{await S(e._id);let t=await k();$(t)}catch(e){console.error("Error removing payment method:",e),q("Failed to remove payment method")}},title:"Remove payment method",children:(0,a.jsx)(v.A,{className:"h-4 w-4 text-destructive"})})]})]},e._id))})})]})]}),(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)(u.tU,{value:t,onValueChange:r,className:"space-y-6",children:[(0,a.jsxs)(u.j7,{className:"grid grid-cols-3",children:[(0,a.jsx)(u.Xi,{value:"add-funds",children:"Add Funds"}),(0,a.jsx)(u.Xi,{value:"payment-history",children:"Payment History"}),(0,a.jsx)(u.Xi,{value:"auto-recharge",children:"Auto-Recharge"})]}),(0,a.jsxs)(u.av,{value:"add-funds",className:"space-y-6",children:[(0,a.jsxs)(o.Zp,{children:[(0,a.jsx)(o.aR,{children:(0,a.jsx)(o.ZB,{className:"text-lg",children:"Select Amount"})}),(0,a.jsxs)(o.Wu,{children:["custom"!==N&&10>parseInt(N)&&(0,a.jsx)("p",{className:"text-red-500 text-sm mb-3",children:"Minimum amount is $10"}),(0,a.jsxs)(p.z,{value:N,onValueChange:_,className:"grid grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(p.C,{value:"25",id:"amount-25",className:"peer sr-only"}),(0,a.jsxs)(c.J,{htmlFor:"amount-25",className:"flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white dark:bg-gray-800 p-4 hover:bg-gray-50 dark:hover:bg-gray-700 peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground mb-1",children:"Basic"}),(0,a.jsx)("span",{className:"text-2xl font-bold",children:"$25"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(p.C,{value:"50",id:"amount-50",className:"peer sr-only"}),(0,a.jsxs)(c.J,{htmlFor:"amount-50",className:"flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white dark:bg-gray-800 p-4 hover:bg-gray-50 dark:hover:bg-gray-700 peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground mb-1",children:"Standard"}),(0,a.jsx)("span",{className:"text-2xl font-bold",children:"$50"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(p.C,{value:"100",id:"amount-100",className:"peer sr-only"}),(0,a.jsxs)(c.J,{htmlFor:"amount-100",className:"flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white dark:bg-gray-800 p-4 hover:bg-gray-50 dark:hover:bg-gray-700 peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground mb-1",children:"Plus"}),(0,a.jsx)("span",{className:"text-2xl font-bold",children:"$100"})]})]})]}),(0,a.jsxs)("div",{className:"mt-4 relative",children:[(0,a.jsx)(c.J,{htmlFor:"custom-amount",className:"text-sm",children:"Custom Amount"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("span",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground",children:"$"}),(0,a.jsx)(l.p,{id:"custom-amount",type:"number",min:"10",placeholder:"Enter amount (min $10)",className:`pl-7 ${"custom"!==N||K?"":"border-red-500"}`,value:P,onChange:e=>z(e.target.value),onClick:()=>_("custom")})]}),"custom"===N&&!K&&(0,a.jsx)("p",{className:"text-red-500 text-sm mt-1",children:"Minimum amount is $10"})]})]})]}),"card"===s&&(0,a.jsxs)(o.Zp,{children:[(0,a.jsxs)(o.aR,{className:"flex flex-row items-center justify-between",children:[(0,a.jsx)(o.ZB,{className:"text-lg",children:"Card Details"}),D.length>0&&(0,a.jsx)(d.$,{variant:"outline",size:"sm",onClick:()=>H(!L),children:L?"Use Saved Card":"Use New Card"})]}),(0,a.jsx)(o.Wu,{children:(0,a.jsxs)(ej,{children:[B&&(0,a.jsx)("div",{className:"text-red-500 text-sm mb-4",children:B}),L?(0,a.jsx)(eN,{amount:J,currency:"usd",description:`Adding funds: $${"custom"===N?P:N}`,onSuccess:async()=>{if("custom"===N?!K:10>parseInt(N)){q("Minimum amount is $10");return}await er()},onError:e=>{q(e)}}):(0,a.jsx)(eC,{amount:J,currency:"usd",description:`Adding funds: $${"custom"===N?P:N}`,onSuccess:async()=>{if("custom"===N?!K:10>parseInt(N)){q("Minimum amount is $10");return}await er()},onError:e=>{q(e)}})]})})]}),"paypal"===s&&(0,a.jsx)(o.Zp,{children:(0,a.jsxs)(o.Wu,{className:"p-6 text-center",children:[(0,a.jsx)("p",{className:"text-lg font-medium mb-4",children:"Continue to PayPal to complete your payment"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:"You'll be redirected to PayPal to complete your payment securely."}),(0,a.jsx)(d.$,{onClick:et,disabled:R||("custom"===N?!K:10>parseInt(N)),children:R?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):"Pay with PayPal"})]})}),"apple"===s&&(0,a.jsx)(o.Zp,{children:(0,a.jsxs)(o.Wu,{className:"p-6 text-center",children:[(0,a.jsx)("p",{className:"text-lg font-medium mb-4",children:"Continue with Apple Pay"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:"You'll be prompted to authenticate your payment with Apple Pay."}),(0,a.jsx)(d.$,{onClick:et,disabled:R||("custom"===N?!K:10>parseInt(N)),className:"bg-black hover:bg-gray-800",children:R?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):"Pay with Apple Pay"})]})})]}),(0,a.jsx)(u.av,{value:"auto-recharge",children:(0,a.jsx)(eE,{})}),(0,a.jsx)(u.av,{value:"payment-history",children:(0,a.jsxs)(o.Zp,{children:[(0,a.jsxs)(o.aR,{className:"pb-2",children:[(0,a.jsx)(o.ZB,{className:"text-lg",children:"Payment History"}),(0,a.jsx)(o.BT,{children:"View all your past transactions"})]}),(0,a.jsxs)(o.Wu,{children:[(0,a.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,a.jsxs)("div",{className:"relative w-64",children:[(0,a.jsx)(b.A,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400"}),(0,a.jsx)(l.p,{placeholder:"Search transactions...",className:"pl-8"})]})}),(0,a.jsx)("div",{className:"rounded-md border",children:(0,a.jsxs)(m.XI,{children:[(0,a.jsx)(m.A0,{children:(0,a.jsxs)(m.Hj,{children:[(0,a.jsx)(m.nd,{children:"Date"}),(0,a.jsx)(m.nd,{children:"ID"}),(0,a.jsx)(m.nd,{children:"Email"}),(0,a.jsx)(m.nd,{className:"text-right",children:"Amount"}),(0,a.jsx)(m.nd,{children:"Status"}),(0,a.jsx)(m.nd,{className:"text-right",children:"Actions"})]})}),(0,a.jsx)(m.BF,{children:I?(0,a.jsx)(m.Hj,{children:(0,a.jsxs)(m.nA,{colSpan:6,className:"text-center py-8",children:[(0,a.jsx)(g.A,{className:"h-6 w-6 animate-spin mx-auto"}),(0,a.jsx)("p",{className:"mt-2 text-sm text-muted-foreground",children:"Loading transaction history..."})]})}):B?(0,a.jsx)(m.Hj,{children:(0,a.jsx)(m.nA,{colSpan:6,className:"text-center py-8 text-red-500",children:B})}):0===F.length?(0,a.jsx)(m.Hj,{children:(0,a.jsx)(m.nA,{colSpan:6,className:"text-center py-8",children:(0,a.jsx)("p",{className:"text-muted-foreground",children:"No transactions found"})})}):F.map(e=>(0,a.jsxs)(m.Hj,{children:[(0,a.jsx)(m.nA,{children:eO(e.createdAt)}),(0,a.jsx)(m.nA,{children:e.stripePaymentIntentId.substring(0,8)}),(0,a.jsx)(m.nA,{className:"max-w-[200px] truncate",children:e.email}),(0,a.jsxs)(m.nA,{className:"text-right font-medium",children:["$",e.amount.toFixed(2)]}),(0,a.jsx)(m.nA,{children:(0,a.jsx)("span",{className:`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${"completed"===e.status||"succeeded"===e.status?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"failed"===e.status?"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400":"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"}`,children:"completed"===e.status||"succeeded"===e.status?"Completed":"failed"===e.status?"Failed":"Pending"})}),(0,a.jsx)(m.nA,{className:"text-right",children:(0,a.jsxs)(d.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:[(0,a.jsx)(j.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"View receipt"})]})})]},e._id))})]})})]})]})})]})})]})]})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>m,Es:()=>h,HM:()=>d,L3:()=>f,c7:()=>p,lG:()=>o,rr:()=>x,zM:()=>l});var s=r(60687);r(43210);var a=r(26134),n=r(11860),i=r(4780);function o({...e}){return(0,s.jsx)(a.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,s.jsx)(a.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,s.jsx)(a.ZL,{"data-slot":"dialog-portal",...e})}function d({...e}){return(0,s.jsx)(a.bm,{"data-slot":"dialog-close",...e})}function u({className:e,...t}){return(0,s.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-650 bg-black/50",e),...t})}function m({className:e,children:t,...r}){return(0,s.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,s.jsx)(u,{}),(0,s.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-650 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...r,children:[t,(0,s.jsxs)(a.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(n.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function p({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function h({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function f({className:e,...t}){return(0,s.jsx)(a.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",e),...t})}function x({className:e,...t}){return(0,s.jsx)(a.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",e),...t})}},64658:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),o=r(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["(workspace)",{children:["billing",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,72099)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\billing\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,50184)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\billing\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(workspace)/billing/page",pathname:"/billing",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},72099:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(37413);r(61120);var a=r(20862);function n(){return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(a.default,{})})}},74075:e=>{"use strict";e.exports=require("zlib")},74678:(e,t,r)=>{"use strict";r.d(t,{H:()=>s,e:()=>a});let s="http://localhost:4000",a="pk_test_51ROz1YRpJ0zLf0aTbgbDkpShvfpNxdZPet1QXClapTckA7Cy0tsaxY2qY1dp8oSBGOFqnh0vugjd8mDluFWgKpRL00bACyumT8"},75913:(e,t,r)=>{"use strict";r(56397);var s=r(43210),a=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(s),n="undefined"!=typeof process&&process.env&&!0,i=function(e){return"[object String]"===Object.prototype.toString.call(e)},o=function(){function e(e){var t=void 0===e?{}:e,r=t.name,s=void 0===r?"stylesheet":r,a=t.optimizeForSpeed,o=void 0===a?n:a;l(i(s),"`name` must be a string"),this._name=s,this._deletedRulePlaceholder="#"+s+"-deleted-rule____{}",l("boolean"==typeof o,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=o,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0,this._nonce=null}var t=e.prototype;return t.setOptimizeForSpeed=function(e){l("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),l(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},t.isOptimizeForSpeed=function(){return this._optimizeForSpeed},t.inject=function(){var e=this;l(!this._injected,"sheet already injected"),this._injected=!0,this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},t.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},t.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},t.insertRule=function(e,t){return l(i(e),"`insertRule` accepts only strings"),"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++},t.replaceRule=function(e,t){this._optimizeForSpeed;var r=this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(s){n||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}return e},t.deleteRule=function(e){this._serverSheet.deleteRule(e)},t.flush=function(){this._injected=!1,this._rulesCount=0,this._serverSheet.cssRules=[]},t.cssRules=function(){return this._serverSheet.cssRules},t.makeStyleTag=function(e,t,r){t&&l(i(t),"makeStyleTag accepts only strings as second parameter");var s=document.createElement("style");this._nonce&&s.setAttribute("nonce",this._nonce),s.type="text/css",s.setAttribute("data-"+e,""),t&&s.appendChild(document.createTextNode(t));var a=document.head||document.getElementsByTagName("head")[0];return r?a.insertBefore(s,r):a.appendChild(s),s},function(e,t){for(var r=0;r<t.length;r++){var s=t[r];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(e,s.key,s)}}(e.prototype,[{key:"length",get:function(){return this._rulesCount}}]),e}();function l(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var c=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},d={};function u(e,t){if(!t)return"jsx-"+e;var r=String(t),s=e+r;return d[s]||(d[s]="jsx-"+c(e+"-"+r)),d[s]}function m(e,t){var r=e+(t=t.replace(/\/style/gi,"\\/style"));return d[r]||(d[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[r]}var p=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,s=void 0===r?null:r,a=t.optimizeForSpeed,n=void 0!==a&&a;this._sheet=s||new o({name:"styled-jsx",optimizeForSpeed:n}),this._sheet.inject(),s&&"boolean"==typeof n&&(this._sheet.setOptimizeForSpeed(n),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed());var r=this.getIdAndRules(e),s=r.styleId,a=r.rules;if(s in this._instancesCounts){this._instancesCounts[s]+=1;return}var n=a.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[s]=n,this._instancesCounts[s]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var s=this._fromServer&&this._fromServer[r];s?(s.parentNode.removeChild(s),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],s=e[1];return a.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:s}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,s=e.id;if(r){var a=u(s,r);return{styleId:a,rules:Array.isArray(t)?t.map(function(e){return m(a,e)}):[m(a,t)]}}return{styleId:u(s),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),h=s.createContext(null);h.displayName="StyleSheetContext";a.default.useInsertionEffect||a.default.useLayoutEffect;var f=void 0;function x(e){var t=f||s.useContext(h);return t&&t.add(e),null}x.dynamic=function(e){return e.map(function(e){return u(e[0],e[1])}).join(" ")},t.style=x},76180:(e,t,r)=>{"use strict";e.exports=r(75913).style},78148:(e,t,r)=>{"use strict";r.d(t,{b:()=>o});var s=r(43210),a=r(14163),n=r(60687),i=s.forwardRef((e,t)=>(0,n.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));i.displayName="Label";var o=i},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80013:(e,t,r)=>{"use strict";r.d(t,{J:()=>i});var s=r(60687);r(43210);var a=r(78148),n=r(4780);function i({className:e,...t}){return(0,s.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},84031:(e,t,r)=>{"use strict";var s=r(34452);function a(){}function n(){}n.resetWarningCache=a,e.exports=function(){function e(e,t,r,a,n,i){if(i!==s){var o=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:n,resetWarningCache:a};return r.PropTypes=r,r}},85763:(e,t,r)=>{"use strict";r.d(t,{Xi:()=>l,av:()=>c,j7:()=>o,tU:()=>i});var s=r(60687);r(43210);var a=r(55146),n=r(4780);function i({className:e,...t}){return(0,s.jsx)(a.bL,{"data-slot":"tabs",className:(0,n.cn)("flex flex-col gap-2",e),...t})}function o({className:e,...t}){return(0,s.jsx)(a.B8,{"data-slot":"tabs-list",className:(0,n.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-1",e),...t})}function l({className:e,...t}){return(0,s.jsx)(a.l9,{"data-slot":"tabs-trigger",className:(0,n.cn)("data-[state=active]:bg-background data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring inline-flex flex-1 items-center justify-center gap-1.5 rounded-md px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}function c({className:e,...t}){return(0,s.jsx)(a.UC,{"data-slot":"tabs-content",className:(0,n.cn)("flex-1 outline-none",e),...t})}},87955:(e,t,r)=>{e.exports=r(84031)()},88233:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},89667:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(60687);r(43210);var a=r(4780);function n({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},90270:(e,t,r)=>{"use strict";r.d(t,{bL:()=>w,zi:()=>N});var s=r(43210),a=r(70569),n=r(98599),i=r(11273),o=r(65551),l=r(83721),c=r(18853),d=r(14163),u=r(60687),m="Switch",[p,h]=(0,i.A)(m),[f,x]=p(m),g=s.forwardRef((e,t)=>{let{__scopeSwitch:r,name:i,checked:l,defaultChecked:c,required:m,disabled:p,value:h="on",onCheckedChange:x,form:g,...y}=e,[v,w]=s.useState(null),N=(0,n.s)(t,e=>w(e)),k=s.useRef(!1),C=!v||g||!!v.closest("form"),[S=!1,A]=(0,o.i)({prop:l,defaultProp:c,onChange:x});return(0,u.jsxs)(f,{scope:r,checked:S,disabled:p,children:[(0,u.jsx)(d.sG.button,{type:"button",role:"switch","aria-checked":S,"aria-required":m,"data-state":j(S),"data-disabled":p?"":void 0,disabled:p,value:h,...y,ref:N,onClick:(0,a.m)(e.onClick,e=>{A(e=>!e),C&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),C&&(0,u.jsx)(b,{control:v,bubbles:!k.current,name:i,value:h,checked:S,required:m,disabled:p,form:g,style:{transform:"translateX(-100%)"}})]})});g.displayName=m;var y="SwitchThumb",v=s.forwardRef((e,t)=>{let{__scopeSwitch:r,...s}=e,a=x(y,r);return(0,u.jsx)(d.sG.span,{"data-state":j(a.checked),"data-disabled":a.disabled?"":void 0,...s,ref:t})});v.displayName=y;var b=e=>{let{control:t,checked:r,bubbles:a=!0,...n}=e,i=s.useRef(null),o=(0,l.Z)(r),d=(0,c.X)(t);return s.useEffect(()=>{let e=i.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(o!==r&&t){let s=new Event("click",{bubbles:a});t.call(e,r),e.dispatchEvent(s)}},[o,r,a]),(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...n,tabIndex:-1,ref:i,style:{...e.style,...d,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function j(e){return e?"checked":"unchecked"}var w=g,N=v},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},99270:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[287,9176,7674,5814,598,5188,6034,9017,1476,4772],()=>r(64658));module.exports=s})();