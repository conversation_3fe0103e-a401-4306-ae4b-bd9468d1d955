"use strict";exports.id=2774,exports.ids=[2774],exports.modules={6607:(e,t,r)=>{r.d(t,{t:()=>a});var s=r(28863);async function a(e,t={}){let r=localStorage.getItem("access_token");if(!r){let e=await (0,s.J1)();if(!e.success)throw Error("No authentication token available");r=e.newAccessToken}let i=new Headers(t.headers||{});i.has("Authorization")||i.set("Authorization",`Bearer ${r}`);let n=await fetch(e,{...t,headers:i});if(401===n.status||403===n.status){console.log("Token expired, attempting refresh...");let r=await (0,s.J1)();if(!r.success)throw console.error("Token refresh failed"),Error("Authentication failed");console.log("Token refreshed, retrying request...");let a=new Headers(t.headers||{});return a.set("Authorization",`Bearer ${r.newAccessToken}`),fetch(e,{...t,headers:a})}return n}},26882:(e,t,r)=>{r.d(t,{A:()=>s});let s={src:"/_next/static/media/OROVA-WHITE.76096952.png",height:124,width:732,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAABCAMAAADU3h9xAAAAD1BMVEX////Aucj7+/vp5e38/PzbXIb5AAAABXRSTlNs0Yl2dMCoCVsAAAAJcEhZcwAACxMAAAsTAQCanBgAAAARSURBVHicY2BkZmBiYWBgAAAAQAALSpjpiwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:1}},28863:(e,t,r)=>{r.d(t,{HW:()=>i,J1:()=>l,_f:()=>n});var s=r(6607);let a="http://localhost:4000";async function i(){try{let e=await (0,s.t)(`${a}/api/auth/me`,{method:"GET"});if(!e.ok)return{success:!1,error:`Error: ${e.status}`};let t=await e.json(),r=t.userId||t._id||t.id,i=t.email;if(r&&i)return{success:!0,user:{fullName:t.fullName||i.split("@")[0],userId:r,email:i,role:t.role||"user"}};return{success:!1,error:"Invalid user data received"}}catch(e){return console.error("Error fetching user data:",e),{success:!1,error:"An error occurred while fetching user data"}}}function n(e=10){return()=>{}}async function l(){let e=localStorage.getItem("refresh_token");if(!e)return{success:!1};try{let t=await fetch(`${a}/api/auth/refresh`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!t.ok)return{success:!1};let r=await t.json();if(r.access_token)return localStorage.setItem("access_token",r.access_token),{success:!0,newAccessToken:r.access_token};return{success:!1}}catch(e){return console.error("Token refresh error:",e),{success:!1}}}},44493:(e,t,r)=>{r.d(t,{BT:()=>c,Wu:()=>o,ZB:()=>l,Zp:()=>i,aR:()=>n,wL:()=>d});var s=r(60687);r(43210);var a=r(4780);function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",e),...t})}function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("flex flex-col gap-1.5 px-6",e),...t})}function l({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6",e),...t})}},89667:(e,t,r)=>{r.d(t,{p:()=>i});var s=r(60687);r(43210);var a=r(4780);function i({className:e,type:t,...r}){return(0,s.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...r})}},91821:(e,t,r)=>{r.d(t,{Fc:()=>l,TN:()=>o,XL:()=>c});var s=r(60687);r(43210);var a=r(24224),i=r(4780);let n=(0,a.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-background text-foreground",destructive:"text-destructive-foreground [&>svg]:text-current *:data-[slot=alert-description]:text-destructive-foreground/80"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...r}){return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,i.cn)(n({variant:t}),e),...r})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-title",className:(0,i.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,i.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",e),...t})}},92774:(e,t,r)=>{r.r(t),r.d(t,{default:()=>A});var s=r(60687),a=r(43210),i=r(16189),n=r(51215),l=r(89667),c=r(44493),o=r(91821),d=r(6475);let u=(0,d.createServerReference)("40922dd30dbc05a2c82413a6ab04d49af413ddd216",d.callServer,void 0,d.findSourceMapURL,"loginUser");var m=r(86128),x=r(30474),f=r(26882),g=r(28863),h=r(12597),p=r(13861),v=r(29523);function b(){let{pending:e}=(0,n.useFormStatus)();return(0,s.jsx)(v.$,{type:"submit",className:"w-full h-12 bg-gradient-to-r from-[#383D73] to-[#74546D] text-white font-semibold transition-all duration-200 hover:scale-[1.02] hover:shadow-md",disabled:e,children:e?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign In"})}function A(){let e=(0,i.useRouter)(),[t,r]=(0,a.useState)(null),[n,d]=(0,a.useState)({}),[v,A]=(0,a.useState)(null),[j,y]=(0,a.useState)(!1);async function w(t){if(!v){r({success:!1,message:"Please complete the CAPTCHA."});return}t.append("recaptchaToken",v);let s=await u(t);if(r(s),s.success&&s.redirect&&s.tokens){localStorage.setItem("access_token",s.tokens.access_token),localStorage.setItem("refresh_token",s.tokens.refresh_token);let t=await (0,g.HW)();if(!t.success){r({success:!1,message:"Account not authorized. Please contact an administrator."}),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user_data");return}t.user&&localStorage.setItem("user_data",JSON.stringify({...t.user,_id:t.user.userId})),e.push(s.redirect);return}if(s.fieldErrors){let e={};Object.entries(s.fieldErrors).forEach(([t,r])=>{Array.isArray(r)&&r.length>0&&(e[t]=r[0])}),d(e)}else d({})}return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"min-h-screen flex flex-col md:flex-row",children:[(0,s.jsx)("div",{className:"hidden md:flex md:w-1/2 bg-gradient-to-br from-[#383D73] to-[#74546D] text-white flex-col justify-center items-center p-8",children:(0,s.jsxs)("div",{className:"max-w-md mx-auto flex flex-col items-center space-y-12",children:[(0,s.jsx)(x.default,{src:f.A,alt:"Orova Logo",width:280,height:70}),(0,s.jsxs)("div",{className:"text-center space-y-4",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold",children:"Welcome to Orova AI"}),(0,s.jsx)("p",{className:"text-lg opacity-80",children:"The next generation calling platform powered by artificial intelligence"})]}),(0,s.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-6 w-full",children:[(0,s.jsx)("blockquote",{className:"text-center italic",children:'"Orova transformed our customer engagement with intelligent calls that feel personal and professional."'}),(0,s.jsxs)("div",{className:"mt-4 flex items-center justify-center",children:[(0,s.jsx)("div",{className:"h-px w-12 bg-white/30"}),(0,s.jsx)("div",{className:"h-px w-12 bg-white/30"})]})]})]})}),(0,s.jsx)("div",{className:"flex flex-1 items-center justify-center bg-gray-50 dark:bg-gray-900 p-8",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsx)("div",{className:"flex justify-center mb-8 md:hidden",children:(0,s.jsx)(x.default,{src:f.A,alt:"Orova Logo",width:200,height:50,className:"dark:filter dark:brightness-0 dark:invert"})}),(0,s.jsxs)(c.Zp,{className:"bg-white dark:bg-gray-800 shadow-xl rounded-xl border-0",children:[(0,s.jsxs)(c.aR,{className:"space-y-2 pb-2",children:[(0,s.jsx)(c.ZB,{className:"text-center text-2xl font-bold bg-gradient-to-r from-[#383D73] to-[#74546D] bg-clip-text text-transparent dark:text-white",children:"Welcome to Orova"}),(0,s.jsx)("p",{className:"text-center text-gray-500 dark:text-gray-400",children:"Sign in to your account"})]}),(0,s.jsxs)(c.Wu,{className:"pt-4",children:[t&&!t.success&&!t.fieldErrors&&(0,s.jsx)(o.Fc,{className:"mb-4 bg-red-50 text-red-800 border-red-200",children:(0,s.jsx)(o.TN,{children:t.message})}),(0,s.jsxs)("form",{action:w,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.p,{name:"email",type:"email",placeholder:"Email",required:!0,className:"h-12 bg-gray-50 dark:bg-gray-700/50 border-gray-200 dark:border-gray-700","aria-invalid":!!n.email}),n.email&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:n.email})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(l.p,{name:"password",type:j?"text":"password",placeholder:"Password",required:!0,className:"h-12 bg-gray-50 dark:bg-gray-700/50 border-gray-200 dark:border-gray-700 pr-10","aria-invalid":!!n.password}),(0,s.jsx)("button",{type:"button",onClick:()=>y(e=>!e),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none",tabIndex:-1,"aria-label":j?"Hide password":"Show password",children:j?(0,s.jsx)(h.A,{className:"h-5 w-5"}):(0,s.jsx)(p.A,{className:"h-5 w-5"})})]}),n.password&&(0,s.jsx)("p",{className:"text-sm text-red-500 mt-1",children:n.password})]}),(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsx)(m.A,{sitekey:"6LfyPfgqAAAAAJy91ZTqWkEaQcGJJtNC-MnxUa6e",onChange:e=>A(e)})}),(0,s.jsx)("div",{className:"flex items-center justify-between"}),(0,s.jsx)(b,{})]})]})]}),(0,s.jsxs)("div",{className:"mt-6 text-center text-xs text-gray-500 dark:text-gray-400",children:["\xa9 ",new Date().getFullYear()," Orova AI. All rights reserved."]})]})})]})})}}};