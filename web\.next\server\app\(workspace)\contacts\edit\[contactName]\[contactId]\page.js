(()=>{var e={};e.id=6478,e.ids=[6478],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7476:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var n=a(37413);a(61120);var s=a(31801);function r({params:e}){return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(s.default,{contactName:e.contactName,contactId:e.contactId})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11860:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(62688).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},15079:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>p,gC:()=>m,l6:()=>d,yv:()=>c});var n=a(60687);a(43210);var s=a(22670),r=a(78272),o=a(13964),i=a(3589),l=a(4780);function d({...e}){return(0,n.jsx)(s.bL,{"data-slot":"select",...e})}function c({...e}){return(0,n.jsx)(s.WT,{"data-slot":"select-value",...e})}function u({className:e,children:t,...a}){return(0,n.jsxs)(s.l9,{"data-slot":"select-trigger",className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...a,children:[t,(0,n.jsx)(s.In,{asChild:!0,children:(0,n.jsx)(r.A,{className:"size-4 opacity-50"})})]})}function m({className:e,children:t,position:a="popper",...r}){return(0,n.jsx)(s.ZL,{children:(0,n.jsxs)(s.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[(0,n.jsx)(g,{}),(0,n.jsx)(s.LM,{className:(0,l.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,n.jsx)(x,{})]})})}function p({className:e,children:t,...a}){return(0,n.jsxs)(s.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...a,children:[(0,n.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,n.jsx)(s.VF,{children:(0,n.jsx)(o.A,{className:"size-4"})})}),(0,n.jsx)(s.p4,{children:t})]})}function g({className:e,...t}){return(0,n.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,n.jsx)(i.A,{className:"size-4"})})}function x({className:e,...t}){return(0,n.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,n.jsx)(r.A,{className:"size-4"})})}},18229:(e,t,a)=>{"use strict";a.d(t,{N:()=>v});var n=a(60687),s=a(45334),r=a.n(s),o=a(43210),i=a(40988),l=a(29523),d=a(70965),c=a(99270),u=a(4780);function m({className:e,...t}){return(0,n.jsx)(d.uB,{"data-slot":"command",className:(0,u.cn)("bg-popover text-popover-foreground flex h-full w-full flex-col overflow-hidden rounded-md",e),...t})}function p({className:e,...t}){return(0,n.jsxs)("div",{"data-slot":"command-input-wrapper",className:"flex h-9 items-center gap-2 border-b px-3",children:[(0,n.jsx)(c.A,{className:"size-4 shrink-0 opacity-50"}),(0,n.jsx)(d.uB.Input,{"data-slot":"command-input",className:(0,u.cn)("placeholder:text-muted-foreground flex h-10 w-full rounded-md bg-transparent py-3 text-sm outline-hidden disabled:cursor-not-allowed disabled:opacity-50",e),...t})]})}function g({...e}){return(0,n.jsx)(d.uB.Empty,{"data-slot":"command-empty",className:"py-6 text-center text-sm",...e})}function x({className:e,...t}){return(0,n.jsx)(d.uB.Group,{"data-slot":"command-group",className:(0,u.cn)("text-foreground [&_[cmdk-group-heading]]:text-muted-foreground overflow-hidden p-1 [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium",e),...t})}function h({className:e,...t}){return(0,n.jsx)(d.uB.Item,{"data-slot":"command-item",className:(0,u.cn)("data-[selected=true]:bg-accent data-[selected=true]:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...t})}a(63503);let v=({value:e,onChange:t})=>{let[a,s]=(0,o.useState)(!1),[d,c]=(0,o.useState)(""),u=r().tz.names().reduce((e,t)=>{let a=t.split("/")[0];return e[a]||(e[a]=[]),e[a].push(t),e},{});return(0,n.jsxs)(i.AM,{modal:!0,open:a,onOpenChange:s,children:[(0,n.jsx)(i.Wv,{asChild:!0,children:(0,n.jsx)(l.$,{variant:"outline",role:"combobox","aria-expanded":a,className:"w-full justify-between",children:e?`${e.split("/").pop()?.replace(/_/g," ")} (${r().tz(e).format("Z")})`:"Select timezone..."})}),(0,n.jsx)(i.hl,{className:"w-[250px] p-0",align:"start",side:"bottom",sideOffset:5,children:(0,n.jsxs)(m,{className:"max-h-[300px]",children:[(0,n.jsx)("div",{className:"sticky top-0 bg-background z-10 border-b",children:(0,n.jsx)(p,{placeholder:"Search timezone...",onValueChange:c,className:"py-2"})}),(0,n.jsxs)("div",{className:"overflow-y-auto max-h-[250px]",children:[(0,n.jsx)(g,{children:"No timezone found."}),Object.entries(u).map(([e,a])=>{let o=a.filter(e=>e.toLowerCase().includes(d.toLowerCase()));return 0===o.length?null:(0,n.jsx)(x,{heading:e,children:o.map(e=>(0,n.jsxs)(h,{value:e,onSelect:()=>{t(e),s(!1)},className:"cursor-pointer",children:[e.split("/").pop()?.replace(/_/g," ")," (",r().tz(e).format("Z"),")"]},e))},e)})]})]})})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26134:(e,t,a)=>{"use strict";a.d(t,{G$:()=>V,Hs:()=>y,UC:()=>et,VY:()=>en,ZL:()=>Q,bL:()=>K,bm:()=>es,hE:()=>ea,hJ:()=>ee,l9:()=>Y,lG:()=>C});var n=a(43210),s=a(70569),r=a(98599),o=a(11273),i=a(96963),l=a(65551),d=a(31355),c=a(32547),u=a(25028),m=a(46059),p=a(14163),g=a(1359),x=a(42247),h=a(63376),v=a(8730),f=a(60687),j="Dialog",[b,y]=(0,o.A)(j),[N,P]=b(j),C=e=>{let{__scopeDialog:t,children:a,open:s,defaultOpen:r,onOpenChange:o,modal:d=!0}=e,c=n.useRef(null),u=n.useRef(null),[m=!1,p]=(0,l.i)({prop:s,defaultProp:r,onChange:o});return(0,f.jsx)(N,{scope:t,triggerRef:c,contentRef:u,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:m,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:d,children:a})};C.displayName=j;var w="DialogTrigger",A=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,o=P(w,a),i=(0,r.s)(t,o.triggerRef);return(0,f.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":W(o.open),...n,ref:i,onClick:(0,s.m)(e.onClick,o.onOpenToggle)})});A.displayName=w;var D="DialogPortal",[I,_]=b(D,{forceMount:void 0}),k=e=>{let{__scopeDialog:t,forceMount:a,children:s,container:r}=e,o=P(D,t);return(0,f.jsx)(I,{scope:t,forceMount:a,children:n.Children.map(s,e=>(0,f.jsx)(m.C,{present:a||o.open,children:(0,f.jsx)(u.Z,{asChild:!0,container:r,children:e})}))})};k.displayName=D;var F="DialogOverlay",O=n.forwardRef((e,t)=>{let a=_(F,e.__scopeDialog),{forceMount:n=a.forceMount,...s}=e,r=P(F,e.__scopeDialog);return r.modal?(0,f.jsx)(m.C,{present:n||r.open,children:(0,f.jsx)(R,{...s,ref:t})}):null});O.displayName=F;var R=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,s=P(F,a);return(0,f.jsx)(x.A,{as:v.DX,allowPinchZoom:!0,shards:[s.contentRef],children:(0,f.jsx)(p.sG.div,{"data-state":W(s.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),q="DialogContent",z=n.forwardRef((e,t)=>{let a=_(q,e.__scopeDialog),{forceMount:n=a.forceMount,...s}=e,r=P(q,e.__scopeDialog);return(0,f.jsx)(m.C,{present:n||r.open,children:r.modal?(0,f.jsx)(E,{...s,ref:t}):(0,f.jsx)(B,{...s,ref:t})})});z.displayName=q;var E=n.forwardRef((e,t)=>{let a=P(q,e.__scopeDialog),o=n.useRef(null),i=(0,r.s)(t,a.contentRef,o);return n.useEffect(()=>{let e=o.current;if(e)return(0,h.Eq)(e)},[]),(0,f.jsx)(S,{...e,ref:i,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,s.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),a.triggerRef.current?.focus()}),onPointerDownOutside:(0,s.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey;(2===t.button||a)&&e.preventDefault()}),onFocusOutside:(0,s.m)(e.onFocusOutside,e=>e.preventDefault())})}),B=n.forwardRef((e,t)=>{let a=P(q,e.__scopeDialog),s=n.useRef(!1),r=n.useRef(!1);return(0,f.jsx)(S,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(s.current||a.triggerRef.current?.focus(),t.preventDefault()),s.current=!1,r.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(s.current=!0,"pointerdown"!==t.detail.originalEvent.type||(r.current=!0));let n=t.target;a.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&r.current&&t.preventDefault()}})}),S=n.forwardRef((e,t)=>{let{__scopeDialog:a,trapFocus:s,onOpenAutoFocus:o,onCloseAutoFocus:i,...l}=e,u=P(q,a),m=n.useRef(null),p=(0,r.s)(t,m);return(0,g.Oh)(),(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(c.n,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:o,onUnmountAutoFocus:i,children:(0,f.jsx)(d.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":W(u.open),...l,ref:p,onDismiss:()=>u.onOpenChange(!1)})}),(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(H,{titleId:u.titleId}),(0,f.jsx)(X,{contentRef:m,descriptionId:u.descriptionId})]})]})}),T="DialogTitle",J=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,s=P(T,a);return(0,f.jsx)(p.sG.h2,{id:s.titleId,...n,ref:t})});J.displayName=T;var L="DialogDescription",$=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,s=P(L,a);return(0,f.jsx)(p.sG.p,{id:s.descriptionId,...n,ref:t})});$.displayName=L;var U="DialogClose",M=n.forwardRef((e,t)=>{let{__scopeDialog:a,...n}=e,r=P(U,a);return(0,f.jsx)(p.sG.button,{type:"button",...n,ref:t,onClick:(0,s.m)(e.onClick,()=>r.onOpenChange(!1))})});function W(e){return e?"open":"closed"}M.displayName=U;var G="DialogTitleWarning",[V,Z]=(0,o.q)(G,{contentName:q,titleName:T,docsSlug:"dialog"}),H=({titleId:e})=>{let t=Z(G),a=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&!document.getElementById(e)&&console.error(a)},[a,e]),null},X=({contentRef:e,descriptionId:t})=>{let a=Z("DialogDescriptionWarning"),s=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${a.contentName}}.`;return n.useEffect(()=>{let a=e.current?.getAttribute("aria-describedby");t&&a&&!document.getElementById(t)&&console.warn(s)},[s,e,t]),null},K=C,Y=A,Q=k,ee=O,et=z,ea=J,en=$,es=M},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31801:(e,t,a)=>{"use strict";a.d(t,{default:()=>n});let n=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\app\\\\(workspace)\\\\contacts\\\\edit\\\\[contactName]\\\\[contactId]\\\\EditContact.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\edit\\[contactName]\\[contactId]\\EditContact.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},34283:(e,t,a)=>{Promise.resolve().then(a.bind(a,65567))},34631:e=>{"use strict";e.exports=require("tls")},40988:(e,t,a)=>{"use strict";a.d(t,{AM:()=>o,Wv:()=>i,hl:()=>l});var n=a(60687);a(43210);var s=a(40599),r=a(4780);function o({...e}){return(0,n.jsx)(s.bL,{"data-slot":"popover",...e})}function i({...e}){return(0,n.jsx)(s.l9,{"data-slot":"popover-trigger",...e})}function l({className:e,align:t="center",sideOffset:a=4,...o}){return(0,n.jsx)(s.ZL,{children:(0,n.jsx)(s.UC,{"data-slot":"popover-content",align:t,sideOffset:a,className:(0,r.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",e),...o})})}},44493:(e,t,a)=>{"use strict";a.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>i,Zp:()=>r,aR:()=>o,wL:()=>c});var n=a(60687);a(43210);var s=a(4780);function r({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",e),...t})}function o({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("flex flex-col gap-1.5 px-6",e),...t})}function i({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",e),...t})}function l({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}function d({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",e),...t})}function c({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6",e),...t})}},46288:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var n=a(65239),s=a(48088),r=a(88170),o=a.n(r),i=a(30893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);a.d(t,l);let d={children:["",{children:["(workspace)",{children:["contacts",{children:["edit",{children:["[contactName]",{children:["[contactId]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,7476)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\edit\\[contactName]\\[contactId]\\page.tsx"]}]},{}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,50184)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\edit\\[contactName]\\[contactId]\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(workspace)/contacts/edit/[contactName]/[contactId]/page",pathname:"/contacts/edit/[contactName]/[contactId]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65567:(e,t,a)=>{"use strict";a.d(t,{default:()=>v});var n=a(60687),s=a(43210),r=a(16189),o=a(29523),i=a(89667),l=a(80013),d=a(44493),c=a(28559),u=a(13964),m=a(85814),p=a.n(m),g=a(63503);a(89757);var x=a(15079);a(4845);var h=a(18229);function v({contactName:e,contactId:t}){let a=(0,r.useParams)();e||a?.contactName;let m=t||a?.contactId;m&&decodeURIComponent(m);let v=(0,r.useRouter)(),[f,j]=(0,s.useState)(!1),[b,y]=(0,s.useState)(""),[N,P]=(0,s.useState)([]),[C,w]=(0,s.useState)(!1),[A,D]=(0,s.useState)(""),[I,_]=(0,s.useState)({_id:"",contactName:"",phoneNumber:"",region:"",projectName:"",unitNumber:"",totalPayableAmount:"",pendingPayableAmount:"",dueDate:"",totalInstallments:"",paymentType:"",pendingInstallments:"",lastPaymentDate:"",lastPaymentAmount:"",lastPaymentType:"",collectionBucket:"",unitPrice:"",paidAmtIncluding:"",eventDate:"",eventLocation:"",eventTime:"",nameOfRegistrant:"",campaigns:[]}),k=e=>e?new Date(e).toISOString().split("T")[0]:"",F=()=>{let e;if(!I.campaigns||0===I.campaigns.length||!N)return null;console.log("Campaign ID for lookup:",e="object"==typeof I.campaigns[0]?"$oid"in I.campaigns[0]?I.campaigns[0].$oid:I.campaigns[0]._id:I.campaigns[0]),console.log("Available campaigns:",N);let t=N.find(t=>t._id===e||"object"==typeof t._id&&"$oid"in t._id&&t._id.$oid===e);if(!t)return console.log("No campaign found with ID:",e),null;let a=t.name.toLowerCase();return a.includes("collections")?"Collections":a.includes("sales")?"Sales":a.includes("aquarise")||a.includes("aqua rise")?"AquaRiseEvent":null},O=async()=>{if(!I.contactName||!I.phoneNumber||!I.region||!I.campaigns||0===I.campaigns.length){y("Contact Name, Phone Number, Region, and Campaign are required");return}j(!0),y("");try{let e={...I,totalPayableAmount:"string"==typeof I.totalPayableAmount?parseFloat(I.totalPayableAmount)||0:I.totalPayableAmount,pendingPayableAmount:"string"==typeof I.pendingPayableAmount?parseFloat(I.pendingPayableAmount)||0:I.pendingPayableAmount,lastPaymentAmount:"string"==typeof I.lastPaymentAmount?parseFloat(I.lastPaymentAmount)||0:I.lastPaymentAmount,unitPrice:"string"==typeof I.unitPrice?parseFloat(I.unitPrice)||0:I.unitPrice,paidAmtIncluding:"string"==typeof I.paidAmtIncluding?parseFloat(I.paidAmtIncluding)||0:I.paidAmtIncluding,totalInstallments:"string"==typeof I.totalInstallments?parseInt(I.totalInstallments)||0:I.totalInstallments,pendingInstallments:"string"==typeof I.pendingInstallments?parseInt(I.pendingInstallments)||0:I.pendingInstallments,eventDate:I.eventDate?(()=>{try{if("string"==typeof I.eventDate&&I.eventDate.includes("T"))return I.eventDate;return new Date(I.eventDate).toISOString()}catch(e){return console.error("Error formatting eventDate for submission:",e),I.eventDate}})():void 0};if(!(await fetch(`http://localhost:4000/api/contacts/${I._id}`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify(e)})).ok)throw Error("Failed to update contact");w(!0)}catch(e){y("Failed to update contact"),console.error(e)}finally{j(!1)}};return(0,n.jsxs)("div",{className:"max-w-6xl mx-auto py-2",children:[(0,n.jsx)("div",{className:"mb-6",children:(0,n.jsxs)(p(),{href:"/contacts",className:"text-sm text-muted-foreground hover:text-primary flex items-center gap-2",children:[(0,n.jsx)(c.A,{className:"h-4 w-4"}),"Back to Contacts"]})}),(0,n.jsxs)(d.Zp,{children:[(0,n.jsxs)(d.aR,{children:[(0,n.jsx)(d.ZB,{children:"Edit Contact"}),(0,n.jsx)(d.BT,{children:"Update contact information."})]}),(0,n.jsx)(d.Wu,{children:(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{className:"mb-2",children:"Campaign*"}),(0,n.jsxs)(x.l6,{value:(()=>{let e="";if(I.campaigns&&I.campaigns.length>0){if("object"==typeof I.campaigns[0]){if("$oid"in I.campaigns[0]){let t=I.campaigns[0].$oid,a=N.find(e=>e._id===t||"object"==typeof e._id&&"$oid"in e._id&&e._id.$oid===t);e=a?.name||""}else"_id"in I.campaigns[0]&&(e=I.campaigns[0].name||"")}else{let t=N.find(e=>e._id===(I.campaigns||[])[0]);e=t?.name||""}}return e})(),onValueChange:e=>{let t=N.find(t=>t.name===e);console.log("Found campaign:",t),t&&_(e=>({...e,campaigns:[t._id]}))},children:[(0,n.jsx)(x.bq,{children:(0,n.jsx)(x.yv,{placeholder:"Select a campaign"})}),(0,n.jsx)(x.gC,{children:N?.map(e=>n.jsx(x.eb,{value:e.name,children:e.name},e._id))})]})]})}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"contactName",className:"mb-2",children:"Contact Name*"}),(0,n.jsx)(i.p,{id:"contactName",value:I.contactName??"",onChange:e=>_(t=>({...t,contactName:e.target.value}))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"phoneNumber",className:"mb-2",children:"Phone Number*"}),(0,n.jsx)(i.p,{id:"phoneNumber",value:I.phoneNumber??"",onChange:e=>_(t=>({...t,phoneNumber:e.target.value}))})]})]}),(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"region",className:"mb-2",children:"Region*"}),(0,n.jsx)(h.N,{value:I.region??"",onChange:e=>_(t=>({...t,region:e}))})]})})]}),"AquaRiseEvent"===F()&&(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"eventDate",className:"mb-2",children:"Event Date"}),(0,n.jsx)(i.p,{id:"eventDate",type:"datetime-local",value:(()=>{if(!I.eventDate)return"";try{return console.log("Formatting eventDate for input:",I.eventDate),I.eventDate.slice(0,16)}catch(e){return console.error("Error formatting eventDate:",e),""}})(),onChange:e=>_(t=>({...t,eventDate:e.target.value}))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"eventLocation",className:"mb-2",children:"Event Location"}),(0,n.jsx)(i.p,{id:"eventLocation",value:(console.log("Event location value:",I.eventLocation),I.eventLocation??""),onChange:e=>_(t=>({...t,eventLocation:e.target.value}))})]})]}),(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"nameOfRegistrant",className:"mb-2",children:"Name of Registrant"}),(0,n.jsx)(i.p,{id:"nameOfRegistrant",value:(console.log("Name of registrant value:",I.nameOfRegistrant),I.nameOfRegistrant??""),onChange:e=>_(t=>({...t,nameOfRegistrant:e.target.value}))})]})})]})}),"Collections"===F()&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"projectName",className:"mb-2",children:"Project Name"}),(0,n.jsx)(i.p,{id:"projectName",value:I.projectName??"",onChange:e=>_(t=>({...t,projectName:e.target.value}))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"unitNumber",className:"mb-2",children:"Unit Number"}),(0,n.jsx)(i.p,{id:"unitNumber",value:I.unitNumber??"",onChange:e=>_(t=>({...t,unitNumber:e.target.value}))})]})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"unitPrice",className:"mb-2",children:"Unit Price"}),(0,n.jsx)(i.p,{id:"unitPrice",type:"number",value:I.unitPrice??"",onChange:e=>_(t=>({...t,unitPrice:e.target.value}))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"paidAmtIncluding",className:"mb-2",children:"Paid Amount Including"}),(0,n.jsx)(i.p,{id:"paidAmtIncluding",type:"number",value:I.paidAmtIncluding??"",onChange:e=>_(t=>({...t,paidAmtIncluding:e.target.value}))})]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"totalPayableAmount",className:"mb-2",children:"Total Payable Amount"}),(0,n.jsx)(i.p,{id:"totalPayableAmount",type:"number",value:I.totalPayableAmount??"",onChange:e=>_(t=>({...t,totalPayableAmount:e.target.value}))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"pendingPayableAmount",className:"mb-2",children:"Pending Payable Amount"}),(0,n.jsx)(i.p,{id:"pendingPayableAmount",type:"number",value:I.pendingPayableAmount??"",onChange:e=>_(t=>({...t,pendingPayableAmount:e.target.value}))})]})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"dueDate",className:"mb-2",children:"Due Date"}),(0,n.jsx)(i.p,{id:"dueDate",type:"date",value:I.dueDate?k(I.dueDate):"",onChange:e=>_(t=>({...t,dueDate:e.target.value}))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"paymentType",className:"mb-2",children:"Payment Type"}),(0,n.jsx)(i.p,{id:"paymentType",value:I.paymentType??"",onChange:e=>_(t=>({...t,paymentType:e.target.value}))})]})]})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"totalInstallments",className:"mb-2",children:"Total Installments"}),(0,n.jsx)(i.p,{id:"totalInstallments",type:"number",value:I.totalInstallments??"",onChange:e=>_(t=>({...t,totalInstallments:e.target.value}))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"pendingInstallments",className:"mb-2",children:"Pending Installments"}),(0,n.jsx)(i.p,{id:"pendingInstallments",type:"number",value:I.pendingInstallments??"",onChange:e=>_(t=>({...t,pendingInstallments:e.target.value}))})]})]}),(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"collectionBucket",className:"mb-2",children:"Collection Bucket"}),(0,n.jsx)(i.p,{id:"collectionBucket",value:I.collectionBucket??"",onChange:e=>_(t=>({...t,collectionBucket:e.target.value}))})]})})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,n.jsx)("div",{className:"space-y-4",children:(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"lastPaymentDate",className:"mb-2",children:"Last Payment Date"}),(0,n.jsx)(i.p,{id:"lastPaymentDate",type:"date",value:I.lastPaymentDate?k(I.lastPaymentDate):"",onChange:e=>_(t=>({...t,lastPaymentDate:e.target.value}))})]})}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"lastPaymentAmount",className:"mb-2",children:"Last Payment Amount"}),(0,n.jsx)(i.p,{id:"lastPaymentAmount",type:"number",value:I.lastPaymentAmount??"",onChange:e=>_(t=>({...t,lastPaymentAmount:e.target.value}))})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)(l.J,{htmlFor:"lastPaymentType",className:"mb-2",children:"Last Payment Type"}),(0,n.jsx)(i.p,{id:"lastPaymentType",value:I.lastPaymentType??"",onChange:e=>_(t=>({...t,lastPaymentType:e.target.value}))})]})]})]})]}),b&&(0,n.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded-md text-sm",children:b}),(0,n.jsxs)("div",{className:"flex justify-end gap-4",children:[(0,n.jsx)(o.$,{variant:"outline",onClick:()=>v.push("/contacts"),children:"Cancel"}),(0,n.jsx)(o.$,{onClick:O,disabled:f,children:f?"Updating...":"Update Contact"})]})]})})]}),(0,n.jsx)(g.lG,{open:C,onOpenChange:w,children:(0,n.jsxs)(g.Cf,{children:[(0,n.jsxs)(g.c7,{children:[(0,n.jsx)(g.L3,{children:"Success"}),(0,n.jsx)(g.rr,{children:"Contact updated successfully."})]}),(0,n.jsx)(g.Es,{children:(0,n.jsxs)(o.$,{onClick:()=>v.push("/contacts"),children:[(0,n.jsx)(u.A,{className:"mr-2 h-4 w-4"})," Back to Contacts"]})})]})})]})}},74035:(e,t,a)=>{Promise.resolve().then(a.bind(a,31801))},74075:e=>{"use strict";e.exports=require("zlib")},78148:(e,t,a)=>{"use strict";a.d(t,{b:()=>i});var n=a(43210),s=a(14163),r=a(60687),o=n.forwardRef((e,t)=>(0,r.jsx)(s.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));o.displayName="Label";var i=o},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80013:(e,t,a)=>{"use strict";a.d(t,{J:()=>o});var n=a(60687);a(43210);var s=a(78148),r=a(4780);function o({className:e,...t}){return(0,n.jsx)(s.b,{"data-slot":"label",className:(0,r.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},99270:(e,t,a)=>{"use strict";a.d(t,{A:()=>n});let n=(0,a(62688).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),n=t.X(0,[287,9176,7674,5814,598,5188,6034,2256,1476,4772,2093],()=>a(46288));module.exports=n})();