(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6910],{4884:(e,t,i)=>{"use strict";i.d(t,{bL:()=>b,zi:()=>D});var n=i(12115),a=i(85185),r=i(6101),o=i(46081),h=i(5845),s=i(45503),d=i(11275),l=i(63655),c=i(95155),p="Switch",[w,u]=(0,o.A)(p),[g,y]=w(p),m=n.forwardRef((e,t)=>{let{__scopeSwitch:i,name:o,checked:s,defaultChecked:d,required:p,disabled:w,value:u="on",onCheckedChange:y,form:m,...x}=e,[v,b]=n.useState(null),D=(0,r.s)(t,e=>b(e)),k=n.useRef(!1),M=!v||m||!!v.closest("form"),[R=!1,E]=(0,h.i)({prop:s,defaultProp:d,onChange:y});return(0,c.jsxs)(g,{scope:i,checked:R,disabled:w,children:[(0,c.jsx)(l.sG.button,{type:"button",role:"switch","aria-checked":R,"aria-required":p,"data-state":f(R),"data-disabled":w?"":void 0,disabled:w,value:u,...x,ref:D,onClick:(0,a.m)(e.onClick,e=>{E(e=>!e),M&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),M&&(0,c.jsx)(C,{control:v,bubbles:!k.current,name:o,value:u,checked:R,required:p,disabled:w,form:m,style:{transform:"translateX(-100%)"}})]})});m.displayName=p;var x="SwitchThumb",v=n.forwardRef((e,t)=>{let{__scopeSwitch:i,...n}=e,a=y(x,i);return(0,c.jsx)(l.sG.span,{"data-state":f(a.checked),"data-disabled":a.disabled?"":void 0,...n,ref:t})});v.displayName=x;var C=e=>{let{control:t,checked:i,bubbles:a=!0,...r}=e,o=n.useRef(null),h=(0,s.Z)(i),l=(0,d.X)(t);return n.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(h!==i&&t){let n=new Event("click",{bubbles:a});t.call(e,i),e.dispatchEvent(n)}},[h,i,a]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i,...r,tabIndex:-1,ref:o,style:{...e.style,...l,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function f(e){return e?"checked":"unchecked"}var b=m,D=v},6076:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});let n=(0,i(19946).A)("PhoneOff",[["path",{d:"M10.68 13.31a16 16 0 0 0 3.41 2.6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7 2 2 0 0 1 1.72 2v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.42 19.42 0 0 1-3.33-2.67m-2.67-3.34a19.79 19.79 0 0 1-3.07-8.63A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91",key:"z86iuo"}],["line",{x1:"22",x2:"2",y1:"2",y2:"22",key:"11kh81"}]])},20690:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});let n=(0,i(19946).A)("WandSparkles",[["path",{d:"m21.64 3.64-1.28-1.28a1.21 1.21 0 0 0-1.72 0L2.36 18.64a1.21 1.21 0 0 0 0 1.72l1.28 1.28a1.2 1.2 0 0 0 1.72 0L21.64 5.36a1.2 1.2 0 0 0 0-1.72",key:"ul74o6"}],["path",{d:"m14 7 3 3",key:"1r5n42"}],["path",{d:"M5 6v4",key:"ilb8ba"}],["path",{d:"M19 14v4",key:"blhpug"}],["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M7 8H3",key:"zfb6yr"}],["path",{d:"M21 16h-4",key:"1cnmox"}],["path",{d:"M11 3H9",key:"1obp7u"}]])},28883:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});let n=(0,i(19946).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},29869:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});let n=(0,i(19946).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},35695:(e,t,i)=>{"use strict";var n=i(18999);i.o(n,"useParams")&&i.d(t,{useParams:function(){return n.useParams}}),i.o(n,"usePathname")&&i.d(t,{usePathname:function(){return n.usePathname}}),i.o(n,"useRouter")&&i.d(t,{useRouter:function(){return n.useRouter}})},54534:()=>{},57434:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});let n=(0,i(19946).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},60704:(e,t,i)=>{"use strict";i.d(t,{B8:()=>_,UC:()=>H,bL:()=>A,l9:()=>P});var n=i(12115),a=i(85185),r=i(46081),o=i(89196),h=i(28905),s=i(63655),d=i(94315),l=i(5845),c=i(61285),p=i(95155),w="Tabs",[u,g]=(0,r.A)(w,[o.RG]),y=(0,o.RG)(),[m,x]=u(w),v=n.forwardRef((e,t)=>{let{__scopeTabs:i,value:n,onValueChange:a,defaultValue:r,orientation:o="horizontal",dir:h,activationMode:w="automatic",...u}=e,g=(0,d.jH)(h),[y,x]=(0,l.i)({prop:n,onChange:a,defaultProp:r});return(0,p.jsx)(m,{scope:i,baseId:(0,c.B)(),value:y,onValueChange:x,orientation:o,dir:g,activationMode:w,children:(0,p.jsx)(s.sG.div,{dir:g,"data-orientation":o,...u,ref:t})})});v.displayName=w;var C="TabsList",f=n.forwardRef((e,t)=>{let{__scopeTabs:i,loop:n=!0,...a}=e,r=x(C,i),h=y(i);return(0,p.jsx)(o.bL,{asChild:!0,...h,orientation:r.orientation,dir:r.dir,loop:n,children:(0,p.jsx)(s.sG.div,{role:"tablist","aria-orientation":r.orientation,...a,ref:t})})});f.displayName=C;var b="TabsTrigger",D=n.forwardRef((e,t)=>{let{__scopeTabs:i,value:n,disabled:r=!1,...h}=e,d=x(b,i),l=y(i),c=R(d.baseId,n),w=E(d.baseId,n),u=n===d.value;return(0,p.jsx)(o.q7,{asChild:!0,...l,focusable:!r,active:u,children:(0,p.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":w,"data-state":u?"active":"inactive","data-disabled":r?"":void 0,disabled:r,id:c,...h,ref:t,onMouseDown:(0,a.m)(e.onMouseDown,e=>{r||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(n)}),onKeyDown:(0,a.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(n)}),onFocus:(0,a.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;u||r||!e||d.onValueChange(n)})})})});D.displayName=b;var k="TabsContent",M=n.forwardRef((e,t)=>{let{__scopeTabs:i,value:a,forceMount:r,children:o,...d}=e,l=x(k,i),c=R(l.baseId,a),w=E(l.baseId,a),u=a===l.value,g=n.useRef(u);return n.useEffect(()=>{let e=requestAnimationFrame(()=>g.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,p.jsx)(h.C,{present:r||u,children:i=>{let{present:n}=i;return(0,p.jsx)(s.sG.div,{"data-state":u?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":c,hidden:!n,id:w,tabIndex:0,...d,ref:t,style:{...e.style,animationDuration:g.current?"0s":void 0},children:n&&o})}})});function R(e,t){return"".concat(e,"-trigger-").concat(t)}function E(e,t){return"".concat(e,"-content-").concat(t)}M.displayName=k;var A=v,_=f,P=D,H=M},69074:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});let n=(0,i(19946).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},75441:(e,t,i)=>{"use strict";i.d(t,{A:()=>n});let n=(0,i(19946).A)("PhoneForwarded",[["polyline",{points:"18 2 22 6 18 10",key:"6vjanh"}],["line",{x1:"14",x2:"22",y1:"6",y2:"6",key:"1jsywh"}],["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},83833:(e,t,i)=>{"use strict";i.d(t,{Ay:()=>u});var n=i(12115);let a={x:0,y:0,width:0,height:0,unit:"px"},r=(e,t,i)=>Math.min(Math.max(e,t),i),o=(...e)=>e.filter(e=>e&&"string"==typeof e).join(" "),h=(e,t)=>e===t||e.width===t.width&&e.height===t.height&&e.x===t.x&&e.y===t.y&&e.unit===t.unit;function s(e,t,i){return"%"===e.unit?{...a,...e,unit:"%"}:{unit:"%",x:e.x?e.x/t*100:0,y:e.y?e.y/i*100:0,width:e.width?e.width/t*100:0,height:e.height?e.height/i*100:0}}function d(e,t,i){return e.unit?"px"===e.unit?{...a,...e,unit:"px"}:{unit:"px",x:e.x?e.x*t/100:0,y:e.y?e.y*i/100:0,width:e.width?e.width*t/100:0,height:e.height?e.height*i/100:0}:{...a,...e,unit:"px"}}function l(e,t,i,n,a,r=0,o=0,h=n,s=a){let d={...e},c=Math.min(r,n),p=Math.min(o,a),w=Math.min(h,n),u=Math.min(s,a);t&&(t>1?(p=(c=o?o*t:c)/t,w=h*t):(c=(p=r?r/t:p)*t,u=s/t)),d.y<0&&(d.height=Math.max(d.height+d.y,p),d.y=0),d.x<0&&(d.width=Math.max(d.width+d.x,c),d.x=0);let g=n-(d.x+d.width);g<0&&(d.x=Math.min(d.x,n-c),d.width+=g);let y=a-(d.y+d.height);if(y<0&&(d.y=Math.min(d.y,a-p),d.height+=y),d.width<c&&(("sw"===i||"nw"==i)&&(d.x-=c-d.width),d.width=c),d.height<p&&(("nw"===i||"ne"==i)&&(d.y-=p-d.height),d.height=p),d.width>w&&(("sw"===i||"nw"==i)&&(d.x-=w-d.width),d.width=w),d.height>u&&(("nw"===i||"ne"==i)&&(d.y-=u-d.height),d.height=u),t){let e=d.width/d.height;if(e<t){let e=Math.max(d.width/t,p);("nw"===i||"ne"==i)&&(d.y-=e-d.height),d.height=e}else if(e>t){let e=Math.max(d.height*t,c);("sw"===i||"nw"==i)&&(d.x-=e-d.width),d.width=e}}return d}let c={capture:!0,passive:!1},p=0,w=class e extends n.PureComponent{constructor(){super(...arguments),this.docMoveBound=!1,this.mouseDownOnCrop=!1,this.dragStarted=!1,this.evData={startClientX:0,startClientY:0,startCropX:0,startCropY:0,clientX:0,clientY:0,isResize:!0},this.componentRef=(0,n.createRef)(),this.mediaRef=(0,n.createRef)(),this.initChangeCalled=!1,this.instanceId=`rc-${p++}`,this.state={cropIsActive:!1,newCropIsBeingDrawn:!1},this.onCropPointerDown=e=>{let{crop:t,disabled:i}=this.props,n=this.getBox();if(!t)return;let a=d(t,n.width,n.height);if(i)return;e.cancelable&&e.preventDefault(),this.bindDocMove(),this.componentRef.current.focus({preventScroll:!0});let r=e.target.dataset.ord,o=e.clientX,h=e.clientY,s=a.x,l=a.y;if(r){let t=e.clientX-n.x,i=e.clientY-n.y,d=0,c=0;"ne"===r||"e"==r?(d=t-(a.x+a.width),c=i-a.y,s=a.x,l=a.y+a.height):"se"===r||"s"===r?(d=t-(a.x+a.width),c=i-(a.y+a.height),s=a.x,l=a.y):"sw"===r||"w"==r?(d=t-a.x,c=i-(a.y+a.height),s=a.x+a.width,l=a.y):("nw"===r||"n"==r)&&(d=t-a.x,c=i-a.y,s=a.x+a.width,l=a.y+a.height),o=s+n.x+d,h=l+n.y+c}this.evData={startClientX:o,startClientY:h,startCropX:s,startCropY:l,clientX:e.clientX,clientY:e.clientY,isResize:!!r,ord:r},this.mouseDownOnCrop=!0,this.setState({cropIsActive:!0})},this.onComponentPointerDown=e=>{let{crop:t,disabled:i,locked:n,keepSelection:a,onChange:r}=this.props,o=this.getBox();if(i||n||a&&t)return;e.cancelable&&e.preventDefault(),this.bindDocMove(),this.componentRef.current.focus({preventScroll:!0});let h=e.clientX-o.x,l=e.clientY-o.y,c={unit:"px",x:h,y:l,width:0,height:0};this.evData={startClientX:e.clientX,startClientY:e.clientY,startCropX:h,startCropY:l,clientX:e.clientX,clientY:e.clientY,isResize:!0},this.mouseDownOnCrop=!0,r(d(c,o.width,o.height),s(c,o.width,o.height)),this.setState({cropIsActive:!0,newCropIsBeingDrawn:!0})},this.onDocPointerMove=e=>{let t;let{crop:i,disabled:n,onChange:a,onDragStart:r}=this.props,o=this.getBox();if(n||!i||!this.mouseDownOnCrop)return;e.cancelable&&e.preventDefault(),this.dragStarted||(this.dragStarted=!0,r&&r(e));let{evData:l}=this;l.clientX=e.clientX,l.clientY=e.clientY,h(i,t=l.isResize?this.resizeCrop():this.dragCrop())||a(d(t,o.width,o.height),s(t,o.width,o.height))},this.onComponentKeyDown=t=>{let{crop:i,disabled:n,onChange:a,onComplete:o}=this.props;if(n)return;let h=t.key,l=!1;if(!i)return;let c=this.getBox(),p=this.makePixelCrop(c),w=(navigator.platform.match("Mac")?t.metaKey:t.ctrlKey)?e.nudgeStepLarge:t.shiftKey?e.nudgeStepMedium:e.nudgeStep;if("ArrowLeft"===h?(p.x-=w,l=!0):"ArrowRight"===h?(p.x+=w,l=!0):"ArrowUp"===h?(p.y-=w,l=!0):"ArrowDown"===h&&(p.y+=w,l=!0),l){t.cancelable&&t.preventDefault(),p.x=r(p.x,0,c.width-p.width),p.y=r(p.y,0,c.height-p.height);let e=d(p,c.width,c.height),i=s(p,c.width,c.height);a(e,i),o&&o(e,i)}},this.onHandlerKeyDown=(t,i)=>{let{aspect:n=0,crop:a,disabled:r,minWidth:o=0,minHeight:c=0,maxWidth:p,maxHeight:w,onChange:u,onComplete:g}=this.props,y=this.getBox();if(r||!a||"ArrowUp"!==t.key&&"ArrowDown"!==t.key&&"ArrowLeft"!==t.key&&"ArrowRight"!==t.key)return;t.stopPropagation(),t.preventDefault();let m=(navigator.platform.match("Mac")?t.metaKey:t.ctrlKey)?e.nudgeStepLarge:t.shiftKey?e.nudgeStepMedium:e.nudgeStep,x=l(function(e,t,i,n){let a={...e};return"ArrowLeft"===t?"nw"===n?(a.x-=i,a.y-=i,a.width+=i,a.height+=i):"w"===n?(a.x-=i,a.width+=i):"sw"===n?(a.x-=i,a.width+=i,a.height+=i):"ne"===n?(a.y+=i,a.width-=i,a.height-=i):"e"===n?a.width-=i:"se"===n&&(a.width-=i,a.height-=i):"ArrowRight"===t&&("nw"===n?(a.x+=i,a.y+=i,a.width-=i,a.height-=i):"w"===n?(a.x+=i,a.width-=i):"sw"===n?(a.x+=i,a.width-=i,a.height-=i):"ne"===n?(a.y-=i,a.width+=i,a.height+=i):"e"===n?a.width+=i:"se"===n&&(a.width+=i,a.height+=i)),"ArrowUp"===t?"nw"===n?(a.x-=i,a.y-=i,a.width+=i,a.height+=i):"n"===n?(a.y-=i,a.height+=i):"ne"===n?(a.y-=i,a.width+=i,a.height+=i):"sw"===n?(a.x+=i,a.width-=i,a.height-=i):"s"===n?a.height-=i:"se"===n&&(a.width-=i,a.height-=i):"ArrowDown"===t&&("nw"===n?(a.x+=i,a.y+=i,a.width-=i,a.height-=i):"n"===n?(a.y+=i,a.height-=i):"ne"===n?(a.y+=i,a.width-=i,a.height-=i):"sw"===n?(a.x-=i,a.width+=i,a.height+=i):"s"===n?a.height+=i:"se"===n&&(a.width+=i,a.height+=i)),a}(d(a,y.width,y.height),t.key,m,i),n,i,y.width,y.height,o,c,p,w);if(!h(a,x)){let e=s(x,y.width,y.height);u(x,e),g&&g(x,e)}},this.onDocPointerDone=e=>{let{crop:t,disabled:i,onComplete:n,onDragEnd:a}=this.props,r=this.getBox();this.unbindDocMove(),!(i||!t)&&this.mouseDownOnCrop&&(this.mouseDownOnCrop=!1,this.dragStarted=!1,a&&a(e),n&&n(d(t,r.width,r.height),s(t,r.width,r.height)),this.setState({cropIsActive:!1,newCropIsBeingDrawn:!1}))},this.onDragFocus=()=>{var e;null==(e=this.componentRef.current)||e.scrollTo(0,0)}}get document(){return document}getBox(){let e=this.mediaRef.current;if(!e)return{x:0,y:0,width:0,height:0};let{x:t,y:i,width:n,height:a}=e.getBoundingClientRect();return{x:t,y:i,width:n,height:a}}componentDidUpdate(e){let{crop:t,onComplete:i}=this.props;if(i&&!e.crop&&t){let{width:e,height:n}=this.getBox();e&&n&&i(d(t,e,n),s(t,e,n))}}componentWillUnmount(){this.resizeObserver&&this.resizeObserver.disconnect(),this.unbindDocMove()}bindDocMove(){this.docMoveBound||(this.document.addEventListener("pointermove",this.onDocPointerMove,c),this.document.addEventListener("pointerup",this.onDocPointerDone,c),this.document.addEventListener("pointercancel",this.onDocPointerDone,c),this.docMoveBound=!0)}unbindDocMove(){this.docMoveBound&&(this.document.removeEventListener("pointermove",this.onDocPointerMove,c),this.document.removeEventListener("pointerup",this.onDocPointerDone,c),this.document.removeEventListener("pointercancel",this.onDocPointerDone,c),this.docMoveBound=!1)}getCropStyle(){let{crop:e}=this.props;if(e)return{top:`${e.y}${e.unit}`,left:`${e.x}${e.unit}`,width:`${e.width}${e.unit}`,height:`${e.height}${e.unit}`}}dragCrop(){let{evData:e}=this,t=this.getBox(),i=this.makePixelCrop(t),n=e.clientX-e.startClientX,a=e.clientY-e.startClientY;return i.x=r(e.startCropX+n,0,t.width-i.width),i.y=r(e.startCropY+a,0,t.height-i.height),i}getPointRegion(e,t,i,n){let a,r;let{evData:o}=this,h=o.clientX-e.x,s=o.clientY-e.y;return a=n&&t?"nw"===t||"n"===t||"ne"===t:s<o.startCropY,(i&&t?"nw"===t||"w"===t||"sw"===t:h<o.startCropX)?a?"nw":"sw":a?"ne":"se"}resolveMinDimensions(e,t,i=0,n=0){let a=Math.min(i,e.width),r=Math.min(n,e.height);return t&&(a||r)?t>1?a?[a,a/t]:[r*t,r]:r?[r*t,r]:[a,a/t]:[a,r]}resizeCrop(){let{evData:t}=this,{aspect:i=0,maxWidth:n,maxHeight:a}=this.props,o=this.getBox(),[h,s]=this.resolveMinDimensions(o,i,this.props.minWidth,this.props.minHeight),d=this.makePixelCrop(o),c=this.getPointRegion(o,t.ord,h,s),p=t.ord||c,w=t.clientX-t.startClientX,u=t.clientY-t.startClientY;(h&&"nw"===p||"w"===p||"sw"===p)&&(w=Math.min(w,-h)),(s&&"nw"===p||"n"===p||"ne"===p)&&(u=Math.min(u,-s));let g={unit:"px",x:0,y:0,width:0,height:0};"ne"===c?(g.x=t.startCropX,g.width=w,i?g.height=g.width/i:g.height=Math.abs(u),g.y=t.startCropY-g.height):"se"===c?(g.x=t.startCropX,g.y=t.startCropY,g.width=w,i?g.height=g.width/i:g.height=u):"sw"===c?(g.x=t.startCropX+w,g.y=t.startCropY,g.width=Math.abs(w),i?g.height=g.width/i:g.height=u):"nw"===c&&(g.x=t.startCropX+w,g.width=Math.abs(w),i?(g.height=g.width/i,g.y=t.startCropY-g.height):(g.height=Math.abs(u),g.y=t.startCropY+u));let y=l(g,i,c,o.width,o.height,h,s,n,a);return i||e.xyOrds.indexOf(p)>-1?d=y:e.xOrds.indexOf(p)>-1?(d.x=y.x,d.width=y.width):e.yOrds.indexOf(p)>-1&&(d.y=y.y,d.height=y.height),d.x=r(d.x,0,o.width-d.width),d.y=r(d.y,0,o.height-d.height),d}renderCropSelection(){let{ariaLabels:t=e.defaultProps.ariaLabels,disabled:i,locked:a,renderSelectionAddon:r,ruleOfThirds:o,crop:h}=this.props,s=this.getCropStyle();if(h)return n.createElement("div",{style:s,className:"ReactCrop__crop-selection",onPointerDown:this.onCropPointerDown,"aria-label":t.cropArea,tabIndex:0,onKeyDown:this.onComponentKeyDown,role:"group"},!i&&!a&&n.createElement("div",{className:"ReactCrop__drag-elements",onFocus:this.onDragFocus},n.createElement("div",{className:"ReactCrop__drag-bar ord-n","data-ord":"n"}),n.createElement("div",{className:"ReactCrop__drag-bar ord-e","data-ord":"e"}),n.createElement("div",{className:"ReactCrop__drag-bar ord-s","data-ord":"s"}),n.createElement("div",{className:"ReactCrop__drag-bar ord-w","data-ord":"w"}),n.createElement("div",{className:"ReactCrop__drag-handle ord-nw","data-ord":"nw",tabIndex:0,"aria-label":t.nwDragHandle,onKeyDown:e=>this.onHandlerKeyDown(e,"nw"),role:"button"}),n.createElement("div",{className:"ReactCrop__drag-handle ord-n","data-ord":"n",tabIndex:0,"aria-label":t.nDragHandle,onKeyDown:e=>this.onHandlerKeyDown(e,"n"),role:"button"}),n.createElement("div",{className:"ReactCrop__drag-handle ord-ne","data-ord":"ne",tabIndex:0,"aria-label":t.neDragHandle,onKeyDown:e=>this.onHandlerKeyDown(e,"ne"),role:"button"}),n.createElement("div",{className:"ReactCrop__drag-handle ord-e","data-ord":"e",tabIndex:0,"aria-label":t.eDragHandle,onKeyDown:e=>this.onHandlerKeyDown(e,"e"),role:"button"}),n.createElement("div",{className:"ReactCrop__drag-handle ord-se","data-ord":"se",tabIndex:0,"aria-label":t.seDragHandle,onKeyDown:e=>this.onHandlerKeyDown(e,"se"),role:"button"}),n.createElement("div",{className:"ReactCrop__drag-handle ord-s","data-ord":"s",tabIndex:0,"aria-label":t.sDragHandle,onKeyDown:e=>this.onHandlerKeyDown(e,"s"),role:"button"}),n.createElement("div",{className:"ReactCrop__drag-handle ord-sw","data-ord":"sw",tabIndex:0,"aria-label":t.swDragHandle,onKeyDown:e=>this.onHandlerKeyDown(e,"sw"),role:"button"}),n.createElement("div",{className:"ReactCrop__drag-handle ord-w","data-ord":"w",tabIndex:0,"aria-label":t.wDragHandle,onKeyDown:e=>this.onHandlerKeyDown(e,"w"),role:"button"})),r&&n.createElement("div",{className:"ReactCrop__selection-addon",onPointerDown:e=>e.stopPropagation()},r(this.state)),o&&n.createElement(n.Fragment,null,n.createElement("div",{className:"ReactCrop__rule-of-thirds-hz"}),n.createElement("div",{className:"ReactCrop__rule-of-thirds-vt"})))}makePixelCrop(e){return d({...a,...this.props.crop||{}},e.width,e.height)}render(){let{aspect:e,children:t,circularCrop:i,className:a,crop:r,disabled:h,locked:s,style:d,ruleOfThirds:l}=this.props,{cropIsActive:c,newCropIsBeingDrawn:p}=this.state,w=r?this.renderCropSelection():null,u=o("ReactCrop",a,c&&"ReactCrop--active",h&&"ReactCrop--disabled",s&&"ReactCrop--locked",p&&"ReactCrop--new-crop",r&&e&&"ReactCrop--fixed-aspect",r&&i&&"ReactCrop--circular-crop",r&&l&&"ReactCrop--rule-of-thirds",!this.dragStarted&&r&&!r.width&&!r.height&&"ReactCrop--invisible-crop",i&&"ReactCrop--no-animate");return n.createElement("div",{ref:this.componentRef,className:u,style:d},n.createElement("div",{ref:this.mediaRef,className:"ReactCrop__child-wrapper",onPointerDown:this.onComponentPointerDown},t),r?n.createElement("svg",{className:"ReactCrop__crop-mask",width:"100%",height:"100%"},n.createElement("defs",null,n.createElement("mask",{id:`hole-${this.instanceId}`},n.createElement("rect",{width:"100%",height:"100%",fill:"white"}),i?n.createElement("ellipse",{cx:`${r.x+r.width/2}${r.unit}`,cy:`${r.y+r.height/2}${r.unit}`,rx:`${r.width/2}${r.unit}`,ry:`${r.height/2}${r.unit}`,fill:"black"}):n.createElement("rect",{x:`${r.x}${r.unit}`,y:`${r.y}${r.unit}`,width:`${r.width}${r.unit}`,height:`${r.height}${r.unit}`,fill:"black"}))),n.createElement("rect",{fill:"black",fillOpacity:.5,width:"100%",height:"100%",mask:`url(#hole-${this.instanceId})`})):void 0,w)}};w.xOrds=["e","w"],w.yOrds=["n","s"],w.xyOrds=["nw","ne","se","sw"],w.nudgeStep=1,w.nudgeStepMedium=10,w.nudgeStepLarge=100,w.defaultProps={ariaLabels:{cropArea:"Use the arrow keys to move the crop selection area",nwDragHandle:"Use the arrow keys to move the north west drag handle to change the crop selection area",nDragHandle:"Use the up and down arrow keys to move the north drag handle to change the crop selection area",neDragHandle:"Use the arrow keys to move the north east drag handle to change the crop selection area",eDragHandle:"Use the up and down arrow keys to move the east drag handle to change the crop selection area",seDragHandle:"Use the arrow keys to move the south east drag handle to change the crop selection area",sDragHandle:"Use the up and down arrow keys to move the south drag handle to change the crop selection area",swDragHandle:"Use the arrow keys to move the south west drag handle to change the crop selection area",wDragHandle:"Use the up and down arrow keys to move the west drag handle to change the crop selection area"}};let u=w}}]);