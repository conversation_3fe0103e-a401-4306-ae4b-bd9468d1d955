(()=>{var e={};e.id=2483,e.ids=[2483],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},5511:(e,t,r)=>{Promise.resolve().then(r.bind(r,18389))},6211:(e,t,r)=>{"use strict";r.d(t,{A0:()=>s,BF:()=>i,Hj:()=>o,XI:()=>l,nA:()=>d,nd:()=>c});var n=r(60687);r(43210);var a=r(4780);function l({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,n.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...t})})}function s({className:e,...t}){return(0,n.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...t})}function i({className:e,...t}){return(0,n.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t})}function o({className:e,...t}){return(0,n.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function c({className:e,...t}){return(0,n.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-muted-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function d({className:e,...t}){return(0,n.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},10022:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15079:(e,t,r)=>{"use strict";r.d(t,{bq:()=>u,eb:()=>m,gC:()=>h,l6:()=>c,yv:()=>d});var n=r(60687);r(43210);var a=r(22670),l=r(78272),s=r(13964),i=r(3589),o=r(4780);function c({...e}){return(0,n.jsx)(a.bL,{"data-slot":"select",...e})}function d({...e}){return(0,n.jsx)(a.WT,{"data-slot":"select-value",...e})}function u({className:e,children:t,...r}){return(0,n.jsxs)(a.l9,{"data-slot":"select-trigger",className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...r,children:[t,(0,n.jsx)(a.In,{asChild:!0,children:(0,n.jsx)(l.A,{className:"size-4 opacity-50"})})]})}function h({className:e,children:t,position:r="popper",...l}){return(0,n.jsx)(a.ZL,{children:(0,n.jsxs)(a.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:r,...l,children:[(0,n.jsx)(f,{}),(0,n.jsx)(a.LM,{className:(0,o.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:t}),(0,n.jsx)(g,{})]})})}function m({className:e,children:t,...r}){return(0,n.jsxs)(a.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...r,children:[(0,n.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,n.jsx)(a.VF,{children:(0,n.jsx)(s.A,{className:"size-4"})})}),(0,n.jsx)(a.p4,{children:t})]})}function f({className:e,...t}){return(0,n.jsx)(a.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,n.jsx)(i.A,{className:"size-4"})})}function g({className:e,...t}){return(0,n.jsx)(a.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,n.jsx)(l.A,{className:"size-4"})})}},16023:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])},18389:(e,t,r)=>{"use strict";let n;r.d(t,{default:()=>t3});var a,l,s,i,o,c,d,u,h,m,f=r(60687),g=r(29523),x=r(89667),p=r(6211),b=r(48340),v=r(16023),y=r(10022),w=r(37911),j=r(11860),N=r(62688);let k=(0,N.A)("FileDown",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 18v-6",key:"17g6i2"}],["path",{d:"m9 15 3 3 3-3",key:"1npd3o"}]]);var C=r(41862),S=r(99270);let E=(0,N.A)("Columns2",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M12 3v18",key:"108xh3"}]]);var D=r(41312),A=r(88233),M=r(81904);let R=(0,N.A)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);var O=r(63143),T=r(96474),I=r(5336),z=r(40228),$=r(58869),L=r(70334),P=r(13943);let F=(0,N.A)("GripVertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]]);var _=r(13861),B=r(12597),q=r(43210),U=r.n(q),H=r(63503),W=r(93500),V=r(32584),X=r(85814),Y=r.n(X),G=r(89757),K=r(21342),Z=r(23328),J=r(75256),Q=r(98585),ee=r(79857),et=r(52581),er=r(15079),en=r(51215);let ea="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;function el(e){let t=Object.prototype.toString.call(e);return"[object Window]"===t||"[object global]"===t}function es(e){return"nodeType"in e}function ei(e){var t,r;return e?el(e)?e:es(e)&&null!=(t=null==(r=e.ownerDocument)?void 0:r.defaultView)?t:window:window}function eo(e){let{Document:t}=ei(e);return e instanceof t}function ec(e){return!el(e)&&e instanceof ei(e).HTMLElement}function ed(e){return e instanceof ei(e).SVGElement}function eu(e){return e?el(e)?e.document:es(e)?eo(e)?e:ec(e)||ed(e)?e.ownerDocument:document:document:document}let eh=ea?q.useLayoutEffect:q.useEffect;function em(e){let t=(0,q.useRef)(e);return eh(()=>{t.current=e}),(0,q.useCallback)(function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return null==t.current?void 0:t.current(...r)},[])}function ef(e,t){void 0===t&&(t=[e]);let r=(0,q.useRef)(e);return eh(()=>{r.current!==e&&(r.current=e)},t),r}function eg(e,t){let r=(0,q.useRef)();return(0,q.useMemo)(()=>{let t=e(r.current);return r.current=t,t},[...t])}function ex(e){let t=em(e),r=(0,q.useRef)(null),n=(0,q.useCallback)(e=>{e!==r.current&&(null==t||t(e,r.current)),r.current=e},[]);return[r,n]}function ep(e){let t=(0,q.useRef)();return(0,q.useEffect)(()=>{t.current=e},[e]),t.current}let eb={};function ev(e,t){return(0,q.useMemo)(()=>{if(t)return t;let r=null==eb[e]?0:eb[e]+1;return eb[e]=r,e+"-"+r},[e,t])}function ey(e){return function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];return n.reduce((t,r)=>{for(let[n,a]of Object.entries(r)){let r=t[n];null!=r&&(t[n]=r+e*a)}return t},{...t})}}let ew=ey(1),ej=ey(-1);function eN(e){if(!e)return!1;let{KeyboardEvent:t}=ei(e.target);return t&&e instanceof t}function ek(e){if(function(e){if(!e)return!1;let{TouchEvent:t}=ei(e.target);return t&&e instanceof t}(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:r}=e.touches[0];return{x:t,y:r}}if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:r}=e.changedTouches[0];return{x:t,y:r}}}return"clientX"in e&&"clientY"in e?{x:e.clientX,y:e.clientY}:null}let eC=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:r}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(r?Math.round(r):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:r}=e;return"scaleX("+t+") scaleY("+r+")"}},Transform:{toString(e){if(e)return[eC.Translate.toString(e),eC.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:r,easing:n}=e;return t+" "+r+"ms "+n}}}),eS="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]",eE={display:"none"};function eD(e){let{id:t,value:r}=e;return U().createElement("div",{id:t,style:eE},r)}function eA(e){let{id:t,announcement:r,ariaLiveType:n="assertive"}=e;return U().createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":n,"aria-atomic":!0},r)}let eM=(0,q.createContext)(null),eR={draggable:"\n    To pick up a draggable item, press the space bar.\n    While dragging, use the arrow keys to move the item.\n    Press space again to drop the item in its new position, or press escape to cancel.\n  "},eO={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:r}=e;return r?"Draggable item "+t.id+" was moved over droppable area "+r.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:r}=e;return r?"Draggable item "+t.id+" was dropped over droppable area "+r.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function eT(e){let{announcements:t=eO,container:r,hiddenTextDescribedById:n,screenReaderInstructions:a=eR}=e,{announce:l,announcement:s}=function(){let[e,t]=(0,q.useState)("");return{announce:(0,q.useCallback)(e=>{null!=e&&t(e)},[]),announcement:e}}(),i=ev("DndLiveRegion"),[o,c]=(0,q.useState)(!1);if((0,q.useEffect)(()=>{c(!0)},[]),function(e){let t=(0,q.useContext)(eM);(0,q.useEffect)(()=>{if(!t)throw Error("useDndMonitor must be used within a children of <DndContext>");return t(e)},[e,t])}((0,q.useMemo)(()=>({onDragStart(e){let{active:r}=e;l(t.onDragStart({active:r}))},onDragMove(e){let{active:r,over:n}=e;t.onDragMove&&l(t.onDragMove({active:r,over:n}))},onDragOver(e){let{active:r,over:n}=e;l(t.onDragOver({active:r,over:n}))},onDragEnd(e){let{active:r,over:n}=e;l(t.onDragEnd({active:r,over:n}))},onDragCancel(e){let{active:r,over:n}=e;l(t.onDragCancel({active:r,over:n}))}}),[l,t])),!o)return null;let d=U().createElement(U().Fragment,null,U().createElement(eD,{id:n,value:a.draggable}),U().createElement(eA,{id:i,announcement:s}));return r?(0,en.createPortal)(d,r):d}function eI(){}function ez(e,t){return(0,q.useMemo)(()=>({sensor:e,options:null!=t?t:{}}),[e,t])}!function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"}(a||(a={}));let e$=Object.freeze({x:0,y:0});function eL(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function eP(e,t){let{data:{value:r}}=e,{data:{value:n}}=t;return r-n}function eF(e,t){let{data:{value:r}}=e,{data:{value:n}}=t;return n-r}function e_(e){let{left:t,top:r,height:n,width:a}=e;return[{x:t,y:r},{x:t+a,y:r},{x:t,y:r+n},{x:t+a,y:r+n}]}function eB(e,t){if(!e||0===e.length)return null;let[r]=e;return t?r[t]:r}function eq(e,t,r){return void 0===t&&(t=e.left),void 0===r&&(r=e.top),{x:t+.5*e.width,y:r+.5*e.height}}let eU=e=>{let{collisionRect:t,droppableRects:r,droppableContainers:n}=e,a=eq(t,t.left,t.top),l=[];for(let e of n){let{id:t}=e,n=r.get(t);if(n){let r=eL(eq(n),a);l.push({id:t,data:{droppableContainer:e,value:r}})}}return l.sort(eP)},eH=e=>{let{collisionRect:t,droppableRects:r,droppableContainers:n}=e,a=e_(t),l=[];for(let e of n){let{id:t}=e,n=r.get(t);if(n){let r=e_(n),s=Number((a.reduce((e,t,n)=>e+eL(r[n],t),0)/4).toFixed(4));l.push({id:t,data:{droppableContainer:e,value:s}})}}return l.sort(eP)},eW=e=>{let{collisionRect:t,droppableRects:r,droppableContainers:n}=e,a=[];for(let e of n){let{id:n}=e,l=r.get(n);if(l){let r=function(e,t){let r=Math.max(t.top,e.top),n=Math.max(t.left,e.left),a=Math.min(t.left+t.width,e.left+e.width),l=Math.min(t.top+t.height,e.top+e.height);if(n<a&&r<l){let s=t.width*t.height,i=e.width*e.height,o=(a-n)*(l-r);return Number((o/(s+i-o)).toFixed(4))}return 0}(l,t);r>0&&a.push({id:n,data:{droppableContainer:e,value:r}})}}return a.sort(eF)};function eV(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:e$}let eX=function(e){return function(t){for(var r=arguments.length,n=Array(r>1?r-1:0),a=1;a<r;a++)n[a-1]=arguments[a];return n.reduce((t,r)=>({...t,top:t.top+e*r.y,bottom:t.bottom+e*r.y,left:t.left+e*r.x,right:t.right+e*r.x}),{...t})}}(1),eY={ignoreTransform:!1};function eG(e,t){void 0===t&&(t=eY);let r=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:t,transformOrigin:n}=ei(e).getComputedStyle(e);t&&(r=function(e,t,r){let n=function(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}(t);if(!n)return e;let{scaleX:a,scaleY:l,x:s,y:i}=n,o=e.left-s-(1-a)*parseFloat(r),c=e.top-i-(1-l)*parseFloat(r.slice(r.indexOf(" ")+1)),d=a?e.width/a:e.width,u=l?e.height/l:e.height;return{width:d,height:u,top:c,right:o+d,bottom:c+u,left:o}}(r,t,n))}let{top:n,left:a,width:l,height:s,bottom:i,right:o}=r;return{top:n,left:a,width:l,height:s,bottom:i,right:o}}function eK(e){return eG(e,{ignoreTransform:!0})}function eZ(e,t){let r=[];return e?function n(a){var l;if(null!=t&&r.length>=t||!a)return r;if(eo(a)&&null!=a.scrollingElement&&!r.includes(a.scrollingElement))return r.push(a.scrollingElement),r;if(!ec(a)||ed(a)||r.includes(a))return r;let s=ei(e).getComputedStyle(a);return(a!==e&&function(e,t){void 0===t&&(t=ei(e).getComputedStyle(e));let r=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(e=>{let n=t[e];return"string"==typeof n&&r.test(n)})}(a,s)&&r.push(a),void 0===(l=s)&&(l=ei(a).getComputedStyle(a)),"fixed"===l.position)?r:n(a.parentNode)}(e):r}function eJ(e){let[t]=eZ(e,1);return null!=t?t:null}function eQ(e){return ea&&e?el(e)?e:es(e)?eo(e)||e===eu(e).scrollingElement?window:ec(e)?e:null:null:null}function e0(e){return el(e)?e.scrollX:e.scrollLeft}function e1(e){return el(e)?e.scrollY:e.scrollTop}function e2(e){return{x:e0(e),y:e1(e)}}function e4(e){return!!ea&&!!e&&e===document.scrollingElement}function e5(e){let t={x:0,y:0},r=e4(e)?{height:window.innerHeight,width:window.innerWidth}:{height:e.clientHeight,width:e.clientWidth},n={x:e.scrollWidth-r.width,y:e.scrollHeight-r.height},a=e.scrollTop<=t.y,l=e.scrollLeft<=t.x;return{isTop:a,isLeft:l,isBottom:e.scrollTop>=n.y,isRight:e.scrollLeft>=n.x,maxScroll:n,minScroll:t}}!function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"}(l||(l={}));let e3={x:.2,y:.2};function e6(e){return e.reduce((e,t)=>ew(e,e2(t)),e$)}let e8=[["x",["left","right"],function(e){return e.reduce((e,t)=>e+e0(t),0)}],["y",["top","bottom"],function(e){return e.reduce((e,t)=>e+e1(t),0)}]];class e7{constructor(e,t){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let r=eZ(t),n=e6(r);for(let[t,a,l]of(this.rect={...e},this.width=e.width,this.height=e.height,e8))for(let e of a)Object.defineProperty(this,e,{get:()=>{let a=l(r),s=n[t]-a;return this.rect[e]+s},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class e9{constructor(e){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(e=>{var t;return null==(t=this.target)?void 0:t.removeEventListener(...e)})},this.target=e}add(e,t,r){var n;null==(n=this.target)||n.addEventListener(e,t,r),this.listeners.push([e,t,r])}}function te(e,t){let r=Math.abs(e.x),n=Math.abs(e.y);return"number"==typeof t?Math.sqrt(r**2+n**2)>t:"x"in t&&"y"in t?r>t.x&&n>t.y:"x"in t?r>t.x:"y"in t&&n>t.y}function tt(e){e.preventDefault()}function tr(e){e.stopPropagation()}(function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"})(s||(s={})),function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter",e.Tab="Tab"}(i||(i={}));let tn={start:[i.Space,i.Enter],cancel:[i.Esc],end:[i.Space,i.Enter,i.Tab]},ta=(e,t)=>{let{currentCoordinates:r}=t;switch(e.code){case i.Right:return{...r,x:r.x+25};case i.Left:return{...r,x:r.x-25};case i.Down:return{...r,y:r.y+25};case i.Up:return{...r,y:r.y-25}}};class tl{constructor(e){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=e;let{event:{target:t}}=e;this.props=e,this.listeners=new e9(eu(t)),this.windowListeners=new e9(ei(t)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(s.Resize,this.handleCancel),this.windowListeners.add(s.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(s.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:e,onStart:t}=this.props,r=e.node.current;r&&function(e,t){if(void 0===t&&(t=eG),!e)return;let{top:r,left:n,bottom:a,right:l}=t(e);eJ(e)&&(a<=0||l<=0||r>=window.innerHeight||n>=window.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}(r),t(e$)}handleKeyDown(e){if(eN(e)){let{active:t,context:r,options:n}=this.props,{keyboardCodes:a=tn,coordinateGetter:l=ta,scrollBehavior:s="smooth"}=n,{code:o}=e;if(a.end.includes(o)){this.handleEnd(e);return}if(a.cancel.includes(o)){this.handleCancel(e);return}let{collisionRect:c}=r.current,d=c?{x:c.left,y:c.top}:e$;this.referenceCoordinates||(this.referenceCoordinates=d);let u=l(e,{active:t,context:r.current,currentCoordinates:d});if(u){let t=ej(u,d),n={x:0,y:0},{scrollableAncestors:a}=r.current;for(let r of a){let a=e.code,{isTop:l,isRight:o,isLeft:c,isBottom:d,maxScroll:h,minScroll:m}=e5(r),f=function(e){if(e===document.scrollingElement){let{innerWidth:e,innerHeight:t}=window;return{top:0,left:0,right:e,bottom:t,width:e,height:t}}let{top:t,left:r,right:n,bottom:a}=e.getBoundingClientRect();return{top:t,left:r,right:n,bottom:a,width:e.clientWidth,height:e.clientHeight}}(r),g={x:Math.min(a===i.Right?f.right-f.width/2:f.right,Math.max(a===i.Right?f.left:f.left+f.width/2,u.x)),y:Math.min(a===i.Down?f.bottom-f.height/2:f.bottom,Math.max(a===i.Down?f.top:f.top+f.height/2,u.y))},x=a===i.Right&&!o||a===i.Left&&!c,p=a===i.Down&&!d||a===i.Up&&!l;if(x&&g.x!==u.x){let e=r.scrollLeft+t.x,l=a===i.Right&&e<=h.x||a===i.Left&&e>=m.x;if(l&&!t.y){r.scrollTo({left:e,behavior:s});return}l?n.x=r.scrollLeft-e:n.x=a===i.Right?r.scrollLeft-h.x:r.scrollLeft-m.x,n.x&&r.scrollBy({left:-n.x,behavior:s});break}if(p&&g.y!==u.y){let e=r.scrollTop+t.y,l=a===i.Down&&e<=h.y||a===i.Up&&e>=m.y;if(l&&!t.x){r.scrollTo({top:e,behavior:s});return}l?n.y=r.scrollTop-e:n.y=a===i.Down?r.scrollTop-h.y:r.scrollTop-m.y,n.y&&r.scrollBy({top:-n.y,behavior:s});break}}this.handleMove(e,ew(ej(u,this.referenceCoordinates),n))}}}handleMove(e,t){let{onMove:r}=this.props;e.preventDefault(),r(t)}handleEnd(e){let{onEnd:t}=this.props;e.preventDefault(),this.detach(),t()}handleCancel(e){let{onCancel:t}=this.props;e.preventDefault(),this.detach(),t()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}function ts(e){return!!(e&&"distance"in e)}function ti(e){return!!(e&&"delay"in e)}tl.activators=[{eventName:"onKeyDown",handler:(e,t,r)=>{let{keyboardCodes:n=tn,onActivation:a}=t,{active:l}=r,{code:s}=e.nativeEvent;if(n.start.includes(s)){let t=l.activatorNode.current;return(!t||e.target===t)&&(e.preventDefault(),null==a||a({event:e.nativeEvent}),!0)}return!1}}];class to{constructor(e,t,r){var n;void 0===r&&(r=function(e){let{EventTarget:t}=ei(e);return e instanceof t?e:eu(e)}(e.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=e,this.events=t;let{event:a}=e,{target:l}=a;this.props=e,this.events=t,this.document=eu(l),this.documentListeners=new e9(this.document),this.listeners=new e9(r),this.windowListeners=new e9(ei(l)),this.initialCoordinates=null!=(n=ek(a))?n:e$,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:e,props:{options:{activationConstraint:t,bypassActivationConstraint:r}}}=this;if(this.listeners.add(e.move.name,this.handleMove,{passive:!1}),this.listeners.add(e.end.name,this.handleEnd),e.cancel&&this.listeners.add(e.cancel.name,this.handleCancel),this.windowListeners.add(s.Resize,this.handleCancel),this.windowListeners.add(s.DragStart,tt),this.windowListeners.add(s.VisibilityChange,this.handleCancel),this.windowListeners.add(s.ContextMenu,tt),this.documentListeners.add(s.Keydown,this.handleKeydown),t){if(null!=r&&r({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(ti(t)){this.timeoutId=setTimeout(this.handleStart,t.delay),this.handlePending(t);return}if(ts(t)){this.handlePending(t);return}}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),null!==this.timeoutId&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(e,t){let{active:r,onPending:n}=this.props;n(r,e,this.initialCoordinates,t)}handleStart(){let{initialCoordinates:e}=this,{onStart:t}=this.props;e&&(this.activated=!0,this.documentListeners.add(s.Click,tr,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(s.SelectionChange,this.removeTextSelection),t(e))}handleMove(e){var t;let{activated:r,initialCoordinates:n,props:a}=this,{onMove:l,options:{activationConstraint:s}}=a;if(!n)return;let i=null!=(t=ek(e))?t:e$,o=ej(n,i);if(!r&&s){if(ts(s)){if(null!=s.tolerance&&te(o,s.tolerance))return this.handleCancel();if(te(o,s.distance))return this.handleStart()}return ti(s)&&te(o,s.tolerance)?this.handleCancel():void this.handlePending(s,o)}e.cancelable&&e.preventDefault(),l(i)}handleEnd(){let{onAbort:e,onEnd:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleCancel(){let{onAbort:e,onCancel:t}=this.props;this.detach(),this.activated||e(this.props.active),t()}handleKeydown(e){e.code===i.Esc&&this.handleCancel()}removeTextSelection(){var e;null==(e=this.document.getSelection())||e.removeAllRanges()}}let tc={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class td extends to{constructor(e){let{event:t}=e;super(e,tc,eu(t.target))}}td.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:n}=t;return!!r.isPrimary&&0===r.button&&(null==n||n({event:r}),!0)}}];let tu={move:{name:"mousemove"},end:{name:"mouseup"}};!function(e){e[e.RightClick=2]="RightClick"}(o||(o={}));class th extends to{constructor(e){super(e,tu,eu(e.event.target))}}th.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:n}=t;return r.button!==o.RightClick&&(null==n||n({event:r}),!0)}}];let tm={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class tf extends to{constructor(e){super(e,tm)}static setup(){return window.addEventListener(tm.move.name,e,{capture:!1,passive:!1}),function(){window.removeEventListener(tm.move.name,e)};function e(){}}}tf.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:r}=e,{onActivation:n}=t,{touches:a}=r;return!(a.length>1)&&(null==n||n({event:r}),!0)}}],function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"}(c||(c={})),function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"}(d||(d={}));let tg={x:{[l.Backward]:!1,[l.Forward]:!1},y:{[l.Backward]:!1,[l.Forward]:!1}};(function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"})(u||(u={})),(h||(h={})).Optimized="optimized";let tx=new Map;function tp(e,t){return eg(r=>e?r||("function"==typeof t?t(e):e):null,[t,e])}function tb(e){let{callback:t,disabled:r}=e,n=em(t),a=(0,q.useMemo)(()=>{if(r||"undefined"==typeof window||void 0===window.ResizeObserver)return;let{ResizeObserver:e}=window;return new e(n)},[r]);return(0,q.useEffect)(()=>()=>null==a?void 0:a.disconnect(),[a]),a}function tv(e){return new e7(eG(e),e)}function ty(e,t,r){void 0===t&&(t=tv);let[n,a]=(0,q.useState)(null);function l(){a(n=>{if(!e)return null;if(!1===e.isConnected){var a;return null!=(a=null!=n?n:r)?a:null}let l=t(e);return JSON.stringify(n)===JSON.stringify(l)?n:l})}let s=function(e){let{callback:t,disabled:r}=e,n=em(t),a=(0,q.useMemo)(()=>{if(r||"undefined"==typeof window||void 0===window.MutationObserver)return;let{MutationObserver:e}=window;return new e(n)},[n,r]);return(0,q.useEffect)(()=>()=>null==a?void 0:a.disconnect(),[a]),a}({callback(t){if(e)for(let r of t){let{type:t,target:n}=r;if("childList"===t&&n instanceof HTMLElement&&n.contains(e)){l();break}}}}),i=tb({callback:l});return eh(()=>{l(),e?(null==i||i.observe(e),null==s||s.observe(document.body,{childList:!0,subtree:!0})):(null==i||i.disconnect(),null==s||s.disconnect())},[e]),n}let tw=[];function tj(e,t){void 0===t&&(t=[]);let r=(0,q.useRef)(null);return(0,q.useEffect)(()=>{r.current=null},t),(0,q.useEffect)(()=>{let t=e!==e$;t&&!r.current&&(r.current=e),!t&&r.current&&(r.current=null)},[e]),r.current?ej(e,r.current):e$}function tN(e){return(0,q.useMemo)(()=>e?function(e){let t=e.innerWidth,r=e.innerHeight;return{top:0,left:0,right:t,bottom:r,width:t,height:r}}(e):null,[e])}let tk=[],tC=[{sensor:td,options:{}},{sensor:tl,options:{}}],tS={current:{}},tE={draggable:{measure:eK},droppable:{measure:eK,strategy:u.WhileDragging,frequency:h.Optimized},dragOverlay:{measure:eG}};class tD extends Map{get(e){var t;return null!=e&&null!=(t=super.get(e))?t:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(e=>{let{disabled:t}=e;return!t})}getNodeFor(e){var t,r;return null!=(t=null==(r=this.get(e))?void 0:r.node.current)?t:void 0}}let tA={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new tD,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:eI},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:tE,measureDroppableContainers:eI,windowRect:null,measuringScheduled:!1},tM={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:eI,draggableNodes:new Map,over:null,measureDroppableContainers:eI},tR=(0,q.createContext)(tM),tO=(0,q.createContext)(tA);function tT(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new tD}}}function tI(e,t){switch(t.type){case a.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case a.DragMove:if(null==e.draggable.active)return e;return{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}};case a.DragEnd:case a.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case a.RegisterDroppable:{let{element:r}=t,{id:n}=r,a=new tD(e.droppable.containers);return a.set(n,r),{...e,droppable:{...e.droppable,containers:a}}}case a.SetDroppableDisabled:{let{id:r,key:n,disabled:a}=t,l=e.droppable.containers.get(r);if(!l||n!==l.key)return e;let s=new tD(e.droppable.containers);return s.set(r,{...l,disabled:a}),{...e,droppable:{...e.droppable,containers:s}}}case a.UnregisterDroppable:{let{id:r,key:n}=t,a=e.droppable.containers.get(r);if(!a||n!==a.key)return e;let l=new tD(e.droppable.containers);return l.delete(r),{...e,droppable:{...e.droppable,containers:l}}}default:return e}}function tz(e){let{disabled:t}=e,{active:r,activatorEvent:n,draggableNodes:a}=(0,q.useContext)(tR),l=ep(n),s=ep(null==r?void 0:r.id);return(0,q.useEffect)(()=>{if(!t&&!n&&l&&null!=s){if(!eN(l)||document.activeElement===l.target)return;let e=a.get(s);if(!e)return;let{activatorNode:t,node:r}=e;if(t.current||r.current)requestAnimationFrame(()=>{for(let e of[t.current,r.current]){if(!e)continue;let t=e.matches(eS)?e:e.querySelector(eS);if(t){t.focus();break}}})}},[n,t,a,s,l]),null}let t$=(0,q.createContext)({...e$,scaleX:1,scaleY:1});!function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"}(m||(m={}));let tL=(0,q.memo)(function(e){var t,r,n,s,i,o;let{id:h,accessibility:f,autoScroll:g=!0,children:x,sensors:p=tC,collisionDetection:b=eW,measuring:v,modifiers:y,...w}=e,[j,N]=(0,q.useReducer)(tI,void 0,tT),[k,C]=function(){let[e]=(0,q.useState)(()=>new Set),t=(0,q.useCallback)(t=>(e.add(t),()=>e.delete(t)),[e]);return[(0,q.useCallback)(t=>{let{type:r,event:n}=t;e.forEach(e=>{var t;return null==(t=e[r])?void 0:t.call(e,n)})},[e]),t]}(),[S,E]=(0,q.useState)(m.Uninitialized),D=S===m.Initialized,{draggable:{active:A,nodes:M,translate:R},droppable:{containers:O}}=j,T=null!=A?M.get(A):null,I=(0,q.useRef)({initial:null,translated:null}),z=(0,q.useMemo)(()=>{var e;return null!=A?{id:A,data:null!=(e=null==T?void 0:T.data)?e:tS,rect:I}:null},[A,T]),$=(0,q.useRef)(null),[L,P]=(0,q.useState)(null),[F,_]=(0,q.useState)(null),B=ef(w,Object.values(w)),H=ev("DndDescribedBy",h),W=(0,q.useMemo)(()=>O.getEnabled(),[O]),V=(0,q.useMemo)(()=>({draggable:{...tE.draggable,...null==v?void 0:v.draggable},droppable:{...tE.droppable,...null==v?void 0:v.droppable},dragOverlay:{...tE.dragOverlay,...null==v?void 0:v.dragOverlay}}),[null==v?void 0:v.draggable,null==v?void 0:v.droppable,null==v?void 0:v.dragOverlay]),{droppableRects:X,measureDroppableContainers:Y,measuringScheduled:G}=function(e,t){let{dragging:r,dependencies:n,config:a}=t,[l,s]=(0,q.useState)(null),{frequency:i,measure:o,strategy:c}=a,d=(0,q.useRef)(e),h=function(){switch(c){case u.Always:return!1;case u.BeforeDragging:return r;default:return!r}}(),m=ef(h),f=(0,q.useCallback)(function(e){void 0===e&&(e=[]),!m.current&&s(t=>null===t?e:t.concat(e.filter(e=>!t.includes(e))))},[m]),g=(0,q.useRef)(null),x=eg(t=>{if(h&&!r)return tx;if(!t||t===tx||d.current!==e||null!=l){let t=new Map;for(let r of e){if(!r)continue;if(l&&l.length>0&&!l.includes(r.id)&&r.rect.current){t.set(r.id,r.rect.current);continue}let e=r.node.current,n=e?new e7(o(e),e):null;r.rect.current=n,n&&t.set(r.id,n)}return t}return t},[e,l,r,h,o]);return(0,q.useEffect)(()=>{d.current=e},[e]),(0,q.useEffect)(()=>{!h&&f()},[r,h]),(0,q.useEffect)(()=>{l&&l.length>0&&s(null)},[JSON.stringify(l)]),(0,q.useEffect)(()=>{!h&&"number"==typeof i&&null===g.current&&(g.current=setTimeout(()=>{f(),g.current=null},i))},[i,h,f,...n]),{droppableRects:x,measureDroppableContainers:f,measuringScheduled:null!=l}}(W,{dragging:D,dependencies:[R.x,R.y],config:V.droppable}),K=function(e,t){let r=null!=t?e.get(t):void 0,n=r?r.node.current:null;return eg(e=>{var r;return null==t?null:null!=(r=null!=n?n:e)?r:null},[n,t])}(M,A),Z=(0,q.useMemo)(()=>F?ek(F):null,[F]),J=function(){let e=(null==L?void 0:L.autoScrollEnabled)===!1,t="object"==typeof g?!1===g.enabled:!1===g,r=D&&!e&&!t;return"object"==typeof g?{...g,enabled:r}:{enabled:r}}(),Q=tp(K,V.draggable.measure);!function(e){let{activeNode:t,measure:r,initialRect:n,config:a=!0}=e,l=(0,q.useRef)(!1),{x:s,y:i}="boolean"==typeof a?{x:a,y:a}:a;eh(()=>{if(!s&&!i||!t){l.current=!1;return}if(l.current||!n)return;let e=null==t?void 0:t.node.current;if(!e||!1===e.isConnected)return;let a=eV(r(e),n);if(s||(a.x=0),i||(a.y=0),l.current=!0,Math.abs(a.x)>0||Math.abs(a.y)>0){let t=eJ(e);t&&t.scrollBy({top:a.y,left:a.x})}},[t,s,i,n,r])}({activeNode:null!=A?M.get(A):null,config:J.layoutShiftCompensation,initialRect:Q,measure:V.draggable.measure});let ee=ty(K,V.draggable.measure,Q),et=ty(K?K.parentElement:null),er=(0,q.useRef)({activatorEvent:null,active:null,activeNode:K,collisionRect:null,collisions:null,droppableRects:X,draggableNodes:M,draggingNode:null,draggingNodeRect:null,droppableContainers:O,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),el=O.getNodeFor(null==(t=er.current.over)?void 0:t.id),es=function(e){let{measure:t}=e,[r,n]=(0,q.useState)(null),a=tb({callback:(0,q.useCallback)(e=>{for(let{target:r}of e)if(ec(r)){n(e=>{let n=t(r);return e?{...e,width:n.width,height:n.height}:n});break}},[t])}),[l,s]=ex((0,q.useCallback)(e=>{let r=function(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return ec(t)?t:e}(e);null==a||a.disconnect(),r&&(null==a||a.observe(r)),n(r?t(r):null)},[t,a]));return(0,q.useMemo)(()=>({nodeRef:l,rect:r,setRef:s}),[r,l,s])}({measure:V.dragOverlay.measure}),eo=null!=(r=es.nodeRef.current)?r:K,ed=D?null!=(n=es.rect)?n:ee:null,eu=!!(es.nodeRef.current&&es.rect),em=function(e){let t=tp(e);return eV(e,t)}(eu?null:ee),eb=tN(eo?ei(eo):null),ey=function(e){let t=(0,q.useRef)(e),r=eg(r=>e?r&&r!==tw&&e&&t.current&&e.parentNode===t.current.parentNode?r:eZ(e):tw,[e]);return(0,q.useEffect)(()=>{t.current=e},[e]),r}(D?null!=el?el:K:null),ej=function(e,t){void 0===t&&(t=eG);let[r]=e,n=tN(r?ei(r):null),[a,l]=(0,q.useState)(tk);function s(){l(()=>e.length?e.map(e=>e4(e)?n:new e7(t(e),e)):tk)}let i=tb({callback:s});return eh(()=>{null==i||i.disconnect(),s(),e.forEach(e=>null==i?void 0:i.observe(e))},[e]),a}(ey),eN=function(e,t){let{transform:r,...n}=t;return null!=e&&e.length?e.reduce((e,t)=>t({transform:e,...n}),r):r}(y,{transform:{x:R.x-em.x,y:R.y-em.y,scaleX:1,scaleY:1},activatorEvent:F,active:z,activeNodeRect:ee,containerNodeRect:et,draggingNodeRect:ed,over:er.current.over,overlayNodeRect:es.rect,scrollableAncestors:ey,scrollableAncestorRects:ej,windowRect:eb}),eC=Z?ew(Z,R):null,eS=function(e){let[t,r]=(0,q.useState)(null),n=(0,q.useRef)(e),a=(0,q.useCallback)(e=>{let t=eQ(e.target);t&&r(e=>e?(e.set(t,e2(t)),new Map(e)):null)},[]);return(0,q.useEffect)(()=>{let t=n.current;if(e!==t){l(t);let s=e.map(e=>{let t=eQ(e);return t?(t.addEventListener("scroll",a,{passive:!0}),[t,e2(t)]):null}).filter(e=>null!=e);r(s.length?new Map(s):null),n.current=e}return()=>{l(e),l(t)};function l(e){e.forEach(e=>{let t=eQ(e);null==t||t.removeEventListener("scroll",a)})}},[a,e]),(0,q.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((e,t)=>ew(e,t),e$):e6(e):e$,[e,t])}(ey),eE=tj(eS),eD=tj(eS,[ee]),eA=ew(eN,eE),eR=ed?eX(ed,eN):null,eO=z&&eR?b({active:z,collisionRect:eR,droppableRects:X,droppableContainers:W,pointerCoordinates:eC}):null,eI=eB(eO,"id"),[ez,eL]=(0,q.useState)(null),eP=(i=eu?eN:ew(eN,eD),o=null!=(s=null==ez?void 0:ez.rect)?s:null,{...i,scaleX:o&&ee?o.width/ee.width:1,scaleY:o&&ee?o.height/ee.height:1}),eF=(0,q.useRef)(null),e_=(0,q.useCallback)((e,t)=>{let{sensor:r,options:n}=t;if(null==$.current)return;let l=M.get($.current);if(!l)return;let s=e.nativeEvent,i=new r({active:$.current,activeNode:l,event:s,options:n,context:er,onAbort(e){if(!M.get(e))return;let{onDragAbort:t}=B.current,r={id:e};null==t||t(r),k({type:"onDragAbort",event:r})},onPending(e,t,r,n){if(!M.get(e))return;let{onDragPending:a}=B.current,l={id:e,constraint:t,initialCoordinates:r,offset:n};null==a||a(l),k({type:"onDragPending",event:l})},onStart(e){let t=$.current;if(null==t)return;let r=M.get(t);if(!r)return;let{onDragStart:n}=B.current,l={activatorEvent:s,active:{id:t,data:r.data,rect:I}};(0,en.unstable_batchedUpdates)(()=>{null==n||n(l),E(m.Initializing),N({type:a.DragStart,initialCoordinates:e,active:t}),k({type:"onDragStart",event:l}),P(eF.current),_(s)})},onMove(e){N({type:a.DragMove,coordinates:e})},onEnd:o(a.DragEnd),onCancel:o(a.DragCancel)});function o(e){return async function(){let{active:t,collisions:r,over:n,scrollAdjustedTranslate:l}=er.current,i=null;if(t&&l){let{cancelDrop:o}=B.current;i={activatorEvent:s,active:t,collisions:r,delta:l,over:n},e===a.DragEnd&&"function"==typeof o&&await Promise.resolve(o(i))&&(e=a.DragCancel)}$.current=null,(0,en.unstable_batchedUpdates)(()=>{N({type:e}),E(m.Uninitialized),eL(null),P(null),_(null),eF.current=null;let t=e===a.DragEnd?"onDragEnd":"onDragCancel";if(i){let e=B.current[t];null==e||e(i),k({type:t,event:i})}})}}eF.current=i},[M]),eq=(0,q.useCallback)((e,t)=>(r,n)=>{let a=r.nativeEvent,l=M.get(n);null===$.current&&l&&!a.dndKit&&!a.defaultPrevented&&!0===e(r,t.options,{active:l})&&(a.dndKit={capturedBy:t.sensor},$.current=n,e_(r,t))},[M,e_]),eU=(0,q.useMemo)(()=>p.reduce((e,t)=>{let{sensor:r}=t;return[...e,...r.activators.map(e=>({eventName:e.eventName,handler:eq(e.handler,t)}))]},[]),[p,eq]);(0,q.useEffect)(()=>{if(!ea)return;let e=p.map(e=>{let{sensor:t}=e;return null==t.setup?void 0:t.setup()});return()=>{for(let t of e)null==t||t()}},p.map(e=>{let{sensor:t}=e;return t})),eh(()=>{ee&&S===m.Initializing&&E(m.Initialized)},[ee,S]),(0,q.useEffect)(()=>{let{onDragMove:e}=B.current,{active:t,activatorEvent:r,collisions:n,over:a}=er.current;if(!t||!r)return;let l={active:t,activatorEvent:r,collisions:n,delta:{x:eA.x,y:eA.y},over:a};(0,en.unstable_batchedUpdates)(()=>{null==e||e(l),k({type:"onDragMove",event:l})})},[eA.x,eA.y]),(0,q.useEffect)(()=>{let{active:e,activatorEvent:t,collisions:r,droppableContainers:n,scrollAdjustedTranslate:a}=er.current;if(!e||null==$.current||!t||!a)return;let{onDragOver:l}=B.current,s=n.get(eI),i=s&&s.rect.current?{id:s.id,rect:s.rect.current,data:s.data,disabled:s.disabled}:null,o={active:e,activatorEvent:t,collisions:r,delta:{x:a.x,y:a.y},over:i};(0,en.unstable_batchedUpdates)(()=>{eL(i),null==l||l(o),k({type:"onDragOver",event:o})})},[eI]),eh(()=>{er.current={activatorEvent:F,active:z,activeNode:K,collisionRect:eR,collisions:eO,droppableRects:X,draggableNodes:M,draggingNode:eo,draggingNodeRect:ed,droppableContainers:O,over:ez,scrollableAncestors:ey,scrollAdjustedTranslate:eA},I.current={initial:ed,translated:eR}},[z,K,eO,eR,M,eo,ed,X,O,ez,ey,eA]),function(e){let{acceleration:t,activator:r=c.Pointer,canScroll:n,draggingRect:a,enabled:s,interval:i=5,order:o=d.TreeOrder,pointerCoordinates:u,scrollableAncestors:h,scrollableAncestorRects:m,delta:f,threshold:g}=e,x=function(e){let{delta:t,disabled:r}=e,n=ep(t);return eg(e=>{if(r||!n||!e)return tg;let a={x:Math.sign(t.x-n.x),y:Math.sign(t.y-n.y)};return{x:{[l.Backward]:e.x[l.Backward]||-1===a.x,[l.Forward]:e.x[l.Forward]||1===a.x},y:{[l.Backward]:e.y[l.Backward]||-1===a.y,[l.Forward]:e.y[l.Forward]||1===a.y}}},[r,t,n])}({delta:f,disabled:!s}),[p,b]=function(){let e=(0,q.useRef)(null);return[(0,q.useCallback)((t,r)=>{e.current=setInterval(t,r)},[]),(0,q.useCallback)(()=>{null!==e.current&&(clearInterval(e.current),e.current=null)},[])]}(),v=(0,q.useRef)({x:0,y:0}),y=(0,q.useRef)({x:0,y:0}),w=(0,q.useMemo)(()=>{switch(r){case c.Pointer:return u?{top:u.y,bottom:u.y,left:u.x,right:u.x}:null;case c.DraggableRect:return a}},[r,a,u]),j=(0,q.useRef)(null),N=(0,q.useCallback)(()=>{let e=j.current;if(!e)return;let t=v.current.x*y.current.x,r=v.current.y*y.current.y;e.scrollBy(t,r)},[]),k=(0,q.useMemo)(()=>o===d.TreeOrder?[...h].reverse():h,[o,h]);(0,q.useEffect)(()=>{if(!s||!h.length||!w){b();return}for(let e of k){if((null==n?void 0:n(e))===!1)continue;let r=m[h.indexOf(e)];if(!r)continue;let{direction:a,speed:s}=function(e,t,r,n,a){let{top:s,left:i,right:o,bottom:c}=r;void 0===n&&(n=10),void 0===a&&(a=e3);let{isTop:d,isBottom:u,isLeft:h,isRight:m}=e5(e),f={x:0,y:0},g={x:0,y:0},x={height:t.height*a.y,width:t.width*a.x};return!d&&s<=t.top+x.height?(f.y=l.Backward,g.y=n*Math.abs((t.top+x.height-s)/x.height)):!u&&c>=t.bottom-x.height&&(f.y=l.Forward,g.y=n*Math.abs((t.bottom-x.height-c)/x.height)),!m&&o>=t.right-x.width?(f.x=l.Forward,g.x=n*Math.abs((t.right-x.width-o)/x.width)):!h&&i<=t.left+x.width&&(f.x=l.Backward,g.x=n*Math.abs((t.left+x.width-i)/x.width)),{direction:f,speed:g}}(e,r,w,t,g);for(let e of["x","y"])x[e][a[e]]||(s[e]=0,a[e]=0);if(s.x>0||s.y>0){b(),j.current=e,p(N,i),v.current=s,y.current=a;return}}v.current={x:0,y:0},y.current={x:0,y:0},b()},[t,N,n,b,s,i,JSON.stringify(w),JSON.stringify(x),p,h,k,m,JSON.stringify(g)])}({...J,delta:R,draggingRect:eR,pointerCoordinates:eC,scrollableAncestors:ey,scrollableAncestorRects:ej});let eH=(0,q.useMemo)(()=>({active:z,activeNode:K,activeNodeRect:ee,activatorEvent:F,collisions:eO,containerNodeRect:et,dragOverlay:es,draggableNodes:M,droppableContainers:O,droppableRects:X,over:ez,measureDroppableContainers:Y,scrollableAncestors:ey,scrollableAncestorRects:ej,measuringConfiguration:V,measuringScheduled:G,windowRect:eb}),[z,K,ee,F,eO,et,es,M,O,X,ez,Y,ey,ej,V,G,eb]),eY=(0,q.useMemo)(()=>({activatorEvent:F,activators:eU,active:z,activeNodeRect:ee,ariaDescribedById:{draggable:H},dispatch:N,draggableNodes:M,over:ez,measureDroppableContainers:Y}),[F,eU,z,ee,N,H,M,ez,Y]);return U().createElement(eM.Provider,{value:C},U().createElement(tR.Provider,{value:eY},U().createElement(tO.Provider,{value:eH},U().createElement(t$.Provider,{value:eP},x)),U().createElement(tz,{disabled:(null==f?void 0:f.restoreFocus)===!1})),U().createElement(eT,{...f,hiddenTextDescribedById:H}))}),tP=(0,q.createContext)(null),tF="button",t_={timeout:25};n={styles:{active:{opacity:"0"}}},e=>{let{active:t,dragOverlay:r}=e,a={},{styles:l,className:s}=n;if(null!=l&&l.active)for(let[e,r]of Object.entries(l.active))void 0!==r&&(a[e]=t.node.style.getPropertyValue(e),t.node.style.setProperty(e,r));if(null!=l&&l.dragOverlay)for(let[e,t]of Object.entries(l.dragOverlay))void 0!==t&&r.node.style.setProperty(e,t);return null!=s&&s.active&&t.node.classList.add(s.active),null!=s&&s.dragOverlay&&r.node.classList.add(s.dragOverlay),function(){for(let[e,r]of Object.entries(a))t.node.style.setProperty(e,r);null!=s&&s.active&&t.node.classList.remove(s.active)}};function tB(e,t,r){let n=e.slice();return n.splice(r<0?n.length+r:r,0,n.splice(t,1)[0]),n}function tq(e){return null!==e&&e>=0}let tU=e=>{let{rects:t,activeIndex:r,overIndex:n,index:a}=e,l=tB(t,n,r),s=t[a],i=l[a];return i&&s?{x:i.left-s.left,y:i.top-s.top,scaleX:i.width/s.width,scaleY:i.height/s.height}:null},tH={scaleX:1,scaleY:1},tW=e=>{var t;let{activeIndex:r,activeNodeRect:n,index:a,rects:l,overIndex:s}=e,i=null!=(t=l[r])?t:n;if(!i)return null;if(a===r){let e=l[s];return e?{x:0,y:r<s?e.top+e.height-(i.top+i.height):e.top-i.top,...tH}:null}let o=function(e,t,r){let n=e[t],a=e[t-1],l=e[t+1];return n?r<t?a?n.top-(a.top+a.height):l?l.top-(n.top+n.height):0:l?l.top-(n.top+n.height):a?n.top-(a.top+a.height):0:0}(l,a,r);return a>r&&a<=s?{x:0,y:-i.height-o,...tH}:a<r&&a>=s?{x:0,y:i.height+o,...tH}:{x:0,y:0,...tH}},tV="Sortable",tX=U().createContext({activeIndex:-1,containerId:tV,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:tU,disabled:{draggable:!1,droppable:!1}});function tY(e){let{children:t,id:r,items:n,strategy:a=tU,disabled:l=!1}=e,{active:s,dragOverlay:i,droppableRects:o,over:c,measureDroppableContainers:d}=(0,q.useContext)(tO),u=ev(tV,r),h=null!==i.rect,m=(0,q.useMemo)(()=>n.map(e=>"object"==typeof e&&"id"in e?e.id:e),[n]),f=null!=s,g=s?m.indexOf(s.id):-1,x=c?m.indexOf(c.id):-1,p=(0,q.useRef)(m),b=!function(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}(m,p.current),v=-1!==x&&-1===g||b,y="boolean"==typeof l?{draggable:l,droppable:l}:l;eh(()=>{b&&f&&d(m)},[b,m,f,d]),(0,q.useEffect)(()=>{p.current=m},[m]);let w=(0,q.useMemo)(()=>({activeIndex:g,containerId:u,disabled:y,disableTransforms:v,items:m,overIndex:x,useDragOverlay:h,sortedRects:m.reduce((e,t,r)=>{let n=o.get(t);return n&&(e[r]=n),e},Array(m.length)),strategy:a}),[g,u,y.draggable,y.droppable,v,m,x,o,h,a]);return U().createElement(tX.Provider,{value:w},t)}let tG=e=>{let{id:t,items:r,activeIndex:n,overIndex:a}=e;return tB(r,n,a).indexOf(t)},tK=e=>{let{containerId:t,isSorting:r,wasDragging:n,index:a,items:l,newIndex:s,previousItems:i,previousContainerId:o,transition:c}=e;return!!c&&!!n&&(i===l||a!==s)&&(!!r||s!==a&&t===o)},tZ={duration:200,easing:"ease"},tJ="transform",tQ=eC.Transition.toString({property:tJ,duration:0,easing:"linear"}),t0={roleDescription:"sortable"};function t1(e){if(!e)return!1;let t=e.data.current;return!!t&&"sortable"in t&&"object"==typeof t.sortable&&"containerId"in t.sortable&&"items"in t.sortable&&"index"in t.sortable}let t2=[i.Down,i.Right,i.Up,i.Left],t4=(e,t)=>{let{context:{active:r,collisionRect:n,droppableRects:a,droppableContainers:l,over:s,scrollableAncestors:o}}=t;if(t2.includes(e.code)){if(e.preventDefault(),!r||!n)return;let t=[];l.getEnabled().forEach(r=>{if(!r||null!=r&&r.disabled)return;let l=a.get(r.id);if(l)switch(e.code){case i.Down:n.top<l.top&&t.push(r);break;case i.Up:n.top>l.top&&t.push(r);break;case i.Left:n.left>l.left&&t.push(r);break;case i.Right:n.left<l.left&&t.push(r)}});let c=eH({active:r,collisionRect:n,droppableRects:a,droppableContainers:t,pointerCoordinates:null}),d=eB(c,"id");if(d===(null==s?void 0:s.id)&&c.length>1&&(d=c[1].id),null!=d){let e=l.get(r.id),t=l.get(d),s=t?a.get(t.id):null,i=null==t?void 0:t.node.current;if(i&&s&&e&&t){let r=eZ(i).some((e,t)=>o[t]!==e),a=t5(e,t),l=function(e,t){return!!(t1(e)&&t1(t)&&t5(e,t))&&e.data.current.sortable.index<t.data.current.sortable.index}(e,t),c=r||!a?{x:0,y:0}:{x:l?n.width-s.width:0,y:l?n.height-s.height:0},d={x:s.left,y:s.top};return c.x&&c.y?d:ej(d,c)}}}};function t5(e,t){return!!(t1(e)&&t1(t))&&e.data.current.sortable.containerId===t.data.current.sortable.containerId}function t3(){let{credits:e,hasSufficientCredits:t,minutes:r,organizationCreditThreshold:n}=(0,J.I)(),[a,l]=(0,q.useState)("all"),[s,i]=(0,q.useState)(null),[o,c]=(0,q.useState)("asc"),[d,u]=(0,q.useState)(0),[h,m]=(0,q.useState)([]),N=(0,q.useRef)(null),[F,_]=(0,q.useState)(1),[B,U]=(0,q.useState)(!0),[X,en]=(0,q.useState)(!1),ea=(0,q.useRef)(null),[el,es]=(0,q.useState)(""),[ei,eo]=(0,q.useState)(!1),[ec,ed]=(0,q.useState)({customerId:!0,name:!0,phoneNumber:!0,lastCall:!0,campaign:!0,createdAt:!0,timeZone:!0,source:!0,addedBy:!0,call:!0,actions:!0}),eu=[{id:"customerId",label:"Contact ID"},{id:"name",label:"Name"},{id:"phoneNumber",label:"Phone Number"},{id:"lastCall",label:"Last Call"},{id:"campaign",label:"Campaign"},{id:"createdAt",label:"Created At"},{id:"timeZone",label:"Time Zone"},{id:"source",label:"Source"},{id:"addedBy",label:"Added By"}],[eh,em]=(0,q.useState)(eu),[ef,eg]=(0,q.useState)(!1),[ex,ep]=(0,q.useState)([]),[eb,ev]=(0,q.useState)(!1),[ey,ew]=(0,q.useState)(null),[ej,eN]=(0,q.useState)({}),[ek,eC]=(0,q.useState)(null),eS=(0,q.useRef)(null),[eE,eD]=(0,q.useState)(!1),[eA,eM]=(0,q.useState)(!1),[eR,eO]=(0,q.useState)(null),[eT,eI]=(0,q.useState)([]),[e$,eL]=(0,q.useState)(!1),[eP,eF]=(0,q.useState)("now"),[e_,eB]=(0,q.useState)(null),[eq,eH]=(0,q.useState)(!1),[eW,eV]=(0,q.useState)(!1),[eX,eY]=(0,q.useState)(null),[eG,eK]=(0,q.useState)(""),[eZ,eJ]=(0,q.useState)(null),[eQ,e0]=(0,q.useState)(!1),[e1,e2]=(0,q.useState)([]),[e4,e5]=(0,q.useState)("name"),[e3,e6]=(0,q.useState)([]),[e8,e7]=(0,q.useState)(null),[e9,te]=(0,q.useState)(!0),[tt,tr]=(0,q.useState)(!1),[tn,ta]=(0,q.useState)(""),ts=Intl.DateTimeFormat().resolvedOptions().timeZone,ti=async(e=1,t)=>{try{1===e?te(!0):en(!0);let r=t?`&search=${encodeURIComponent(t)}`:"",n=`&filterType=${e4}`,l="all"!==a&&"unknown"!==a?`&campaignId=${a}`:"unknown"===a?"&noCampaign=true":"",s=await fetch(`http://localhost:4000/api/contacts?page=${e}&limit=20${r}${n}${l}`,{headers:{Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!s.ok)throw Error("Failed to fetch contacts");let i=await s.json();1===e?e6(i):e6(e=>[...e,...i]),U(20===i.length)}catch(e){console.error("Error loading contacts:",e)}finally{te(!1),en(!1)}},to=async()=>{try{let e=await fetch("http://localhost:4000/api/scheduled-call?page=1&limit=1",{headers:{Authorization:`Bearer ${localStorage.getItem("access_token")}`}});if(!e.ok)throw Error("Failed to fetch scheduled calls");let t=await e.json();if(t.length>0&&"totalSchedules"in t[0]){let e=Number(t[0].totalSchedules);u(isNaN(e)?0:e)}else u(0)}catch(e){console.error("Error fetching scheduled calls count:",e),u(0)}},tc=async()=>{tr(!0);try{await (0,G.TX)(),await ti()}catch(e){console.error("Error importing contacts:",e)}finally{tr(!1)}},tu=e=>{eO(e),eI([]),eM(!0)},th=e=>{eT.some(t=>t._id===e._id)?eI(eT.filter(t=>t._id!==e._id)):eI([...eT,e])},tm=async e=>{let t;if(eB(null),!e){eB("Please select an agent before scheduling");return}if(!eG){eB("Please select a date and time for the schedule");return}let r=ts;if(eR)t=[{Name:eR.contactName,MobileNumber:eR.phoneNumber.startsWith("+")?eR.phoneNumber:`+${eR.phoneNumber}`}],r=eR.region||ts;else{if(!(eT.length>0))return;t=eT.map(e=>({Name:e.contactName,MobileNumber:e.phoneNumber.startsWith("+")?e.phoneNumber:`+${e.phoneNumber}`})),1===eT.length&&(r=eT[0].region||ts)}eL(!0);try{await (0,G.oz)(e,t,eG,r),await to(),eY({contacts:t,scheduledTime:eG,agentName:h.find(t=>t.id===e)?.name||"Unknown",region:r}),eV(!0),et.o.success("Call scheduled successfully")}catch(t){console.error("Error scheduling call:",t);let e=t instanceof Error?t.message:"Failed to schedule call";e.includes("Insufficient credits")||e.includes("add funds")?et.o.error(e):eB("Failed to schedule call. Please try again.")}finally{eL(!1),eM(!1),eO(null),eI([]),eK("")}},tf=async e=>{let t;if(eB(null),!e){eB("Please select an agent before starting the call");return}if(eR)t=[{Name:eR.contactName,MobileNumber:eR.phoneNumber.startsWith("+")?eR.phoneNumber:`+${eR.phoneNumber}`}];else{if(!(eT.length>0))return;t=eT.map(e=>({Name:e.contactName,MobileNumber:e.phoneNumber.startsWith("+")?e.phoneNumber:`+${e.phoneNumber}`}))}eL(!0);try{let r=await (0,G.G6)(e,t,ts);console.log("Call initiated:",r),et.o.success("Call initiated successfully")}catch(t){console.error("Error calling contact(s):",t);let e=t instanceof Error?t.message:"Failed to initiate call";e.includes("Insufficient credits")||e.includes("add funds")?et.o.error(e):eB("Failed to initiate call. Please try again.")}finally{eL(!1),eM(!1),eO(null),eI([])}},tg=e=>{eJ(e),eD(!0)},tx=async()=>{if(eZ)try{await (0,G.MO)(eZ),await ti(),eD(!1),eJ(null)}catch(e){console.error("Error deleting contact:",e)}},tp=async()=>{try{te(!0),await Promise.all(eT.map(e=>(0,G.MO)(e._id))),await ti(),eI([]),e0(!1)}catch(e){console.error("Error deleting contacts:",e)}finally{te(!1)}},tb=e=>{s===e?c("asc"===o?"desc":"asc"):(i(e),c("asc"))},tv=e=>{ed(t=>({...t,[e]:!t[e]}))},ty=(0,q.useMemo)(()=>s?[...e3].sort((e,t)=>{let r,n;if("name"===s)r=e.contactName.toLowerCase(),n=t.contactName.toLowerCase();else if("phoneNumber"===s)r=e.phoneNumber,n=t.phoneNumber;else if("lastCall"===s)r=e.lastCall?new Date(e.lastCall).getTime():0,n=t.lastCall?new Date(t.lastCall).getTime():0;else if("timeZone"===s)r=e.region?e.region.toLowerCase():"",n=t.region?t.region.toLowerCase():"";else if("campaign"===s)r=e.campaigns&&e.campaigns.length>0?"object"==typeof e.campaigns[0]?e.campaigns[0].name||"":e.campaigns[0]||"":"",n=t.campaigns&&t.campaigns.length>0?"object"==typeof t.campaigns[0]?t.campaigns[0].name||"":t.campaigns[0]||"":"";else if("source"===s)r=e.source?.toLowerCase()||"",n=t.source?.toLowerCase()||"";else if("addedBy"===s)r=e.addedBy?.toLowerCase()||"",n=t.addedBy?.toLowerCase()||"";else{if("createdAt"!==s)return 0;r=new Date(e.createdAt).getTime(),n=new Date(t.createdAt).getTime()}return"asc"===o?r>n?1:r<n?-1:0:r<n?1:r>n?-1:0}):e3,[e3,s,o]),tw=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,q.useMemo)(()=>[...t].filter(e=>null!=e),[...t])}(ez(td),ez(tl,{coordinateGetter:t4})),tj=async()=>{tr(!0),eN({}),eC(null);try{let e=[];for(let t of ex){let r=new FormData;r.append("file",t);let n=await fetch("http://localhost:4000/api/contacts/upload",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:r});if(!n.ok)throw Error(`Failed to import contacts from ${t.name}`);let a=await n.json();e.push({fileName:t.name,...a})}let t={totalFiles:e.length,totalProcessed:e.reduce((e,t)=>e+t.totalProcessed,0),successCount:e.reduce((e,t)=>e+t.successCount,0),errorCount:e.reduce((e,t)=>e+t.errorCount,0),fileResults:e};ew(t),ev(!0),ep([]),eg(!1),await ti()}catch(e){console.error("Import error:",e),eC("Failed to import contacts. your file(s) doesn't have the correct columns structure.")}finally{tr(!1)}};return(0,f.jsx)(f.Fragment,{children:(0,f.jsxs)(Z.default,{children:[(0,f.jsx)(ee.O,{credits:e,threshold:n}),(0,f.jsx)(Q.m,{credits:e,threshold:n}),(0,f.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6",children:[(0,f.jsxs)("div",{className:"flex items-center gap-3 w-full sm:w-auto",children:[(0,f.jsx)("h1",{className:"text-2xl font-semibold",children:"Contacts"}),d>0&&(0,f.jsx)(Y(),{href:"/schedule",className:"flex items-center gap-2 bg-blue-50 text-blue-600 px-3 py-1.5 rounded-full text-sm font-medium hover:bg-blue-100 transition-colors mt-1 dark:bg-gray-250 ",children:(0,f.jsx)("div",{className:"flex items-center",children:e3.length>0&&(0,f.jsxs)("span",{className:"flex items-center text-sm font-medium mr-2 ",children:[(0,f.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Showing ",e3.length," of ",e3[0]?.filteredContacts?`${e3[0].filteredContacts} ${"all"===a?"contacts":"unknown"===a?"contacts (No Campaign)":`contacts in ${e1.find(e=>e._id===a)?.name||"campaign"}`}`:e3[0]?.totalContacts?`${e3[0].totalContacts} contacts`:`${e3.length} contacts`]})})})]}),(0,f.jsxs)("div",{className:"flex flex-wrap gap-2 w-full sm:w-auto",children:[(0,f.jsx)(g.$,{className:"bg-[#383D73] text-white flex items-center gap-2 text-sm sm:text-base flex-1 sm:flex-auto",onClick:tc,disabled:tt,children:tt?(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)("div",{className:"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"}),(0,f.jsx)("span",{className:"whitespace-nowrap",children:"Importing..."})]}):(0,f.jsx)(f.Fragment,{children:(0,f.jsx)("span",{className:"whitespace-nowrap",children:"Import contacts"})})}),(0,f.jsxs)(g.$,{variant:"outline",onClick:()=>eg(!ef),className:"flex items-center gap-2 text-sm sm:text-base flex-1 sm:flex-auto dark:border-gray-300 ",children:[(0,f.jsx)(v.A,{className:"h-4 w-4"}),"Upload Contacts"]}),(0,f.jsx)(Y(),{href:"/contacts/create",className:"flex-1 sm:flex-auto",children:(0,f.jsx)(g.$,{className:"bg-primary hover:bg-primary/90 w-full text-sm sm:text-base",children:"Add Contact"})})]})]}),ef&&(0,f.jsx)("div",{className:"mt-4 mb-4 p-4  border rounded-lg bg-card dark:bg-gray-800/50 animate-in fade-in transition-all duration-600 dark:border-gray-500",children:(0,f.jsxs)("div",{className:"flex gap-6",children:[(0,f.jsx)("div",{className:"flex-1",children:(0,f.jsxs)("label",{htmlFor:"file-upload",className:"border-2 border-dashed rounded-lg hover:border-primary/50 transition-colors block cursor-pointer h-[250px] relative",children:[0===ex.length?(0,f.jsxs)("div",{className:"flex flex-col items-center justify-center h-full gap-3 p-8",children:[(0,f.jsx)(v.A,{className:"h-8 w-8 text-muted-foreground"}),(0,f.jsxs)("div",{className:"flex flex-col items-center gap-1",children:[(0,f.jsx)("span",{className:"text-sm font-medium text-primary",children:"Click to upload"}),(0,f.jsx)("span",{className:"text-sm text-muted-foreground",children:"or drag and drop"})]})]}):(0,f.jsxs)("div",{className:"flex flex-col h-full",children:[(0,f.jsx)("div",{className:"p-4 border-b dark:border-gray-700",children:(0,f.jsxs)("span",{className:"text-sm font-medium text-muted-foreground",children:["Selected Files (",ex.length,")"]})}),(0,f.jsx)("div",{className:"flex-1 overflow-auto p-2 space-y-2",children:ex.map((e,t)=>(0,f.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800/50 rounded-lg",children:[(0,f.jsxs)("div",{className:"flex items-center gap-2",children:[e.name.endsWith(".csv")?(0,f.jsx)(y.A,{className:"h-7 w-7 text-green-600 dark:text-green-500"}):(0,f.jsx)(w.A,{className:"h-7 w-7 text-emerald-600 dark:text-emerald-500"}),(0,f.jsxs)("div",{className:"flex flex-col",children:[(0,f.jsx)("span",{className:"text-sm truncate max-w-[200px]",children:e.name}),(0,f.jsxs)("span",{className:"text-xs text-muted-foreground",children:["(",(e.size/1024).toFixed(1)," KB)"]})]})]}),(0,f.jsx)(g.$,{variant:"ghost",size:"icon",onClick:e=>{e.preventDefault(),ep(e=>e.filter((e,r)=>r!==t)),eS.current&&(eS.current.value="")},className:"h-8 w-8 text-red-500 hover:text-red-600",children:(0,f.jsx)(j.A,{className:"h-4 w-4"})})]},`${e.name}-${t}`))}),(0,f.jsx)("div",{className:"p-3 border-t dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 flex items-center justify-center",children:(0,f.jsx)("span",{className:"text-sm text-muted-foreground",children:"Drop more files here or click to browse"})})]}),(0,f.jsx)("input",{id:"file-upload",type:"file",className:"hidden",accept:".csv,.xlsx,.xls",multiple:!0,ref:eS,onChange:e=>{if(e.target.files?.length){let t=ex.reduce((e,t)=>e+t.size,0),r=Array.from(e.target.files);if(t+r.reduce((e,t)=>e+t.size,0)>0xa00000){alert("File(s) size exceeds 10MB limit. Please select smaller files."),eS.current&&(eS.current.value="");return}ep(e=>[...e,...r])}}})]})}),(0,f.jsxs)("div",{className:"flex-1",children:[(0,f.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"File Requirements"}),(0,f.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Upload a CSV or Excel file with contact details. At minimum include Name and Phone number or mobile. You can check an example by clicking on one of the templates."}),(0,f.jsxs)("div",{className:"flex space-x-2 mt-8",children:[(0,f.jsxs)(g.$,{variant:"outline",className:" justify-start dark:border-gray-500",onClick:()=>{let e=document.createElement("a");e.href="/templates/Orova_Template-csv.csv",e.download="orova-template.csv",document.body.appendChild(e),e.click(),document.body.removeChild(e)},children:[(0,f.jsx)(k,{className:"h-4 w-4 mr-2"}),"Download CSV Template"]}),(0,f.jsxs)(g.$,{variant:"outline",className:" justify-start dark:border-gray-500 ",onClick:()=>{let e=document.createElement("a");e.href="/templates/Orova_Template-excel.xlsx",e.download="orova-template.xlsx",document.body.appendChild(e),e.click(),document.body.removeChild(e)},children:[(0,f.jsx)(k,{className:"h-4 w-4 mr-2"}),"Download Excel Template"]})]}),ex.length>0&&(0,f.jsxs)("div",{className:"mt-4",children:[Object.entries(ej).map(([e,t])=>(0,f.jsxs)("div",{className:"text-red-500 text-sm mb-2",children:["Error in ",e,": ",t]},e)),ek&&(0,f.jsx)("p",{className:"text-red-500 text-sm mb-2",children:ek}),(0,f.jsx)(g.$,{className:"w-1/3 mt-5 bg-primary",onClick:tj,disabled:tt||Object.keys(ej).length>0,children:tt?(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(C.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Importing..."]}):(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Import ",ex.length," ",1===ex.length?"File":"Files"]})})]})]})]})}),(0,f.jsx)("div",{className:"flex flex-col md:flex-row gap-6",children:(0,f.jsxs)("div",{className:"flex-1 max-w-full",children:[(0,f.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 mb-6",children:[(0,f.jsxs)("form",{onSubmit:e=>{e.preventDefault(),_(1),ti(1,el)},className:"relative flex-1",children:[(0,f.jsx)(S.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,f.jsx)(x.p,{placeholder:"name"===e4?"Search by name or phone...":"Search by campaign...",className:"pl-10 pr-20",value:el,onChange:e=>es(e.target.value)})]}),(0,f.jsxs)("div",{className:"flex gap-2",children:[(0,f.jsxs)(er.l6,{value:a,onValueChange:e=>{l(e),_(1);let t=el?`&search=${encodeURIComponent(el)}`:"",r=`&filterType=${e4}`,n="all"!==e&&"unknown"!==e?`&campaignId=${e}`:"unknown"===e?"&noCampaign=true":"";te(!0),fetch(`http://localhost:4000/api/contacts?page=1&limit=20${t}${r}${n}`,{headers:{Authorization:`Bearer ${localStorage.getItem("access_token")}`}}).then(e=>{if(!e.ok)throw Error("Failed to fetch contacts");return e.json()}).then(e=>{e6(e),U(20===e.length)}).catch(e=>{console.error("Error loading contacts:",e)}).finally(()=>{te(!1)})},children:[(0,f.jsx)(er.bq,{className:"w-full ",children:(0,f.jsx)(er.yv,{placeholder:"Select Campaign"})}),(0,f.jsxs)(er.gC,{children:[(0,f.jsx)(er.eb,{value:"all",children:"All Campaigns"}),e1.map(e=>(0,f.jsx)(er.eb,{value:e._id,children:e.name},e._id)),(0,f.jsx)(er.eb,{value:"unknown",children:"Unknown (No Campaign)"})]})]}),(0,f.jsxs)(g.$,{variant:"outline",onClick:()=>eo(!0),className:"flex items-center gap-2",children:[(0,f.jsx)(E,{className:"h-4 w-4"}),(0,f.jsx)("span",{className:"hidden sm:inline",children:"Columns"})]})]})]}),eT.length>0&&(0,f.jsxs)("div",{className:"flex items-center justify-between mb-4 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800",children:[(0,f.jsxs)("span",{className:"flex items-center text-sm font-medium text-blue-600 dark:text-blue-400 gap-x-3 ml-2",children:[(0,f.jsx)(D.A,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"}),eT.length," contact",1!==eT.length?"s":""," selected"]}),(0,f.jsxs)("div",{className:"flex items-center gap-2",children:[(0,f.jsxs)(g.$,{className:"bg-green-600 text-white flex items-center gap-2 text-sm sm:text-base flex-1 sm:flex-auto",onClick:()=>{eT.length>0&&(eO(null),eM(!0))},disabled:0===eT.length,children:[(0,f.jsx)(b.A,{className:"h-4 w-4"}),(0,f.jsx)("span",{className:"whitespace-nowrap",children:"Call Selected"})]}),(0,f.jsxs)(g.$,{variant:"outline",className:"text-red-600 dark:text-red-400 border-red-200 dark:border-red-800 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-700 dark:hover:text-red-300 transition-colors",onClick:()=>e0(!0),children:[(0,f.jsx)(A.A,{className:"h-4 w-4 mr-2"}),"Delete Selection"]})]})]}),(0,f.jsx)("div",{className:" bg-card rounded-lg border shadow-sm overflow-hidden dark:bg-gray-800 dark:border-gray-700",children:(0,f.jsx)("div",{className:"w-full overflow-x-auto ",children:(0,f.jsxs)(p.XI,{children:[(0,f.jsx)(p.A0,{className:"sticky top-0 bg-card z-10 dark:bg-gray-800 border-b-2 border-gray-300  dark:border-gray-700",children:(0,f.jsxs)(p.Hj,{className:"bg-gray-50 dark:bg-gray-800/90",children:[(0,f.jsx)(p.nd,{className:"w-12 py-3 font-semibold text-gray-700 dark:text-gray-200 ",children:(0,f.jsx)("input",{type:"checkbox",className:"rounded ml-2",checked:e3.length>0&&eT.length===e3.length,onChange:()=>{eT.length===e3.length?eI([]):eI(e3)}})}),eh.map(e=>ec[e.id]&&(0,f.jsx)(p.nd,{className:"font-bold cursor-pointer text-gray-700 dark:text-gray-200",onClick:()=>tb(e.id),children:(0,f.jsxs)("div",{className:"flex items-center",children:[e.label,s===e.id&&(0,f.jsx)("span",{className:"ml-1",children:"asc"===o?"↑":"↓"})]})},e.id)),(0,f.jsx)(p.nd,{className:"font-bold text-gray-700 dark:text-gray-200 text-center",children:"Call"}),(0,f.jsx)(p.nd,{className:"font-bold text-gray-700 dark:text-gray-200 text-center",children:"Actions"})]})}),(0,f.jsxs)(p.BF,{children:[e9&&1===F?(0,f.jsx)(p.Hj,{children:(0,f.jsx)(p.nA,{colSpan:Object.values(ec).filter(Boolean).length+2,className:"h-80 text-center p-0",children:(0,f.jsxs)("div",{className:"flex flex-col items-center justify-center h-full",children:[(0,f.jsx)(C.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,f.jsx)("p",{className:"text-lg font-medium",children:"Loading..."})]})})}):e3.length>0?ty.map(e=>(0,f.jsxs)(p.Hj,{className:"animate-in fade-in slide-in-from-bottom-10 duration-500",children:[(0,f.jsx)(p.nA,{children:(0,f.jsx)("input",{type:"checkbox",className:"rounded ml-2",checked:eT.some(t=>t._id===e._id),onChange:()=>th(e)})}),eh.map(t=>ec[t.id]&&(0,f.jsxs)(p.nA,{children:["customerId"===t.id&&(0,f.jsx)("span",{className:"font-mono text-sm text-black dark:text-white",children:e.customerId||"-"}),"name"===t.id&&(0,f.jsx)("span",{className:"font-medium",children:e.contactName}),"phoneNumber"===t.id&&e.phoneNumber,"lastCall"===t.id&&(null!=e.lastCall&&new Date(e.lastCall).toLocaleString("en-GB",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1})||"-"),"campaign"===t.id&&(0,f.jsxs)("div",{className:"flex flex-wrap gap-2",children:[e.campaigns&&e.campaigns.length>0&&e.campaigns.map((e,t)=>(0,f.jsx)("span",{className:"bg-blue-100 text-blue-600 text-xs font-medium px-2 py-1 rounded-full",children:"object"==typeof e?e.name:e},t)),e.campaignNames&&e.campaignNames.length>0&&e.campaignNames.map((e,t)=>(0,f.jsx)("span",{className:"bg-blue-100 text-blue-600 text-xs font-medium px-2 py-1 rounded-full",children:e},`name-${t}`)),!e.campaigns?.length&&!e.campaignNames?.length&&"-"]}),"createdAt"===t.id&&(0,f.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.createdAt?new Date(e.createdAt).toLocaleString("en-GB",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",hour12:!1}):"-"}),"timeZone"===t.id&&(e.region||"-"),"source"===t.id&&(0,f.jsx)("span",{className:`
                                  inline-flex px-2 py-1 rounded-sm text-xs font-medium
                                  ${"manual"===e.source?"bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400":"file Upload"===e.source?"bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400":"CRM"===e.source?"bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400":" text-gray-700 dark:bg-gray-800 dark:text-gray-400"}
                                `,children:e.source||"-"}),"addedBy"===t.id&&(0,f.jsx)("span",{className:"text-sm text-black font-semibold italic dark:text-gray-100",children:e.addedBy||"-"})]},t.id)),(0,f.jsx)(p.nA,{children:(0,f.jsx)("div",{className:"flex flex-wrap justify-center gap-2",children:(0,f.jsxs)(g.$,{variant:"outline",size:"sm",onClick:()=>tu(e),className:"text-green-600 border-green-200 hover:bg-green-50 hover:text-green-700",children:[(0,f.jsx)(b.A,{className:"h-4 w-4 mr-1"}),"Call"]})})}),(0,f.jsx)(p.nA,{children:(0,f.jsx)("div",{className:"flex justify-center",children:(0,f.jsxs)(K.rI,{children:[(0,f.jsx)(K.ty,{asChild:!0,children:(0,f.jsx)(g.$,{variant:"outline",size:"icon",className:"text-gray-600 hover:bg-gray-100",children:(0,f.jsx)(M.A,{className:"h-4 w-4 dark:text-gray-200"})})}),(0,f.jsxs)(K.SQ,{align:"end",className:"w-28 p-2 bg-white shadow-lg rounded-md",children:[(0,f.jsx)(K._2,{asChild:!0,children:(0,f.jsxs)(Y(),{href:`/history/${encodeURIComponent(e.contactName)}`,className:"flex items-center p-2 rounded-md hover:bg-blue-50 cursor-pointer",children:[(0,f.jsx)(R,{className:"h-4 w-4 mr-2 text-[#383D73]"}),(0,f.jsx)("span",{className:"text-[#383D73]",children:"History"})]})}),(0,f.jsx)(K._2,{asChild:!0,children:(0,f.jsxs)(Y(),{href:`/contacts/edit/${encodeURIComponent(e.contactName)}/${encodeURIComponent(e._id)}`,className:"flex items-center p-2 w-full text-left rounded-md hover:bg-blue-50 cursor-pointer",children:[(0,f.jsx)(O.A,{className:"h-4 w-4 mr-2 text-blue-600"}),(0,f.jsx)("span",{className:"text-blue-600",children:"Edit"})]})}),(0,f.jsx)(K._2,{asChild:!0,children:(0,f.jsxs)("button",{onClick:()=>tg(e._id),className:"flex items-center p-2 w-full text-left rounded-md hover:bg-red-50 cursor-pointer",children:[(0,f.jsx)(A.A,{className:"h-4 w-4 mr-2 text-red-600"}),(0,f.jsx)("span",{className:"text-red-600",children:"Delete"})]})})]})]})})})]},e._id)):(0,f.jsx)(p.Hj,{children:(0,f.jsx)(p.nA,{colSpan:Object.values(ec).filter(Boolean).length+2,className:"h-80 text-center p-0",children:(0,f.jsxs)("div",{className:"flex flex-col items-center justify-center h-full",children:[(0,f.jsx)("p",{className:"text-lg font-medium text-gray-600 dark:text-gray-300 mb-2",children:el?"No contacts found":"No contacts yet"}),(0,f.jsx)("p",{className:"text-sm text-muted-foreground max-w-md text-center",children:el?`No contacts match "${el}"`:"Contacts are created, or you can create them manually"}),!el&&(0,f.jsx)(Y(),{href:"/contacts/create",children:(0,f.jsxs)(g.$,{variant:"outline",className:"mt-4",children:[(0,f.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"Create your first contact"]})})]})})}),B&&(0,f.jsx)(p.Hj,{ref:ea,className:"h-20",children:(0,f.jsx)(p.nA,{colSpan:7,children:(0,f.jsx)("div",{className:"flex items-center justify-center py-4",children:X?(0,f.jsx)(C.A,{className:"h-6 w-6 animate-spin text-primary"}):(0,f.jsx)("div",{className:"h-8"})})})})]})]})})})]})}),(0,f.jsx)(W.Lt,{open:eE,onOpenChange:eD,children:(0,f.jsxs)(W.EO,{children:[(0,f.jsxs)(W.wd,{children:[(0,f.jsx)(W.r7,{children:"Are you sure?"}),(0,f.jsx)(W.$v,{children:"This action cannot be undone. This will permanently delete the contact from your database."})]}),(0,f.jsxs)(W.ck,{children:[(0,f.jsx)(W.Zr,{children:"Cancel"}),(0,f.jsx)(W.Rx,{className:"bg-red-600 hover:bg-red-700 text-white",onClick:tx,children:"Delete"})]})]})}),(0,f.jsx)(W.Lt,{open:eQ,onOpenChange:e0,children:(0,f.jsxs)(W.EO,{children:[(0,f.jsxs)(W.wd,{children:[(0,f.jsx)(W.r7,{children:"Delete Multiple Contacts"}),(0,f.jsxs)(W.$v,{children:["Are you sure you want to delete ",eT.length," contact",1!==eT.length?"s":"","? This action cannot be undone."]})]}),(0,f.jsxs)(W.ck,{children:[(0,f.jsx)(W.Zr,{children:"Cancel"}),(0,f.jsx)(W.Rx,{className:"bg-red-600 hover:bg-red-700 text-white",onClick:tp,children:e9?(0,f.jsxs)("div",{className:"flex items-center gap-2",children:[(0,f.jsx)(C.A,{className:"h-4 w-4 animate-spin"}),"Deleting..."]}):(0,f.jsxs)(f.Fragment,{children:["Delete ",eT.length," Contact",1!==eT.length?"s":""]})})]})]})}),(0,f.jsx)(H.lG,{open:eW,onOpenChange:eV,children:(0,f.jsxs)(H.Cf,{className:"sm:max-w-[425px]",children:[(0,f.jsxs)("div",{className:"flex flex-col items-center pt-6",children:[(0,f.jsx)("div",{className:"h-12 w-12 rounded-full bg-green-100 flex items-center justify-center mb-4",children:(0,f.jsx)(I.A,{className:"h-6 w-6 text-green-600"})}),(0,f.jsx)(H.L3,{className:"text-xl font-semibold text-center mb-2",children:"Call Scheduled Successfully!"}),(0,f.jsx)(H.rr,{className:"text-center mb-6",children:"Your call has been scheduled with the following details:"})]}),(0,f.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-3 mb-6",children:[(0,f.jsxs)("div",{className:"flex items-center gap-2",children:[(0,f.jsx)(z.A,{className:"h-4 w-4 text-gray-500"}),(0,f.jsxs)("div",{className:"flex flex-col",children:[(0,f.jsx)("span",{className:"text-sm",children:new Date(eX?.scheduledTime||"").toLocaleString()}),(0,f.jsxs)("span",{className:"text-xs text-gray-500",children:["Timezone: ",eX?.region||""]})]})]}),(0,f.jsxs)("div",{className:"flex items-center gap-2",children:[(0,f.jsx)($.A,{className:"h-4 w-4 text-gray-500"}),(0,f.jsxs)("span",{className:"text-sm",children:["Agent: ",eX?.agentName]})]}),(0,f.jsxs)("div",{className:"flex items-center gap-2",children:[(0,f.jsx)(D.A,{className:"h-4 w-4 text-gray-500"}),(0,f.jsx)("span",{className:"text-sm",children:eX?.contacts.length===1?eX.contacts[0].Name:`${eX?.contacts.length} contacts`})]})]}),(0,f.jsx)(H.Es,{className:"flex justify-center",children:(0,f.jsxs)(g.$,{className:"w-full flex items-center justify-center gap-2 bg-[#383D73] hover:bg-[#383D73]/90",onClick:()=>{eV(!1),window.location.href="/schedule"},children:["View in Schedule",(0,f.jsx)(L.A,{className:"h-4 w-4"})]})})]})}),(0,f.jsx)(H.lG,{open:ei,onOpenChange:eo,children:(0,f.jsxs)(H.Cf,{className:"sm:max-w-md",children:[(0,f.jsxs)(H.c7,{children:[(0,f.jsx)(H.L3,{children:"Table Columns"}),(0,f.jsx)(H.rr,{children:"Drag to reorder columns. Toggle visibility with the eye icon."})]}),(0,f.jsx)("div",{className:"py-4",children:(0,f.jsx)(tL,{sensors:tw,collisionDetection:eU,onDragEnd:e=>{let{active:t,over:r}=e;t.id!==r.id&&em(e=>{let n=e.findIndex(e=>e.id===t.id),a=e.findIndex(e=>e.id===r.id);return tB(e,n,a)})},children:(0,f.jsx)(tY,{items:eh.map(e=>e.id),strategy:tW,children:(0,f.jsx)("div",{className:"space-y-2 overflow-auto max-h-100",children:eh.map(e=>(0,f.jsx)(t6,{id:e.id,label:e.label,visible:ec[e.id],onToggle:()=>tv(e.id)},e.id))})})})}),(0,f.jsxs)(H.Es,{className:"flex justify-between",children:[(0,f.jsxs)(g.$,{variant:"outline",onClick:()=>em(eu),className:"flex items-center gap-2",children:[(0,f.jsx)(P.A,{className:"h-4 w-4"}),"Reset Order"]}),(0,f.jsx)(g.$,{onClick:()=>eo(!1),children:"Done"})]})]})}),(0,f.jsx)(H.lG,{open:eb,onOpenChange:ev,children:(0,f.jsxs)(H.Cf,{className:"sm:max-w-[800px]",children:[(0,f.jsxs)("div",{className:"flex flex-col items-center pt-6",children:[(0,f.jsx)("div",{className:"h-12 w-12 rounded-full bg-green-100 dark:bg-green-900/20 flex items-center justify-center mb-4",children:(0,f.jsx)(I.A,{className:"h-6 w-6 text-green-600 dark:text-green-500"})}),(0,f.jsx)(H.L3,{className:"text-xl font-semibold text-center mb-2",children:"Files Imported Successfully!"}),(0,f.jsxs)(H.rr,{className:"text-center mb-6",children:[ey?.totalFiles," ",ey?.totalFiles===1?"file":"files"," processed with the following results:"]})]}),(0,f.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 space-y-4 mb-6",children:[(0,f.jsxs)("div",{className:"border-b dark:border-gray-700 pb-3",children:[(0,f.jsx)("h3",{className:"font-medium mb-2",children:"Imported Files:"}),(0,f.jsx)("div",{className:"flex flex-wrap gap-2",children:ey?.fileResults?.map((e,t)=>f.jsxs("div",{className:"flex items-center gap-2 bg-white dark:bg-gray-800 px-3 py-1.5 rounded-full border dark:border-gray-700",children:[e.fileName.endsWith(".csv")?f.jsx(y.A,{className:"h-4 w-4 text-green-600"}):f.jsx(w.A,{className:"h-4 w-4 text-emerald-600"}),f.jsx("span",{className:"text-sm",children:e.fileName})]},t))})]}),(0,f.jsxs)("div",{className:"border-b dark:border-gray-700 pb-3",children:[(0,f.jsx)("h3",{className:"font-medium mb-2",children:"Results Summary:"}),(0,f.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[(0,f.jsxs)("div",{children:[(0,f.jsx)("p",{className:"text-muted-foreground",children:"Total Contacts:"}),(0,f.jsx)("p",{className:"font-medium",children:ey?.totalProcessed})]}),(0,f.jsxs)("div",{children:[(0,f.jsx)("p",{className:"text-muted-foreground",children:"Successful:"}),(0,f.jsx)("p",{className:"font-medium text-green-600 dark:text-green-500",children:ey?.successCount})]}),(0,f.jsxs)("div",{children:[(0,f.jsx)("p",{className:"text-muted-foreground",children:"Errors:"}),(0,f.jsx)("p",{className:"font-medium text-red-600 dark:text-red-500",children:ey?.errorCount})]})]})]}),(0,f.jsxs)("div",{className:"overflow-auto max-h-[170px]",children:[(0,f.jsx)("h3",{className:"font-medium mb-2",children:"Imported Contacts:"}),(0,f.jsx)("div",{className:"border dark:border-gray-700 rounded-lg overflow-hidden",children:(0,f.jsxs)("table",{className:"w-full text-sm",children:[(0,f.jsx)("thead",{className:"bg-gray-100 dark:bg-gray-800/90",children:(0,f.jsxs)("tr",{children:[(0,f.jsx)("th",{className:"px-4 py-2 text-left font-medium",children:"Name"}),(0,f.jsx)("th",{className:"px-4 py-2 text-left font-medium",children:"Phone Number"})]})}),(0,f.jsx)("tbody",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:ey?.fileResults?.flatMap(e=>e.successfulContacts?.map((t,r)=>f.jsxs("tr",{className:"bg-white dark:bg-gray-800/50",children:[f.jsx("td",{className:"px-4 py-2",children:t.contactName}),f.jsx("td",{className:"px-4 py-2",children:t.phoneNumber})]},`${e.fileName}-${r}`)))})]})}),(ey?.totalProcessed||0)>50&&(0,f.jsxs)("p",{className:"text-sm text-muted-foreground mt-2 text-center",children:["Showing first 50 contacts of ",ey?.totalProcessed]})]})]}),(0,f.jsx)(H.Es,{children:(0,f.jsx)(g.$,{className:"w-full",onClick:()=>ev(!1),children:"Done"})})]})}),(0,f.jsx)(H.lG,{open:eA,onOpenChange:e=>{eM(e),e||eB(null)},children:(0,f.jsxs)(H.Cf,{className:"sm:max-w-[600px] p-0 overflow-hidden rounded-xl border-0 shadow-xl dark:bg-gray-900",children:[(0,f.jsx)("div",{className:"bg-gray-950 p-4",children:(0,f.jsxs)(H.c7,{className:"pb-2",children:[(0,f.jsxs)("div",{className:"flex justify-between items-center",children:[(0,f.jsx)(H.L3,{className:"text-white text-xl font-medium",children:eR?"Start Call":"Group Call"}),(0,f.jsx)(H.HM,{asChild:!0,children:(0,f.jsx)("button",{className:"absolute top-4 right-4 text-white hover:bg-blue-900 rounded-full p-2",children:(0,f.jsx)(j.A,{className:"h-5 w-5"})})})]}),(0,f.jsx)(H.rr,{className:"text-white/90 text-lg",children:eR?`Connect with ${eR.contactName}`:`Connect with ${eT.length} contacts`})]})}),(0,f.jsxs)("div",{className:"p-5",children:[eR?(0,f.jsxs)("div",{className:"flex items-center gap-4 mb-6 bg-green-50 p-4 rounded-lg dark:bg-green-900/20",children:[(0,f.jsx)(V.eu,{className:"h-16 w-16 border-2 border-green-500",children:(0,f.jsx)(V.q5,{className:"bg-green-100 dark:bg-green-900 dark:text-green-400 text-green-600 text-lg",children:eR.contactName.charAt(0)})}),(0,f.jsxs)("div",{children:[(0,f.jsx)("p",{className:"text-lg font-semibold dark:text-white",children:eR.contactName}),(0,f.jsx)("p",{className:"text-sm text-muted-foreground ",children:eR.phoneNumber})]})]}):(0,f.jsxs)("div",{className:"mb-6 bg-green-50 p-4 rounded-lg dark:bg-green-900/20",children:[(0,f.jsx)("p",{className:"font-medium mb-2 text-green-800 dark:text-green-400",children:"Selected contacts:"}),(0,f.jsx)("div",{className:"max-h-40 overflow-y-auto scrollbar-thin scrollbar-thumb-green-600 scrollbar-track-green-300",children:(0,f.jsx)("ul",{className:"space-y-2",children:eT.map(e=>(0,f.jsx)("li",{className:"flex items-center justify-between",children:(0,f.jsxs)("div",{className:"flex items-center",children:[(0,f.jsx)(V.eu,{className:"h-8 w-8 mr-2",children:(0,f.jsx)(V.q5,{className:"bg-green-100 text-green-600 text-xs",children:e.contactName.charAt(0)})}),(0,f.jsxs)("div",{children:[(0,f.jsx)("p",{className:"text-sm font-medium",children:e.contactName}),(0,f.jsx)("p",{className:"text-xs text-muted-foreground",children:e.phoneNumber})]})]})},e._id))})})]}),(0,f.jsxs)("div",{className:"flex gap-4 mb-4",children:[(0,f.jsx)(g.$,{variant:"now"===eP?"default":"outline",onClick:()=>eF("now"),className:"flex-1 dark:border-gray-700",children:"Call Now"}),(0,f.jsx)(g.$,{variant:"schedule"===eP?"default":"outline",onClick:()=>eF("schedule"),className:"flex-1 dark:border-gray-700",children:"Schedule Call"})]}),"schedule"===eP&&(0,f.jsxs)("div",{className:"mb-4",children:[(0,f.jsx)("label",{className:"block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300",children:"Select Date & Time:"}),(0,f.jsxs)("div",{className:"relative flex items-center",children:[(0,f.jsx)("input",{type:"datetime-local",value:eG,onChange:e=>eK(e.target.value),min:(()=>{let e=new Date;return e.setMinutes(e.getMinutes()-e.getTimezoneOffset()),e.toISOString().slice(0,16)})(),className:"border dark:border-gray-700 dark:bg-gray-800 dark:text-white p-2 rounded w-full",ref:N}),(0,f.jsx)("div",{className:"absolute right-0 top-0 h-full flex items-center gap-2 pr-2",children:eq?(0,f.jsx)(g.$,{size:"sm",className:"bg-green-600 hover:bg-green-700 text-white h-8",onClick:()=>{N.current?.blur(),eH(!1)},children:"Save"}):(0,f.jsx)(g.$,{size:"sm",variant:"outline",className:"h-8",onClick:()=>{N.current?.showPicker(),eH(!0)},children:"Set Date"})})]}),eG&&!eq&&(0,f.jsxs)("p",{className:"text-sm text-green-600 mt-1",children:["Scheduled: ",new Date(eG).toLocaleString()]})]}),(0,f.jsxs)("div",{className:"mb-4",children:[(0,f.jsx)("h3",{className:"text-sm font-medium mb-2 dark:text-gray-200",children:"Select an agent for this call:"}),(0,f.jsx)("div",{className:"max-h-[230px] overflow-y-auto pr-2",style:{scrollbarWidth:"thin"},children:(0,f.jsx)("div",{className:"grid grid-cols-2 gap-3",children:h.map(e=>(0,f.jsxs)("div",{onClick:()=>ta(e.id),className:`border rounded-lg p-3 flex items-center gap-2 cursor-pointer transition-all ${tn===e.id?"border-green-500 bg-green-50 dark:bg-green-900/20":"border-gray-200 dark:border-gray-700"} hover:border-green-300 dark:hover:border-green-600`,children:[(0,f.jsxs)(V.eu,{className:"h-10 w-10 flex-shrink-0",children:[(0,f.jsx)(V.BK,{src:e.avatar,alt:e.name}),(0,f.jsx)(V.q5,{className:"bg-gray-100",children:e.name.charAt(0)})]}),(0,f.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,f.jsx)("p",{className:"text-sm font-medium truncate",children:e.name}),(0,f.jsx)("p",{className:"text-xs text-muted-foreground",children:e.role})]}),tn===e.id&&(0,f.jsx)(I.A,{className:"h-5 w-5 text-green-500 flex-shrink-0"})]},e.id))})})]})]}),e_&&(0,f.jsx)("div",{className:"px-5 mb-4",children:(0,f.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-2 rounded-md text-sm",children:e_})}),(0,f.jsxs)(H.Es,{className:"flex flex-col sm:flex-row gap-2 p-4 bg-gray-50 dark:bg-gray-800/50 border-t dark:border-gray-700",children:[(0,f.jsx)(H.HM,{asChild:!0,children:(0,f.jsx)(g.$,{variant:"outline",className:"w-full sm:w-auto",children:"Cancel"})}),"now"===eP?(0,f.jsx)(g.$,{className:"w-full sm:w-auto bg-green-600 hover:bg-green-700",onClick:()=>tf(tn),disabled:e$||!t,children:e$?"Connecting...":(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(b.A,{className:"h-4 w-4 mr-2"})," Start Call"]})}):(0,f.jsx)(g.$,{className:"w-full sm:w-auto bg-green-600 hover:bg-green-700",onClick:()=>tm(tn),disabled:e$||eq||!t,children:e$?"Scheduling...":"Schedule Call"})]})]})})]})})}let t6=({id:e,label:t,visible:r,onToggle:n})=>{let{attributes:l,listeners:s,setNodeRef:i,transform:o,transition:c}=function(e){var t,r,n,l;let{animateLayoutChanges:s=tK,attributes:i,disabled:o,data:c,getNewIndex:d=tG,id:u,strategy:h,resizeObserverConfig:m,transition:f=tZ}=e,{items:g,containerId:x,activeIndex:p,disabled:b,disableTransforms:v,sortedRects:y,overIndex:w,useDragOverlay:j,strategy:N}=(0,q.useContext)(tX),k=(t=o,r=b,"boolean"==typeof t?{draggable:t,droppable:!1}:{draggable:null!=(n=null==t?void 0:t.draggable)?n:r.draggable,droppable:null!=(l=null==t?void 0:t.droppable)?l:r.droppable}),C=g.indexOf(u),S=(0,q.useMemo)(()=>({sortable:{containerId:x,index:C,items:g},...c}),[x,c,C,g]),E=(0,q.useMemo)(()=>g.slice(g.indexOf(u)),[g,u]),{rect:D,node:A,isOver:M,setNodeRef:R}=function(e){let{data:t,disabled:r=!1,id:n,resizeObserverConfig:l}=e,s=ev("Droppable"),{active:i,dispatch:o,over:c,measureDroppableContainers:d}=(0,q.useContext)(tR),u=(0,q.useRef)({disabled:r}),h=(0,q.useRef)(!1),m=(0,q.useRef)(null),f=(0,q.useRef)(null),{disabled:g,updateMeasurementsFor:x,timeout:p}={...t_,...l},b=ef(null!=x?x:n),v=tb({callback:(0,q.useCallback)(()=>{if(!h.current){h.current=!0;return}null!=f.current&&clearTimeout(f.current),f.current=setTimeout(()=>{d(Array.isArray(b.current)?b.current:[b.current]),f.current=null},p)},[p]),disabled:g||!i}),[y,w]=ex((0,q.useCallback)((e,t)=>{v&&(t&&(v.unobserve(t),h.current=!1),e&&v.observe(e))},[v])),j=ef(t);return(0,q.useEffect)(()=>{v&&y.current&&(v.disconnect(),h.current=!1,v.observe(y.current))},[y,v]),(0,q.useEffect)(()=>(o({type:a.RegisterDroppable,element:{id:n,key:s,disabled:r,node:y,rect:m,data:j}}),()=>o({type:a.UnregisterDroppable,key:s,id:n})),[n]),(0,q.useEffect)(()=>{r!==u.current.disabled&&(o({type:a.SetDroppableDisabled,id:n,key:s,disabled:r}),u.current.disabled=r)},[n,s,r,o]),{active:i,rect:m,isOver:(null==c?void 0:c.id)===n,node:y,over:c,setNodeRef:w}}({id:u,data:S,disabled:k.droppable,resizeObserverConfig:{updateMeasurementsFor:E,...m}}),{active:O,activatorEvent:T,activeNodeRect:I,attributes:z,setNodeRef:$,listeners:L,isDragging:P,over:F,setActivatorNodeRef:_,transform:B}=function(e){let{id:t,data:r,disabled:n=!1,attributes:a}=e,l=ev("Draggable"),{activators:s,activatorEvent:i,active:o,activeNodeRect:c,ariaDescribedById:d,draggableNodes:u,over:h}=(0,q.useContext)(tR),{role:m=tF,roleDescription:f="draggable",tabIndex:g=0}=null!=a?a:{},x=(null==o?void 0:o.id)===t,p=(0,q.useContext)(x?t$:tP),[b,v]=ex(),[y,w]=ex(),j=(0,q.useMemo)(()=>s.reduce((e,r)=>{let{eventName:n,handler:a}=r;return e[n]=e=>{a(e,t)},e},{}),[s,t]),N=ef(r);return eh(()=>(u.set(t,{id:t,key:l,node:b,activatorNode:y,data:N}),()=>{let e=u.get(t);e&&e.key===l&&u.delete(t)}),[u,t]),{active:o,activatorEvent:i,activeNodeRect:c,attributes:(0,q.useMemo)(()=>({role:m,tabIndex:g,"aria-disabled":n,"aria-pressed":!!x&&m===tF||void 0,"aria-roledescription":f,"aria-describedby":d.draggable}),[n,m,g,x,f,d.draggable]),isDragging:x,listeners:n?void 0:j,node:b,over:h,setNodeRef:v,setActivatorNodeRef:w,transform:p}}({id:u,data:S,attributes:{...t0,...i},disabled:k.draggable}),U=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,q.useMemo)(()=>e=>{t.forEach(t=>t(e))},t)}(R,$),H=!!O,W=H&&!v&&tq(p)&&tq(w),V=!j&&P,X=V&&W?B:null,Y=W?null!=X?X:(null!=h?h:N)({rects:y,activeNodeRect:I,activeIndex:p,overIndex:w,index:C}):null,G=tq(p)&&tq(w)?d({id:u,items:g,activeIndex:p,overIndex:w}):C,K=null==O?void 0:O.id,Z=(0,q.useRef)({activeId:K,items:g,newIndex:G,containerId:x}),J=g!==Z.current.items,Q=s({active:O,containerId:x,isDragging:P,isSorting:H,id:u,index:C,items:g,newIndex:Z.current.newIndex,previousItems:Z.current.items,previousContainerId:Z.current.containerId,transition:f,wasDragging:null!=Z.current.activeId}),ee=function(e){let{disabled:t,index:r,node:n,rect:a}=e,[l,s]=(0,q.useState)(null),i=(0,q.useRef)(r);return eh(()=>{if(!t&&r!==i.current&&n.current){let e=a.current;if(e){let t=eG(n.current,{ignoreTransform:!0}),r={x:e.left-t.left,y:e.top-t.top,scaleX:e.width/t.width,scaleY:e.height/t.height};(r.x||r.y)&&s(r)}}r!==i.current&&(i.current=r)},[t,r,n,a]),(0,q.useEffect)(()=>{l&&s(null)},[l]),l}({disabled:!Q,index:C,node:A,rect:D});return(0,q.useEffect)(()=>{H&&Z.current.newIndex!==G&&(Z.current.newIndex=G),x!==Z.current.containerId&&(Z.current.containerId=x),g!==Z.current.items&&(Z.current.items=g)},[H,G,x,g]),(0,q.useEffect)(()=>{if(K===Z.current.activeId)return;if(null!=K&&null==Z.current.activeId){Z.current.activeId=K;return}let e=setTimeout(()=>{Z.current.activeId=K},50);return()=>clearTimeout(e)},[K]),{active:O,activeIndex:p,attributes:z,data:S,rect:D,index:C,newIndex:G,items:g,isOver:M,isSorting:H,isDragging:P,listeners:L,node:A,overIndex:w,over:F,setNodeRef:U,setActivatorNodeRef:_,setDroppableNodeRef:R,setDraggableNodeRef:$,transform:null!=ee?ee:Y,transition:ee||J&&Z.current.newIndex===C?tQ:(!V||eN(T))&&f&&(H||Q)?eC.Transition.toString({...f,property:tJ}):void 0}}({id:e}),d={transform:eC.Transform.toString(o),transition:c};return(0,f.jsxs)("div",{ref:i,style:d,className:"flex items-center justify-between p-3 bg-card border rounded-lg",children:[(0,f.jsxs)("div",{className:"flex items-center gap-3",children:[(0,f.jsx)("button",{className:"cursor-grab text-muted-foreground",...l,...s,children:(0,f.jsx)(F,{className:"h-5 w-5"})}),(0,f.jsx)("span",{className:"font-medium",children:t})]}),(0,f.jsx)(g.$,{variant:"ghost",size:"icon",onClick:n,className:r?"text-primary":"text-muted-foreground",children:r?(0,f.jsx)(_.A,{className:"h-5 w-5"}):(0,f.jsx)(B.A,{className:"h-5 w-5"})})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},22383:(e,t,r)=>{Promise.resolve().then(r.bind(r,43654))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},37911:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("FileSpreadsheet",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M8 13h2",key:"yr2amv"}],["path",{d:"M14 13h2",key:"un5t4a"}],["path",{d:"M8 17h2",key:"2yhykz"}],["path",{d:"M14 17h2",key:"10kma7"}]])},41635:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var n=r(37413),a=r(43654);function l(){return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(a.default,{})})}},43654:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\app\\\\(workspace)\\\\contacts\\\\ContactsContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\ContactsContent.tsx","default")},48340:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63503:(e,t,r)=>{"use strict";r.d(t,{Cf:()=>h,Es:()=>f,HM:()=>d,L3:()=>g,c7:()=>m,lG:()=>i,rr:()=>x,zM:()=>o});var n=r(60687);r(43210);var a=r(26134),l=r(11860),s=r(4780);function i({...e}){return(0,n.jsx)(a.bL,{"data-slot":"dialog",...e})}function o({...e}){return(0,n.jsx)(a.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,n.jsx)(a.ZL,{"data-slot":"dialog-portal",...e})}function d({...e}){return(0,n.jsx)(a.bm,{"data-slot":"dialog-close",...e})}function u({className:e,...t}){return(0,n.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,s.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-650 bg-black/50",e),...t})}function h({className:e,children:t,...r}){return(0,n.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,n.jsx)(u,{}),(0,n.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,s.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-650 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...r,children:[t,(0,n.jsxs)(a.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,n.jsx)(l.A,{}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"dialog-header",className:(0,s.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function f({className:e,...t}){return(0,n.jsx)("div",{"data-slot":"dialog-footer",className:(0,s.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function g({className:e,...t}){return(0,n.jsx)(a.hE,{"data-slot":"dialog-title",className:(0,s.cn)("text-lg leading-none font-semibold",e),...t})}function x({className:e,...t}){return(0,n.jsx)(a.VY,{"data-slot":"dialog-description",className:(0,s.cn)("text-muted-foreground text-sm",e),...t})}},70334:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89757:(e,t,r)=>{"use strict";r.d(t,{G6:()=>d,MO:()=>o,SQ:()=>i,TX:()=>c,oe:()=>a,oz:()=>u,to:()=>l,vY:()=>s});let n="http://localhost:4000";async function a(){try{let e=localStorage.getItem("access_token");if(!e)return console.error("No access token available"),[];let t=await fetch(`${n}/api/contacts`,{headers:{Authorization:`Bearer ${e}`}});if(!t.ok)throw Error("Failed to fetch contacts");return await t.json()}catch(e){throw console.error("Error fetching contacts:",e),e}}async function l(){try{let e=localStorage.getItem("access_token");if(!e)return console.error("No access token available"),[];let t=await fetch(`${n}/api/campaigns`,{headers:{Authorization:`Bearer ${e}`}});if(!t.ok)throw Error("Failed to fetch contacts");return await t.json()}catch(e){throw console.error("Error fetching contacts:",e),e}}async function s(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");let r=await fetch(`${n}/api/contacts`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify(e)});if(!r.ok)throw Error("Failed to create contact");return await r.json()}catch(e){throw console.error("Error creating contact:",e),e}}async function i(e){let t=localStorage.getItem("access_token");if(!t)throw Error("No access token available");let r=await fetch("http://localhost:4000/api/contacts",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify(e)});if(!r.ok)throw Error((await r.json()).error||`Failed to create contact: ${r.statusText}`);return r.json()}async function o(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");if(!(await fetch(`${n}/api/contacts/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`}})).ok)throw Error("Failed to delete contact")}catch(e){throw console.error("Error deleting contact:",e),e}}async function c(){try{let e=localStorage.getItem("access_token");if(!e)throw console.error("No access token available"),Error("No access token available");let t=await fetch(`${n}/api/contacts/import-contacts`,{headers:{Authorization:`Bearer ${e}`}});if(!t.ok)throw Error("Failed to import contacts");await t.json()}catch(e){throw console.error("Error importing contacts:",e),e}}async function d(e,t,r){try{let a=localStorage.getItem("access_token");if(!a)throw console.error("No access token available"),Error("No access token available");let l=await fetch(`${n}/api/vapi/call-contacts`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a}`},body:JSON.stringify({agentId:e,contacts:t,region:r})});if(402===l.status){let e=await l.json();throw Error(e.error||"Insufficient credits to make this call. Please add funds to your account.")}if(!l.ok)throw Error("Failed to start call");return await l.json()}catch(e){throw console.error("Error calling contact(s):",e),e}}async function u(e,t,r,a){try{let l=localStorage.getItem("access_token");if(!l)throw console.error("No access token available"),Error("No access token available");let s=await fetch(`${n}/api/scheduled-call`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${l}`},body:JSON.stringify({agentId:e,contacts:t,scheduledTime:r,region:a})});if(402===s.status){let e=await s.json();throw Error(e.error||"Insufficient credits to schedule this call. Please add funds to your account.")}if(!s.ok)throw Error("Failed to schedule call");return await s.json()}catch(e){throw console.error("Error scheduling call:",e),e}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96474:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},99948:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>s.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>h,tree:()=>c});var n=r(65239),a=r(48088),l=r(88170),s=r.n(l),i=r(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let c={children:["",{children:["(workspace)",{children:["contacts",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,41635)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,50184)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},h=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(workspace)/contacts/page",pathname:"/contacts",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[287,9176,7674,5814,598,5188,6034,9677,1476,4772,8021],()=>r(99948));module.exports=n})();