(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9694],{2488:(e,r,a)=>{"use strict";a.d(r,{default:()=>n});var t=a(95155),s=a(226);function n(e){let{children:r,duration:a=.5,delay:n=0,direction:l="up",distance:i=30,className:c="",once:o=!0,viewOffset:d=.1}=e,h=0,m=0;return"up"===l&&(h=i),"down"===l&&(h=-i),"left"===l&&(m=i),"right"===l&&(m=-i),(0,t.jsx)(s.P.div,{initial:{y:h,x:m,opacity:0},whileInView:{y:0,x:0,opacity:1},transition:{duration:a,delay:n,ease:"easeOut"},viewport:{once:o,amount:d},className:c,children:r})}},11302:(e,r,a)=>{"use strict";a.d(r,{default:()=>n});var t=a(95155),s=a(226);function n(e){let{children:r,duration:a=.5,delay:n=0,initialScale:l=.9,className:i="",once:c=!0,viewOffset:o=.1}=e;return(0,t.jsx)(s.P.div,{initial:{scale:l,opacity:0},whileInView:{scale:1,opacity:1},transition:{duration:a,delay:n,ease:"easeOut"},viewport:{once:c,amount:o},className:i,children:r})}},12421:(e,r,a)=>{"use strict";a.d(r,{t:()=>s});var t=a(57297);async function s(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=localStorage.getItem("access_token");if(!a){let e=await (0,t.J1)();if(!e.success)throw Error("No authentication token available");a=e.newAccessToken}let s=new Headers(r.headers||{});s.has("Authorization")||s.set("Authorization","Bearer ".concat(a));let n=await fetch(e,{...r,headers:s});if(401===n.status||403===n.status){console.log("Token expired, attempting refresh...");let a=await (0,t.J1)();if(!a.success)throw console.error("Token refresh failed"),window.location.href="/login",Error("Authentication failed");console.log("Token refreshed, retrying request...");let s=new Headers(r.headers||{});return s.set("Authorization","Bearer ".concat(a.newAccessToken)),fetch(e,{...r,headers:s})}return n}},17923:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>W});var t=a(95155),s=a(12115),n=a(6874),l=a.n(n),i=a(66766),c=a(35695),o=a(51362),d=a(73783),h=a(25657),m=a(46697),u=a(23323),x=a(69074),g=a(49376),f=a(81497),y=a(381),A=a(17580),p=a(39785),b=a(23227),j=a(51154),w=a(62098),k=a(93509);let v={src:"/_next/static/media/OROVA-PURPLE.2cb479d5.png",height:118,width:724,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAABCAMAAADU3h9xAAAACVBMVEUyL1drYoAyLlUe8VZxAAAAA3RSTlNyzJlDn8tCAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAEUlEQVR4nGNgZGBgYmBgYAAAABsABEKIhOgAAAAASUVORK5CYII=",blurWidth:8,blurHeight:1};var N=a(77107),S=a(91394),I=a(30285),_=a(44838),B=a(71007),C=a(34835),E=a(34477);let L=(0,E.createServerReference)("00bd6ed5ddcea8d6a33b0b0c1e7ed159ce451d0d84",E.callServer,void 0,E.findSourceMapURL,"logoutUser");function R(e){let{user:r}=e,[a,n]=(0,s.useState)(!1),i=(0,c.useRouter)(),o=async()=>{try{n(!0);let e=await L();localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user_data"),e.success||console.error("Logout failed:",e.message),i.push("/login")}catch(e){console.error("Error during logout:",e),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user_data"),i.push("/login")}finally{n(!1)}};return(0,t.jsxs)(_.rI,{children:[(0,t.jsx)(_.ty,{asChild:!0,children:(0,t.jsx)(I.$,{variant:"ghost",className:"relative h-9 w-9 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700",children:(0,t.jsxs)(S.eu,{className:"h-9 w-9",children:[(0,t.jsx)(S.BK,{src:r.avatar,alt:r.name}),(0,t.jsxs)(S.q5,{children:[r.name[0].toUpperCase()," "]})]})})}),(0,t.jsxs)(_.SQ,{className:"w-56 z-600",align:"end",forceMount:!0,children:[(0,t.jsx)(_.lp,{className:"font-normal",children:(0,t.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,t.jsx)("p",{className:"text-sm font-medium leading-none dark:text-gray-100",children:r.fullName}),(0,t.jsx)("p",{className:"text-xs leading-none text-gray-500 dark:text-gray-400",children:r.email})]})}),(0,t.jsx)(_.mB,{}),(0,t.jsxs)(_.I,{children:[(0,t.jsx)(l(),{href:"/profile",children:(0,t.jsxs)(_._2,{className:"cursor-pointer",children:[(0,t.jsx)(B.A,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Profile"})]})}),(0,t.jsx)(l(),{href:"/settings",children:(0,t.jsxs)(_._2,{className:"cursor-pointer",children:[(0,t.jsx)(y.A,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:"Settings"})]})})]}),(0,t.jsx)(_.mB,{}),(0,t.jsxs)(_._2,{className:"cursor-pointer text-red-600 focus:text-red-600",onClick:o,disabled:a,children:[(0,t.jsx)(C.A,{className:"mr-2 h-4 w-4"}),(0,t.jsx)("span",{children:a?"Logging out...":"Logout"})]})]})]})}var U=a(2488),O=a(12421),M=a(57297),T=a(11302),z=a(4672);function V(){let{totalMinutesAvailable:e,monthlyAllowance:r,isLoading:a,totalAvailable:s,organizationCreditThreshold:n,isConnected:i,hasValidData:c,lastSuccessfulFetch:o}=(0,z.I)(),d=c&&s<n,h=c&&s<2*n&&s>=n,m=o&&Date.now()-o>12e4;return(0,t.jsx)(T.default,{delay:.2,children:(0,t.jsx)("div",{className:"mx-1 my-3 transition-all duration-800 ease-in-out delay-1000",children:(0,t.jsxs)("div",{className:"bg-white dark:bg-gray-800 border rounded-lg p-4 transform transition-all duration-500 ease-in-out ".concat(d?"border-red-300 dark:border-red-700 bg-red-50 dark:bg-red-900/10":h?"border-yellow-300 dark:border-yellow-700 bg-white dark:bg-yellow-900/10":"border-gray-200 dark:border-gray-700"),children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("h3",{className:"text-xs font-medium",children:"Credits"}),(!i||m)&&(0,t.jsx)("div",{className:"w-2 h-2 rounded-full bg-orange-400 animate-pulse",title:"Connection issue - data may be outdated"})]}),(d||h)&&(0,t.jsx)("p",{className:" text-xs font-medium ".concat(d?"text-red-500":"text-yellow-500"),children:d?"Low Credits":"Warning"})]}),(0,t.jsxs)("div",{className:"mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,t.jsx)("span",{className:"text-xs ".concat(d?"text-red-600 dark:text-red-400":h?"text-yellow-600 dark:text-yellow-400":"text-green-600 dark:text-green-400"),children:"Minutes Remaining"}),(0,t.jsx)("span",{className:"text-sm font-semibold ".concat(d?"text-red-600 dark:text-red-400":h?"text-yellow-600 dark:text-yellow-400":"text-green-600 dark:text-green-400"),children:a?"...":"".concat(e.toFixed(0)," min")})]}),(0,t.jsx)("div",{className:"h-1.5 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden",children:(0,t.jsx)("div",{className:"h-full rounded-full ".concat(d?"bg-red-500":h?"bg-yellow-500":"bg-green-500"),style:{width:"".concat(Math.min(100,e/Math.max(r,1)*100),"%")}})})]}),(0,t.jsx)(l(),{href:"/billing",className:"block",children:(0,t.jsx)(I.$,{variant:"default",className:"w-full bg-black hover:bg-gray-800 dark:bg-gray-900 dark:hover:bg-gray-800 text-white text-xs py-1 h-8",children:"Recharge"})})]})})})}function W(e){let{children:r}=e,{theme:a,setTheme:n}=(0,o.D)(),[S,I]=(0,s.useState)(!1),[_,B]=(0,s.useState)(!1),C=(0,c.usePathname)(),E=(0,c.useRouter)(),[L,W]=(0,s.useState)(!0),[P,D]=(0,s.useState)(null),[J,H]=(0,s.useState)(!0);(0,s.useEffect)(()=>{(async function(){try{let e=await (0,O.t)("".concat("http://localhost:4000","/api/auth/me"));if(!e.ok){E.push("/login");return}let r=await e.json(),a=r.fullName||r.name,t=r.userId||r._id||r.id,s=r.email,n=r.organizationId;if(t&&s){let e={fullName:a||s.split("@")[0],userId:t,_id:t,email:s,role:r.role||"user",organizationId:n||null};localStorage.setItem("user_data",JSON.stringify(e)),D(e)}else E.push("/login")}catch(e){console.error("Error fetching user data:",e),E.push("/login")}finally{H(!1)}})()},[E]),(0,s.useEffect)(()=>(0,M._f)(50),[]),(0,s.useEffect)(()=>{I(!0)},[]),(0,s.useEffect)(()=>{let e=()=>{window.innerWidth<768?W(!1):W(!0)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let Y=()=>{window.innerWidth<768&&W(!1)},G=P?{fullName:P.fullName||P.email.split("@")[0],name:P.email.split("@")[0],email:P.email,avatar:"",role:P.role}:{fullName:"Loading...",name:"Loading...",email:"",avatar:"",role:""},K=[{title:"Overview",links:[{name:"Dashboard",href:"/dashboard",icon:d.A},{name:"Agents",href:"/agents",icon:h.A},{name:"Campaigns",href:"/campaign",icon:m.A},{name:"History",href:"/history",icon:u.A},{name:"Schedule",href:"/schedule",icon:x.A}]},{title:"Resources",links:[{name:"Brain",href:"/brain",icon:g.A},{name:"Contacts",href:"/contacts",icon:f.A}]}],X={title:"Configuration",links:[{name:"Settings",href:"/settings",icon:y.A,isParent:!0,subLinks:[{name:"General",href:"/settings",icon:y.A},{name:"Users",href:"/users",icon:A.A},{name:"Billing",href:"/billing",icon:p.A},...(null==P?void 0:P.role)==="superadmin"?[{name:"Workspaces",href:"/workspaces",icon:b.A}]:[]]}]};return!S||J?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,t.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,t.jsx)(j.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,t.jsx)("p",{className:"text-lg font-medium",children:"Loading..."})]})}):(0,t.jsx)(z.n,{creditThreshold:1,children:(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,t.jsxs)("div",{className:"fixed inset-y-0 left-0 z-550 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 flex flex-col transition-all duration-500 ease-in-out ".concat(L?"w-64 translate-x-0":"w-16 md:translate-x-0 -translate-x-full"),children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"flex items-center justify-start h-16 px-4 border-b border-gray-200 dark:border-gray-700",children:(0,t.jsx)("div",{className:"flex items-center  ".concat(L?"gap-6 ml-3":"justify-center w-full"),children:(0,t.jsxs)(l(),{href:"/",children:[L&&(0,t.jsx)(T.default,{delay:.2,children:(0,t.jsx)(i.default,{src:"dark"===a?N.A:v,alt:"Orova AI",className:"h-5 w-auto"})}),!L&&(0,t.jsx)(T.default,{delay:.1,children:(0,t.jsx)("div",{className:"h-8 w-8 rounded-full bg-purple-600 flex items-center justify-center text-white font-bold",children:"O"})})]})})})}),(0,t.jsx)("div",{className:"flex-grow overflow-hidden",children:(0,t.jsx)("nav",{className:"px-4 py-4 space-y-6 ".concat(!L&&"px-2"),children:K.map((e,r)=>(0,t.jsxs)("div",{children:[L&&(0,t.jsx)("h3",{className:"text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400 font-semibold mb-2 px-3",children:e.title}),(0,t.jsx)("div",{className:"space-y-1",children:e.links.map(e=>{let r=e.icon,a=C.includes(e.href);return(0,t.jsxs)(l(),{href:e.href,onClick:Y,className:"flex items-center ".concat(L?"px-3":"px-0 justify-center"," py-2 text-sm font-medium rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 gap-3 mb-1 text-[#192c54] dark:text-white transition-all duration-200  ").concat(a?"bg-gray-100 dark:bg-gray-700":""),title:L?"":e.name,children:[(0,t.jsx)(r,{className:"h-5 w-5"}),L&&e.name]},e.name)})})]},r))})}),(0,t.jsxs)("div",{className:"flex-shrink-0 border-t border-gray-200 dark:border-gray-700 px-4 py-4 ".concat(!L&&"px-2"),children:[L&&(0,t.jsx)(V,{}),(0,t.jsx)("div",{className:"space-y-3",children:X.links.map(e=>{var r,a;let s=e.icon,n=C.includes(e.href),i=e.isParent&&(n||(null===(r=e.subLinks)||void 0===r?void 0:r.some(e=>C.includes(e.href))));return(0,t.jsx)("div",{children:e.isParent?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("button",{onClick:()=>B(!_),className:"w-full cursor-pointer flex items-center ".concat(L?"justify-between px-3":"justify-center px-0"," py-2 text-sm font-medium rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 gap-3 mb-1 text-[#192c54] dark:text-white transition-all duration-200  ").concat(i?"bg-gray-100 dark:bg-gray-700":""),title:L?"":e.name,children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)(s,{className:"h-5 w-5"}),L&&e.name]}),L&&(0,t.jsx)("svg",{className:"w-4 h-4 transition-transform duration-300 ease-in-out ".concat(_?"rotate-180":"rotate-0"),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),L&&(0,t.jsx)("div",{className:"ml-8 space-y-1 mt-1 mb-2 overflow-hidden transition-all duration-700 ease-in-out ".concat(_?"max-h-[200px] opacity-100 transform-none":"max-h-0 opacity-0 transform translate-y-2"),children:null===(a=e.subLinks)||void 0===a?void 0:a.map(e=>{let r=e.icon,a=C===e.href;return(0,t.jsxs)(l(),{href:e.href,className:"flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 gap-3 mb-1 text-[#192c54] dark:text-white transition-all duration-300  hover:translate-x-1 hover:scale-[1.02] ".concat(a?"bg-gray-100 dark:bg-gray-700":""),children:[(0,t.jsx)(r,{className:"h-4 w-4"}),e.name]},e.name)})})]}):(0,t.jsxs)(l(),{href:e.href,className:"flex items-center ".concat(L?"px-3":"px-0 justify-center"," py-2 text-sm font-medium rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 gap-3 mb-1 text-[#192c54] dark:text-white transition-all duration-300 hover:translate-x-1 hover:scale-[1.02] ").concat(n?"bg-gray-100 dark:bg-gray-700":""),title:L?"":e.name,children:[(0,t.jsx)(s,{className:"h-5 w-5"}),L&&e.name]})},e.name)})})]})]}),L&&(0,t.jsx)("div",{className:"fixed inset-0 bg-gray-800 bg-opacity-50 z-10 md:hidden",onClick:()=>W(!1)}),(0,t.jsxs)("div",{className:"transition-all duration-300 ".concat(L?"md:pl-60 pl-0":"md:pl-16 pl-0"),children:[(0,t.jsx)("header",{className:"bg-white sticky top-0 z-500 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 w-full",children:(0,t.jsxs)("div",{className:"h-16 px-4 md:px-8 flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("button",{onClick:()=>{W(!L)},className:"p-2 rounded-md cursor-pointer text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white",children:(0,t.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,t.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),(0,t.jsxs)("h2",{className:"text-xl md:text-2xl font-bold text-gray-800 dark:text-gray-100 hidden sm:block",children:["Welcome Back, ",(null==P?void 0:P.fullName)||"Guest"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(R,{user:G}),(0,t.jsx)("div",{className:"h-6 w-px bg-gray-200 dark:bg-gray-700"}),(0,t.jsx)("button",{onClick:()=>{n("dark"===a?"light":"dark")},className:"p-2 rounded-lg text-gray-800 dark:text-white",children:"dark"===a?(0,t.jsx)(w.A,{size:18}):(0,t.jsx)(k.A,{size:18})})]})]})}),(0,t.jsx)(U.default,{children:(0,t.jsx)("main",{className:"p-4 md:p-10",children:r})})]})]})})}},57297:(e,r,a)=>{"use strict";a.d(r,{HW:()=>n,J1:()=>i,_f:()=>l});var t=a(12421);let s="http://localhost:4000";async function n(){try{let e=await (0,t.t)("".concat(s,"/api/auth/me"),{method:"GET"});if(!e.ok)return{success:!1,error:"Error: ".concat(e.status)};let r=await e.json(),a=r.userId||r._id||r.id,n=r.email;if(a&&n)return{success:!0,user:{fullName:r.fullName||n.split("@")[0],userId:a,email:n,role:r.role||"user"}};return{success:!1,error:"Invalid user data received"}}catch(e){return console.error("Error fetching user data:",e),{success:!1,error:"An error occurred while fetching user data"}}}function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,r=setInterval(async()=>{if(localStorage.getItem("access_token"))try{await i()}catch(e){console.error("Background token refresh failed:",e)}},6e4*e);return()=>clearInterval(r)}async function i(){let e=localStorage.getItem("refresh_token");if(!e)return{success:!1};try{let r=await fetch("".concat(s,"/api/auth/refresh"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!r.ok)return{success:!1};let a=await r.json();if(a.access_token)return localStorage.setItem("access_token",a.access_token),{success:!0,newAccessToken:a.access_token};return{success:!1}}catch(e){return console.error("Token refresh error:",e),{success:!1}}}},77107:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t={src:"/_next/static/media/OROVA-WHITE.76096952.png",height:124,width:732,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAABCAMAAADU3h9xAAAAD1BMVEX////Aucj7+/vp5e38/PzbXIb5AAAABXRSTlNs0Yl2dMCoCVsAAAAJcEhZcwAACxMAAAsTAQCanBgAAAARSURBVHicY2BkZmBiYWBgAAAAQAALSpjpiwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:1}},78657:(e,r,a)=>{Promise.resolve().then(a.bind(a,17923))}},e=>{var r=r=>e(e.s=r);e.O(0,[4201,4341,6403,6671,226,6766,6874,424,9778,5079,7526,8441,1684,7358],()=>r(78657)),_N_E=e.O()}]);