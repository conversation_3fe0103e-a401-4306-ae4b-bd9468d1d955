"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4194],{17313:(e,a,l)=>{l.d(a,{Xi:()=>o,av:()=>d,j7:()=>t,tU:()=>n});var s=l(95155);l(12115);var r=l(60704),i=l(59434);function n(e){let{className:a,...l}=e;return(0,s.jsx)(r.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",a),...l})}function t(e){let{className:a,...l}=e;return(0,s.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-1",a),...l})}function o(e){let{className:a,...l}=e;return(0,s.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring inline-flex flex-1 items-center justify-center gap-1.5 rounded-md px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...l})}function d(e){let{className:a,...l}=e;return(0,s.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",a),...l})}},19940:(e,a,l)=>{l.d(a,{f:()=>t});var s=l(26715),r=l(32960),i=l(5041);async function n(e){let a=localStorage.getItem("access_token"),[l,s]=await Promise.all([fetch("".concat("http://localhost:4000","/api/agents/").concat(e),{headers:{Authorization:"Bearer ".concat(a)}}),fetch("".concat("http://localhost:4000","/api/phone-numbers"),{headers:{Authorization:"Bearer ".concat(a)}})]);if(!l.ok||!s.ok)throw Error("Failed to fetch agent data");let[r,i]=await Promise.all([l.json(),s.json()]);return{agent:r,phoneNumbers:i}}function t(e){let a=(0,s.jE)(),{data:l}=(0,r.I)({queryKey:["phone-numbers"],queryFn:async()=>{let e=localStorage.getItem("access_token"),a=await fetch("".concat("http://localhost:4000","/api/phone-numbers"),{headers:{Authorization:"Bearer ".concat(e)}});if(!a.ok)throw Error("Failed to fetch phone numbers");return a.json()},staleTime:6e5,gcTime:9e5}),{data:t,isLoading:o,error:d}=(0,r.I)({queryKey:["agent",e],queryFn:()=>n(e),enabled:!!e,staleTime:6e5,gcTime:9e5}),c=(0,i.n)({mutationFn:async e=>{let a=localStorage.getItem("access_token");if(!a)throw Error("Authentication required");let l=await fetch("".concat("http://localhost:4000","/api/agents/").concat(e.id),{method:"PATCH",headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"},body:JSON.stringify(e)});if(!l.ok)throw Error("Failed to update agent: ".concat(l.status));return l.json()},onMutate:async l=>{await a.cancelQueries({queryKey:["agent",e]}),await a.cancelQueries({queryKey:["agents"]});let s=a.getQueryData(["agent",e]),r=a.getQueryData(["agents"]);return a.setQueryData(["agent",e],e=>({...null!=e?e:{phoneNumbers:[]},agent:l})),a.setQueryData(["agents"],function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.map(e=>e.id===l.id?l:e)}),{previousAgent:s,previousAgents:r}},onSettled:()=>{a.invalidateQueries({queryKey:["agent",e]}),a.invalidateQueries({queryKey:["agents"]})}}),u=(0,i.n)({mutationFn:async e=>{let a=localStorage.getItem("access_token");if(!a)throw Error("Authentication required");let l=await fetch("".concat("http://localhost:4000","/api/agents"),{method:"POST",headers:{Authorization:"Bearer ".concat(a),"Content-Type":"application/json"},body:JSON.stringify(e)});if(!l.ok)throw Error("Failed to create agent: ".concat(l.status));return l.json()},onSuccess:()=>{a.invalidateQueries({queryKey:["agents"]})}}),h=(0,i.n)({mutationFn:async e=>{let a=localStorage.getItem("access_token");if(!a)throw Error("Authentication required");let l=await fetch("".concat("http://localhost:4000","/api/agents/").concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(a)}});if(!l.ok)throw Error("Failed to delete agent: ".concat(l.status))},onSuccess:(e,l)=>{a.removeQueries({queryKey:["agent",l]}),a.setQueryData(["agents"],function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.filter(e=>e.id!==l)})}});return{agent:null==t?void 0:t.agent,setAgent:e=>{e&&c.mutate(e)},phoneNumbers:e?null==t?void 0:t.phoneNumbers:null!=l?l:[],agentIsLoading:o,agentError:d instanceof Error?d.message:null,createAgentMutation:u,updateAgentMutation:c,deleteAgentMutation:h}}},21773:(e,a,l)=>{l.d(a,{A:()=>u});var s=l(95155),r=l(30285),i=l(88539),n=l(66695),t=l(59409),o=l(20690),d=l(12115),c=l(62523);function u(e){var a,l,u;let{agent:h,setAgent:m}=e,[v,x]=(0,d.useState)("customer-service");return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"space-y-6 p-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Welcome Message"}),(0,s.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>{let e=["Hello there! I'm ".concat(h.name||"your friend",", your dedicated ").concat(h.role||"assistant",". I'm here to help with any questions or concerns you might have today."),"Welcome! This is ".concat(h.role||"your assistant",". I'm ready to assist you with anything you need!"),"Hi, thanks for reaching out! I'm ".concat(h.name||"your assitant",", and I'll be your ").concat(h.role||"guide and advisor"," today. How may I help you?")],a=e[Math.floor(Math.random()*e.length)];m({...h,firstMessage:a})},children:[(0,s.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Generate"]})]}),(0,s.jsx)(n.Zp,{className:"p-4",children:(0,s.jsx)(i.T,{className:"min-h-[100px] resize-y",placeholder:"Enter a welcome message that the agent will use to start conversations...",value:(null==h?void 0:h.firstMessage)||"",onChange:e=>{m({...h,firstMessage:e.target.value})}})})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Role Instructions"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(t.l6,{value:v,onValueChange:x,children:[(0,s.jsx)(t.bq,{className:"w-[180px]",children:(0,s.jsx)(t.yv,{placeholder:"Select template"})}),(0,s.jsxs)(t.gC,{children:[(0,s.jsx)(t.eb,{value:"customer-service",children:"Customer Service"}),(0,s.jsx)(t.eb,{value:"sales",children:"Sales Agent"}),(0,s.jsx)(t.eb,{value:"support",children:"Technical Support"})]})]}),(0,s.jsxs)(r.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Generate"]})]})]}),(0,s.jsx)(n.Zp,{className:"p-4",children:(0,s.jsx)(i.T,{id:"systemContent",className:"max-h-[500px] resize-y",placeholder:"Describe the role and responsibilities of this agent...",value:(null==h?void 0:null===(u=h.model)||void 0===u?void 0:null===(l=u.messages)||void 0===l?void 0:null===(a=l.find(e=>"system"===e.role))||void 0===a?void 0:a.content)||"",onChange:e=>{let a;let l=e.target.value,s=h.model||{messages:[]},r=s.messages||[];a=r.find(e=>"system"===e.role)?r.map(e=>"system"===e.role?{...e,content:l}:e):[...r,{role:"system",content:l}],m({...h,model:{...s,messages:a}})}})})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Voicemail Message"}),(0,s.jsx)(n.Zp,{className:"p-4",children:(0,s.jsx)(c.p,{placeholder:"Enter a message to leave when reaching voicemail...",value:(null==h?void 0:h.voicemailMessage)||"",onChange:e=>{m({...h,voicemailMessage:e.target.value})}})})]})]})})}},26083:(e,a,l)=>{l.d(a,{A:()=>d});var s=l(95155),r=l(85057),i=l(59409),n=l(76202),t=l(66695),o=l(12115);function d(e){var a,l,d,c,u,h,m,v;let{agent:x,setAgent:g}=e,[p,b]=(0,o.useState)({provider:(null==x?void 0:null===(a=x.transcriber)||void 0===a?void 0:a.provider)||"",language:(null==x?void 0:null===(l=x.transcriber)||void 0===l?void 0:l.language)||"",model:(null==x?void 0:null===(d=x.transcriber)||void 0===d?void 0:d.model)||"",confidenceThreshold:(null==x?void 0:null===(c=x.transcriber)||void 0===c?void 0:c.confidenceThreshold)||.4}),[j,f]=(0,o.useState)({provider:(null==x?void 0:null===(u=x.voice)||void 0===u?void 0:u.provider)||"11labs",model:(null==x?void 0:null===(h=x.voice)||void 0===h?void 0:h.model)||"eleven_turbo_v2_5",voiceId:(null==x?void 0:null===(m=x.voice)||void 0===m?void 0:m.voiceId)||"",stability:(null==x?void 0:null===(v=x.voice)||void 0===v?void 0:v.stability)||.5}),y=(e,a)=>{let l={...p,[e]:a};if("provider"===e)switch(a){case"deepgram":l.model="nova-3";break;case"elevenlabs":l.model="scribe";break;case"gladia":l.model="fast";break;case"google":l.model="gemini-2.0-flash";break;case"openai":l.model="gpt-4o-transcribe";break;case"speechmatics":l.model="default";break;case"talkscriber":l.model="whisper";break;case"assemblyai":l.model="none";break;default:l.model=""}b(l),g({...x,transcriber:{...x.transcriber,provider:l.provider,model:l.model,language:l.language,confidenceThreshold:l.confidenceThreshold}})},N=(e,a)=>{let l={...j,[e]:a};if("provider"===e)switch(a){case"11labs":l.model="eleven_turbo_v2_5",l.voiceId="N2lVS1w4EtoT3dr4eOWO";break;case"cartesia":l.model="sonic_2",l.voiceId="3b554273-4299-48b9-9aaf-eefd438e3941";break;case"rime":l.model="mist",l.voiceId="gerald";break;case"playht":l.model="playht2_turbo",l.voiceId="melissa";break;case"openai":l.model="tts-1",l.voiceId="alloy";break;case"deepgram":l.model="aura",l.voiceId="asteria";break;default:l.model=""}f(l),g({...x,voice:{...x.voice,[e]:a}})};return(0,s.jsxs)("div",{className:"space-y-6 p-6",children:[(0,s.jsx)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4",children:(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Voice Configuration"})}),(0,s.jsxs)("div",{className:"space-y-4 mt-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Transcriber Settings"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(r.J,{htmlFor:"transcriber-provider",children:"Provider"}),(0,s.jsxs)(i.l6,{value:(null==p?void 0:p.provider)||"",onValueChange:e=>y("provider",e),children:[(0,s.jsx)(i.bq,{id:"transcriber-provider",className:"w-full",children:(0,s.jsx)(i.yv,{placeholder:"Select provider"})}),(0,s.jsxs)(i.gC,{children:[(0,s.jsx)(i.eb,{value:"assemblyai",children:"Assembly AI"}),(0,s.jsx)(i.eb,{value:"deepgram",children:"Deepgram"}),(0,s.jsx)(i.eb,{value:"elevenlabs",children:"Elevenlabs"}),(0,s.jsx)(i.eb,{value:"gladia",children:"Gladia"}),(0,s.jsx)(i.eb,{value:"google",children:"Google"}),(0,s.jsx)(i.eb,{value:"openai",children:"OpenAI"}),(0,s.jsx)(i.eb,{value:"speechmatics",children:"Speechmatics"}),(0,s.jsx)(i.eb,{value:"talkscriber",children:"Talkscriber"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(r.J,{htmlFor:"transcriber-language",children:"Language"}),(0,s.jsxs)(i.l6,{value:(null==p?void 0:p.language)||"en",onValueChange:e=>y("language",e),children:[(0,s.jsx)(i.bq,{id:"transcriber-language",className:"w-full",children:(0,s.jsx)(i.yv,{placeholder:"Select language"})}),(0,s.jsxs)(i.gC,{children:[(0,s.jsx)(i.eb,{value:"en",children:"English"}),(0,s.jsx)(i.eb,{value:"en-US",children:"English (US)"}),(0,s.jsx)(i.eb,{value:"multi",children:"Multi-language"})]})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(r.J,{htmlFor:"transcriber-model",children:"Model"}),(0,s.jsxs)(i.l6,{value:(null==p?void 0:p.model)||"none",onValueChange:e=>y("model",e),disabled:"assemblyai"===p.provider,children:[(0,s.jsx)(i.bq,{id:"transcriber-model",className:"w-full",children:(0,s.jsx)(i.yv,{placeholder:"Select model"})}),(0,s.jsxs)(i.gC,{children:["deepgram"===p.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.eb,{value:"nova-3",children:"Nova 3"}),(0,s.jsx)(i.eb,{value:"nova-3-general",children:"Nova 3 General"}),(0,s.jsx)(i.eb,{value:"nova-3-medical",children:"Nova 3 Medical"}),(0,s.jsx)(i.eb,{value:"nova-2",children:"Nova 2"}),(0,s.jsx)(i.eb,{value:"nova-2-general",children:"Nova 2 General"}),(0,s.jsx)(i.eb,{value:"nova-2-meeting",children:"Nova 2 Meeting"}),(0,s.jsx)(i.eb,{value:"nova-2-phonecall",children:"Nova 2 Phonecall"}),(0,s.jsx)(i.eb,{value:"nova-2-finance",children:"Nova 2 Finance"}),(0,s.jsx)(i.eb,{value:"nova-2-conversational-ai",children:"Nova 2 Conversational AI"}),(0,s.jsx)(i.eb,{value:"nova-2-voicemail",children:"Nova 2 Voicemail"}),(0,s.jsx)(i.eb,{value:"nova-2-video",children:"Nova 2 Video"}),(0,s.jsx)(i.eb,{value:"nova-2-medical",children:"Nova 2 Medical"}),(0,s.jsx)(i.eb,{value:"nova-2-drive-thru",children:"Nova 2 Drive Thru"}),(0,s.jsx)(i.eb,{value:"nova-2-automotive",children:"Nova 2 Automotive"})]}),"elevenlabs"===p.provider&&(0,s.jsx)(i.eb,{value:"scribe",children:"Scribe"}),"gladia"===p.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.eb,{value:"fast",children:"Fast"}),(0,s.jsx)(i.eb,{value:"accurate",children:"Accurate"})]}),"google"===p.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.eb,{value:"gemini-2.0-flash",children:"Gemini 2.0 Flash"}),(0,s.jsx)(i.eb,{value:"gemini-2.0-flash-lite",children:"Gemini 2.0 Flash Lite"}),(0,s.jsx)(i.eb,{value:"gemini-1.5-flash-lite",children:"Gemini 1.5 Flash Lite"}),(0,s.jsx)(i.eb,{value:"gemini-1.5-pro",children:"Gemini 1.5 Pro"})]}),"openai"===p.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.eb,{value:"gpt-4o-transcribe",children:"GPT-4o Transcribe"}),(0,s.jsx)(i.eb,{value:"gpt-4o-mini-transcribe",children:"GPT-4o Mini Transcribe"})]}),"speechmatics"===p.provider&&(0,s.jsx)(i.eb,{value:"default",children:"Default"}),"talkscriber"===p.provider&&(0,s.jsx)(i.eb,{value:"whisper",children:"Whisper"}),"assemblyai"===p.provider&&(0,s.jsx)(i.eb,{value:"none",children:"No models available"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)(r.J,{htmlFor:"confidence-threshold",children:"Confidence Threshold"}),(0,s.jsx)("span",{className:"text-sm text-muted-foreground mt-3",children:p.confidenceThreshold.toFixed(1)})]}),(0,s.jsx)(n.A,{id:"confidence-threshold",value:[p.confidenceThreshold],min:0,max:1,step:.1,className:"w-full",onValueChange:e=>y("confidenceThreshold",e[0])})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Voice Configuration"}),(0,s.jsx)(t.Zp,{className:"p-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(r.J,{htmlFor:"voice-provider",children:"Voice Provider"}),(0,s.jsxs)(i.l6,{value:(null==j?void 0:j.provider)||"",onValueChange:e=>N("provider",e),children:[(0,s.jsx)(i.bq,{id:"voice-provider",className:"w-full",children:(0,s.jsx)(i.yv,{placeholder:"Select provider"})}),(0,s.jsxs)(i.gC,{children:[(0,s.jsx)(i.eb,{value:"11labs",children:"ElevenLabs"}),(0,s.jsx)(i.eb,{value:"cartesia",children:"Cartesia"}),(0,s.jsx)(i.eb,{value:"rime",children:"Rime AI"}),(0,s.jsx)(i.eb,{value:"playht",children:"PlayHT"}),(0,s.jsx)(i.eb,{value:"openai",children:"OpenAI"}),(0,s.jsx)(i.eb,{value:"deepgram",children:"Deepgram"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(r.J,{htmlFor:"voice-model",children:"Voice Model"}),(0,s.jsxs)(i.l6,{value:(null==j?void 0:j.model)||"",onValueChange:e=>N("model",e),children:[(0,s.jsx)(i.bq,{id:"voice-model",className:"w-full",children:(0,s.jsx)(i.yv,{placeholder:"Select model"})}),(0,s.jsxs)(i.gC,{children:["11labs"===j.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.eb,{value:"eleven_turbo_v2_5",children:"Eleven Turbo v2.5"}),(0,s.jsx)(i.eb,{value:"eleven_flash_v2_5",children:"Eleven Flash 2.5"}),(0,s.jsx)(i.eb,{value:"eleven_flash_v2",children:"Eleven Flash V2"}),(0,s.jsx)(i.eb,{value:"eleven_multilingual_v2",children:"Eleven Multilingual v2"}),(0,s.jsx)(i.eb,{value:"eleven_english_v1",children:"Eleven English v1"})]}),"cartesia"===j.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.eb,{value:"sonic_2",children:"Sonic 2"}),(0,s.jsx)(i.eb,{value:"sonic_english",children:"Sonic English"}),(0,s.jsx)(i.eb,{value:"sonic_multilingual",children:"Sonic Multilingual"}),(0,s.jsx)(i.eb,{value:"sonic_preview",children:"Sonic Preview"}),(0,s.jsx)(i.eb,{value:"sonic",children:"Sonic"})]}),"rime"===j.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.eb,{value:"mist",children:"Mist"}),(0,s.jsx)(i.eb,{value:"mistv2",children:"Mist v2"})]}),"playht"===j.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.eb,{value:"playht2_turbo",children:"PlayHT 2.0 Turbo"}),(0,s.jsx)(i.eb,{value:"playht2",children:"PlayHT 2.0"}),(0,s.jsx)(i.eb,{value:"play3_mini",children:"Play 3.0 Mini"}),(0,s.jsx)(i.eb,{value:"playdialog",children:"PlayDialog"})]}),"openai"===j.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.eb,{value:"tts-1",children:"TTS-1"}),(0,s.jsx)(i.eb,{value:"tts-1-hd",children:"TTS-1-HD"}),(0,s.jsx)(i.eb,{value:"gpt-4o-mini-tts",children:"GPT-4o Mini TTS"})]}),"deepgram"===j.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.eb,{value:"aura",children:"Aura"}),(0,s.jsx)(i.eb,{value:"aura_2",children:"Aura 2"})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(r.J,{htmlFor:"voice-id",children:"Voice ID"}),(0,s.jsxs)(i.l6,{value:(null==j?void 0:j.voiceId)||"",onValueChange:e=>N("voiceId",e),children:[(0,s.jsx)(i.bq,{id:"voice-id",className:"w-full",children:(0,s.jsx)(i.yv,{placeholder:"Select voice"})}),(0,s.jsxs)(i.gC,{children:["11labs"===j.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.eb,{value:"N2lVS1w4EtoT3dr4eOWO",children:"Callum"}),(0,s.jsx)(i.eb,{value:"piTKgcLEGmPE4e6mEKli",children:"Nicole"})]}),"cartesia"===j.provider&&(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(i.eb,{value:"3b554273-4299-48b9-9aaf-eefd438e3941",children:"Indian Lady"})}),"rime"===j.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.eb,{value:"gerald",children:"Gerald"}),(0,s.jsx)(i.eb,{value:"gultch",children:"Gultch"}),(0,s.jsx)(i.eb,{value:"rob",children:"Rob"})]}),"playht"===j.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.eb,{value:"melissa",children:"Melissa"}),(0,s.jsx)(i.eb,{value:"davis",children:"Davis"})]}),"openai"===j.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.eb,{value:"alloy",children:"Alloy"}),(0,s.jsx)(i.eb,{value:"shimmer",children:"Shimmer"})]}),"deepgram"===j.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(i.eb,{value:"asteria",children:"Asteria"}),(0,s.jsx)(i.eb,{value:"perseus",children:"Perseus"})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)(r.J,{htmlFor:"stability",children:["Stability: ",j.stability]}),(0,s.jsx)(n.A,{id:"stability",min:0,max:1,step:.1,value:[j.stability],onValueChange:e=>N("stability",e[0])})]})]})})]})]})]})}l(54534)},29281:(e,a,l)=>{l.d(a,{A:()=>r});var s=l(95155);function r(e){let{agent:a,setAgent:l,phoneNumbers:r}=e;return(0,s.jsx)("div",{className:"space-y-6 p-6",children:(0,s.jsx)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4",children:(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Brain Configuration"})})})}},35600:(e,a,l)=>{l.d(a,{G:()=>x});var s=l(95155),r=l(12115),i=l(30285),n=l(54165),t=l(62523),o=l(51154),d=l(19420),c=l(25561),u=l(39365),h=l.n(u),m=l(56671);l(30133);var v=l(59071);function x(e){let{isOpen:a,onClose:l,agent:u}=e,[x,g]=(0,r.useState)({name:"",phoneNumber:""}),[p,b]=(0,r.useState)(!1),[j,f]=(0,r.useState)(null),y=async()=>{if(u){b(!0),f(null);try{let e=x.phoneNumber.replace(/\s+/g,""),a=e.startsWith("+")?e:"+".concat(e),s=!1;try{await (0,c.SQ)({contactName:x.name,phoneNumber:a,campaigns:[]})}catch(e){if(e.message.includes("Conflict")||e.message.includes("already exists"))s=!0;else throw e}let r=(0,v.s)(a)||"",i=[{Name:x.name,MobileNumber:a}];await (0,c.G6)(u.id,i,r),m.o.success(s?"Call initiated with existing contact":"Contact created and call initiated"),l()}catch(e){console.error("Call error:",e),e.message.includes("already exists")?f("Contact already exists with this name and phone number"):f(e instanceof Error?e.message:"Failed to initiate call"),m.o.error("Failed to process request")}finally{b(!1)}}};return(0,s.jsx)(n.lG,{open:a,onOpenChange:e=>{e||(g({name:"",phoneNumber:""}),f(null)),l()},children:(0,s.jsxs)(n.Cf,{className:"sm:max-w-[425px]",children:[(0,s.jsxs)(n.c7,{children:[(0,s.jsx)(n.L3,{children:"Start Phone Call"}),(0,s.jsxs)(n.rr,{children:["Enter your details to start a call with",(0,s.jsx)("span",{className:"font-bold ml-1",children:null==u?void 0:u.name})]})]}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"name",className:"text-sm font-medium",children:"Your Name"}),(0,s.jsx)(t.p,{id:"name",placeholder:"Enter your name",className:"mt-2",value:x.name,onChange:e=>g(a=>({...a,name:e.target.value}))})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"phone",className:"text-sm font-medium",children:"Phone Number"}),(0,s.jsx)(h(),{country:(0,v.u)(),value:x.phoneNumber,onChange:e=>g(a=>({...a,phoneNumber:"+".concat(e)})),containerClass:"mt-2",inputClass:"!w-full !h-10 !pl-[48px] !rounded-md !border !border-input !bg-background !px-3 !py-2 !text-sm !ring-offset-background file:!border-0 file:!bg-transparent file:!text-sm file:!font-medium placeholder:!text-muted-foreground focus-visible:!outline-none focus-visible:!ring-2 focus-visible:!ring-ring focus-visible:!ring-offset-2 disabled:!cursor-not-allowed disabled:!opacity-50",buttonClass:"!border-r-0 !bg-transparent !border !border-input",dropdownClass:"!bg-background !border !border-input",specialLabel:""})]}),j&&(0,s.jsx)("div",{className:"text-sm text-red-500 bg-red-50 dark:bg-red-900/20 p-3 rounded-md",children:j})]}),(0,s.jsxs)(n.Es,{children:[(0,s.jsx)(i.$,{variant:"outline",onClick:l,children:"Cancel"}),(0,s.jsx)(i.$,{onClick:y,disabled:p||!x.name||!x.phoneNumber,className:"bg-green-600 hover:bg-green-700 text-white",children:p?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Initiating..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Start Call"]})})]})]})})}},38816:(e,a,l)=>{l.d(a,{A:()=>f});var s=l(95155),r=l(62523),i=l(85057),n=l(59409),t=l(30285),o=l(12115),d=l(29869),c=l(15273),u=l(81497),h=l(21714),m=l(62209),v=l(83833);l(54534);var x=l(54165),g=l(46102),p=l(35600),b=l(86902);let j="http://localhost:4000";function f(e){var a;let{agent:l,setAgent:f,phoneNumbers:y,isCreateMode:N}=e,[w,A]=(0,o.useState)(!1),[k,C]=(0,o.useState)(!1),[S,F]=(0,o.useState)(null),[E,T]=(0,o.useState)({unit:"%",width:100,height:100,x:0,y:0}),[I,P]=(0,o.useState)(!1),[M,G]=(0,o.useState)(!1),[_,z]=(0,o.useState)(null),[D,L]=(0,o.useState)(null),B=(0,o.useRef)(null),q=(0,o.useRef)(null),Q=(e,a)=>{let l=document.createElement("canvas"),s=e.naturalWidth/e.width,r=e.naturalHeight/e.height;l.width=a.width,l.height=a.height;let i=l.getContext("2d");if(!i)throw Error("No 2d context");return i.fillStyle="#FFFFFF",i.fillRect(0,0,l.width,l.height),i.drawImage(e,a.x*s,a.y*r,a.width*s,a.height*r,0,0,a.width,a.height),new Promise(e=>{l.toBlob(a=>{if(!a)throw Error("Canvas is empty");e(a)},"image/jpeg",.95)})},V=e=>{let a=e.name.split("."),l=a.length>1?a.pop():"",s=a.join("."),r=Math.floor(9e3*Math.random()+1e3);return new File([e],"".concat(s,"_").concat(r,".").concat(l),{type:e.type})},O=async()=>{if(B.current&&D)try{C(!1),A(!0);let e=_||{x:E.x,y:E.y,width:"number"==typeof E.width?E.width:0,height:"number"==typeof E.height?E.height:0,unit:"px"},a=await Q(B.current,e),s=V(new File([a],D.name,{type:"image/jpeg"})),r=localStorage.getItem("access_token");if(!r){console.error("No access token available");return}let i=new FormData;i.append("avatar",s);let n=await fetch("".concat(j,"/api/agents/").concat(l.id,"/avatar"),{method:"POST",headers:{Authorization:"Bearer ".concat(r)},body:i});if(!n.ok)throw Error("Failed to upload avatar: ".concat(n.status));let t=await n.json();f({...l,avatar:"".concat(j).concat(t.avatarPath)})}catch(e){console.error("Error uploading avatar:",e)}finally{A(!1),L(null),F(null),q.current&&(q.current.value="")}},U=e=>{if(e.width!==e.height){let a=Math.min(e.width,e.height);return{...e,width:a,height:a}}return{...e}};return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"space-y-8 p-4 sm:p-6",children:[(0,s.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-8",children:[(0,s.jsxs)("div",{className:"w-full lg:w-1/3 xl:w-1/4 flex flex-col items-center",children:[(0,s.jsxs)("div",{className:"relative cursor-pointer group w-full max-w-[200px]",onClick:()=>{var e;q.current&&(q.current.value=""),null===(e=q.current)||void 0===e||e.click()},children:[(0,s.jsxs)("div",{className:"aspect-square rounded-lg bg-black p-1 overflow-hidden",children:[(null==l?void 0:l.avatar)?(0,s.jsx)("img",{src:l.avatar,alt:"".concat(l.name," avatar"),className:"h-full w-full object-cover"}):(0,s.jsx)("div",{className:"h-full w-full bg-gray-200 flex items-center justify-center text-5xl",children:(null==l?void 0:l.name)?l.name.charAt(0).toUpperCase():"A"}),w&&(0,s.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-70 rounded-lg flex items-center justify-center",children:(0,s.jsx)("div",{className:"animate-spin h-8 w-8 border-4 border-t-transparent border-white rounded-full"})})]}),(0,s.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity",children:(0,s.jsx)(d.A,{className:"h-8 w-8 text-white"})}),(0,s.jsx)("input",{type:"file",ref:q,className:"hidden",accept:"image/jpeg,image/png,image/gif,image/webp",onChange:e=>{var a;let l=null===(a=e.target.files)||void 0===a?void 0:a[0];if(!l)return;if(l.size>2097152){alert("File is too large. Maximum size is 2MB."),q.current&&(q.current.value="");return}L(l),z(null),T({unit:"%",width:100,height:100,x:0,y:0});let s=new FileReader;s.onload=()=>{F(s.result),C(!0)},s.readAsDataURL(l)}})]}),!N&&(0,s.jsx)("div",{className:"flex items-center justify-center gap-2 mt-4",children:(0,s.jsxs)(g.Bc,{children:[(0,s.jsxs)(g.m_,{children:[(0,s.jsx)(g.k$,{asChild:!0,children:(0,s.jsx)(t.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 rounded-full hover:bg-emerald-50 dark:hover:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-700 transition-all duration-200 hover:scale-115",children:(0,s.jsx)(c.A,{className:"h-4 w-4 text-emerald-500 dark:text-emerald-400"})})}),(0,s.jsx)(g.ZI,{className:"border-2 border-gray-200 dark:border-gray-700",children:(0,s.jsx)("p",{children:"Test voice"})})]}),(0,s.jsxs)(g.m_,{children:[(0,s.jsx)(g.k$,{asChild:!0,children:(0,s.jsx)(t.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 rounded-full hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-blue-200 dark:border-gray-700 transition-all duration-200 hover:scale-115",onClick:()=>G(!0),children:(0,s.jsx)(u.A,{className:"h-4 w-4 text-blue-500 dark:text-blue-400"})})}),(0,s.jsx)(g.ZI,{className:"border-2 border-gray-200 dark:border-gray-700",children:(0,s.jsx)("p",{children:"Chat with agent"})})]}),(0,s.jsxs)(g.m_,{children:[(0,s.jsx)(g.k$,{asChild:!0,children:(0,s.jsx)(t.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 rounded-full hover:bg-indigo-50 dark:hover:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-700 transition-all duration-200 hover:scale-115",children:(0,s.jsx)(h.A,{className:"h-4 w-4 text-indigo-500 dark:text-indigo-400"})})}),(0,s.jsx)(g.ZI,{className:"border-2 border-gray-200 dark:border-gray-700",children:(0,s.jsx)("p",{children:"Start web call"})})]}),(0,s.jsxs)(g.m_,{children:[(0,s.jsx)(g.k$,{asChild:!0,children:(0,s.jsx)(t.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 rounded-full hover:bg-amber-50 dark:hover:bg-amber-900/20 border border-purple-200 dark:border-gray-700 transition-all duration-200 hover:scale-115",onClick:()=>P(!0),children:(0,s.jsx)(m.A,{className:"h-4 w-4 text-purple-500 dark:text-purple-400"})})}),(0,s.jsx)(g.ZI,{className:"border-2 border-gray-200 dark:border-gray-700",children:(0,s.jsx)("p",{children:"Start phone call"})})]})]})})]}),(0,s.jsxs)("div",{className:"w-full lg:w-2/3 xl:w-3/4 space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{htmlFor:"name",children:"Name"}),(0,s.jsx)(r.p,{id:"name",value:(null==l?void 0:l.name)||"",onChange:e=>{f({...l,name:e.target.value})},placeholder:"Enter agent name",className:"w-full"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{htmlFor:"role",children:"Role"}),(0,s.jsx)(r.p,{id:"role",value:(null==l?void 0:l.role)||"",onChange:e=>{f({...l,role:e.target.value})},placeholder:"Enter agent role",className:"w-full"})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Phone Numbers"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{htmlFor:"localPhoneNumber",children:"Local Number"}),(0,s.jsxs)(n.l6,{value:(null==l?void 0:l.localPhoneNumberId)||"",onValueChange:e=>{f({...l,localPhoneNumberId:e})},children:[(0,s.jsx)(n.bq,{id:"localPhoneNumber",className:"w-full",children:(0,s.jsx)(n.yv,{placeholder:"Select a phone number"})}),(0,s.jsxs)(n.gC,{children:[(0,s.jsx)(n.eb,{value:"none",children:"None"}),null==y?void 0:y.map(e=>(0,s.jsxs)(n.eb,{value:e.id,children:[e.number," ",e.name?"(".concat(e.name,")"):""]},e.id))]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{htmlFor:"internationalPhoneNumber",children:"International Number"}),(0,s.jsxs)(n.l6,{value:(null==l?void 0:l.internationalPhoneNumberId)||"",onValueChange:e=>{f({...l,internationalPhoneNumberId:e})},children:[(0,s.jsx)(n.bq,{id:"internationalPhoneNumber",className:"w-full",children:(0,s.jsx)(n.yv,{placeholder:"Select a phone number"})}),(0,s.jsxs)(n.gC,{children:[(0,s.jsx)(n.eb,{value:"none",children:"None"}),null==y?void 0:y.map(e=>(0,s.jsxs)(n.eb,{value:e.id,children:[e.number," ",e.name?"(".concat(e.name,")"):""]},e.id))]})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{htmlFor:"language",children:"Language"}),(0,s.jsxs)(n.l6,{value:(null==l?void 0:null===(a=l.transcriber)||void 0===a?void 0:a.language)||"",children:[(0,s.jsx)(n.bq,{id:"language",className:"w-full sm:w-auto",children:(0,s.jsx)(n.yv,{placeholder:"Select a language"})}),(0,s.jsx)(n.gC,{children:[{code:"en-US",name:"English (US)"},{code:"en-GB",name:"English (UK)"},{code:"en",name:"English"},{code:"fr-FR",name:"French"},{code:"fr",name:"French"},{code:"es-ES",name:"Spanish"},{code:"de-DE",name:"German"},{code:"it-IT",name:"Italian"},{code:"ar-SA",name:"Arabic"}].map(e=>(0,s.jsx)(n.eb,{value:e.code,children:e.name},e.code))})]})]})]})]}),!N&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.G,{isOpen:I,onClose:()=>P(!1),agent:l}),(0,s.jsx)(b.M,{agent:l,isOpen:M,onClose:()=>G(!1)})]}),(0,s.jsx)(x.lG,{open:k,onOpenChange:C,children:(0,s.jsxs)(x.Cf,{className:"sm:max-w-md",children:[(0,s.jsx)(x.c7,{children:(0,s.jsx)(x.L3,{children:"Crop Avatar Image"})}),S&&(0,s.jsxs)("div",{className:"mt-4 flex flex-col items-center",children:[(0,s.jsx)(v.Ay,{crop:E,onChange:e=>T(U(e)),onComplete:e=>{z(e)},circularCrop:!0,minWidth:50,minHeight:50,keepSelection:!0,locked:!0,children:(0,s.jsx)("img",{ref:B,src:S,alt:"Crop preview",style:{maxHeight:"400px"},onLoad:e=>{let{width:a,height:l}=e.currentTarget,s=Math.min(a,l),r=(a-s)/2,i=(l-s)/2;T({unit:"px",width:s,height:s,x:r,y:i})}})}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:"Drag to adjust the crop area."})]}),(0,s.jsxs)(x.Es,{className:"flex justify-between mt-4",children:[(0,s.jsx)(t.$,{variant:"outline",onClick:()=>{C(!1),L(null),F(null),q.current&&(q.current.value="")},children:"Cancel"}),(0,s.jsx)(t.$,{onClick:O,children:"Apply & Upload"})]})]})})]})})}},46102:(e,a,l)=>{l.d(a,{Bc:()=>n,ZI:()=>d,k$:()=>o,m_:()=>t});var s=l(95155);l(12115);var r=l(89613),i=l(59434);function n(e){let{delayDuration:a=0,...l}=e;return(0,s.jsx)(r.Kq,{"data-slot":"tooltip-provider",delayDuration:a,...l})}function t(e){let{...a}=e;return(0,s.jsx)(n,{children:(0,s.jsx)(r.bL,{"data-slot":"tooltip",...a})})}function o(e){let{...a}=e;return(0,s.jsx)(r.l9,{"data-slot":"tooltip-trigger",...a})}function d(e){let{className:a,sideOffset:l=0,children:n,...t}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"tooltip-content",sideOffset:l,className:(0,i.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit rounded-md px-3 py-1.5 text-xs text-balance",a),...t,children:[n,(0,s.jsx)(r.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},59409:(e,a,l)=>{l.d(a,{bq:()=>u,eb:()=>m,gC:()=>h,l6:()=>d,yv:()=>c});var s=l(95155);l(12115);var r=l(31992),i=l(66474),n=l(5196),t=l(47863),o=l(59434);function d(e){let{...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...a})}function c(e){let{...a}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...a})}function u(e){let{className:a,children:l,...n}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger",className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...n,children:[l,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function h(e){let{className:a,children:l,position:i="popper",...n}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:i,...n,children:[(0,s.jsx)(v,{}),(0,s.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:l}),(0,s.jsx)(x,{})]})})}function m(e){let{className:a,children:l,...i}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...i,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:l})]})}function v(e){let{className:a,...l}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...l,children:(0,s.jsx)(t.A,{className:"size-4"})})}function x(e){let{className:a,...l}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",a),...l,children:(0,s.jsx)(i.A,{className:"size-4"})})}},62829:(e,a,l)=>{l.d(a,{A:()=>s});let s={src:"/_next/static/media/Binghatti-Lisa.85c81ecb.jpeg",height:1586,width:1586,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/2wBDAQoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/wgARCAAIAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAX/xAAUAQEAAAAAAAAAAAAAAAAAAAAC/9oADAMBAAIQAxAAAACeA//EABsQAAEFAQEAAAAAAAAAAAAAAAECAwQREwAi/9oACAEBAAE/AG6e3khuoyZAbQNDWYR6SO//xAAVEQEBAAAAAAAAAAAAAAAAAAABAP/aAAgBAgEBPwAL/8QAFhEAAwAAAAAAAAAAAAAAAAAAAAFB/9oACAEDAQE/AHD/2Q==",blurWidth:8,blurHeight:8}},66695:(e,a,l)=>{l.d(a,{BT:()=>o,Wu:()=>d,ZB:()=>t,Zp:()=>i,aR:()=>n,wL:()=>c});var s=l(95155);l(12115);var r=l(59434);function i(e){let{className:a,...l}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",a),...l})}function n(e){let{className:a,...l}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("flex flex-col gap-1.5 px-6",a),...l})}function t(e){let{className:a,...l}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...l})}function o(e){let{className:a,...l}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...l})}function d(e){let{className:a,...l}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...l})}function c(e){let{className:a,...l}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6",a),...l})}},76202:(e,a,l)=>{l.d(a,{A:()=>t});var s=l(95155),r=l(12115),i=l(54073),n=l(59434);function t(e){let{className:a,defaultValue:l,value:t,min:o=0,max:d=100,...c}=e,u=r.useMemo(()=>Array.isArray(t)?t:Array.isArray(l)?l:[o,d],[t,l,o,d]);return(0,s.jsxs)(i.bL,{"data-slot":"slider",defaultValue:l,value:t,min:o,max:d,className:(0,n.cn)("relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",a),...c,children:[(0,s.jsx)(i.CC,{"data-slot":"slider-track",className:(0,n.cn)("bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),children:(0,s.jsx)(i.Q6,{"data-slot":"slider-range",className:(0,n.cn)("bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full")})}),Array.from({length:u.length},(e,a)=>(0,s.jsx)(i.zi,{"data-slot":"slider-thumb",className:"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"},a))]})}},79167:(e,a,l)=>{l.d(a,{A:()=>u});var s=l(95155),r=l(62523),i=l(85057),n=l(59409),t=l(76202),o=l(80333),d=l(12115),c=l(66695);function u(e){var a,l,u,h,m;let{agent:v,setAgent:x,phoneNumbers:g}=e,[p,b]=(0,d.useState)({model:(null==v?void 0:null===(a=v.model)||void 0===a?void 0:a.model)||"",provider:(null==v?void 0:null===(l=v.model)||void 0===l?void 0:l.provider)||"",maxTokens:(null==v?void 0:null===(u=v.model)||void 0===u?void 0:u.maxTokens)||100,temperature:(null==v?void 0:null===(h=v.model)||void 0===h?void 0:h.temperature)||.2}),j=(e,a)=>{let l={...p,[e]:a};if("provider"===e)switch(a){case"openai":l.model="gpt-4o";break;case"anthropic":l.model="claude-3-7-sonnet-20250219";break;case"google":l.model="gemini-2.0-flash";break;case"cerebras":l.model="llama-3.3-70b";break;case"deep-seek":l.model="deepseek-chat";break;case"xai":l.model="grok-2";break;case"mistral":l.model="mistral-large-latest";break;default:l.model=""}b(l),x({...v,model:{...v.model,provider:l.provider,model:l.model,maxTokens:l.maxTokens,temperature:l.temperature}})};return(0,s.jsxs)("div",{className:"space-y-6 p-6",children:[(0,s.jsx)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4",children:(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Advanced Settings"})}),(0,s.jsxs)("div",{className:"space-y-4 mt-6",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Model Settings"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{htmlFor:"model-provider",children:"Provider"}),(0,s.jsxs)(n.l6,{value:(null==p?void 0:p.provider)||"",onValueChange:e=>j("provider",e),children:[(0,s.jsx)(n.bq,{id:"model-provider",className:"w-full",children:(0,s.jsx)(n.yv,{placeholder:"Select provider"})}),(0,s.jsxs)(n.gC,{children:[(0,s.jsx)(n.eb,{value:"openai",children:"OpenAI"}),(0,s.jsx)(n.eb,{value:"anthropic",children:"Anthropic"}),(0,s.jsx)(n.eb,{value:"google",children:"Google"}),(0,s.jsx)(n.eb,{value:"cerebras",children:"Cerebras"}),(0,s.jsx)(n.eb,{value:"deep-seek",children:"Deepseek"}),(0,s.jsx)(n.eb,{value:"xai",children:"Xai"}),(0,s.jsx)(n.eb,{value:"mistral",children:"Mistral"}),(0,s.jsx)(n.eb,{value:"perplexity-ai",children:"Perplexity AI"}),(0,s.jsx)(n.eb,{value:"anyscale",children:"Anyscale"}),(0,s.jsx)(n.eb,{value:"inflection-ai",children:"Inflection AI"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{htmlFor:"model-name",children:"Model"}),(0,s.jsxs)(n.l6,{value:(null==p?void 0:p.model)||"",onValueChange:e=>j("model",e),disabled:["perplexity-ai","anyscale","inflection-ai"].includes(p.provider),children:[(0,s.jsx)(n.bq,{id:"model-name",className:"w-full",children:(0,s.jsx)(n.yv,{placeholder:"Select model"})}),(0,s.jsxs)(n.gC,{children:["openai"===p.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.eb,{value:"gpt-4o",children:"GPT 4o"}),(0,s.jsx)(n.eb,{value:"gpt-4.1",children:"GPT 4.1"}),(0,s.jsx)(n.eb,{value:"gpt-4.1-mini",children:"GPT 4.1 Mini"}),(0,s.jsx)(n.eb,{value:"gpt-4.1-nano",children:"GPT 4.1 Nano"}),(0,s.jsx)(n.eb,{value:"gpt-4.5-preview",children:"GPT 4.5 Preview"}),(0,s.jsx)(n.eb,{value:"gpt-4o-mini",children:"GPT 4o Mini"}),(0,s.jsx)(n.eb,{value:"chatgpt-4o-latest",children:"ChatGPT 4o Latest"}),(0,s.jsx)(n.eb,{value:"o3",children:"O3"}),(0,s.jsx)(n.eb,{value:"o3-mini",children:"O3 Mini"}),(0,s.jsx)(n.eb,{value:"o4-mini",children:"O4 Mini"}),(0,s.jsx)(n.eb,{value:"o1-preview",children:"O1 Preview"}),(0,s.jsx)(n.eb,{value:"o1-mini",children:"O1 Mini"}),(0,s.jsx)(n.eb,{value:"gpt-4o-realtime-preview-2024-10-01",children:"GPT 4o Realtime Preview"}),(0,s.jsx)(n.eb,{value:"gpt-4-turbo",children:"GPT 4 Turbo"}),(0,s.jsx)(n.eb,{value:"gpt-4",children:"GPT 4"}),(0,s.jsx)(n.eb,{value:"gpt-3.5-turbo",children:"GPT 3.5 Turbo"})]}),"anthropic"===p.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.eb,{value:"claude-3-7-sonnet-20250219",children:"Claude 3.7 Sonnet"}),(0,s.jsx)(n.eb,{value:"claude-3-5-sonnet-20241022",children:"Claude 3.5 Sonnet"}),(0,s.jsx)(n.eb,{value:"claude-3-5-haiku-20241022",children:"Claude 3.5 Haiku (Old)"}),(0,s.jsx)(n.eb,{value:"claude-3-opus-20240229",children:"Claude 3 Opus"}),(0,s.jsx)(n.eb,{value:"claude-3-sonnet-20240229",children:"Claude 3 Sonnet"}),(0,s.jsx)(n.eb,{value:"claude-3-haiku-20240307",children:"Claude 3 Haiku (Latest)"})]}),"google"===p.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.eb,{value:"gemini-2.5-flash-preview-04-17",children:"Gemini 2.5 Flash Preview"}),(0,s.jsx)(n.eb,{value:"gemini-2.0-flash",children:"Gemini 2.0 Flash"}),(0,s.jsx)(n.eb,{value:"gemini-2.0-flash-thinking-exp",children:"Gemini 2.0 Flash Thinking (Experimental)"}),(0,s.jsx)(n.eb,{value:"gemini-2.0-pro-exp-02-05",children:"Gemini 2.0 Pro (Experimental)"}),(0,s.jsx)(n.eb,{value:"gemini-2.0-flash-lite",children:"Gemini 2.0 Flash Lite"}),(0,s.jsx)(n.eb,{value:"gemini-2.0-flash-lite-preview-02-05",children:"Gemini 2.0 Flash Lite Preview"}),(0,s.jsx)(n.eb,{value:"gemini-2.0-flash-exp",children:"Gemini 2.0 Flash (Experimental)"}),(0,s.jsx)(n.eb,{value:"gemini-2.0-flash-realtime-exp",children:"Gemini 2.0 Flash Realtime (Experimental)"}),(0,s.jsx)(n.eb,{value:"gemini-1.5-flash",children:"Gemini 1.5 Flash"}),(0,s.jsx)(n.eb,{value:"gemini-1.5-flash-002",children:"Gemini 1.5 Flash 002"}),(0,s.jsx)(n.eb,{value:"gemini-1.5-pro",children:"Gemini 1.5 Pro"}),(0,s.jsx)(n.eb,{value:"gemini-1.5-pro-002",children:"Gemini 1.5 Pro 002"}),(0,s.jsx)(n.eb,{value:"gemini-1.0-pro",children:"Gemini 1.0 Pro"})]}),"cerebras"===p.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.eb,{value:"llama-3.3-70b",children:"Llama 3.3 70B"}),(0,s.jsx)(n.eb,{value:"llama3.1-8b",children:"Llama 3.1 8B"})]}),"deep-seek"===p.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.eb,{value:"deepseek-chat",children:"DeepSeek Chat (V3)"}),(0,s.jsx)(n.eb,{value:"deepseek-reasoner",children:"Deepseek-R1"})]}),"xai"===p.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.eb,{value:"grok-2",children:"Grok 2"}),(0,s.jsx)(n.eb,{value:"grok-3",children:"Grok 3"}),(0,s.jsx)(n.eb,{value:"grok-beta",children:"Grok Beta"})]}),"mistral"===p.provider&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(n.eb,{value:"mistral-large-latest",children:"Mistral Large"}),(0,s.jsx)(n.eb,{value:"pixtral-large-latest",children:"Pixtral Large"}),(0,s.jsx)(n.eb,{value:"mistral-small",children:"Mistral Small"})]}),["perplexity-ai","anyscale","inflection-ai"].includes(p.provider)&&(0,s.jsx)(n.eb,{value:"none",disabled:!0,children:"No models available"})]})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(i.J,{htmlFor:"max-tokens",children:"Max Tokens"}),(0,s.jsx)(r.p,{id:"max-tokens",type:"number",min:1,max:500,value:(null==p?void 0:p.maxTokens)||100,onChange:e=>j("maxTokens",Math.min(500,parseInt(e.target.value)||0)),className:"w-full"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[(0,s.jsx)(i.J,{htmlFor:"temperature",children:"Temperature"}),(0,s.jsx)("span",{className:"text-sm text-muted-foreground mt-3",children:(null==p?void 0:p.temperature.toFixed(1))||.2})]}),(0,s.jsx)(t.A,{id:"temperature",value:[null==p?void 0:p.temperature],min:0,max:2,step:.1,className:"w-full",onValueChange:e=>j("temperature",e[0])})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Webhook (Server Url)"}),(0,s.jsx)(c.Zp,{className:"p-4",children:(0,s.jsx)(r.p,{placeholder:"https://your-service.com/webhook",value:(null==v?void 0:null===(m=v.server)||void 0===m?void 0:m.url)||"",onChange:e=>{x({...v,server:{url:e.target.value}})}})})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Background Denoising"}),(0,s.jsx)(c.Zp,{className:"p-4",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(i.J,{htmlFor:"background-denoising",children:"Enable background noise reduction"}),(0,s.jsx)(o.d,{id:"background-denoising",checked:(null==v?void 0:v.backgroundDenoisingEnabled)||!1,onCheckedChange:e=>{x({...v,backgroundDenoisingEnabled:e})}})]})})]})]})}l(54534)},80333:(e,a,l)=>{l.d(a,{d:()=>n});var s=l(95155);l(12115);var r=l(4884),i=l(59434);function n(e){let{className:a,...l}=e;return(0,s.jsx)(r.bL,{"data-slot":"switch",className:(0,i.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 inline-flex h-5 w-9 shrink-0 items-center rounded-full border-2 border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...l,children:(0,s.jsx)(r.zi,{"data-slot":"switch-thumb",className:(0,i.cn)("bg-background pointer-events-none block size-4 rounded-full ring-0 shadow-lg transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")})})}},85057:(e,a,l)=>{l.d(a,{J:()=>n});var s=l(95155);l(12115);var r=l(40968),i=l(59434);function n(e){let{className:a,...l}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...l})}},86902:(e,a,l)=>{l.d(a,{M:()=>h});var s=l(95155),r=l(12115),i=l(54416),n=l(51154),t=l(90105),o=l(62829),d=l(66766),c=l(30285),u=l(62523);function h(e){let{agent:a,isOpen:l,onClose:h}=e,[m,v]=(0,r.useState)([]),[x,g]=(0,r.useState)(""),[p,b]=(0,r.useState)(!1),j=["I understand what you're saying. Let me help you with that.","That's interesting! Could you tell me more?","I'm processing your request. Here's what I think...","Based on what you've told me, I would suggest...","Let me check that for you quickly."],f=async e=>{if(!e.trim())return;let a={id:Date.now().toString(),content:e,sender:"user",timestamp:new Date};v(e=>[...e,a]),g(""),b(!0),setTimeout(()=>{let e=j[Math.floor(Math.random()*j.length)],a={id:(Date.now()+1).toString(),content:e,sender:"agent",timestamp:new Date};v(e=>[...e,a]),b(!1)},1e3)};return(0,r.useState)(()=>{l&&0===m.length&&v([{id:"initial",content:"Hello! My name is ".concat(null==a?void 0:a.name,". How can I assist you today?"),sender:"agent",timestamp:new Date}])}),(0,s.jsxs)("div",{className:"fixed right-0 top-19 h-[90vh] border-2 w-96 bg-white dark:bg-gray-800 shadow-xl transition-all duration-200 ".concat(l?"opacity-100 visible":"opacity-0 invisible"," z-50 rounded-lg"),children:[(0,s.jsxs)("div",{className:"border-b p-4 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"relative h-10 w-10 rounded-full overflow-hidden",children:(null==a?void 0:a.avatar)?(0,s.jsx)("img",{src:a.avatar,alt:"".concat(a.name," avatar"),className:"h-full w-full object-cover"}):(0,s.jsx)(d.default,{src:o.A,alt:"".concat(null==a?void 0:a.name," avatar"),className:"object-cover",fill:!0})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold",children:null==a?void 0:a.name}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:null==a?void 0:a.role})]})]}),(0,s.jsx)(c.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:h,children:(0,s.jsx)(i.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"h-[calc(100%-8rem)] overflow-y-auto p-4 space-y-4",children:[m.map(e=>(0,s.jsx)("div",{className:"flex ".concat("user"===e.sender?"justify-end":"justify-start"),children:(0,s.jsx)("div",{className:"max-w-[80%] rounded-lg p-3 ".concat("user"===e.sender?"bg-primary text-primary-foreground":"bg-gray-100 dark:bg-gray-700"),children:e.content})},e.id)),p&&(0,s.jsx)("div",{className:"flex justify-start",children:(0,s.jsx)("div",{className:"bg-gray-100 dark:bg-gray-700 rounded-lg p-3",children:(0,s.jsx)(n.A,{className:"h-4 w-4 animate-spin"})})})]}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-4 bg-white dark:bg-gray-800 border-t",children:(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),f(x)},className:"flex gap-2",children:[(0,s.jsx)(u.p,{value:x,onChange:e=>g(e.target.value),placeholder:"Type a message...",className:"flex-1"}),(0,s.jsx)(c.$,{type:"submit",size:"icon",children:(0,s.jsx)(t.A,{className:"h-4 w-4"})})]})})]})}},88006:(e,a,l)=>{l.d(a,{A:()=>u});var s=l(95155),r=l(90105),i=l(28883),n=l(75441),t=l(6076),o=l(57434),d=l(69074),c=l(12115);function u(e){let{agent:a,setAgent:l,phoneNumbers:u}=e,[h,m]=(0,c.useState)(null),[v,x]=(0,c.useState)("config"),[g,p]=(0,c.useState)({sms:{type:"sms",icon:(0,s.jsx)(r.A,{className:"h-4 w-4"}),label:"Send SMS",description:"Send SMS messages to customers",color:"bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300",enabled:!0},email:{type:"email",icon:(0,s.jsx)(i.A,{className:"h-4 w-4"}),label:"Send Email",description:"Send email communications",color:"bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300",enabled:!0},transfer:{type:"transfer",icon:(0,s.jsx)(n.A,{className:"h-4 w-4"}),label:"Transfer Call",description:"Transfer calls to other agents",color:"bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300",enabled:!0},end:{type:"end",icon:(0,s.jsx)(t.A,{className:"h-4 w-4"}),label:"End Call",description:"End the current call",color:"bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300",enabled:!0},extract:{type:"extract",icon:(0,s.jsx)(o.A,{className:"h-4 w-4"}),label:"Extract Info",description:"Extract information from conversations",color:"bg-amber-100 text-amber-700 dark:bg-amber-900 dark:text-amber-300",enabled:!0},calendar:{type:"calendar",icon:(0,s.jsx)(d.A,{className:"h-4 w-4"}),label:"Calendar",description:"Schedule appointments and meetings",color:"bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300",enabled:!0}}),[b,j]=(0,c.useState)(""),[f,y]=(0,c.useState)("both"),[N,w]=(0,c.useState)("end"),[A,k]=(0,c.useState)(""),[C,S]=(0,c.useState)(""),[F,E]=(0,c.useState)("followup");return(0,s.jsx)("div",{className:"space-y-6 p-6",children:(0,s.jsx)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4",children:(0,s.jsx)("h3",{className:"text-lg font-semibold",children:"Actions Configuration"})})})}},88539:(e,a,l)=>{l.d(a,{T:()=>i});var s=l(95155);l(12115);var r=l(59434);function i(e){let{className:a,...l}=e;return(0,s.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...l})}}}]);