"use strict";exports.id=2093,exports.ids=[2093],exports.modules={4845:(a,r,e)=>{e.d(r,{s:()=>i,u:()=>t});let i=a=>{let r=a.replace(/\D/g,"");for(let[a,e]of Object.entries({1:"America/New_York",30:"Europe/Athens",31:"Europe/Amsterdam",32:"Europe/Brussels",33:"Europe/Paris",34:"Europe/Madrid",36:"Europe/Budapest",39:"Europe/Rome",40:"Europe/Bucharest",41:"Europe/Zurich",43:"Europe/Vienna",44:"Europe/London",45:"Europe/Copenhagen",46:"Europe/Stockholm",47:"Europe/Oslo",48:"Europe/Warsaw",49:"Europe/Berlin",351:"Europe/Lisbon",352:"Europe/Luxembourg",353:"Europe/Dublin",354:"Atlantic/Reykjavik",355:"Europe/Tirane",356:"Europe/Malta",357:"Asia/Nicosia",358:"Europe/Helsinki",359:"Europe/Sofia",370:"Europe/Vilnius",371:"Europe/Riga",372:"Europe/Tallinn",373:"Europe/Chisinau",374:"Asia/Yerevan",375:"Europe/Minsk",376:"Europe/Andorra",377:"Europe/Monaco",378:"Europe/San_Marino",380:"Europe/Kiev",381:"Europe/Belgrade",382:"Europe/Podgorica",385:"Europe/Zagreb",386:"Europe/Ljubljana",387:"Europe/Sarajevo",389:"Europe/Skopje",420:"Europe/Prague",421:"Europe/Bratislava",60:"Asia/Kuala_Lumpur",61:"Australia/Sydney",62:"Asia/Jakarta",63:"Asia/Manila",64:"Pacific/Auckland",65:"Asia/Singapore",66:"Asia/Bangkok",81:"Asia/Tokyo",82:"Asia/Seoul",84:"Asia/Ho_Chi_Minh",86:"Asia/Shanghai",90:"Europe/Istanbul",91:"Asia/Kolkata",92:"Asia/Karachi",93:"Asia/Kabul",94:"Asia/Colombo",95:"Asia/Yangon",98:"Asia/Tehran",960:"Indian/Maldives",961:"Asia/Beirut",962:"Asia/Amman",963:"Asia/Damascus",964:"Asia/Baghdad",965:"Asia/Kuwait",966:"Asia/Riyadh",967:"Asia/Aden",968:"Asia/Muscat",970:"Asia/Gaza",971:"Asia/Dubai",972:"Asia/Jerusalem",973:"Asia/Bahrain",974:"Asia/Qatar",975:"Asia/Thimphu",976:"Asia/Ulaanbaatar",977:"Asia/Kathmandu",992:"Asia/Dushanbe",993:"Asia/Ashgabat",994:"Asia/Baku",995:"Asia/Tbilisi",996:"Asia/Bishkek",998:"Asia/Tashkent",20:"Africa/Cairo",211:"Africa/Juba",212:"Africa/Casablanca",213:"Africa/Algiers",216:"Africa/Tunis",218:"Africa/Tripoli",220:"Africa/Banjul",221:"Africa/Dakar",222:"Africa/Nouakchott",223:"Africa/Bamako",224:"Africa/Conakry",225:"Africa/Abidjan",226:"Africa/Ouagadougou",227:"Africa/Niamey",228:"Africa/Lome",229:"Africa/Porto-Novo",230:"Indian/Mauritius",231:"Africa/Monrovia",232:"Africa/Freetown",233:"Africa/Accra",234:"Africa/Lagos",235:"Africa/Ndjamena",236:"Africa/Bangui",237:"Africa/Douala",238:"Atlantic/Cape_Verde",239:"Africa/Sao_Tome",240:"Africa/Malabo",241:"Africa/Libreville",242:"Africa/Brazzaville",243:"Africa/Kinshasa",244:"Africa/Luanda",245:"Africa/Bissau",246:"Indian/Chagos",248:"Indian/Mahe",249:"Africa/Khartoum",250:"Africa/Kigali",251:"Africa/Addis_Ababa",252:"Africa/Mogadishu",253:"Africa/Djibouti",254:"Africa/Nairobi",255:"Africa/Dar_es_Salaam",256:"Africa/Kampala",257:"Africa/Bujumbura",258:"Africa/Maputo",260:"Africa/Lusaka",261:"Indian/Antananarivo",262:"Indian/Reunion",263:"Africa/Harare",264:"Africa/Windhoek",265:"Africa/Blantyre",266:"Africa/Maseru",267:"Africa/Gaborone",268:"Africa/Mbabane",269:"Indian/Comoro",27:"Africa/Johannesburg",290:"Atlantic/St_Helena",291:"Africa/Asmara",297:"America/Aruba",298:"Atlantic/Faroe",299:"America/Godthab",51:"America/Lima",52:"America/Mexico_City",53:"America/Havana",54:"America/Argentina/Buenos_Aires",55:"America/Sao_Paulo",56:"America/Santiago",57:"America/Bogota",58:"America/Caracas",591:"America/La_Paz",592:"America/Guyana",593:"America/Guayaquil",595:"America/Asuncion",597:"America/Paramaribo",598:"America/Montevideo",501:"America/Belize",502:"America/Guatemala",503:"America/El_Salvador",504:"America/Tegucigalpa",505:"America/Managua",506:"America/Costa_Rica",507:"America/Panama",509:"America/Port-au-Prince",590:"America/Guadeloupe",596:"America/Martinique",599:"America/Curacao",670:"Asia/Dili",672:"Antarctica/Macquarie",673:"Asia/Brunei",674:"Pacific/Nauru",675:"Pacific/Port_Moresby",676:"Pacific/Tongatapu",677:"Pacific/Guadalcanal",678:"Pacific/Efate",679:"Pacific/Fiji",680:"Pacific/Palau",681:"Pacific/Wallis",682:"Pacific/Rarotonga",683:"Pacific/Niue",685:"Pacific/Apia",686:"Pacific/Tarawa",687:"Pacific/Noumea",688:"Pacific/Funafuti",689:"Pacific/Tahiti",690:"Pacific/Fakaofo",691:"Pacific/Pohnpei",692:"Pacific/Majuro"}))if(r.startsWith(a))return e;return null},o={"Africa/Abidjan":"ci","Africa/Accra":"gh","Africa/Addis_Ababa":"et","Africa/Algiers":"dz","Africa/Asmara":"er","Africa/Bamako":"ml","Africa/Bangui":"cf","Africa/Banjul":"gm","Africa/Bissau":"gw","Africa/Blantyre":"mw","Africa/Brazzaville":"cg","Africa/Bujumbura":"bi","Africa/Cairo":"eg","Africa/Casablanca":"ma","Africa/Ceuta":"es","Africa/Conakry":"gn","Africa/Dakar":"sn","Africa/Dar_es_Salaam":"tz","Africa/Djibouti":"dj","Africa/Douala":"cm","Africa/El_Aaiun":"eh","Africa/Freetown":"sl","Africa/Gaborone":"bw","Africa/Harare":"zw","Africa/Johannesburg":"za","Africa/Juba":"ss","Africa/Kampala":"ug","Africa/Khartoum":"sd","Africa/Kigali":"rw","Africa/Kinshasa":"cd","Africa/Lagos":"ng","Africa/Libreville":"ga","Africa/Lome":"tg","Africa/Luanda":"ao","Africa/Lubumbashi":"cd","Africa/Lusaka":"zm","Africa/Malabo":"gq","Africa/Maputo":"mz","Africa/Maseru":"ls","Africa/Mbabane":"sz","Africa/Mogadishu":"so","Africa/Monrovia":"lr","Africa/Nairobi":"ke","Africa/Ndjamena":"td","Africa/Niamey":"ne","Africa/Nouakchott":"mr","Africa/Ouagadougou":"bf","Africa/Porto-Novo":"bj","Africa/Sao_Tome":"st","Africa/Tripoli":"ly","Africa/Tunis":"tn","Africa/Windhoek":"na","America/Anchorage":"us","America/Argentina/Buenos_Aires":"ar","America/Bogota":"co","America/Caracas":"ve","America/Chicago":"us","America/Denver":"us","America/Guatemala":"gt","America/Halifax":"ca","America/La_Paz":"bo","America/Lima":"pe","America/Los_Angeles":"us","America/Mexico_City":"mx","America/New_York":"us","America/Panama":"pa","America/Port-au-Prince":"ht","America/Santiago":"cl","America/Sao_Paulo":"br","America/Toronto":"ca","America/Vancouver":"ca","Asia/Almaty":"kz","Asia/Amman":"jo","Asia/Baghdad":"iq","Asia/Baku":"az","Asia/Bangkok":"th","Asia/Beirut":"lb","Asia/Colombo":"lk","Asia/Damascus":"sy","Asia/Dhaka":"bd","Asia/Dubai":"ae","Asia/Hong_Kong":"hk","Asia/Jakarta":"id","Asia/Jerusalem":"il","Asia/Karachi":"pk","Asia/Kathmandu":"np","Asia/Kolkata":"in","Asia/Kuala_Lumpur":"my","Asia/Macau":"mo","Asia/Manila":"ph","Asia/Seoul":"kr","Asia/Shanghai":"cn","Asia/Singapore":"sg","Asia/Taipei":"tw","Asia/Tehran":"ir","Asia/Tokyo":"jp","Asia/Vientiane":"la","Asia/Yangon":"mm","Europe/Amsterdam":"nl","Europe/Athens":"gr","Europe/Belgrade":"rs","Europe/Berlin":"de","Europe/Bratislava":"sk","Europe/Brussels":"be","Europe/Bucharest":"ro","Europe/Budapest":"hu","Europe/Chisinau":"md","Europe/Copenhagen":"dk","Europe/Dublin":"ie","Europe/Gibraltar":"gi","Europe/Helsinki":"fi","Europe/Istanbul":"tr","Europe/Kiev":"ua","Europe/Lisbon":"pt","Europe/Ljubljana":"si","Europe/London":"gb","Europe/Luxembourg":"lu","Europe/Madrid":"es","Europe/Malta":"mt","Europe/Minsk":"by","Europe/Moscow":"ru","Europe/Oslo":"no","Europe/Paris":"fr","Europe/Prague":"cz","Europe/Riga":"lv","Europe/Rome":"it","Europe/Sarajevo":"ba","Europe/Skopje":"mk","Europe/Sofia":"bg","Europe/Stockholm":"se","Europe/Tallinn":"ee","Europe/Tirane":"al","Europe/Vienna":"at","Europe/Vilnius":"lt","Europe/Warsaw":"pl","Europe/Zagreb":"hr","Europe/Zurich":"ch","Pacific/Auckland":"nz","Pacific/Fiji":"fj","Pacific/Honolulu":"us","Pacific/Pago_Pago":"as","Pacific/Port_Moresby":"pg","Pacific/Tongatapu":"to"},t=()=>{try{return o[Intl.DateTimeFormat().resolvedOptions().timeZone]||"nl"}catch(a){return console.error("Error detecting country from timezone:",a),"nl"}}},63503:(a,r,e)=>{e.d(r,{Cf:()=>f,Es:()=>p,HM:()=>u,L3:()=>m,c7:()=>d,lG:()=>n,rr:()=>g,zM:()=>s});var i=e(60687);e(43210);var o=e(26134),t=e(11860),c=e(4780);function n({...a}){return(0,i.jsx)(o.bL,{"data-slot":"dialog",...a})}function s({...a}){return(0,i.jsx)(o.l9,{"data-slot":"dialog-trigger",...a})}function l({...a}){return(0,i.jsx)(o.ZL,{"data-slot":"dialog-portal",...a})}function u({...a}){return(0,i.jsx)(o.bm,{"data-slot":"dialog-close",...a})}function A({className:a,...r}){return(0,i.jsx)(o.hJ,{"data-slot":"dialog-overlay",className:(0,c.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-650 bg-black/50",a),...r})}function f({className:a,children:r,...e}){return(0,i.jsxs)(l,{"data-slot":"dialog-portal",children:[(0,i.jsx)(A,{}),(0,i.jsxs)(o.UC,{"data-slot":"dialog-content",className:(0,c.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-650 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...e,children:[r,(0,i.jsxs)(o.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,i.jsx)(t.A,{}),(0,i.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function d({className:a,...r}){return(0,i.jsx)("div",{"data-slot":"dialog-header",className:(0,c.cn)("flex flex-col gap-2 text-center sm:text-left",a),...r})}function p({className:a,...r}){return(0,i.jsx)("div",{"data-slot":"dialog-footer",className:(0,c.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...r})}function m({className:a,...r}){return(0,i.jsx)(o.hE,{"data-slot":"dialog-title",className:(0,c.cn)("text-lg leading-none font-semibold",a),...r})}function g({className:a,...r}){return(0,i.jsx)(o.VY,{"data-slot":"dialog-description",className:(0,c.cn)("text-muted-foreground text-sm",a),...r})}},89667:(a,r,e)=>{e.d(r,{p:()=>t});var i=e(60687);e(43210);var o=e(4780);function t({className:a,type:r,...e}){return(0,i.jsx)("input",{type:r,"data-slot":"input",className:(0,o.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...e})}},89757:(a,r,e)=>{e.d(r,{G6:()=>u,MO:()=>s,SQ:()=>n,TX:()=>l,oe:()=>o,oz:()=>A,to:()=>t,vY:()=>c});let i="http://localhost:4000";async function o(){try{let a=localStorage.getItem("access_token");if(!a)return console.error("No access token available"),[];let r=await fetch(`${i}/api/contacts`,{headers:{Authorization:`Bearer ${a}`}});if(!r.ok)throw Error("Failed to fetch contacts");return await r.json()}catch(a){throw console.error("Error fetching contacts:",a),a}}async function t(){try{let a=localStorage.getItem("access_token");if(!a)return console.error("No access token available"),[];let r=await fetch(`${i}/api/campaigns`,{headers:{Authorization:`Bearer ${a}`}});if(!r.ok)throw Error("Failed to fetch contacts");return await r.json()}catch(a){throw console.error("Error fetching contacts:",a),a}}async function c(a){try{let r=localStorage.getItem("access_token");if(!r)throw console.error("No access token available"),Error("No access token available");let e=await fetch(`${i}/api/contacts`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`},body:JSON.stringify(a)});if(!e.ok)throw Error("Failed to create contact");return await e.json()}catch(a){throw console.error("Error creating contact:",a),a}}async function n(a){let r=localStorage.getItem("access_token");if(!r)throw Error("No access token available");let e=await fetch("http://localhost:4000/api/contacts",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`},body:JSON.stringify(a)});if(!e.ok)throw Error((await e.json()).error||`Failed to create contact: ${e.statusText}`);return e.json()}async function s(a){try{let r=localStorage.getItem("access_token");if(!r)throw console.error("No access token available"),Error("No access token available");if(!(await fetch(`${i}/api/contacts/${a}`,{method:"DELETE",headers:{Authorization:`Bearer ${r}`}})).ok)throw Error("Failed to delete contact")}catch(a){throw console.error("Error deleting contact:",a),a}}async function l(){try{let a=localStorage.getItem("access_token");if(!a)throw console.error("No access token available"),Error("No access token available");let r=await fetch(`${i}/api/contacts/import-contacts`,{headers:{Authorization:`Bearer ${a}`}});if(!r.ok)throw Error("Failed to import contacts");await r.json()}catch(a){throw console.error("Error importing contacts:",a),a}}async function u(a,r,e){try{let o=localStorage.getItem("access_token");if(!o)throw console.error("No access token available"),Error("No access token available");let t=await fetch(`${i}/api/vapi/call-contacts`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o}`},body:JSON.stringify({agentId:a,contacts:r,region:e})});if(402===t.status){let a=await t.json();throw Error(a.error||"Insufficient credits to make this call. Please add funds to your account.")}if(!t.ok)throw Error("Failed to start call");return await t.json()}catch(a){throw console.error("Error calling contact(s):",a),a}}async function A(a,r,e,o){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");let c=await fetch(`${i}/api/scheduled-call`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify({agentId:a,contacts:r,scheduledTime:e,region:o})});if(402===c.status){let a=await c.json();throw Error(a.error||"Insufficient credits to schedule this call. Please add funds to your account.")}if(!c.ok)throw Error("Failed to schedule call");return await c.json()}catch(a){throw console.error("Error scheduling call:",a),a}}}};