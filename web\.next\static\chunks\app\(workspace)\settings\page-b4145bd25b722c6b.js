(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3211],{12421:(e,t,a)=>{"use strict";a.d(t,{t:()=>r});var s=a(57297);async function r(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=localStorage.getItem("access_token");if(!a){let e=await (0,s.J1)();if(!e.success)throw Error("No authentication token available");a=e.newAccessToken}let r=new Headers(t.headers||{});r.has("Authorization")||r.set("Authorization","Bearer ".concat(a));let n=await fetch(e,{...t,headers:r});if(401===n.status||403===n.status){console.log("Token expired, attempting refresh...");let a=await (0,s.J1)();if(!a.success)throw console.error("Token refresh failed"),window.location.href="/login",Error("Authentication failed");console.log("Token refreshed, retrying request...");let r=new Headers(t.headers||{});return r.set("Authorization","Bearer ".concat(a.newAccessToken)),fetch(e,{...t,headers:r})}return n}},14636:(e,t,a)=>{"use strict";a.d(t,{AM:()=>i,Wv:()=>l,hl:()=>o});var s=a(95155);a(12115);var r=a(20547),n=a(59434);function i(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"popover",...t})}function l(e){let{...t}=e;return(0,s.jsx)(r.l9,{"data-slot":"popover-trigger",...t})}function o(e){let{className:t,align:a="center",sideOffset:i=4,...l}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsx)(r.UC,{"data-slot":"popover-content",align:a,sideOffset:i,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-72 origin-(--radix-popover-content-transform-origin) rounded-md border p-4 shadow-md outline-hidden",t),...l})})}},17313:(e,t,a)=>{"use strict";a.d(t,{Xi:()=>o,av:()=>c,j7:()=>l,tU:()=>i});var s=a(95155);a(12115);var r=a(60704),n=a(59434);function i(e){let{className:t,...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"tabs",className:(0,n.cn)("flex flex-col gap-2",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,n.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-1",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,n.cn)("data-[state=active]:bg-background data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring inline-flex flex-1 items-center justify-center gap-1.5 rounded-md px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,n.cn)("flex-1 outline-none",t),...a})}},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>o,r:()=>l});var s=a(95155);a(12115);var r=a(99708),n=a(74466),i=a(59434);let l=(0,n.F)("inline-flex items-center cursor-pointer justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:n,asChild:o=!1,...c}=e,d=o?r.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,i.cn)(l({variant:a,size:n,className:t})),...c})}},57297:(e,t,a)=>{"use strict";a.d(t,{HW:()=>n,J1:()=>l,_f:()=>i});var s=a(12421);let r="http://localhost:4000";async function n(){try{let e=await (0,s.t)("".concat(r,"/api/auth/me"),{method:"GET"});if(!e.ok)return{success:!1,error:"Error: ".concat(e.status)};let t=await e.json(),a=t.userId||t._id||t.id,n=t.email;if(a&&n)return{success:!0,user:{fullName:t.fullName||n.split("@")[0],userId:a,email:n,role:t.role||"user"}};return{success:!1,error:"Invalid user data received"}}catch(e){return console.error("Error fetching user data:",e),{success:!1,error:"An error occurred while fetching user data"}}}function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=setInterval(async()=>{if(localStorage.getItem("access_token"))try{await l()}catch(e){console.error("Background token refresh failed:",e)}},6e4*e);return()=>clearInterval(t)}async function l(){let e=localStorage.getItem("refresh_token");if(!e)return{success:!1};try{let t=await fetch("".concat(r,"/api/auth/refresh"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!t.ok)return{success:!1};let a=await t.json();if(a.access_token)return localStorage.setItem("access_token",a.access_token),{success:!0,newAccessToken:a.access_token};return{success:!1}}catch(e){return console.error("Token refresh error:",e),{success:!1}}}},59409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>u,eb:()=>m,gC:()=>x,l6:()=>c,yv:()=>d});var s=a(95155);a(12115);var r=a(31992),n=a(66474),i=a(5196),l=a(47863),o=a(59434);function c(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,children:a,...i}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger",className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i,children:[a,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:t,children:a,position:n="popper",...i}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,s.jsx)(h,{}),(0,s.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(g,{})]})})}function m(e){let{className:t,children:a,...n}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(i.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:a})]})}function h(e){let{className:t,...a}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(l.A,{className:"size-4"})})}function g(e){let{className:t,...a}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(n.A,{className:"size-4"})})}},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n,v:()=>i});var s=a(52596),r=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}function i(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var s=a(95155);a(12115);var r=a(59434);function n(e){let{className:t,type:a,...n}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>d});var s=a(95155);a(12115);var r=a(59434);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("flex flex-col gap-1.5 px-6",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6",t),...a})}},80333:(e,t,a)=>{"use strict";a.d(t,{d:()=>i});var s=a(95155);a(12115);var r=a(4884),n=a(59434);function i(e){let{className:t,...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"switch",className:(0,n.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 inline-flex h-5 w-9 shrink-0 items-center rounded-full border-2 border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,s.jsx)(r.zi,{"data-slot":"switch-thumb",className:(0,n.cn)("bg-background pointer-events-none block size-4 rounded-full ring-0 shadow-lg transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")})})}},81733:(e,t,a)=>{Promise.resolve().then(a.bind(a,90823))},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>i});var s=a(95155);a(12115);var r=a(40968),n=a(59434);function i(e){let{className:t,...a}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},85511:(e,t,a)=>{"use strict";a.d(t,{V:()=>c});var s=a(95155);a(12115);var r=a(42355),n=a(13052),i=a(29746),l=a(59434),o=a(30285);function c(e){let{className:t,classNames:a,showOutsideDays:c=!0,...d}=e;return(0,s.jsx)(i.hv,{showOutsideDays:c,className:(0,l.cn)("p-3",t),classNames:{months:"flex flex-col sm:flex-row gap-2",month:"flex flex-col gap-4",caption:"flex justify-center pt-1 relative items-center w-full",caption_label:"text-sm font-medium",nav:"flex items-center gap-1",nav_button:(0,l.cn)((0,o.r)({variant:"outline"}),"size-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-x-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:(0,l.cn)("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md","range"===d.mode?"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md":"[&:has([aria-selected])]:rounded-md"),day:(0,l.cn)((0,o.r)({variant:"ghost"}),"size-8 p-0 font-normal aria-selected:opacity-100"),day_range_start:"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground",day_range_end:"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...a},components:{IconLeft:e=>{let{className:t,...a}=e;return(0,s.jsx)(r.A,{className:(0,l.cn)("size-4",t),...a})},IconRight:e=>{let{className:t,...a}=e;return(0,s.jsx)(n.A,{className:(0,l.cn)("size-4",t),...a})}},...d})}},90823:(e,t,a)=>{"use strict";a.d(t,{default:()=>P});var s=a(95155),r=a(30285),n=a(12115),i=a(62523),l=a(85057),o=a(51154),c=a(40646);function d(e){let{label:t,description:a,value:d,onChange:u,onTest:x,placeholder:m="Enter API key"}=e,[h,g]=(0,n.useState)(!1),[p,f]=(0,n.useState)(!1),[v,j]=(0,n.useState)(null),y=async()=>{if(x&&d.trim()){f(!0),j(null);try{let e=await x();j(e)}catch(e){j(!1),console.error("API test failed:",e)}finally{f(!1)}setTimeout(()=>j(null),3e3)}};return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-baseline justify-between",children:[(0,s.jsx)(l.J,{htmlFor:t.replace(/\s+/g,"-").toLowerCase(),children:t}),a&&(0,s.jsx)("span",{className:"text-xs text-muted-foreground",children:a})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)(i.p,{id:t.replace(/\s+/g,"-").toLowerCase(),type:h?"text":"password",value:d,onChange:e=>u(e.target.value),placeholder:m,className:"pr-20"}),(0,s.jsx)(r.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-0 text-xs font-normal",onClick:()=>g(!h),children:h?"Hide":"Show"})]}),x&&(0,s.jsx)(r.$,{type:"button",variant:"outline",size:"sm",onClick:y,disabled:p||!d.trim(),className:"flex items-center gap-1 whitespace-nowrap",children:p?(0,s.jsx)(o.A,{className:"h-3 w-3 animate-spin"}):!0===v?(0,s.jsx)(c.A,{className:"h-3 w-3 text-green-500"}):!1===v?(0,s.jsx)("span",{className:"text-red-500",children:"Failed"}):"Test"})]})]})})}function u(){let[e,t]=(0,n.useState)(""),[a,i]=(0,n.useState)(""),[l,o]=(0,n.useState)(""),[c,u]=(0,n.useState)(""),[x,m]=(0,n.useState)(!1),h=async()=>(await new Promise(e=>setTimeout(e,1e3)),!0),g=async()=>(await new Promise(e=>setTimeout(e,1e3)),!0),p=async()=>(await new Promise(e=>setTimeout(e,1e3)),!0),f=async()=>(await new Promise(e=>setTimeout(e,1e3)),!1),v=async()=>{m(!0),await new Promise(e=>setTimeout(e,1e3)),m(!1)};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Language Model APIs"}),(0,s.jsxs)("div",{className:"grid gap-6",children:[(0,s.jsx)(d,{label:"OpenAI API Key",description:"Required for GPT-3.5, GPT-4, and DALL-E models",value:e,onChange:t,onTest:h,placeholder:"sk-..."}),(0,s.jsx)(d,{label:"Anthropic API Key",description:"Required for Claude models",value:a,onChange:i,onTest:g,placeholder:"sk-ant-..."}),(0,s.jsx)(d,{label:"Google API Key",description:"Required for Gemini models",value:l,onChange:o,onTest:p}),(0,s.jsx)(d,{label:"DeepSeek API Key",description:"Required for DeepSeek models",value:c,onChange:u,onTest:f})]})]}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)(r.$,{onClick:v,disabled:x,className:"bg-primary text-primary-foreground hover:bg-primary/90",children:x?"Saving...":"Save Changes"})})]})}var x=a(59409),m=a(80333),h=a(51362);function g(){let{theme:e,setTheme:t}=(0,h.D)(),[a,o]=(0,n.useState)(""),[c,d]=(0,n.useState)(""),[u,g]=(0,n.useState)("en"),[p,f]=(0,n.useState)(!0),[v,j]=(0,n.useState)(!1),y=async()=>{j(!0),await new Promise(e=>setTimeout(e,800)),j(!1)};return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Profile Information"}),(0,s.jsxs)("div",{className:"grid gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"name",children:"Name"}),(0,s.jsx)(i.p,{id:"name",value:a,onChange:e=>o(e.target.value),placeholder:"Your name"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"email",children:"Email"}),(0,s.jsx)(i.p,{id:"email",type:"email",value:c,onChange:e=>d(e.target.value),placeholder:"<EMAIL>"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Preferences"}),(0,s.jsxs)("div",{className:"grid gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{children:"Theme"}),(0,s.jsxs)(x.l6,{value:e,onValueChange:t,children:[(0,s.jsx)(x.bq,{children:(0,s.jsx)(x.yv,{placeholder:"Select theme"})}),(0,s.jsxs)(x.gC,{children:[(0,s.jsx)(x.eb,{value:"light",children:"Light"}),(0,s.jsx)(x.eb,{value:"dark",children:"Dark"}),(0,s.jsx)(x.eb,{value:"system",children:"System"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{children:"Language"}),(0,s.jsxs)(x.l6,{value:u,onValueChange:g,children:[(0,s.jsx)(x.bq,{children:(0,s.jsx)(x.yv,{placeholder:"Select language"})}),(0,s.jsxs)(x.gC,{children:[(0,s.jsx)(x.eb,{value:"en",children:"English"}),(0,s.jsx)(x.eb,{value:"es",children:"Espa\xf1ol"}),(0,s.jsx)(x.eb,{value:"fr",children:"Fran\xe7ais"})]})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-0.5",children:[(0,s.jsx)(l.J,{children:"Notifications"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Receive notifications about your agents and tasks"})]}),(0,s.jsx)(m.d,{checked:p,onCheckedChange:f})]})]})]}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)(r.$,{onClick:y,disabled:v,className:"bg-primary text-primary-foreground hover:bg-primary/90",children:v?"Saving...":"Save Changes"})})]})})}var p=a(85511),f=a(14636),v=a(83013),j=a(53904),y=a(47924),b=a(69074),w=a(1482),N=a(54416);function k(){let[e,t]=(0,n.useState)([]),[a,l]=(0,n.useState)(!0),[o,c]=(0,n.useState)(""),[d,u]=(0,n.useState)(""),[m,h]=(0,n.useState)("all"),[g,k]=(0,n.useState)(void 0),[S,_]=(0,n.useState)(void 0),[A,C]=(0,n.useState)(1),[P,T]=(0,n.useState)(!0),[z,I]=(0,n.useState)(0),[E,L]=(0,n.useState)(0),[F,R]=(0,n.useState)(!1),D=(0,n.useRef)(null),q="http://localhost:4000",O=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,a=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{R(!0);let s=localStorage.getItem("access_token");if(!s){c("No access token available"),l(!1),R(!1);return}let r=new URLSearchParams;r.append("page",e.toString()),r.append("limit","20"),m&&"all"!==m&&r.append("level",m),d.trim()&&r.append("search",d.trim()),g&&r.append("startDate",g.toISOString().split("T")[0]),S&&r.append("endDate",S.toISOString().split("T")[0]);let n=await fetch("".concat(q,"/api/logs?").concat(r.toString()),{headers:{Authorization:"Bearer ".concat(s)}});if(!n.ok)throw Error("Failed to fetch logs");let i=await n.json();a?t(i.logs):t(e=>[...e,...i.logs]),I(i.total),L(i.totalPages),T(e<i.totalPages)}catch(e){console.error("Error fetching logs:",e),c("Failed to fetch logs. Please try again.")}finally{l(!1),R(!1)}},$=()=>{C(1),O(1,!0)},J=async()=>{try{l(!0);let e=localStorage.getItem("access_token");if(!e){c("No access token available"),l(!1);return}let t=await fetch("".concat(q,"/api/logs/cleanup"),{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error("Failed to cleanup logs");let a=await t.json();alert("Successfully deleted ".concat(a.deletedCount," logs older than 4 days")),C(1),O(1,!0)}catch(e){console.error("Error cleaning up logs:",e),c("Failed to cleanup logs. Please try again.")}finally{l(!1)}},V=(0,n.useCallback)(e=>{!a&&(D.current&&D.current.disconnect(),D.current=new IntersectionObserver(e=>{e[0].isIntersecting&&P&&!F&&C(e=>e+1)}),e&&D.current.observe(e))},[a,P,F]);(0,n.useEffect)(()=>{O(1,!0)},[]),(0,n.useEffect)(()=>{A>1&&O(A)},[A]);let B=e=>{switch(e.toLowerCase()){case"error":return"text-red-500";case"warn":return"text-yellow-500";case"info":return"text-green-500";default:return"text-gray-900"}};return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("h2",{className:"text-xl font-bold",children:"Logs"}),(0,s.jsxs)(r.$,{variant:"outline",size:"sm",onClick:J,disabled:a,children:[(0,s.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Clean Old Logs"]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(y.A,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-gray-500"}),(0,s.jsx)(i.p,{placeholder:"Search logs...",value:d,onChange:e=>u(e.target.value),className:"pl-8",onKeyDown:e=>"Enter"===e.key&&$()})]}),(0,s.jsxs)(x.l6,{value:m||"all",onValueChange:h,children:[(0,s.jsx)(x.bq,{children:(0,s.jsx)(x.yv,{placeholder:"Filter by level"})}),(0,s.jsxs)(x.gC,{children:[(0,s.jsx)(x.eb,{value:"all",children:"All Levels"}),(0,s.jsx)(x.eb,{value:"INFO",children:"Info"}),(0,s.jsx)(x.eb,{value:"WARN",children:"Warning"}),(0,s.jsx)(x.eb,{value:"ERROR",children:"Error"})]})]}),(0,s.jsxs)(f.AM,{children:[(0,s.jsx)(f.Wv,{asChild:!0,children:(0,s.jsxs)(r.$,{variant:"outline",className:"justify-start text-left font-normal",children:[(0,s.jsx)(b.A,{className:"mr-2 h-4 w-4"}),g?(0,v.GP)(g,"PPP"):"Start date"]})}),(0,s.jsx)(f.hl,{className:"w-auto p-0",align:"start",children:(0,s.jsx)(p.V,{mode:"single",selected:g,onSelect:k,initialFocus:!0})})]}),(0,s.jsxs)(f.AM,{children:[(0,s.jsx)(f.Wv,{asChild:!0,children:(0,s.jsxs)(r.$,{variant:"outline",className:"justify-start text-left font-normal",children:[(0,s.jsx)(b.A,{className:"mr-2 h-4 w-4"}),S?(0,v.GP)(S,"PPP"):"End date"]})}),(0,s.jsx)(f.hl,{className:"w-auto p-0",align:"start",children:(0,s.jsx)(p.V,{mode:"single",selected:S,onSelect:_,initialFocus:!0})})]})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(r.$,{onClick:$,disabled:a||F,children:[(0,s.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Apply Filters"]}),(0,s.jsxs)(r.$,{variant:"outline",onClick:()=>{u(""),h("all"),k(void 0),_(void 0),C(1),O(1,!0)},disabled:a||F,children:[(0,s.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"Reset"]})]}),!a&&e.length>0&&(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:["Showing ",e.length," of ",z," logs ",m&&"all"!==m&&"(filtered by ".concat(m,")"),E>1&&" - Page ".concat(A," of ").concat(E)]}),a&&1===A?(0,s.jsx)("div",{className:"flex justify-center items-center h-40",children:(0,s.jsx)("p",{children:"Loading logs..."})}):o?(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:(0,s.jsxs)("p",{children:["Error: ",o]})}):0===e.length?(0,s.jsx)("div",{className:"bg-gray-50 border border-gray-200 text-gray-700 px-4 py-8 rounded text-center",children:(0,s.jsx)("p",{children:"No logs found."})}):(0,s.jsxs)("div",{className:"overflow-x-auto border rounded-md",children:[(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Timestamp"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Level"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Message"}),(0,s.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Trace"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map((t,a)=>(0,s.jsxs)("tr",{ref:a===e.length-5?V:null,className:"hover:bg-gray-50 transition-colors",children:[(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:new Date(t.timestamp).toLocaleString()}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm ".concat(B(t.level)),children:(0,s.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium",children:t.level})}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900 max-w-md truncate",children:t.message}),(0,s.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900 max-w-xs truncate",children:t.trace||"-"})]},t._id||a))})]}),a&&A>1&&(0,s.jsx)("div",{className:"py-4 text-center text-gray-500",children:"Loading more logs..."}),!P&&e.length>0&&(0,s.jsx)("div",{className:"py-4 text-center text-gray-500",children:"End of logs"})]})]})}function S(){let[e,t]=(0,n.useState)(""),[a,i]=(0,n.useState)(""),[l,o]=(0,n.useState)(""),[c,u]=(0,n.useState)(!1),x=async()=>(await new Promise(e=>setTimeout(e,1e3)),!0),m=async()=>(await new Promise(e=>setTimeout(e,1e3)),!0),h=async()=>(await new Promise(e=>setTimeout(e,1e3)),!0),g=async()=>{u(!0),await new Promise(e=>setTimeout(e,1e3)),u(!1)};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Voice APIs"}),(0,s.jsxs)("div",{className:"grid gap-6",children:[(0,s.jsx)(d,{label:"ElevenLabs API Key",description:"Required for high-quality text-to-speech",value:e,onChange:t,onTest:x}),(0,s.jsx)(d,{label:"Deepgram API Key",description:"Required for real-time speech recognition",value:a,onChange:i,onTest:m}),(0,s.jsx)(d,{label:"AssemblyAI API Key",description:"Required for advanced speech recognition and analysis",value:l,onChange:o,onTest:h})]})]}),(0,s.jsx)("div",{className:"flex justify-end",children:(0,s.jsx)(r.$,{onClick:g,disabled:c,className:"bg-primary text-primary-foreground hover:bg-primary/90",children:c?"Saving...":"Save Changes"})})]})}var _=a(66695),A=a(17313),C=a(12421);function P(){let[e,t]=(0,n.useState)("general"),[a,r]=(0,n.useState)(null);(0,n.useEffect)(()=>{!async function(){let e=await (0,C.t)("".concat("http://localhost:4000","/api/auth/me"));r(await e.json())}()},[]);let i=(null==a?void 0:a.role)==="superadmin",l="grid-cols-3";return i&&(l="grid-cols-4"),(0,s.jsx)("div",{className:"container py-6 px-4 md:px-6",children:(0,s.jsxs)(_.Zp,{children:[(0,s.jsx)(_.aR,{children:(0,s.jsx)(_.ZB,{className:"text-2xl",children:"Settings"})}),(0,s.jsx)(_.Wu,{children:(0,s.jsxs)(A.tU,{value:e,onValueChange:t,className:"w-full",children:[(0,s.jsxs)(A.j7,{className:"grid w-full ".concat(l," mb-8"),children:[(0,s.jsx)(A.Xi,{value:"general",children:"General"}),(0,s.jsx)(A.Xi,{value:"api",children:"API Keys"}),(0,s.jsx)(A.Xi,{value:"voice",children:"Voice Settings"}),i&&(0,s.jsx)(A.Xi,{value:"logs",children:"Logs"})]}),(0,s.jsx)(A.av,{value:"general",children:(0,s.jsx)(g,{})}),(0,s.jsx)(A.av,{value:"api",children:(0,s.jsx)(u,{})}),(0,s.jsx)(A.av,{value:"voice",children:(0,s.jsx)(S,{})}),i&&(0,s.jsx)(A.av,{value:"logs",children:(0,s.jsx)(k,{})})]})})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4201,4341,6403,6544,2593,6482,4912,8441,1684,7358],()=>t(81733)),_N_E=e.O()}]);