"use strict";exports.id=9017,exports.ids=[9017],exports.modules={14555:(e,t,r)=>{r.d(t,{C1:()=>W,bL:()=>_,q7:()=>U});var n=r(43210),o=r(70569),i=r(98599),a=r(11273),u=r(14163),l=r(72942),s=r(65551),d=r(43),c=r(18853),f=r(83721),p=r(46059),m=r(60687),v="Radio",[y,h]=(0,a.A)(v),[b,w]=y(v),g=n.forwardRef((e,t)=>{let{__scopeRadio:r,name:a,checked:l=!1,required:s,disabled:d,value:c="on",onCheck:f,form:p,...v}=e,[y,h]=n.useState(null),w=(0,i.s)(t,e=>h(e)),g=n.useRef(!1),x=!y||p||!!y.closest("form");return(0,m.jsxs)(b,{scope:r,checked:l,disabled:d,children:[(0,m.jsx)(u.sG.button,{type:"button",role:"radio","aria-checked":l,"data-state":R(l),"data-disabled":d?"":void 0,disabled:d,value:c,...v,ref:w,onClick:(0,o.m)(e.onClick,e=>{l||f?.(),x&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})}),x&&(0,m.jsx)(E,{control:y,bubbles:!g.current,name:a,value:c,checked:l,required:s,disabled:d,form:p,style:{transform:"translateX(-100%)"}})]})});g.displayName=v;var x="RadioIndicator",N=n.forwardRef((e,t)=>{let{__scopeRadio:r,forceMount:n,...o}=e,i=w(x,r);return(0,m.jsx)(p.C,{present:n||i.checked,children:(0,m.jsx)(u.sG.span,{"data-state":R(i.checked),"data-disabled":i.disabled?"":void 0,...o,ref:t})})});N.displayName=x;var E=e=>{let{control:t,checked:r,bubbles:o=!0,...i}=e,a=n.useRef(null),u=(0,f.Z)(r),l=(0,c.X)(t);return n.useEffect(()=>{let e=a.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(u!==r&&t){let n=new Event("click",{bubbles:o});t.call(e,r),e.dispatchEvent(n)}},[u,r,o]),(0,m.jsx)("input",{type:"radio","aria-hidden":!0,defaultChecked:r,...i,tabIndex:-1,ref:a,style:{...e.style,...l,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function R(e){return e?"checked":"unchecked"}var k=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],C="RadioGroup",[j,O]=(0,a.A)(C,[l.RG,h]),A=(0,l.RG)(),M=h(),[P,I]=j(C),S=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,name:n,defaultValue:o,value:i,required:a=!1,disabled:c=!1,orientation:f,dir:p,loop:v=!0,onValueChange:y,...h}=e,b=A(r),w=(0,d.jH)(p),[g,x]=(0,s.i)({prop:i,defaultProp:o,onChange:y});return(0,m.jsx)(P,{scope:r,name:n,required:a,disabled:c,value:g,onValueChange:x,children:(0,m.jsx)(l.bL,{asChild:!0,...b,orientation:f,dir:w,loop:v,children:(0,m.jsx)(u.sG.div,{role:"radiogroup","aria-required":a,"aria-orientation":f,"data-disabled":c?"":void 0,dir:w,...h,ref:t})})})});S.displayName=C;var L="RadioGroupItem",T=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,disabled:a,...u}=e,s=I(L,r),d=s.disabled||a,c=A(r),f=M(r),p=n.useRef(null),v=(0,i.s)(t,p),y=s.value===u.value,h=n.useRef(!1);return n.useEffect(()=>{let e=e=>{k.includes(e.key)&&(h.current=!0)},t=()=>h.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,m.jsx)(l.q7,{asChild:!0,...c,focusable:!d,active:y,children:(0,m.jsx)(g,{disabled:d,required:s.required,checked:y,...f,...u,name:s.name,ref:v,onCheck:()=>s.onValueChange(u.value),onKeyDown:(0,o.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,o.m)(u.onFocus,()=>{h.current&&p.current?.click()})})})});T.displayName=L;var D=n.forwardRef((e,t)=>{let{__scopeRadioGroup:r,...n}=e,o=M(r);return(0,m.jsx)(N,{...o,...n,ref:t})});D.displayName="RadioGroupIndicator";var _=S,U=T,W=D},25112:(e,t,r)=>{r.d(t,{C1:()=>O,bL:()=>j});var n=r(43210);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function a(...e){return n.useCallback(i(...e),e)}var u=r(60687);function l(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}function s(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}var d=globalThis?.document?n.useLayoutEffect:()=>{},c=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[o,i]=n.useState(),a=n.useRef({}),u=n.useRef(e),l=n.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=f(a.current);l.current="mounted"===s?e:"none"},[s]),d(()=>{let t=a.current,r=u.current;if(r!==e){let n=l.current,o=f(t);e?c("MOUNT"):"none"===o||t?.display==="none"?c("UNMOUNT"):r&&n!==o?c("ANIMATION_OUT"):c("UNMOUNT"),u.current=e}},[e,c]),d(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,r=r=>{let n=f(a.current).includes(r.animationName);if(r.target===o&&n&&(c("ANIMATION_END"),!u.current)){let r=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=r)})}},n=e=>{e.target===o&&(l.current=f(a.current))};return o.addEventListener("animationstart",n),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",n),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}c("ANIMATION_END")},[o,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:n.useCallback(e=>{e&&(a.current=getComputedStyle(e)),i(e)},[])}}(t),i="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),u=a(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof r||o.isPresent?n.cloneElement(i,{ref:u}):null};function f(e){return e?.animationName||"none"}c.displayName="Presence",r(51215);var p=Symbol("radix.slottable");function m(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===p}var v=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){var a;let e,u;let l=(a=r,(u=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(u=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),s=function(e,t){let r={...t};for(let n in t){let o=e[n],i=t[n];/^on[A-Z]/.test(n)?o&&i?r[n]=(...e)=>{i(...e),o(...e)}:o&&(r[n]=o):"style"===n?r[n]={...o,...i}:"className"===n&&(r[n]=[o,i].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props);return r.type!==n.Fragment&&(s.ref=t?i(t,l):l),n.cloneElement(r,s)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...i}=e,a=n.Children.toArray(o),l=a.find(m);if(l){let e=l.props.children,o=a.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,u.jsx)(t,{...i,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,u.jsx)(t,{...i,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(o?r:t,{...i,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),y="Checkbox",[h,b]=function(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return o.scopeName=e,[function(t,o){let i=n.createContext(o),a=r.length;r=[...r,o];let l=t=>{let{scope:r,children:o,...l}=t,s=r?.[e]?.[a]||i,d=n.useMemo(()=>l,Object.values(l));return(0,u.jsx)(s.Provider,{value:d,children:o})};return l.displayName=t+"Provider",[l,function(r,u){let l=u?.[e]?.[a]||i,s=n.useContext(l);if(s)return s;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(o,...t)]}(y),[w,g]=h(y),x=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:o,checked:i,defaultChecked:d,required:c,disabled:f,value:p="on",onCheckedChange:m,form:y,...h}=e,[b,g]=n.useState(null),x=a(t,e=>g(e)),N=n.useRef(!1),E=!b||y||!!b.closest("form"),[j=!1,O]=function({prop:e,defaultProp:t,onChange:r=()=>{}}){let[o,i]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[o]=r,i=n.useRef(o),a=s(t);return n.useEffect(()=>{i.current!==o&&(a(o),i.current=o)},[o,i,a]),r}({defaultProp:t,onChange:r}),a=void 0!==e,u=a?e:o,l=s(r);return[u,n.useCallback(t=>{if(a){let r="function"==typeof t?t(e):t;r!==e&&l(r)}else i(t)},[a,e,i,l])]}({prop:i,defaultProp:d,onChange:m}),A=n.useRef(j);return n.useEffect(()=>{let e=b?.form;if(e){let t=()=>O(A.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[b,O]),(0,u.jsxs)(w,{scope:r,state:j,disabled:f,children:[(0,u.jsx)(v.button,{type:"button",role:"checkbox","aria-checked":k(j)?"mixed":j,"aria-required":c,"data-state":C(j),"data-disabled":f?"":void 0,disabled:f,value:p,...h,ref:x,onKeyDown:l(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:l(e.onClick,e=>{O(e=>!!k(e)||!e),E&&(N.current=e.isPropagationStopped(),N.current||e.stopPropagation())})}),E&&(0,u.jsx)(R,{control:b,bubbles:!N.current,name:o,value:p,checked:j,required:c,disabled:f,form:y,style:{transform:"translateX(-100%)"},defaultChecked:!k(d)&&d})]})});x.displayName=y;var N="CheckboxIndicator",E=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...o}=e,i=g(N,r);return(0,u.jsx)(c,{present:n||k(i.state)||!0===i.state,children:(0,u.jsx)(v.span,{"data-state":C(i.state),"data-disabled":i.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});E.displayName=N;var R=e=>{let{control:t,checked:r,bubbles:o=!0,defaultChecked:i,...a}=e,l=n.useRef(null),s=function(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(r),c=function(e){let[t,r]=n.useState(void 0);return d(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(t);n.useEffect(()=>{let e=l.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(s!==r&&t){let n=new Event("click",{bubbles:o});e.indeterminate=k(r),t.call(e,!k(r)&&r),e.dispatchEvent(n)}},[s,r,o]);let f=n.useRef(!k(r)&&r);return(0,u.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i??f.current,...a,tabIndex:-1,ref:l,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function k(e){return"indeterminate"===e}function C(e){return k(e)?"indeterminate":e?"checked":"unchecked"}var j=x,O=E},65822:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(62688).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])}};