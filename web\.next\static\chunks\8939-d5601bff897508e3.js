"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8939],{995:(e,a,t)=>{t.d(a,{A:()=>v});var s=t(95155),r=t(12115),n=t(62523),l=t(85057),i=t(30285),c=t(66695),d=t(17580);let o=(0,t(19946).A)("FileUp",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 12v6",key:"3ahymv"}],["path",{d:"m15 15-3-3-3 3",key:"15xj92"}]]);var u=t(38164),m=t(47924),h=t(5196),x=t(51154),p=t(30356),f=t(85127);function v(e){let{data:a,updateData:t}=e,[v,y]=(0,r.useState)([]),[g,j]=(0,r.useState)(!1),[N,b]=(0,r.useState)(a.sources||[]),[A,w]=(0,r.useState)(a.sourceType||"contacts"),[k,C]=(0,r.useState)(""),[S,T]=(0,r.useState)({}),[E,D]=(0,r.useState)(1),[F,z]=(0,r.useState)(!0),[I,_]=(0,r.useState)(!1),J=(0,r.useRef)(null);(0,r.useEffect)(()=>{a.sources&&a.sources.length>0&&b(a.sources)},[a.sources]),(0,r.useEffect)(()=>{W()},[]);let W=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,a=arguments.length>1?arguments[1]:void 0;try{1===e?j(!0):_(!0);let t=a?"&search=".concat(encodeURIComponent(a)):"",s=await fetch("".concat("http://localhost:4000","/api/contacts?page=").concat(e,"&limit=").concat(20).concat(t),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!s.ok)throw Error("Failed to fetch contacts");let r=await s.json();1===e?y(r):y(e=>[...e,...r]),z(20===r.length)}catch(e){console.error("Error loading contacts:",e)}finally{j(!1),_(!1)}};(0,r.useEffect)(()=>{let e=new IntersectionObserver(e=>{e[0].isIntersecting&&F&&!g&&!I&&D(e=>e+1)},{threshold:.1}),a=J.current;return a&&e.observe(a),()=>{a&&e.unobserve(a)}},[F,g,I]),(0,r.useEffect)(()=>{E>1&&W(E,k)},[E,k]);let M=e=>{let a;b(a=N.some(a=>a.contactName===e.contactName&&a.phoneNumber===e.phoneNumber)?N.filter(a=>a.contactName!==e.contactName||a.phoneNumber!==e.phoneNumber):[...N,e]),t({sources:a,sourceType:A})},O=e=>{w(e),t({sourceType:e})};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"name",children:"Campaign Name*"}),(0,s.jsx)(n.p,{id:"name",value:a.name,onChange:e=>{t({name:e.target.value})},placeholder:"Enter campaign name",className:S.name?"border-red-500":""}),S.name&&(0,s.jsx)("p",{className:"text-red-500 text-sm",children:S.name})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(l.J,{children:"Source Type"}),(0,s.jsxs)(p.z,{value:A,onValueChange:O,className:"grid grid-cols-3 gap-4",children:[(0,s.jsx)(c.Zp,{className:"cursor-pointer transition-all ".concat("contacts"===A?"border-primary":""),onClick:()=>O("contacts"),children:(0,s.jsxs)(c.Wu,{className:"p-4 flex flex-col items-center justify-center space-y-2",children:[(0,s.jsx)(d.A,{className:"h-8 w-8 text-primary"}),(0,s.jsx)(l.J,{htmlFor:"contacts",className:"font-medium",children:"Contact List"})]})}),(0,s.jsx)(c.Zp,{className:"cursor-pointer transition-all ".concat("import"===A?"border-primary":""),onClick:()=>O("import"),children:(0,s.jsxs)(c.Wu,{className:"p-4 flex flex-col items-center justify-center space-y-2",children:[(0,s.jsx)(o,{className:"h-8 w-8 text-primary"}),(0,s.jsx)(l.J,{htmlFor:"import",className:"font-medium",children:"Import File"})]})}),(0,s.jsx)(c.Zp,{className:"cursor-pointer transition-all ".concat("thirdparty"===A?"border-primary":""),onClick:()=>O("thirdparty"),children:(0,s.jsxs)(c.Wu,{className:"p-4 flex flex-col items-center justify-center space-y-2",children:[(0,s.jsx)(u.A,{className:"h-8 w-8 text-primary"}),(0,s.jsx)(l.J,{htmlFor:"thirdparty",className:"font-medium",children:"3rd Party App"})]})})]})]}),(0,s.jsxs)("div",{className:"space-y-4 mt-6",children:["contacts"===A&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(l.J,{children:"Select Contact Sources"}),(0,s.jsx)("form",{onSubmit:e=>{e.preventDefault(),D(1),W(1,k)},children:(0,s.jsxs)("div",{className:"relative w-64",children:[(0,s.jsx)(m.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,s.jsx)(n.p,{placeholder:"Search contacts...",className:"pl-8",value:k,onChange:e=>C(e.target.value)})]})})]}),g?(0,s.jsx)("div",{className:"text-center py-4",children:"Loading contacts..."}):0===v.length?(0,s.jsx)("div",{className:"text-center py-4 text-muted-foreground",children:0===v.length?"No contacts available. Please create contacts first.":"No contacts match your search."}):(0,s.jsxs)("div",{className:"border max-h-[300px] overflow-y-auto rounded-md mt-4",children:[(0,s.jsxs)(f.XI,{children:[(0,s.jsx)(f.A0,{children:(0,s.jsxs)(f.Hj,{children:[(0,s.jsx)(f.nd,{className:"w-12"}),(0,s.jsx)(f.nd,{children:"Name"}),(0,s.jsx)(f.nd,{children:"Phone Number"})]})}),(0,s.jsxs)(f.BF,{children:[v.map((e,a)=>(0,s.jsxs)(f.Hj,{className:"cursor-pointer hover:bg-muted",onClick:()=>M(e),children:[(0,s.jsx)(f.nA,{children:(0,s.jsx)("div",{className:"w-5 h-5 rounded-full border ".concat(N.some(a=>a.contactName===e.contactName&&a.phoneNumber===e.phoneNumber)?"bg-primary border-primary":"border-gray-300"),children:N.some(a=>a.contactName===e.contactName&&a.phoneNumber===e.phoneNumber)&&(0,s.jsx)(h.A,{className:"h-4.5 w-4.5 text-white"})})}),(0,s.jsx)(f.nA,{className:"font-medium",children:e.contactName}),(0,s.jsx)(f.nA,{children:e.phoneNumber})]},"".concat(e._id,"-").concat(a))),F&&(0,s.jsx)(f.Hj,{ref:J,className:"h-20",children:(0,s.jsx)(f.nA,{colSpan:7,children:(0,s.jsx)("div",{className:"flex items-center justify-center py-4",children:I?(0,s.jsx)(x.A,{className:"h-6 w-6 animate-spin text-primary"}):(0,s.jsx)("div",{className:"h-8"})})})})]})]}),S.contacts&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-2",children:S.contacts})]}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Selected ",N.length," contacts"]})})]}),"import"===A&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(l.J,{children:"Upload Contact File"}),(0,s.jsx)(c.Zp,{className:"border-dashed border-2",children:(0,s.jsxs)(c.Wu,{className:"p-6 flex flex-col items-center justify-center space-y-4",children:[(0,s.jsx)(o,{className:"h-12 w-12 text-muted-foreground"}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"font-medium",children:"Drag and drop your file here"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Supports CSV and XLSX files"})]}),(0,s.jsx)(n.p,{type:"file",accept:".csv,.xlsx",className:"hidden",id:"file-upload",onChange:e=>{var a;let s=null===(a=e.target.files)||void 0===a?void 0:a[0];s&&(console.log("File selected:",s.name),t({sources:[{_id:Date.now().toString(),contactName:s.name,phoneNumber:"",email:"",type:"file"}],sourceType:A}))}}),(0,s.jsx)(i.$,{variant:"outline",onClick:()=>{var e;return null===(e=document.getElementById("file-upload"))||void 0===e?void 0:e.click()},children:"Browse Files"})]})}),N.length>0&&"file"===N[0].type&&(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:"Selected file:"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:N[0].contactName})]})]}),"thirdparty"===A&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(l.J,{children:"Connect to Third-Party App"}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:["Zapier","Encharge"].map(e=>(0,s.jsx)(c.Zp,{className:"cursor-pointer hover:border-primary transition-all",children:(0,s.jsxs)(c.Wu,{className:"p-4 flex flex-col items-center justify-center space-y-2",children:[(0,s.jsx)("div",{className:"w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-primary font-medium",children:e[0]})}),(0,s.jsx)("p",{className:"font-medium",children:e}),(0,s.jsx)(i.$,{variant:"outline",size:"sm",children:"Connect"})]})},e))})]})]})]})}},12421:(e,a,t)=>{t.d(a,{t:()=>r});var s=t(57297);async function r(e){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},t=localStorage.getItem("access_token");if(!t){let e=await (0,s.J1)();if(!e.success)throw Error("No authentication token available");t=e.newAccessToken}let r=new Headers(a.headers||{});r.has("Authorization")||r.set("Authorization","Bearer ".concat(t));let n=await fetch(e,{...a,headers:r});if(401===n.status||403===n.status){console.log("Token expired, attempting refresh...");let t=await (0,s.J1)();if(!t.success)throw console.error("Token refresh failed"),window.location.href="/login",Error("Authentication failed");console.log("Token refreshed, retrying request...");let r=new Headers(a.headers||{});return r.set("Authorization","Bearer ".concat(t.newAccessToken)),fetch(e,{...a,headers:r})}return n}},16798:(e,a,t)=>{t.d(a,{A:()=>d});var s=t(95155),r=t(59409),n=t(62523),l=t(85057),i=t(76202),c=t(30356);function d(e){let{data:a,updateData:t}=e,d=a.recallHours||24,o=a.maxRecalls||3,u=a.concurrentCalls||10,m=a.instantCall||!1,h=a.batchIntervalMinutes||3;return(0,s.jsxs)("div",{className:"space-y-10 ",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(l.J,{className:"text-base font-medium",children:"Call Settings"}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between mb-2",children:[(0,s.jsx)(l.J,{htmlFor:"concurrent-calls",children:"Concurrent Calls"}),(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:u})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Set the maximum number of simultaneous calls for this campaign"}),(0,s.jsx)(i.A,{id:"concurrent-calls",min:10,max:100,step:10,value:[u],onValueChange:e=>{t({concurrentCalls:e[0]})}}),(0,s.jsxs)("div",{className:"flex justify-between text-xs text-muted-foreground",children:[(0,s.jsx)("span",{children:"10"}),(0,s.jsx)("span",{children:"20"}),(0,s.jsx)("span",{children:"30"}),(0,s.jsx)("span",{children:"40"}),(0,s.jsx)("span",{children:"50"}),(0,s.jsx)("span",{children:"60"}),(0,s.jsx)("span",{children:"70"}),(0,s.jsx)("span",{children:"80"}),(0,s.jsx)("span",{children:"90"}),(0,s.jsx)("span",{children:"100"})]}),(0,s.jsxs)("div",{className:"text-center font-medium",children:[a.concurrentCalls," ",1===a.concurrentCalls?"call":"calls"," at a time"]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"batch-interval",children:"Batch Interval (minutes)"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Set the time interval between batches of scheduled calls"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 w-32",children:[(0,s.jsx)(n.p,{id:"batch-interval",type:"number",min:"1",max:"60",value:h,onChange:e=>{let a=parseInt(e.target.value);!isNaN(a)&&a>0&&t({batchIntervalMinutes:a})},className:"text-center"}),(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:"minutes"})]})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(l.J,{className:"text-base font-medium",children:"Recall Settings"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2 flex flex-col items-center text-center",children:[(0,s.jsx)(l.J,{htmlFor:"recall-hours",children:"Hours Between Recalls"}),(0,s.jsxs)("div",{className:"flex items-center space-x-2 w-32",children:[(0,s.jsx)(n.p,{id:"recall-hours",type:"number",min:"1",value:d,onChange:e=>{let a=parseInt(e.target.value);!isNaN(a)&&a>0&&t({recallHours:a})},className:"text-center"}),(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:"hours"})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:"Time to wait before attempting to recall unanswered contacts"})]}),(0,s.jsxs)("div",{className:"space-y-2 flex flex-col items-center text-center",children:[(0,s.jsx)(l.J,{htmlFor:"max-recalls",children:"Maximum Recall Attempts"}),(0,s.jsxs)(r.l6,{value:o.toString(),onValueChange:e=>{t({maxRecalls:parseInt(e)})},children:[(0,s.jsx)(r.bq,{id:"max-recalls",className:"text-center",children:(0,s.jsx)(r.yv,{placeholder:"Select maximum recalls"})}),(0,s.jsxs)(r.gC,{children:[(0,s.jsx)(r.eb,{value:"1",children:"1 Attempt"}),(0,s.jsx)(r.eb,{value:"2",children:"2 Attempts"}),(0,s.jsx)(r.eb,{value:"3",children:"3 Attempts"}),(0,s.jsx)(r.eb,{value:"4",children:"4 Attempts"}),(0,s.jsx)(r.eb,{value:"5",children:"5 Attempts"}),(0,s.jsx)(r.eb,{value:"6",children:"6 Attempts"}),(0,s.jsx)(r.eb,{value:"7",children:"7 Attempts"}),(0,s.jsx)(r.eb,{value:"8",children:"8 Attempts"}),(0,s.jsx)(r.eb,{value:"9",children:"9 Attempts"}),(0,s.jsx)(r.eb,{value:"10",children:"10 Attempts"})]})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:"Number of times to retry reaching a contact"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(l.J,{className:"text-base font-medium",children:"Instant Call"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Enable instant calling to immediately connect with contacts when the campaign is active"}),(0,s.jsxs)(c.z,{value:m?"yes":"no",onValueChange:e=>{t({instantCall:"yes"===e})},className:"flex space-x-8",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(c.C,{value:"yes",id:"instant-call-yes"}),(0,s.jsx)(l.J,{htmlFor:"instant-call-yes",children:"Yes"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(c.C,{value:"no",id:"instant-call-no"}),(0,s.jsx)(l.J,{htmlFor:"instant-call-no",children:"No"})]})]})]})]})}},17580:(e,a,t)=>{t.d(a,{A:()=>s});let s=(0,t(19946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},28054:(e,a,t)=>{t.d(a,{K_:()=>d,Nt:()=>u,SX:()=>i,am:()=>m,bi:()=>l,ge:()=>n,tm:()=>o,yi:()=>c});var s=t(12421);let r="http://localhost:4000";async function n(e){let a=e&&"all"!==e?"".concat(r,"/api/campaigns?status=").concat(e):"".concat(r,"/api/campaigns"),t=await (0,s.t)(a);if(!t.ok)throw Error("Failed to fetch campaigns");return await t.json()}async function l(e){let a=await (0,s.t)("".concat(r,"/api/campaigns"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok)throw Error("Failed to create campaign");return await a.json()}async function i(e,a){let t=await (0,s.t)("".concat(r,"/api/campaigns/").concat(e),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!t.ok)throw Error("Failed to update campaign");return await t.json()}async function c(e,a){let t=await (0,s.t)("".concat(r,"/api/campaigns/").concat(e,"/status"),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:a})});if(!t.ok)throw Error("Failed to update campaign status");return await t.json()}async function d(e){if(!(await (0,s.t)("".concat(r,"/api/campaigns/").concat(e),{method:"DELETE"})).ok)throw Error("Failed to delete campaign")}async function o(e){let a=await (0,s.t)("".concat(r,"/api/campaigns/").concat(e));if(!a.ok)throw Error("Failed to fetch campaign");return await a.json()}async function u(e){let a=await (0,s.t)("".concat(r,"/api/scheduled-call/remove-duplicates"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok)throw Error("Failed to remove duplicate calls");return await a.json()}async function m(e){let a=await (0,s.t)("".concat(r,"/api/scheduled-call/reschedule-campaign"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok)throw Error("Failed to reschedule campaign calls");return await a.json()}},30285:(e,a,t)=>{t.d(a,{$:()=>c,r:()=>i});var s=t(95155);t(12115);var r=t(99708),n=t(74466),l=t(59434);let i=(0,n.F)("inline-flex items-center cursor-pointer justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:a,variant:t,size:n,asChild:c=!1,...d}=e,o=c?r.DX:"button";return(0,s.jsx)(o,{"data-slot":"button",className:(0,l.cn)(i({variant:t,size:n,className:a})),...d})}},30356:(e,a,t)=>{t.d(a,{C:()=>c,z:()=>i});var s=t(95155);t(12115);var r=t(54059),n=t(9428),l=t(59434);function i(e){let{className:a,...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"radio-group",className:(0,l.cn)("grid gap-3",a),...t})}function c(e){let{className:a,...t}=e;return(0,s.jsx)(r.q7,{"data-slot":"radio-group-item",className:(0,l.cn)("border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...t,children:(0,s.jsx)(r.C1,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,s.jsx)(n.A,{className:"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2"})})})}},38164:(e,a,t)=>{t.d(a,{A:()=>s});let s=(0,t(19946).A)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},41526:(e,a,t)=>{t.d(a,{A:()=>u});var s=t(95155),r=t(12115),n=t(85057),l=t(91394),i=t(66695),c=t(40646),d=t(66766),o=t(62829);function u(e){var a;let{data:t,updateData:u,loading:m,userRole:h}=e,[x,p]=(0,r.useState)(t.agentId),f=e=>{p(e),u({agentId:e})},v=(0,r.useMemo)(()=>t.agents?"superadmin"===h?t.agents:t.agents.filter(e=>"active"===e.status):[],[t.agents,h]);return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(n.J,{children:"Select an agent for this campaign"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Choose one agent who will handle all the calls in this campaign"})]}),m?(0,s.jsx)("div",{className:"text-center py-8",children:"Loading agents..."}):t.agents&&0!==t.agents.length?(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4",children:v.map(e=>(0,s.jsxs)(i.Zp,{onClick:()=>f(e.id),className:"border rounded-lg p-4 flex items-center gap-3 cursor-pointer transition-all ".concat(x===e.id?"border-green-500 bg-green-50 dark:bg-green-900/20":"border-gray-200 dark:border-gray-700"," hover:border-green-300 dark:hover:border-green-600"),children:[(0,s.jsxs)(l.eu,{className:"h-12 w-12 flex-shrink-0",children:[e.avatar?(0,s.jsx)("img",{src:e.avatar,alt:"".concat(e.name," avatar"),className:"object-cover h-full w-full"}):(0,s.jsx)(d.default,{src:o.A,alt:"".concat(e.name," avatar"),width:64,height:64,className:"object-cover h-full w-full"}),(0,s.jsx)(l.q5,{className:"bg-gray-100",children:e.name.charAt(0)})]}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm font-medium truncate",children:e.name}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:e.role})]}),x===e.id&&(0,s.jsx)(c.A,{className:"h-5 w-5 text-green-500 flex-shrink-0"})]},e.id))}):(0,s.jsx)("div",{className:"text-center py-8 text-muted-foreground",children:"No agents available. Please create agents first."}),x&&t.agents&&(0,s.jsx)("div",{className:"mt-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-md border border-green-200 dark:border-green-800",children:(0,s.jsxs)("p",{className:"text-sm font-medium text-green-800 dark:text-green-400",children:["Selected agent: ",null===(a=t.agents.find(e=>e.id===x))||void 0===a?void 0:a.name]})})]})}},47262:(e,a,t)=>{t.d(a,{S:()=>i});var s=t(95155);t(12115);var r=t(14885),n=t(5196),l=t(59434);function i(e){let{className:a,...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...t,children:(0,s.jsx)(r.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(n.A,{className:"size-3.5"})})})}},57297:(e,a,t)=>{t.d(a,{HW:()=>n,J1:()=>i,_f:()=>l});var s=t(12421);let r="http://localhost:4000";async function n(){try{let e=await (0,s.t)("".concat(r,"/api/auth/me"),{method:"GET"});if(!e.ok)return{success:!1,error:"Error: ".concat(e.status)};let a=await e.json(),t=a.userId||a._id||a.id,n=a.email;if(t&&n)return{success:!0,user:{fullName:a.fullName||n.split("@")[0],userId:t,email:n,role:a.role||"user"}};return{success:!1,error:"Invalid user data received"}}catch(e){return console.error("Error fetching user data:",e),{success:!1,error:"An error occurred while fetching user data"}}}function l(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,a=setInterval(async()=>{if(localStorage.getItem("access_token"))try{await i()}catch(e){console.error("Background token refresh failed:",e)}},6e4*e);return()=>clearInterval(a)}async function i(){let e=localStorage.getItem("refresh_token");if(!e)return{success:!1};try{let a=await fetch("".concat(r,"/api/auth/refresh"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!a.ok)return{success:!1};let t=await a.json();if(t.access_token)return localStorage.setItem("access_token",t.access_token),{success:!0,newAccessToken:t.access_token};return{success:!1}}catch(e){return console.error("Token refresh error:",e),{success:!1}}}},59434:(e,a,t)=>{t.d(a,{cn:()=>n,v:()=>l});var s=t(52596),r=t(39688);function n(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,s.$)(a))}function l(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}},62829:(e,a,t)=>{t.d(a,{A:()=>s});let s={src:"/_next/static/media/Binghatti-Lisa.85c81ecb.jpeg",height:1586,width:1586,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/2wBDAQoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/wgARCAAIAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAX/xAAUAQEAAAAAAAAAAAAAAAAAAAAC/9oADAMBAAIQAxAAAACeA//EABsQAAEFAQEAAAAAAAAAAAAAAAECAwQREwAi/9oACAEBAAE/AG6e3khuoyZAbQNDWYR6SO//xAAVEQEBAAAAAAAAAAAAAAAAAAABAP/aAAgBAgEBPwAL/8QAFhEAAwAAAAAAAAAAAAAAAAAAAAFB/9oACAEDAQE/AHD/2Q==",blurWidth:8,blurHeight:8}},66695:(e,a,t)=>{t.d(a,{BT:()=>c,Wu:()=>d,ZB:()=>i,Zp:()=>n,aR:()=>l,wL:()=>o});var s=t(95155);t(12115);var r=t(59434);function n(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",a),...t})}function l(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("flex flex-col gap-1.5 px-6",a),...t})}function i(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",a),...t})}function c(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",a),...t})}function d(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",a),...t})}function o(e){let{className:a,...t}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6",a),...t})}},70598:(e,a,t)=>{t.d(a,{A:()=>y});var s=t(95155),r=t(12115),n=t(30285),l=t(85057),i=t(14636),c=t(85511),d=t(62523),o=t(54165),u=t(47262),m=t(16820),h=t(69074);let x=(0,t(19946).A)("Settings2",[["path",{d:"M20 7h-9",key:"3s1dr2"}],["path",{d:"M14 17H5",key:"gfn3mx"}],["circle",{cx:"17",cy:"17",r:"3",key:"18b49y"}],["circle",{cx:"7",cy:"7",r:"3",key:"dfmy0x"}]]);var p=t(62846),f=t(2488);let v=[{label:"Mon",value:"monday"},{label:"Tue",value:"tuesday"},{label:"Wed",value:"wednesday"},{label:"Thu",value:"thursday"},{label:"Fri",value:"friday"},{label:"Sat",value:"saturday"},{label:"Sun",value:"sunday"}];function y(e){var a,t,y,g,j,N,b,A;let{data:w,updateData:k}=e,[C,S]=(0,r.useState)(()=>{if(w.startDate){let e=new Date(w.startDate);return isNaN(e.getTime())?new Date:e}return new Date}),[T,E]=(0,r.useState)((null===(a=w.callSchedule)||void 0===a?void 0:a.timezone)||"America/New_York"),[D,F]=(0,r.useState)((null===(t=w.callSchedule)||void 0===t?void 0:t.callTime)||"09:00"),[z,I]=(0,r.useState)(()=>{if(w.endDate){let e=new Date(w.endDate);return isNaN(e.getTime())?new Date(Date.now()+6048e5):e}return new Date(Date.now()+6048e5)}),[_,J]=(0,r.useState)((null===(y=w.callSchedule)||void 0===y?void 0:y.startTime)||"09:00"),[W,M]=(0,r.useState)((null===(g=w.callSchedule)||void 0===g?void 0:g.endTime)||"17:00"),[O,P]=(0,r.useState)(!1),[B,H]=(0,r.useState)(null),[L,Q]=(0,r.useState)(w.followUpDays||["monday","tuesday","wednesday","thursday","friday"]),[R,U]=(0,r.useState)((null===(j=w.callSchedule)||void 0===j?void 0:j.callTime)||"09:00"),[G,$]=(0,r.useState)(!!w.endDate),[V,Z]=(0,r.useState)((null===(N=w.callWindow)||void 0===N?void 0:N.daysOfWeek)||["monday","tuesday","wednesday","thursday","friday"]),[q,X]=(0,r.useState)((null===(b=w.callWindow)||void 0===b?void 0:b.startTime)||"09:00"),[Y,K]=(0,r.useState)((null===(A=w.callWindow)||void 0===A?void 0:A.endTime)||"17:00"),ee=w.followUpDays||["monday","tuesday","wednesday","thursday","friday"],ea=e=>{let a=new Date,t=new Date;t.setDate(t.getDate()+e),S(a),I(t),H(e),G||$(!0),et(a,t,_,W)},et=(e,a,t,s)=>{var r;k({startDate:(0,p.GP)(e,"yyyy-MM-dd'T'HH:mm"),endDate:a?(0,p.GP)(a,"yyyy-MM-dd'T'HH:mm"):null,callSchedule:{startTime:t,endTime:s,timezone:T,daysOfWeek:(null===(r=w.callSchedule)||void 0===r?void 0:r.daysOfWeek)||["monday","tuesday","wednesday","thursday","friday"],callTime:D}})},es=(e,a)=>{Q(t=>a?[...t,e]:t.filter(a=>a!==e))},er=e=>{let a=[];"weekdays"===e?a=["monday","tuesday","wednesday","thursday","friday"]:"weekend"===e?a=["saturday","sunday"]:"everyday"===e&&(a=["monday","tuesday","wednesday","thursday","friday","saturday","sunday"]),Z(a),k({callWindow:{startTime:q,endTime:Y,daysOfWeek:a}})},en=e=>{"weekdays"===e?Q(["monday","tuesday","wednesday","thursday","friday"]):"weekend"===e?Q(["saturday","sunday"]):"everyday"===e&&Q(["monday","tuesday","wednesday","thursday","friday","saturday","sunday"])},el=(e,a)=>{let t=a?[...V,e].filter((e,a,t)=>t.indexOf(e)===a):V.filter(a=>a!==e);Z(t),k({callWindow:{startTime:q,endTime:Y,daysOfWeek:t}})};return(0,r.useEffect)(()=>{w.startDate||et(C,G?z:null,_,W)},[]),(0,r.useEffect)(()=>{O&&(Q(ee),U(D))},[O,ee,D]),(0,s.jsxs)("div",{className:"space-y-10",children:[(0,s.jsxs)("div",{className:"space-y-8 ",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(l.J,{className:"text-base font-medium",children:"Campaign Duration"}),(0,s.jsx)("div",{className:"flex ",children:G&&(0,s.jsx)(f.default,{duration:.5,delay:.1,children:(0,s.jsx)("div",{className:"flex gap-2",children:[{label:"1 Week",value:7},{label:"2 Weeks",value:14},{label:"1 Month",value:30},{label:"3 Months",value:90}].map(e=>(0,s.jsx)(n.$,{variant:B===e.value?"default":"outline",size:"sm",onClick:()=>ea(e.value),children:e.label},e.value))})})})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{htmlFor:"timezone",children:"Timezone"}),(0,s.jsx)("div",{className:"flex space-x-2",children:(0,s.jsx)(m.N,{value:T,onChange:e=>{var a,t,s;E(e),k({callSchedule:{startTime:(null===(a=w.callSchedule)||void 0===a?void 0:a.startTime)||"09:00",endTime:(null===(t=w.callSchedule)||void 0===t?void 0:t.endTime)||"17:00",timezone:e,daysOfWeek:(null===(s=w.callSchedule)||void 0===s?void 0:s.daysOfWeek)||["monday","tuesday","wednesday","thursday","friday"],callTime:D}})}})})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{children:"Start Date & Time"}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)(i.AM,{children:[(0,s.jsx)(i.Wv,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"outline",className:"flex-1 justify-start text-left font-normal",children:[(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4"}),C?(0,p.GP)(C,"PPP"):"Select date"]})}),(0,s.jsx)(i.hl,{className:"w-auto p-0",align:"start",children:(0,s.jsx)(c.V,{mode:"single",selected:C,onSelect:e=>{e&&(S(e),H(null),G?et(e,z,_,W):k({endDate:null}))},initialFocus:!0})})]}),(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(d.p,{type:"time",value:_,onChange:e=>{let a=e.target.value;a&&a.includes(":")||(a="00:00"),J(a),et(C,z,a,W)},className:"w-24"})})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(u.S,{id:"hasEndDate",checked:G,onCheckedChange:e=>{if($(e),e)et(C,z,_,W);else{var a;k({endDate:null,callSchedule:{startTime:_,endTime:W,timezone:T,daysOfWeek:(null===(a=w.callSchedule)||void 0===a?void 0:a.daysOfWeek)||["monday","tuesday","wednesday","thursday","friday"],callTime:D}})}},className:"cursor-pointer"}),(0,s.jsx)(l.J,{htmlFor:"hasEndDate",className:"cursor-pointer",children:"Set End Date & Time"})]}),G?(0,s.jsxs)("div",{className:"flex space-x-2 mt-2",children:[(0,s.jsxs)(i.AM,{children:[(0,s.jsx)(i.Wv,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"outline",className:"flex-1 justify-start text-left font-normal",children:[(0,s.jsx)(h.A,{className:"mr-2 h-4 w-4"}),z?(0,p.GP)(z,"PPP"):"Select date"]})}),(0,s.jsx)(i.hl,{className:"w-auto p-0",align:"start",children:(0,s.jsx)(c.V,{mode:"single",selected:z,onSelect:e=>{e&&(I(e),H(null),et(C,e,_,W))},initialFocus:!0})})]}),(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)(d.p,{type:"time",value:W,onChange:e=>{let a=e.target.value;a&&a.includes(":")||(a="00:00"),M(a),et(C,z,_,a)},className:"w-24"})})]}):(0,s.jsx)("div",{className:"text-sm text-muted-foreground mt-2",children:"Campaign will run indefinitely"})]})]})]}),(0,s.jsxs)("div",{className:"space-y-6 mt-5",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center ",children:[(0,s.jsx)(l.J,{className:"text-base font-medium",children:"Call Window"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>er("weekdays"),children:"Weekdays"}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>er("weekend"),children:"Weekend"}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>er("everyday"),children:"Every Day"})]})]}),(0,s.jsxs)("div",{className:"space-y-4 border rounded-md p-4 bg-muted/30",children:[(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsx)(l.J,{children:"Days of the Week"}),(0,s.jsx)("div",{className:"grid grid-cols-7 gap-2",children:v.map(e=>(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-1.5",children:[(0,s.jsx)(u.S,{id:"window-".concat(e.value),checked:V.includes(e.value),onCheckedChange:a=>el(e.value,a),className:"h-6 w-6 cursor-pointer"}),(0,s.jsx)(l.J,{htmlFor:"window-".concat(e.value),className:"text-xs cursor-pointer",children:e.label})]},"window-".concat(e.value)))})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{children:"Start Time"}),(0,s.jsx)(d.p,{type:"time",value:q,onChange:e=>{let a=e.target.value;X(a),k({callWindow:{startTime:a,endTime:Y,daysOfWeek:V}})}})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(l.J,{children:"End Time"}),(0,s.jsx)(d.p,{type:"time",value:Y,onChange:e=>{let a=e.target.value;K(a),k({callWindow:{startTime:q,endTime:a,daysOfWeek:V}})}})]})]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["Calls will only be made during these hours on the selected days in the ",T," timezone."]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(l.J,{className:"text-base font-medium",children:"Follow-up Schedule"}),(0,s.jsxs)(o.lG,{open:O,onOpenChange:P,children:[(0,s.jsx)(o.zM,{asChild:!0,children:(0,s.jsxs)(n.$,{variant:"outline",size:"sm",children:[(0,s.jsx)(x,{className:"mr-2 h-4 w-4"}),"Configure Recurring"]})}),(0,s.jsxs)(o.Cf,{children:[(0,s.jsx)(o.c7,{children:(0,s.jsx)(o.L3,{children:"Configure Recurring Schedule"})}),(0,s.jsxs)("div",{className:"space-y-4 py-4",children:[(0,s.jsxs)("div",{className:"space-y-7",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(l.J,{children:"Days of the Week"}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>en("weekdays"),children:"Weekdays"}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>en("weekend"),children:"Weekend"}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>en("everyday"),children:"Every Day"})]})]}),(0,s.jsx)("div",{className:"grid grid-cols-7 gap-2",children:v.map(e=>(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-1.5",children:[(0,s.jsx)(u.S,{id:e.value,checked:L.includes(e.value),onCheckedChange:a=>es(e.value,a),className:"h-6 w-6"}),(0,s.jsx)(l.J,{htmlFor:e.value,className:"text-xs cursor-pointer",children:e.label})]},e.value))})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(l.J,{children:"Call Time"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(d.p,{type:"time",value:R,onChange:e=>{let a=e.target.value;a&&a.includes(":")||(a="00:00"),U(a)},className:"w-22"}),(0,s.jsxs)("span",{className:"text-sm text-gray-500",children:["in ",T]})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Calls will be initiated at this time on the selected days."})]})]}),(0,s.jsxs)(o.Es,{children:[(0,s.jsx)(n.$,{variant:"outline",onClick:()=>{Q(ee),U(D),P(!1)},children:"Cancel"}),(0,s.jsx)(n.$,{onClick:()=>{var e,a,t;k({followUpDays:L,callSchedule:{startTime:(null===(e=w.callSchedule)||void 0===e?void 0:e.startTime)||"09:00",endTime:(null===(a=w.callSchedule)||void 0===a?void 0:a.endTime)||"17:00",timezone:T,daysOfWeek:(null===(t=w.callSchedule)||void 0===t?void 0:t.daysOfWeek)||["monday","tuesday","wednesday","thursday","friday"],callTime:R}}),F(R),P(!1)},children:"Save"})]})]})]})]}),(0,s.jsxs)("div",{className:"p-4 border rounded-md bg-muted/50",children:[(0,s.jsx)("div",{className:"text-sm font-medium mb-2",children:(()=>{if(7===ee.length)return"Occurs every day";if(0===ee.length)return"No recurrence set";if(5===ee.length&&ee.includes("monday")&&ee.includes("tuesday")&&ee.includes("wednesday")&&ee.includes("thursday")&&ee.includes("friday"))return"Occurs every weekday";{if(2===ee.length&&ee.includes("saturday")&&ee.includes("sunday"))return"Occurs every weekend";let e=ee.map(e=>e.charAt(0).toUpperCase()+e.slice(1));return"Occurs every ".concat(e.join(", "))}})()}),(0,s.jsxs)("div",{className:"mt-2 text-sm",children:["Calls scheduled for (",T,")"]})]})]})]})}},76202:(e,a,t)=>{t.d(a,{A:()=>i});var s=t(95155),r=t(12115),n=t(54073),l=t(59434);function i(e){let{className:a,defaultValue:t,value:i,min:c=0,max:d=100,...o}=e,u=r.useMemo(()=>Array.isArray(i)?i:Array.isArray(t)?t:[c,d],[i,t,c,d]);return(0,s.jsxs)(n.bL,{"data-slot":"slider",defaultValue:t,value:i,min:c,max:d,className:(0,l.cn)("relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",a),...o,children:[(0,s.jsx)(n.CC,{"data-slot":"slider-track",className:(0,l.cn)("bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),children:(0,s.jsx)(n.Q6,{"data-slot":"slider-range",className:(0,l.cn)("bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full")})}),Array.from({length:u.length},(e,a)=>(0,s.jsx)(n.zi,{"data-slot":"slider-thumb",className:"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"},a))]})}},85057:(e,a,t)=>{t.d(a,{J:()=>l});var s=t(95155);t(12115);var r=t(40968),n=t(59434);function l(e){let{className:a,...t}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...t})}},85511:(e,a,t)=>{t.d(a,{V:()=>d});var s=t(95155);t(12115);var r=t(42355),n=t(13052),l=t(29746),i=t(59434),c=t(30285);function d(e){let{className:a,classNames:t,showOutsideDays:d=!0,...o}=e;return(0,s.jsx)(l.hv,{showOutsideDays:d,className:(0,i.cn)("p-3",a),classNames:{months:"flex flex-col sm:flex-row gap-2",month:"flex flex-col gap-4",caption:"flex justify-center pt-1 relative items-center w-full",caption_label:"text-sm font-medium",nav:"flex items-center gap-1",nav_button:(0,i.cn)((0,c.r)({variant:"outline"}),"size-7 bg-transparent p-0 opacity-50 hover:opacity-100"),nav_button_previous:"absolute left-1",nav_button_next:"absolute right-1",table:"w-full border-collapse space-x-1",head_row:"flex",head_cell:"text-muted-foreground rounded-md w-8 font-normal text-[0.8rem]",row:"flex w-full mt-2",cell:(0,i.cn)("relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&:has([aria-selected])]:bg-accent [&:has([aria-selected].day-range-end)]:rounded-r-md","range"===o.mode?"[&:has(>.day-range-end)]:rounded-r-md [&:has(>.day-range-start)]:rounded-l-md first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md":"[&:has([aria-selected])]:rounded-md"),day:(0,i.cn)((0,c.r)({variant:"ghost"}),"size-8 p-0 font-normal aria-selected:opacity-100"),day_range_start:"day-range-start aria-selected:bg-primary aria-selected:text-primary-foreground",day_range_end:"day-range-end aria-selected:bg-primary aria-selected:text-primary-foreground",day_selected:"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground",day_today:"bg-accent text-accent-foreground",day_outside:"day-outside text-muted-foreground aria-selected:text-muted-foreground",day_disabled:"text-muted-foreground opacity-50",day_range_middle:"aria-selected:bg-accent aria-selected:text-accent-foreground",day_hidden:"invisible",...t},components:{IconLeft:e=>{let{className:a,...t}=e;return(0,s.jsx)(r.A,{className:(0,i.cn)("size-4",a),...t})},IconRight:e=>{let{className:a,...t}=e;return(0,s.jsx)(n.A,{className:(0,i.cn)("size-4",a),...t})}},...o})}},85977:(e,a,t)=>{t.d(a,{H4:()=>N,_V:()=>j,bL:()=>g});var s=t(12115),r=t(46081),n=t(39033),l=t(52712),i=t(63655),c=t(95155),d="Avatar",[o,u]=(0,r.A)(d),[m,h]=o(d),x=s.forwardRef((e,a)=>{let{__scopeAvatar:t,...r}=e,[n,l]=s.useState("idle");return(0,c.jsx)(m,{scope:t,imageLoadingStatus:n,onImageLoadingStatusChange:l,children:(0,c.jsx)(i.sG.span,{...r,ref:a})})});x.displayName=d;var p="AvatarImage",f=s.forwardRef((e,a)=>{let{__scopeAvatar:t,src:r,onLoadingStatusChange:d=()=>{},...o}=e,u=h(p,t),m=function(e,a){let[t,r]=s.useState("idle");return(0,l.N)(()=>{if(!e){r("error");return}let t=!0,s=new window.Image,n=e=>()=>{t&&r(e)};return r("loading"),s.onload=n("loaded"),s.onerror=n("error"),s.src=e,a&&(s.referrerPolicy=a),()=>{t=!1}},[e,a]),t}(r,o.referrerPolicy),x=(0,n.c)(e=>{d(e),u.onImageLoadingStatusChange(e)});return(0,l.N)(()=>{"idle"!==m&&x(m)},[m,x]),"loaded"===m?(0,c.jsx)(i.sG.img,{...o,ref:a,src:r}):null});f.displayName=p;var v="AvatarFallback",y=s.forwardRef((e,a)=>{let{__scopeAvatar:t,delayMs:r,...n}=e,l=h(v,t),[d,o]=s.useState(void 0===r);return s.useEffect(()=>{if(void 0!==r){let e=window.setTimeout(()=>o(!0),r);return()=>window.clearTimeout(e)}},[r]),d&&"loaded"!==l.imageLoadingStatus?(0,c.jsx)(i.sG.span,{...n,ref:a}):null});y.displayName=v;var g=x,j=f,N=y},91394:(e,a,t)=>{t.d(a,{BK:()=>i,eu:()=>l,q5:()=>c});var s=t(95155);t(12115);var r=t(85977),n=t(59434);function l(e){let{className:a,...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"avatar",className:(0,n.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",a),...t})}function i(e){let{className:a,...t}=e;return(0,s.jsx)(r._V,{"data-slot":"avatar-image",className:(0,n.cn)("aspect-square size-full",a),...t})}function c(e){let{className:a,...t}=e;return(0,s.jsx)(r.H4,{"data-slot":"avatar-fallback",className:(0,n.cn)("bg-muted flex size-full items-center justify-center rounded-full",a),...t})}}}]);