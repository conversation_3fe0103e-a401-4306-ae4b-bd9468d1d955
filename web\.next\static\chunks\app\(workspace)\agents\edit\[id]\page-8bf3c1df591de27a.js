(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[257],{6108:(e,a,s)=>{"use strict";s.d(a,{a:()=>c});var t=s(26715),r=s(32960),l=s(5041);async function n(){let e=localStorage.getItem("access_token"),a=await fetch("".concat("http://localhost:4000","/api/agents"),{headers:{Authorization:"Bearer ".concat(e)}});if(!a.ok)throw Error("Failed to fetch agents");return a.json()}function c(){let e=(0,t.jE)(),{data:a=[],isLoading:s,error:c}=(0,r.I)({queryKey:["agents"],queryFn:n,staleTime:1/0,gcTime:1/0}),{mutate:i}=(0,l.n)({mutationFn:e=>Promise.resolve(e),onSuccess:a=>{e.setQueryData(["agents"],a)}});return{agents:a,setAgents:i,agentsisLoading:s,deleteAgentMutation:(0,l.n)({mutationFn:async e=>{let a=localStorage.getItem("access_token");if(!a)throw Error("Authentication required");let s=await fetch("".concat("http://localhost:4000","/api/agents/").concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(a)}});if(!s.ok)throw Error("Failed to delete agent: ".concat(s.status))},onMutate:async a=>{await e.cancelQueries({queryKey:["agents"]});let s=e.getQueryData(["agents"]);return e.setQueryData(["agents"],e=>{var s;return null!==(s=null==e?void 0:e.filter(e=>e.id!==a))&&void 0!==s?s:[]}),{previousAgents:s}},onSuccess:(a,s)=>{e.invalidateQueries({queryKey:["agents"]}),e.removeQueries({queryKey:["agent",s]})}}),AgentsError:c instanceof Error?c.message:null}}},11635:(e,a,s)=>{Promise.resolve().then(s.bind(s,37766))},37766:(e,a,s)=>{"use strict";s.d(a,{default:()=>z});var t=s(95155),r=s(12115),l=s(35695),n=s(35169),c=s(25657),i=s(40646),o=s(85339),d=s(30285),m=s(17313),u=s(38816),h=s(21773),x=s(88006),g=s(54165),p=s(66681),v=s(19940),b=s(26083),f=s(29281),j=s(79167),N=s(91394);function y(e){let{currentAgentId:a,userRole:s,agents:r,agentsisLoading:n}=e,c=(0,l.useRouter)(),i=r.filter(e=>"superadmin"===s||"active"===e.status),o=e=>{e!==a&&c.push("/agents/edit/".concat(e))};return(0,t.jsxs)("div",{className:"sticky top-4 w-80 h-[calc(100vh-5rem)] min-h-[calc(100vh-4rem)] border-1 border-border bg-card rounded-lg mt-2",children:[(0,t.jsx)("div",{className:"p-4 border-b-2 flex items-center gap-2",children:(0,t.jsx)("h2",{className:"font-semibold text-lg",children:"Agents"})}),(0,t.jsx)("div",{className:"overflow-y-auto h-[calc(100%-4rem)] scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600",children:n?[,,,,,].fill(0).map((e,a)=>(0,t.jsx)("div",{className:"p-3 border-b",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse"}),(0,t.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,t.jsx)("div",{className:"h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"}),(0,t.jsx)("div",{className:"h-3 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"})]})]})},a)):i.map(e=>(0,t.jsx)("div",{onClick:()=>o(e.id),className:"p-3 border-b hover:bg-accent/50 transition-colors cursor-pointer ".concat(e.id===a?"bg-accent":""),children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)(N.eu,{className:"h-15 w-15",children:[(0,t.jsx)(N.BK,{src:e.avatar}),(0,t.jsx)(N.q5,{children:e.name.slice(0,2).toUpperCase()})]}),(0,t.jsx)("div",{className:"absolute bottom-0 right-0 h-2.5 w-2.5 rounded-full border-2 border-white dark:border-gray-800 ".concat("active"===e.status?"bg-green-500":"bg-gray-400")})]}),(0,t.jsxs)("div",{className:"flex flex-col min-w-0",children:[(0,t.jsx)("span",{className:"font-medium truncate text-base",children:e.name}),(0,t.jsx)("span",{className:"text-sm truncate text-gray-700 dark:text-gray-200",children:e.role||"Assistant"})]})]})},e.id))})]})}var w=s(6108),k=s(80244),A=s(59434);function C(e){let{className:a,children:s,...r}=e;return(0,t.jsxs)(k.bL,{"data-slot":"scroll-area",className:(0,A.cn)("relative",a),...r,children:[(0,t.jsx)(k.LM,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:s}),(0,t.jsx)(E,{}),(0,t.jsx)(k.OK,{})]})}function E(e){let{className:a,orientation:s="vertical",...r}=e;return(0,t.jsx)(k.VM,{"data-slot":"scroll-area-scrollbar",orientation:s,className:(0,A.cn)("flex touch-none p-px transition-colors select-none","vertical"===s&&"h-full w-2.5 border-l border-l-transparent","horizontal"===s&&"h-2.5 flex-col border-t border-t-transparent",a),...r,children:(0,t.jsx)(k.lr,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}let S=[{value:"profile",label:"Profile",component:u.A},{value:"prompt",label:"Prompt",component:h.A},{value:"voice",label:"Voice",component:b.A},{value:"brain",label:"Brain",component:f.A},{value:"actions",label:"Actions",component:x.A},{value:"advanced",label:"Advanced",component:j.A}];function z(e){let{agentId:a}=e,s=(0,l.useRouter)(),u=(0,l.useParams)(),[h,x]=(0,r.useState)("profile"),b=a||u.id,[f,j]=(0,r.useState)(!1),[k,A]=(0,r.useState)(!1),[E,z]=(0,r.useState)(!1),[q,F]=(0,r.useState)(null),{userRole:_}=(0,p.A)(),{agent:B,setAgent:L,phoneNumbers:K,agentIsLoading:I,updateAgentMutation:$}=(0,v.f)(b),{agents:O,agentsisLoading:Q}=(0,w.a)();(0,r.useEffect)(()=>{b||s.push("/agents")},[b,s]);let D=async()=>{if(B){A(!0),F(null);try{await $.mutateAsync(B),z(!0)}catch(e){console.error("Error updating agent:",e),F(e instanceof Error?e.message:"Failed to update agent")}finally{A(!1)}}},M=async()=>{if(!B)return;let e="active"===B.status?"inactive":"active";try{await $.mutateAsync({...B,status:e})}catch(e){console.error("Error updating agent status:",e),F(e instanceof Error?e.message:"Failed to update agent status")}},P=e=>{e!==b&&s.push("/agents/edit/".concat(e)),j(!1)};return(0,t.jsx)(t.Fragment,{children:(0,t.jsxs)("div",{className:"min-h-screen bg-gray-50/50 dark:bg-gray-900/50 ",children:[(0,t.jsx)("div",{className:"w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,t.jsxs)("div",{className:"container flex h-14 sm:h-16 items-center px-2 sm:px-4",children:[(0,t.jsxs)(d.$,{variant:"ghost",size:"icon",onClick:()=>s.push("/agents"),className:"mr-4 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800",children:[(0,t.jsx)(n.A,{className:"h-4 w-4 sm:h-5 sm:w-5"}),(0,t.jsx)("span",{className:"sr-only",children:"Back"})]}),(0,t.jsx)("h1",{className:"text-lg sm:text-xl md:text-2xl font-bold    tracking-tight truncate",children:null==B?void 0:B.name})]})}),(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row",children:[(0,t.jsx)("div",{className:"hidden xl:block",children:(0,t.jsx)(y,{currentAgentId:b,userRole:_,agents:O,agentsisLoading:Q})}),(0,t.jsx)("div",{className:"flex-1 min-h-[calc(100vh-4rem)]",children:I?(0,t.jsx)("div",{className:"container py-4 px-4",children:(0,t.jsxs)("div",{className:"animate-pulse",children:[(0,t.jsx)("div",{className:"h-8 w-64 bg-gray-200 dark:bg-gray-700 rounded mb-4"}),(0,t.jsx)("div",{className:"h-4 w-48 bg-gray-200 dark:bg-gray-700 rounded"}),(0,t.jsxs)("div",{className:"mt-8 space-y-4",children:[(0,t.jsx)("div",{className:"h-12 bg-gray-200 dark:bg-gray-700 rounded"}),(0,t.jsx)("div",{className:"h-64 bg-gray-200 dark:bg-gray-700 rounded"})]})]})}):B&&(0,t.jsxs)("div",{className:"p-3 sm:p-4 md:p-6 max-w-full md:max-w-5xl mx-auto",children:[(0,t.jsxs)(m.tU,{value:h,onValueChange:x,className:"space-y-4",children:[(0,t.jsx)("div",{className:"border-b overflow-x-auto scrollbar-hide",children:(0,t.jsx)(m.j7,{className:"w-full justify-start h-auto bg-transparent p-0 flex",children:S.map(e=>(0,t.jsx)(m.Xi,{value:e.value,className:"data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none rounded-none px-2 sm:px-4 py-2 sm:py-3 bg-transparent text-xs sm:text-sm whitespace-nowrap",children:e.label},e.value))})}),(0,t.jsx)("div",{className:"bg-card rounded-lg border shadow-sm",children:S.map(e=>(0,t.jsx)(m.av,{value:e.value,className:"m-0 focus-visible:outline-none focus-visible:ring-0",children:(0,t.jsx)(e.component,{agent:B,setAgent:L,phoneNumbers:K,isCreateMode:!1})},e.value))})]}),(0,t.jsx)("div",{className:"mt-4 sm:mt-6 flex justify-end",children:(0,t.jsx)(d.$,{onClick:D,disabled:k,className:"bg-black text-white dark:text-black dark:bg-white hover:from-purple-700 hover:to-blue-600 w-full sm:w-auto",children:k?"Saving...":"Save Changes"})}),"superadmin"===_&&(0,t.jsxs)("div",{className:"mt-6 sm:mt-8 flex flex-col sm:flex-row sm:items-center justify-between py-3 sm:py-4 px-4 sm:px-6 bg-muted rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-3 sm:mb-0",children:[(0,t.jsx)("div",{className:"h-3 w-3 rounded-full ".concat("active"===B.status?"bg-green-500":"bg-gray-400")}),(0,t.jsx)("span",{className:"text-sm",children:"active"===B.status?"Active":"Inactive"})]}),(0,t.jsx)(d.$,{variant:"outline",size:"sm",onClick:M,children:"active"===B.status?"Deactivate":"Activate"})]}),(0,t.jsx)("div",{className:"fixed bottom-6 right-6 xl:hidden z-50",children:(0,t.jsx)(d.$,{onClick:()=>j(!0),size:"icon",className:"h-12 w-12 rounded-full shadow-lg bg-primary hover:bg-primary/90",children:(0,t.jsx)(c.A,{className:"h-6 w-6"})})}),(0,t.jsx)(g.lG,{open:f,onOpenChange:j,children:(0,t.jsxs)(g.Cf,{className:"sm:max-w-md",children:[(0,t.jsx)(g.c7,{children:(0,t.jsxs)(g.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(c.A,{className:"h-5 w-5"}),"Select Agent"]})}),Q?(0,t.jsx)("div",{className:"py-4 flex justify-center",children:(0,t.jsx)("div",{className:"animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"})}):(0,t.jsx)(C,{className:"h-[50vh] sm:h-[40vh] pr-4",children:(0,t.jsx)("div",{className:"space-y-1",children:O.map(e=>(0,t.jsxs)("div",{onClick:()=>P(e.id),className:"flex items-center gap-3 p-2 rounded-md cursor-pointer transition-colors\n                      ".concat(e.id===b?"bg-primary/10 text-primary":"hover:bg-muted"),children:[(0,t.jsxs)(N.eu,{className:"h-9 w-9",children:[(0,t.jsx)(N.BK,{src:e.avatar||"",alt:e.name}),(0,t.jsx)(N.q5,{className:"bg-primary/10 text-primary",children:e.name.charAt(0).toUpperCase()})]}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("p",{className:"font-medium truncate",children:e.name}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground truncate",children:e.role})]}),e.id===b&&(0,t.jsx)("div",{className:"h-2 w-2 rounded-full bg-primary"})]},e.id))})})]})}),(0,t.jsx)(g.lG,{open:E,onOpenChange:z,children:(0,t.jsxs)(g.Cf,{className:"sm:max-w-md",children:[(0,t.jsx)(g.c7,{children:(0,t.jsxs)(g.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(i.A,{className:"h-5 w-5 text-green-500"}),"Agent Updated Successfully"]})}),(0,t.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Your changes to ",null==B?void 0:B.name," have been saved."]}),(0,t.jsxs)(g.Es,{className:"flex gap-2 sm:justify-start",children:[(0,t.jsx)(d.$,{variant:"outline",onClick:()=>z(!1),children:"Continue Editing"}),(0,t.jsx)(d.$,{onClick:()=>s.push("/agents"),className:"bg-black text-white dark:text-black dark:bg-white",children:"Back to Agents"})]})]})}),q&&(0,t.jsx)(g.lG,{open:!!q,onOpenChange:()=>F(null),children:(0,t.jsxs)(g.Cf,{className:"sm:max-w-md",children:[(0,t.jsx)(g.c7,{children:(0,t.jsxs)(g.L3,{className:"flex items-center gap-2 text-red-500",children:[(0,t.jsx)(o.A,{className:"h-5 w-5"}),"Error Updating Agent"]})}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:q}),(0,t.jsx)(g.Es,{children:(0,t.jsx)(d.$,{variant:"outline",onClick:()=>F(null),children:"Close"})})]})})]})})]})]})})}},66681:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});var t=s(12115);function r(){let[e,a]=(0,t.useState)(null),[s,r]=(0,t.useState)(!0),[l,n]=(0,t.useState)(null);(0,t.useEffect)(()=>{!async function(){try{let e=localStorage.getItem("access_token");if(!e)throw Error("No access token available");let s=await fetch("".concat("http://localhost:4000","/api/auth/me"),{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!s.ok)throw Error("Failed to fetch user profile: ".concat(s.status));let t=await s.json();a({email:t.email,fullName:t.fullName,role:t.role})}catch(e){n(e instanceof Error?e.message:"Failed to load user profile")}finally{r(!1)}}()},[]);let c=(null==e?void 0:e.role)||null;return{user:e,userRole:c,authIsLoading:s,authError:l}}},91394:(e,a,s)=>{"use strict";s.d(a,{BK:()=>c,eu:()=>n,q5:()=>i});var t=s(95155);s(12115);var r=s(85977),l=s(59434);function n(e){let{className:a,...s}=e;return(0,t.jsx)(r.bL,{"data-slot":"avatar",className:(0,l.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",a),...s})}function c(e){let{className:a,...s}=e;return(0,t.jsx)(r._V,{"data-slot":"avatar-image",className:(0,l.cn)("aspect-square size-full",a),...s})}function i(e){let{className:a,...s}=e;return(0,t.jsx)(r.H4,{"data-slot":"avatar-fallback",className:(0,l.cn)("bg-muted flex size-full items-center justify-center rounded-full",a),...s})}}},e=>{var a=a=>e(e.s=a);e.O(0,[2111,7540,4201,4341,6403,1071,6671,6544,6766,3860,7812,9735,6910,6425,9621,4194,8441,1684,7358],()=>a(11635)),_N_E=e.O()}]);