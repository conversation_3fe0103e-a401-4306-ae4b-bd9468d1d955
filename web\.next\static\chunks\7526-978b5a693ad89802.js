"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7526],{4672:(e,t,o)=>{o.d(t,{n:()=>l,I:()=>d});var r=o(95155),a=o(12115),n=o(7524),i=o(14298),s=o(56671);let c=(0,a.createContext)(void 0);function l(e){let{children:t,creditThreshold:o=1}=e,[l,d]=(0,a.useState)(0),[u,g]=(0,a.useState)(0),[f,m]=(0,a.useState)(0),[h,v]=(0,a.useState)(0),[p,b]=(0,a.useState)(0),[w,k]=(0,a.useState)(!1),[y,S]=(0,a.useState)(0),[C,x]=(0,a.useState)(0),[A,E]=(0,a.useState)(0),[z,N]=(0,a.useState)(.1),[I,M]=(0,a.useState)(0),[R,j]=(0,a.useState)(1),[P,_]=(0,a.useState)(!0),[F,D]=(0,a.useState)(null),[T,O]=(0,a.useState)(null),[U,B]=(0,a.useState)(!1),[W,J]=(0,a.useState)(null),[L,H]=(0,a.useState)(0),{isConnected:K,on:q,registerUser:Y}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",[t,o]=(0,a.useState)(!1),r=(0,a.useRef)(null),[n,s]=(0,a.useState)(null);(0,a.useEffect)(()=>{try{let t="".concat("http://localhost:4000").concat(e?"/".concat(e):"");console.log("Connecting to WebSocket at: ".concat(t)),r.current&&(console.log("Disconnecting existing socket before creating a new one"),r.current.disconnect());let a=(0,i.io)(t,{transports:["websocket","polling"],autoConnect:!0,reconnection:!0,reconnectionAttempts:10,reconnectionDelay:1e3,timeout:2e4,forceNew:!0,path:"/socket.io/"});a.on("connect",()=>{console.log("WebSocket connected to ".concat(e||"default"," namespace")),o(!0),s(null)}),a.on("disconnect",t=>{console.log("WebSocket disconnected from ".concat(e||"default"," namespace: ").concat(t)),o(!1)}),a.on("connect_error",e=>{console.error("WebSocket connection error: ".concat(e.message)),s("Connection error: ".concat(e.message));try{var t,o,r;(null===(r=a.io)||void 0===r?void 0:null===(o=r.opts)||void 0===o?void 0:null===(t=o.transports)||void 0===t?void 0:t[0])==="websocket"&&(console.log("Switching to polling transport due to connection error"),a.io.opts.transports=["polling","websocket"])}catch(e){console.error("Error switching transports:",e)}setTimeout(()=>{console.log("Attempting to reconnect manually..."),a.connect()},3e3)});let n=setInterval(()=>{a.connected&&(console.log("Sending ping to server"),a.emit("ping",{},e=>{console.log("Received pong from server:",e)}))},3e4);return r.current=a,()=>{clearInterval(n),a&&(console.log("Disconnecting from WebSocket at: ".concat(t)),a.disconnect(),r.current=null)}}catch(e){return console.error("Error setting up WebSocket connection:",e),()=>{}}},[e]);let c=(0,a.useCallback)((e,o,a)=>!!r.current&&!!t&&(a?r.current.emit(e,o,a):r.current.emit(e,o),!0),[t]),l=(0,a.useCallback)((e,t)=>r.current?(r.current.on(e,t),()=>{var o;null===(o=r.current)||void 0===o||o.off(e,t)}):()=>{},[]),d=(0,a.useCallback)(e=>e?r.current&&t?(console.log("Registering user ".concat(e," with socket ").concat(r.current.id)),r.current.emit("registerUser",{userId:e},t=>{t&&t.success?console.log("User ".concat(e," successfully registered with socket: ").concat(t.message)):console.error("Failed to register user ".concat(e," with socket:"),t)}),!0):(console.warn("Cannot register user ".concat(e,": socket is ").concat(r.current?"created":"not created"," and connection is ").concat(t?"active":"inactive")),r.current&&!t&&(console.log("Attempting to connect socket before registering user"),r.current.connect(),setTimeout(()=>{r.current&&r.current.connected&&(console.log("Retrying registration for user ".concat(e," after connection")),r.current.emit("registerUser",{userId:e}))},1e3)),!1):(console.error("Cannot register user: userId is empty"),!1),[t]);return{isConnected:t,error:n,emit:c,on:l,registerUser:d,socket:r.current}}("credits"),Q=e=>{try{let t={...e,timestamp:Date.now()};localStorage.setItem("lastKnownCredits",JSON.stringify(t))}catch(e){console.warn("Failed to save credit data to localStorage:",e)}},V=()=>{try{let e=localStorage.getItem("lastKnownCredits");if(e){let t=JSON.parse(e);if(Date.now()-t.timestamp<3e5)return t}}catch(e){console.warn("Failed to load credit data from localStorage:",e)}return null},$=async()=>{try{_(!0);let e=await (0,n.mP)();return m(e.freeCreditsRemaining||0),v(e.paidCredits||0),b(e.totalAvailable||0),k(e.usingFreeCredits||!1),S(e.freeMinutesRemaining||0),x(e.paidMinutes||0),E(e.totalMinutesAvailable||0),d(e.credits||e.totalAvailable||0),g(e.minutes||e.totalMinutesAvailable||0),N(e.callPricePerMinute||.1),M(e.monthlyAllowance||0),j(e.minimumCreditsThreshold||1),B(!0),J(Date.now()),H(0),Q(e),e}catch(t){console.error("Error fetching user credits:",t),H(e=>e+1);let e=V();if(e&&L<3)return console.log("Using cached credit data due to fetch failure"),m(e.freeCreditsRemaining||0),v(e.paidCredits||0),b(e.totalAvailable||0),k(e.usingFreeCredits||!1),S(e.freeMinutesRemaining||0),x(e.paidMinutes||0),E(e.totalMinutesAvailable||0),d(e.credits||e.totalAvailable||0),g(e.minutes||e.totalMinutesAvailable||0),N(e.callPricePerMinute||.1),M(e.monthlyAllowance||0),j(e.minimumCreditsThreshold||1),e;return{credits:0,minutes:0,callPricePerMinute:.1,freeCreditsRemaining:0,paidCredits:0,totalAvailable:0,usingFreeCredits:!1,freeMinutesRemaining:0,paidMinutes:0,totalMinutesAvailable:0,monthlyAllowance:0}}finally{_(!1)}};(0,a.useEffect)(()=>{try{let e=localStorage.getItem("user_data");if(e){let t=JSON.parse(e),o=t._id||null,r=t.organizationId||null;D(o),O(r),!r&&o&&(console.log("No organization ID found in localStorage, will try to fetch from API"),$().then(()=>{console.log("Fetched credits and possibly updated organization ID")}))}else console.warn("No user data found in localStorage")}catch(e){console.error("Error getting user data from localStorage:",e)}},[]),(0,a.useEffect)(()=>{K&&F&&(console.log("Registering user with WebSocket:",81),Y(F))},[K,F,Y]),(0,a.useEffect)(()=>{if(!K){console.log("WebSocket not connected, skipping credit update listeners setup");return}console.log("Setting up credit update listeners, organizationId:",T);let e=q("creditUpdate",e=>{console.log("Received credit update via WebSocket:",e),d(e.credits),z>0&&g(e.credits/z),s.o.info("Your credits have been updated")}),t=q("organizationCreditUpdate",e=>{var t;console.log("Received organization credit update via WebSocket:",e),console.log("Current organizationId:",T),console.log("Received organizationId:",e.organizationId);let o=null==T?void 0:T.toString(),r=null===(t=e.organizationId)||void 0===t?void 0:t.toString();console.log("Comparing organization IDs:",{ourOrgId:o,receivedOrgId:r,match:o===r}),o&&r&&o===r?(console.log("Organization ID matched, updating credits to:",e.credits),d(e.credits),z>0&&g(e.credits/z),s.o.info("Your organization credits have been updated")):(console.log("Organization ID did not match or is missing, not updating credits"),console.log("Our organization ID:",o),console.log("Received organization ID:",r))});return()=>{console.log("Cleaning up credit update listeners"),e(),t()}},[K,q,T]),(0,a.useEffect)(()=>{let e=V();e&&(console.log("Loading cached credit data on mount"),m(e.freeCreditsRemaining||0),v(e.paidCredits||0),b(e.totalAvailable||0),k(e.usingFreeCredits||!1),S(e.freeMinutesRemaining||0),x(e.paidMinutes||0),E(e.totalMinutesAvailable||0),d(e.credits||e.totalAvailable||0),g(e.minutes||e.totalMinutesAvailable||0),N(e.callPricePerMinute||.1),M(e.monthlyAllowance||0),j(e.minimumCreditsThreshold||1),B(!0),J(e.timestamp)),$();let t=setInterval(()=>{$()},3e4);return()=>clearInterval(t)},[z]);let G=async()=>{await $()},X=Math.max(o,R);return(0,r.jsx)(c.Provider,{value:{credits:l,minutes:u,freeCreditsRemaining:f,paidCredits:h,totalAvailable:p,usingFreeCredits:w,freeMinutesRemaining:y,paidMinutes:C,totalMinutesAvailable:A,callPricePerMinute:z,monthlyAllowance:I,hasSufficientCredits:p>=X,isLoading:P,refreshCredits:G,creditThreshold:o,organizationCreditThreshold:R,effectiveThreshold:X,isConnected:K,hasValidData:U,lastSuccessfulFetch:W},children:t})}function d(){let e=(0,a.useContext)(c);if(void 0===e)throw Error("useCredits must be used within a CreditProvider");return e}},7524:(e,t,o)=>{o.d(t,{LB:()=>l,TK:()=>s,hG:()=>c,hU:()=>a,lo:()=>n,mP:()=>u,nu:()=>i,s2:()=>d});let r="http://localhost:4000";async function a(){try{let e=localStorage.getItem("access_token");if(!e)throw console.error("No access token available"),Error("No access token available");let t=await fetch("".concat(r,"/api/users"),{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error("Failed to fetch users");return await t.json()}catch(e){throw console.error("Error fetching users:",e),e}}let n=async()=>{let e=localStorage.getItem("access_token");if(!e)throw Error("No access token available");let t=await fetch("".concat(r,"/api/users"),{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch users");return t.json()};async function i(e){try{if(!localStorage.getItem("access_token"))throw console.error("No access token available"),Error("No access token available");let t=await fetch("".concat(r,"/api/users/register"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.text();throw Error(e||"Failed to add user")}return}catch(e){throw console.error("Error adding user:",e),e}}async function s(e,t){try{let o=localStorage.getItem("access_token");if(!o)throw console.error("No access token available"),Error("No access token available");let a=await fetch("".concat(r,"/api/users/").concat(e),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(o)},body:JSON.stringify(t)});if(!a.ok)throw Error("Failed to update user");return await a.json()}catch(e){throw console.error("Error updating user:",e),e}}async function c(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");if(!(await fetch("".concat(r,"/api/users/").concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(t)}})).ok)throw Error("Failed to delete user")}catch(e){throw console.error("Error deleting user:",e),e}}async function l(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");let o=await fetch("".concat(r,"/api/users/approve/").concat(e),{method:"PATCH",headers:{Authorization:"Bearer ".concat(t)}});if(!o.ok)throw Error("Failed to approve user");return await o.json()}catch(e){throw console.error("Error approving user:",e),e}}async function d(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");let o=await fetch("".concat(r,"/api/users/").concat(e),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify({isApproved:!1})});if(!o.ok)throw Error("Failed to revoke user access");return await o.json()}catch(e){throw console.error("Error revoking user access:",e),e}}async function u(){try{let e=localStorage.getItem("access_token");if(!e)throw console.error("No access token available"),Error("No access token available");let t=await fetch("".concat(r,"/api/users/me/credits"),{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error("Failed to fetch user credits");let o=await t.json();return{credits:o.credits||0,minutes:o.minutes||0,freeCreditsRemaining:o.freeCreditsRemaining||0,paidCredits:o.paidCredits||0,totalAvailable:o.totalAvailable||0,usingFreeCredits:o.usingFreeCredits||!1,freeMinutesRemaining:o.freeMinutesRemaining||0,paidMinutes:o.paidMinutes||0,totalMinutesAvailable:o.totalMinutesAvailable||0,callPricePerMinute:o.callPricePerMinute||.1,monthlyResetDate:o.monthlyResetDate||1,monthlyAllowance:o.monthlyAllowance||0,minimumCreditsThreshold:o.minimumCreditsThreshold||1}}catch(e){return console.error("Error fetching user credits:",e),{credits:0,minutes:0,freeCreditsRemaining:0,paidCredits:0,totalAvailable:0,usingFreeCredits:!1,freeMinutesRemaining:0,paidMinutes:0,totalMinutesAvailable:0,callPricePerMinute:.1,monthlyResetDate:1,monthlyAllowance:0,minimumCreditsThreshold:1}}}},30285:(e,t,o)=>{o.d(t,{$:()=>c,r:()=>s});var r=o(95155);o(12115);var a=o(99708),n=o(74466),i=o(59434);let s=(0,n.F)("inline-flex items-center cursor-pointer justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:o,size:n,asChild:c=!1,...l}=e,d=c?a.DX:"button";return(0,r.jsx)(d,{"data-slot":"button",className:(0,i.cn)(s({variant:o,size:n,className:t})),...l})}},44838:(e,t,o)=>{o.d(t,{I:()=>l,SQ:()=>c,_2:()=>d,lp:()=>u,mB:()=>g,rI:()=>i,ty:()=>s});var r=o(95155);o(12115);var a=o(48698),n=o(59434);function i(e){let{...t}=e;return(0,r.jsx)(a.bL,{"data-slot":"dropdown-menu",...t})}function s(e){let{...t}=e;return(0,r.jsx)(a.l9,{"data-slot":"dropdown-menu-trigger",...t})}function c(e){let{className:t,sideOffset:o=4,...i}=e;return(0,r.jsx)(a.ZL,{children:(0,r.jsx)(a.UC,{"data-slot":"dropdown-menu-content",sideOffset:o,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-md",t),...i})})}function l(e){let{...t}=e;return(0,r.jsx)(a.YJ,{"data-slot":"dropdown-menu-group",...t})}function d(e){let{className:t,inset:o,variant:i="default",...s}=e;return(0,r.jsx)(a.q7,{"data-slot":"dropdown-menu-item","data-inset":o,"data-variant":i,className:(0,n.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s})}function u(e){let{className:t,inset:o,...i}=e;return(0,r.jsx)(a.JU,{"data-slot":"dropdown-menu-label","data-inset":o,className:(0,n.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...i})}function g(e){let{className:t,...o}=e;return(0,r.jsx)(a.wv,{"data-slot":"dropdown-menu-separator",className:(0,n.cn)("bg-border -mx-1 my-1 h-px",t),...o})}},59434:(e,t,o)=>{o.d(t,{cn:()=>n,v:()=>i});var r=o(52596),a=o(39688);function n(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];return(0,a.QP)((0,r.$)(t))}function i(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}},91394:(e,t,o)=>{o.d(t,{BK:()=>s,eu:()=>i,q5:()=>c});var r=o(95155);o(12115);var a=o(85977),n=o(59434);function i(e){let{className:t,...o}=e;return(0,r.jsx)(a.bL,{"data-slot":"avatar",className:(0,n.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...o})}function s(e){let{className:t,...o}=e;return(0,r.jsx)(a._V,{"data-slot":"avatar-image",className:(0,n.cn)("aspect-square size-full",t),...o})}function c(e){let{className:t,...o}=e;return(0,r.jsx)(a.H4,{"data-slot":"avatar-fallback",className:(0,n.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...o})}}}]);