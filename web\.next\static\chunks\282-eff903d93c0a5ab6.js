"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[282],{12421:(e,t,r)=>{r.d(t,{t:()=>s});var a=r(57297);async function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=localStorage.getItem("access_token");if(!r){let e=await (0,a.J1)();if(!e.success)throw Error("No authentication token available");r=e.newAccessToken}let s=new Headers(t.headers||{});s.has("Authorization")||s.set("Authorization","Bearer ".concat(r));let n=await fetch(e,{...t,headers:s});if(401===n.status||403===n.status){console.log("Token expired, attempting refresh...");let r=await (0,a.J1)();if(!r.success)throw console.error("Token refresh failed"),window.location.href="/login",Error("Authentication failed");console.log("Token refreshed, retrying request...");let s=new Headers(t.headers||{});return s.set("Authorization","Bearer ".concat(r.newAccessToken)),fetch(e,{...t,headers:s})}return n}},30285:(e,t,r)=>{r.d(t,{$:()=>c,r:()=>o});var a=r(95155);r(12115);var s=r(99708),n=r(74466),i=r(59434);let o=(0,n.F)("inline-flex items-center cursor-pointer justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:r,size:n,asChild:c=!1,...l}=e,d=c?s.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,i.cn)(o({variant:r,size:n,className:t})),...l})}},55365:(e,t,r)=>{r.d(t,{Fc:()=>o,TN:()=>l,XL:()=>c});var a=r(95155);r(12115);var s=r(74466),n=r(59434);let i=(0,s.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-background text-foreground",destructive:"text-destructive-foreground [&>svg]:text-current *:data-[slot=alert-description]:text-destructive-foreground/80"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,n.cn)(i({variant:r}),t),...s})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-title",className:(0,n.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"alert-description",className:(0,n.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...r})}},57297:(e,t,r)=>{r.d(t,{HW:()=>n,J1:()=>o,_f:()=>i});var a=r(12421);let s="http://localhost:4000";async function n(){try{let e=await (0,a.t)("".concat(s,"/api/auth/me"),{method:"GET"});if(!e.ok)return{success:!1,error:"Error: ".concat(e.status)};let t=await e.json(),r=t.userId||t._id||t.id,n=t.email;if(r&&n)return{success:!0,user:{fullName:t.fullName||n.split("@")[0],userId:r,email:n,role:t.role||"user"}};return{success:!1,error:"Invalid user data received"}}catch(e){return console.error("Error fetching user data:",e),{success:!1,error:"An error occurred while fetching user data"}}}function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=setInterval(async()=>{if(localStorage.getItem("access_token"))try{await o()}catch(e){console.error("Background token refresh failed:",e)}},6e4*e);return()=>clearInterval(t)}async function o(){let e=localStorage.getItem("refresh_token");if(!e)return{success:!1};try{let t=await fetch("".concat(s,"/api/auth/refresh"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!t.ok)return{success:!1};let r=await t.json();if(r.access_token)return localStorage.setItem("access_token",r.access_token),{success:!0,newAccessToken:r.access_token};return{success:!1}}catch(e){return console.error("Token refresh error:",e),{success:!1}}}},59434:(e,t,r)=>{r.d(t,{cn:()=>n,v:()=>i});var a=r(52596),s=r(39688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}function i(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}},62523:(e,t,r)=>{r.d(t,{p:()=>n});var a=r(95155);r(12115);var s=r(59434);function n(e){let{className:t,type:r,...n}=e;return(0,a.jsx)("input",{type:r,"data-slot":"input",className:(0,s.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},66695:(e,t,r)=>{r.d(t,{BT:()=>c,Wu:()=>l,ZB:()=>o,Zp:()=>n,aR:()=>i,wL:()=>d});var a=r(95155);r(12115);var s=r(59434);function n(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",t),...r})}function i(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("flex flex-col gap-1.5 px-6",t),...r})}function o(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...r})}function c(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...r})}function l(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...r})}function d(e){let{className:t,...r}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,s.cn)("flex items-center px-6",t),...r})}},77107:(e,t,r)=>{r.d(t,{A:()=>a});let a={src:"/_next/static/media/OROVA-WHITE.76096952.png",height:124,width:732,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAABCAMAAADU3h9xAAAAD1BMVEX////Aucj7+/vp5e38/PzbXIb5AAAABXRSTlNs0Yl2dMCoCVsAAAAJcEhZcwAACxMAAAsTAQCanBgAAAARSURBVHicY2BkZmBiYWBgAAAAQAALSpjpiwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:1}},90282:(e,t,r)=>{r.r(t),r.d(t,{default:()=>y});var a=r(95155),s=r(12115),n=r(35695),i=r(47650),o=r(62523),c=r(66695),l=r(55365),d=r(34477);let u=(0,d.createServerReference)("40922dd30dbc05a2c82413a6ab04d49af413ddd216",d.callServer,void 0,d.findSourceMapURL,"loginUser");var g=r(72509),m=r(66766),f=r(77107),x=r(57297),h=r(78749),v=r(92657),p=r(30285);function b(){let{pending:e}=(0,i.useFormStatus)();return(0,a.jsx)(p.$,{type:"submit",className:"w-full h-12 bg-gradient-to-r from-[#383D73] to-[#74546D] text-white font-semibold transition-all duration-200 hover:scale-[1.02] hover:shadow-md",disabled:e,children:e?(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2"}),"Signing in..."]}):"Sign In"})}function y(){let e=(0,n.useRouter)(),[t,r]=(0,s.useState)(null),[i,d]=(0,s.useState)({}),[p,y]=(0,s.useState)(null),[w,A]=(0,s.useState)(!1);async function j(t){if(!p){r({success:!1,message:"Please complete the CAPTCHA."});return}t.append("recaptchaToken",p);let a=await u(t);if(r(a),a.success&&a.redirect&&a.tokens){localStorage.setItem("access_token",a.tokens.access_token),localStorage.setItem("refresh_token",a.tokens.refresh_token);let t=await (0,x.HW)();if(!t.success){r({success:!1,message:"Account not authorized. Please contact an administrator."}),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user_data");return}t.user&&localStorage.setItem("user_data",JSON.stringify({...t.user,_id:t.user.userId})),e.push(a.redirect);return}if(a.fieldErrors){let e={};Object.entries(a.fieldErrors).forEach(t=>{let[r,a]=t;Array.isArray(a)&&a.length>0&&(e[r]=a[0])}),d(e)}else d({})}return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"min-h-screen flex flex-col md:flex-row",children:[(0,a.jsx)("div",{className:"hidden md:flex md:w-1/2 bg-gradient-to-br from-[#383D73] to-[#74546D] text-white flex-col justify-center items-center p-8",children:(0,a.jsxs)("div",{className:"max-w-md mx-auto flex flex-col items-center space-y-12",children:[(0,a.jsx)(m.default,{src:f.A,alt:"Orova Logo",width:280,height:70}),(0,a.jsxs)("div",{className:"text-center space-y-4",children:[(0,a.jsx)("h1",{className:"text-4xl font-bold",children:"Welcome to Orova AI"}),(0,a.jsx)("p",{className:"text-lg opacity-80",children:"The next generation calling platform powered by artificial intelligence"})]}),(0,a.jsxs)("div",{className:"bg-white/10 backdrop-blur-sm rounded-xl p-6 w-full",children:[(0,a.jsx)("blockquote",{className:"text-center italic",children:'"Orova transformed our customer engagement with intelligent calls that feel personal and professional."'}),(0,a.jsxs)("div",{className:"mt-4 flex items-center justify-center",children:[(0,a.jsx)("div",{className:"h-px w-12 bg-white/30"}),(0,a.jsx)("div",{className:"h-px w-12 bg-white/30"})]})]})]})}),(0,a.jsx)("div",{className:"flex flex-1 items-center justify-center bg-gray-50 dark:bg-gray-900 p-8",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsx)("div",{className:"flex justify-center mb-8 md:hidden",children:(0,a.jsx)(m.default,{src:f.A,alt:"Orova Logo",width:200,height:50,className:"dark:filter dark:brightness-0 dark:invert"})}),(0,a.jsxs)(c.Zp,{className:"bg-white dark:bg-gray-800 shadow-xl rounded-xl border-0",children:[(0,a.jsxs)(c.aR,{className:"space-y-2 pb-2",children:[(0,a.jsx)(c.ZB,{className:"text-center text-2xl font-bold bg-gradient-to-r from-[#383D73] to-[#74546D] bg-clip-text text-transparent dark:text-white",children:"Welcome to Orova"}),(0,a.jsx)("p",{className:"text-center text-gray-500 dark:text-gray-400",children:"Sign in to your account"})]}),(0,a.jsxs)(c.Wu,{className:"pt-4",children:[t&&!t.success&&!t.fieldErrors&&(0,a.jsx)(l.Fc,{className:"mb-4 bg-red-50 text-red-800 border-red-200",children:(0,a.jsx)(l.TN,{children:t.message})}),(0,a.jsxs)("form",{action:j,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.p,{name:"email",type:"email",placeholder:"Email",required:!0,className:"h-12 bg-gray-50 dark:bg-gray-700/50 border-gray-200 dark:border-gray-700","aria-invalid":!!i.email}),i.email&&(0,a.jsx)("p",{className:"text-sm text-red-500 mt-1",children:i.email})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.p,{name:"password",type:w?"text":"password",placeholder:"Password",required:!0,className:"h-12 bg-gray-50 dark:bg-gray-700/50 border-gray-200 dark:border-gray-700 pr-10","aria-invalid":!!i.password}),(0,a.jsx)("button",{type:"button",onClick:()=>A(e=>!e),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none",tabIndex:-1,"aria-label":w?"Hide password":"Show password",children:w?(0,a.jsx)(h.A,{className:"h-5 w-5"}):(0,a.jsx)(v.A,{className:"h-5 w-5"})})]}),i.password&&(0,a.jsx)("p",{className:"text-sm text-red-500 mt-1",children:i.password})]}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)(g.A,{sitekey:"6LfyPfgqAAAAAJy91ZTqWkEaQcGJJtNC-MnxUa6e",onChange:e=>y(e)})}),(0,a.jsx)("div",{className:"flex items-center justify-between"}),(0,a.jsx)(b,{})]})]})]}),(0,a.jsxs)("div",{className:"mt-6 text-center text-xs text-gray-500 dark:text-gray-400",children:["\xa9 ",new Date().getFullYear()," Orova AI. All rights reserved."]})]})})]})})}}}]);