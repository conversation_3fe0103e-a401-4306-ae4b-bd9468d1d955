(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3793],{2488:(e,t,a)=>{"use strict";a.d(t,{default:()=>n});var s=a(95155),r=a(226);function n(e){let{children:t,duration:a=.5,delay:n=0,direction:i="up",distance:l=30,className:o="",once:c=!0,viewOffset:d=.1}=e,m=0,u=0;return"up"===i&&(m=l),"down"===i&&(m=-l),"left"===i&&(u=l),"right"===i&&(u=-l),(0,s.jsx)(r.P.div,{initial:{y:m,x:u,opacity:0},whileInView:{y:0,x:0,opacity:1},transition:{duration:a,delay:n,ease:"easeOut"},viewport:{once:c,amount:d},className:o,children:t})}},7524:(e,t,a)=>{"use strict";a.d(t,{LB:()=>c,TK:()=>l,hG:()=>o,hU:()=>r,lo:()=>n,mP:()=>m,nu:()=>i,s2:()=>d});let s="http://localhost:4000";async function r(){try{let e=localStorage.getItem("access_token");if(!e)throw console.error("No access token available"),Error("No access token available");let t=await fetch("".concat(s,"/api/users"),{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error("Failed to fetch users");return await t.json()}catch(e){throw console.error("Error fetching users:",e),e}}let n=async()=>{let e=localStorage.getItem("access_token");if(!e)throw Error("No access token available");let t=await fetch("".concat(s,"/api/users"),{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch users");return t.json()};async function i(e){try{if(!localStorage.getItem("access_token"))throw console.error("No access token available"),Error("No access token available");let t=await fetch("".concat(s,"/api/users/register"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.text();throw Error(e||"Failed to add user")}return}catch(e){throw console.error("Error adding user:",e),e}}async function l(e,t){try{let a=localStorage.getItem("access_token");if(!a)throw console.error("No access token available"),Error("No access token available");let r=await fetch("".concat(s,"/api/users/").concat(e),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a)},body:JSON.stringify(t)});if(!r.ok)throw Error("Failed to update user");return await r.json()}catch(e){throw console.error("Error updating user:",e),e}}async function o(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");if(!(await fetch("".concat(s,"/api/users/").concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(t)}})).ok)throw Error("Failed to delete user")}catch(e){throw console.error("Error deleting user:",e),e}}async function c(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");let a=await fetch("".concat(s,"/api/users/approve/").concat(e),{method:"PATCH",headers:{Authorization:"Bearer ".concat(t)}});if(!a.ok)throw Error("Failed to approve user");return await a.json()}catch(e){throw console.error("Error approving user:",e),e}}async function d(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");let a=await fetch("".concat(s,"/api/users/").concat(e),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify({isApproved:!1})});if(!a.ok)throw Error("Failed to revoke user access");return await a.json()}catch(e){throw console.error("Error revoking user access:",e),e}}async function m(){try{let e=localStorage.getItem("access_token");if(!e)throw console.error("No access token available"),Error("No access token available");let t=await fetch("".concat(s,"/api/users/me/credits"),{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error("Failed to fetch user credits");let a=await t.json();return{credits:a.credits||0,minutes:a.minutes||0,freeCreditsRemaining:a.freeCreditsRemaining||0,paidCredits:a.paidCredits||0,totalAvailable:a.totalAvailable||0,usingFreeCredits:a.usingFreeCredits||!1,freeMinutesRemaining:a.freeMinutesRemaining||0,paidMinutes:a.paidMinutes||0,totalMinutesAvailable:a.totalMinutesAvailable||0,callPricePerMinute:a.callPricePerMinute||.1,monthlyResetDate:a.monthlyResetDate||1,monthlyAllowance:a.monthlyAllowance||0,minimumCreditsThreshold:a.minimumCreditsThreshold||1}}catch(e){return console.error("Error fetching user credits:",e),{credits:0,minutes:0,freeCreditsRemaining:0,paidCredits:0,totalAvailable:0,usingFreeCredits:!1,freeMinutesRemaining:0,paidMinutes:0,totalMinutesAvailable:0,callPricePerMinute:.1,monthlyResetDate:1,monthlyAllowance:0,minimumCreditsThreshold:1}}}},17313:(e,t,a)=>{"use strict";a.d(t,{Xi:()=>o,av:()=>c,j7:()=>l,tU:()=>i});var s=a(95155);a(12115);var r=a(60704),n=a(59434);function i(e){let{className:t,...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"tabs",className:(0,n.cn)("flex flex-col gap-2",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,n.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-1",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,n.cn)("data-[state=active]:bg-background data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring inline-flex flex-1 items-center justify-center gap-1.5 rounded-md px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,n.cn)("flex-1 outline-none",t),...a})}},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>o,r:()=>l});var s=a(95155);a(12115);var r=a(99708),n=a(74466),i=a(59434);let l=(0,n.F)("inline-flex items-center cursor-pointer justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:n,asChild:o=!1,...c}=e,d=o?r.DX:"button";return(0,s.jsx)(d,{"data-slot":"button",className:(0,i.cn)(l({variant:a,size:n,className:t})),...c})}},30356:(e,t,a)=>{"use strict";a.d(t,{C:()=>o,z:()=>l});var s=a(95155);a(12115);var r=a(54059),n=a(9428),i=a(59434);function l(e){let{className:t,...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"radio-group",className:(0,i.cn)("grid gap-3",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)(r.q7,{"data-slot":"radio-group-item",className:(0,i.cn)("border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,s.jsx)(r.C1,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,s.jsx)(n.A,{className:"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2"})})})}},40026:(e,t,a)=>{"use strict";a.d(t,{H:()=>s,e:()=>r});let s="http://localhost:4000",r="pk_test_51ROz1YRpJ0zLf0aTbgbDkpShvfpNxdZPet1QXClapTckA7Cy0tsaxY2qY1dp8oSBGOFqnh0vugjd8mDluFWgKpRL00bACyumT8"},44671:(e,t,a)=>{Promise.resolve().then(a.bind(a,81789))},47262:(e,t,a)=>{"use strict";a.d(t,{S:()=>l});var s=a(95155);a(12115);var r=a(14885),n=a(5196),i=a(59434);function l(e){let{className:t,...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,s.jsx)(r.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,s.jsx)(n.A,{className:"size-3.5"})})})}},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>u,Es:()=>x,HM:()=>d,L3:()=>f,c7:()=>h,lG:()=>l,rr:()=>p,zM:()=>o});var s=a(95155);a(12115);var r=a(15452),n=a(54416),i=a(59434);function l(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"dialog",...t})}function o(e){let{...t}=e;return(0,s.jsx)(r.l9,{"data-slot":"dialog-trigger",...t})}function c(e){let{...t}=e;return(0,s.jsx)(r.ZL,{"data-slot":"dialog-portal",...t})}function d(e){let{...t}=e;return(0,s.jsx)(r.bm,{"data-slot":"dialog-close",...t})}function m(e){let{className:t,...a}=e;return(0,s.jsx)(r.hJ,{"data-slot":"dialog-overlay",className:(0,i.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-650 bg-black/50",t),...a})}function u(e){let{className:t,children:a,...l}=e;return(0,s.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,s.jsx)(m,{}),(0,s.jsxs)(r.UC,{"data-slot":"dialog-content",className:(0,i.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-650 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...l,children:[a,(0,s.jsxs)(r.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(n.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function h(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,i.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function x(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,i.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function f(e){let{className:t,...a}=e;return(0,s.jsx)(r.hE,{"data-slot":"dialog-title",className:(0,i.cn)("text-lg leading-none font-semibold",t),...a})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(r.VY,{"data-slot":"dialog-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...a})}},59409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>m,eb:()=>h,gC:()=>u,l6:()=>c,yv:()=>d});var s=a(95155);a(12115);var r=a(31992),n=a(66474),i=a(5196),l=a(47863),o=a(59434);function c(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,s.jsx)(r.WT,{"data-slot":"select-value",...t})}function m(e){let{className:t,children:a,...i}=e;return(0,s.jsxs)(r.l9,{"data-slot":"select-trigger",className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i,children:[a,(0,s.jsx)(r.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function u(e){let{className:t,children:a,position:n="popper",...i}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,s.jsx)(x,{}),(0,s.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(f,{})]})})}function h(e){let{className:t,children:a,...n}=e;return(0,s.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(r.VF,{children:(0,s.jsx)(i.A,{className:"size-4"})})}),(0,s.jsx)(r.p4,{children:a})]})}function x(e){let{className:t,...a}=e;return(0,s.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(l.A,{className:"size-4"})})}function f(e){let{className:t,...a}=e;return(0,s.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(n.A,{className:"size-4"})})}},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>n,v:()=>i});var s=a(52596),r=a(39688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}function i(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>n});var s=a(95155);a(12115);var r=a(59434);function n(e){let{className:t,type:a,...n}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,r.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},66695:(e,t,a)=>{"use strict";a.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>n,aR:()=>i,wL:()=>d});var s=a(95155);a(12115);var r=a(59434);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("flex flex-col gap-1.5 px-6",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6",t),...a})}},80333:(e,t,a)=>{"use strict";a.d(t,{d:()=>i});var s=a(95155);a(12115);var r=a(4884),n=a(59434);function i(e){let{className:t,...a}=e;return(0,s.jsx)(r.bL,{"data-slot":"switch",className:(0,n.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 inline-flex h-5 w-9 shrink-0 items-center rounded-full border-2 border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,s.jsx)(r.zi,{"data-slot":"switch-thumb",className:(0,n.cn)("bg-background pointer-events-none block size-4 rounded-full ring-0 shadow-lg transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")})})}},81789:(e,t,a)=>{"use strict";a.d(t,{default:()=>W});var s=a(95155),r=a(12115),n=a(35695),i=a(66695),l=a(62523),o=a(85057),c=a(30285),d=a(17313),m=a(85127),u=a(30356),h=a(81586),x=a(27414),f=a(35169),p=a(51154),g=a(43453),y=a(62525),j=a(47924),b=a(57434),v=a(2488),w=a(40026);let N=async()=>{let e=await fetch("".concat(w.H,"/api/billing/payment-methods"),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch payment methods");return e.json()},k=async e=>{let t=await fetch("".concat(w.H,"/api/billing/payment-methods/").concat(e,"/default"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!t.ok)throw Error((await t.json()).message||"Failed to set default payment method");return t.json()},A=async e=>{let t=await fetch("".concat(w.H,"/api/billing/payment-methods/").concat(e),{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!t.ok)throw Error((await t.json()).message||"Failed to remove payment method");return t.json()},C=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,a=await fetch("".concat(w.H,"/api/billing/transactions?page=").concat(e,"&limit=").concat(t),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!a.ok)throw Error((await a.json()).message||"Failed to fetch transaction history");return a.json()},S=async e=>{let t=await fetch("".concat(w.H,"/api/billing/process-payment"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Failed to process payment");return t.json()};var z=a(7524),M=a(55855);let E=(0,a(7368).c)(w.e),P=(0,r.memo)(e=>{let{children:t}=e;return(0,s.jsx)(M.Elements,{stripe:E,children:t})});var F=a(47262);let _=(0,r.memo)(e=>{let{amount:t,currency:a,description:n,onSuccess:i,onError:l,showSaveOption:d=!0}=e,m=(0,M.useStripe)(),u=(0,M.useElements)(),[x,f]=(0,r.useState)(!1),[g,y]=(0,r.useState)(!1),[j,b]=(0,r.useState)(!0),v=(0,r.useMemo)(()=>new Intl.NumberFormat("en-US",{style:"currency",currency:a}).format(t/100),[t,a]),w=async e=>{if(e.preventDefault(),!m||!u){l("Stripe has not loaded yet. Please try again.");return}f(!0);try{let e=u.getElement(M.CardElement);if(!e)throw Error("Card element not found");let{error:s,paymentMethod:r}=await m.createPaymentMethod({type:"card",card:e});if(s)throw Error(s.message);if(!r)throw Error("Failed to create payment method");let l=await S({paymentMethodId:r.id,amount:t,currency:a,description:n,savePaymentMethod:g,setAsDefault:g&&j});if(l.success){e.clear();try{await new Promise(e=>setTimeout(e,500)),i()}catch(e){console.error("Error refreshing credits:",e),i()}}else if(("requires_action"===l.status||"requires_confirmation"===l.status)&&l.clientSecret){let{error:t,paymentIntent:a}=await m.confirmCardPayment(l.clientSecret);if(t)throw Error(t.message);if("succeeded"===a.status){e.clear();try{await new Promise(e=>setTimeout(e,500)),i()}catch(e){console.error("Error refreshing credits:",e),i()}}else throw Error("Payment failed: ".concat(a.status))}else if("requires_action"===l.status||"requires_confirmation"===l.status)throw Error("Authentication required but client secret is missing");else throw Error("Payment failed: ".concat(l.status))}catch(e){console.error("Payment error:",e),l(e.message||"Payment processing failed")}finally{f(!1)}};return(0,s.jsxs)("form",{onSubmit:w,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"p-4 border rounded-lg bg-white dark:bg-gray-800 shadow-sm",children:[(0,s.jsx)("div",{className:"mb-3 flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(h.A,{className:"h-5 w-5 mr-2 text-primary"}),(0,s.jsx)("span",{className:"text-sm font-medium",children:"Enter Card Details"})]})}),(0,s.jsx)(M.CardElement,{options:{style:{base:{fontSize:"16px",color:"#424770",fontFamily:'system-ui, -apple-system, "Segoe UI", Roboto, sans-serif',"::placeholder":{color:"#aab7c4"},":-webkit-autofill":{color:"#424770"}},invalid:{color:"#9e2146",iconColor:"#9e2146"}}}})]}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground flex items-center",children:[(0,s.jsx)(h.A,{className:"h-3 w-3 mr-1"}),"Your card information is securely processed by Stripe."]})]}),d&&(0,s.jsxs)("div",{className:"space-y-3 p-3 border rounded-md bg-primary/5 border-primary/10",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(F.S,{id:"save-card",checked:g,onCheckedChange:e=>y(!0===e)}),(0,s.jsxs)(o.J,{htmlFor:"save-card",className:"flex items-center cursor-pointer",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2 text-primary"}),"Save card for future payments"]})]}),g&&(0,s.jsxs)("div",{className:"flex items-center space-x-2 ml-6",children:[(0,s.jsx)(F.S,{id:"default-card",checked:j,onCheckedChange:e=>b(!0===e)}),(0,s.jsx)(o.J,{htmlFor:"default-card",className:"cursor-pointer",children:"Set as default payment method"})]})]})]}),(0,s.jsx)(c.$,{type:"submit",className:"w-full",disabled:!m||x||t<1e3,children:x?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):"Pay ".concat(v)})]})});var D=a(59409);let B=(0,r.memo)(e=>{let{amount:t,currency:a,description:n,onSuccess:l,onError:o}=e,[d,m]=(0,r.useState)([]),[u,x]=(0,r.useState)(null),[f,g]=(0,r.useState)(!0),[y,j]=(0,r.useState)(!1),b=(0,r.useMemo)(()=>new Intl.NumberFormat("en-US",{style:"currency",currency:a}).format(t/100),[t,a]),v=(0,r.useMemo)(()=>d.map(e=>(0,s.jsx)(D.eb,{value:e._id,children:(0,s.jsxs)("div",{className:"flex items-center",children:["visa"===e.brand&&(0,s.jsxs)("svg",{viewBox:"0 0 32 21",className:"h-4 w-4 mr-2",children:[(0,s.jsx)("path",{d:"M26.58 21H5.42A5.42 5.42 0 0 1 0 15.58V5.42A5.42 5.42 0 0 1 5.42 0h21.16A5.42 5.42 0 0 1 32 5.42v10.16A5.42 5.42 0 0 1 26.58 21z",fill:"#fff"}),(0,s.jsx)("path",{d:"M12.322 7.583c.165-.437.352-.867.55-1.289h-1.09c-.33.39-.65.803-.957 1.234-.1-.431-.415-.845-.736-1.234h-.922c.616.452 1.013 1.014 1.013 1.716 0 .669-.352 1.289-.968 1.716h1.112c.308-.365.583-.803.781-1.274.187.471.451.909.77 1.274h1.134c-.627-.427-.968-1.047-.968-1.716a2.069 2.069 0 0 1 .28-1.427z",fill:"#00579f"}),(0,s.jsx)("path",{d:"M16.9 7.233c-.319 0-.605.075-.825.224-.209.149-.319.373-.319.634 0 .5.407.764 1.013.94l.297.09c.341.104.517.193.517.365 0 .268-.275.432-.726.432-.385 0-.671-.075-.979-.277l-.143.67c.33.164.737.254 1.112.254.374 0 .682-.075.902-.239.242-.164.374-.403.374-.7 0-.537-.44-.81-1.024-.97l-.297-.09c-.275-.074-.506-.164-.506-.343 0-.224.231-.373.594-.373.33 0 .649.09.869.194l.132-.634a2.365 2.365 0 0 0-.99-.179zm2.772 0c-.22 0-.407.03-.55.09l-.11.642c.143-.06.33-.104.55-.104.407-.15.66.224.66.642 0 .582-.308.94-.77.94-.242 0-.44-.06-.627-.164l-.121.656c.187.09.506.149.792.149.891 0 1.463-.642 1.463-1.61 0-.94-.517-1.24-1.288-1.24zm-6.578.09l-.209 1.28c-.22-.06-.44-.09-.66-.09-.56 0-1.024.149-1.024.582 0 .194.121.328.297.403.22.09.275.12.275.209 0 .134-.143.194-.341.194a1.9 1.9 0 0 1-.638-.12l-.11.626c.22.075.55.12.847.12.891 0 1.365-.373 1.365-.94 0-.224-.132-.387-.33-.477-.165-.075-.22-.09-.22-.179 0-.104.11-.164.297-.164.165 0 .374.03.55.09l.11-.634h-.209zm13.64-.09c-.33 0-.638.09-.869.373l.088-.373h-.748l-.572 2.68h.847l.33-1.582c.11-.507.396-.776.737-.776.11 0 .22.015.308.045l.165-.746a1.274 1.274 0 0 0-.286-.03zm-8.415.09l-.671 2.68h.847l.66-2.68h-.836zm-3.96 0l-.847 1.88-.341-1.88h-.902l-.781 2.68h.792l.484-1.85.396 1.85h.55l.99-1.85-.495 1.85h.803l.847-2.68h-1.497z",fill:"#00579f"})]}),"mastercard"===e.brand&&(0,s.jsxs)("svg",{viewBox:"0 0 32 21",className:"h-4 w-4 mr-2",children:[(0,s.jsx)("path",{d:"M26.58 21H5.42A5.42 5.42 0 0 1 0 15.58V5.42A5.42 5.42 0 0 1 5.42 0h21.16A5.42 5.42 0 0 1 32 5.42v10.16A5.42 5.42 0 0 1 26.58 21z",fill:"#fff"}),(0,s.jsx)("path",{d:"M23.209 7.465c0-1.196-.946-2.142-2.142-2.142h-10.12c-1.196 0-2.142.946-2.142 2.142v6.07c0 1.196.946 2.142 2.142 2.142h10.12c1.196 0 2.142-.946 2.142-2.142v-6.07z",fill:"#ff5f00"}),(0,s.jsx)("path",{d:"M12.947 10.5a4.006 4.006 0 0 1 1.535-3.16 4.006 4.006 0 1 0 0 6.32 4.006 4.006 0 0 1-1.535-3.16z",fill:"#eb001b"}),(0,s.jsx)("path",{d:"M20.994 10.5a4.006 4.006 0 0 1-6.512 3.16 4.006 4.006 0 0 0 0-6.32a4.006 4.006 0 0 1 6.512 3.16z",fill:"#f79e1b"})]}),(!e.brand||"visa"!==e.brand&&"mastercard"!==e.brand)&&(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2"}),(0,s.jsxs)("span",{children:["•••• ",e.last4," ",e.isDefault&&(0,s.jsx)("span",{className:"text-xs text-primary ml-1",children:"(Default)"})]})]})},e._id)),[d]),w=(0,r.useMemo)(()=>{if(!u)return null;let e=d.find(e=>e._id===u);return e?(0,s.jsx)(i.Zp,{className:"border-primary/20 shadow-sm",children:(0,s.jsx)(i.Wu,{className:"p-4",children:(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsxs)("div",{className:"p-3 rounded-md ".concat(e.isDefault?"bg-primary/10":"bg-gray-100 dark:bg-gray-800"),children:["visa"===e.brand&&(0,s.jsxs)("svg",{viewBox:"0 0 32 21",className:"h-5 w-5",children:[(0,s.jsx)("path",{d:"M26.58 21H5.42A5.42 5.42 0 0 1 0 15.58V5.42A5.42 5.42 0 0 1 5.42 0h21.16A5.42 5.42 0 0 1 32 5.42v10.16A5.42 5.42 0 0 1 26.58 21z",fill:"#fff"}),(0,s.jsx)("path",{d:"M12.322 7.583c.165-.437.352-.867.55-1.289h-1.09c-.33.39-.65.803-.957 1.234-.1-.431-.415-.845-.736-1.234h-.922c.616.452 1.013 1.014 1.013 1.716 0 .669-.352 1.289-.968 1.716h1.112c.308-.365.583-.803.781-1.274.187.471.451.909.77 1.274h1.134c-.627-.427-.968-1.047-.968-1.716a2.069 2.069 0 0 1 .28-1.427z",fill:"#00579f"}),(0,s.jsx)("path",{d:"M16.9 7.233c-.319 0-.605.075-.825.224-.209.149-.319.373-.319.634 0 .5.407.764 1.013.94l.297.09c.341.104.517.193.517.365 0 .268-.275.432-.726.432-.385 0-.671-.075-.979-.277l-.143.67c.33.164.737.254 1.112.254.374 0 .682-.075.902-.239.242-.164.374-.403.374-.7 0-.537-.44-.81-1.024-.97l-.297-.09c-.275-.074-.506-.164-.506-.343 0-.224.231-.373.594-.373.33 0 .649.09.869.194l.132-.634a2.365 2.365 0 0 0-.99-.179zm2.772 0c-.22 0-.407.03-.55.09l-.11.642c.143-.06.33-.104.55-.104.407-.15.66.224.66.642 0 .582-.308.94-.77.94-.242 0-.44-.06-.627-.164l-.121.656c.187.09.506.149.792.149.891 0 1.463-.642 1.463-1.61 0-.94-.517-1.24-1.288-1.24zm-6.578.09l-.209 1.28c-.22-.06-.44-.09-.66-.09-.56 0-1.024.149-1.024.582 0 .194.121.328.297.403.22.09.275.12.275.209 0 .134-.143.194-.341.194a1.9 1.9 0 0 1-.638-.12l-.11.626c.22.075.55.12.847.12.891 0 1.365-.373 1.365-.94 0-.224-.132-.387-.33-.477-.165-.075-.22-.09-.22-.179 0-.104.11-.164.297-.164.165 0 .374.03.55.09l.11-.634h-.209zm13.64-.09c-.33 0-.638.09-.869.373l.088-.373h-.748l-.572 2.68h.847l.33-1.582c.11-.507.396-.776.737-.776.11 0 .22.015.308.045l.165-.746a1.274 1.274 0 0 0-.286-.03zm-8.415.09l-.671 2.68h.847l.66-2.68h-.836zm-3.96 0l-.847 1.88-.341-1.88h-.902l-.781 2.68h.792l.484-1.85.396 1.85h.55l.99-1.85-.495 1.85h.803l.847-2.68h-1.497z",fill:"#00579f"})]}),"mastercard"===e.brand&&(0,s.jsxs)("svg",{viewBox:"0 0 32 21",className:"h-5 w-5",children:[(0,s.jsx)("path",{d:"M26.58 21H5.42A5.42 5.42 0 0 1 0 15.58V5.42A5.42 5.42 0 0 1 5.42 0h21.16A5.42 5.42 0 0 1 32 5.42v10.16A5.42 5.42 0 0 1 26.58 21z",fill:"#fff"}),(0,s.jsx)("path",{d:"M23.209 7.465c0-1.196-.946-2.142-2.142-2.142h-10.12c-1.196 0-2.142.946-2.142 2.142v6.07c0 1.196.946 2.142 2.142 2.142h10.12c1.196 0 2.142-.946 2.142-2.142v-6.07z",fill:"#ff5f00"}),(0,s.jsx)("path",{d:"M12.947 10.5a4.006 4.006 0 0 1 1.535-3.16a4.006 4.006 0 1 0 0 6.32a4.006 4.006 0 0 1-1.535-3.16z",fill:"#eb001b"}),(0,s.jsx)("path",{d:"M20.994 10.5a4.006 4.006 0 0 1-6.512 3.16a4.006 4.006 0 0 0 0-6.32a4.006 4.006 0 0 1 6.512 3.16z",fill:"#f79e1b"})]}),(!e.brand||"visa"!==e.brand&&"mastercard"!==e.brand)&&(0,s.jsx)(h.A,{className:"h-5 w-5 ".concat(e.isDefault?"text-primary":"")})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsxs)("p",{className:"font-medium",children:["•••• ",e.last4]}),e.isDefault&&(0,s.jsx)("span",{className:"ml-2 text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full",children:"Default"})]}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.brand&&e.brand.charAt(0).toUpperCase()+e.brand.slice(1)," • Expires ",e.expMonth,"/",e.expYear]}),e.cardholderName&&(0,s.jsx)("p",{className:"text-sm mt-1",children:e.cardholderName})]})]})})})}):null},[u,d]);(0,r.useEffect)(()=>{(async()=>{try{g(!0);let e=await N();m(e);let t=e.find(e=>e.isDefault);t?x(t._id):e.length>0&&x(e[0]._id)}catch(e){console.error("Error fetching payment methods:",e),o("Failed to load payment methods")}finally{g(!1)}})()},[o]);let k=async()=>{if(!u){o("Please select a payment method");return}j(!0);try{let e=d.find(e=>e._id===u);if(!e)throw Error("Selected payment method not found");let s=await S({paymentMethodId:e.stripePaymentMethodId,amount:t,currency:a,description:n});if(s.success)l();else if("requires_action"===s.status||"requires_confirmation"===s.status)throw Error("This payment requires additional authentication. Please use a different payment method.");else throw Error("Payment failed: ".concat(s.status))}catch(e){console.error("Payment error:",e),o(e.message||"Payment processing failed")}finally{j(!1)}};return f?(0,s.jsx)("div",{className:"flex justify-center items-center py-8",children:(0,s.jsx)(p.A,{className:"h-8 w-8 animate-spin text-primary"})}):0===d.length?(0,s.jsx)("div",{className:"text-center py-4",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"No saved payment methods"})}):(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("label",{className:"text-sm font-medium flex items-center",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2 text-primary"}),"Select Payment Method"]}),(0,s.jsxs)(D.l6,{value:u||"",onValueChange:x,children:[(0,s.jsx)(D.bq,{className:"bg-white dark:bg-gray-800",children:(0,s.jsx)(D.yv,{placeholder:"Select a payment method"})}),(0,s.jsx)(D.gC,{children:v})]})]}),w,(0,s.jsx)(c.$,{onClick:k,disabled:!u||y||t<1e3,className:"w-full bg-green-600 hover:bg-green-700 text-white",children:y?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"mr-2 h-4 w-4",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[(0,s.jsx)("path",{d:"M13 9L15 11L21 5",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,s.jsx)("path",{d:"M21 12V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H16",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})]}),"Recharge ",b]})})]})});var T=a(80333),R=a(56671);let I="http://localhost:4000";async function $(){let e=await fetch("".concat(I,"/api/users/me/auto-recharge"),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch auto-recharge settings");return e.json()}async function L(e){let t=await fetch("".concat(I,"/api/users/me/auto-recharge"),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Failed to update auto-recharge settings");return t.json()}function H(){let[e,t]=(0,r.useState)(!0),[a,n]=(0,r.useState)(!1),[d,m]=(0,r.useState)(!1),[u,h]=(0,r.useState)(1),[x,f]=(0,r.useState)(0);(0,r.useEffect)(()=>{(async()=>{try{t(!0);let e=await $();m(e.autoRechargeEnabled),h(e.autoRechargeThreshold),f(e.autoRechargeAmount)}catch(e){console.error("Failed to fetch auto-recharge settings:",e),R.o.error("Failed to load auto-recharge settings. Please try again.")}finally{t(!1)}})()},[]);let g=async()=>{try{if(n(!0),u<0)throw Error("Threshold cannot be negative");await L({autoRechargeEnabled:d,autoRechargeThreshold:u}),R.o.success("Auto-recharge settings updated successfully")}catch(e){console.error("Failed to update auto-recharge settings:",e),R.o.error(e.message||"Failed to update auto-recharge settings")}finally{n(!1)}};return(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsx)(i.ZB,{className:"text-lg",children:"Auto-Recharge Settings"}),(0,s.jsx)(i.BT,{children:"Automatically add funds when your balance falls below a threshold"})]}),(0,s.jsx)(i.Wu,{children:e?(0,s.jsx)("div",{className:"flex justify-center py-6",children:(0,s.jsx)(p.A,{className:"h-6 w-6 animate-spin text-primary"})}):(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"space-y-0.5",children:[(0,s.jsx)(o.J,{children:"Enable Auto-Recharge"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Automatically add funds when your balance is low"})]}),(0,s.jsx)(T.d,{checked:d,onCheckedChange:m})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.J,{htmlFor:"amount",children:"Amount to reload ($)"}),(0,s.jsx)(l.p,{id:"amount",type:"number",step:"0.01",min:"10",value:x,onChange:e=>f(parseFloat(e.target.value)),placeholder:"10.00"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Amount to add when auto-recharge is triggered (minimum $10)"})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(o.J,{htmlFor:"threshold",children:"When threshold reaches ($)"}),(0,s.jsx)(l.p,{id:"threshold",type:"number",step:"0.01",min:"0",value:u,onChange:e=>h(parseFloat(e.target.value)),placeholder:"1.00",disabled:!d}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Auto-recharge when your balance falls below this amount"})]}),(0,s.jsx)(c.$,{onClick:g,disabled:a||!d,className:"w-full",children:a?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):"Save Settings"})]})})]})}var Z=a(11518),U=a.n(Z),J=a(54165),q=a(40646);function O(e){let{open:t,onOpenChange:a,title:n,description:i,actionLabel:l,onAction:o}=e,[d,m]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{if(t){m(!0);let e=setTimeout(()=>{m(!1)},3e3);return()=>clearTimeout(e)}},[t]),(0,s.jsxs)(J.lG,{open:t,onOpenChange:a,children:[(0,s.jsxs)(J.Cf,{className:"sm:max-w-md",children:[(0,s.jsxs)(J.c7,{className:"flex flex-col items-center text-center",children:[(0,s.jsx)("div",{className:"jsx-8384fac354370dcf bg-green-100 dark:bg-green-900/30 p-3 rounded-full mb-4",children:(0,s.jsx)(q.A,{className:"h-8 w-8 text-green-600 dark:text-green-400"})}),(0,s.jsx)(J.L3,{className:"text-xl",children:n}),(0,s.jsx)(J.rr,{className:"pt-2 max-w-sm mx-auto",children:i})]}),(0,s.jsx)("div",{className:"jsx-8384fac354370dcf flex justify-center mt-4",children:l&&o?(0,s.jsx)(c.$,{onClick:o,children:l}):(0,s.jsx)(c.$,{onClick:()=>a(!1),children:"Close"})}),d&&(0,s.jsx)("div",{className:"jsx-8384fac354370dcf confetti-container",children:Array.from({length:50}).map((e,t)=>(0,s.jsx)("div",{style:{left:"".concat(100*Math.random(),"%"),animationDelay:"".concat(3*Math.random(),"s"),backgroundColor:"hsl(".concat(360*Math.random(),", 100%, 50%)")},className:"jsx-8384fac354370dcf confetti"},t))})]}),(0,s.jsx)(U(),{id:"8384fac354370dcf",children:".confetti-container{position:absolute;top:0;left:0;width:100%;height:100%;overflow:hidden;pointer-events:none;z-index:1000}.confetti{position:absolute;width:10px;height:10px;opacity:0;-webkit-transform:translatey(0)rotate(0);-moz-transform:translatey(0)rotate(0);-ms-transform:translatey(0)rotate(0);-o-transform:translatey(0)rotate(0);transform:translatey(0)rotate(0);-webkit-animation:confetti-fall 3s ease-out forwards;-moz-animation:confetti-fall 3s ease-out forwards;-o-animation:confetti-fall 3s ease-out forwards;animation:confetti-fall 3s ease-out forwards}@-webkit-keyframes confetti-fall{0%{opacity:1;-webkit-transform:translatey(-100px)rotate(0deg);transform:translatey(-100px)rotate(0deg)}100%{opacity:0;-webkit-transform:translatey(100vh)rotate(720deg);transform:translatey(100vh)rotate(720deg)}}@-moz-keyframes confetti-fall{0%{opacity:1;-moz-transform:translatey(-100px)rotate(0deg);transform:translatey(-100px)rotate(0deg)}100%{opacity:0;-moz-transform:translatey(100vh)rotate(720deg);transform:translatey(100vh)rotate(720deg)}}@-o-keyframes confetti-fall{0%{opacity:1;-o-transform:translatey(-100px)rotate(0deg);transform:translatey(-100px)rotate(0deg)}100%{opacity:0;-o-transform:translatey(100vh)rotate(720deg);transform:translatey(100vh)rotate(720deg)}}@keyframes confetti-fall{0%{opacity:1;-webkit-transform:translatey(-100px)rotate(0deg);-moz-transform:translatey(-100px)rotate(0deg);-o-transform:translatey(-100px)rotate(0deg);transform:translatey(-100px)rotate(0deg)}100%{opacity:0;-webkit-transform:translatey(100vh)rotate(720deg);-moz-transform:translatey(100vh)rotate(720deg);-o-transform:translatey(100vh)rotate(720deg);transform:translatey(100vh)rotate(720deg)}}"})]})}let V=e=>new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});function W(){let e=(0,n.useRouter)(),[t,a]=(0,r.useState)("add-funds"),[x,w]=(0,r.useState)("card"),[S,M]=(0,r.useState)("25"),[E,F]=(0,r.useState)(""),[D,T]=(0,r.useState)(!1),[R,I]=(0,r.useState)([]),[$,L]=(0,r.useState)([]),[Z,U]=(0,r.useState)(!1),[J,q]=(0,r.useState)(""),[W,Y]=(0,r.useState)(!1),[X,G]=(0,r.useState)(!1),[K,Q]=(0,r.useState)({title:"",description:""}),[ee,et]=(0,r.useState)({freeMinutesRemaining:0,paidMinutes:0,totalMinutesAvailable:0,usingFreeCredits:!1,callPricePerMinute:.1,monthlyResetDate:1,monthlyAllowance:0}),[ea,es]=(0,r.useState)(100*parseInt(S)),[er,en]=(0,r.useState)(!0);(0,r.useEffect)(()=>{if("custom"===S){let e=parseFloat(E||"0");en(e>=10&&!isNaN(e)),es(Math.round(100*e))}else es(100*parseInt(S))},[S,E]);let ei=async()=>{try{let e=await (0,z.mP)();return console.log("Refreshed user credits:",e),et({freeMinutesRemaining:e.freeMinutesRemaining||0,paidMinutes:e.paidMinutes||0,totalMinutesAvailable:e.totalMinutesAvailable||0,usingFreeCredits:e.usingFreeCredits||!1,callPricePerMinute:e.callPricePerMinute||.1,monthlyResetDate:e.monthlyResetDate||1,monthlyAllowance:e.monthlyAllowance||0}),e}catch(e){return console.error("Error fetching user credits:",e),null}};(0,r.useEffect)(()=>{(async()=>{try{if(U(!0),"payment-history"===t){let e=await C();I(e.transactions)}else if("add-funds"===t){let e=await N();L(e),Y(0===e.length)}await ei()}catch(e){console.error("Error fetching data:",e),q("payment-history"===t?"Failed to load transaction history":"Failed to load payment methods")}finally{U(!1)}})()},[t]);let el=()=>{T(!0),q("");try{if(ea<1e3)throw Error("Minimum amount is $10");"paypal"===x?alert("PayPal integration would be implemented here"):"apple"===x&&alert("Apple Pay integration would be implemented here")}catch(e){console.error("Payment error:",e),q(e.message||"Payment processing failed")}finally{T(!1)}},eo=async()=>{await ei(),Q({title:"Payment Successful",description:"Your payment of ".concat(new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(ea/100)," has been processed successfully.")}),G(!0),"payment-history"===t&&I((await C()).transactions)};return(0,s.jsxs)(v.default,{children:[(0,s.jsx)(O,{open:X,onOpenChange:G,title:K.title,description:K.description}),(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-8",children:[(0,s.jsxs)(c.$,{variant:"ghost",size:"icon",onClick:()=>e.back(),className:"rounded-full hover:bg-gray-100 dark:hover:bg-gray-800",children:[(0,s.jsx)(f.A,{className:"h-5 w-5"}),(0,s.jsx)("span",{className:"sr-only",children:"Back"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Billing & Payments"}),(0,s.jsx)("p",{className:"text-muted-foreground mt-1",children:"Manage your payment methods and view transaction history"})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[(0,s.jsxs)("div",{className:"lg:col-span-1 space-y-4",children:[(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{className:"text-xl",children:"Current Plan"})}),(0,s.jsxs)(i.Wu,{className:"space-y-4",children:[(0,s.jsx)("div",{className:"space-y-4",children:ee.monthlyAllowance>0?(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Monthly Allowance"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-foreground",children:[ee.monthlyAllowance.toFixed(0)," ",(0,s.jsx)("span",{className:"text-lg font-normal text-muted-foreground",children:"minutes"})]})]}),ee.paidMinutes>0&&(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsx)("p",{className:"text-xs font-medium text-muted-foreground mb-1",children:"Balance"}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("div",{className:"w-2 h-2 rounded-full bg-emerald-500"}),(0,s.jsxs)("span",{className:"text-lg font-semibold text-emerald-600 dark:text-emerald-400",children:["$",(ee.paidMinutes*ee.callPricePerMinute).toFixed(2)]})]}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:["(",ee.paidMinutes.toFixed(0)," min)"]})]})]}):(0,s.jsx)("div",{className:"text-center space-y-3",children:ee.paidMinutes>0?(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground mb-2",children:"Current Balance"}),(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full bg-emerald-500"}),(0,s.jsxs)("span",{className:"text-3xl font-bold text-emerald-600 dark:text-emerald-400",children:["$",(ee.paidMinutes*ee.callPricePerMinute).toFixed(2)]})]}),(0,s.jsxs)("p",{className:"text-sm text-muted-foreground mt-1",children:[ee.paidMinutes.toFixed(0)," minutes available"]})]}):(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground mb-2",children:"Current Balance"}),(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,s.jsx)("div",{className:"w-3 h-3 rounded-full bg-gray-400"}),(0,s.jsx)("span",{className:"text-3xl font-bold text-gray-500 dark:text-gray-400",children:"$0.00"})]}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:"Add funds to start making calls"})]})})}),(0,s.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,s.jsx)("span",{className:"text-emerald-700 dark:text-emerald-400",children:"Minutes Remaining"}),(0,s.jsxs)("span",{className:"font-medium text-emerald-600 dark:text-emerald-400",children:[ee.totalMinutesAvailable.toFixed(0)," min"]})]}),(0,s.jsx)("div",{className:"h-2 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden",children:(0,s.jsx)("div",{className:"h-full bg-emerald-500 rounded-full",style:{width:"".concat(Math.min(100,ee.totalMinutesAvailable/Math.max(ee.monthlyAllowance,1)*100),"%")}})})]}),(0,s.jsx)("div",{className:"pt-4 border-t border-gray-200 dark:border-gray-700",children:(0,s.jsx)("div",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium text-muted-foreground",children:"Next Reset Date"}),(0,s.jsx)("p",{className:"text-sm font-semibold",children:(()=>{let e=new Date,t=e.getMonth(),a=e.getFullYear(),s=ee.monthlyResetDate||1,r=new Date(a,t,s);return e.getDate()>=s&&(r=new Date(a,t+1,s)),r.toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})})()})]})})})]})]}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{className:"pb-2 flex flex-row items-center justify-between",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(i.ZB,{className:"text-xl",children:"Payment Methods"}),(0,s.jsx)(i.BT,{children:"Manage your saved payment methods"})]}),"add-funds"===t&&(0,s.jsxs)(c.$,{variant:"outline",size:"sm",onClick:()=>Y(!0),children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Add New Card"]})]}),(0,s.jsx)(i.Wu,{className:"pt-2",children:Z?(0,s.jsx)("div",{className:"flex justify-center py-6",children:(0,s.jsx)(p.A,{className:"h-6 w-6 animate-spin text-primary"})}):0===$.length?(0,s.jsxs)("div",{className:"text-center py-6",children:[(0,s.jsx)("div",{className:"bg-gray-100 dark:bg-gray-800 p-4 rounded-md inline-flex mb-3",children:(0,s.jsx)(h.A,{className:"h-6 w-6 text-muted-foreground"})}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"No payment methods saved"}),(0,s.jsx)(c.$,{variant:"outline",size:"sm",className:"mt-4",onClick:()=>{a("add-funds"),Y(!0)},children:"Add Payment Method"})]}):(0,s.jsx)("div",{className:"space-y-3",children:$.map(e=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg border ".concat(e.isDefault?"border-primary/50 bg-primary/5":"border-border"),children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"p-2 rounded-md ".concat(e.isDefault?"bg-primary/10":"bg-gray-100 dark:bg-gray-800"),children:(0,s.jsx)(h.A,{className:"h-5 w-5 ".concat(e.isDefault?"text-primary":"")})}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsxs)("p",{className:"font-medium",children:["•••• ",e.last4]}),e.isDefault&&(0,s.jsx)("span",{className:"ml-2 text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full",children:"Default"})]}),(0,s.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.brand&&e.brand.charAt(0).toUpperCase()+e.brand.slice(1)," ","• Expires ",e.expMonth,"/",e.expYear]})]})]}),(0,s.jsxs)("div",{className:"flex gap-1",children:[!e.isDefault&&(0,s.jsx)(c.$,{variant:"ghost",size:"sm",onClick:async()=>{try{await k(e._id);let t=await N();L(t)}catch(e){console.error("Error setting default payment method:",e),q("Failed to set default payment method")}},title:"Set as default",children:(0,s.jsx)(g.A,{className:"h-4 w-4"})}),(0,s.jsx)(c.$,{variant:"ghost",size:"sm",onClick:async()=>{try{await A(e._id);let t=await N();L(t)}catch(e){console.error("Error removing payment method:",e),q("Failed to remove payment method")}},title:"Remove payment method",children:(0,s.jsx)(y.A,{className:"h-4 w-4 text-destructive"})})]})]},e._id))})})]})]}),(0,s.jsx)("div",{className:"lg:col-span-2",children:(0,s.jsxs)(d.tU,{value:t,onValueChange:a,className:"space-y-6",children:[(0,s.jsxs)(d.j7,{className:"grid grid-cols-3",children:[(0,s.jsx)(d.Xi,{value:"add-funds",children:"Add Funds"}),(0,s.jsx)(d.Xi,{value:"payment-history",children:"Payment History"}),(0,s.jsx)(d.Xi,{value:"auto-recharge",children:"Auto-Recharge"})]}),(0,s.jsxs)(d.av,{value:"add-funds",className:"space-y-6",children:[(0,s.jsxs)(i.Zp,{children:[(0,s.jsx)(i.aR,{children:(0,s.jsx)(i.ZB,{className:"text-lg",children:"Select Amount"})}),(0,s.jsxs)(i.Wu,{children:["custom"!==S&&10>parseInt(S)&&(0,s.jsx)("p",{className:"text-red-500 text-sm mb-3",children:"Minimum amount is $10"}),(0,s.jsxs)(u.z,{value:S,onValueChange:M,className:"grid grid-cols-3 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(u.C,{value:"25",id:"amount-25",className:"peer sr-only"}),(0,s.jsxs)(o.J,{htmlFor:"amount-25",className:"flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white dark:bg-gray-800 p-4 hover:bg-gray-50 dark:hover:bg-gray-700 peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer",children:[(0,s.jsx)("span",{className:"text-xs text-muted-foreground mb-1",children:"Basic"}),(0,s.jsx)("span",{className:"text-2xl font-bold",children:"$25"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u.C,{value:"50",id:"amount-50",className:"peer sr-only"}),(0,s.jsxs)(o.J,{htmlFor:"amount-50",className:"flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white dark:bg-gray-800 p-4 hover:bg-gray-50 dark:hover:bg-gray-700 peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer",children:[(0,s.jsx)("span",{className:"text-xs text-muted-foreground mb-1",children:"Standard"}),(0,s.jsx)("span",{className:"text-2xl font-bold",children:"$50"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)(u.C,{value:"100",id:"amount-100",className:"peer sr-only"}),(0,s.jsxs)(o.J,{htmlFor:"amount-100",className:"flex flex-col items-center justify-between rounded-md border-2 border-muted bg-white dark:bg-gray-800 p-4 hover:bg-gray-50 dark:hover:bg-gray-700 peer-data-[state=checked]:border-primary [&:has([data-state=checked])]:border-primary cursor-pointer",children:[(0,s.jsx)("span",{className:"text-xs text-muted-foreground mb-1",children:"Plus"}),(0,s.jsx)("span",{className:"text-2xl font-bold",children:"$100"})]})]})]}),(0,s.jsxs)("div",{className:"mt-4 relative",children:[(0,s.jsx)(o.J,{htmlFor:"custom-amount",className:"text-sm",children:"Custom Amount"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("span",{className:"absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground",children:"$"}),(0,s.jsx)(l.p,{id:"custom-amount",type:"number",min:"10",placeholder:"Enter amount (min $10)",className:"pl-7 ".concat("custom"!==S||er?"":"border-red-500"),value:E,onChange:e=>F(e.target.value),onClick:()=>M("custom")})]}),"custom"===S&&!er&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:"Minimum amount is $10"})]})]})]}),"card"===x&&(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{className:"flex flex-row items-center justify-between",children:[(0,s.jsx)(i.ZB,{className:"text-lg",children:"Card Details"}),$.length>0&&(0,s.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>Y(!W),children:W?"Use Saved Card":"Use New Card"})]}),(0,s.jsx)(i.Wu,{children:(0,s.jsxs)(P,{children:[J&&(0,s.jsx)("div",{className:"text-red-500 text-sm mb-4",children:J}),W?(0,s.jsx)(_,{amount:ea,currency:"usd",description:"Adding funds: $".concat("custom"===S?E:S),onSuccess:async()=>{if("custom"===S?!er:10>parseInt(S)){q("Minimum amount is $10");return}await eo()},onError:e=>{q(e)}}):(0,s.jsx)(B,{amount:ea,currency:"usd",description:"Adding funds: $".concat("custom"===S?E:S),onSuccess:async()=>{if("custom"===S?!er:10>parseInt(S)){q("Minimum amount is $10");return}await eo()},onError:e=>{q(e)}})]})})]}),"paypal"===x&&(0,s.jsx)(i.Zp,{children:(0,s.jsxs)(i.Wu,{className:"p-6 text-center",children:[(0,s.jsx)("p",{className:"text-lg font-medium mb-4",children:"Continue to PayPal to complete your payment"}),(0,s.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:"You'll be redirected to PayPal to complete your payment securely."}),(0,s.jsx)(c.$,{onClick:el,disabled:D||("custom"===S?!er:10>parseInt(S)),children:D?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):"Pay with PayPal"})]})}),"apple"===x&&(0,s.jsx)(i.Zp,{children:(0,s.jsxs)(i.Wu,{className:"p-6 text-center",children:[(0,s.jsx)("p",{className:"text-lg font-medium mb-4",children:"Continue with Apple Pay"}),(0,s.jsx)("p",{className:"text-gray-500 dark:text-gray-400 mb-6",children:"You'll be prompted to authenticate your payment with Apple Pay."}),(0,s.jsx)(c.$,{onClick:el,disabled:D||("custom"===S?!er:10>parseInt(S)),className:"bg-black hover:bg-gray-800",children:D?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):"Pay with Apple Pay"})]})})]}),(0,s.jsx)(d.av,{value:"auto-recharge",children:(0,s.jsx)(H,{})}),(0,s.jsx)(d.av,{value:"payment-history",children:(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{className:"pb-2",children:[(0,s.jsx)(i.ZB,{className:"text-lg",children:"Payment History"}),(0,s.jsx)(i.BT,{children:"View all your past transactions"})]}),(0,s.jsxs)(i.Wu,{children:[(0,s.jsx)("div",{className:"flex justify-between items-center mb-6",children:(0,s.jsxs)("div",{className:"relative w-64",children:[(0,s.jsx)(j.A,{className:"absolute left-2.5 top-2.5 h-4 w-4 text-gray-500 dark:text-gray-400"}),(0,s.jsx)(l.p,{placeholder:"Search transactions...",className:"pl-8"})]})}),(0,s.jsx)("div",{className:"rounded-md border",children:(0,s.jsxs)(m.XI,{children:[(0,s.jsx)(m.A0,{children:(0,s.jsxs)(m.Hj,{children:[(0,s.jsx)(m.nd,{children:"Date"}),(0,s.jsx)(m.nd,{children:"ID"}),(0,s.jsx)(m.nd,{children:"Email"}),(0,s.jsx)(m.nd,{className:"text-right",children:"Amount"}),(0,s.jsx)(m.nd,{children:"Status"}),(0,s.jsx)(m.nd,{className:"text-right",children:"Actions"})]})}),(0,s.jsx)(m.BF,{children:Z?(0,s.jsx)(m.Hj,{children:(0,s.jsxs)(m.nA,{colSpan:6,className:"text-center py-8",children:[(0,s.jsx)(p.A,{className:"h-6 w-6 animate-spin mx-auto"}),(0,s.jsx)("p",{className:"mt-2 text-sm text-muted-foreground",children:"Loading transaction history..."})]})}):J?(0,s.jsx)(m.Hj,{children:(0,s.jsx)(m.nA,{colSpan:6,className:"text-center py-8 text-red-500",children:J})}):0===R.length?(0,s.jsx)(m.Hj,{children:(0,s.jsx)(m.nA,{colSpan:6,className:"text-center py-8",children:(0,s.jsx)("p",{className:"text-muted-foreground",children:"No transactions found"})})}):R.map(e=>(0,s.jsxs)(m.Hj,{children:[(0,s.jsx)(m.nA,{children:V(e.createdAt)}),(0,s.jsx)(m.nA,{children:e.stripePaymentIntentId.substring(0,8)}),(0,s.jsx)(m.nA,{className:"max-w-[200px] truncate",children:e.email}),(0,s.jsxs)(m.nA,{className:"text-right font-medium",children:["$",e.amount.toFixed(2)]}),(0,s.jsx)(m.nA,{children:(0,s.jsx)("span",{className:"inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ".concat("completed"===e.status||"succeeded"===e.status?"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400":"failed"===e.status?"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400":"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"),children:"completed"===e.status||"succeeded"===e.status?"Completed":"failed"===e.status?"Failed":"Pending"})}),(0,s.jsx)(m.nA,{className:"text-right",children:(0,s.jsxs)(c.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",children:[(0,s.jsx)(b.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"View receipt"})]})})]},e._id))})]})})]})]})})]})})]})]})]})}h.A,x.A},85057:(e,t,a)=>{"use strict";a.d(t,{J:()=>i});var s=a(95155);a(12115);var r=a(40968),n=a(59434);function i(e){let{className:t,...a}=e;return(0,s.jsx)(r.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},85127:(e,t,a)=>{"use strict";a.d(t,{A0:()=>i,BF:()=>l,Hj:()=>o,XI:()=>n,nA:()=>d,nd:()=>c});var s=a(95155);a(12115);var r=a(59434);function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",t),...a})})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-muted-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4201,4341,6403,1071,6671,6544,226,1906,6928,8441,1684,7358],()=>t(44671)),_N_E=e.O()}]);