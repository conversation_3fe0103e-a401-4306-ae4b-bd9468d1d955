(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8268],{17313:(e,s,r)=>{"use strict";r.d(s,{Xi:()=>o,av:()=>c,j7:()=>l,tU:()=>n});var i=r(95155);r(12115);var a=r(60704),t=r(59434);function n(e){let{className:s,...r}=e;return(0,i.jsx)(a.bL,{"data-slot":"tabs",className:(0,t.cn)("flex flex-col gap-2",s),...r})}function l(e){let{className:s,...r}=e;return(0,i.jsx)(a.B8,{"data-slot":"tabs-list",className:(0,t.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-1",s),...r})}function o(e){let{className:s,...r}=e;return(0,i.jsx)(a.l9,{"data-slot":"tabs-trigger",className:(0,t.cn)("data-[state=active]:bg-background data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring inline-flex flex-1 items-center justify-center gap-1.5 rounded-md px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",s),...r})}function c(e){let{className:s,...r}=e;return(0,i.jsx)(a.UC,{"data-slot":"tabs-content",className:(0,t.cn)("flex-1 outline-none",s),...r})}},26126:(e,s,r)=>{"use strict";r.d(s,{E:()=>o});var i=r(95155);r(12115);var a=r(99708),t=r(74466),n=r(59434);let l=(0,t.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:s,variant:r,asChild:t=!1,...o}=e,c=t?a.DX:"span";return(0,i.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(l({variant:r}),s),...o})}},30285:(e,s,r)=>{"use strict";r.d(s,{$:()=>o,r:()=>l});var i=r(95155);r(12115);var a=r(99708),t=r(74466),n=r(59434);let l=(0,t.F)("inline-flex items-center cursor-pointer justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:s,variant:r,size:t,asChild:o=!1,...c}=e,d=o?a.DX:"button";return(0,i.jsx)(d,{"data-slot":"button",className:(0,n.cn)(l({variant:r,size:t,className:s})),...c})}},30356:(e,s,r)=>{"use strict";r.d(s,{C:()=>o,z:()=>l});var i=r(95155);r(12115);var a=r(54059),t=r(9428),n=r(59434);function l(e){let{className:s,...r}=e;return(0,i.jsx)(a.bL,{"data-slot":"radio-group",className:(0,n.cn)("grid gap-3",s),...r})}function o(e){let{className:s,...r}=e;return(0,i.jsx)(a.q7,{"data-slot":"radio-group-item",className:(0,n.cn)("border-input text-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive aspect-square size-4 shrink-0 rounded-full border shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",s),...r,children:(0,i.jsx)(a.C1,{"data-slot":"radio-group-indicator",className:"relative flex items-center justify-center",children:(0,i.jsx)(t.A,{className:"fill-primary absolute top-1/2 left-1/2 size-2 -translate-x-1/2 -translate-y-1/2"})})})}},59434:(e,s,r)=>{"use strict";r.d(s,{cn:()=>t,v:()=>n});var i=r(52596),a=r(39688);function t(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,a.QP)((0,i.$)(s))}function n(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}},62523:(e,s,r)=>{"use strict";r.d(s,{p:()=>t});var i=r(95155);r(12115);var a=r(59434);function t(e){let{className:s,type:r,...t}=e;return(0,i.jsx)("input",{type:r,"data-slot":"input",className:(0,a.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",s),...t})}},66695:(e,s,r)=>{"use strict";r.d(s,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>t,aR:()=>n,wL:()=>d});var i=r(95155);r(12115);var a=r(59434);function t(e){let{className:s,...r}=e;return(0,i.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",s),...r})}function n(e){let{className:s,...r}=e;return(0,i.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("flex flex-col gap-1.5 px-6",s),...r})}function l(e){let{className:s,...r}=e;return(0,i.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",s),...r})}function o(e){let{className:s,...r}=e;return(0,i.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",s),...r})}function c(e){let{className:s,...r}=e;return(0,i.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",s),...r})}function d(e){let{className:s,...r}=e;return(0,i.jsx)("div",{"data-slot":"card-footer",className:(0,a.cn)("flex items-center px-6",s),...r})}},74891:(e,s,r)=>{"use strict";r.d(s,{default:()=>N});var i=r(95155),a=r(12115),t=r(30285),n=r(85127),l=r(62523),o=r(85057),c=r(17313),d=r(26126),u=r(66695),m=r(35169),x=r(47924),h=r(51154),p=r(13052),v=r(40646),g=r(35695),b=r(66766),f=r(30356);let j=[{id:"twilio",name:"Twilio",logo:"https://www.vectorlogo.zone/logos/twilio/twilio-icon.svg"},{id:"vonage",name:"Vonage",logo:"https://www.vectorlogo.zone/logos/ansi/ansi-icon.svg"},{id:"telnyx",name:"Telnyx",logo:"https://www.vectorlogo.zone/logos/google_cloud_run/google_cloud_run-icon.svg"}];function N(){var e;let s=(0,g.useRouter)(),[r,N]=(0,a.useState)(""),[y,w]=(0,a.useState)(""),[k,F]=(0,a.useState)("twilio"),[P,A]=(0,a.useState)(!1),[C,S]=(0,a.useState)("search"),[z,_]=(0,a.useState)(null),[B,$]=(0,a.useState)(!1),Z=({twilio:[{id:"1",number:"+****************",region:"San Francisco, CA",capabilities:["voice","sms","mms"],monthlyPrice:1,setupFee:0,provider:"twilio"},{id:"2",number:"+****************",region:"New York, NY",capabilities:["voice","sms"],monthlyPrice:1,setupFee:0,provider:"twilio"},{id:"3",number:"+****************",region:"Chicago, IL",capabilities:["voice","sms","mms"],monthlyPrice:1,setupFee:0,provider:"twilio"},{id:"9",number:"+****************",region:"Pittsburgh, PA",capabilities:["voice","sms"],monthlyPrice:1,setupFee:0,provider:"twilio"},{id:"10",number:"+****************",region:"Boston, MA",capabilities:["voice","sms","mms"],monthlyPrice:1,setupFee:0,provider:"twilio"},{id:"11",number:"+1 (503) 555-5566",region:"Portland, OR",capabilities:["voice","sms"],monthlyPrice:1,setupFee:0,provider:"twilio"},{id:"12",number:"+1 (713) 555-7788",region:"Houston, TX",capabilities:["voice","sms","mms"],monthlyPrice:1,setupFee:0,provider:"twilio"}],vonage:[{id:"4",number:"+****************",region:"San Mateo, CA",capabilities:["voice","sms"],monthlyPrice:.9,setupFee:0,provider:"vonage"},{id:"5",number:"+****************",region:"Miami, FL",capabilities:["voice","sms"],monthlyPrice:.9,setupFee:0,provider:"vonage"},{id:"13",number:"+****************",region:"Minneapolis, MN",capabilities:["voice","sms"],monthlyPrice:.9,setupFee:0,provider:"vonage"}],telnyx:[{id:"6",number:"+****************",region:"Dallas, TX",capabilities:["voice","sms","mms"],monthlyPrice:.8,setupFee:0,provider:"telnyx"},{id:"7",number:"+****************",region:"Atlanta, GA",capabilities:["voice","sms"],monthlyPrice:.8,setupFee:0,provider:"telnyx"},{id:"8",number:"+****************",region:"Seattle, WA",capabilities:["voice","sms","mms"],monthlyPrice:.8,setupFee:0,provider:"telnyx"},{id:"14",number:"+****************",region:"Las Vegas, NV",capabilities:["voice","sms"],monthlyPrice:.8,setupFee:0,provider:"telnyx"}]})[k].filter(e=>{if(!r&&!y)return!0;let s=!r||e.number.includes(r)||e.region.toLowerCase().includes(r.toLowerCase()),i=!y||e.number.replace(/\D/g,"").substring(1,4)===y;return s&&i}),D=async()=>{z&&($(!0),await new Promise(e=>setTimeout(e,1500)),s.push("/phonenumber"))};return(0,i.jsxs)("div",{className:"container py-6",children:[(0,i.jsx)("div",{className:"flex items-center justify-between mb-4",children:(0,i.jsxs)("div",{className:"flex items-center",children:[(0,i.jsx)(t.$,{variant:"ghost",className:"mr-2",onClick:()=>s.push("/phonenumber"),children:(0,i.jsx)(m.A,{className:"h-4 w-4 mr-2"})}),(0,i.jsx)("h1",{className:"text-2xl font-semibold",children:"Buy a Phone Number"})]})}),(0,i.jsxs)(c.tU,{value:C,onValueChange:S,children:[(0,i.jsxs)(c.j7,{className:"grid w-full grid-cols-2 mb-6",children:[(0,i.jsx)(c.Xi,{value:"search",children:"Search for Numbers"}),(0,i.jsx)(c.Xi,{value:"configure",disabled:!z,children:"Configure Number"})]}),(0,i.jsx)(c.av,{value:"search",children:(0,i.jsxs)("div",{className:"grid gap-6 md:grid-cols-4",children:[(0,i.jsxs)(u.Zp,{className:"md:col-span-1",children:[(0,i.jsx)(u.aR,{children:(0,i.jsx)(u.ZB,{children:"Search Filters"})}),(0,i.jsxs)(u.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(o.J,{children:"Provider"}),(0,i.jsx)(f.z,{value:k,onValueChange:e=>F(e),className:"space-y-2",children:j.map(e=>(0,i.jsxs)("div",{className:"flex items-center space-x-2 rounded-md border p-3 ".concat(k===e.id?"border-primary bg-primary/5":""),children:[(0,i.jsx)(f.C,{value:e.id,id:e.id}),(0,i.jsx)(o.J,{htmlFor:e.id,className:"flex flex-1 items-center cursor-pointer",children:(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"h-10 w-10 rounded-md flex items-center justify-center bg-background border",children:(0,i.jsx)("div",{className:"relative h-6 w-6",children:(0,i.jsx)(b.default,{src:e.logo,alt:e.name,fill:!0,className:"object-contain"})})}),(0,i.jsx)("p",{className:"text-sm font-medium",children:e.name})]})})]},e.id))})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(o.J,{children:"Search"}),(0,i.jsxs)("div",{className:"relative",children:[(0,i.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,i.jsx)(l.p,{placeholder:"Number or location",value:r,onChange:e=>N(e.target.value),className:"pl-10"})]})]}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(o.J,{children:"Area Code"}),(0,i.jsx)(l.p,{placeholder:"e.g. 415",value:y,onChange:e=>{let s=e.target.value.replace(/\D/g,"");s.length<=3&&w(s)}})]}),(0,i.jsx)(t.$,{className:"w-full mt-4",onClick:()=>{A(!0),setTimeout(()=>{A(!1)},1e3)},disabled:P,children:P?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Searching..."]}):"Search Numbers"})]})]}),(0,i.jsxs)(u.Zp,{className:"md:col-span-3",children:[(0,i.jsx)(u.aR,{children:(0,i.jsx)(u.ZB,{children:"Available Numbers"})}),(0,i.jsx)(u.Wu,{children:(0,i.jsx)("div",{className:"border rounded-md overflow-hidden",children:(0,i.jsx)("div",{className:"max-h-[calc(100vh-340px)] overflow-auto",children:(0,i.jsxs)(n.XI,{children:[(0,i.jsx)(n.A0,{className:"sticky top-0 bg-background z-10",children:(0,i.jsxs)(n.Hj,{children:[(0,i.jsx)(n.nd,{className:"bg-background w-[180px]",children:"Number"}),(0,i.jsx)(n.nd,{className:"bg-background w-[200px]",children:"Location"}),(0,i.jsx)(n.nd,{className:"bg-background",children:"Capabilities"}),(0,i.jsx)(n.nd,{className:"bg-background",children:"Price"}),(0,i.jsx)(n.nd,{className:"bg-background w-[100px]"})]})}),(0,i.jsx)(n.BF,{children:Z.length>0?Z.map(e=>(0,i.jsxs)(n.Hj,{className:(null==z?void 0:z.id)===e.id?"bg-muted/50":"",children:[(0,i.jsx)(n.nA,{className:"font-medium",children:e.number}),(0,i.jsx)(n.nA,{children:e.region}),(0,i.jsx)(n.nA,{children:(0,i.jsx)("div",{className:"flex flex-wrap gap-1",children:e.capabilities.map(e=>(0,i.jsx)(d.E,{variant:"outline",className:"capitalize",children:e},e))})}),(0,i.jsxs)(n.nA,{children:["$",e.monthlyPrice.toFixed(2),"/mo"]}),(0,i.jsx)(n.nA,{children:(0,i.jsxs)(t.$,{variant:"outline",size:"sm",className:"w-full",onClick:()=>{_(e),S("configure")},children:["Select ",(0,i.jsx)(p.A,{className:"ml-1 h-4 w-4"})]})})]},e.id)):(0,i.jsx)(n.Hj,{children:(0,i.jsx)(n.nA,{colSpan:5,className:"text-center h-24 text-muted-foreground",children:r||y?"No numbers found matching your search criteria":"Enter search criteria to find available numbers"})})})]})})})})]})]})}),(0,i.jsx)(c.av,{value:"configure",children:z&&(0,i.jsxs)("div",{className:"grid gap-6 md:grid-cols-2",children:[(0,i.jsxs)(u.Zp,{children:[(0,i.jsx)(u.aR,{children:(0,i.jsx)(u.ZB,{children:"Number Details"})}),(0,i.jsxs)(u.Wu,{className:"space-y-4",children:[(0,i.jsx)("div",{className:"p-6 border rounded-md bg-muted/30",children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Selected Number"}),(0,i.jsx)("p",{className:"text-2xl font-semibold",children:z.number}),(0,i.jsx)("p",{className:"text-sm",children:z.region})]}),(0,i.jsxs)("div",{className:"flex items-center gap-3",children:[(0,i.jsx)("div",{className:"h-12 w-12 rounded-md flex items-center justify-center bg-background border",children:(0,i.jsx)("div",{className:"relative h-7 w-7",children:(0,i.jsx)(b.default,{src:(null===(e=j.find(e=>e.id===z.provider))||void 0===e?void 0:e.logo)||"",alt:z.provider,fill:!0,className:"object-contain"})})}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Provider"}),(0,i.jsx)("p",{className:"text-lg font-medium capitalize",children:z.provider})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("p",{className:"text-sm text-muted-foreground",children:"Capabilities"}),(0,i.jsx)("div",{className:"flex flex-wrap gap-1 mt-1",children:z.capabilities.map(e=>(0,i.jsx)(d.E,{variant:"outline",className:"capitalize",children:e},e))})]})]})}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(o.J,{children:"Number Verification"}),(0,i.jsxs)("div",{className:"flex items-center space-x-2 p-4 border rounded-md bg-green-50 dark:bg-green-900/20",children:[(0,i.jsx)(v.A,{className:"h-5 w-5 text-green-500"}),(0,i.jsx)("p",{children:"This number is ready to purchase"})]})]}),(0,i.jsxs)("div",{className:"flex gap-2 pt-4",children:[(0,i.jsx)(t.$,{variant:"outline",className:"flex-1",onClick:()=>{_(null),S("search")},children:"Go Back"}),(0,i.jsx)(t.$,{className:"flex-1 bg-primary text-primary-foreground hover:bg-primary/90",onClick:D,disabled:B,children:B?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Purchasing..."]}):"Purchase Number"})]})]})]}),(0,i.jsxs)(u.Zp,{children:[(0,i.jsx)(u.aR,{children:(0,i.jsx)(u.ZB,{children:"Pricing Information"})}),(0,i.jsxs)(u.Wu,{className:"space-y-4",children:[(0,i.jsx)("div",{className:"border rounded-md p-4",children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"text-muted-foreground",children:"Monthly fee"}),(0,i.jsxs)("span",{className:"font-medium",children:["$",z.monthlyPrice.toFixed(2),"/month"]})]}),z.setupFee>0&&(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("span",{className:"text-muted-foreground",children:"Setup fee"}),(0,i.jsxs)("span",{className:"font-medium",children:["$",z.setupFee.toFixed(2)]})]}),(0,i.jsxs)("div",{className:"flex justify-between pt-4 border-t",children:[(0,i.jsx)("span",{className:"font-semibold",children:"Total today"}),(0,i.jsxs)("span",{className:"font-semibold",children:["$",(z.monthlyPrice+z.setupFee).toFixed(2)]})]}),(0,i.jsxs)("div",{className:"flex justify-between text-muted-foreground text-sm",children:[(0,i.jsx)("span",{children:"Recurring monthly charge"}),(0,i.jsxs)("span",{children:["$",z.monthlyPrice.toFixed(2)]})]})]})}),(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(o.J,{children:"Additional Information"}),(0,i.jsxs)("div",{className:"text-sm text-muted-foreground space-y-2",children:[(0,i.jsx)("p",{children:"• Voice calls are billed at per-minute rates according to your plan."}),(0,i.jsx)("p",{children:"• SMS messages are charged per segment according to your plan."}),(0,i.jsx)("p",{children:"• You can cancel this number at any time with no further charges."})]})]})]})]})]})})]})]})}},85057:(e,s,r)=>{"use strict";r.d(s,{J:()=>n});var i=r(95155);r(12115);var a=r(40968),t=r(59434);function n(e){let{className:s,...r}=e;return(0,i.jsx)(a.b,{"data-slot":"label",className:(0,t.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",s),...r})}},85127:(e,s,r)=>{"use strict";r.d(s,{A0:()=>n,BF:()=>l,Hj:()=>o,XI:()=>t,nA:()=>d,nd:()=>c});var i=r(95155);r(12115);var a=r(59434);function t(e){let{className:s,...r}=e;return(0,i.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,i.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",s),...r})})}function n(e){let{className:s,...r}=e;return(0,i.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",s),...r})}function l(e){let{className:s,...r}=e;return(0,i.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",s),...r})}function o(e){let{className:s,...r}=e;return(0,i.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",s),...r})}function c(e){let{className:s,...r}=e;return(0,i.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-muted-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...r})}function d(e){let{className:s,...r}=e;return(0,i.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...r})}},87193:(e,s,r)=>{Promise.resolve().then(r.bind(r,74891))}},e=>{var s=s=>e(e.s=s);e.O(0,[4201,6766,6299,8441,1684,7358],()=>s(87193)),_N_E=e.O()}]);