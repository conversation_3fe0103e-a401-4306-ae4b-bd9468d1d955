(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2483],{2488:(e,t,a)=>{"use strict";a.d(t,{default:()=>r});var s=a(95155),l=a(226);function r(e){let{children:t,duration:a=.5,delay:r=0,direction:n="up",distance:c=30,className:o="",once:i=!0,viewOffset:d=.1}=e,m=0,x=0;return"up"===n&&(m=c),"down"===n&&(m=-c),"left"===n&&(x=c),"right"===n&&(x=-c),(0,s.jsx)(l.P.div,{initial:{y:m,x:x,opacity:0},whileInView:{y:0,x:0,opacity:1},transition:{duration:a,delay:r,ease:"easeOut"},viewport:{once:i,amount:d},className:o,children:t})}},14853:(e,t,a)=>{"use strict";a.d(t,{O:()=>m});var s=a(95155),l=a(1243),r=a(54416),n=a(55365),c=a(30285),o=a(35695),i=a(4672),d=a(12115);function m(e){let{credits:t,threshold:a}=e,m=(0,o.useRouter)(),{totalAvailable:x,organizationCreditThreshold:h,isLoading:u,totalMinutesAvailable:g,callPricePerMinute:f,monthlyAllowance:p,isConnected:b,hasValidData:j,lastSuccessfulFetch:N}=(0,i.I)(),[v,w]=(0,d.useState)(!1),y=void 0!==t?t:x,k=void 0!==a?a:h,C=2*k;if((0,d.useEffect)(()=>{let e="credit_warning_dismissed_".concat(C),t=localStorage.getItem(e),a=!1;if(t)try{let{timestamp:s}=JSON.parse(t);(a=Date.now()-s<72e5)||localStorage.removeItem(e)}catch(t){localStorage.removeItem(e)}w(a),console.log("CreditWarningAlert Debug:",{credits:y,threshold:k,warningThreshold:C,isDismissed:a,dismissedKey:e,dismissedData:t,shouldShow:!u&&!a&&y<C&&y>=k})},[C,y,k,u]),u)return console.log("CreditWarningAlert: Not showing - still loading"),null;if(!j)return console.log("CreditWarningAlert: Not showing - no valid data"),null;let S=N&&Date.now()-N<12e4;if(!b&&!S)return console.log("CreditWarningAlert: Not showing - connection lost and data is stale"),null;if(v)return console.log("CreditWarningAlert: Not showing - dismissed"),null;if(y>=C||y<k)return console.log("CreditWarningAlert: Not showing - outside threshold range",{credits:y,threshold:k,warningThreshold:C,belowWarning:y<C,aboveCritical:y>=k}),null;console.log("CreditWarningAlert: Showing warning alert");let A=f>0?y/f:0;return(0,s.jsx)("div",{className:"flex justify-center mb-3",children:(0,s.jsx)(n.Fc,{className:"border-yellow-200 bg-yellow-50 dark:bg-yellow-900/10 dark:border-yellow-800 w-auto p-2",children:(0,s.jsx)(n.TN,{className:"text-yellow-800 dark:text-yellow-200",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(l.A,{className:"h-4 w-4 text-yellow-600 dark:text-yellow-400 flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium",children:p>0?"".concat(Math.floor(g)," minutes remaining"):"$".concat(y.toFixed(2)," balance (").concat(Math.floor(A)," min)")}),(0,s.jsx)("span",{className:"text-xs text-yellow-600 dark:text-yellow-400 ml-2",children:"Credits running low - consider adding funds soon"})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2 flex-shrink-0",children:[(0,s.jsx)(c.$,{size:"sm",variant:"outline",className:"h-7 px-3 text-xs border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/20",onClick:()=>m.push("/billing"),children:"Add Funds"}),(0,s.jsx)(c.$,{size:"sm",variant:"ghost",className:"h-7 w-7 p-0 text-yellow-600 hover:bg-yellow-100 dark:text-yellow-400 dark:hover:bg-yellow-900/20",onClick:()=>{let e={timestamp:Date.now(),warningThreshold:C};localStorage.setItem("credit_warning_dismissed_".concat(C),JSON.stringify(e)),w(!0)},children:(0,s.jsx)(r.A,{className:"h-3 w-3"})})]})]})})})})}},25561:(e,t,a)=>{"use strict";a.d(t,{G6:()=>d,MO:()=>o,SQ:()=>c,TX:()=>i,oe:()=>l,oz:()=>m,to:()=>r,vY:()=>n});let s="http://localhost:4000";async function l(){try{let e=localStorage.getItem("access_token");if(!e)return console.error("No access token available"),[];let t=await fetch("".concat(s,"/api/contacts"),{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error("Failed to fetch contacts");return await t.json()}catch(e){throw console.error("Error fetching contacts:",e),e}}async function r(){try{let e=localStorage.getItem("access_token");if(!e)return console.error("No access token available"),[];let t=await fetch("".concat(s,"/api/campaigns"),{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error("Failed to fetch contacts");return await t.json()}catch(e){throw console.error("Error fetching contacts:",e),e}}async function n(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");let a=await fetch("".concat(s,"/api/contacts"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify(e)});if(!a.ok)throw Error("Failed to create contact");return await a.json()}catch(e){throw console.error("Error creating contact:",e),e}}async function c(e){let t=localStorage.getItem("access_token");if(!t)throw Error("No access token available");let a=await fetch("".concat("http://localhost:4000","/api/contacts"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(t)},body:JSON.stringify(e)});if(!a.ok)throw Error((await a.json()).error||"Failed to create contact: ".concat(a.statusText));return a.json()}async function o(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");if(!(await fetch("".concat(s,"/api/contacts/").concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(t)}})).ok)throw Error("Failed to delete contact")}catch(e){throw console.error("Error deleting contact:",e),e}}async function i(){try{let e=localStorage.getItem("access_token");if(!e)throw console.error("No access token available"),Error("No access token available");let t=await fetch("".concat(s,"/api/contacts/import-contacts"),{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error("Failed to import contacts");await t.json()}catch(e){throw console.error("Error importing contacts:",e),e}}async function d(e,t,a){try{let l=localStorage.getItem("access_token");if(!l)throw console.error("No access token available"),Error("No access token available");let r=await fetch("".concat(s,"/api/vapi/call-contacts"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(l)},body:JSON.stringify({agentId:e,contacts:t,region:a})});if(402===r.status){let e=await r.json();throw Error(e.error||"Insufficient credits to make this call. Please add funds to your account.")}if(!r.ok)throw Error("Failed to start call");return await r.json()}catch(e){throw console.error("Error calling contact(s):",e),e}}async function m(e,t,a,l){try{let r=localStorage.getItem("access_token");if(!r)throw console.error("No access token available"),Error("No access token available");let n=await fetch("".concat(s,"/api/scheduled-call"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r)},body:JSON.stringify({agentId:e,contacts:t,scheduledTime:a,region:l})});if(402===n.status){let e=await n.json();throw Error(e.error||"Insufficient credits to schedule this call. Please add funds to your account.")}if(!n.ok)throw Error("Failed to schedule call");return await n.json()}catch(e){throw console.error("Error scheduling call:",e),e}}},38009:(e,t,a)=>{"use strict";a.d(t,{m:()=>i});var s=a(95155),l=a(85339),r=a(55365),n=a(30285),c=a(35695),o=a(4672);function i(e){let{credits:t,threshold:a}=e,i=(0,c.useRouter)(),{totalAvailable:d,organizationCreditThreshold:m,isLoading:x,totalMinutesAvailable:h,callPricePerMinute:u,monthlyAllowance:g,isConnected:f,hasValidData:p,lastSuccessfulFetch:b}=(0,o.I)(),j=void 0!==t?t:d,N=void 0!==a?a:m;if(x||!p)return null;let v=b&&Date.now()-b<12e4;if(!f&&!v||j>=N)return null;let w=u>0?j/u:0;return(0,s.jsx)("div",{className:"flex justify-center mb-5",children:(0,s.jsx)(r.Fc,{className:"border-red-200 bg-red-50 dark:bg-red-900/10 dark:border-red-800 p-1.5 w-auto",children:(0,s.jsx)(r.TN,{className:"text-red-700 dark:text-red-200",children:(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)(l.A,{className:"h-4 w-4 text-orange-600 dark:text-red-400 flex-shrink-0"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("span",{className:"text-sm font-medium",children:g>0?"".concat(Math.floor(h)," minutes remaining"):"$".concat(j.toFixed(2)," balance (").concat(Math.floor(w)," min) it should be atleast $").concat(N.toFixed(0))}),(0,s.jsx)("span",{className:"text-xs text-red-600 dark:text-red-400 ml-2",children:"Add funds to continue making calls"})]}),(0,s.jsx)(n.$,{size:"sm",variant:"outline",className:"h-7 px-3 text-xs border-red-300 text-red-700 hover:bg-red-100 dark:border-red-600 dark:text-red-300 dark:hover:bg-red-900/20 flex-shrink-0",onClick:()=>i.push("/billing"),children:"Add Funds"})]})})})})}},54165:(e,t,a)=>{"use strict";a.d(t,{Cf:()=>x,Es:()=>u,HM:()=>d,L3:()=>g,c7:()=>h,lG:()=>c,rr:()=>f,zM:()=>o});var s=a(95155);a(12115);var l=a(15452),r=a(54416),n=a(59434);function c(e){let{...t}=e;return(0,s.jsx)(l.bL,{"data-slot":"dialog",...t})}function o(e){let{...t}=e;return(0,s.jsx)(l.l9,{"data-slot":"dialog-trigger",...t})}function i(e){let{...t}=e;return(0,s.jsx)(l.ZL,{"data-slot":"dialog-portal",...t})}function d(e){let{...t}=e;return(0,s.jsx)(l.bm,{"data-slot":"dialog-close",...t})}function m(e){let{className:t,...a}=e;return(0,s.jsx)(l.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-650 bg-black/50",t),...a})}function x(e){let{className:t,children:a,...c}=e;return(0,s.jsxs)(i,{"data-slot":"dialog-portal",children:[(0,s.jsx)(m,{}),(0,s.jsxs)(l.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-650 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...c,children:[a,(0,s.jsxs)(l.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,s.jsx)(r.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function h(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function u(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function g(e){let{className:t,...a}=e;return(0,s.jsx)(l.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",t),...a})}function f(e){let{className:t,...a}=e;return(0,s.jsx)(l.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",t),...a})}},55365:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>c,TN:()=>i,XL:()=>o});var s=a(95155);a(12115);var l=a(74466),r=a(59434);let n=(0,l.F)("relative w-full rounded-lg border px-4 py-3 text-sm grid has-[>svg]:grid-cols-[calc(var(--spacing)*4)_1fr] grid-cols-[0_1fr] has-[>svg]:gap-x-3 gap-y-0.5 items-start [&>svg]:size-4 [&>svg]:translate-y-0.5 [&>svg]:text-current",{variants:{variant:{default:"bg-background text-foreground",destructive:"text-destructive-foreground [&>svg]:text-current *:data-[slot=alert-description]:text-destructive-foreground/80"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:a,...l}=e;return(0,s.jsx)("div",{"data-slot":"alert",role:"alert",className:(0,r.cn)(n({variant:a}),t),...l})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-title",className:(0,r.cn)("col-start-2 line-clamp-1 min-h-4 font-medium tracking-tight",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-description",className:(0,r.cn)("text-muted-foreground col-start-2 grid justify-items-start gap-1 text-sm [&_p]:leading-relaxed",t),...a})}},59409:(e,t,a)=>{"use strict";a.d(t,{bq:()=>m,eb:()=>h,gC:()=>x,l6:()=>i,yv:()=>d});var s=a(95155);a(12115);var l=a(31992),r=a(66474),n=a(5196),c=a(47863),o=a(59434);function i(e){let{...t}=e;return(0,s.jsx)(l.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,s.jsx)(l.WT,{"data-slot":"select-value",...t})}function m(e){let{className:t,children:a,...n}=e;return(0,s.jsxs)(l.l9,{"data-slot":"select-trigger",className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n,children:[a,(0,s.jsx)(l.In,{asChild:!0,children:(0,s.jsx)(r.A,{className:"size-4 opacity-50"})})]})}function x(e){let{className:t,children:a,position:r="popper",...n}=e;return(0,s.jsx)(l.ZL,{children:(0,s.jsxs)(l.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,...n,children:[(0,s.jsx)(u,{}),(0,s.jsx)(l.LM,{className:(0,o.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,s.jsx)(g,{})]})})}function h(e){let{className:t,children:a,...r}=e;return(0,s.jsxs)(l.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...r,children:[(0,s.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,s.jsx)(l.VF,{children:(0,s.jsx)(n.A,{className:"size-4"})})}),(0,s.jsx)(l.p4,{children:a})]})}function u(e){let{className:t,...a}=e;return(0,s.jsx)(l.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(c.A,{className:"size-4"})})}function g(e){let{className:t,...a}=e;return(0,s.jsx)(l.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,s.jsx)(r.A,{className:"size-4"})})}},62523:(e,t,a)=>{"use strict";a.d(t,{p:()=>r});var s=a(95155);a(12115);var l=a(59434);function r(e){let{className:t,type:a,...r}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,l.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...r})}},67710:(e,t,a)=>{"use strict";a.d(t,{default:()=>J});var s=a(95155),l=a(30285),r=a(62523),n=a(85127),c=a(19420),o=a(29869),i=a(57434),d=a(64261),m=a(54416),x=a(23837),h=a(51154),u=a(47924),g=a(12324),f=a(17580),p=a(62525),b=a(44020),j=a(29676),N=a(13717),v=a(84616),w=a(40646),y=a(69074),k=a(71007),C=a(92138),S=a(40133),A=a(48021),E=a(92657),_=a(78749),z=a(12115),I=a(54165),F=a(90010),D=a(91394),T=a(6874),O=a.n(T),$=a(25561),B=a(44838),L=a(2488),P=a(4672),M=a(38009),R=a(14853),W=a(56671),U=a(59409),Z=a(75143),G=a(50402),H=a(78266);function J(){var e,t,a,A,E;let{credits:_,hasSufficientCredits:T,minutes:H,organizationCreditThreshold:J}=(0,P.I)(),[q,X]=(0,z.useState)("all"),[Y,K]=(0,z.useState)(null),[Q,ee]=(0,z.useState)("asc"),[et,ea]=(0,z.useState)(0),[es,el]=(0,z.useState)([]),er=(0,z.useRef)(null),[en,ec]=(0,z.useState)(1),[eo,ei]=(0,z.useState)(!0),[ed,em]=(0,z.useState)(!1),ex=(0,z.useRef)(null),[eh,eu]=(0,z.useState)(""),[eg,ef]=(0,z.useState)(!1),[ep,eb]=(0,z.useState)({customerId:!0,name:!0,phoneNumber:!0,lastCall:!0,campaign:!0,createdAt:!0,timeZone:!0,source:!0,addedBy:!0,call:!0,actions:!0}),ej=[{id:"customerId",label:"Contact ID"},{id:"name",label:"Name"},{id:"phoneNumber",label:"Phone Number"},{id:"lastCall",label:"Last Call"},{id:"campaign",label:"Campaign"},{id:"createdAt",label:"Created At"},{id:"timeZone",label:"Time Zone"},{id:"source",label:"Source"},{id:"addedBy",label:"Added By"}],[eN,ev]=(0,z.useState)(ej),[ew,ey]=(0,z.useState)(!1),[ek,eC]=(0,z.useState)([]),[eS,eA]=(0,z.useState)(!1),[eE,e_]=(0,z.useState)(null),[ez,eI]=(0,z.useState)({}),[eF,eD]=(0,z.useState)(null),eT=(0,z.useRef)(null),[eO,e$]=(0,z.useState)(!1),[eB,eL]=(0,z.useState)(!1),[eP,eM]=(0,z.useState)(null),[eR,eW]=(0,z.useState)([]),[eU,eZ]=(0,z.useState)(!1),[eG,eH]=(0,z.useState)("now"),[eJ,eV]=(0,z.useState)(null),[eq,eX]=(0,z.useState)(!1),[eY,eK]=(0,z.useState)(!1),[eQ,e0]=(0,z.useState)(null),[e2,e1]=(0,z.useState)(""),[e4,e5]=(0,z.useState)(null),[e6,e3]=(0,z.useState)(!1),[e7,e9]=(0,z.useState)([]),[e8,te]=(0,z.useState)("name"),[tt,ta]=(0,z.useState)([]),[ts,tl]=(0,z.useState)(null),[tr,tn]=(0,z.useState)(!0),[tc,to]=(0,z.useState)(!1),[ti,td]=(0,z.useState)(""),tm=Intl.DateTimeFormat().resolvedOptions().timeZone,tx=async()=>{try{let e=localStorage.getItem("access_token");if(!e){console.error("No access token available");return}let t=await fetch("".concat("http://localhost:4000","/api/auth/me"),{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!t.ok)throw Error("Failed to fetch user profile: ".concat(t.status));let a=await t.json();tl(a.role)}catch(e){console.error("Failed to load user profile:",e)}},th=async()=>{try{let e=await fetch("".concat("http://localhost:4000","/api/agents"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!e.ok)throw Error("Failed to fetch agents");let t=await e.json();if("superadmin"===ts)el(t);else{let e=t.filter(e=>"active"===e.status);el(e)}}catch(e){console.error("Error fetching agents:",e)}},tu=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1?arguments[1]:void 0;try{1===e?tn(!0):em(!0);let a=t?"&search=".concat(encodeURIComponent(t)):"",s="all"!==q&&"unknown"!==q?"&campaignId=".concat(q):"unknown"===q?"&noCampaign=true":"",l=await fetch("".concat("http://localhost:4000","/api/contacts?page=").concat(e,"&limit=").concat(20).concat(a).concat("&filterType=".concat(e8)).concat(s),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!l.ok)throw Error("Failed to fetch contacts");let r=await l.json();1===e?ta(r):ta(e=>[...e,...r]),ei(20===r.length)}catch(e){console.error("Error loading contacts:",e)}finally{tn(!1),em(!1)}};(0,z.useEffect)(()=>{let e=new IntersectionObserver(e=>{e[0].isIntersecting&&eo&&!tr&&!ed&&ec(e=>e+1)},{threshold:.1}),t=ex.current;return t&&e.observe(t),()=>{t&&e.unobserve(t)}},[eo,tr,ed]),(0,z.useEffect)(()=>{en>1&&tu(en,eh)},[en,q]);let tg=async()=>{try{let e=await (0,$.to)();e9(e)}catch(e){console.error("Error loading campaigns:",e)}},tf=async()=>{try{let e=await fetch("".concat("http://localhost:4000","/api/scheduled-call?page=1&limit=1"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!e.ok)throw Error("Failed to fetch scheduled calls");let t=await e.json();if(t.length>0&&"totalSchedules"in t[0]){let e=Number(t[0].totalSchedules);ea(isNaN(e)?0:e)}else ea(0)}catch(e){console.error("Error fetching scheduled calls count:",e),ea(0)}};(0,z.useEffect)(()=>{(async()=>{tn(!0);try{await tx(),await th(),ec(1),await tu(1),await tg(),await tf()}catch(e){console.error("Error fetching data:",e)}finally{tn(!1)}})()},[]);let tp=async()=>{to(!0);try{await (0,$.TX)(),await tu()}catch(e){console.error("Error importing contacts:",e)}finally{to(!1)}},tb=e=>{eM(e),eW([]),eL(!0)},tj=e=>{eR.some(t=>t._id===e._id)?eW(eR.filter(t=>t._id!==e._id)):eW([...eR,e])},tN=async e=>{let t;if(eV(null),!e){eV("Please select an agent before scheduling");return}if(!e2){eV("Please select a date and time for the schedule");return}let a=tm;if(eP)t=[{Name:eP.contactName,MobileNumber:eP.phoneNumber.startsWith("+")?eP.phoneNumber:"+".concat(eP.phoneNumber)}],a=eP.region||tm;else{if(!(eR.length>0))return;t=eR.map(e=>({Name:e.contactName,MobileNumber:e.phoneNumber.startsWith("+")?e.phoneNumber:"+".concat(e.phoneNumber)})),1===eR.length&&(a=eR[0].region||tm)}eZ(!0);try{var s;await (0,$.oz)(e,t,e2,a),await tf(),e0({contacts:t,scheduledTime:e2,agentName:(null===(s=es.find(t=>t.id===e))||void 0===s?void 0:s.name)||"Unknown",region:a}),eK(!0),W.o.success("Call scheduled successfully")}catch(t){console.error("Error scheduling call:",t);let e=t instanceof Error?t.message:"Failed to schedule call";e.includes("Insufficient credits")||e.includes("add funds")?W.o.error(e):eV("Failed to schedule call. Please try again.")}finally{eZ(!1),eL(!1),eM(null),eW([]),e1("")}},tv=async e=>{let t;if(eV(null),!e){eV("Please select an agent before starting the call");return}if(eP)t=[{Name:eP.contactName,MobileNumber:eP.phoneNumber.startsWith("+")?eP.phoneNumber:"+".concat(eP.phoneNumber)}];else{if(!(eR.length>0))return;t=eR.map(e=>({Name:e.contactName,MobileNumber:e.phoneNumber.startsWith("+")?e.phoneNumber:"+".concat(e.phoneNumber)}))}eZ(!0);try{let a=await (0,$.G6)(e,t,tm);console.log("Call initiated:",a),W.o.success("Call initiated successfully")}catch(t){console.error("Error calling contact(s):",t);let e=t instanceof Error?t.message:"Failed to initiate call";e.includes("Insufficient credits")||e.includes("add funds")?W.o.error(e):eV("Failed to initiate call. Please try again.")}finally{eZ(!1),eL(!1),eM(null),eW([])}},tw=e=>{e5(e),e$(!0)},ty=async()=>{if(e4)try{await (0,$.MO)(e4),await tu(),e$(!1),e5(null)}catch(e){console.error("Error deleting contact:",e)}},tk=async()=>{try{tn(!0),await Promise.all(eR.map(e=>(0,$.MO)(e._id))),await tu(),eW([]),e3(!1)}catch(e){console.error("Error deleting contacts:",e)}finally{tn(!1)}},tC=e=>{Y===e?ee("asc"===Q?"desc":"asc"):(K(e),ee("asc"))},tS=e=>{eb(t=>({...t,[e]:!t[e]}))},tA=(0,z.useMemo)(()=>Y?[...tt].sort((e,t)=>{var a,s,l,r;let n,c;if("name"===Y)n=e.contactName.toLowerCase(),c=t.contactName.toLowerCase();else if("phoneNumber"===Y)n=e.phoneNumber,c=t.phoneNumber;else if("lastCall"===Y)n=e.lastCall?new Date(e.lastCall).getTime():0,c=t.lastCall?new Date(t.lastCall).getTime():0;else if("timeZone"===Y)n=e.region?e.region.toLowerCase():"",c=t.region?t.region.toLowerCase():"";else if("campaign"===Y)n=e.campaigns&&e.campaigns.length>0?"object"==typeof e.campaigns[0]?e.campaigns[0].name||"":e.campaigns[0]||"":"",c=t.campaigns&&t.campaigns.length>0?"object"==typeof t.campaigns[0]?t.campaigns[0].name||"":t.campaigns[0]||"":"";else if("source"===Y)n=(null===(a=e.source)||void 0===a?void 0:a.toLowerCase())||"",c=(null===(s=t.source)||void 0===s?void 0:s.toLowerCase())||"";else if("addedBy"===Y)n=(null===(l=e.addedBy)||void 0===l?void 0:l.toLowerCase())||"",c=(null===(r=t.addedBy)||void 0===r?void 0:r.toLowerCase())||"";else{if("createdAt"!==Y)return 0;n=new Date(e.createdAt).getTime(),c=new Date(t.createdAt).getTime()}return"asc"===Q?n>c?1:n<c?-1:0:n<c?1:n>c?-1:0}):tt,[tt,Y,Q]),tE=(0,Z.FR)((0,Z.MS)(Z.AN),(0,Z.MS)(Z.uN,{coordinateGetter:G.JR})),t_=async()=>{to(!0),eI({}),eD(null);try{let e=[];for(let t of ek){let a=new FormData;a.append("file",t);let s=await fetch("".concat("http://localhost:4000","/api/contacts/upload"),{method:"POST",headers:{Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:a});if(!s.ok)throw Error("Failed to import contacts from ".concat(t.name));let l=await s.json();e.push({fileName:t.name,...l})}let t={totalFiles:e.length,totalProcessed:e.reduce((e,t)=>e+t.totalProcessed,0),successCount:e.reduce((e,t)=>e+t.successCount,0),errorCount:e.reduce((e,t)=>e+t.errorCount,0),fileResults:e};e_(t),eA(!0),eC([]),ey(!1),await tu()}catch(e){console.error("Import error:",e),eD("Failed to import contacts. your file(s) doesn't have the correct columns structure.")}finally{to(!1)}};return(0,s.jsx)(s.Fragment,{children:(0,s.jsxs)(L.default,{children:[(0,s.jsx)(R.O,{credits:_,threshold:J}),(0,s.jsx)(M.m,{credits:_,threshold:J}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 w-full sm:w-auto",children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold",children:"Contacts"}),et>0&&(0,s.jsx)(O(),{href:"/schedule",className:"flex items-center gap-2 bg-blue-50 text-blue-600 px-3 py-1.5 rounded-full text-sm font-medium hover:bg-blue-100 transition-colors mt-1 dark:bg-gray-250 ",children:(0,s.jsx)("div",{className:"flex items-center",children:tt.length>0&&(0,s.jsxs)("span",{className:"flex items-center text-sm font-medium mr-2 ",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Showing ",tt.length," of ",(null===(e=tt[0])||void 0===e?void 0:e.filteredContacts)?"".concat(tt[0].filteredContacts," ").concat("all"===q?"contacts":"unknown"===q?"contacts (No Campaign)":"contacts in ".concat((null===(t=e7.find(e=>e._id===q))||void 0===t?void 0:t.name)||"campaign")):(null===(a=tt[0])||void 0===a?void 0:a.totalContacts)?"".concat(tt[0].totalContacts," contacts"):"".concat(tt.length," contacts")]})})})]}),(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 w-full sm:w-auto",children:[(0,s.jsx)(l.$,{className:"bg-[#383D73] text-white flex items-center gap-2 text-sm sm:text-base flex-1 sm:flex-auto",onClick:tp,disabled:tc,children:tc?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"}),(0,s.jsx)("span",{className:"whitespace-nowrap",children:"Importing..."})]}):(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("span",{className:"whitespace-nowrap",children:"Import contacts"})})}),(0,s.jsxs)(l.$,{variant:"outline",onClick:()=>ey(!ew),className:"flex items-center gap-2 text-sm sm:text-base flex-1 sm:flex-auto dark:border-gray-300 ",children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),"Upload Contacts"]}),(0,s.jsx)(O(),{href:"/contacts/create",className:"flex-1 sm:flex-auto",children:(0,s.jsx)(l.$,{className:"bg-primary hover:bg-primary/90 w-full text-sm sm:text-base",children:"Add Contact"})})]})]}),ew&&(0,s.jsx)("div",{className:"mt-4 mb-4 p-4  border rounded-lg bg-card dark:bg-gray-800/50 animate-in fade-in transition-all duration-600 dark:border-gray-500",children:(0,s.jsxs)("div",{className:"flex gap-6",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsxs)("label",{htmlFor:"file-upload",className:"border-2 border-dashed rounded-lg hover:border-primary/50 transition-colors block cursor-pointer h-[250px] relative",children:[0===ek.length?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full gap-3 p-8",children:[(0,s.jsx)(o.A,{className:"h-8 w-8 text-muted-foreground"}),(0,s.jsxs)("div",{className:"flex flex-col items-center gap-1",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-primary",children:"Click to upload"}),(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:"or drag and drop"})]})]}):(0,s.jsxs)("div",{className:"flex flex-col h-full",children:[(0,s.jsx)("div",{className:"p-4 border-b dark:border-gray-700",children:(0,s.jsxs)("span",{className:"text-sm font-medium text-muted-foreground",children:["Selected Files (",ek.length,")"]})}),(0,s.jsx)("div",{className:"flex-1 overflow-auto p-2 space-y-2",children:ek.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800/50 rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[e.name.endsWith(".csv")?(0,s.jsx)(i.A,{className:"h-7 w-7 text-green-600 dark:text-green-500"}):(0,s.jsx)(d.A,{className:"h-7 w-7 text-emerald-600 dark:text-emerald-500"}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"text-sm truncate max-w-[200px]",children:e.name}),(0,s.jsxs)("span",{className:"text-xs text-muted-foreground",children:["(",(e.size/1024).toFixed(1)," KB)"]})]})]}),(0,s.jsx)(l.$,{variant:"ghost",size:"icon",onClick:e=>{e.preventDefault(),eC(e=>e.filter((e,a)=>a!==t)),eT.current&&(eT.current.value="")},className:"h-8 w-8 text-red-500 hover:text-red-600",children:(0,s.jsx)(m.A,{className:"h-4 w-4"})})]},"".concat(e.name,"-").concat(t)))}),(0,s.jsx)("div",{className:"p-3 border-t dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50 flex items-center justify-center",children:(0,s.jsx)("span",{className:"text-sm text-muted-foreground",children:"Drop more files here or click to browse"})})]}),(0,s.jsx)("input",{id:"file-upload",type:"file",className:"hidden",accept:".csv,.xlsx,.xls",multiple:!0,ref:eT,onChange:e=>{var t;if(null===(t=e.target.files)||void 0===t?void 0:t.length){let t=ek.reduce((e,t)=>e+t.size,0),a=Array.from(e.target.files);if(t+a.reduce((e,t)=>e+t.size,0)>0xa00000){alert("File(s) size exceeds 10MB limit. Please select smaller files."),eT.current&&(eT.current.value="");return}eC(e=>[...e,...a])}}})]})}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"File Requirements"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground mb-4",children:"Upload a CSV or Excel file with contact details. At minimum include Name and Phone number or mobile. You can check an example by clicking on one of the templates."}),(0,s.jsxs)("div",{className:"flex space-x-2 mt-8",children:[(0,s.jsxs)(l.$,{variant:"outline",className:" justify-start dark:border-gray-500",onClick:()=>{let e=document.createElement("a");e.href="/templates/Orova_Template-csv.csv",e.download="orova-template.csv",document.body.appendChild(e),e.click(),document.body.removeChild(e)},children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Download CSV Template"]}),(0,s.jsxs)(l.$,{variant:"outline",className:" justify-start dark:border-gray-500 ",onClick:()=>{let e=document.createElement("a");e.href="/templates/Orova_Template-excel.xlsx",e.download="orova-template.xlsx",document.body.appendChild(e),e.click(),document.body.removeChild(e)},children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Download Excel Template"]})]}),ek.length>0&&(0,s.jsxs)("div",{className:"mt-4",children:[Object.entries(ez).map(e=>{let[t,a]=e;return(0,s.jsxs)("div",{className:"text-red-500 text-sm mb-2",children:["Error in ",t,": ",a]},t)}),eF&&(0,s.jsx)("p",{className:"text-red-500 text-sm mb-2",children:eF}),(0,s.jsx)(l.$,{className:"w-1/3 mt-5 bg-primary",onClick:t_,disabled:tc||Object.keys(ez).length>0,children:tc?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2 animate-spin"}),"Importing..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Import ",ek.length," ",1===ek.length?"File":"Files"]})})]})]})]})}),(0,s.jsx)("div",{className:"flex flex-col md:flex-row gap-6",children:(0,s.jsxs)("div",{className:"flex-1 max-w-full",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 mb-6",children:[(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),ec(1),tu(1,eh)},className:"relative flex-1",children:[(0,s.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,s.jsx)(r.p,{placeholder:"name"===e8?"Search by name or phone...":"Search by campaign...",className:"pl-10 pr-20",value:eh,onChange:e=>eu(e.target.value)})]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)(U.l6,{value:q,onValueChange:e=>{X(e),ec(1);let t=eh?"&search=".concat(encodeURIComponent(eh)):"",a="all"!==e&&"unknown"!==e?"&campaignId=".concat(e):"unknown"===e?"&noCampaign=true":"";tn(!0),fetch("".concat("http://localhost:4000","/api/contacts?page=1&limit=").concat(20).concat(t).concat("&filterType=".concat(e8)).concat(a),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}}).then(e=>{if(!e.ok)throw Error("Failed to fetch contacts");return e.json()}).then(e=>{ta(e),ei(20===e.length)}).catch(e=>{console.error("Error loading contacts:",e)}).finally(()=>{tn(!1)})},children:[(0,s.jsx)(U.bq,{className:"w-full ",children:(0,s.jsx)(U.yv,{placeholder:"Select Campaign"})}),(0,s.jsxs)(U.gC,{children:[(0,s.jsx)(U.eb,{value:"all",children:"All Campaigns"}),e7.map(e=>(0,s.jsx)(U.eb,{value:e._id,children:e.name},e._id)),(0,s.jsx)(U.eb,{value:"unknown",children:"Unknown (No Campaign)"})]})]}),(0,s.jsxs)(l.$,{variant:"outline",onClick:()=>ef(!0),className:"flex items-center gap-2",children:[(0,s.jsx)(g.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"hidden sm:inline",children:"Columns"})]})]})]}),eR.length>0&&(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4 p-2 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800",children:[(0,s.jsxs)("span",{className:"flex items-center text-sm font-medium text-blue-600 dark:text-blue-400 gap-x-3 ml-2",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 text-blue-600 dark:text-blue-400"}),eR.length," contact",1!==eR.length?"s":""," selected"]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsxs)(l.$,{className:"bg-green-600 text-white flex items-center gap-2 text-sm sm:text-base flex-1 sm:flex-auto",onClick:()=>{eR.length>0&&(eM(null),eL(!0))},disabled:0===eR.length,children:[(0,s.jsx)(c.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"whitespace-nowrap",children:"Call Selected"})]}),(0,s.jsxs)(l.$,{variant:"outline",className:"text-red-600 dark:text-red-400 border-red-200 dark:border-red-800 hover:bg-red-50 dark:hover:bg-red-900/20 hover:text-red-700 dark:hover:text-red-300 transition-colors",onClick:()=>e3(!0),children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Delete Selection"]})]})]}),(0,s.jsx)("div",{className:" bg-card rounded-lg border shadow-sm overflow-hidden dark:bg-gray-800 dark:border-gray-700",children:(0,s.jsx)("div",{className:"w-full overflow-x-auto ",children:(0,s.jsxs)(n.XI,{children:[(0,s.jsx)(n.A0,{className:"sticky top-0 bg-card z-10 dark:bg-gray-800 border-b-2 border-gray-300  dark:border-gray-700",children:(0,s.jsxs)(n.Hj,{className:"bg-gray-50 dark:bg-gray-800/90",children:[(0,s.jsx)(n.nd,{className:"w-12 py-3 font-semibold text-gray-700 dark:text-gray-200 ",children:(0,s.jsx)("input",{type:"checkbox",className:"rounded ml-2",checked:tt.length>0&&eR.length===tt.length,onChange:()=>{eR.length===tt.length?eW([]):eW(tt)}})}),eN.map(e=>ep[e.id]&&(0,s.jsx)(n.nd,{className:"font-bold cursor-pointer text-gray-700 dark:text-gray-200",onClick:()=>tC(e.id),children:(0,s.jsxs)("div",{className:"flex items-center",children:[e.label,Y===e.id&&(0,s.jsx)("span",{className:"ml-1",children:"asc"===Q?"↑":"↓"})]})},e.id)),(0,s.jsx)(n.nd,{className:"font-bold text-gray-700 dark:text-gray-200 text-center",children:"Call"}),(0,s.jsx)(n.nd,{className:"font-bold text-gray-700 dark:text-gray-200 text-center",children:"Actions"})]})}),(0,s.jsxs)(n.BF,{children:[tr&&1===en?(0,s.jsx)(n.Hj,{children:(0,s.jsx)(n.nA,{colSpan:Object.values(ep).filter(Boolean).length+2,className:"h-80 text-center p-0",children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full",children:[(0,s.jsx)(h.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,s.jsx)("p",{className:"text-lg font-medium",children:"Loading..."})]})})}):tt.length>0?tA.map(e=>(0,s.jsxs)(n.Hj,{className:"animate-in fade-in slide-in-from-bottom-10 duration-500",children:[(0,s.jsx)(n.nA,{children:(0,s.jsx)("input",{type:"checkbox",className:"rounded ml-2",checked:eR.some(t=>t._id===e._id),onChange:()=>tj(e)})}),eN.map(t=>{var a,l;return ep[t.id]&&(0,s.jsxs)(n.nA,{children:["customerId"===t.id&&(0,s.jsx)("span",{className:"font-mono text-sm text-black dark:text-white",children:e.customerId||"-"}),"name"===t.id&&(0,s.jsx)("span",{className:"font-medium",children:e.contactName}),"phoneNumber"===t.id&&e.phoneNumber,"lastCall"===t.id&&(null!=e.lastCall&&new Date(e.lastCall).toLocaleString("en-GB",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1})||"-"),"campaign"===t.id&&(0,s.jsxs)("div",{className:"flex flex-wrap gap-2",children:[e.campaigns&&e.campaigns.length>0&&e.campaigns.map((e,t)=>(0,s.jsx)("span",{className:"bg-blue-100 text-blue-600 text-xs font-medium px-2 py-1 rounded-full",children:"object"==typeof e?e.name:e},t)),e.campaignNames&&e.campaignNames.length>0&&e.campaignNames.map((e,t)=>(0,s.jsx)("span",{className:"bg-blue-100 text-blue-600 text-xs font-medium px-2 py-1 rounded-full",children:e},"name-".concat(t))),!(null===(a=e.campaigns)||void 0===a?void 0:a.length)&&!(null===(l=e.campaignNames)||void 0===l?void 0:l.length)&&"-"]}),"createdAt"===t.id&&(0,s.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.createdAt?new Date(e.createdAt).toLocaleString("en-GB",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",hour12:!1}):"-"}),"timeZone"===t.id&&(e.region||"-"),"source"===t.id&&(0,s.jsx)("span",{className:"\n                                  inline-flex px-2 py-1 rounded-sm text-xs font-medium\n                                  ".concat("manual"===e.source?"bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400":"file Upload"===e.source?"bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400":"CRM"===e.source?"bg-purple-100 text-purple-700 dark:bg-purple-900/20 dark:text-purple-400":" text-gray-700 dark:bg-gray-800 dark:text-gray-400","\n                                "),children:e.source||"-"}),"addedBy"===t.id&&(0,s.jsx)("span",{className:"text-sm text-black font-semibold italic dark:text-gray-100",children:e.addedBy||"-"})]},t.id)}),(0,s.jsx)(n.nA,{children:(0,s.jsx)("div",{className:"flex flex-wrap justify-center gap-2",children:(0,s.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>tb(e),className:"text-green-600 border-green-200 hover:bg-green-50 hover:text-green-700",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-1"}),"Call"]})})}),(0,s.jsx)(n.nA,{children:(0,s.jsx)("div",{className:"flex justify-center",children:(0,s.jsxs)(B.rI,{children:[(0,s.jsx)(B.ty,{asChild:!0,children:(0,s.jsx)(l.$,{variant:"outline",size:"icon",className:"text-gray-600 hover:bg-gray-100",children:(0,s.jsx)(b.A,{className:"h-4 w-4 dark:text-gray-200"})})}),(0,s.jsxs)(B.SQ,{align:"end",className:"w-28 p-2 bg-white shadow-lg rounded-md",children:[(0,s.jsx)(B._2,{asChild:!0,children:(0,s.jsxs)(O(),{href:"/history/".concat(encodeURIComponent(e.contactName)),className:"flex items-center p-2 rounded-md hover:bg-blue-50 cursor-pointer",children:[(0,s.jsx)(j.A,{className:"h-4 w-4 mr-2 text-[#383D73]"}),(0,s.jsx)("span",{className:"text-[#383D73]",children:"History"})]})}),(0,s.jsx)(B._2,{asChild:!0,children:(0,s.jsxs)(O(),{href:"/contacts/edit/".concat(encodeURIComponent(e.contactName),"/").concat(encodeURIComponent(e._id)),className:"flex items-center p-2 w-full text-left rounded-md hover:bg-blue-50 cursor-pointer",children:[(0,s.jsx)(N.A,{className:"h-4 w-4 mr-2 text-blue-600"}),(0,s.jsx)("span",{className:"text-blue-600",children:"Edit"})]})}),(0,s.jsx)(B._2,{asChild:!0,children:(0,s.jsxs)("button",{onClick:()=>tw(e._id),className:"flex items-center p-2 w-full text-left rounded-md hover:bg-red-50 cursor-pointer",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-2 text-red-600"}),(0,s.jsx)("span",{className:"text-red-600",children:"Delete"})]})})]})]})})})]},e._id)):(0,s.jsx)(n.Hj,{children:(0,s.jsx)(n.nA,{colSpan:Object.values(ep).filter(Boolean).length+2,className:"h-80 text-center p-0",children:(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center h-full",children:[(0,s.jsx)("p",{className:"text-lg font-medium text-gray-600 dark:text-gray-300 mb-2",children:eh?"No contacts found":"No contacts yet"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground max-w-md text-center",children:eh?'No contacts match "'.concat(eh,'"'):"Contacts are created, or you can create them manually"}),!eh&&(0,s.jsx)(O(),{href:"/contacts/create",children:(0,s.jsxs)(l.$,{variant:"outline",className:"mt-4",children:[(0,s.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Create your first contact"]})})]})})}),eo&&(0,s.jsx)(n.Hj,{ref:ex,className:"h-20",children:(0,s.jsx)(n.nA,{colSpan:7,children:(0,s.jsx)("div",{className:"flex items-center justify-center py-4",children:ed?(0,s.jsx)(h.A,{className:"h-6 w-6 animate-spin text-primary"}):(0,s.jsx)("div",{className:"h-8"})})})})]})]})})})]})}),(0,s.jsx)(F.Lt,{open:eO,onOpenChange:e$,children:(0,s.jsxs)(F.EO,{children:[(0,s.jsxs)(F.wd,{children:[(0,s.jsx)(F.r7,{children:"Are you sure?"}),(0,s.jsx)(F.$v,{children:"This action cannot be undone. This will permanently delete the contact from your database."})]}),(0,s.jsxs)(F.ck,{children:[(0,s.jsx)(F.Zr,{children:"Cancel"}),(0,s.jsx)(F.Rx,{className:"bg-red-600 hover:bg-red-700 text-white",onClick:ty,children:"Delete"})]})]})}),(0,s.jsx)(F.Lt,{open:e6,onOpenChange:e3,children:(0,s.jsxs)(F.EO,{children:[(0,s.jsxs)(F.wd,{children:[(0,s.jsx)(F.r7,{children:"Delete Multiple Contacts"}),(0,s.jsxs)(F.$v,{children:["Are you sure you want to delete ",eR.length," contact",1!==eR.length?"s":"","? This action cannot be undone."]})]}),(0,s.jsxs)(F.ck,{children:[(0,s.jsx)(F.Zr,{children:"Cancel"}),(0,s.jsx)(F.Rx,{className:"bg-red-600 hover:bg-red-700 text-white",onClick:tk,children:tr?(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(h.A,{className:"h-4 w-4 animate-spin"}),"Deleting..."]}):(0,s.jsxs)(s.Fragment,{children:["Delete ",eR.length," Contact",1!==eR.length?"s":""]})})]})]})}),(0,s.jsx)(I.lG,{open:eY,onOpenChange:eK,children:(0,s.jsxs)(I.Cf,{className:"sm:max-w-[425px]",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center pt-6",children:[(0,s.jsx)("div",{className:"h-12 w-12 rounded-full bg-green-100 flex items-center justify-center mb-4",children:(0,s.jsx)(w.A,{className:"h-6 w-6 text-green-600"})}),(0,s.jsx)(I.L3,{className:"text-xl font-semibold text-center mb-2",children:"Call Scheduled Successfully!"}),(0,s.jsx)(I.rr,{className:"text-center mb-6",children:"Your call has been scheduled with the following details:"})]}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 space-y-3 mb-6",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(y.A,{className:"h-4 w-4 text-gray-500"}),(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"text-sm",children:new Date((null==eQ?void 0:eQ.scheduledTime)||"").toLocaleString()}),(0,s.jsxs)("span",{className:"text-xs text-gray-500",children:["Timezone: ",(null==eQ?void 0:eQ.region)||""]})]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(k.A,{className:"h-4 w-4 text-gray-500"}),(0,s.jsxs)("span",{className:"text-sm",children:["Agent: ",null==eQ?void 0:eQ.agentName]})]}),(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 text-gray-500"}),(0,s.jsx)("span",{className:"text-sm",children:(null==eQ?void 0:eQ.contacts.length)===1?eQ.contacts[0].Name:"".concat(null==eQ?void 0:eQ.contacts.length," contacts")})]})]}),(0,s.jsx)(I.Es,{className:"flex justify-center",children:(0,s.jsxs)(l.$,{className:"w-full flex items-center justify-center gap-2 bg-[#383D73] hover:bg-[#383D73]/90",onClick:()=>{eK(!1),window.location.href="/schedule"},children:["View in Schedule",(0,s.jsx)(C.A,{className:"h-4 w-4"})]})})]})}),(0,s.jsx)(I.lG,{open:eg,onOpenChange:ef,children:(0,s.jsxs)(I.Cf,{className:"sm:max-w-md",children:[(0,s.jsxs)(I.c7,{children:[(0,s.jsx)(I.L3,{children:"Table Columns"}),(0,s.jsx)(I.rr,{children:"Drag to reorder columns. Toggle visibility with the eye icon."})]}),(0,s.jsx)("div",{className:"py-4",children:(0,s.jsx)(Z.Mp,{sensors:tE,collisionDetection:Z.fp,onDragEnd:e=>{let{active:t,over:a}=e;t.id!==a.id&&ev(e=>{let s=e.findIndex(e=>e.id===t.id),l=e.findIndex(e=>e.id===a.id);return(0,G.be)(e,s,l)})},children:(0,s.jsx)(G.gB,{items:eN.map(e=>e.id),strategy:G._G,children:(0,s.jsx)("div",{className:"space-y-2 overflow-auto max-h-100",children:eN.map(e=>(0,s.jsx)(V,{id:e.id,label:e.label,visible:ep[e.id],onToggle:()=>tS(e.id)},e.id))})})})}),(0,s.jsxs)(I.Es,{className:"flex justify-between",children:[(0,s.jsxs)(l.$,{variant:"outline",onClick:()=>ev(ej),className:"flex items-center gap-2",children:[(0,s.jsx)(S.A,{className:"h-4 w-4"}),"Reset Order"]}),(0,s.jsx)(l.$,{onClick:()=>ef(!1),children:"Done"})]})]})}),(0,s.jsx)(I.lG,{open:eS,onOpenChange:eA,children:(0,s.jsxs)(I.Cf,{className:"sm:max-w-[800px]",children:[(0,s.jsxs)("div",{className:"flex flex-col items-center pt-6",children:[(0,s.jsx)("div",{className:"h-12 w-12 rounded-full bg-green-100 dark:bg-green-900/20 flex items-center justify-center mb-4",children:(0,s.jsx)(w.A,{className:"h-6 w-6 text-green-600 dark:text-green-500"})}),(0,s.jsx)(I.L3,{className:"text-xl font-semibold text-center mb-2",children:"Files Imported Successfully!"}),(0,s.jsxs)(I.rr,{className:"text-center mb-6",children:[null==eE?void 0:eE.totalFiles," ",(null==eE?void 0:eE.totalFiles)===1?"file":"files"," processed with the following results:"]})]}),(0,s.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 space-y-4 mb-6",children:[(0,s.jsxs)("div",{className:"border-b dark:border-gray-700 pb-3",children:[(0,s.jsx)("h3",{className:"font-medium mb-2",children:"Imported Files:"}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:null==eE?void 0:null===(A=eE.fileResults)||void 0===A?void 0:A.map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center gap-2 bg-white dark:bg-gray-800 px-3 py-1.5 rounded-full border dark:border-gray-700",children:[e.fileName.endsWith(".csv")?(0,s.jsx)(i.A,{className:"h-4 w-4 text-green-600"}):(0,s.jsx)(d.A,{className:"h-4 w-4 text-emerald-600"}),(0,s.jsx)("span",{className:"text-sm",children:e.fileName})]},t))})]}),(0,s.jsxs)("div",{className:"border-b dark:border-gray-700 pb-3",children:[(0,s.jsx)("h3",{className:"font-medium mb-2",children:"Results Summary:"}),(0,s.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-muted-foreground",children:"Total Contacts:"}),(0,s.jsx)("p",{className:"font-medium",children:null==eE?void 0:eE.totalProcessed})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-muted-foreground",children:"Successful:"}),(0,s.jsx)("p",{className:"font-medium text-green-600 dark:text-green-500",children:null==eE?void 0:eE.successCount})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-muted-foreground",children:"Errors:"}),(0,s.jsx)("p",{className:"font-medium text-red-600 dark:text-red-500",children:null==eE?void 0:eE.errorCount})]})]})]}),(0,s.jsxs)("div",{className:"overflow-auto max-h-[170px]",children:[(0,s.jsx)("h3",{className:"font-medium mb-2",children:"Imported Contacts:"}),(0,s.jsx)("div",{className:"border dark:border-gray-700 rounded-lg overflow-hidden",children:(0,s.jsxs)("table",{className:"w-full text-sm",children:[(0,s.jsx)("thead",{className:"bg-gray-100 dark:bg-gray-800/90",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-4 py-2 text-left font-medium",children:"Name"}),(0,s.jsx)("th",{className:"px-4 py-2 text-left font-medium",children:"Phone Number"})]})}),(0,s.jsx)("tbody",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:null==eE?void 0:null===(E=eE.fileResults)||void 0===E?void 0:E.flatMap(e=>{var t;return null===(t=e.successfulContacts)||void 0===t?void 0:t.map((t,a)=>(0,s.jsxs)("tr",{className:"bg-white dark:bg-gray-800/50",children:[(0,s.jsx)("td",{className:"px-4 py-2",children:t.contactName}),(0,s.jsx)("td",{className:"px-4 py-2",children:t.phoneNumber})]},"".concat(e.fileName,"-").concat(a)))})})]})}),((null==eE?void 0:eE.totalProcessed)||0)>50&&(0,s.jsxs)("p",{className:"text-sm text-muted-foreground mt-2 text-center",children:["Showing first 50 contacts of ",null==eE?void 0:eE.totalProcessed]})]})]}),(0,s.jsx)(I.Es,{children:(0,s.jsx)(l.$,{className:"w-full",onClick:()=>eA(!1),children:"Done"})})]})}),(0,s.jsx)(I.lG,{open:eB,onOpenChange:e=>{eL(e),e||eV(null)},children:(0,s.jsxs)(I.Cf,{className:"sm:max-w-[600px] p-0 overflow-hidden rounded-xl border-0 shadow-xl dark:bg-gray-900",children:[(0,s.jsx)("div",{className:"bg-gray-950 p-4",children:(0,s.jsxs)(I.c7,{className:"pb-2",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(I.L3,{className:"text-white text-xl font-medium",children:eP?"Start Call":"Group Call"}),(0,s.jsx)(I.HM,{asChild:!0,children:(0,s.jsx)("button",{className:"absolute top-4 right-4 text-white hover:bg-blue-900 rounded-full p-2",children:(0,s.jsx)(m.A,{className:"h-5 w-5"})})})]}),(0,s.jsx)(I.rr,{className:"text-white/90 text-lg",children:eP?"Connect with ".concat(eP.contactName):"Connect with ".concat(eR.length," contacts")})]})}),(0,s.jsxs)("div",{className:"p-5",children:[eP?(0,s.jsxs)("div",{className:"flex items-center gap-4 mb-6 bg-green-50 p-4 rounded-lg dark:bg-green-900/20",children:[(0,s.jsx)(D.eu,{className:"h-16 w-16 border-2 border-green-500",children:(0,s.jsx)(D.q5,{className:"bg-green-100 dark:bg-green-900 dark:text-green-400 text-green-600 text-lg",children:eP.contactName.charAt(0)})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-lg font-semibold dark:text-white",children:eP.contactName}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground ",children:eP.phoneNumber})]})]}):(0,s.jsxs)("div",{className:"mb-6 bg-green-50 p-4 rounded-lg dark:bg-green-900/20",children:[(0,s.jsx)("p",{className:"font-medium mb-2 text-green-800 dark:text-green-400",children:"Selected contacts:"}),(0,s.jsx)("div",{className:"max-h-40 overflow-y-auto scrollbar-thin scrollbar-thumb-green-600 scrollbar-track-green-300",children:(0,s.jsx)("ul",{className:"space-y-2",children:eR.map(e=>(0,s.jsx)("li",{className:"flex items-center justify-between",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(D.eu,{className:"h-8 w-8 mr-2",children:(0,s.jsx)(D.q5,{className:"bg-green-100 text-green-600 text-xs",children:e.contactName.charAt(0)})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:e.contactName}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:e.phoneNumber})]})]})},e._id))})})]}),(0,s.jsxs)("div",{className:"flex gap-4 mb-4",children:[(0,s.jsx)(l.$,{variant:"now"===eG?"default":"outline",onClick:()=>eH("now"),className:"flex-1 dark:border-gray-700",children:"Call Now"}),(0,s.jsx)(l.$,{variant:"schedule"===eG?"default":"outline",onClick:()=>eH("schedule"),className:"flex-1 dark:border-gray-700",children:"Schedule Call"})]}),"schedule"===eG&&(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("label",{className:"block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300",children:"Select Date & Time:"}),(0,s.jsxs)("div",{className:"relative flex items-center",children:[(0,s.jsx)("input",{type:"datetime-local",value:e2,onChange:e=>e1(e.target.value),min:(()=>{let e=new Date;return e.setMinutes(e.getMinutes()-e.getTimezoneOffset()),e.toISOString().slice(0,16)})(),className:"border dark:border-gray-700 dark:bg-gray-800 dark:text-white p-2 rounded w-full",ref:er}),(0,s.jsx)("div",{className:"absolute right-0 top-0 h-full flex items-center gap-2 pr-2",children:eq?(0,s.jsx)(l.$,{size:"sm",className:"bg-green-600 hover:bg-green-700 text-white h-8",onClick:()=>{var e;null===(e=er.current)||void 0===e||e.blur(),eX(!1)},children:"Save"}):(0,s.jsx)(l.$,{size:"sm",variant:"outline",className:"h-8",onClick:()=>{var e;null===(e=er.current)||void 0===e||e.showPicker(),eX(!0)},children:"Set Date"})})]}),e2&&!eq&&(0,s.jsxs)("p",{className:"text-sm text-green-600 mt-1",children:["Scheduled: ",new Date(e2).toLocaleString()]})]}),(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsx)("h3",{className:"text-sm font-medium mb-2 dark:text-gray-200",children:"Select an agent for this call:"}),(0,s.jsx)("div",{className:"max-h-[230px] overflow-y-auto pr-2",style:{scrollbarWidth:"thin"},children:(0,s.jsx)("div",{className:"grid grid-cols-2 gap-3",children:es.map(e=>(0,s.jsxs)("div",{onClick:()=>td(e.id),className:"border rounded-lg p-3 flex items-center gap-2 cursor-pointer transition-all ".concat(ti===e.id?"border-green-500 bg-green-50 dark:bg-green-900/20":"border-gray-200 dark:border-gray-700"," hover:border-green-300 dark:hover:border-green-600"),children:[(0,s.jsxs)(D.eu,{className:"h-10 w-10 flex-shrink-0",children:[(0,s.jsx)(D.BK,{src:e.avatar,alt:e.name}),(0,s.jsx)(D.q5,{className:"bg-gray-100",children:e.name.charAt(0)})]}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,s.jsx)("p",{className:"text-sm font-medium truncate",children:e.name}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:e.role})]}),ti===e.id&&(0,s.jsx)(w.A,{className:"h-5 w-5 text-green-500 flex-shrink-0"})]},e.id))})})]})]}),eJ&&(0,s.jsx)("div",{className:"px-5 mb-4",children:(0,s.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 text-red-700 dark:text-red-400 px-4 py-2 rounded-md text-sm",children:eJ})}),(0,s.jsxs)(I.Es,{className:"flex flex-col sm:flex-row gap-2 p-4 bg-gray-50 dark:bg-gray-800/50 border-t dark:border-gray-700",children:[(0,s.jsx)(I.HM,{asChild:!0,children:(0,s.jsx)(l.$,{variant:"outline",className:"w-full sm:w-auto",children:"Cancel"})}),"now"===eG?(0,s.jsx)(l.$,{className:"w-full sm:w-auto bg-green-600 hover:bg-green-700",onClick:()=>tv(ti),disabled:eU||!T,children:eU?"Connecting...":(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-2"})," Start Call"]})}):(0,s.jsx)(l.$,{className:"w-full sm:w-auto bg-green-600 hover:bg-green-700",onClick:()=>tN(ti),disabled:eU||eq||!T,children:eU?"Scheduling...":"Schedule Call"})]})]})})]})})}let V=e=>{let{id:t,label:a,visible:r,onToggle:n}=e,{attributes:c,listeners:o,setNodeRef:i,transform:d,transition:m}=(0,G.gl)({id:t}),x={transform:H.Ks.Transform.toString(d),transition:m};return(0,s.jsxs)("div",{ref:i,style:x,className:"flex items-center justify-between p-3 bg-card border rounded-lg",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("button",{className:"cursor-grab text-muted-foreground",...c,...o,children:(0,s.jsx)(A.A,{className:"h-5 w-5"})}),(0,s.jsx)("span",{className:"font-medium",children:a})]}),(0,s.jsx)(l.$,{variant:"ghost",size:"icon",onClick:n,className:r?"text-primary":"text-muted-foreground",children:r?(0,s.jsx)(E.A,{className:"h-5 w-5"}):(0,s.jsx)(_.A,{className:"h-5 w-5"})})]})}},69073:(e,t,a)=>{Promise.resolve().then(a.bind(a,67710))},85127:(e,t,a)=>{"use strict";a.d(t,{A0:()=>n,BF:()=>c,Hj:()=>o,XI:()=>r,nA:()=>d,nd:()=>i});var s=a(95155);a(12115);var l=a(59434);function r(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,l.cn)("w-full caption-bottom text-sm",t),...a})})}function n(e){let{className:t,...a}=e;return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,l.cn)("[&_tr]:border-b",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,l.cn)("[&_tr:last-child]:border-0",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,l.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...a})}function i(e){let{className:t,...a}=e;return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,l.cn)("text-muted-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,l.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...a})}},90010:(e,t,a)=>{"use strict";a.d(t,{$v:()=>u,EO:()=>d,Lt:()=>c,Rx:()=>g,Zr:()=>f,ck:()=>x,r7:()=>h,wd:()=>m});var s=a(95155);a(12115);var l=a(17649),r=a(59434),n=a(30285);function c(e){let{...t}=e;return(0,s.jsx)(l.bL,{"data-slot":"alert-dialog",...t})}function o(e){let{...t}=e;return(0,s.jsx)(l.ZL,{"data-slot":"alert-dialog-portal",...t})}function i(e){let{className:t,...a}=e;return(0,s.jsx)(l.hJ,{"data-slot":"alert-dialog-overlay",className:(0,r.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-650 bg-black/80",t),...a})}function d(e){let{className:t,...a}=e;return(0,s.jsxs)(o,{children:[(0,s.jsx)(i,{}),(0,s.jsx)(l.UC,{"data-slot":"alert-dialog-content",className:(0,r.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-650 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...a})]})}function m(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,r.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function x(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,r.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function h(e){let{className:t,...a}=e;return(0,s.jsx)(l.hE,{"data-slot":"alert-dialog-title",className:(0,r.cn)("text-lg font-semibold",t),...a})}function u(e){let{className:t,...a}=e;return(0,s.jsx)(l.VY,{"data-slot":"alert-dialog-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...a})}function g(e){let{className:t,...a}=e;return(0,s.jsx)(l.rc,{className:(0,r.cn)((0,n.r)(),t),...a})}function f(e){let{className:t,...a}=e;return(0,s.jsx)(l.ZD,{className:(0,r.cn)((0,n.r)({variant:"outline"}),t),...a})}}},e=>{var t=t=>e(e.s=t);e.O(0,[4201,4341,6403,1071,6671,6544,226,6874,424,9778,9757,7526,8441,1684,7358],()=>t(69073)),_N_E=e.O()}]);