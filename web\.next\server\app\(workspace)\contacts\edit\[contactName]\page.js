(()=>{var e={};e.id=1400,e.ids=[1400],e.modules={2314:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\app\\\\(workspace)\\\\contacts\\\\edit\\\\[contactName]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\edit\\[contactName]\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},44256:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var o=r(60687);r(43210);var a=r(16189);function s(){let e=(0,a.useParams)();return e?.contactName,(0,a.useRouter)(),(0,o.jsx)("div",{className:"flex items-center justify-center h-screen",children:(0,o.jsxs)("div",{className:"text-center",children:[(0,o.jsx)("h2",{className:"text-xl font-semibold mb-2",children:"Redirecting..."}),(0,o.jsx)("p",{className:"text-muted-foreground",children:"Please wait while we redirect you to the contact edit page."})]})})}r(89757)},44692:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>d,pages:()=>u,routeModule:()=>p,tree:()=>l});var o=r(65239),a=r(48088),s=r(88170),n=r.n(s),c=r(30893),i={};for(let e in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>c[e]);r.d(t,i);let l={children:["",{children:["(workspace)",{children:["contacts",{children:["edit",{children:["[contactName]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2314)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\edit\\[contactName]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,50184)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\contacts\\edit\\[contactName]\\page.tsx"],d={require:r,loadChunk:()=>Promise.resolve()},p=new o.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(workspace)/contacts/edit/[contactName]/page",pathname:"/contacts/edit/[contactName]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61064:(e,t,r)=>{Promise.resolve().then(r.bind(r,44256))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},89757:(e,t,r)=>{"use strict";r.d(t,{G6:()=>u,MO:()=>i,SQ:()=>c,TX:()=>l,oe:()=>a,oz:()=>d,to:()=>s,vY:()=>n});let o="http://localhost:4000";async function a(){try{let e=localStorage.getItem("access_token");if(!e)return console.error("No access token available"),[];let t=await fetch(`${o}/api/contacts`,{headers:{Authorization:`Bearer ${e}`}});if(!t.ok)throw Error("Failed to fetch contacts");return await t.json()}catch(e){throw console.error("Error fetching contacts:",e),e}}async function s(){try{let e=localStorage.getItem("access_token");if(!e)return console.error("No access token available"),[];let t=await fetch(`${o}/api/campaigns`,{headers:{Authorization:`Bearer ${e}`}});if(!t.ok)throw Error("Failed to fetch contacts");return await t.json()}catch(e){throw console.error("Error fetching contacts:",e),e}}async function n(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");let r=await fetch(`${o}/api/contacts`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify(e)});if(!r.ok)throw Error("Failed to create contact");return await r.json()}catch(e){throw console.error("Error creating contact:",e),e}}async function c(e){let t=localStorage.getItem("access_token");if(!t)throw Error("No access token available");let r=await fetch("http://localhost:4000/api/contacts",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify(e)});if(!r.ok)throw Error((await r.json()).error||`Failed to create contact: ${r.statusText}`);return r.json()}async function i(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");if(!(await fetch(`${o}/api/contacts/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`}})).ok)throw Error("Failed to delete contact")}catch(e){throw console.error("Error deleting contact:",e),e}}async function l(){try{let e=localStorage.getItem("access_token");if(!e)throw console.error("No access token available"),Error("No access token available");let t=await fetch(`${o}/api/contacts/import-contacts`,{headers:{Authorization:`Bearer ${e}`}});if(!t.ok)throw Error("Failed to import contacts");await t.json()}catch(e){throw console.error("Error importing contacts:",e),e}}async function u(e,t,r){try{let a=localStorage.getItem("access_token");if(!a)throw console.error("No access token available"),Error("No access token available");let s=await fetch(`${o}/api/vapi/call-contacts`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a}`},body:JSON.stringify({agentId:e,contacts:t,region:r})});if(402===s.status){let e=await s.json();throw Error(e.error||"Insufficient credits to make this call. Please add funds to your account.")}if(!s.ok)throw Error("Failed to start call");return await s.json()}catch(e){throw console.error("Error calling contact(s):",e),e}}async function d(e,t,r,a){try{let s=localStorage.getItem("access_token");if(!s)throw console.error("No access token available"),Error("No access token available");let n=await fetch(`${o}/api/scheduled-call`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s}`},body:JSON.stringify({agentId:e,contacts:t,scheduledTime:r,region:a})});if(402===n.status){let e=await n.json();throw Error(e.error||"Insufficient credits to schedule this call. Please add funds to your account.")}if(!n.ok)throw Error("Failed to schedule call");return await n.json()}catch(e){throw console.error("Error scheduling call:",e),e}}},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97512:(e,t,r)=>{Promise.resolve().then(r.bind(r,2314))}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[287,9176,7674,5814,598,5188,1476,4772],()=>r(44692));module.exports=o})();