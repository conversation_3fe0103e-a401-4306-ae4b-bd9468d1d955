(()=>{var e={};e.id=6878,e.ids=[6878],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(e,t,s)=>{"use strict";s.d(t,{A0:()=>l,BF:()=>i,Hj:()=>o,XI:()=>n,nA:()=>c,nd:()=>d});var r=s(60687);s(43210);var a=s(4780);function n({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,a.cn)("w-full caption-bottom text-sm",e),...t})})}function l({className:e,...t}){return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,a.cn)("[&_tr]:border-b",e),...t})}function i({className:e,...t}){return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,a.cn)("[&_tr:last-child]:border-0",e),...t})}function o({className:e,...t}){return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,a.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",e),...t})}function d({className:e,...t}){return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,a.cn)("text-muted-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}function c({className:e,...t}){return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,a.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},21987:(e,t,s)=>{Promise.resolve().then(s.bind(s,41256))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30789:(e,t,s)=>{"use strict";s.d(t,{default:()=>y});var r=s(60687),a=s(43210),n=s(29523),l=s(6211),i=s(63503),o=s(89667),d=s(80013),c=s(96834),u=s(96474),m=s(99270),x=s(41862),p=s(62688);let h=(0,p.A)("ShieldCheck",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),g=(0,p.A)("ShieldAlert",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"M12 8v4",key:"1got3b"}],["path",{d:"M12 16h.01",key:"1drbdi"}]]);var f=s(57175),v=s(88233),b=s(52581),j=s(93500),w=s(24258);function y(){let[e,t]=(0,a.useState)([]),[s,p]=(0,a.useState)(!0),[y,N]=(0,a.useState)(!1),[k,A]=(0,a.useState)(!1),[C,_]=(0,a.useState)(!1),[q,E]=(0,a.useState)(!1),[S,z]=(0,a.useState)(null),[P,U]=(0,a.useState)(""),[F,D]=(0,a.useState)({fullName:"",email:"",password:""}),[O,L]=(0,a.useState)(null),M=async()=>{p(!0);try{let e=await (0,w.hU)();t(e)}catch(e){console.error("Error loading users:",e),b.o.error("Failed to load users")}finally{p(!1)}},$=e=>{let{name:t,value:s}=e.target;D(e=>({...e,[t]:s}))},G=async e=>{e.preventDefault(),L("add");try{let e={fullName:F.fullName,email:F.email,password:F.password};D({fullName:"",email:"",password:""}),N(!1),await (0,w.nu)(e),await M()}catch(e){console.error("Error adding user:",e)}finally{L(null)}},V=async e=>{if(e.preventDefault(),S){L("edit");try{let e={};F.fullName&&(e.fullName=F.fullName),F.email&&(e.email=F.email),F.password&&(e.password=F.password),await (0,w.TK)(S._id,e),b.o.success("User updated successfully"),A(!1),D({fullName:"",email:"",password:""}),M()}catch(e){console.error("Error updating user:",e),b.o.error("Failed to update user")}finally{L(null)}}},R=async()=>{if(S){L("delete");try{await (0,w.hG)(S._id),b.o.success("User deleted successfully"),_(!1),M()}catch(e){console.error("Error deleting user:",e),b.o.error("Failed to delete user")}finally{L(null)}}},B=async()=>{if(!S)return;let e=!S.isApproved;L("access");try{e?await (0,w.LB)(S._id):await (0,w.s2)(S._id),b.o.success(`User ${e?"access granted":"access revoked"} successfully`),E(!1),M()}catch(e){console.error("Error updating user status:",e),b.o.error("Failed to update user status")}finally{L(null)}},J=e=>{let t=new Date(e);return isNaN(t.getTime())?"N/A":t.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})},H=e.filter(e=>e.fullName.toLowerCase().includes(P.toLowerCase())||e.email.toLowerCase().includes(P.toLowerCase()));return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold",children:"Users Management"}),(0,r.jsxs)(n.$,{className:"bg-primary hover:bg-primary/90 flex items-center gap-2",onClick:()=>N(!0),children:[(0,r.jsx)(u.A,{className:"h-4 w-4"}),"Add User"]})]}),(0,r.jsx)("div",{className:"flex flex-col sm:flex-row gap-2 mb-6",children:(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(m.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,r.jsx)(o.p,{placeholder:"Search users...",className:"pl-10",value:P,onChange:e=>U(e.target.value)})]})}),(0,r.jsx)("div",{className:"bg-card rounded-lg border shadow-sm overflow-hidden",children:(0,r.jsxs)(l.XI,{children:[(0,r.jsx)(l.A0,{children:(0,r.jsxs)(l.Hj,{children:[(0,r.jsx)(l.nd,{children:"Name"}),(0,r.jsx)(l.nd,{children:"Email"}),(0,r.jsx)(l.nd,{children:"Date Created"}),(0,r.jsx)(l.nd,{children:"Status"}),(0,r.jsx)(l.nd,{className:"text-right",children:"Actions"})]})}),(0,r.jsx)(l.BF,{children:s?(0,r.jsx)(l.Hj,{children:(0,r.jsx)(l.nA,{colSpan:5,className:"h-64 text-center",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 animate-spin text-primary mb-2"}),(0,r.jsx)("p",{className:"text-lg font-medium text-gray-600 dark:text-gray-300",children:"Loading users..."})]})})}):H.length>0?H.map(e=>(0,r.jsxs)(l.Hj,{children:[(0,r.jsx)(l.nA,{className:"font-medium",children:e.fullName}),(0,r.jsx)(l.nA,{children:e.email}),(0,r.jsx)(l.nA,{children:J(e.createdAt)}),(0,r.jsx)(l.nA,{children:e.isApproved?(0,r.jsxs)(c.E,{variant:"outline",className:"bg-green-50 text-green-700 border-green-200 hover:bg-green-50",children:[(0,r.jsx)(h,{className:"h-3 w-3 mr-1"}),"Approved"]}):(0,r.jsxs)(c.E,{variant:"outline",className:"bg-red-50 text-red-700 border-red-200 hover:bg-red-50",children:[(0,r.jsx)(g,{className:"h-3 w-3 mr-1"}),"Denied"]})}),(0,r.jsx)(l.nA,{className:"text-right",children:(0,r.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,r.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>{z(e),E(!0)},className:e.isApproved?"text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700":"text-green-600 border-green-200 hover:bg-green-50 hover:text-green-700",disabled:O===e._id,children:e.isApproved?"Revoke Access":"Grant Access"}),(0,r.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>{z(e),D({fullName:"",email:"",password:""}),A(!0)},className:"text-gray-600 border-gray-200 hover:bg-gray-50 hover:text-gray-700",children:[(0,r.jsx)(f.A,{className:"h-4 w-4 mr-1"}),"Edit"]}),(0,r.jsx)(n.$,{variant:"outline",size:"sm",className:"text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700",onClick:()=>{z(e),_(!0)},children:(0,r.jsx)(v.A,{className:"h-4 w-4 mr-1"})})]})})]},e._id)):(0,r.jsx)(l.Hj,{children:(0,r.jsx)(l.nA,{colSpan:5,className:"h-64 text-center",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,r.jsx)("p",{className:"text-lg font-medium text-gray-600 dark:text-gray-300 mb-2",children:"No users found"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground max-w-md text-center mb-4",children:P?"No users match your search criteria":"Create users to manage access to the platform"}),(0,r.jsxs)(n.$,{variant:"outline",onClick:()=>N(!0),children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Add your first user"]})]})})})})]})}),(0,r.jsx)(i.lG,{open:y,onOpenChange:N,children:(0,r.jsxs)(i.Cf,{className:"sm:max-w-[425px]",children:[(0,r.jsx)(i.c7,{children:(0,r.jsx)(i.L3,{children:"Add New User"})}),(0,r.jsxs)("form",{onSubmit:G,children:[(0,r.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(d.J,{htmlFor:"fullName",children:"Full Name"}),(0,r.jsx)(o.p,{id:"fullName",name:"fullName",value:F.fullName,onChange:$,required:!0})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(d.J,{htmlFor:"email",children:"Email"}),(0,r.jsx)(o.p,{id:"email",name:"email",type:"email",value:F.email,onChange:$,required:!0})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(d.J,{htmlFor:"password",children:"Password"}),(0,r.jsx)(o.p,{id:"password",name:"password",type:"password",value:F.password,onChange:$,required:!0})]})]}),(0,r.jsxs)(i.Es,{className:"flex flex-col sm:flex-row gap-2",children:[(0,r.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>N(!1),disabled:!!O,children:"Cancel"}),(0,r.jsx)(n.$,{type:"submit",disabled:!!O,className:"bg-primary hover:bg-primary/90",children:"add"===O?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Adding..."]}):"Add User"})]})]})]})}),(0,r.jsx)(i.lG,{open:k,onOpenChange:A,children:(0,r.jsxs)(i.Cf,{className:"sm:max-w-[425px]",children:[(0,r.jsx)(i.c7,{children:(0,r.jsx)(i.L3,{children:"Edit User"})}),(0,r.jsxs)("form",{onSubmit:V,children:[(0,r.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(d.J,{htmlFor:"edit-fullName",children:"Full Name"}),(0,r.jsx)(o.p,{id:"edit-fullName",name:"fullName",value:F.fullName,onChange:$,placeholder:S?.fullName})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(d.J,{htmlFor:"edit-email",children:"Email"}),(0,r.jsx)(o.p,{id:"edit-email",name:"email",type:"email",value:F.email,onChange:$,placeholder:S?.email})]}),(0,r.jsxs)("div",{className:"grid gap-2",children:[(0,r.jsx)(d.J,{htmlFor:"edit-password",children:"New Password (leave blank to keep current)"}),(0,r.jsx)(o.p,{id:"edit-password",name:"password",type:"password",value:F.password,onChange:$})]})]}),(0,r.jsxs)(i.Es,{children:[(0,r.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>A(!1),disabled:!!O,children:"Cancel"}),(0,r.jsx)(n.$,{type:"submit",disabled:!!O,className:"bg-blue-600 hover:bg-blue-700 text-white",children:"edit"===O?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Updating..."]}):"Update User"})]})]})]})}),(0,r.jsx)(j.Lt,{open:C,onOpenChange:_,children:(0,r.jsxs)(j.EO,{children:[(0,r.jsxs)(j.wd,{children:[(0,r.jsx)(j.r7,{children:"Delete User"}),(0,r.jsxs)(j.$v,{children:["Are you sure you want to delete user ",(0,r.jsx)("strong",{children:S?.fullName}),"? This action cannot be undone."]})]}),(0,r.jsxs)(j.ck,{children:[(0,r.jsx)(j.Zr,{disabled:!!O,children:"Cancel"}),(0,r.jsx)(j.Rx,{className:"bg-red-600 hover:bg-red-700 text-white",onClick:e=>{e.preventDefault(),R()},disabled:!!O,children:"delete"===O?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Deleting..."]}):"Delete User"})]})]})}),(0,r.jsx)(j.Lt,{open:q,onOpenChange:E,children:(0,r.jsxs)(j.EO,{children:[(0,r.jsxs)(j.wd,{children:[(0,r.jsx)(j.r7,{children:S?.isApproved?"Revoke Access":"Grant Access"}),(0,r.jsx)(j.$v,{children:S?.isApproved?(0,r.jsxs)(r.Fragment,{children:["Are you sure you want to ",(0,r.jsx)("strong",{children:"revoke access"})," for ",S?.fullName,"? They will no longer be able to log in until access is restored."]}):(0,r.jsxs)(r.Fragment,{children:["Are you sure you want to ",(0,r.jsx)("strong",{children:"grant access"})," to ",S?.fullName,"? This will allow them to log in to the platform."]})})]}),(0,r.jsxs)(j.ck,{children:[(0,r.jsx)(j.Zr,{disabled:"access"===O,children:"Cancel"}),(0,r.jsx)(j.Rx,{className:S?.isApproved?"bg-black hover:bg-gray-800 text-white":"bg-green-600 hover:bg-green-700 text-white",onClick:e=>{e.preventDefault(),B()},disabled:"access"===O,children:"access"===O?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Processing..."]}):S?.isApproved?"Revoke Access":"Grant Access"})]})]})})]})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40008:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>d});var r=s(65239),a=s(48088),n=s(88170),l=s.n(n),i=s(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(t,o);let d={children:["",{children:["(workspace)",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,48440)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\users\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,50184)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\users\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(workspace)/users/page",pathname:"/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},41256:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\app\\\\(workspace)\\\\users\\\\UsersManagement.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\users\\UsersManagement.tsx","default")},48440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>n});var r=s(37413);s(61120);var a=s(41256);function n(){return(0,r.jsx)(r.Fragment,{children:(0,r.jsx)(a.default,{})})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57175:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("Pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63503:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>m,Es:()=>p,HM:()=>c,L3:()=>h,c7:()=>x,lG:()=>i,rr:()=>g,zM:()=>o});var r=s(60687);s(43210);var a=s(26134),n=s(11860),l=s(4780);function i({...e}){return(0,r.jsx)(a.bL,{"data-slot":"dialog",...e})}function o({...e}){return(0,r.jsx)(a.l9,{"data-slot":"dialog-trigger",...e})}function d({...e}){return(0,r.jsx)(a.ZL,{"data-slot":"dialog-portal",...e})}function c({...e}){return(0,r.jsx)(a.bm,{"data-slot":"dialog-close",...e})}function u({className:e,...t}){return(0,r.jsx)(a.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-650 bg-black/50",e),...t})}function m({className:e,children:t,...s}){return(0,r.jsxs)(d,{"data-slot":"dialog-portal",children:[(0,r.jsx)(u,{}),(0,r.jsxs)(a.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-650 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...s,children:[t,(0,r.jsxs)(a.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,r.jsx)(n.A,{}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function p({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function h({className:e,...t}){return(0,r.jsx)(a.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",e),...t})}function g({className:e,...t}){return(0,r.jsx)(a.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",e),...t})}},74075:e=>{"use strict";e.exports=require("zlib")},78148:(e,t,s)=>{"use strict";s.d(t,{b:()=>i});var r=s(43210),a=s(14163),n=s(60687),l=r.forwardRef((e,t)=>(0,n.jsx)(a.sG.label,{...e,ref:t,onMouseDown:t=>{t.target.closest("button, input, select, textarea")||(e.onMouseDown?.(t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=l},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},80013:(e,t,s)=>{"use strict";s.d(t,{J:()=>l});var r=s(60687);s(43210);var a=s(78148),n=s(4780);function l({className:e,...t}){return(0,r.jsx)(a.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...t})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87643:(e,t,s)=>{Promise.resolve().then(s.bind(s,30789))},89667:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var r=s(60687);s(43210);var a=s(4780);function n({className:e,type:t,...s}){return(0,r.jsx)("input",{type:t,"data-slot":"input",className:(0,a.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...s})}},91645:e=>{"use strict";e.exports=require("net")},93500:(e,t,s)=>{"use strict";s.d(t,{$v:()=>p,EO:()=>c,Lt:()=>i,Rx:()=>h,Zr:()=>g,ck:()=>m,r7:()=>x,wd:()=>u});var r=s(60687);s(43210);var a=s(97895),n=s(4780),l=s(29523);function i({...e}){return(0,r.jsx)(a.bL,{"data-slot":"alert-dialog",...e})}function o({...e}){return(0,r.jsx)(a.ZL,{"data-slot":"alert-dialog-portal",...e})}function d({className:e,...t}){return(0,r.jsx)(a.hJ,{"data-slot":"alert-dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-650 bg-black/80",e),...t})}function c({className:e,...t}){return(0,r.jsxs)(o,{children:[(0,r.jsx)(d,{}),(0,r.jsx)(a.UC,{"data-slot":"alert-dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-650 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...t})]})}function u({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",e),...t})}function m({className:e,...t}){return(0,r.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function x({className:e,...t}){return(0,r.jsx)(a.hE,{"data-slot":"alert-dialog-title",className:(0,n.cn)("text-lg font-semibold",e),...t})}function p({className:e,...t}){return(0,r.jsx)(a.VY,{"data-slot":"alert-dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...t})}function h({className:e,...t}){return(0,r.jsx)(a.rc,{className:(0,n.cn)((0,l.r)(),e),...t})}function g({className:e,...t}){return(0,r.jsx)(a.ZD,{className:(0,n.cn)((0,l.r)({variant:"outline"}),e),...t})}},94735:e=>{"use strict";e.exports=require("events")},96474:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(62688).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96834:(e,t,s)=>{"use strict";s.d(t,{E:()=>o});var r=s(60687);s(43210);var a=s(8730),n=s(24224),l=s(4780);let i=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,asChild:s=!1,...n}){let o=s?a.DX:"span";return(0,r.jsx)(o,{"data-slot":"badge",className:(0,l.cn)(i({variant:t}),e),...n})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[287,9176,7674,5814,598,5188,9677,1476,4772],()=>s(40008));module.exports=r})();