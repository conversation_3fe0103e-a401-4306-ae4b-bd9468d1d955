(()=>{var e={};e.id=7893,e.ids=[7893],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4982:(e,t,r)=>{Promise.resolve().then(r.bind(r,86304))},7298:(e,t,r)=>{"use strict";r.d(t,{default:()=>j});var s=r(60687),a=r(43210),n=r(29523),i=r(44493),o=r(28559),c=r(13964),l=r(70334),d=r(5336),u=r(5522),p=r(2280),m=r(85814),x=r.n(m),h=r(19352),g=r(61502),v=r(63503),b=r(84925);let f=[{id:"sources",label:"Sources"},{id:"agents",label:"Agent"},{id:"schedule",label:"Schedule"},{id:"settings",label:"Settings"}];function j(){let[e,t]=(0,a.useState)(0),[r,m]=(0,a.useState)(!1),[j,y]=(0,a.useState)(!1),[w,C]=(0,a.useState)(!1),[N,k]=(0,a.useState)(""),[q,P]=(0,a.useState)([]),[S,A]=(0,a.useState)(null),[_,O]=(0,a.useState)({name:"",concurrentCalls:1,dailyCost:0,startDate:"",endDate:"",successRate:0,sentiment:"neutral",status:"inactive",instantCall:!1,batchIntervalMinutes:3,sources:[],sourceType:"contacts",agents:[],callSchedule:{startTime:"09:00",endTime:"17:00",timezone:"America/New_York",daysOfWeek:["monday","tuesday","wednesday","thursday","friday"]},callWindow:{startTime:"09:00",endTime:"17:00",timezone:"America/New_York",daysOfWeek:["monday","tuesday","wednesday","thursday","friday"]},recallHours:24,maxRecalls:3,followUpDays:["monday","tuesday","wednesday","thursday","friday"]}),D=e=>{O(t=>({...t,...e}))},W=async e=>{m(!0);try{let t={..._,contacts:_.sources.map(e=>({contactId:e._id,contactName:e.contactName,phoneNumber:e.phoneNumber})),endDate:_.endDate||null,status:e};await (0,u.bi)(t),k(_.name),C(!0)}catch(e){console.error("Error creating campaign:",e)}finally{m(!1)}};return(0,s.jsxs)("div",{className:"container max-w-4xl mx-auto py-6",children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)(x(),{href:"/campaign",className:"text-sm text-muted-foreground hover:text-primary flex items-center gap-2",children:[(0,s.jsx)(o.A,{className:"h-4 w-4"}),"Back to Campaigns"]})}),(0,s.jsxs)(i.Zp,{children:[(0,s.jsxs)(i.aR,{children:[(0,s.jsx)(i.ZB,{children:"Create New Campaign"}),(0,s.jsx)(i.BT,{children:"Set up your outbound call campaign in just a few steps"})]}),(0,s.jsxs)(i.Wu,{children:[(0,s.jsxs)("div",{className:"mb-8",children:[(0,s.jsx)("div",{className:"flex justify-between",children:f.map((t,r)=>(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)("div",{className:`w-10 h-10 rounded-full flex items-center justify-center border-2
                      ${r<e?"bg-primary border-primary text-white":r===e?"border-primary text-primary":"border-gray-300 text-gray-400"}`,children:r<e?(0,s.jsx)(c.A,{className:"h-5 w-5"}):(0,s.jsx)("span",{children:r+1})}),(0,s.jsx)("span",{className:`mt-2 text-sm ${r<=e?"text-primary font-medium":"text-gray-500"}`,children:t.label})]},t.id))}),(0,s.jsx)("div",{className:"relative mt-2",children:(0,s.jsx)("div",{className:"absolute top-0 left-0 right-0 h-1 bg-gray-200",children:(0,s.jsx)("div",{className:"h-1 bg-primary transition-all",style:{width:`${e/(f.length-1)*100}%`}})})})]}),(0,s.jsx)("div",{className:"py-4",children:(()=>{switch(e){case 0:return(0,s.jsx)(p.A,{data:_,updateData:D});case 1:return(0,s.jsx)(h.A,{data:_,updateData:D,loading:j,userRole:S});case 2:return(0,s.jsx)(b.A,{data:_,updateData:e=>D(e)});case 3:return(0,s.jsx)(g.A,{data:_,updateData:D});default:return null}})()}),q.length>0&&(0,s.jsx)("div",{className:"mt-4 p-3 bg-red-50 border border-red-200 rounded-md",children:q.map((e,t)=>(0,s.jsxs)("p",{className:"text-red-600 text-sm flex items-center",children:[(0,s.jsx)("span",{className:"mr-2",children:"•"})," ",e]},t))})]}),(0,s.jsxs)(i.wL,{className:"flex justify-between",children:[(0,s.jsx)(n.$,{variant:"outline",onClick:()=>t(e-1),disabled:0===e,children:"Previous"}),e<f.length-1?(0,s.jsxs)(n.$,{onClick:()=>{let r=[];switch(e){case 0:_.name&&""!==_.name.trim()||r.push("Campaign name is required"),0===_.sources.length&&r.push("Please select at least one contact");break;case 1:_.agentId&&""!==_.agentId.trim()||r.push("Please select an agent")}P(r),0===r.length&&e<f.length-1&&t(e+1)},children:["Next ",(0,s.jsx)(l.A,{className:"ml-2 h-4 w-4"})]}):(0,s.jsxs)("div",{className:"flex gap-3",children:[(0,s.jsx)(n.$,{variant:"outline",onClick:()=>W("inactive"),disabled:r,children:r?"Saving...":"Save Campaign"}),(0,s.jsx)(n.$,{onClick:()=>W("active"),disabled:r,children:r?"Publishing...":"Publish Campaign"})]})]})]}),(0,s.jsx)(v.lG,{open:w,onOpenChange:C,children:(0,s.jsxs)(v.Cf,{className:"sm:max-w-md",children:[(0,s.jsxs)(v.c7,{children:[(0,s.jsxs)(v.L3,{className:"flex items-center gap-2",children:[(0,s.jsx)(d.A,{className:"h-5 w-5 text-green-500"}),"Campaign Created Successfully"]}),(0,s.jsxs)(v.rr,{children:['Your campaign "',N,'" has been created and is ready to use.']})]}),(0,s.jsx)("div",{className:"py-4",children:(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"You can now view and manage your campaign from the campaigns dashboard."})}),(0,s.jsx)(v.Es,{children:(0,s.jsx)(x(),{href:"/campaign",children:(0,s.jsx)(n.$,{className:"w-full",children:"Go to Campaigns"})})})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16606:(e,t,r)=>{Promise.resolve().then(r.bind(r,7298))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},57354:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>l});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),o=r(30893),c={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);r.d(t,c);let l={children:["",{children:["(workspace)",{children:["campaign",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81267)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\create\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,50184)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\create\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(workspace)/campaign/create/page",pathname:"/campaign/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70334:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81267:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(37413);r(61120);var a=r(86304);function n(){return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(a.default,{})})}},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},86304:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\app\\\\(workspace)\\\\campaign\\\\create\\\\CreateCampaign.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\campaign\\create\\CreateCampaign.tsx","default")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[287,9176,7674,5814,598,5188,6034,2256,2766,8606,3697,9017,1476,4772,1158,6407],()=>r(57354));module.exports=s})();