"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1504],{2564:(e,t,r)=>{r.d(t,{b:()=>i,s:()=>a});var o=r(12115),s=r(63655),n=r(95155),a=o.forwardRef((e,t)=>(0,n.jsx)(s.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));a.displayName="VisuallyHidden";var i=a},6736:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.client=void 0,t.client=new(r(54301)).Api({baseUrl:"https://api.vapi.ai",baseApiParams:{secure:!0},securityWorker:async e=>{if(e)return{headers:{Authorization:`Bearer ${e}`}}}})},17649:(e,t,r)=>{r.d(t,{UC:()=>$,VY:()=>F,ZD:()=>x,ZL:()=>O,bL:()=>S,hE:()=>G,hJ:()=>L,rc:()=>D});var o=r(12115),s=r(46081),n=r(6101),a=r(15452),i=r(85185),l=r(99708),u=r(95155),h="AlertDialog",[d,c]=(0,s.A)(h,[a.Hs]),p=(0,a.Hs)(),m=e=>{let{__scopeAlertDialog:t,...r}=e,o=p(t);return(0,u.jsx)(a.bL,{...o,...r,modal:!0})};m.displayName=h,o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,s=p(r);return(0,u.jsx)(a.l9,{...s,...o,ref:t})}).displayName="AlertDialogTrigger";var y=e=>{let{__scopeAlertDialog:t,...r}=e,o=p(t);return(0,u.jsx)(a.ZL,{...o,...r})};y.displayName="AlertDialogPortal";var f=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,s=p(r);return(0,u.jsx)(a.hJ,{...s,...o,ref:t})});f.displayName="AlertDialogOverlay";var v="AlertDialogContent",[C,g]=d(v),b=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:s,...h}=e,d=p(r),c=o.useRef(null),m=(0,n.s)(t,c),y=o.useRef(null);return(0,u.jsx)(a.G$,{contentName:v,titleName:q,docsSlug:"alert-dialog",children:(0,u.jsx)(C,{scope:r,cancelRef:y,children:(0,u.jsxs)(a.UC,{role:"alertdialog",...d,...h,ref:m,onOpenAutoFocus:(0,i.m)(h.onOpenAutoFocus,e=>{var t;e.preventDefault(),null===(t=y.current)||void 0===t||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,u.jsx)(l.xV,{children:s}),(0,u.jsx)(P,{contentRef:c})]})})})});b.displayName=v;var q="AlertDialogTitle",j=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,s=p(r);return(0,u.jsx)(a.hE,{...s,...o,ref:t})});j.displayName=q;var T="AlertDialogDescription",E=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,s=p(r);return(0,u.jsx)(a.VY,{...s,...o,ref:t})});E.displayName=T;var w=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,s=p(r);return(0,u.jsx)(a.bm,{...s,...o,ref:t})});w.displayName="AlertDialogAction";var A="AlertDialogCancel",k=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,{cancelRef:s}=g(A,r),i=p(r),l=(0,n.s)(t,s);return(0,u.jsx)(a.bm,{...i,...o,ref:l})});k.displayName=A;var P=e=>{let{contentRef:t}=e,r="`".concat(v,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(v,"` by passing a `").concat(T,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(v,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return o.useEffect(()=>{var e;document.getElementById(null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},S=m,O=y,L=f,$=b,D=w,x=k,G=j,F=E},40662:e=>{var t,r="object"==typeof Reflect?Reflect:null,o=r&&"function"==typeof r.apply?r.apply:function(e,t,r){return Function.prototype.apply.call(e,t,r)};t=r&&"function"==typeof r.ownKeys?r.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var s=Number.isNaN||function(e){return e!=e};function n(){n.init.call(this)}e.exports=n,e.exports.once=function(e,t){return new Promise(function(r,o){var s,n,a;function i(r){e.removeListener(t,l),o(r)}function l(){"function"==typeof e.removeListener&&e.removeListener("error",i),r([].slice.call(arguments))}y(e,t,l,{once:!0}),"error"!==t&&(s=e,n=i,a={once:!0},"function"==typeof s.on&&y(s,"error",n,a))})},n.EventEmitter=n,n.prototype._events=void 0,n.prototype._eventsCount=0,n.prototype._maxListeners=void 0;var a=10;function i(e){if("function"!=typeof e)throw TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function l(e){return void 0===e._maxListeners?n.defaultMaxListeners:e._maxListeners}function u(e,t,r,o){if(i(r),void 0===(n=e._events)?(n=e._events=Object.create(null),e._eventsCount=0):(void 0!==n.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),n=e._events),a=n[t]),void 0===a)a=n[t]=r,++e._eventsCount;else if("function"==typeof a?a=n[t]=o?[r,a]:[a,r]:o?a.unshift(r):a.push(r),(s=l(e))>0&&a.length>s&&!a.warned){a.warned=!0;var s,n,a,u=Error("Possible EventEmitter memory leak detected. "+a.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");u.name="MaxListenersExceededWarning",u.emitter=e,u.type=t,u.count=a.length,console&&console.warn&&console.warn(u)}return e}function h(){if(!this.fired)return(this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0==arguments.length)?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function d(e,t,r){var o={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},s=h.bind(o);return s.listener=r,o.wrapFn=s,s}function c(e,t,r){var o=e._events;if(void 0===o)return[];var s=o[t];return void 0===s?[]:"function"==typeof s?r?[s.listener||s]:[s]:r?function(e){for(var t=Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(s):m(s,s.length)}function p(e){var t=this._events;if(void 0!==t){var r=t[e];if("function"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function m(e,t){for(var r=Array(t),o=0;o<t;++o)r[o]=e[o];return r}function y(e,t,r,o){if("function"==typeof e.on)o.once?e.once(t,r):e.on(t,r);else if("function"==typeof e.addEventListener)e.addEventListener(t,function s(n){o.once&&e.removeEventListener(t,s),r(n)});else throw TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}Object.defineProperty(n,"defaultMaxListeners",{enumerable:!0,get:function(){return a},set:function(e){if("number"!=typeof e||e<0||s(e))throw RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");a=e}}),n.init=function(){(void 0===this._events||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},n.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||s(e))throw RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},n.prototype.getMaxListeners=function(){return l(this)},n.prototype.emit=function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var s="error"===e,n=this._events;if(void 0!==n)s=s&&void 0===n.error;else if(!s)return!1;if(s){if(t.length>0&&(a=t[0]),a instanceof Error)throw a;var a,i=Error("Unhandled error."+(a?" ("+a.message+")":""));throw i.context=a,i}var l=n[e];if(void 0===l)return!1;if("function"==typeof l)o(l,this,t);else for(var u=l.length,h=m(l,u),r=0;r<u;++r)o(h[r],this,t);return!0},n.prototype.addListener=function(e,t){return u(this,e,t,!1)},n.prototype.on=n.prototype.addListener,n.prototype.prependListener=function(e,t){return u(this,e,t,!0)},n.prototype.once=function(e,t){return i(t),this.on(e,d(this,e,t)),this},n.prototype.prependOnceListener=function(e,t){return i(t),this.prependListener(e,d(this,e,t)),this},n.prototype.removeListener=function(e,t){var r,o,s,n,a;if(i(t),void 0===(o=this._events)||void 0===(r=o[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete o[e],o.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(s=-1,n=r.length-1;n>=0;n--)if(r[n]===t||r[n].listener===t){a=r[n].listener,s=n;break}if(s<0)return this;0===s?r.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(r,s),1===r.length&&(o[e]=r[0]),void 0!==o.removeListener&&this.emit("removeListener",e,a||t)}return this},n.prototype.off=n.prototype.removeListener,n.prototype.removeAllListeners=function(e){var t,r,o;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0==arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0==arguments.length){var s,n=Object.keys(r);for(o=0;o<n.length;++o)"removeListener"!==(s=n[o])&&this.removeAllListeners(s);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(void 0!==t)for(o=t.length-1;o>=0;o--)this.removeListener(e,t[o]);return this},n.prototype.listeners=function(e){return c(this,e,!0)},n.prototype.rawListeners=function(e){return c(this,e,!1)},n.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):p.call(e,t)},n.prototype.listenerCount=p,n.prototype.eventNames=function(){return this._eventsCount>0?t(this._events):[]}},41185:function(e,t,r){var o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=o(r(20848)),n=o(r(40662)),a=r(6736);async function i(e,t){e.muted=!1,e.autoplay=!0,null!=t&&(e.srcObject=new MediaStream([t]),await e.play())}async function l(e,t){let r=document.createElement("audio");return r.dataset.participantId=t,document.body.appendChild(r),await i(r,e),r}class u extends n.default{on(e,t){return super.on(e,t),this}once(e,t){return super.once(e,t),this}emit(e,...t){return super.emit(e,...t)}removeListener(e,t){return super.removeListener(e,t),this}removeAllListeners(e){return super.removeAllListeners(e),this}}class h extends u{started=!1;call=null;speakingTimeout=null;dailyCallConfig={};dailyCallObject={};hasEmittedCallEndedStatus=!1;constructor(e,t,r,o){super(),a.client.baseUrl=t??"https://api.vapi.ai",a.client.setSecurityData(e),this.dailyCallConfig=r??{},this.dailyCallObject=o??{}}cleanup(){this.started=!1,this.hasEmittedCallEndedStatus=!1,this.call?.destroy(),this.call=null,this.speakingTimeout=null}isMobileDevice(){if("undefined"==typeof navigator)return!1;let e=navigator.userAgent;return/android|iphone|ipad|ipod|iemobile|blackberry|bada/i.test(e.toLowerCase())}async sleep(e){return new Promise(t=>setTimeout(t,e))}async start(e,t,r,o){if(!e&&!r&&!o)throw Error("Assistant or Squad or Workflow must be provided.");if(this.started)return null;this.started=!0;try{let n=(await a.client.call.callControllerCreateWebCall({assistant:"string"==typeof e?void 0:e,assistantId:"string"==typeof e?e:void 0,assistantOverrides:t,squad:"string"==typeof r?void 0:r,squadId:"string"==typeof r?r:void 0,workflow:"string"==typeof o?void 0:o,workflowId:"string"==typeof o?o:void 0})).data;this.call&&this.cleanup();let i=n?.artifactPlan?.videoRecordingEnabled??!1,u=n?.assistant?.voice?.provider==="tavus";if(this.call=s.default.createCallObject({audioSource:this.dailyCallObject.audioSource??!0,videoSource:this.dailyCallObject.videoSource??i,dailyConfig:this.dailyCallConfig}),this.call.iframe()?.style.setProperty("display","none"),this.call.on("left-meeting",()=>{this.emit("call-end"),this.hasEmittedCallEndedStatus||(this.emit("message",{type:"status-update",status:"ended",endedReason:"customer-ended-call"}),this.hasEmittedCallEndedStatus=!0),i&&this.call?.stopRecording(),this.cleanup()}),this.call.on("error",e=>{this.emit("error",e),i&&this.call?.stopRecording()}),this.call.on("camera-error",e=>{this.emit("error",e)}),this.call.on("track-started",async e=>{!(!e||!e.participant||e.participant?.local)&&e.participant?.user_name==="Vapi Speaker"&&("video"===e.track.kind&&this.emit("video",e.track),"audio"===e.track.kind&&await l(e.track,e.participant.session_id),this.call?.sendAppMessage("playable"))}),this.call.on("participant-joined",e=>{if(e&&this.call){var t;t=this.call,e.participant.local||t.updateParticipant(e.participant.session_id,{setSubscribedTracks:{audio:!0,video:i||u}})}}),this.call.on("participant-updated",e=>{e&&this.emit("daily-participant-updated",e.participant)}),this.call.on("participant-left",e=>{e&&!function(e){let t=document.querySelector(`audio[data-participant-id="${e}"]`);t?.remove()}(e.participant.session_id)}),this.isMobileDevice()&&await this.sleep(1e3),await this.call.join({url:n.webCallUrl,subscribeToTracksAutomatically:!1}),i){let e=new Date().getTime();this.call.startRecording({width:1280,height:720,backgroundColor:"#FF1F2D3D",layout:{preset:"default"}}),this.call.on("recording-started",()=>{this.send({type:"control",control:"say-first-message",videoRecordingStartDelaySeconds:(new Date().getTime()-e)/1e3})})}return this.call.startRemoteParticipantsAudioLevelObserver(100),this.call.on("remote-participants-audio-level",e=>{e&&this.handleRemoteParticipantsAudioLevel(e)}),this.call.on("app-message",e=>this.onAppMessage(e)),this.call.on("nonfatal-error",e=>{e?.type==="audio-processor-error"&&this.call?.updateInputSettings({audio:{processor:{type:"none"}}}).then(()=>{this.call?.setLocalAudio(!0)})}),this.call.updateInputSettings({audio:{processor:{type:"noise-cancellation"}}}),n}catch(e){return console.error(e),this.emit("error",e),this.cleanup(),null}}onAppMessage(e){if(e)try{if("listening"===e.data)return this.emit("call-start");try{let t=JSON.parse(e.data);this.emit("message",t),t&&"type"in t&&"status"in t&&"status-update"===t.type&&"ended"===t.status&&(this.hasEmittedCallEndedStatus=!0)}catch(e){console.log("Error parsing message data: ",e)}}catch(e){console.error(e)}}handleRemoteParticipantsAudioLevel(e){let t=Object.values(e.participantsAudioLevel).reduce((e,t)=>e+t,0);this.emit("volume-level",Math.min(1,t/.15)),t>.01&&(this.speakingTimeout?(clearTimeout(this.speakingTimeout),this.speakingTimeout=null):this.emit("speech-start"),this.speakingTimeout=setTimeout(()=>{this.emit("speech-end"),this.speakingTimeout=null},1e3))}stop(){this.started=!1,this.call?.destroy(),this.call=null}send(e){this.call?.sendAppMessage(JSON.stringify(e))}setMuted(e){if(!this.call)throw Error("Call object is not available.");this.call.setLocalAudio(!e)}isMuted(){return!!this.call&&!1===this.call.localAudio()}say(e,t,r){this.send({type:"say",message:e,endCallAfterSpoken:t,interruptionsEnabled:r??!1})}setInputDevicesAsync(e){this.call?.setInputDevicesAsync(e)}async increaseMicLevel(e){if(!this.call)throw Error("Call object is not available.");try{let t=await navigator.mediaDevices.getUserMedia({audio:!0}),r=new AudioContext,o=r.createMediaStreamSource(t),s=r.createGain();s.gain.value=e,o.connect(s);let n=r.createMediaStreamDestination();s.connect(n);let[a]=n.stream.getAudioTracks();await this.call.setInputDevicesAsync({audioSource:a})}catch(e){console.error("Error adjusting microphone level:",e)}}setOutputDeviceAsync(e){this.call?.setOutputDeviceAsync(e)}getDailyCallObject(){return this.call}startScreenSharing(e,t){this.call?.startScreenShare({displayMediaOptions:e,screenVideoSendSettings:t})}stopScreenSharing(){this.call?.stopScreenShare()}}t.default=h},44020:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(19946).A)("EllipsisVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]])},47924:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},51154:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(19946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54301:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.Api=t.HttpClient=t.ContentType=void 0,function(e){e.Json="application/json",e.FormData="multipart/form-data",e.UrlEncoded="application/x-www-form-urlencoded",e.Text="text/plain"}(r||(t.ContentType=r={}));class o{baseUrl="https://api.vapi.ai";securityData=null;securityWorker;abortControllers=new Map;customFetch=(...e)=>fetch(...e);baseApiParams={credentials:"same-origin",headers:{},redirect:"follow",referrerPolicy:"no-referrer"};constructor(e={}){Object.assign(this,e)}setSecurityData=e=>{this.securityData=e};encodeQueryParam(e,t){let r=encodeURIComponent(e);return`${r}=${encodeURIComponent("number"==typeof t?t:`${t}`)}`}addQueryParam(e,t){return this.encodeQueryParam(t,e[t])}addArrayQueryParam(e,t){return e[t].map(e=>this.encodeQueryParam(t,e)).join("&")}toQueryString(e){let t=e||{};return Object.keys(t).filter(e=>void 0!==t[e]).map(e=>Array.isArray(t[e])?this.addArrayQueryParam(t,e):this.addQueryParam(t,e)).join("&")}addQueryParams(e){let t=this.toQueryString(e);return t?`?${t}`:""}contentFormatters={[r.Json]:e=>null!==e&&("object"==typeof e||"string"==typeof e)?JSON.stringify(e):e,[r.Text]:e=>null!==e&&"string"!=typeof e?JSON.stringify(e):e,[r.FormData]:e=>Object.keys(e||{}).reduce((t,r)=>{let o=e[r];return t.append(r,o instanceof Blob?o:"object"==typeof o&&null!==o?JSON.stringify(o):`${o}`),t},new FormData),[r.UrlEncoded]:e=>this.toQueryString(e)};mergeRequestParams(e,t){return{...this.baseApiParams,...e,...t||{},headers:{...this.baseApiParams.headers||{},...e.headers||{},...t&&t.headers||{}}}}createAbortSignal=e=>{if(this.abortControllers.has(e)){let t=this.abortControllers.get(e);return t?t.signal:void 0}let t=new AbortController;return this.abortControllers.set(e,t),t.signal};abortRequest=e=>{let t=this.abortControllers.get(e);t&&(t.abort(),this.abortControllers.delete(e))};request=async({body:e,secure:t,path:o,type:s,query:n,format:a,baseUrl:i,cancelToken:l,...u})=>{let h=("boolean"==typeof t?t:this.baseApiParams.secure)&&this.securityWorker&&await this.securityWorker(this.securityData)||{},d=this.mergeRequestParams(u,h),c=n&&this.toQueryString(n),p=this.contentFormatters[s||r.Json],m=a||d.format;return this.customFetch(`${i||this.baseUrl||""}${o}${c?`?${c}`:""}`,{...d,headers:{...d.headers||{},...s&&s!==r.FormData?{"Content-Type":s}:{}},signal:(l?this.createAbortSignal(l):d.signal)||null,body:null==e?null:p(e)}).then(async e=>{e.data=null,e.error=null;let t=m?await e[m]().then(t=>(e.ok?e.data=t:e.error=t,e)).catch(t=>(e.error=t,e)):e;if(l&&this.abortControllers.delete(l),!e.ok)throw t;return t})}}t.HttpClient=o;class s extends o{call={callControllerCreate:(e,t={})=>this.request({path:"/call",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),callControllerFindAll:(e,t={})=>this.request({path:"/call",method:"GET",query:e,secure:!0,format:"json",...t}),callControllerFindOne:(e,t={})=>this.request({path:`/call/${e}`,method:"GET",secure:!0,format:"json",...t}),callControllerUpdate:(e,t,o={})=>this.request({path:`/call/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...o}),callControllerDeleteCallData:(e,t={})=>this.request({path:`/call/${e}`,method:"DELETE",secure:!0,format:"json",...t}),callControllerCreatePhoneCall:(e,t={})=>this.request({path:"/call/phone",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),callControllerCreateWebCall:(e,t={})=>this.request({path:"/call/web",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t})};v2={callControllerExportCalls:(e,t={})=>this.request({path:"/v2/call/export",method:"GET",query:e,secure:!0,...t}),callControllerFindAllPaginated:(e,t={})=>this.request({path:"/v2/call",method:"GET",query:e,secure:!0,format:"json",...t}),callControllerFindAllMetadataPaginated:(e,t={})=>this.request({path:"/v2/call/metadata",method:"GET",query:e,secure:!0,format:"json",...t}),assistantControllerFindAllPaginated:(e,t={})=>this.request({path:"/v2/assistant",method:"GET",query:e,secure:!0,format:"json",...t}),phoneNumberControllerFindAllPaginated:(e,t={})=>this.request({path:"/v2/phone-number",method:"GET",query:e,secure:!0,format:"json",...t})};chat={chatControllerListChats:(e,t={})=>this.request({path:"/chat",method:"GET",query:e,secure:!0,format:"json",...t}),chatControllerCreateChat:(e,t={})=>this.request({path:"/chat",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),chatControllerGetChat:(e,t={})=>this.request({path:`/chat/${e}`,method:"GET",secure:!0,format:"json",...t}),chatControllerDeleteChat:(e,t={})=>this.request({path:`/chat/${e}`,method:"DELETE",secure:!0,format:"json",...t}),chatControllerCreateOpenAiChat:(e,t={})=>this.request({path:"/chat/responses",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t})};session={sessionControllerCreate:(e,t={})=>this.request({path:"/session",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),sessionControllerFindAllPaginated:(e,t={})=>this.request({path:"/session",method:"GET",query:e,secure:!0,format:"json",...t}),sessionControllerFindOne:(e,t={})=>this.request({path:`/session/${e}`,method:"GET",secure:!0,format:"json",...t}),sessionControllerUpdate:(e,t,o={})=>this.request({path:`/session/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...o}),sessionControllerRemove:(e,t={})=>this.request({path:`/session/${e}`,method:"DELETE",secure:!0,format:"json",...t})};assistant={assistantControllerCreate:(e,t={})=>this.request({path:"/assistant",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),assistantControllerFindAll:(e,t={})=>this.request({path:"/assistant",method:"GET",query:e,secure:!0,format:"json",...t}),assistantControllerFindOne:(e,t={})=>this.request({path:`/assistant/${e}`,method:"GET",secure:!0,format:"json",...t}),assistantControllerUpdate:(e,t,o={})=>this.request({path:`/assistant/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...o}),assistantControllerReplace:(e,t,o={})=>this.request({path:`/assistant/${e}`,method:"PUT",body:t,secure:!0,type:r.Json,format:"json",...o}),assistantControllerRemove:(e,t={})=>this.request({path:`/assistant/${e}`,method:"DELETE",secure:!0,format:"json",...t}),assistantControllerFindVersions:(e,t,r={})=>this.request({path:`/assistant/${e}/version`,method:"GET",query:t,secure:!0,format:"json",...r})};phoneNumber={phoneNumberControllerImportTwilio:(e,t={})=>this.request({path:"/phone-number/import/twilio",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),phoneNumberControllerImportVonage:(e,t={})=>this.request({path:"/phone-number/import/vonage",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),phoneNumberControllerCreate:(e,t={})=>this.request({path:"/phone-number",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),phoneNumberControllerFindAll:(e,t={})=>this.request({path:"/phone-number",method:"GET",query:e,secure:!0,format:"json",...t}),phoneNumberControllerFindOne:(e,t={})=>this.request({path:`/phone-number/${e}`,method:"GET",secure:!0,format:"json",...t}),phoneNumberControllerUpdate:(e,t,o={})=>this.request({path:`/phone-number/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...o}),phoneNumberControllerRemove:(e,t={})=>this.request({path:`/phone-number/${e}`,method:"DELETE",secure:!0,format:"json",...t})};tool={toolControllerCreate:(e,t={})=>this.request({path:"/tool",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),toolControllerFindAll:(e,t={})=>this.request({path:"/tool",method:"GET",query:e,secure:!0,format:"json",...t}),toolControllerFindOne:(e,t={})=>this.request({path:`/tool/${e}`,method:"GET",secure:!0,format:"json",...t}),toolControllerUpdate:(e,t,o={})=>this.request({path:`/tool/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...o}),toolControllerRemove:(e,t={})=>this.request({path:`/tool/${e}`,method:"DELETE",secure:!0,format:"json",...t})};file={fileControllerCreateDeprecated:(e,t={})=>this.request({path:"/file/upload",method:"POST",body:e,secure:!0,type:r.FormData,format:"json",...t}),fileControllerCreate:(e,t={})=>this.request({path:"/file",method:"POST",body:e,secure:!0,type:r.FormData,format:"json",...t}),fileControllerFindAll:(e={})=>this.request({path:"/file",method:"GET",secure:!0,format:"json",...e}),fileControllerFindOne:(e,t={})=>this.request({path:`/file/${e}`,method:"GET",secure:!0,format:"json",...t}),fileControllerUpdate:(e,t,o={})=>this.request({path:`/file/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...o}),fileControllerRemove:(e,t={})=>this.request({path:`/file/${e}`,method:"DELETE",secure:!0,format:"json",...t})};knowledgeBase={knowledgeBaseControllerCreate:(e,t={})=>this.request({path:"/knowledge-base",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),knowledgeBaseControllerFindAll:(e,t={})=>this.request({path:"/knowledge-base",method:"GET",query:e,secure:!0,format:"json",...t}),knowledgeBaseControllerFindOne:(e,t={})=>this.request({path:`/knowledge-base/${e}`,method:"GET",secure:!0,format:"json",...t}),knowledgeBaseControllerUpdate:(e,t,o={})=>this.request({path:`/knowledge-base/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...o}),knowledgeBaseControllerRemove:(e,t={})=>this.request({path:`/knowledge-base/${e}`,method:"DELETE",secure:!0,format:"json",...t})};workflow={workflowControllerFindAll:(e={})=>this.request({path:"/workflow",method:"GET",secure:!0,format:"json",...e}),workflowControllerCreate:(e,t={})=>this.request({path:"/workflow",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),workflowControllerFindOne:(e,t={})=>this.request({path:`/workflow/${e}`,method:"GET",secure:!0,format:"json",...t}),workflowControllerDelete:(e,t={})=>this.request({path:`/workflow/${e}`,method:"DELETE",secure:!0,format:"json",...t}),workflowControllerUpdate:(e,t,o={})=>this.request({path:`/workflow/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...o})};squad={squadControllerCreate:(e,t={})=>this.request({path:"/squad",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),squadControllerFindAll:(e,t={})=>this.request({path:"/squad",method:"GET",query:e,secure:!0,format:"json",...t}),squadControllerFindOne:(e,t={})=>this.request({path:`/squad/${e}`,method:"GET",secure:!0,format:"json",...t}),squadControllerUpdate:(e,t,o={})=>this.request({path:`/squad/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...o}),squadControllerRemove:(e,t={})=>this.request({path:`/squad/${e}`,method:"DELETE",secure:!0,format:"json",...t})};testSuite={testSuiteControllerFindAllPaginated:(e,t={})=>this.request({path:"/test-suite",method:"GET",query:e,secure:!0,format:"json",...t}),testSuiteControllerCreate:(e,t={})=>this.request({path:"/test-suite",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),testSuiteControllerFindOne:(e,t={})=>this.request({path:`/test-suite/${e}`,method:"GET",secure:!0,format:"json",...t}),testSuiteControllerUpdate:(e,t,o={})=>this.request({path:`/test-suite/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...o}),testSuiteControllerRemove:(e,t={})=>this.request({path:`/test-suite/${e}`,method:"DELETE",secure:!0,format:"json",...t}),testSuiteTestControllerFindAllPaginated:(e,t,r={})=>this.request({path:`/test-suite/${e}/test`,method:"GET",query:t,secure:!0,format:"json",...r}),testSuiteTestControllerCreate:(e,t,o={})=>this.request({path:`/test-suite/${e}/test`,method:"POST",body:t,secure:!0,type:r.Json,format:"json",...o}),testSuiteTestControllerFindOne:(e,t,r={})=>this.request({path:`/test-suite/${e}/test/${t}`,method:"GET",secure:!0,format:"json",...r}),testSuiteTestControllerUpdate:(e,t,o,s={})=>this.request({path:`/test-suite/${e}/test/${t}`,method:"PATCH",body:o,secure:!0,type:r.Json,format:"json",...s}),testSuiteTestControllerRemove:(e,t,r={})=>this.request({path:`/test-suite/${e}/test/${t}`,method:"DELETE",secure:!0,format:"json",...r}),testSuiteRunControllerFindAllPaginated:(e,t,r={})=>this.request({path:`/test-suite/${e}/run`,method:"GET",query:t,secure:!0,format:"json",...r}),testSuiteRunControllerCreate:(e,t,o={})=>this.request({path:`/test-suite/${e}/run`,method:"POST",body:t,secure:!0,type:r.Json,format:"json",...o}),testSuiteRunControllerFindOne:(e,t,r={})=>this.request({path:`/test-suite/${e}/run/${t}`,method:"GET",secure:!0,format:"json",...r}),testSuiteRunControllerUpdate:(e,t,o,s={})=>this.request({path:`/test-suite/${e}/run/${t}`,method:"PATCH",body:o,secure:!0,type:r.Json,format:"json",...s}),testSuiteRunControllerRemove:(e,t,r={})=>this.request({path:`/test-suite/${e}/run/${t}`,method:"DELETE",secure:!0,format:"json",...r})};metrics={analyticsControllerFindAllDeprecated:(e,t={})=>this.request({path:"/metrics",method:"GET",query:e,secure:!0,format:"json",...t})};analytics={analyticsControllerQuery:(e,t={})=>this.request({path:"/analytics",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t})};log={loggingControllerCallLogsQuery:(e,t={})=>this.request({path:"/log",method:"GET",query:e,secure:!0,format:"json",...t}),loggingControllerCallLogsDeleteQuery:(e,t={})=>this.request({path:"/log",method:"DELETE",query:e,secure:!0,...t})};logs={loggingControllerLogsQuery:(e,t={})=>this.request({path:"/logs",method:"GET",query:e,secure:!0,format:"json",...t}),loggingControllerLogsDeleteQuery:(e,t={})=>this.request({path:"/logs",method:"DELETE",query:e,secure:!0,...t})};org={orgControllerCreate:(e,t={})=>this.request({path:"/org",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),orgControllerFindAll:(e={})=>this.request({path:"/org",method:"GET",secure:!0,format:"json",...e}),orgControllerFindOne:(e,t={})=>this.request({path:`/org/${e}`,method:"GET",secure:!0,format:"json",...t}),orgControllerUpdate:(e,t,o={})=>this.request({path:`/org/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...o}),orgControllerDeleteOrg:(e,t={})=>this.request({path:`/org/${e}`,method:"DELETE",secure:!0,...t}),orgControllerFindAllUsers:(e,t={})=>this.request({path:`/org/${e}/user`,method:"GET",secure:!0,format:"json",...t}),orgControllerOrgLeave:(e,t={})=>this.request({path:`/org/${e}/leave`,method:"DELETE",secure:!0,...t}),orgControllerOrgRemoveUser:(e,t,r={})=>this.request({path:`/org/${e}/member/${t}/leave`,method:"DELETE",secure:!0,...r}),orgControllerUsersInvite:(e,t,o={})=>this.request({path:`/org/${e}/invite`,method:"POST",body:t,secure:!0,type:r.Json,...o}),orgControllerUserUpdate:(e,t,o={})=>this.request({path:`/org/${e}/role`,method:"PATCH",body:t,secure:!0,type:r.Json,...o}),orgControllerOrgToken:(e,t={})=>this.request({path:`/org/${e}/auth`,method:"GET",secure:!0,format:"json",...t})};token={tokenControllerCreate:(e,t={})=>this.request({path:"/token",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),tokenControllerFindAll:(e,t={})=>this.request({path:"/token",method:"GET",query:e,secure:!0,format:"json",...t}),tokenControllerFindOne:(e,t={})=>this.request({path:`/token/${e}`,method:"GET",secure:!0,format:"json",...t}),tokenControllerUpdate:(e,t,o={})=>this.request({path:`/token/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...o}),tokenControllerRemove:(e,t={})=>this.request({path:`/token/${e}`,method:"DELETE",secure:!0,format:"json",...t})};credential={credentialControllerCreate:(e,t={})=>this.request({path:"/credential",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),credentialControllerFindAll:(e,t={})=>this.request({path:"/credential",method:"GET",query:e,secure:!0,format:"json",...t}),credentialControllerFindOne:(e,t={})=>this.request({path:`/credential/${e}`,method:"GET",secure:!0,format:"json",...t}),credentialControllerUpdate:(e,t,o={})=>this.request({path:`/credential/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...o}),credentialControllerRemove:(e,t={})=>this.request({path:`/credential/${e}`,method:"DELETE",secure:!0,format:"json",...t}),credentialControllerGenerateSession:(e,t={})=>this.request({path:"/credential/session",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),credentialControllerHandleWebhook:(e,t={})=>this.request({path:"/credential/webhook",method:"POST",body:e,type:r.Json,...t}),credentialControllerTriggerCredentialAction:(e,t={})=>this.request({path:"/credential/trigger",method:"POST",body:e,secure:!0,type:r.Json,...t})};template={templateControllerCreate:(e,t={})=>this.request({path:"/template",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),templateControllerFindAll:(e,t={})=>this.request({path:"/template",method:"GET",query:e,secure:!0,format:"json",...t}),templateControllerFindAllPinned:(e={})=>this.request({path:"/template/pinned",method:"GET",secure:!0,format:"json",...e}),templateControllerFindOne:(e,t={})=>this.request({path:`/template/${e}`,method:"GET",secure:!0,format:"json",...t}),templateControllerUpdate:(e,t,o={})=>this.request({path:`/template/${e}`,method:"PATCH",body:t,secure:!0,type:r.Json,format:"json",...o}),templateControllerRemove:(e,t={})=>this.request({path:`/template/${e}`,method:"DELETE",secure:!0,format:"json",...t})};voiceLibrary={voiceLibraryControllerVoiceGetByProvider:(e,t,r={})=>this.request({path:`/voice-library/${e}`,method:"GET",query:t,secure:!0,format:"json",...r}),voiceLibraryControllerVoiceGetAccentsByProvider:(e,t={})=>this.request({path:`/voice-library/${e}/accents`,method:"GET",secure:!0,format:"json",...t}),voiceLibraryControllerVoiceLibrarySyncByProvider:(e,t={})=>this.request({path:`/voice-library/sync/${e}`,method:"POST",secure:!0,format:"json",...t}),voiceLibraryControllerVoiceLibrarySyncDefaultVoices:(e,t={})=>this.request({path:"/voice-library/sync",method:"POST",body:e,secure:!0,type:r.Json,format:"json",...t}),voiceLibraryControllerVoiceLibraryCreateSesameVoice:(e,t={})=>this.request({path:"/voice-library/create-sesame-voice",method:"POST",body:e,secure:!0,type:r.Json,...t})};provider={providerControllerGetWorkflows:(e,t,r={})=>this.request({path:`/${e}/workflows`,method:"GET",query:t,secure:!0,format:"json",...r}),providerControllerGetWorkflowTriggerHook:(e,t,r={})=>this.request({path:`/${e}/workflows/${t}/hooks`,method:"GET",secure:!0,format:"json",...r}),providerControllerGetLocations:(e,t={})=>this.request({path:`/${e}/locations`,method:"GET",secure:!0,format:"json",...t}),voiceProviderControllerSearchVoices:(e,t,r={})=>this.request({path:`/${e}/voices/search`,method:"GET",query:t,secure:!0,format:"json",...r}),voiceProviderControllerSearchVoice:(e,t,r={})=>this.request({path:`/${e}/voice/search`,method:"GET",query:t,secure:!0,format:"json",...r}),voiceProviderControllerAddVoices:(e,t,o={})=>this.request({path:`/${e}/voices/add`,method:"POST",body:t,secure:!0,type:r.Json,format:"json",...o}),voiceProviderControllerAddVoice:(e,t,o={})=>this.request({path:`/${e}/voice/add`,method:"POST",body:t,secure:!0,type:r.Json,format:"json",...o})};v11Labs={voiceProviderControllerCloneVoices:(e,t={})=>this.request({path:"/11labs/voice/clone",method:"POST",body:e,secure:!0,type:r.FormData,...t})}}t.Api=s},62525:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(19946).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},84616:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(19946).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},89917:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(19946).A)("Pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},91951:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(19946).A)("MicOff",[["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}],["path",{d:"M18.89 13.23A7.12 7.12 0 0 0 19 12v-2",key:"80xlxr"}],["path",{d:"M5 10v2a7 7 0 0 0 12 5",key:"p2k8kg"}],["path",{d:"M15 9.34V5a3 3 0 0 0-5.68-1.33",key:"1gzdoj"}],["path",{d:"M9 9v3a3 3 0 0 0 5.12 2.12",key:"r2i35w"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]])},97207:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(19946).A)("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]])}}]);