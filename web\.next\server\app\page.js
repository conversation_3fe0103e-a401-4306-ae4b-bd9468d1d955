(()=>{var e={};e.id=8974,e.ids=[8974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12228:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>c,tree:()=>p});var s=t(65239),o=t(48088),n=t(88170),a=t.n(n),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let p=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,21204)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}],l=["C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21204:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},50880:(e,r,t)=>{Promise.resolve().then(t.bind(t,75694))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75694:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(60687),o=t(92774),n=t(16189),a=t(43210);function i(){(0,n.useRouter)();let[e,r]=(0,a.useState)(!0),[t,i]=(0,a.useState)(!1);return e?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,s.jsx)("div",{className:"h-12 w-12 border-4 border-t-blue-500 border-gray-200 rounded-full animate-spin"}),(0,s.jsx)("p",{className:"text-lg font-medium",children:"Loading..."})]})}):(0,s.jsx)(s.Fragment,{children:(0,s.jsx)(o.default,{})})}t(6607)},80384:(e,r,t)=>{Promise.resolve().then(t.bind(t,21204))}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[287,9176,7674,598,544,1476,2774],()=>t(12228));module.exports=s})();