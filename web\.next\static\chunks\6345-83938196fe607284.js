(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6345],{7342:(e,t,s)=>{"use strict";s.d(t,{default:()=>U});var a=s(95155),l=s(12115),r=s(62523),n=s(17313),i=s(30285),d=s(35169),c=s(47924),o=s(1482),m=s(44020),x=s(62525),u=s(40133),h=s(82178),f=s(85690),v=s(89114),j=s(91788),N=s(85339),g=s(26126),p=s(91394),w=s(4672),b=s(38009),y=s(14853),P=s(35695),S=s(62829),k=s(44838),A=s(90010),C=s(12421),T=function(e){return e.Positive="Positive",e.Neutral="Neutral",e.SlightlyPositive="Slightly Positive",e.SlightlyNegative="Slightly Negative",e.Negative="Negative",e}(T||{});let E="http://localhost:4000";async function R(){let e=localStorage.getItem("access_token");if(!e)throw Error("No access token available");let t=await fetch("".concat(E,"/api/agents"),{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error("Failed to fetch agents: ".concat(t.status));return t.json()}async function L(e){try{let t=localStorage.getItem("access_token");if(!t)return console.error("No access token available"),!1;let s=await fetch("".concat(E,"/api/history/").concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(t)}});if(!s.ok)throw Error("Failed to delete call: ".concat(s.status));return!0}catch(e){return console.error("Error deleting call:",e),!1}}function z(e){let t=Math.floor(e/60),s=Math.floor(e%60);return"".concat(t.toString().padStart(2,"0"),":").concat(s.toString().padStart(2,"0"))}function _(e){if(!e)return"N/A";let t=new Date(e);return isNaN(t.getTime())?"N/A":t.toLocaleString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric"})}let B={Positive:{emoji:"\uD83D\uDE0A",color:"bg-green-500"},Neutral:{emoji:"\uD83D\uDE10",color:"bg-blue-500"},"Slightly Positive":{emoji:"\uD83D\uDE42",color:"bg-cyan-500"},"Slightly Negative":{emoji:"\uD83D\uDE15",color:"bg-slate-500"},Negative:{emoji:"\uD83D\uDE1F",color:"bg-red-500"}},I=e=>B[e]||B.Neutral;function U(e){var t;let{contactName:s}=e,T=(0,P.useParams)(),B=s||(null==T?void 0:T.fullName),U=(0,P.useRouter)(),D=B?decodeURIComponent(B):"",[F,M]=(0,l.useState)(""),{credits:O,organizationCreditThreshold:V}=(0,w.I)(),[q,W]=(0,l.useState)([]),[Z,$]=(0,l.useState)([]),[H,X]=(0,l.useState)(null),[Q,G]=(0,l.useState)(0),[K,Y]=(0,l.useState)("overview"),[J,ee]=(0,l.useState)(D||""),[et,es]=(0,l.useState)(0),[ea,el]=(0,l.useState)(null),[er,en]=(0,l.useState)(!0),[ei,ed]=(0,l.useState)(null),ec=(0,l.useRef)(null),[eo,em]=(0,l.useState)(!1),[ex,eu]=(0,l.useState)("all"),[eh,ef]=(0,l.useState)(1),[ev,ej]=(0,l.useState)(!0),[eN,eg]=(0,l.useState)(!1),ep=(0,l.useRef)(null),[ew,eb]=(0,l.useState)(!1),[ey,eP]=(0,l.useState)(null),[eS,ek]=(0,l.useState)(!1),[eA,eC]=(0,l.useState)(!0);async function eT(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let s=localStorage.getItem("access_token");if(!s)return console.error("No access token available"),[];t||en(!0),t&&eg(!0);let a=new URLSearchParams;a.append("page",e.toString()),a.append("limit","20"),J.trim()&&(a.append("search",J.trim()),a.append("filterType",ex));let l=await fetch("".concat(E,"/api/history?").concat(a.toString()),{headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(s)}});if(!l.ok)throw Error("Failed to fetch calls: ".concat(l.status));let r=await l.json();if(!Array.isArray(r))throw console.error("Unexpected response format from API:",r),Error("Invalid response format from API");return t?W(e=>{let t=new Set(e.map(e=>e._id)),s=r.filter(e=>!t.has(e._id));return[...e,...s]}):W(r),ej(20===r.length),r}catch(e){return console.error("Error fetching calls:",e),[]}finally{t||en(!1),t&&eg(!1)}}(0,l.useEffect)(()=>{(async function(){try{let e=await (0,C.t)("".concat(E,"/api/auth/me")),t=await e.json();M(t.role)}catch(e){console.error("Error fetching user data:",e)}})()},[]),(0,l.useEffect)(()=>{let e=new IntersectionObserver(e=>{e[0].isIntersecting&&ev&&!er&&!eN&&ef(e=>{let t=e+1;return eT(t,!0),t})},{threshold:.1}),t=ep.current;return t&&e.observe(t),()=>{t&&e.unobserve(t)}},[ev,eN,er]),(0,l.useEffect)(()=>{!async function(){en(!0);try{let e=await R();$(e),ef(1),await eT(1,!1)}finally{en(!1)}}()},[]);let[eE,eR]=(0,l.useState)(!1),eL=async()=>{if(ey)try{if(await L(ey._id)){let e=await eT();W(e),eb(!1),eP(null),(null==H?void 0:H._id)===ey._id&&X(null)}}catch(e){console.error("Error in delete handler:",e)}};(0,l.useEffect)(()=>{ec.current&&(ec.current.pause(),ec.current=null),em(!1),es(0),H&&e_(H)&&Y("overview")},[H]),(0,l.useEffect)(()=>{let e=()=>{let e=window.innerWidth<768;ek(e),e&&H?eC(!1):eC(!0)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[H]);let ez=e=>parseInt(e.callDuration)/1e3,e_=e=>"voicemail"===e.callEndReason||"customer-did-not-answer"===e.callEndReason||"customer-busy"===e.callEndReason||"customer-out-of-reach"===e.callEndReason;[...q].sort((e,t)=>{let s=new Date(e.callStartTime).getTime();return new Date(t.callStartTime).getTime()-s});let eB=B?"Calls for ".concat(D):"Recent Calls";function eI(e){if(!e)return null;let t=e.toLowerCase().trim(),s=Z.find(e=>e.name.toLowerCase().trim()===t||t.includes(e.name.toLowerCase().trim())||e.name.toLowerCase().trim().includes(t));return s&&s.avatar?s.avatar:S.A.src}return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(y.O,{credits:O,threshold:V}),(0,a.jsx)(b.m,{credits:O,threshold:V}),(0,a.jsxs)("div",{className:"flex h-[calc(100vh-60px)] w-full overflow-hidden overflow-x-auto bg-background",children:[(0,a.jsxs)("div",{className:"w-80 border-r border-border flex flex-col h-full dark:bg-card",children:[(0,a.jsxs)("div",{className:"p-4 border-b border-border",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[B&&(0,a.jsx)(i.$,{variant:"ghost",size:"icon",onClick:()=>U.back(),className:"mr-1",children:(0,a.jsx)(d.A,{className:"h-4 w-4"})}),(0,a.jsx)("h2",{className:"text-lg font-semibold",children:eB})]}),q.length>0&&!er?(0,a.jsx)(g.E,{className:"rounded-full bg-primary/10 hover:bg-primary/10 text-primary",children:J.trim()&&q[0].filteredCalls?"".concat(q.length," of ").concat(q[0].filteredCalls," results"):"".concat(q.length," of ").concat(q[0].totalCalls)}):null]}),(0,a.jsxs)("div",{className:"relative mt-3 flex gap-2",children:[(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),ef(1),eT(1)},className:"relative flex-1",children:[(0,a.jsx)(c.A,{className:"absolute left-2.5 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,a.jsx)(r.p,{placeholder:"agent"===ex?"Filter by agent...":"Filter by name...",className:"pl-9 h-9 bg-background dark:bg-muted",value:J,onChange:e=>ee(e.target.value)})]}),(0,a.jsxs)(k.rI,{children:[(0,a.jsx)(k.ty,{asChild:!0,children:(0,a.jsx)(i.$,{variant:"ghost",size:"icon",className:"h-9 w-9",children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})}),(0,a.jsxs)(k.SQ,{align:"end",children:[(0,a.jsx)(k._2,{className:"all"===ex?"bg-muted":"",onClick:()=>eu("all"),children:"All Fields"}),(0,a.jsx)(k._2,{className:"name"===ex?"bg-muted":"",onClick:()=>eu("name"),children:"Filter by Name"}),(0,a.jsx)(k._2,{className:"agent"===ex?"bg-muted":"",onClick:()=>eu("agent"),children:"Filter by Agent"})]})]})]})]}),(0,a.jsxs)("div",{className:"overflow-y-auto flex-1",children:[er?(0,a.jsx)("div",{className:"flex justify-center items-center h-24",children:(0,a.jsx)("div",{className:"animate-spin h-5 w-5 border-2 border-primary border-t-transparent rounded-full"})}):q.map((e,t)=>(0,a.jsx)("div",{className:"w-full text-left px-4 py-3 border-b border-border hover:bg-muted/50 cursor-pointer transition-colors ".concat((null==H?void 0:H._id)===e._id?"bg-muted/50":""),onClick:()=>X(e),children:(0,a.jsxs)("div",{className:"flex items-center gap-2 mb-1",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)(p.eu,{className:"h-8 w-8",children:eI(e.agent)?(0,a.jsx)("div",{className:"relative h-full w-full rounded-full overflow-hidden",children:(0,a.jsx)(p.BK,{src:eI(e.agent),alt:"Agent",className:"object-cover"})}):(0,a.jsx)(p.q5,{className:"bg-muted text-muted-foreground",children:(0,a.jsxs)("svg",{className:"h-6 w-6",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 122.88 119.35",children:[(0,a.jsx)("title",{children:"chatbot"}),(0,a.jsx)("path",{d:"M57.49,29.2V23.53a14.41,14.41,0,0,1-2-.93A12.18,12.18,0,0,1,50.44,7.5a12.39,12.39,0,0,1,2.64-3.95A12.21,12.21,0,0,1,57,.92,12,12,0,0,1,61.66,0,12.14,12.14,0,0,1,72.88,7.5a12.14,12.14,0,0,1,0,9.27,12.08,12.08,0,0,1-2.64,3.94l-.06.06a12.74,12.74,0,0,1-2.36,1.83,11.26,11.26,0,0,1-2,.93V29.2H94.3a15.47,15.47,0,0,1,15.42,15.43v2.29H115a7.93,7.93,0,0,1,7.9,7.91V73.2A7.93,7.93,0,0,1,115,81.11h-5.25v2.07A15.48,15.48,0,0,1,94.3,98.61H55.23L31.81,118.72a2.58,2.58,0,0,1-3.65-.29,2.63,2.63,0,0,1-.63-1.85l1.25-18h-.21A15.45,15.45,0,0,1,13.16,83.18V81.11H7.91A7.93,7.93,0,0,1,0,73.2V54.83a7.93,7.93,0,0,1,7.9-7.91h5.26v-2.3A15.45,15.45,0,0,1,28.57,29.2H57.49ZM82.74,47.32a9.36,9.36,0,1,1-9.36,9.36,9.36,9.36,0,0,1,9.36-9.36Zm-42.58,0a9.36,9.36,0,1,1-9.36,9.36,9.36,9.36,0,0,1,9.36-9.36Zm6.38,31.36a2.28,2.28,0,0,1-.38-.38,2.18,2.18,0,0,1-.52-1.36,2.21,2.21,0,0,1,.46-1.39,2.4,2.4,0,0,1,.39-.39,3.22,3.22,0,0,1,3.88-.08A22.36,22.36,0,0,0,56,78.32a14.86,14.86,0,0,0,5.47,1A16.18,16.18,0,0,0,67,78.22,25.39,25.39,0,0,0,72.75,75a3.24,3.24,0,0,1,3.89.18,3,3,0,0,1,.37.41,2.22,2.22,0,0,1,.42,1.4,2.33,2.33,0,0,1-.58,1.35,2.29,2.29,0,0,1-.43.38,30.59,30.59,0,0,1-7.33,4,22.28,22.28,0,0,1-7.53,1.43A21.22,21.22,0,0,1,54,82.87a27.78,27.78,0,0,1-7.41-4.16l0,0ZM94.29,34.4H28.57A10.26,10.26,0,0,0,18.35,44.63V83.18A10.26,10.26,0,0,0,28.57,93.41h3.17a2.61,2.61,0,0,1,2.41,2.77l-1,14.58L52.45,94.15a2.56,2.56,0,0,1,1.83-.75h40a10.26,10.26,0,0,0,10.22-10.23V44.62A10.24,10.24,0,0,0,94.29,34.4Z"})]})})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("p",{className:"text-sm font-medium truncate",children:e.fullName||"Unknown Agent"}),(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("span",{className:"text-xs text-muted-foreground",children:function(e){if(!e)return"N/A";let t=new Date(e);return isNaN(t.getTime())?"N/A":t.toLocaleDateString("en-US",{month:"short",day:"numeric"})}(e.callStartTime)}),"superadmin"===F&&(0,a.jsxs)(k.rI,{children:[(0,a.jsx)(k.ty,{asChild:!0,children:(0,a.jsx)(i.$,{variant:"ghost",size:"icon",className:"h-6 w-6 hover:bg-muted",children:(0,a.jsx)(m.A,{className:"h-4 w-4"})})}),(0,a.jsx)(k.SQ,{align:"end",children:(0,a.jsxs)(k._2,{className:"text-red-600",onClick:t=>{t.stopPropagation(),eP(e),eb(!0)},children:[(0,a.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Delete"]})})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs text-muted-foreground mt-0.5",children:[(0,a.jsx)("div",{className:"flex items-center",children:e_(e)?(0,a.jsx)(g.E,{className:"bg-red-500 text-white hover:bg-red-600 px-1 py-0 text-[10px]",children:"didn't pick"}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("svg",{className:"h-3 w-3 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),z(ez(e))]})}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("svg",{className:"h-3 w-3 mr-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),new Date(e.callStartTime).toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})]})]})]})]})},"".concat(e._id,"-").concat(t))),(0,a.jsx)("div",{ref:ep,className:"py-4 text-center",children:eN?(0,a.jsx)("div",{className:"flex justify-center items-center py-4",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary"})}):ev?(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"Scroll for more"}):(0,a.jsx)("span",{className:"text-sm text-muted-foreground",children:"No more calls"})}),ei&&(0,a.jsx)("div",{className:"p-4 text-sm text-amber-600",children:ei})]})]}),(0,a.jsx)("div",{className:"flex-1 min-w-[500px] flex flex-col overflow-hidden",children:H?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"p-4 border-b border-border",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(p.eu,{className:"h-10 w-10 mr-3",children:H.agent?(0,a.jsx)("div",{className:"relative h-full w-full rounded-full overflow-hidden",children:(0,a.jsx)(p.BK,{src:eI(H.agent),alt:H.agent||"Agent",className:"object-cover"})}):(0,a.jsx)(p.q5,{className:"bg-primary/10 text-primary",children:(null===(t=H.agent)||void 0===t?void 0:t.charAt(0))||"A"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-lg font-semibold",children:H.fullName||"Caller"}),(0,a.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Agent: ",H.agent||""]})]})]}),e_(H)?(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(g.E,{className:"bg-red-500 text-white px-2 py-1",children:"didn't pick"})}):(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(i.$,{variant:"ghost",size:"icon",className:"h-9 w-9",onClick:()=>{ec.current&&(ec.current.currentTime=0,es(0),eo&&ec.current.play())},children:(0,a.jsx)(u.A,{size:16})}),(0,a.jsx)(i.$,{variant:"ghost",size:"icon",className:"h-9 w-9",onClick:()=>{(null==H?void 0:H.recordingUrl)&&(ec.current||(ec.current=new Audio(H.recordingUrl),ec.current.addEventListener("timeupdate",()=>{if(ec.current){let e=ez(H);es(ec.current.currentTime/e*100)}}),ec.current.addEventListener("ended",()=>{em(!1),es(0)})),eo?(ec.current.pause(),em(!1)):(ec.current.play(),em(!0)))},children:eo?(0,a.jsx)(h.A,{size:16}):(0,a.jsx)(f.A,{size:16})}),(0,a.jsx)(i.$,{variant:"ghost",size:"icon",className:"h-9 w-9",onClick:()=>{ec.current&&(ec.current.currentTime=Math.min(ec.current.currentTime+10,ez(H)))},children:(0,a.jsx)(v.A,{size:16})}),(0,a.jsx)(i.$,{variant:"ghost",size:"icon",className:"h-9 w-9",onClick:()=>{(null==H?void 0:H.recordingUrl)&&window.open(H.recordingUrl,"_blank")},children:(0,a.jsx)(j.A,{size:16})})]})]}),!e_(H)&&(0,a.jsxs)("div",{className:"mt-6 flex items-center",children:[(0,a.jsxs)("div",{className:"relative w-full h-12 bg-muted rounded-md overflow-hidden cursor-pointer",onClick:e=>{if(!ec.current||!H)return;let t=e.currentTarget.getBoundingClientRect(),s=(e.clientX-t.left)/t.width,a=ez(H);ec.current.currentTime=a*s,es(100*s),!eo&&ec.current.paused&&(ec.current.play(),em(!0))},children:[(0,a.jsx)("div",{className:"absolute top-0 left-0 h-full bg-primary/20 pointer-events-none",style:{width:"".concat(et,"%")}}),(0,a.jsx)("div",{className:"absolute inset-0 flex items-center justify-between px-1 pointer-events-none",children:Array.from({length:80}).map((e,t)=>{let s=t*H._id.charCodeAt(t%H._id.length),l=30+25*Math.sin(.1*s)+20*Math.cos(.2*s);return(0,a.jsx)("div",{className:t<.8*et?"bg-primary/50":"bg-muted-foreground/20",style:{height:"".concat(Math.max(15,Math.min(85,l)),"%"),width:"1px"}},t)})}),(0,a.jsx)("div",{className:"absolute top-0 w-0.5 h-full bg-primary z-10 pointer-events-none",style:{left:"".concat(et,"%")}})]}),(0,a.jsx)("div",{className:"ml-2 text-sm text-foreground whitespace-nowrap",children:"".concat(z(ez(H)*et/100)," / ").concat(z(ez(H)))})]})]}),(0,a.jsxs)(n.tU,{value:K,onValueChange:Y,className:"flex-1 overflow-hidden",children:[(0,a.jsx)("div",{className:"border-b border-border",children:(0,a.jsxs)(n.j7,{className:"h-12 bg-transparent border-b-0 px-4 gap-6",children:[(0,a.jsx)(n.Xi,{value:"overview",className:"data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none h-12 rounded-none px-0 text-muted-foreground data-[state=active]:text-foreground",children:"Overview"}),!e_(H)&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.Xi,{value:"summary",className:"data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none h-12 rounded-none px-0 text-muted-foreground data-[state=active]:text-foreground",children:"Summary"}),(0,a.jsx)(n.Xi,{value:"transcript",className:"data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none h-12 rounded-none px-0 text-muted-foreground data-[state=active]:text-foreground",children:"Transcript"}),(0,a.jsx)(n.Xi,{value:"additionalInfo",className:"data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none h-12 rounded-none px-0 text-muted-foreground data-[state=active]:text-foreground",children:"Additional Info"})]})]})}),(0,a.jsxs)(n.av,{value:"overview",className:"flex-1 p-6 overflow-y-auto",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Information"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"Full Name"}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("span",{className:"text-sm",children:H.fullName||"Unknown"})})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"Agent"}),(0,a.jsx)("div",{className:"text-sm",children:H.agent||""})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"Mobile"}),(0,a.jsx)("div",{className:"text-sm",children:H.mobileNumber||"N/A"})]}),!e_(H)&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"Duration"}),(0,a.jsx)("div",{className:"text-sm",children:z(ez(H))})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"Started at"}),(0,a.jsx)("div",{className:"text-sm",children:_(H.callStartTime)})]}),!e_(H)&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"Ended at"}),(0,a.jsx)("div",{className:"text-sm",children:_(H.callEndTime)})]})]}),(0,a.jsx)("h3",{className:"text-lg font-medium mt-8 mb-4",children:"Analysis"}),(0,a.jsx)("div",{className:"space-y-4",children:e_(H)?(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"End call reason"}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(g.E,{className:"bg-red-500 text-white",children:H.callEndReason||"N/A"})})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"Call sentiment"}),(0,a.jsx)("div",{className:"flex items-center",children:(()=>{let e=I(H.emotions);return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"h-2 w-2 rounded-full ".concat(e.color," mr-2 mt-1")}),(0,a.jsxs)("span",{className:"text-sm flex items-center",children:[e.emoji," ",(0,a.jsx)("span",{className:"ml-1",children:H.emotions})]})]})})()})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"Call status"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"h-2 w-2 rounded-full bg-green-500 mr-2 mt-1"}),(0,a.jsx)("span",{className:"text-sm",children:"Completed"})]})]}),(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"w-32 text-sm font-medium text-muted-foreground",children:"End call reason"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"h-2 w-2 rounded-full bg-green-500 mr-2 mt-1"}),(0,a.jsx)("span",{className:"text-sm",children:H.callEndReason||"N/A"})]})]})]})})]}),(0,a.jsx)(n.av,{value:"summary",className:"flex-1 p-6 overflow-y-auto",children:(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsx)("h3",{className:"text-md font-medium mb-4 text-muted-foreground",children:"Summary"}),(0,a.jsx)("div",{className:"p-4 bg-muted/50 rounded-md",children:(0,a.jsx)("p",{children:H.callSummary?H.callSummary:"No summary available."})})]})}),(0,a.jsxs)(n.av,{value:"transcript",className:"flex-1 overflow-hidden flex flex-col",children:[(0,a.jsx)("div",{className:"p-4 border-b border-border",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),(0,a.jsx)(r.p,{placeholder:"Search",className:"pl-10 pr-10 bg-background dark:bg-card"})]})}),(0,a.jsx)("div",{className:"flex-1 overflow-y-auto p-4",children:(0,a.jsx)("div",{className:"space-y-6",children:H.callTranscript?H.callTranscript.split("\n").map((e,t)=>{if(e.startsWith("AI:")||e.startsWith("User:")){var s,l;let r=e.startsWith("User:"),n=e.substring(e.indexOf(":")+1).trim();return(0,a.jsxs)("div",{className:"flex ".concat(r?"justify-end":""),children:[!r&&(0,a.jsx)("div",{className:"flex-shrink-0 mr-3",children:(0,a.jsx)(p.eu,{className:"h-8 w-8",children:(0,a.jsx)(p.q5,{className:"bg-primary/10 text-primary",children:(null===(s=H.agent)||void 0===s?void 0:s.charAt(0))||"A"})})}),(0,a.jsxs)("div",{className:"max-w-[75%] ".concat(r?"ml-auto":"mr-auto"),children:[(0,a.jsx)("div",{className:"text-xs text-muted-foreground mb-1",children:r?"You":H.agent}),(0,a.jsx)("div",{className:"text-sm p-3 rounded-lg ".concat(r?"bg-primary/10":"bg-muted"),children:n})]}),r&&(0,a.jsx)("div",{className:"flex-shrink-0 ml-3",children:(0,a.jsx)(p.eu,{className:"h-8 w-8",children:(0,a.jsx)(p.q5,{className:"bg-muted",children:(null===(l=H.fullName)||void 0===l?void 0:l.charAt(0))||"U"})})})]},t)}return null}):(0,a.jsx)("div",{className:"text-center text-muted-foreground",children:"No transcript available"})})})]}),(0,a.jsxs)(n.av,{value:"additionalInfo",className:"flex-1 p-6 overflow-y-auto",children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Preferences"}),(0,a.jsxs)("div",{className:"space-y-4",children:[H.interest&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Interest"}),(0,a.jsx)("div",{className:"text-sm",children:H.interest})]}),H.preferredProject&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Preferred Project"}),(0,a.jsx)("div",{className:"text-sm",children:H.preferredProject})]}),H.preferredLocation&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Location"}),(0,a.jsx)("div",{className:"text-sm",children:H.preferredLocation})]}),H.preferredUnitType&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Unit Type"}),(0,a.jsx)("div",{className:"text-sm",children:H.preferredUnitType})]}),H.projectType&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Project Type"}),(0,a.jsx)("div",{className:"text-sm",children:H.projectType})]}),H.investmentType&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Investment Type"}),(0,a.jsx)("div",{className:"text-sm",children:H.investmentType})]}),H.budget&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Budget"}),(0,a.jsx)("div",{className:"text-sm",children:H.budget})]}),H.brokenPromise&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Broken Promise"}),(0,a.jsx)("div",{className:"text-sm",children:H.brokenPromise})]}),H.callBackLanguage&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Call Back Language"}),(0,a.jsx)("div",{className:"text-sm",children:H.callBackLanguage})]}),H.callBackRequest&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Call Back Request"}),(0,a.jsx)("div",{className:"text-sm",children:H.callBackRequest})]}),H.claimedPaidAwaitingPOP&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Claimed Paid Awaiting POP"}),(0,a.jsx)("div",{className:"text-sm",children:H.claimedPaidAwaitingPOP})]}),H.doNotCall&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Do Not Call"}),(0,a.jsx)("div",{className:"text-sm",children:H.doNotCall})]}),H.followingPaymentPlan&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Following Payment Plan"}),(0,a.jsx)("div",{className:"text-sm",children:H.followingPaymentPlan})]}),H.fullyPaid&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Fully Paid"}),(0,a.jsx)("div",{className:"text-sm",children:H.fullyPaid})]}),H.fullyPaidByPDC&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Fully Paid By PDC"}),(0,a.jsx)("div",{className:"text-sm",children:H.fullyPaidByPDC})]}),H.incorrectContactDetails&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Incorrect Contact Details"}),(0,a.jsx)("div",{className:"text-sm",children:H.incorrectContactDetails})]}),H.mortgage&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Mortgage"}),(0,a.jsx)("div",{className:"text-sm",children:H.mortgage})]}),H.notResponding&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Not Responding"}),(0,a.jsx)("div",{className:"text-sm",children:H.notResponding})]}),H.notRespondingSOASent&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Not Responding (SOA Sent)"}),(0,a.jsx)("div",{className:"text-sm",children:H.notRespondingSOASent})]}),H.notWillingToPay&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Not Willing To Pay"}),(0,a.jsx)("div",{className:"text-sm",children:H.notWillingToPay})]}),H.popRaised&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"POP Raised"}),(0,a.jsx)("div",{className:"text-sm",children:H.popRaised})]}),H.promiseToPay&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Promise To Pay"}),(0,a.jsx)("div",{className:"text-sm",children:H.promiseToPay})]}),H.promiseToPayPartial&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Promise To Pay (Partial)"}),(0,a.jsx)("div",{className:"text-sm",children:H.promiseToPayPartial})]}),H.refuseToPay&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Refuse To Pay"}),(0,a.jsx)("div",{className:"text-sm",children:H.refuseToPay})]}),H.thirdParty&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Third Party"}),(0,a.jsx)("div",{className:"text-sm",children:H.thirdParty})]}),H.willingToPay&&(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Willing To Pay"}),(0,a.jsx)("div",{className:"text-sm",children:H.willingToPay})]})]}),H.bookedStatus&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mt-8 mb-4",children:"Booking Information"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Booked Status"}),(0,a.jsx)("div",{className:"text-sm",children:H.bookedStatus})]}),(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Confirmed Status"}),(0,a.jsx)("div",{className:"text-sm",children:H.confirmedStatus||"N/A"})]})]})]}),H.additionalQuestions&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mt-8 mb-4",children:"Additional Questions"}),(0,a.jsx)("div",{className:"p-4 bg-muted/50 rounded-md",children:(0,a.jsx)("p",{className:"text-sm",children:H.additionalQuestions})})]}),H.Response&&(0,a.jsxs)("div",{className:"flex mt-5",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Response"}),(0,a.jsx)("div",{className:"text-sm",children:H.Response})]}),H.Notes&&(0,a.jsxs)("div",{className:"flex mt-3",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Notes"}),(0,a.jsx)("div",{className:"text-sm",children:H.Notes})]}),H.Channel&&(0,a.jsxs)("div",{className:"flex mt-3",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"Channel"}),(0,a.jsx)("div",{className:"text-sm",children:H.Channel})]}),H.GuestRequest&&(0,a.jsxs)("div",{className:"flex mt-3",children:[(0,a.jsx)("div",{className:"w-40 text-sm font-medium text-muted-foreground",children:"GuestRequest"}),(0,a.jsx)("div",{className:"text-sm",children:H.GuestRequest})]})]})]})]}):(0,a.jsx)("div",{className:"flex items-center justify-center h-full text-muted-foreground",children:er?(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)("div",{className:"animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full mb-4"}),(0,a.jsx)("p",{children:"Loading call data..."})]}):(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)(N.A,{className:"h-12 w-12 text-muted-foreground mb-4"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:ei||"Select a call to view details"})]})})}),(0,a.jsx)(A.Lt,{open:ew,onOpenChange:eb,children:(0,a.jsxs)(A.EO,{children:[(0,a.jsxs)(A.wd,{children:[(0,a.jsx)(A.r7,{children:"Are you sure?"}),(0,a.jsx)(A.$v,{children:"This will permanently delete this call history record. This action cannot be undone."})]}),(0,a.jsxs)(A.ck,{children:[(0,a.jsx)(A.Zr,{children:"Cancel"}),(0,a.jsx)(A.Rx,{onClick:eL,className:"bg-red-600 hover:bg-red-700 focus:ring-red-600",children:"Delete"})]})]})})]})]})}},17313:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>d,av:()=>c,j7:()=>i,tU:()=>n});var a=s(95155);s(12115);var l=s(60704),r=s(59434);function n(e){let{className:t,...s}=e;return(0,a.jsx)(l.bL,{"data-slot":"tabs",className:(0,r.cn)("flex flex-col gap-2",t),...s})}function i(e){let{className:t,...s}=e;return(0,a.jsx)(l.B8,{"data-slot":"tabs-list",className:(0,r.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-1",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)(l.l9,{"data-slot":"tabs-trigger",className:(0,r.cn)("data-[state=active]:bg-background data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring inline-flex flex-1 items-center justify-center gap-1.5 rounded-md px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)(l.UC,{"data-slot":"tabs-content",className:(0,r.cn)("flex-1 outline-none",t),...s})}},36345:(e,t,s)=>{Promise.resolve().then(s.bind(s,7342))},90010:(e,t,s)=>{"use strict";s.d(t,{$v:()=>h,EO:()=>o,Lt:()=>i,Rx:()=>f,Zr:()=>v,ck:()=>x,r7:()=>u,wd:()=>m});var a=s(95155);s(12115);var l=s(17649),r=s(59434),n=s(30285);function i(e){let{...t}=e;return(0,a.jsx)(l.bL,{"data-slot":"alert-dialog",...t})}function d(e){let{...t}=e;return(0,a.jsx)(l.ZL,{"data-slot":"alert-dialog-portal",...t})}function c(e){let{className:t,...s}=e;return(0,a.jsx)(l.hJ,{"data-slot":"alert-dialog-overlay",className:(0,r.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-650 bg-black/80",t),...s})}function o(e){let{className:t,...s}=e;return(0,a.jsxs)(d,{children:[(0,a.jsx)(c,{}),(0,a.jsx)(l.UC,{"data-slot":"alert-dialog-content",className:(0,r.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-650 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...s})]})}function m(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,r.cn)("flex flex-col gap-2 text-center sm:text-left",t),...s})}function x(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,r.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...s})}function u(e){let{className:t,...s}=e;return(0,a.jsx)(l.hE,{"data-slot":"alert-dialog-title",className:(0,r.cn)("text-lg font-semibold",t),...s})}function h(e){let{className:t,...s}=e;return(0,a.jsx)(l.VY,{"data-slot":"alert-dialog-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function f(e){let{className:t,...s}=e;return(0,a.jsx)(l.rc,{className:(0,r.cn)((0,n.r)(),t),...s})}function v(e){let{className:t,...s}=e;return(0,a.jsx)(l.ZD,{className:(0,r.cn)((0,n.r)({variant:"outline"}),t),...s})}}}]);