"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6403],{11275:(e,t,n)=>{n.d(t,{X:()=>o});var r=n(12115),i=n(52712);function o(e){let[t,n]=r.useState(void 0);return(0,i.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,i;if(!Array.isArray(t)||!t.length)return;let o=t[0];if("borderBoxSize"in o){let e=o.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,i=t.blockSize}else r=e.offsetWidth,i=e.offsetHeight;n({width:r,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}},35152:(e,t,n)=>{n.d(t,{Mz:()=>eJ,i3:()=>eQ,UC:()=>eK,bL:()=>eU,Bk:()=>eD});var r=n(12115);let i=["top","right","bottom","left"],o=Math.min,l=Math.max,a=Math.round,f=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},s={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}function y(e){return["top","bottom"].includes(p(e))?"y":"x"}function v(e){return e.replace(/start|end/g,e=>s[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function x(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function b(e){let{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function R(e,t,n){let r,{reference:i,floating:o}=e,l=y(t),a=m(y(t)),f=g(a),u=p(t),c="y"===l,s=i.x+i.width/2-o.width/2,d=i.y+i.height/2-o.height/2,v=i[f]/2-o[f]/2;switch(u){case"top":r={x:s,y:i.y-o.height};break;case"bottom":r={x:s,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:d};break;case"left":r={x:i.x-o.width,y:d};break;default:r={x:i.x,y:i.y}}switch(h(t)){case"start":r[a]-=v*(n&&c?-1:1);break;case"end":r[a]+=v*(n&&c?-1:1)}return r}let A=async(e,t,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,a=o.filter(Boolean),f=await (null==l.isRTL?void 0:l.isRTL(t)),u=await l.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:s}=R(u,r,f),d=r,p={},h=0;for(let n=0;n<a.length;n++){let{name:o,fn:m}=a[n],{x:g,y:y,data:v,reset:w}=await m({x:c,y:s,initialPlacement:r,placement:d,strategy:i,middlewareData:p,rects:u,platform:l,elements:{reference:e,floating:t}});c=null!=g?g:c,s=null!=y?y:s,p={...p,[o]:{...p[o],...v}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(u=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:i}):w.rects),{x:c,y:s}=R(u,d,f)),n=-1)}return{x:c,y:s,placement:d,strategy:i,middlewareData:p}};async function C(e,t){var n;void 0===t&&(t={});let{x:r,y:i,platform:o,rects:l,elements:a,strategy:f}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:s="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=x(h),g=a[p?"floating"===s?"reference":"floating":s],y=b(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:f})),v="floating"===s?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,w=await (null==o.getOffsetParent?void 0:o.getOffsetParent(a.floating)),R=await (null==o.isElement?void 0:o.isElement(w))&&await (null==o.getScale?void 0:o.getScale(w))||{x:1,y:1},A=b(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:w,strategy:f}):v);return{top:(y.top-A.top+m.top)/R.y,bottom:(A.bottom-y.bottom+m.bottom)/R.y,left:(y.left-A.left+m.left)/R.x,right:(A.right-y.right+m.right)/R.x}}function S(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function L(e){return i.some(t=>e[t]>=0)}async function E(e,t){let{placement:n,platform:r,elements:i}=e,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=p(n),a=h(n),f="y"===y(n),u=["left","top"].includes(l)?-1:1,c=o&&f?-1:1,s=d(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:v}="number"==typeof s?{mainAxis:s,crossAxis:0,alignmentAxis:null}:{mainAxis:s.mainAxis||0,crossAxis:s.crossAxis||0,alignmentAxis:s.alignmentAxis};return a&&"number"==typeof v&&(g="end"===a?-1*v:v),f?{x:g*c,y:m*u}:{x:m*u,y:g*c}}function T(){return"undefined"!=typeof window}function O(e){return H(e)?(e.nodeName||"").toLowerCase():"#document"}function P(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function k(e){var t;return null==(t=(H(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function H(e){return!!T()&&(e instanceof Node||e instanceof P(e).Node)}function D(e){return!!T()&&(e instanceof Element||e instanceof P(e).Element)}function M(e){return!!T()&&(e instanceof HTMLElement||e instanceof P(e).HTMLElement)}function N(e){return!!T()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof P(e).ShadowRoot)}function j(e){let{overflow:t,overflowX:n,overflowY:r,display:i}=V(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function F(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function W(e){let t=z(),n=D(e)?V(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function B(e){return["html","body","#document"].includes(O(e))}function V(e){return P(e).getComputedStyle(e)}function X(e){return D(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function I(e){if("html"===O(e))return e;let t=e.assignedSlot||e.parentNode||N(e)&&e.host||k(e);return N(t)?t.host:t}function _(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let i=function e(t){let n=I(t);return B(n)?t.ownerDocument?t.ownerDocument.body:t.body:M(n)&&j(n)?n:e(n)}(e),o=i===(null==(r=e.ownerDocument)?void 0:r.body),l=P(i);if(o){let e=Y(l);return t.concat(l,l.visualViewport||[],j(i)?i:[],e&&n?_(e):[])}return t.concat(i,_(i,[],n))}function Y(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function q(e){let t=V(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,i=M(e),o=i?e.offsetWidth:n,l=i?e.offsetHeight:r,f=a(n)!==o||a(r)!==l;return f&&(n=o,r=l),{width:n,height:r,$:f}}function G(e){return D(e)?e:e.contextElement}function $(e){let t=G(e);if(!M(t))return u(1);let n=t.getBoundingClientRect(),{width:r,height:i,$:o}=q(t),l=(o?a(n.width):n.width)/r,f=(o?a(n.height):n.height)/i;return l&&Number.isFinite(l)||(l=1),f&&Number.isFinite(f)||(f=1),{x:l,y:f}}let U=u(0);function J(e){let t=P(e);return z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:U}function K(e,t,n,r){var i;void 0===t&&(t=!1),void 0===n&&(n=!1);let o=e.getBoundingClientRect(),l=G(e),a=u(1);t&&(r?D(r)&&(a=$(r)):a=$(e));let f=(void 0===(i=n)&&(i=!1),r&&(!i||r===P(l))&&i)?J(l):u(0),c=(o.left+f.x)/a.x,s=(o.top+f.y)/a.y,d=o.width/a.x,p=o.height/a.y;if(l){let e=P(l),t=r&&D(r)?P(r):r,n=e,i=Y(n);for(;i&&r&&t!==n;){let e=$(i),t=i.getBoundingClientRect(),r=V(i),o=t.left+(i.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(i.clientTop+parseFloat(r.paddingTop))*e.y;c*=e.x,s*=e.y,d*=e.x,p*=e.y,c+=o,s+=l,i=Y(n=P(i))}}return b({width:d,height:p,x:c,y:s})}function Q(e,t){let n=X(e).scrollLeft;return t?t.left+n:K(k(e)).left+n}function Z(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:Q(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=P(e),r=k(e),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,a=0,f=0;if(i){o=i.width,l=i.height;let e=z();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,f=i.offsetTop)}return{width:o,height:l,x:a,y:f}}(e,n);else if("document"===t)r=function(e){let t=k(e),n=X(e),r=e.ownerDocument.body,i=l(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),o=l(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+Q(e),f=-n.scrollTop;return"rtl"===V(r).direction&&(a+=l(t.clientWidth,r.clientWidth)-i),{width:i,height:o,x:a,y:f}}(k(e));else if(D(t))r=function(e,t){let n=K(e,!0,"fixed"===t),r=n.top+e.clientTop,i=n.left+e.clientLeft,o=M(e)?$(e):u(1),l=e.clientWidth*o.x,a=e.clientHeight*o.y;return{width:l,height:a,x:i*o.x,y:r*o.y}}(t,n);else{let n=J(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return b(r)}function et(e){return"static"===V(e).position}function en(e,t){if(!M(e)||"fixed"===V(e).position)return null;if(t)return t(e);let n=e.offsetParent;return k(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=P(e);if(F(e))return n;if(!M(e)){let t=I(e);for(;t&&!B(t);){if(D(t)&&!et(t))return t;t=I(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(O(r))&&et(r);)r=en(r,t);return r&&B(r)&&et(r)&&!W(r)?n:r||function(e){let t=I(e);for(;M(t)&&!B(t);){if(W(t))return t;if(F(t))break;t=I(t)}return null}(e)||n}let ei=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=M(t),i=k(t),o="fixed"===n,l=K(e,!0,o,t),a={scrollLeft:0,scrollTop:0},f=u(0);if(r||!r&&!o){if(("body"!==O(t)||j(i))&&(a=X(t)),r){let e=K(t,!0,o,t);f.x=e.x+t.clientLeft,f.y=e.y+t.clientTop}else i&&(f.x=Q(i))}let c=!i||r||o?u(0):Z(i,a);return{x:l.left+a.scrollLeft-f.x-c.x,y:l.top+a.scrollTop-f.y-c.y,width:l.width,height:l.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eo={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e,o="fixed"===i,l=k(r),a=!!t&&F(t.floating);if(r===l||a&&o)return n;let f={scrollLeft:0,scrollTop:0},c=u(1),s=u(0),d=M(r);if((d||!d&&!o)&&(("body"!==O(r)||j(l))&&(f=X(r)),M(r))){let e=K(r);c=$(r),s.x=e.x+r.clientLeft,s.y=e.y+r.clientTop}let p=!l||d||o?u(0):Z(l,f,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-f.scrollLeft*c.x+s.x+p.x,y:n.y*c.y-f.scrollTop*c.y+s.y+p.y}},getDocumentElement:k,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e,a=[..."clippingAncestors"===n?F(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=_(e,[],!1).filter(e=>D(e)&&"body"!==O(e)),i=null,o="fixed"===V(e).position,l=o?I(e):e;for(;D(l)&&!B(l);){let t=V(l),n=W(l);n||"fixed"!==t.position||(i=null),(o?!n&&!i:!n&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||j(l)&&!n&&function e(t,n){let r=I(t);return!(r===n||!D(r)||B(r))&&("fixed"===V(r).position||e(r,n))}(e,l))?r=r.filter(e=>e!==l):i=t,l=I(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],f=a[0],u=a.reduce((e,n)=>{let r=ee(t,n,i);return e.top=l(r.top,e.top),e.right=o(r.right,e.right),e.bottom=o(r.bottom,e.bottom),e.left=l(r.left,e.left),e},ee(t,f,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:er,getElementRects:ei,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=q(e);return{width:t,height:n}},getScale:$,isElement:D,isRTL:function(e){return"rtl"===V(e).direction}};function el(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:i,rects:a,platform:f,elements:u,middlewareData:c}=t,{element:s,padding:p=0}=d(e,t)||{};if(null==s)return{};let v=x(p),w={x:n,y:r},b=m(y(i)),R=g(b),A=await f.getDimensions(s),C="y"===b,S=C?"clientHeight":"clientWidth",L=a.reference[R]+a.reference[b]-w[b]-a.floating[R],E=w[b]-a.reference[b],T=await (null==f.getOffsetParent?void 0:f.getOffsetParent(s)),O=T?T[S]:0;O&&await (null==f.isElement?void 0:f.isElement(T))||(O=u.floating[S]||a.floating[R]);let P=O/2-A[R]/2-1,k=o(v[C?"top":"left"],P),H=o(v[C?"bottom":"right"],P),D=O-A[R]-H,M=O/2-A[R]/2+(L/2-E/2),N=l(k,o(M,D)),j=!c.arrow&&null!=h(i)&&M!==N&&a.reference[R]/2-(M<k?k:H)-A[R]/2<0,F=j?M<k?M-k:M-D:0;return{[b]:w[b]+F,data:{[b]:N,centerOffset:M-N-F,...j&&{alignmentOffset:F}},reset:j}}}),ef=(e,t,n)=>{let r=new Map,i={platform:eo,...n},o={...i.platform,_c:r};return A(e,t,{...i,platform:o})};var eu=n(47650),ec="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function es(e,t){let n,r,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!es(e[r],t[r]))return!1;return!0}if((n=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!e.$$typeof)&&!es(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let n=ed(e);return Math.round(t*n)/n}function eh(e){let t=r.useRef(e);return ec(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),eg=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:i,y:o,placement:l,middlewareData:a}=t,f=await E(t,e);return l===(null==(n=a.offset)?void 0:n.placement)&&null!=(r=a.arrow)&&r.alignmentOffset?{}:{x:i+f.x,y:o+f.y,data:{...f,placement:l}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:i}=t,{mainAxis:a=!0,crossAxis:f=!1,limiter:u={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...c}=d(e,t),s={x:n,y:r},h=await C(t,c),g=y(p(i)),v=m(g),w=s[v],x=s[g];if(a){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=w+h[e],r=w-h[t];w=l(n,o(w,r))}if(f){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",n=x+h[e],r=x-h[t];x=l(n,o(x,r))}let b=u.fn({...t,[v]:w,[g]:x});return{...b,data:{x:b.x-n,y:b.y-r,enabled:{[v]:a,[g]:f}}}}}}(e),options:[e,t]}),ev=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=t,{offset:a=0,mainAxis:f=!0,crossAxis:u=!0}=d(e,t),c={x:n,y:r},s=y(i),h=m(s),g=c[h],v=c[s],w=d(a,t),x="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(f){let e="y"===h?"height":"width",t=o.reference[h]-o.floating[e]+x.mainAxis,n=o.reference[h]+o.reference[e]-x.mainAxis;g<t?g=t:g>n&&(g=n)}if(u){var b,R;let e="y"===h?"width":"height",t=["top","left"].includes(p(i)),n=o.reference[s]-o.floating[e]+(t&&(null==(b=l.offset)?void 0:b[s])||0)+(t?0:x.crossAxis),r=o.reference[s]+o.reference[e]+(t?0:(null==(R=l.offset)?void 0:R[s])||0)-(t?x.crossAxis:0);v<n?v=n:v>r&&(v=r)}return{[h]:g,[s]:v}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,i,o,l;let{placement:a,middlewareData:f,rects:u,initialPlacement:c,platform:s,elements:x}=t,{mainAxis:b=!0,crossAxis:R=!0,fallbackPlacements:A,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:L="none",flipAlignment:E=!0,...T}=d(e,t);if(null!=(n=f.arrow)&&n.alignmentOffset)return{};let O=p(a),P=y(c),k=p(c)===c,H=await (null==s.isRTL?void 0:s.isRTL(x.floating)),D=A||(k||!E?[w(c)]:function(e){let t=w(e);return[v(e),t,v(t)]}(c)),M="none"!==L;!A&&M&&D.push(...function(e,t,n,r){let i=h(e),o=function(e,t,n){let r=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(n)return t?i:r;return t?r:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return i&&(o=o.map(e=>e+"-"+i),t&&(o=o.concat(o.map(v)))),o}(c,E,L,H));let N=[c,...D],j=await C(t,T),F=[],W=(null==(r=f.flip)?void 0:r.overflows)||[];if(b&&F.push(j[O]),R){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),i=m(y(e)),o=g(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[o]>t.floating[o]&&(l=w(l)),[l,w(l)]}(a,u,H);F.push(j[e[0]],j[e[1]])}if(W=[...W,{placement:a,overflows:F}],!F.every(e=>e<=0)){let e=((null==(i=f.flip)?void 0:i.index)||0)+1,t=N[e];if(t)return{data:{index:e,overflows:W},reset:{placement:t}};let n=null==(o=W.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:o.placement;if(!n)switch(S){case"bestFit":{let e=null==(l=W.filter(e=>{if(M){let t=y(e.placement);return t===P||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:l[0];e&&(n=e);break}case"initialPlacement":n=c}if(a!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let i,a;let{placement:f,rects:u,platform:c,elements:s}=t,{apply:m=()=>{},...g}=d(e,t),v=await C(t,g),w=p(f),x=h(f),b="y"===y(f),{width:R,height:A}=u.floating;"top"===w||"bottom"===w?(i=w,a=x===(await (null==c.isRTL?void 0:c.isRTL(s.floating))?"start":"end")?"left":"right"):(a=w,i="end"===x?"top":"bottom");let S=A-v.top-v.bottom,L=R-v.left-v.right,E=o(A-v[i],S),T=o(R-v[a],L),O=!t.middlewareData.shift,P=E,k=T;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(k=L),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(P=S),O&&!x){let e=l(v.left,0),t=l(v.right,0),n=l(v.top,0),r=l(v.bottom,0);b?k=R-2*(0!==e||0!==t?e+t:l(v.left,v.right)):P=A-2*(0!==n||0!==r?n+r:l(v.top,v.bottom))}await m({...t,availableWidth:k,availableHeight:P});let H=await c.getDimensions(s.floating);return R!==H.width||A!==H.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...i}=d(e,t);switch(r){case"referenceHidden":{let e=S(await C(t,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:L(e)}}}case"escaped":{let e=S(await C(t,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:L(e)}}}default:return{}}}}}(e),options:[e,t]}),eR=(e,t)=>({...em(e),options:[e,t]});var eA=n(63655),eC=n(95155),eS=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:i=5,...o}=e;return(0,eC.jsx)(eA.sG.svg,{...o,ref:t,width:r,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eC.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eS.displayName="Arrow";var eL=n(6101),eE=n(46081),eT=n(39033),eO=n(52712),eP=n(11275),ek="Popper",[eH,eD]=(0,eE.A)(ek),[eM,eN]=eH(ek),ej=e=>{let{__scopePopper:t,children:n}=e,[i,o]=r.useState(null);return(0,eC.jsx)(eM,{scope:t,anchor:i,onAnchorChange:o,children:n})};ej.displayName=ek;var eF="PopperAnchor",eW=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:i,...o}=e,l=eN(eF,n),a=r.useRef(null),f=(0,eL.s)(t,a);return r.useEffect(()=>{l.onAnchorChange((null==i?void 0:i.current)||a.current)}),i?null:(0,eC.jsx)(eA.sG.div,{...o,ref:f})});eW.displayName=eF;var ez="PopperContent",[eB,eV]=eH(ez),eX=r.forwardRef((e,t)=>{var n,i,a,u,c,s,d,p;let{__scopePopper:h,side:m="bottom",sideOffset:g=0,align:y="center",alignOffset:v=0,arrowPadding:w=0,avoidCollisions:x=!0,collisionBoundary:b=[],collisionPadding:R=0,sticky:A="partial",hideWhenDetached:C=!1,updatePositionStrategy:S="optimized",onPlaced:L,...E}=e,T=eN(ez,h),[O,P]=r.useState(null),H=(0,eL.s)(t,e=>P(e)),[D,M]=r.useState(null),N=(0,eP.X)(D),j=null!==(d=null==N?void 0:N.width)&&void 0!==d?d:0,F=null!==(p=null==N?void 0:N.height)&&void 0!==p?p:0,W="number"==typeof R?R:{top:0,right:0,bottom:0,left:0,...R},z=Array.isArray(b)?b:[b],B=z.length>0,V={padding:W,boundary:z.filter(eq),altBoundary:B},{refs:X,floatingStyles:I,placement:Y,isPositioned:q,middlewareData:$}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:i=[],platform:o,elements:{reference:l,floating:a}={},transform:f=!0,whileElementsMounted:u,open:c}=e,[s,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(i);es(p,i)||h(i);let[m,g]=r.useState(null),[y,v]=r.useState(null),w=r.useCallback(e=>{e!==A.current&&(A.current=e,g(e))},[]),x=r.useCallback(e=>{e!==C.current&&(C.current=e,v(e))},[]),b=l||m,R=a||y,A=r.useRef(null),C=r.useRef(null),S=r.useRef(s),L=null!=u,E=eh(u),T=eh(o),O=eh(c),P=r.useCallback(()=>{if(!A.current||!C.current)return;let e={placement:t,strategy:n,middleware:p};T.current&&(e.platform=T.current),ef(A.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==O.current};k.current&&!es(S.current,t)&&(S.current=t,eu.flushSync(()=>{d(t)}))})},[p,t,n,T,O]);ec(()=>{!1===c&&S.current.isPositioned&&(S.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let k=r.useRef(!1);ec(()=>(k.current=!0,()=>{k.current=!1}),[]),ec(()=>{if(b&&(A.current=b),R&&(C.current=R),b&&R){if(E.current)return E.current(b,R,P);P()}},[b,R,P,E,L]);let H=r.useMemo(()=>({reference:A,floating:C,setReference:w,setFloating:x}),[w,x]),D=r.useMemo(()=>({reference:b,floating:R}),[b,R]),M=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!D.floating)return e;let t=ep(D.floating,s.x),r=ep(D.floating,s.y);return f?{...e,transform:"translate("+t+"px, "+r+"px)",...ed(D.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,f,D.floating,s.x,s.y]);return r.useMemo(()=>({...s,update:P,refs:H,elements:D,floatingStyles:M}),[s,P,H,D,M])}({strategy:"fixed",placement:m+("center"!==y?"-"+y:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let i;void 0===r&&(r={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=G(e),h=a||u?[...p?_(p):[],..._(t)]:[];h.forEach(e=>{a&&e.addEventListener("scroll",n,{passive:!0}),u&&e.addEventListener("resize",n)});let m=p&&s?function(e,t){let n,r=null,i=k(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function u(c,s){void 0===c&&(c=!1),void 0===s&&(s=1),a();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(c||t(),!m||!g)return;let y=f(h),v=f(i.clientWidth-(p+m)),w={rootMargin:-y+"px "+-v+"px "+-f(i.clientHeight-(h+g))+"px "+-f(p)+"px",threshold:l(0,o(1,s))||1},x=!0;function b(t){let r=t[0].intersectionRatio;if(r!==s){if(!x)return u();r?u(!1,r):n=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==r||el(d,e.getBoundingClientRect())||u(),x=!1}try{r=new IntersectionObserver(b,{...w,root:i.ownerDocument})}catch(e){r=new IntersectionObserver(b,w)}r.observe(e)}(!0),a}(p,n):null,g=-1,y=null;c&&(y=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&y&&(y.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),n()}),p&&!d&&y.observe(p),y.observe(t));let v=d?K(e):null;return d&&function t(){let r=K(e);v&&!el(v,r)&&n(),v=r,i=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{a&&e.removeEventListener("scroll",n),u&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=y)||e.disconnect(),y=null,d&&cancelAnimationFrame(i)}}(...t,{animationFrame:"always"===S})},elements:{reference:T.anchor},middleware:[eg({mainAxis:g+F,alignmentAxis:v}),x&&ey({mainAxis:!0,crossAxis:!1,limiter:"partial"===A?ev():void 0,...V}),x&&ew({...V}),ex({...V,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:i}=e,{width:o,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(i,"px")),a.setProperty("--radix-popper-anchor-width","".concat(o,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),D&&eR({element:D,padding:w}),eG({arrowWidth:j,arrowHeight:F}),C&&eb({strategy:"referenceHidden",...V})]}),[U,J]=e$(Y),Q=(0,eT.c)(L);(0,eO.N)(()=>{q&&(null==Q||Q())},[q,Q]);let Z=null===(n=$.arrow)||void 0===n?void 0:n.x,ee=null===(i=$.arrow)||void 0===i?void 0:i.y,et=(null===(a=$.arrow)||void 0===a?void 0:a.centerOffset)!==0,[en,er]=r.useState();return(0,eO.N)(()=>{O&&er(window.getComputedStyle(O).zIndex)},[O]),(0,eC.jsx)("div",{ref:X.setFloating,"data-radix-popper-content-wrapper":"",style:{...I,transform:q?I.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:en,"--radix-popper-transform-origin":[null===(u=$.transformOrigin)||void 0===u?void 0:u.x,null===(c=$.transformOrigin)||void 0===c?void 0:c.y].join(" "),...(null===(s=$.hide)||void 0===s?void 0:s.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eC.jsx)(eB,{scope:h,placedSide:U,onArrowChange:M,arrowX:Z,arrowY:ee,shouldHideArrow:et,children:(0,eC.jsx)(eA.sG.div,{"data-side":U,"data-align":J,...E,ref:H,style:{...E.style,animation:q?void 0:"none"}})})})});eX.displayName=ez;var eI="PopperArrow",e_={top:"bottom",right:"left",bottom:"top",left:"right"},eY=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,i=eV(eI,n),o=e_[i.placedSide];return(0,eC.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[o]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eC.jsx)(eS,{...r,ref:t,style:{...r.style,display:"block"}})})});function eq(e){return null!==e}eY.displayName=eI;var eG=e=>({name:"transformOrigin",options:e,fn(t){var n,r,i,o,l;let{placement:a,rects:f,middlewareData:u}=t,c=(null===(n=u.arrow)||void 0===n?void 0:n.centerOffset)!==0,s=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[p,h]=e$(a),m={start:"0%",center:"50%",end:"100%"}[h],g=(null!==(o=null===(r=u.arrow)||void 0===r?void 0:r.x)&&void 0!==o?o:0)+s/2,y=(null!==(l=null===(i=u.arrow)||void 0===i?void 0:i.y)&&void 0!==l?l:0)+d/2,v="",w="";return"bottom"===p?(v=c?m:"".concat(g,"px"),w="".concat(-d,"px")):"top"===p?(v=c?m:"".concat(g,"px"),w="".concat(f.floating.height+d,"px")):"right"===p?(v="".concat(-d,"px"),w=c?m:"".concat(y,"px")):"left"===p&&(v="".concat(f.floating.width+d,"px"),w=c?m:"".concat(y,"px")),{data:{x:v,y:w}}}});function e$(e){let[t,n="center"]=e.split("-");return[t,n]}var eU=ej,eJ=eW,eK=eX,eQ=eY},82284:(e,t,n)=>{n.d(t,{N:()=>f});var r=n(12115),i=n(46081),o=n(6101),l=n(99708),a=n(95155);function f(e){let t=e+"CollectionProvider",[n,f]=(0,i.A)(t),[u,c]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:n}=e,i=r.useRef(null),o=r.useRef(new Map).current;return(0,a.jsx)(u,{scope:t,itemMap:o,collectionRef:i,children:n})};s.displayName=t;let d=e+"CollectionSlot",p=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,i=c(d,n),f=(0,o.s)(t,i.collectionRef);return(0,a.jsx)(l.DX,{ref:f,children:r})});p.displayName=d;let h=e+"CollectionItemSlot",m="data-radix-collection-item",g=r.forwardRef((e,t)=>{let{scope:n,children:i,...f}=e,u=r.useRef(null),s=(0,o.s)(t,u),d=c(h,n);return r.useEffect(()=>(d.itemMap.set(u,{ref:u,...f}),()=>void d.itemMap.delete(u))),(0,a.jsx)(l.DX,{[m]:"",ref:s,children:i})});return g.displayName=h,[{Provider:s,Slot:p,ItemSlot:g},function(t){let n=c(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},f]}},94315:(e,t,n)=>{n.d(t,{jH:()=>o});var r=n(12115);n(95155);var i=r.createContext(void 0);function o(e){let t=r.useContext(i);return e||t||"ltr"}}}]);