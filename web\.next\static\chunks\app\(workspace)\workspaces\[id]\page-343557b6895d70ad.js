(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3914],{12421:(e,t,s)=>{"use strict";s.d(t,{t:()=>r});var a=s(57297);async function r(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=localStorage.getItem("access_token");if(!s){let e=await (0,a.J1)();if(!e.success)throw Error("No authentication token available");s=e.newAccessToken}let r=new Headers(t.headers||{});r.has("Authorization")||r.set("Authorization","Bearer ".concat(s));let i=await fetch(e,{...t,headers:r});if(401===i.status||403===i.status){console.log("Token expired, attempting refresh...");let s=await (0,a.J1)();if(!s.success)throw console.error("Token refresh failed"),window.location.href="/login",Error("Authentication failed");console.log("Token refreshed, retrying request...");let r=new Headers(t.headers||{});return r.set("Authorization","Bearer ".concat(s.newAccessToken)),fetch(e,{...t,headers:r})}return i}},17313:(e,t,s)=>{"use strict";s.d(t,{Xi:()=>o,av:()=>c,j7:()=>l,tU:()=>n});var a=s(95155);s(12115);var r=s(60704),i=s(59434);function n(e){let{className:t,...s}=e;return(0,a.jsx)(r.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-1",t),...s})}function o(e){let{className:t,...s}=e;return(0,a.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring inline-flex flex-1 items-center justify-center gap-1.5 rounded-md px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",t),...s})}},27944:(e,t,s)=>{"use strict";s.d(t,{default:()=>y});var a=s(95155),r=s(12115),i=s(35695),n=s(66695),l=s(17313),o=s(30285),c=s(62523),d=s(85057),u=s(88539),m=s(59409),h=s(51154),g=s(35169),p=s(4229),x=s(56671),f=s(99672),v=s(12421);let j=async(e,t)=>{let s=await (0,v.t)("".concat("http://localhost:4000","/api/organizations/").concat(e,"/settings"),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!s.ok)throw Error((await s.json()).message||"Failed to update organization settings");return s.json()};var b=s(59434);function y(e){let{organizationId:t}=e,[s,v]=(0,r.useState)(null),[y,w]=(0,r.useState)(!0),[N,k]=(0,r.useState)(!1),[C,T]=(0,r.useState)("details"),[z,S]=(0,r.useState)({name:"",description:"",status:"active"}),[A,F]=(0,r.useState)({credits:0,autoRechargeEnabled:!1,autoRechargeThreshold:1,autoRechargeAmount:0,callPricePerMinute:.1,minimumCreditsThreshold:1,monthlyMinutesAllowance:0,monthlyResetDate:1}),[_,E]=(0,r.useState)({fullName:"",email:""}),P=(0,i.useRouter)();(0,r.useEffect)(()=>{B()},[t]);let B=async()=>{try{w(!0);let e=await (0,f.SA)(t);v(e),S({name:e.name,description:e.description||"",status:e.status}),F({credits:e.credits||0,autoRechargeEnabled:e.autoRechargeEnabled||!1,autoRechargeThreshold:e.autoRechargeThreshold||1,autoRechargeAmount:e.autoRechargeAmount||0,callPricePerMinute:e.callPricePerMinute||.1,minimumCreditsThreshold:e.minimumCreditsThreshold||1,monthlyMinutesAllowance:e.monthlyMinutesAllowance||0,monthlyResetDate:e.monthlyResetDate||1}),E({fullName:e.fullName||"",email:e.email||""})}catch(e){console.error("Error fetching Workspace:",e),x.o.error("Failed to fetch workspace details")}finally{w(!1)}},R=async()=>{try{k(!0);let e=await (0,f.L_)(t,z);v(e),x.o.success("Workspace details updated successfully")}catch(e){console.error("Error updating Workspace:",e),x.o.error("Failed to update Workspace details")}finally{k(!1)}},M=async()=>{try{k(!0);let e=await (0,f.co)(t,A);v(e),x.o.success("Workspace billing settings updated successfully")}catch(e){console.error("Error updating Workspace billing:",e),x.o.error("Failed to update Workspace billing settings")}finally{k(!1)}},J=async()=>{try{k(!0),await j(t,_),await B(),x.o.success("Notification settings updated successfully")}catch(e){console.error("Error updating notification settings:",e),x.o.error("Failed to update notification settings")}finally{k(!1)}};return y?(0,a.jsx)("div",{className:"container mx-auto py-6 flex justify-center items-center min-h-[60vh]",children:(0,a.jsx)(h.A,{className:"h-8 w-8 animate-spin"})}):s?(0,a.jsxs)("div",{className:"container mx-auto py-6",children:[(0,a.jsxs)("div",{className:"flex items-center mb-6",children:[(0,a.jsxs)(o.$,{variant:"outline",onClick:()=>P.back(),className:"mr-4",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,a.jsx)("h1",{className:"text-3xl font-bold",children:s.name})]}),(0,a.jsxs)(l.tU,{value:C,onValueChange:T,children:[(0,a.jsxs)(l.j7,{className:"mb-6",children:[(0,a.jsx)(l.Xi,{value:"details",children:"Details"}),(0,a.jsx)(l.Xi,{value:"billing",children:"Billing"}),(0,a.jsx)(l.Xi,{value:"notifications",children:"Notifications"})]}),(0,a.jsx)(l.av,{value:"details",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Workspace Details"}),(0,a.jsx)(n.BT,{children:"Update the Workspace's basic information."})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"name",className:"md:text-right",children:"Name"}),(0,a.jsx)(c.p,{id:"name",value:z.name,onChange:e=>S({...z,name:e.target.value}),className:"md:col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"description",className:"md:text-right",children:"Description"}),(0,a.jsx)(u.T,{id:"description",value:z.description,onChange:e=>S({...z,description:e.target.value}),className:"md:col-span-3"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"status",className:"md:text-right",children:"Status"}),(0,a.jsxs)(m.l6,{value:z.status,onValueChange:e=>S({...z,status:e}),children:[(0,a.jsx)(m.bq,{className:"md:col-span-3",children:(0,a.jsx)(m.yv,{placeholder:"Select status"})}),(0,a.jsxs)(m.gC,{children:[(0,a.jsx)(m.eb,{value:"active",children:"Active"}),(0,a.jsx)(m.eb,{value:"inactive",children:"Inactive"}),(0,a.jsx)(m.eb,{value:"suspended",children:"Suspended"})]})]})]})]}),(0,a.jsx)(n.wL,{className:"flex justify-end",children:(0,a.jsx)(o.$,{onClick:R,disabled:N,children:N?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Save Changes"]})})})]})}),(0,a.jsx)(l.av,{value:"billing",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Billing Settings"}),(0,a.jsx)(n.BT,{children:"Manage the Workspace's billing and credits."})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"credits",className:"md:text-right",children:"Credits"}),(0,a.jsxs)("div",{className:"md:col-span-3 flex items-center",children:[(0,a.jsx)(c.p,{id:"credits",type:"number",min:"0",step:"0.01",value:A.credits,onChange:e=>F({...A,credits:parseFloat(e.target.value)||0}),className:"w-full"}),(0,a.jsxs)("span",{className:"ml-2 text-sm text-gray-500",children:["Current: ",(0,b.v)(s.credits||0)]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"callPricePerMinute",className:"md:text-right",children:"Call Price Per Minute ($)"}),(0,a.jsxs)("div",{className:"md:col-span-3",children:[(0,a.jsx)(c.p,{id:"callPricePerMinute",type:"number",min:"0.01",step:"0.01",value:A.callPricePerMinute,onChange:e=>F({...A,callPricePerMinute:parseFloat(e.target.value)||.1}),className:"w-full"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"The price charged per minute for calls"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"minimumCreditsThreshold",className:"md:text-right",children:"Minimum Credits Threshold ($)"}),(0,a.jsxs)("div",{className:"md:col-span-3",children:[(0,a.jsx)(c.p,{id:"minimumCreditsThreshold",type:"number",min:"0",step:"0.01",value:A.minimumCreditsThreshold,onChange:e=>F({...A,minimumCreditsThreshold:parseFloat(e.target.value)||1}),className:"w-full"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"When credits fall below this amount, calls will be blocked"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"monthlyMinutesAllowance",className:"md:text-right",children:"Monthly Minutes Allowance"}),(0,a.jsxs)("div",{className:"md:col-span-3",children:[(0,a.jsx)(c.p,{id:"monthlyMinutesAllowance",type:"number",min:"0",step:"1",value:A.monthlyMinutesAllowance,onChange:e=>F({...A,monthlyMinutesAllowance:parseFloat(e.target.value)||0}),className:"w-full"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Monthly minutes allowance for this workspace (0 to disable)"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"monthlyResetDate",className:"md:text-right",children:"Monthly Reset Date"}),(0,a.jsxs)("div",{className:"md:col-span-3",children:[(0,a.jsxs)(m.l6,{value:A.monthlyResetDate.toString(),onValueChange:e=>F({...A,monthlyResetDate:parseInt(e)}),children:[(0,a.jsx)(m.bq,{children:(0,a.jsx)(m.yv,{placeholder:"Select reset date"})}),(0,a.jsx)(m.gC,{children:Array.from({length:28},(e,t)=>t+1).map(e=>(0,a.jsxs)(m.eb,{value:e.toString(),children:[e,1===e?"st":2===e?"nd":3===e?"rd":"th"," of each month"]},e))})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Day of the month when monthly credits and allowances reset"})]})]})]}),(0,a.jsx)(n.wL,{className:"flex justify-end",children:(0,a.jsx)(o.$,{onClick:M,disabled:N,children:N?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Save Billing Settings"]})})})]})}),(0,a.jsx)(l.av,{value:"notifications",children:(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Email Notification Settings"}),(0,a.jsx)(n.BT,{children:"Configure email notifications for credit alerts and warnings."})]}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"fullName",className:"md:text-right",children:"Client Full Name"}),(0,a.jsxs)("div",{className:"md:col-span-3",children:[(0,a.jsx)(c.p,{id:"fullName",type:"text",value:_.fullName,onChange:e=>E({..._,fullName:e.target.value}),placeholder:"Enter client's full name",className:"w-full"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"This name will be used in email notifications for personalization"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 items-center gap-4",children:[(0,a.jsx)(d.J,{htmlFor:"email",className:"md:text-right",children:"Notification Email"}),(0,a.jsxs)("div",{className:"md:col-span-3",children:[(0,a.jsx)(c.p,{id:"email",type:"email",value:_.email,onChange:e=>E({..._,email:e.target.value}),placeholder:"Enter email address for notifications",className:"w-full"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Credit alerts and warnings will be sent to this email address"})]})]}),(0,a.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-900 mb-2",children:"Email Notification Types"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-800 space-y-1",children:[(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Credit Runout Alert:"})," Sent when credits are completely depleted"]}),(0,a.jsxs)("li",{children:["• ",(0,a.jsx)("strong",{children:"Credit Warning:"})," Sent when credits fall below 2x the minimum threshold"]}),(0,a.jsx)("li",{children:"• Both emails include a direct link to add funds to the account"})]})]}),s.minimumCreditsThreshold&&(0,a.jsxs)("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Current Thresholds"}),(0,a.jsxs)("div",{className:"text-sm text-gray-700 space-y-1",children:[(0,a.jsxs)("p",{children:["• ",(0,a.jsx)("strong",{children:"Minimum Credits Threshold:"})," $",s.minimumCreditsThreshold.toFixed(2)]}),(0,a.jsxs)("p",{children:["• ",(0,a.jsx)("strong",{children:"Warning Threshold:"})," $",(2*s.minimumCreditsThreshold).toFixed(2)]}),(0,a.jsxs)("p",{children:["• ",(0,a.jsx)("strong",{children:"Current Credits:"})," $",s.credits.toFixed(2)]})]})]})]}),(0,a.jsx)(n.wL,{className:"flex justify-end",children:(0,a.jsx)(o.$,{onClick:J,disabled:N,children:N?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Saving..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Save Notification Settings"]})})})]})})]})]}):(0,a.jsx)("div",{className:"container mx-auto py-6",children:(0,a.jsx)("div",{className:"text-center py-8 text-gray-500",children:"Workspace not found."})})}},30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>o,r:()=>l});var a=s(95155);s(12115);var r=s(99708),i=s(74466),n=s(59434);let l=(0,i.F)("inline-flex items-center cursor-pointer justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:s,size:i,asChild:o=!1,...c}=e,d=o?r.DX:"button";return(0,a.jsx)(d,{"data-slot":"button",className:(0,n.cn)(l({variant:s,size:i,className:t})),...c})}},40026:(e,t,s)=>{"use strict";s.d(t,{H:()=>a,e:()=>r});let a="http://localhost:4000",r="pk_test_51ROz1YRpJ0zLf0aTbgbDkpShvfpNxdZPet1QXClapTckA7Cy0tsaxY2qY1dp8oSBGOFqnh0vugjd8mDluFWgKpRL00bACyumT8"},57297:(e,t,s)=>{"use strict";s.d(t,{HW:()=>i,J1:()=>l,_f:()=>n});var a=s(12421);let r="http://localhost:4000";async function i(){try{let e=await (0,a.t)("".concat(r,"/api/auth/me"),{method:"GET"});if(!e.ok)return{success:!1,error:"Error: ".concat(e.status)};let t=await e.json(),s=t.userId||t._id||t.id,i=t.email;if(s&&i)return{success:!0,user:{fullName:t.fullName||i.split("@")[0],userId:s,email:i,role:t.role||"user"}};return{success:!1,error:"Invalid user data received"}}catch(e){return console.error("Error fetching user data:",e),{success:!1,error:"An error occurred while fetching user data"}}}function n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10,t=setInterval(async()=>{if(localStorage.getItem("access_token"))try{await l()}catch(e){console.error("Background token refresh failed:",e)}},6e4*e);return()=>clearInterval(t)}async function l(){let e=localStorage.getItem("refresh_token");if(!e)return{success:!1};try{let t=await fetch("".concat(r,"/api/auth/refresh"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!t.ok)return{success:!1};let s=await t.json();if(s.access_token)return localStorage.setItem("access_token",s.access_token),{success:!0,newAccessToken:s.access_token};return{success:!1}}catch(e){return console.error("Token refresh error:",e),{success:!1}}}},59409:(e,t,s)=>{"use strict";s.d(t,{bq:()=>u,eb:()=>h,gC:()=>m,l6:()=>c,yv:()=>d});var a=s(95155);s(12115);var r=s(31992),i=s(66474),n=s(5196),l=s(47863),o=s(59434);function c(e){let{...t}=e;return(0,a.jsx)(r.bL,{"data-slot":"select",...t})}function d(e){let{...t}=e;return(0,a.jsx)(r.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,children:s,...n}=e;return(0,a.jsxs)(r.l9,{"data-slot":"select-trigger",className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...n,children:[s,(0,a.jsx)(r.In,{asChild:!0,children:(0,a.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:t,children:s,position:i="popper",...n}=e;return(0,a.jsx)(r.ZL,{children:(0,a.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md","popper"===i&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:i,...n,children:[(0,a.jsx)(g,{}),(0,a.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===i&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:s}),(0,a.jsx)(p,{})]})})}function h(e){let{className:t,children:s,...i}=e;return(0,a.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...i,children:[(0,a.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,a.jsx)(r.VF,{children:(0,a.jsx)(n.A,{className:"size-4"})})}),(0,a.jsx)(r.p4,{children:s})]})}function g(e){let{className:t,...s}=e;return(0,a.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(l.A,{className:"size-4"})})}function p(e){let{className:t,...s}=e;return(0,a.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...s,children:(0,a.jsx)(i.A,{className:"size-4"})})}},59434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>i,v:()=>n});var a=s(52596),r=s(39688);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}function n(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}},61579:(e,t,s)=>{Promise.resolve().then(s.bind(s,27944))},62523:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var a=s(95155);s(12115);var r=s(59434);function i(e){let{className:t,type:s,...i}=e;return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,r.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...i})}},66695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>o,Wu:()=>c,ZB:()=>l,Zp:()=>i,aR:()=>n,wL:()=>d});var a=s(95155);s(12115);var r=s(59434);function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",t),...s})}function n(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("flex flex-col gap-1.5 px-6",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...s})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6",t),...s})}},85057:(e,t,s)=>{"use strict";s.d(t,{J:()=>n});var a=s(95155);s(12115);var r=s(40968),i=s(59434);function n(e){let{className:t,...s}=e;return(0,a.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...s})}},88539:(e,t,s)=>{"use strict";s.d(t,{T:()=>i});var a=s(95155);s(12115);var r=s(59434);function i(e){let{className:t,...s}=e;return(0,a.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...s})}},99672:(e,t,s)=>{"use strict";s.d(t,{Dp:()=>c,EC:()=>n,J:()=>u,L_:()=>l,SA:()=>i,VO:()=>d,co:()=>o,h6:()=>r});var a=s(40026);let r=async()=>{let e=await fetch("".concat(a.H,"/api/organizations"),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!e.ok)throw Error((await e.json()).message||"Failed to fetch organizations");return e.json()},i=async e=>{let t=await fetch("".concat(a.H,"/api/organizations/").concat(e),{method:"GET",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch organization");return t.json()},n=async e=>{let t=await fetch("".concat(a.H,"/api/organizations"),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify(e)});if(!t.ok)throw Error((await t.json()).message||"Failed to create organization");return t.json()},l=async(e,t)=>{let s=await fetch("".concat(a.H,"/api/organizations/").concat(e),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify(t)});if(!s.ok)throw Error((await s.json()).message||"Failed to update organization");return s.json()},o=async(e,t)=>{let s=await fetch("".concat(a.H,"/api/organizations/").concat(e,"/billing"),{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify(t)});if(!s.ok)throw Error((await s.json()).message||"Failed to update organization billing");return s.json()},c=async e=>{let t=await fetch("".concat(a.H,"/api/organizations/").concat(e),{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!t.ok)throw Error((await t.json()).message||"Failed to delete organization");return t.json()},d=async(e,t,s)=>{let r=await fetch("".concat(a.H,"/api/organizations/").concat(e,"/users/").concat(t),{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))},body:JSON.stringify({isAdmin:s})});if(!r.ok)throw Error((await r.json()).message||"Failed to add user to organization");return r.json()},u=async(e,t)=>{let s=await fetch("".concat(a.H,"/api/organizations/").concat(e,"/users/").concat(t),{method:"DELETE",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(localStorage.getItem("access_token"))}});if(!s.ok)throw Error((await s.json()).message||"Failed to remove user from organization");return s.json()}}},e=>{var t=t=>e(e.s=t);e.O(0,[4201,4341,6403,6671,6544,5e3,8441,1684,7358],()=>t(61579)),_N_E=e.O()}]);