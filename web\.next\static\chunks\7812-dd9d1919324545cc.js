"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7812],{35169:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40968:(e,t,r)=>{r.d(t,{b:()=>a});var n=r(12115),o=r(63655),i=r(95155),l=n.forwardRef((e,t)=>(0,i.jsx)(o.sG.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null===(r=e.onMouseDown)||void 0===r||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var a=l},51154:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(19946).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},54073:(e,t,r)=>{r.d(t,{CC:()=>B,Q6:()=>O,bL:()=>U,zi:()=>X});var n=r(12115),o=r(89367),i=r(85185),l=r(6101),a=r(46081),u=r(5845),s=r(94315),d=r(45503),c=r(11275),f=r(63655),m=r(82284),p=r(95155),v=["PageUp","PageDown"],h=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],w={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},g="Slider",[b,y,x]=(0,m.N)(g),[S,D]=(0,a.A)(g,[x]),[R,A]=S(g),E=n.forwardRef((e,t)=>{let{name:r,min:l=0,max:a=100,step:s=1,orientation:d="horizontal",disabled:c=!1,minStepsBetweenThumbs:f=0,defaultValue:m=[l],value:w,onValueChange:g=()=>{},onValueCommit:y=()=>{},inverted:x=!1,form:S,...D}=e,A=n.useRef(new Set),E=n.useRef(0),j="horizontal"===d,[P=[],_]=(0,u.i)({prop:w,defaultProp:m,onChange:e=>{var t;null===(t=[...A.current][E.current])||void 0===t||t.focus(),g(e)}}),k=n.useRef(P);function C(e,t){let{commit:r}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{commit:!1},n=(String(s).split(".")[1]||"").length,i=function(e,t){let r=Math.pow(10,t);return Math.round(e*r)/r}(Math.round((e-l)/s)*s+l,n),u=(0,o.q)(i,[l,a]);_(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,r=arguments.length>2?arguments[2]:void 0,n=[...e];return n[r]=t,n.sort((e,t)=>e-t)}(e,u,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,r)=>e[r+1]-t))>=t;return!0}(n,f*s))return e;{E.current=n.indexOf(u);let t=String(n)!==String(e);return t&&r&&y(n),t?n:e}})}return(0,p.jsx)(R,{scope:e.__scopeSlider,name:r,disabled:c,min:l,max:a,valueIndexToChangeRef:E,thumbs:A.current,values:P,orientation:d,form:S,children:(0,p.jsx)(b.Provider,{scope:e.__scopeSlider,children:(0,p.jsx)(b.Slot,{scope:e.__scopeSlider,children:(0,p.jsx)(j?M:I,{"aria-disabled":c,"data-disabled":c?"":void 0,...D,ref:t,onPointerDown:(0,i.m)(D.onPointerDown,()=>{c||(k.current=P)}),min:l,max:a,inverted:x,onSlideStart:c?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let r=e.map(e=>Math.abs(e-t)),n=Math.min(...r);return r.indexOf(n)}(P,e);C(e,t)},onSlideMove:c?void 0:function(e){C(e,E.current)},onSlideEnd:c?void 0:function(){let e=k.current[E.current];P[E.current]!==e&&y(P)},onHomeKeyDown:()=>!c&&C(l,0,{commit:!0}),onEndKeyDown:()=>!c&&C(a,P.length-1,{commit:!0}),onStepKeyDown:e=>{let{event:t,direction:r}=e;if(!c){let e=v.includes(t.key)||t.shiftKey&&h.includes(t.key),n=E.current;C(P[n]+s*(e?10:1)*r,n,{commit:!0})}}})})})})});E.displayName=g;var[j,P]=S(g,{startEdge:"left",endEdge:"right",size:"width",direction:1}),M=n.forwardRef((e,t)=>{let{min:r,max:o,dir:i,inverted:a,onSlideStart:u,onSlideMove:d,onSlideEnd:c,onStepKeyDown:f,...m}=e,[v,h]=n.useState(null),g=(0,l.s)(t,e=>h(e)),b=n.useRef(void 0),y=(0,s.jH)(i),x="ltr"===y,S=x&&!a||!x&&a;function D(e){let t=b.current||v.getBoundingClientRect(),n=N([0,t.width],S?[r,o]:[o,r]);return b.current=t,n(e-t.left)}return(0,p.jsx)(j,{scope:e.__scopeSlider,startEdge:S?"left":"right",endEdge:S?"right":"left",direction:S?1:-1,size:"width",children:(0,p.jsx)(_,{dir:y,"data-orientation":"horizontal",...m,ref:g,style:{...m.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=D(e.clientX);null==u||u(t)},onSlideMove:e=>{let t=D(e.clientX);null==d||d(t)},onSlideEnd:()=>{b.current=void 0,null==c||c()},onStepKeyDown:e=>{let t=w[S?"from-left":"from-right"].includes(e.key);null==f||f({event:e,direction:t?-1:1})}})})}),I=n.forwardRef((e,t)=>{let{min:r,max:o,inverted:i,onSlideStart:a,onSlideMove:u,onSlideEnd:s,onStepKeyDown:d,...c}=e,f=n.useRef(null),m=(0,l.s)(t,f),v=n.useRef(void 0),h=!i;function g(e){let t=v.current||f.current.getBoundingClientRect(),n=N([0,t.height],h?[o,r]:[r,o]);return v.current=t,n(e-t.top)}return(0,p.jsx)(j,{scope:e.__scopeSlider,startEdge:h?"bottom":"top",endEdge:h?"top":"bottom",size:"height",direction:h?1:-1,children:(0,p.jsx)(_,{"data-orientation":"vertical",...c,ref:m,style:{...c.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=g(e.clientY);null==a||a(t)},onSlideMove:e=>{let t=g(e.clientY);null==u||u(t)},onSlideEnd:()=>{v.current=void 0,null==s||s()},onStepKeyDown:e=>{let t=w[h?"from-bottom":"from-top"].includes(e.key);null==d||d({event:e,direction:t?-1:1})}})})}),_=n.forwardRef((e,t)=>{let{__scopeSlider:r,onSlideStart:n,onSlideMove:o,onSlideEnd:l,onHomeKeyDown:a,onEndKeyDown:u,onStepKeyDown:s,...d}=e,c=A(g,r);return(0,p.jsx)(f.sG.span,{...d,ref:t,onKeyDown:(0,i.m)(e.onKeyDown,e=>{"Home"===e.key?(a(e),e.preventDefault()):"End"===e.key?(u(e),e.preventDefault()):v.concat(h).includes(e.key)&&(s(e),e.preventDefault())}),onPointerDown:(0,i.m)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),c.thumbs.has(t)?t.focus():n(e)}),onPointerMove:(0,i.m)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&o(e)}),onPointerUp:(0,i.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),l(e))})})}),k="SliderTrack",C=n.forwardRef((e,t)=>{let{__scopeSlider:r,...n}=e,o=A(k,r);return(0,p.jsx)(f.sG.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...n,ref:t})});C.displayName=k;var F="SliderRange",L=n.forwardRef((e,t)=>{let{__scopeSlider:r,...o}=e,i=A(F,r),a=P(F,r),u=n.useRef(null),s=(0,l.s)(t,u),d=i.values.length,c=i.values.map(e=>H(e,i.min,i.max)),m=d>1?Math.min(...c):0,v=100-Math.max(...c);return(0,p.jsx)(f.sG.span,{"data-orientation":i.orientation,"data-disabled":i.disabled?"":void 0,...o,ref:s,style:{...e.style,[a.startEdge]:m+"%",[a.endEdge]:v+"%"}})});L.displayName=F;var K="SliderThumb",T=n.forwardRef((e,t)=>{let r=y(e.__scopeSlider),[o,i]=n.useState(null),a=(0,l.s)(t,e=>i(e)),u=n.useMemo(()=>o?r().findIndex(e=>e.ref.current===o):-1,[r,o]);return(0,p.jsx)(G,{...e,ref:a,index:u})}),G=n.forwardRef((e,t)=>{let{__scopeSlider:r,index:o,name:a,...u}=e,s=A(K,r),d=P(K,r),[m,v]=n.useState(null),h=(0,l.s)(t,e=>v(e)),w=!m||s.form||!!m.closest("form"),g=(0,c.X)(m),y=s.values[o],x=void 0===y?0:H(y,s.min,s.max),S=function(e,t){return t>2?"Value ".concat(e+1," of ").concat(t):2===t?["Minimum","Maximum"][e]:void 0}(o,s.values.length),D=null==g?void 0:g[d.size],R=D?function(e,t,r){let n=e/2,o=N([0,50],[0,n]);return(n-o(t)*r)*r}(D,x,d.direction):0;return n.useEffect(()=>{if(m)return s.thumbs.add(m),()=>{s.thumbs.delete(m)}},[m,s.thumbs]),(0,p.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[d.startEdge]:"calc(".concat(x,"% + ").concat(R,"px)")},children:[(0,p.jsx)(b.ItemSlot,{scope:e.__scopeSlider,children:(0,p.jsx)(f.sG.span,{role:"slider","aria-label":e["aria-label"]||S,"aria-valuemin":s.min,"aria-valuenow":y,"aria-valuemax":s.max,"aria-orientation":s.orientation,"data-orientation":s.orientation,"data-disabled":s.disabled?"":void 0,tabIndex:s.disabled?void 0:0,...u,ref:h,style:void 0===y?{display:"none"}:e.style,onFocus:(0,i.m)(e.onFocus,()=>{s.valueIndexToChangeRef.current=o})})}),w&&(0,p.jsx)(z,{name:null!=a?a:s.name?s.name+(s.values.length>1?"[]":""):void 0,form:s.form,value:y},o)]})});T.displayName=K;var z=e=>{let{value:t,...r}=e,o=n.useRef(null),i=(0,d.Z)(t);return n.useEffect(()=>{let e=o.current,r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(i!==t&&r){let n=new Event("input",{bubbles:!0});r.call(e,t),e.dispatchEvent(n)}},[i,t]),(0,p.jsx)("input",{style:{display:"none"},...r,ref:o,defaultValue:t})};function H(e,t,r){return(0,o.q)(100/(r-t)*(e-t),[0,100])}function N(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var U=E,B=C,O=L,X=T},89196:(e,t,r)=>{r.d(t,{RG:()=>x,bL:()=>I,q7:()=>_});var n=r(12115),o=r(85185),i=r(82284),l=r(6101),a=r(46081),u=r(61285),s=r(63655),d=r(39033),c=r(5845),f=r(94315),m=r(95155),p="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},h="RovingFocusGroup",[w,g,b]=(0,i.N)(h),[y,x]=(0,a.A)(h,[b]),[S,D]=y(h),R=n.forwardRef((e,t)=>(0,m.jsx)(w.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(w.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(A,{...e,ref:t})})}));R.displayName=h;var A=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:i,loop:a=!1,dir:u,currentTabStopId:h,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:b,onEntryFocus:y,preventScrollOnEntryFocus:x=!1,...D}=e,R=n.useRef(null),A=(0,l.s)(t,R),E=(0,f.jH)(u),[j=null,P]=(0,c.i)({prop:h,defaultProp:w,onChange:b}),[I,_]=n.useState(!1),k=(0,d.c)(y),C=g(r),F=n.useRef(!1),[L,K]=n.useState(0);return n.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(p,k),()=>e.removeEventListener(p,k)},[k]),(0,m.jsx)(S,{scope:r,orientation:i,dir:E,loop:a,currentTabStopId:j,onItemFocus:n.useCallback(e=>P(e),[P]),onItemShiftTab:n.useCallback(()=>_(!0),[]),onFocusableItemAdd:n.useCallback(()=>K(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>K(e=>e-1),[]),children:(0,m.jsx)(s.sG.div,{tabIndex:I||0===L?-1:0,"data-orientation":i,...D,ref:A,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{F.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!F.current;if(e.target===e.currentTarget&&t&&!I){let t=new CustomEvent(p,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=C().filter(e=>e.focusable);M([e.find(e=>e.active),e.find(e=>e.id===j),...e].filter(Boolean).map(e=>e.ref.current),x)}}F.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>_(!1))})})}),E="RovingFocusGroupItem",j=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:i=!0,active:l=!1,tabStopId:a,...d}=e,c=(0,u.B)(),f=a||c,p=D(E,r),v=p.currentTabStopId===f,h=g(r),{onFocusableItemAdd:b,onFocusableItemRemove:y}=p;return n.useEffect(()=>{if(i)return b(),()=>y()},[i,b,y]),(0,m.jsx)(w.ItemSlot,{scope:r,id:f,focusable:i,active:l,children:(0,m.jsx)(s.sG.span,{tabIndex:v?0:-1,"data-orientation":p.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{i?p.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let o=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return P[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=h().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=p.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>M(r))}})})})});j.displayName=E;var P={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function M(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var I=R,_=j}}]);