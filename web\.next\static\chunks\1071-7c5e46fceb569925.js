"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1071],{15452:(e,n,t)=>{t.d(n,{G$:()=>X,Hs:()=>O,UC:()=>en,VY:()=>er,ZL:()=>Q,bL:()=>$,bm:()=>eo,hE:()=>et,hJ:()=>ee,l9:()=>z,lG:()=>w});var r=t(12115),o=t(85185),i=t(6101),a=t(46081),l=t(61285),s=t(5845),u=t(19178),d=t(25519),c=t(34378),f=t(28905),p=t(63655),m=t(92293),g=t(93795),v=t(38168),N=t(99708),y=t(95155),D="Dialog",[h,O]=(0,a.A)(D),[R,b]=h(D),w=e=>{let{__scopeDialog:n,children:t,open:o,defaultOpen:i,onOpenChange:a,modal:u=!0}=e,d=r.useRef(null),c=r.useRef(null),[f=!1,p]=(0,s.i)({prop:o,defaultProp:i,onChange:a});return(0,y.jsx)(R,{scope:n,triggerRef:d,contentRef:c,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:u,children:t})};w.displayName=D;var C="DialogTrigger",I=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,a=b(C,t),l=(0,i.s)(n,a.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":Z(a.open),...r,ref:l,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});I.displayName=C;var E="DialogPortal",[j,x]=h(E,{forceMount:void 0}),A=e=>{let{__scopeDialog:n,forceMount:t,children:o,container:i}=e,a=b(E,n);return(0,y.jsx)(j,{scope:n,forceMount:t,children:r.Children.map(o,e=>(0,y.jsx)(f.C,{present:t||a.open,children:(0,y.jsx)(c.Z,{asChild:!0,container:i,children:e})}))})};A.displayName=E;var M="DialogOverlay",_=r.forwardRef((e,n)=>{let t=x(M,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,i=b(M,e.__scopeDialog);return i.modal?(0,y.jsx)(f.C,{present:r||i.open,children:(0,y.jsx)(T,{...o,ref:n})}):null});_.displayName=M;var T=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=b(M,t);return(0,y.jsx)(g.A,{as:N.DX,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":Z(o.open),...r,ref:n,style:{pointerEvents:"auto",...r.style}})})}),F="DialogContent",P=r.forwardRef((e,n)=>{let t=x(F,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,i=b(F,e.__scopeDialog);return(0,y.jsx)(f.C,{present:r||i.open,children:i.modal?(0,y.jsx)(k,{...o,ref:n}):(0,y.jsx)(U,{...o,ref:n})})});P.displayName=F;var k=r.forwardRef((e,n)=>{let t=b(F,e.__scopeDialog),a=r.useRef(null),l=(0,i.s)(n,t.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(L,{...e,ref:l,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var n;e.preventDefault(),null===(n=t.triggerRef.current)||void 0===n||n.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let n=e.detail.originalEvent,t=0===n.button&&!0===n.ctrlKey;(2===n.button||t)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),U=r.forwardRef((e,n)=>{let t=b(F,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,y.jsx)(L,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:n=>{var r,a;null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,n),n.defaultPrevented||(o.current||null===(a=t.triggerRef.current)||void 0===a||a.focus(),n.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:n=>{var r,a;null===(r=e.onInteractOutside)||void 0===r||r.call(e,n),n.defaultPrevented||(o.current=!0,"pointerdown"!==n.detail.originalEvent.type||(i.current=!0));let l=n.target;(null===(a=t.triggerRef.current)||void 0===a?void 0:a.contains(l))&&n.preventDefault(),"focusin"===n.detail.originalEvent.type&&i.current&&n.preventDefault()}})}),L=r.forwardRef((e,n)=>{let{__scopeDialog:t,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:l,...s}=e,c=b(F,t),f=r.useRef(null),p=(0,i.s)(n,f);return(0,m.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:l,children:(0,y.jsx)(u.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":Z(c.open),...s,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(K,{titleId:c.titleId}),(0,y.jsx)(Y,{contentRef:f,descriptionId:c.descriptionId})]})]})}),W="DialogTitle",G=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=b(W,t);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...r,ref:n})});G.displayName=W;var S="DialogDescription",B=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=b(S,t);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:n})});B.displayName=S;var q="DialogClose",V=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,i=b(q,t);return(0,y.jsx)(p.sG.button,{type:"button",...r,ref:n,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}V.displayName=q;var H="DialogTitleWarning",[X,J]=(0,a.q)(H,{contentName:F,titleName:W,docsSlug:"dialog"}),K=e=>{let{titleId:n}=e,t=J(H),o="`".concat(t.contentName,"` requires a `").concat(t.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(t.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(t.docsSlug);return r.useEffect(()=>{n&&!document.getElementById(n)&&console.error(o)},[o,n]),null},Y=e=>{let{contentRef:n,descriptionId:t}=e,o=J("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null===(e=n.current)||void 0===e?void 0:e.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(i)},[i,n,t]),null},$=w,z=I,Q=A,ee=_,en=P,et=G,er=B,eo=V},28905:(e,n,t)=>{t.d(n,{C:()=>a});var r=t(12115),o=t(6101),i=t(52712),a=e=>{let{present:n,children:t}=e,a=function(e){var n,t;let[o,a]=r.useState(),s=r.useRef({}),u=r.useRef(e),d=r.useRef("none"),[c,f]=(n=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,n)=>{let r=t[e][n];return null!=r?r:e},n));return r.useEffect(()=>{let e=l(s.current);d.current="mounted"===c?e:"none"},[c]),(0,i.N)(()=>{let n=s.current,t=u.current;if(t!==e){let r=d.current,o=l(n);e?f("MOUNT"):"none"===o||(null==n?void 0:n.display)==="none"?f("UNMOUNT"):t&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,i.N)(()=>{if(o){var e;let n;let t=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=l(s.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",n=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(d.current=l(s.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(n),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{e&&(s.current=getComputedStyle(e)),a(e)},[])}}(n),s="function"==typeof t?t({present:a.isPresent}):r.Children.only(t),u=(0,o.s)(a.ref,function(e){var n,t;let r=null===(n=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===n?void 0:n.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(t=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof t||a.isPresent?r.cloneElement(s,{ref:u}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence"},54416:(e,n,t)=>{t.d(n,{A:()=>r});let r=(0,t(19946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])}}]);