(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8809],{4229:(e,a,n)=>{"use strict";n.d(a,{A:()=>s});let s=(0,n(19946).A)("Save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},30206:(e,a,n)=>{"use strict";n.d(a,{default:()=>u});var s=n(95155),r=n(30285),t=n(85127),l=n(47924),i=n(85690),d=n(4229),o=n(12115);let c=[{id:"1",name:"<PERSON>",gender:"Male",language:"us English",platform:"Eleven Labs",credits:null},{id:"2",name:"Matt",gender:"Male",language:"us English",platform:"Eleven Labs",credits:null},{id:"3",name:"Blondie - Saleswoman",gender:"Female",language:"us English",platform:"Eleven Labs",credits:null},{id:"4",name:"Blondie - Conversational",gender:"Female",language:"us English",platform:"Eleven Labs",credits:null},{id:"5",name:"Peter I The GREAT",gender:"Male",language:"pl Polish",platform:"Eleven Labs",credits:"4x credits"},{id:"6",name:"Mike - Aussie Entrepreneur",gender:"Male",language:"us English",platform:"Eleven Labs",credits:null},{id:"7",name:"Monika Sogam - Friendly Customer Care Voice",gender:"Female",language:"us English",platform:"Eleven Labs",credits:null},{id:"8",name:"Remi",gender:"Male",language:"de German",platform:"Eleven Labs",credits:null},{id:"9",name:"Talkative Joe: Lively British RP Male Voice for Engaging Conversations",gender:"Male",language:"us English",platform:"Eleven Labs",credits:"4x credits"},{id:"10",name:"Jin",gender:"Male",language:"us English",platform:"Eleven Labs",credits:null},{id:"11",name:"Ana Maria",gender:"Female",language:"ro Romanian",platform:"Eleven Labs",credits:null},{id:"12",name:"Carter D",gender:"Male",language:"us English",platform:"Eleven Labs",credits:null},{id:"13",name:"Diego Alez - Professional, narrative and conversational",gender:"Male",language:"es Spanish",platform:"Eleven Labs",credits:"3x credits"}];function u(){let[e,a]=(0,o.useState)(!1),[n,u]=(0,o.useState)(""),[m,g]=(0,o.useState)(c),[h,v]=(0,o.useState)(null),x=m.filter(e=>!n||e.name.toLowerCase().includes(n.toLowerCase())||e.language.toLowerCase().includes(n.toLowerCase())||e.platform.toLowerCase().includes(n.toLowerCase())),b=e=>{v(e),setTimeout(()=>{v(null)},2e3)};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h1",{className:"text-2xl font-semibold",children:"Voices"}),(0,s.jsx)("div",{className:"flex gap-2",children:e?(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("input",{type:"text",value:n,onChange:e=>u(e.target.value),placeholder:"Search voices...",className:"h-10 px-3 pr-10 rounded-md border border-input bg-background text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",autoFocus:!0}),(0,s.jsx)("button",{className:"absolute right-0 top-0 h-full px-3",onClick:()=>{a(!1),u("")},children:(0,s.jsx)(l.A,{className:"h-4 w-4 text-muted-foreground"})})]}):(0,s.jsx)(r.$,{variant:"outline",className:"flex items-center gap-2",onClick:()=>a(!0),children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})})]}),(0,s.jsx)("div",{className:"bg-card rounded-lg border shadow-sm overflow-hidden",children:(0,s.jsxs)(t.XI,{children:[(0,s.jsx)(t.A0,{children:(0,s.jsxs)(t.Hj,{children:[(0,s.jsx)(t.nd,{children:"Name"}),(0,s.jsx)(t.nd,{className:"hidden md:table-cell",children:"Gender"}),(0,s.jsx)(t.nd,{children:"Language"}),(0,s.jsx)(t.nd,{children:"Actions"})]})}),(0,s.jsxs)(t.BF,{children:[x.map(e=>(0,s.jsxs)(t.Hj,{children:[(0,s.jsx)(t.nA,{className:"font-medium",children:e.name}),(0,s.jsx)(t.nA,{className:"hidden md:table-cell",children:e.gender}),(0,s.jsx)(t.nA,{children:e.language}),(0,s.jsx)(t.nA,{children:(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-1",children:[(0,s.jsxs)(r.$,{size:"sm",variant:"outline",className:"h-8 px-2",onClick:()=>b(e.id),disabled:h===e.id,children:[h===e.id?(0,s.jsx)("div",{className:"h-3 w-3 rounded-full bg-primary animate-pulse mr-1"}):(0,s.jsx)(i.A,{className:"h-3 w-3 mr-1"}),"Preview"]}),(0,s.jsxs)(r.$,{size:"sm",variant:"outline",className:"h-8 px-2",children:[(0,s.jsx)(d.A,{className:"h-3 w-3 mr-1"})," Save"]})]})})]},e.id)),0===x.length&&(0,s.jsx)(t.Hj,{children:(0,s.jsxs)(t.nA,{colSpan:5,className:"h-60 text-center",children:[(0,s.jsx)("p",{className:"text-lg font-medium text-muted-foreground",children:"No matching voices found"}),(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:"Try adjusting your search terms"})]})})]})]})})]})}},30285:(e,a,n)=>{"use strict";n.d(a,{$:()=>d,r:()=>i});var s=n(95155);n(12115);var r=n(99708),t=n(74466),l=n(59434);let i=(0,t.F)("inline-flex items-center cursor-pointer justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:a,variant:n,size:t,asChild:d=!1,...o}=e,c=d?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,l.cn)(i({variant:n,size:t,className:a})),...o})}},47924:(e,a,n)=>{"use strict";n.d(a,{A:()=>s});let s=(0,n(19946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},59434:(e,a,n)=>{"use strict";n.d(a,{cn:()=>t,v:()=>l});var s=n(52596),r=n(39688);function t(){for(var e=arguments.length,a=Array(e),n=0;n<e;n++)a[n]=arguments[n];return(0,r.QP)((0,s.$)(a))}function l(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}},74466:(e,a,n)=>{"use strict";n.d(a,{F:()=>l});var s=n(52596);let r=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,t=s.$,l=(e,a)=>n=>{var s;if((null==a?void 0:a.variants)==null)return t(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:l,defaultVariants:i}=a,d=Object.keys(l).map(e=>{let a=null==n?void 0:n[e],s=null==i?void 0:i[e];if(null===a)return null;let t=r(a)||r(s);return l[e][t]}),o=n&&Object.entries(n).reduce((e,a)=>{let[n,s]=a;return void 0===s||(e[n]=s),e},{});return t(e,d,null==a?void 0:null===(s=a.compoundVariants)||void 0===s?void 0:s.reduce((e,a)=>{let{class:n,className:s,...r}=a;return Object.entries(r).every(e=>{let[a,n]=e;return Array.isArray(n)?n.includes({...i,...o}[a]):({...i,...o})[a]===n})?[...e,n,s]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},76817:(e,a,n)=>{Promise.resolve().then(n.bind(n,30206))},85127:(e,a,n)=>{"use strict";n.d(a,{A0:()=>l,BF:()=>i,Hj:()=>d,XI:()=>t,nA:()=>c,nd:()=>o});var s=n(95155);n(12115);var r=n(59434);function t(e){let{className:a,...n}=e;return(0,s.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,s.jsx)("table",{"data-slot":"table",className:(0,r.cn)("w-full caption-bottom text-sm",a),...n})})}function l(e){let{className:a,...n}=e;return(0,s.jsx)("thead",{"data-slot":"table-header",className:(0,r.cn)("[&_tr]:border-b",a),...n})}function i(e){let{className:a,...n}=e;return(0,s.jsx)("tbody",{"data-slot":"table-body",className:(0,r.cn)("[&_tr:last-child]:border-0",a),...n})}function d(e){let{className:a,...n}=e;return(0,s.jsx)("tr",{"data-slot":"table-row",className:(0,r.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",a),...n})}function o(e){let{className:a,...n}=e;return(0,s.jsx)("th",{"data-slot":"table-head",className:(0,r.cn)("text-muted-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...n})}function c(e){let{className:a,...n}=e;return(0,s.jsx)("td",{"data-slot":"table-cell",className:(0,r.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...n})}},85690:(e,a,n)=>{"use strict";n.d(a,{A:()=>s});let s=(0,n(19946).A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])}},e=>{var a=a=>e(e.s=a);e.O(0,[4201,8441,1684,7358],()=>a(76817)),_N_E=e.O()}]);