(()=>{var e={};e.id=257,e.ids=[257],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5336:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},9941:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(37413);r(61120);var s=r(95090);function a({params:e}){return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(s.default,{agentId:e.id})})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10940:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c});var n=r(65239),s=r(48088),a=r(88170),o=r.n(a),l=r(30893),i={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>l[e]);r.d(t,i);let c={children:["",{children:["(workspace)",{children:["agents",{children:["edit",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9941)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\edit\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,50184)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\edit\\[id]\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/(workspace)/agents/edit/[id]/page",pathname:"/agents/edit/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},17908:(e,t,r)=>{"use strict";r.d(t,{a:()=>l});var n=r(8693),s=r(51423),a=r(54050);async function o(){let e=localStorage.getItem("access_token"),t=await fetch("http://localhost:4000/api/agents",{headers:{Authorization:`Bearer ${e}`}});if(!t.ok)throw Error("Failed to fetch agents");return t.json()}function l(){let e=(0,n.jE)(),{data:t=[],isLoading:r,error:l}=(0,s.I)({queryKey:["agents"],queryFn:o,staleTime:1/0,gcTime:1/0}),{mutate:i}=(0,a.n)({mutationFn:e=>Promise.resolve(e),onSuccess:t=>{e.setQueryData(["agents"],t)}});return{agents:t,setAgents:i,agentsisLoading:r,deleteAgentMutation:(0,a.n)({mutationFn:async e=>{let t=localStorage.getItem("access_token");if(!t)throw Error("Authentication required");let r=await fetch(`http://localhost:4000/api/agents/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`}});if(!r.ok)throw Error(`Failed to delete agent: ${r.status}`)},onMutate:async t=>{await e.cancelQueries({queryKey:["agents"]});let r=e.getQueryData(["agents"]);return e.setQueryData(["agents"],e=>e?.filter(e=>e.id!==t)??[]),{previousAgents:r}},onSuccess:(t,r)=>{e.invalidateQueries({queryKey:["agents"]}),e.removeQueries({queryKey:["agent",r]})}}),AgentsError:l instanceof Error?l.message:null}}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24007:(e,t,r)=>{"use strict";r.d(t,{default:()=>eb});var n=r(60687),s=r(43210),a=r(16189),o=r(28559),l=r(83753),i=r(5336),c=r(93613),d=r(29523),u=r(85763),p=r(34688),m=r(93187),h=r(22758),f=r(63503),v=r(87979),g=r(21696),x=r(283),b=r(45685),w=r(78885),y=r(32584);function j({currentAgentId:e,userRole:t,agents:r,agentsisLoading:s}){let o=(0,a.useRouter)(),l=r.filter(e=>"superadmin"===t||"active"===e.status),i=t=>{t!==e&&o.push(`/agents/edit/${t}`)};return(0,n.jsxs)("div",{className:"sticky top-4 w-80 h-[calc(100vh-5rem)] min-h-[calc(100vh-4rem)] border-1 border-border bg-card rounded-lg mt-2",children:[(0,n.jsx)("div",{className:"p-4 border-b-2 flex items-center gap-2",children:(0,n.jsx)("h2",{className:"font-semibold text-lg",children:"Agents"})}),(0,n.jsx)("div",{className:"overflow-y-auto h-[calc(100%-4rem)] scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600",children:s?[,,,,,].fill(0).map((e,t)=>(0,n.jsx)("div",{className:"p-3 border-b",children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsx)("div",{className:"w-10 h-10 rounded-full bg-gray-200 dark:bg-gray-700 animate-pulse"}),(0,n.jsxs)("div",{className:"flex-1 space-y-2",children:[(0,n.jsx)("div",{className:"h-4 w-24 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"}),(0,n.jsx)("div",{className:"h-3 w-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"})]})]})},t)):l.map(t=>(0,n.jsx)("div",{onClick:()=>i(t.id),className:`p-3 border-b hover:bg-accent/50 transition-colors cursor-pointer ${t.id===e?"bg-accent":""}`,children:(0,n.jsxs)("div",{className:"flex items-center gap-3",children:[(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsxs)(y.eu,{className:"h-15 w-15",children:[(0,n.jsx)(y.BK,{src:t.avatar}),(0,n.jsx)(y.q5,{children:t.name.slice(0,2).toUpperCase()})]}),(0,n.jsx)("div",{className:`absolute bottom-0 right-0 h-2.5 w-2.5 rounded-full border-2 border-white dark:border-gray-800 ${"active"===t.status?"bg-green-500":"bg-gray-400"}`})]}),(0,n.jsxs)("div",{className:"flex flex-col min-w-0",children:[(0,n.jsx)("span",{className:"font-medium truncate text-base",children:t.name}),(0,n.jsx)("span",{className:"text-sm truncate text-gray-700 dark:text-gray-200",children:t.role||"Assistant"})]})]})},t.id))})]})}var N=r(17908);function C(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function S(...e){return t=>{let r=!1,n=e.map(e=>{let n=C(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():C(e[t],null)}}}}function E(...e){return s.useCallback(S(...e),e)}r(51215);var A=Symbol("radix.slottable");function k(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===A}var P=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=function(e){let t=function(e){let t=s.forwardRef((e,t)=>{let{children:r,...n}=e;if(s.isValidElement(r)){var a;let e,o;let l=(a=r,(o=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(o=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),i=function(e,t){let r={...t};for(let n in t){let s=e[n],a=t[n];/^on[A-Z]/.test(n)?s&&a?r[n]=(...e)=>{let t=a(...e);return s(...e),t}:s&&(r[n]=s):"style"===n?r[n]={...s,...a}:"className"===n&&(r[n]=[s,a].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==s.Fragment&&(i.ref=t?S(t,l):l),s.cloneElement(r,i)}return s.Children.count(r)>1?s.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=s.forwardRef((e,r)=>{let{children:a,...o}=e,l=s.Children.toArray(a),i=l.find(k);if(i){let e=i.props.children,a=l.map(t=>t!==i?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...o,ref:r,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,n.jsx)(t,{...o,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}(`Primitive.${t}`),a=s.forwardRef((e,s)=>{let{asChild:a,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(a?r:t,{...o,ref:s})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{}),T=globalThis?.document?s.useLayoutEffect:()=>{},R=e=>{let{present:t,children:r}=e,n=function(e){var t,r;let[n,a]=s.useState(),o=s.useRef(null),l=s.useRef(e),i=s.useRef("none"),[c,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},s.useReducer((e,t)=>r[e][t]??e,t));return s.useEffect(()=>{let e=_(o.current);i.current="mounted"===c?e:"none"},[c]),T(()=>{let t=o.current,r=l.current;if(r!==e){let n=i.current,s=_(t);e?d("MOUNT"):"none"===s||t?.display==="none"?d("UNMOUNT"):r&&n!==s?d("ANIMATION_OUT"):d("UNMOUNT"),l.current=e}},[e,d]),T(()=>{if(n){let e;let t=n.ownerDocument.defaultView??window,r=r=>{let s=_(o.current).includes(r.animationName);if(r.target===n&&s&&(d("ANIMATION_END"),!l.current)){let r=n.style.animationFillMode;n.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=r)})}},s=e=>{e.target===n&&(i.current=_(o.current))};return n.addEventListener("animationstart",s),n.addEventListener("animationcancel",r),n.addEventListener("animationend",r),()=>{t.clearTimeout(e),n.removeEventListener("animationstart",s),n.removeEventListener("animationcancel",r),n.removeEventListener("animationend",r)}}d("ANIMATION_END")},[n,d]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:s.useCallback(e=>{o.current=e?getComputedStyle(e):null,a(e)},[])}}(t),a="function"==typeof r?r({present:n.isPresent}):s.Children.only(r),o=E(n.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof r||n.isPresent?s.cloneElement(a,{ref:o}):null};function _(e){return e?.animationName||"none"}function O(e){let t=s.useRef(e);return s.useEffect(()=>{t.current=e}),s.useMemo(()=>(...e)=>t.current?.(...e),[])}R.displayName="Presence";var L=s.createContext(void 0);function D(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}var q="ScrollArea",[I,U]=function(e,t=[]){let r=[],a=()=>{let t=r.map(e=>s.createContext(e));return function(r){let n=r?.[e]||t;return s.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return a.scopeName=e,[function(t,a){let o=s.createContext(a),l=r.length;r=[...r,a];let i=t=>{let{scope:r,children:a,...i}=t,c=r?.[e]?.[l]||o,d=s.useMemo(()=>i,Object.values(i));return(0,n.jsx)(c.Provider,{value:d,children:a})};return i.displayName=t+"Provider",[i,function(r,n){let i=n?.[e]?.[l]||o,c=s.useContext(i);if(c)return c;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let s=r(e)[`__scope${n}`];return{...t,...s}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(a,...t)]}(q),[M,W]=I(q),z=s.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:a="hover",dir:o,scrollHideDelay:l=600,...i}=e,[c,d]=s.useState(null),[u,p]=s.useState(null),[m,h]=s.useState(null),[f,v]=s.useState(null),[g,x]=s.useState(null),[b,w]=s.useState(0),[y,j]=s.useState(0),[N,C]=s.useState(!1),[S,A]=s.useState(!1),k=E(t,e=>d(e)),T=function(e){let t=s.useContext(L);return e||t||"ltr"}(o);return(0,n.jsx)(M,{scope:r,type:a,dir:T,scrollHideDelay:l,scrollArea:c,viewport:u,onViewportChange:p,content:m,onContentChange:h,scrollbarX:f,onScrollbarXChange:v,scrollbarXEnabled:N,onScrollbarXEnabledChange:C,scrollbarY:g,onScrollbarYChange:x,scrollbarYEnabled:S,onScrollbarYEnabledChange:A,onCornerWidthChange:w,onCornerHeightChange:j,children:(0,n.jsx)(P.div,{dir:T,...i,ref:k,style:{position:"relative","--radix-scroll-area-corner-width":b+"px","--radix-scroll-area-corner-height":y+"px",...e.style}})})});z.displayName=q;var $="ScrollAreaViewport",B=s.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:a,nonce:o,...l}=e,i=W($,r),c=E(t,s.useRef(null),i.onViewportChange);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,n.jsx)(P.div,{"data-radix-scroll-area-viewport":"",...l,ref:c,style:{overflowX:i.scrollbarXEnabled?"scroll":"hidden",overflowY:i.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,n.jsx)("div",{ref:i.onContentChange,style:{minWidth:"100%",display:"table"},children:a})})]})});B.displayName=$;var F="ScrollAreaScrollbar",V=s.forwardRef((e,t)=>{let{forceMount:r,...a}=e,o=W(F,e.__scopeScrollArea),{onScrollbarXEnabledChange:l,onScrollbarYEnabledChange:i}=o,c="horizontal"===e.orientation;return s.useEffect(()=>(c?l(!0):i(!0),()=>{c?l(!1):i(!1)}),[c,l,i]),"hover"===o.type?(0,n.jsx)(H,{...a,ref:t,forceMount:r}):"scroll"===o.type?(0,n.jsx)(X,{...a,ref:t,forceMount:r}):"auto"===o.type?(0,n.jsx)(Y,{...a,ref:t,forceMount:r}):"always"===o.type?(0,n.jsx)(G,{...a,ref:t}):null});V.displayName=F;var H=s.forwardRef((e,t)=>{let{forceMount:r,...a}=e,o=W(F,e.__scopeScrollArea),[l,i]=s.useState(!1);return s.useEffect(()=>{let e=o.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),i(!0)},n=()=>{t=window.setTimeout(()=>i(!1),o.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[o.scrollArea,o.scrollHideDelay]),(0,n.jsx)(R,{present:r||l,children:(0,n.jsx)(Y,{"data-state":l?"visible":"hidden",...a,ref:t})})}),X=s.forwardRef((e,t)=>{var r;let{forceMount:a,...o}=e,l=W(F,e.__scopeScrollArea),i="horizontal"===e.orientation,c=em(()=>u("SCROLL_END"),100),[d,u]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},s.useReducer((e,t)=>r[e][t]??e,"hidden"));return s.useEffect(()=>{if("idle"===d){let e=window.setTimeout(()=>u("HIDE"),l.scrollHideDelay);return()=>window.clearTimeout(e)}},[d,l.scrollHideDelay,u]),s.useEffect(()=>{let e=l.viewport,t=i?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(u("SCROLL"),c()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[l.viewport,i,u,c]),(0,n.jsx)(R,{present:a||"hidden"!==d,children:(0,n.jsx)(G,{"data-state":"hidden"===d?"hidden":"visible",...o,ref:t,onPointerEnter:D(e.onPointerEnter,()=>u("POINTER_ENTER")),onPointerLeave:D(e.onPointerLeave,()=>u("POINTER_LEAVE"))})})}),Y=s.forwardRef((e,t)=>{let r=W(F,e.__scopeScrollArea),{forceMount:a,...o}=e,[l,i]=s.useState(!1),c="horizontal"===e.orientation,d=em(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;i(c?e:t)}},10);return eh(r.viewport,d),eh(r.content,d),(0,n.jsx)(R,{present:a||l,children:(0,n.jsx)(G,{"data-state":l?"visible":"hidden",...o,ref:t})})}),G=s.forwardRef((e,t)=>{let{orientation:r="vertical",...a}=e,o=W(F,e.__scopeScrollArea),l=s.useRef(null),i=s.useRef(0),[c,d]=s.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=ei(c.viewport,c.content),p={...a,sizes:c,onSizesChange:d,hasThumb:!!(u>0&&u<1),onThumbChange:e=>l.current=e,onThumbPointerUp:()=>i.current=0,onThumbPointerDown:e=>i.current=e};function m(e,t){return function(e,t,r,n="ltr"){let s=ec(r),a=t||s/2,o=r.scrollbar.paddingStart+a,l=r.scrollbar.size-r.scrollbar.paddingEnd-(s-a),i=r.content-r.viewport;return eu([o,l],"ltr"===n?[0,i]:[-1*i,0])(e)}(e,i.current,c,t)}return"horizontal"===r?(0,n.jsx)(K,{...p,ref:t,onThumbPositionChange:()=>{if(o.viewport&&l.current){let e=ed(o.viewport.scrollLeft,c,o.dir);l.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{o.viewport&&(o.viewport.scrollLeft=e)},onDragScroll:e=>{o.viewport&&(o.viewport.scrollLeft=m(e,o.dir))}}):"vertical"===r?(0,n.jsx)(Q,{...p,ref:t,onThumbPositionChange:()=>{if(o.viewport&&l.current){let e=ed(o.viewport.scrollTop,c);l.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{o.viewport&&(o.viewport.scrollTop=e)},onDragScroll:e=>{o.viewport&&(o.viewport.scrollTop=m(e))}}):null}),K=s.forwardRef((e,t)=>{let{sizes:r,onSizesChange:a,...o}=e,l=W(F,e.__scopeScrollArea),[i,c]=s.useState(),d=s.useRef(null),u=E(t,d,l.onScrollbarXChange);return s.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,n.jsx)(ee,{"data-orientation":"horizontal",...o,ref:u,sizes:r,style:{bottom:0,left:"rtl"===l.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===l.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":ec(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(l.viewport){let n=l.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&l.viewport&&i&&a({content:l.viewport.scrollWidth,viewport:l.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:el(i.paddingLeft),paddingEnd:el(i.paddingRight)}})}})}),Q=s.forwardRef((e,t)=>{let{sizes:r,onSizesChange:a,...o}=e,l=W(F,e.__scopeScrollArea),[i,c]=s.useState(),d=s.useRef(null),u=E(t,d,l.onScrollbarYChange);return s.useEffect(()=>{d.current&&c(getComputedStyle(d.current))},[d]),(0,n.jsx)(ee,{"data-orientation":"vertical",...o,ref:u,sizes:r,style:{top:0,right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":ec(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(l.viewport){let n=l.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&l.viewport&&i&&a({content:l.viewport.scrollHeight,viewport:l.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:el(i.paddingTop),paddingEnd:el(i.paddingBottom)}})}})}),[Z,J]=I(F),ee=s.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:a,hasThumb:o,onThumbChange:l,onThumbPointerUp:i,onThumbPointerDown:c,onThumbPositionChange:d,onDragScroll:u,onWheelScroll:p,onResize:m,...h}=e,f=W(F,r),[v,g]=s.useState(null),x=E(t,e=>g(e)),b=s.useRef(null),w=s.useRef(""),y=f.viewport,j=a.content-a.viewport,N=O(p),C=O(d),S=em(m,10);function A(e){b.current&&u({x:e.clientX-b.current.left,y:e.clientY-b.current.top})}return s.useEffect(()=>{let e=e=>{let t=e.target;v?.contains(t)&&N(e,j)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[y,v,j,N]),s.useEffect(C,[a,C]),eh(v,S),eh(f.content,S),(0,n.jsx)(Z,{scope:r,scrollbar:v,hasThumb:o,onThumbChange:O(l),onThumbPointerUp:O(i),onThumbPositionChange:C,onThumbPointerDown:O(c),children:(0,n.jsx)(P.div,{...h,ref:x,style:{position:"absolute",...h.style},onPointerDown:D(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),b.current=v.getBoundingClientRect(),w.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",f.viewport&&(f.viewport.style.scrollBehavior="auto"),A(e))}),onPointerMove:D(e.onPointerMove,A),onPointerUp:D(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=w.current,f.viewport&&(f.viewport.style.scrollBehavior=""),b.current=null})})})}),et="ScrollAreaThumb",er=s.forwardRef((e,t)=>{let{forceMount:r,...s}=e,a=J(et,e.__scopeScrollArea);return(0,n.jsx)(R,{present:r||a.hasThumb,children:(0,n.jsx)(en,{ref:t,...s})})}),en=s.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:a,...o}=e,l=W(et,r),i=J(et,r),{onThumbPositionChange:c}=i,d=E(t,e=>i.onThumbChange(e)),u=s.useRef(void 0),p=em(()=>{u.current&&(u.current(),u.current=void 0)},100);return s.useEffect(()=>{let e=l.viewport;if(e){let t=()=>{p(),u.current||(u.current=ep(e,c),c())};return c(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[l.viewport,p,c]),(0,n.jsx)(P.div,{"data-state":i.hasThumb?"visible":"hidden",...o,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...a},onPointerDownCapture:D(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;i.onThumbPointerDown({x:r,y:n})}),onPointerUp:D(e.onPointerUp,i.onThumbPointerUp)})});er.displayName=et;var es="ScrollAreaCorner",ea=s.forwardRef((e,t)=>{let r=W(es,e.__scopeScrollArea),s=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&s?(0,n.jsx)(eo,{...e,ref:t}):null});ea.displayName=es;var eo=s.forwardRef((e,t)=>{let{__scopeScrollArea:r,...a}=e,o=W(es,r),[l,i]=s.useState(0),[c,d]=s.useState(0),u=!!(l&&c);return eh(o.scrollbarX,()=>{let e=o.scrollbarX?.offsetHeight||0;o.onCornerHeightChange(e),d(e)}),eh(o.scrollbarY,()=>{let e=o.scrollbarY?.offsetWidth||0;o.onCornerWidthChange(e),i(e)}),u?(0,n.jsx)(P.div,{...a,ref:t,style:{width:l,height:c,position:"absolute",right:"ltr"===o.dir?0:void 0,left:"rtl"===o.dir?0:void 0,bottom:0,...e.style}}):null});function el(e){return e?parseInt(e,10):0}function ei(e,t){let r=e/t;return isNaN(r)?0:r}function ec(e){let t=ei(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function ed(e,t,r="ltr"){let n=ec(t),s=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,a=t.scrollbar.size-s,o=t.content-t.viewport,l=function(e,[t,r]){return Math.min(r,Math.max(t,e))}(e,"ltr"===r?[0,o]:[-1*o,0]);return eu([0,o],[0,a-n])(l)}function eu(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var ep=(e,t=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},n=0;return function s(){let a={left:e.scrollLeft,top:e.scrollTop},o=r.left!==a.left,l=r.top!==a.top;(o||l)&&t(),r=a,n=window.requestAnimationFrame(s)}(),()=>window.cancelAnimationFrame(n)};function em(e,t){let r=O(e),n=s.useRef(0);return s.useEffect(()=>()=>window.clearTimeout(n.current),[]),s.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(r,t)},[r,t])}function eh(e,t){let r=O(t);T(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var ef=r(4780);function ev({className:e,children:t,...r}){return(0,n.jsxs)(z,{"data-slot":"scroll-area",className:(0,ef.cn)("relative",e),...r,children:[(0,n.jsx)(B,{"data-slot":"scroll-area-viewport",className:"focus-visible:ring-ring/50 size-full rounded-[inherit] transition-[color,box-shadow] outline-none focus-visible:ring-[3px] focus-visible:outline-1",children:t}),(0,n.jsx)(eg,{}),(0,n.jsx)(ea,{})]})}function eg({className:e,orientation:t="vertical",...r}){return(0,n.jsx)(V,{"data-slot":"scroll-area-scrollbar",orientation:t,className:(0,ef.cn)("flex touch-none p-px transition-colors select-none","vertical"===t&&"h-full w-2.5 border-l border-l-transparent","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent",e),...r,children:(0,n.jsx)(er,{"data-slot":"scroll-area-thumb",className:"bg-border relative flex-1 rounded-full"})})}let ex=[{value:"profile",label:"Profile",component:p.A},{value:"prompt",label:"Prompt",component:m.A},{value:"voice",label:"Voice",component:x.A},{value:"brain",label:"Brain",component:b.A},{value:"actions",label:"Actions",component:h.A},{value:"advanced",label:"Advanced",component:w.A}];function eb({agentId:e}){let t=(0,a.useRouter)(),r=(0,a.useParams)(),[p,m]=(0,s.useState)("profile"),h=e||r.id,[x,b]=(0,s.useState)(!1),[w,C]=(0,s.useState)(!1),[S,E]=(0,s.useState)(!1),[A,k]=(0,s.useState)(null),{userRole:P}=(0,v.A)(),{agent:T,setAgent:R,phoneNumbers:_,agentIsLoading:O,updateAgentMutation:L}=(0,g.f)(h),{agents:D,agentsisLoading:q}=(0,N.a)(),I=async()=>{if(T){C(!0),k(null);try{await L.mutateAsync(T),E(!0)}catch(e){console.error("Error updating agent:",e),k(e instanceof Error?e.message:"Failed to update agent")}finally{C(!1)}}},U=async()=>{if(!T)return;let e="active"===T.status?"inactive":"active";try{await L.mutateAsync({...T,status:e})}catch(e){console.error("Error updating agent status:",e),k(e instanceof Error?e.message:"Failed to update agent status")}},M=e=>{e!==h&&t.push(`/agents/edit/${e}`),b(!1)};return(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("div",{className:"min-h-screen bg-gray-50/50 dark:bg-gray-900/50 ",children:[(0,n.jsx)("div",{className:"w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",children:(0,n.jsxs)("div",{className:"container flex h-14 sm:h-16 items-center px-2 sm:px-4",children:[(0,n.jsxs)(d.$,{variant:"ghost",size:"icon",onClick:()=>t.push("/agents"),className:"mr-4 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800",children:[(0,n.jsx)(o.A,{className:"h-4 w-4 sm:h-5 sm:w-5"}),(0,n.jsx)("span",{className:"sr-only",children:"Back"})]}),(0,n.jsx)("h1",{className:"text-lg sm:text-xl md:text-2xl font-bold    tracking-tight truncate",children:T?.name})]})}),(0,n.jsxs)("div",{className:"flex flex-col lg:flex-row",children:[(0,n.jsx)("div",{className:"hidden xl:block",children:(0,n.jsx)(j,{currentAgentId:h,userRole:P,agents:D,agentsisLoading:q})}),(0,n.jsx)("div",{className:"flex-1 min-h-[calc(100vh-4rem)]",children:O?(0,n.jsx)("div",{className:"container py-4 px-4",children:(0,n.jsxs)("div",{className:"animate-pulse",children:[(0,n.jsx)("div",{className:"h-8 w-64 bg-gray-200 dark:bg-gray-700 rounded mb-4"}),(0,n.jsx)("div",{className:"h-4 w-48 bg-gray-200 dark:bg-gray-700 rounded"}),(0,n.jsxs)("div",{className:"mt-8 space-y-4",children:[(0,n.jsx)("div",{className:"h-12 bg-gray-200 dark:bg-gray-700 rounded"}),(0,n.jsx)("div",{className:"h-64 bg-gray-200 dark:bg-gray-700 rounded"})]})]})}):T&&(0,n.jsxs)("div",{className:"p-3 sm:p-4 md:p-6 max-w-full md:max-w-5xl mx-auto",children:[(0,n.jsxs)(u.tU,{value:p,onValueChange:m,className:"space-y-4",children:[(0,n.jsx)("div",{className:"border-b overflow-x-auto scrollbar-hide",children:(0,n.jsx)(u.j7,{className:"w-full justify-start h-auto bg-transparent p-0 flex",children:ex.map(e=>(0,n.jsx)(u.Xi,{value:e.value,className:"data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:shadow-none rounded-none px-2 sm:px-4 py-2 sm:py-3 bg-transparent text-xs sm:text-sm whitespace-nowrap",children:e.label},e.value))})}),(0,n.jsx)("div",{className:"bg-card rounded-lg border shadow-sm",children:ex.map(e=>(0,n.jsx)(u.av,{value:e.value,className:"m-0 focus-visible:outline-none focus-visible:ring-0",children:(0,n.jsx)(e.component,{agent:T,setAgent:R,phoneNumbers:_,isCreateMode:!1})},e.value))})]}),(0,n.jsx)("div",{className:"mt-4 sm:mt-6 flex justify-end",children:(0,n.jsx)(d.$,{onClick:I,disabled:w,className:"bg-black text-white dark:text-black dark:bg-white hover:from-purple-700 hover:to-blue-600 w-full sm:w-auto",children:w?"Saving...":"Save Changes"})}),"superadmin"===P&&(0,n.jsxs)("div",{className:"mt-6 sm:mt-8 flex flex-col sm:flex-row sm:items-center justify-between py-3 sm:py-4 px-4 sm:px-6 bg-muted rounded-lg",children:[(0,n.jsxs)("div",{className:"flex items-center gap-2 mb-3 sm:mb-0",children:[(0,n.jsx)("div",{className:`h-3 w-3 rounded-full ${"active"===T.status?"bg-green-500":"bg-gray-400"}`}),(0,n.jsx)("span",{className:"text-sm",children:"active"===T.status?"Active":"Inactive"})]}),(0,n.jsx)(d.$,{variant:"outline",size:"sm",onClick:U,children:"active"===T.status?"Deactivate":"Activate"})]}),(0,n.jsx)("div",{className:"fixed bottom-6 right-6 xl:hidden z-50",children:(0,n.jsx)(d.$,{onClick:()=>b(!0),size:"icon",className:"h-12 w-12 rounded-full shadow-lg bg-primary hover:bg-primary/90",children:(0,n.jsx)(l.A,{className:"h-6 w-6"})})}),(0,n.jsx)(f.lG,{open:x,onOpenChange:b,children:(0,n.jsxs)(f.Cf,{className:"sm:max-w-md",children:[(0,n.jsx)(f.c7,{children:(0,n.jsxs)(f.L3,{className:"flex items-center gap-2",children:[(0,n.jsx)(l.A,{className:"h-5 w-5"}),"Select Agent"]})}),q?(0,n.jsx)("div",{className:"py-4 flex justify-center",children:(0,n.jsx)("div",{className:"animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"})}):(0,n.jsx)(ev,{className:"h-[50vh] sm:h-[40vh] pr-4",children:(0,n.jsx)("div",{className:"space-y-1",children:D.map(e=>(0,n.jsxs)("div",{onClick:()=>M(e.id),className:`flex items-center gap-3 p-2 rounded-md cursor-pointer transition-colors
                      ${e.id===h?"bg-primary/10 text-primary":"hover:bg-muted"}`,children:[(0,n.jsxs)(y.eu,{className:"h-9 w-9",children:[(0,n.jsx)(y.BK,{src:e.avatar||"",alt:e.name}),(0,n.jsx)(y.q5,{className:"bg-primary/10 text-primary",children:e.name.charAt(0).toUpperCase()})]}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsx)("p",{className:"font-medium truncate",children:e.name}),(0,n.jsx)("p",{className:"text-xs text-muted-foreground truncate",children:e.role})]}),e.id===h&&(0,n.jsx)("div",{className:"h-2 w-2 rounded-full bg-primary"})]},e.id))})})]})}),(0,n.jsx)(f.lG,{open:S,onOpenChange:E,children:(0,n.jsxs)(f.Cf,{className:"sm:max-w-md",children:[(0,n.jsx)(f.c7,{children:(0,n.jsxs)(f.L3,{className:"flex items-center gap-2",children:[(0,n.jsx)(i.A,{className:"h-5 w-5 text-green-500"}),"Agent Updated Successfully"]})}),(0,n.jsxs)("p",{className:"text-sm text-muted-foreground",children:["Your changes to ",T?.name," have been saved."]}),(0,n.jsxs)(f.Es,{className:"flex gap-2 sm:justify-start",children:[(0,n.jsx)(d.$,{variant:"outline",onClick:()=>E(!1),children:"Continue Editing"}),(0,n.jsx)(d.$,{onClick:()=>t.push("/agents"),className:"bg-black text-white dark:text-black dark:bg-white",children:"Back to Agents"})]})]})}),A&&(0,n.jsx)(f.lG,{open:!!A,onOpenChange:()=>k(null),children:(0,n.jsxs)(f.Cf,{className:"sm:max-w-md",children:[(0,n.jsx)(f.c7,{children:(0,n.jsxs)(f.L3,{className:"flex items-center gap-2 text-red-500",children:[(0,n.jsx)(c.A,{className:"h-5 w-5"}),"Error Updating Agent"]})}),(0,n.jsx)("p",{className:"text-sm text-muted-foreground",children:A}),(0,n.jsx)(f.Es,{children:(0,n.jsx)(d.$,{variant:"outline",onClick:()=>k(null),children:"Close"})})]})})]})})]})]})})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30757:(e,t,r)=>{Promise.resolve().then(r.bind(r,95090))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67205:(e,t,r)=>{Promise.resolve().then(r.bind(r,24007))},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87979:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(43210);function s(){let[e,t]=(0,n.useState)(null),[r,s]=(0,n.useState)(!0),[a,o]=(0,n.useState)(null),l=e?.role||null;return{user:e,userRole:l,authIsLoading:r,authError:a}}},91645:e=>{"use strict";e.exports=require("net")},93613:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},94735:e=>{"use strict";e.exports=require("events")},95090:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});var n=r(12907);(0,n.registerClientReference)(function(){throw Error("Attempted to call agentTabs() from the server but agentTabs is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\edit\\[id]\\EditAgentContent.tsx","agentTabs");let s=(0,n.registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\app\\\\(workspace)\\\\agents\\\\edit\\\\[id]\\\\EditAgentContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\agents\\edit\\[id]\\EditAgentContent.tsx","default")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[287,9176,7674,5814,598,5188,6034,8606,6913,5901,1476,4772,2093,1816],()=>r(10940));module.exports=n})();