(()=>{var t={};t.id=2050,t.ids=[2050],t.modules={22:(t,e,r)=>{var n=r(75254),o=r(20623),i=r(48169),a=r(40542),c=r(45058);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):n(t):c(t)}},658:(t,e,r)=>{t.exports=r(41547)(r(85718),"Map")},1566:(t,e,r)=>{var n=r(89167),o=r(658),i=r(30401),a=r(34772),c=r(17830),u=r(29395),l=r(12290),s="[object Map]",f="[object Promise]",p="[object Set]",d="[object WeakMap]",h="[object DataView]",y=l(n),v=l(o),m=l(i),b=l(a),g=l(c),x=u;(n&&x(new n(new ArrayBuffer(1)))!=h||o&&x(new o)!=s||i&&x(i.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=d)&&(x=function(t){var e=u(t),r="[object Object]"==e?t.constructor:void 0,n=r?l(r):"";if(n)switch(n){case y:return h;case v:return s;case m:return f;case b:return p;case g:return d}return e}),t.exports=x},1707:(t,e,r)=>{var n=r(35142),o=r(46436);t.exports=function(t,e){e=n(e,t);for(var r=0,i=e.length;null!=t&&r<i;)t=t[o(e[r++])];return r&&r==i?t:void 0}},1944:t=>{t.exports=function(){return!1}},2408:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},2896:(t,e,r)=>{var n=r(81488),o=r(59467);t.exports=function(t,e){return null!=t&&o(t,e,n)}},2984:(t,e,r)=>{var n=r(49227);t.exports=function(t,e,r){for(var o=-1,i=t.length;++o<i;){var a=t[o],c=e(a);if(null!=c&&(void 0===u?c==c&&!n(c):r(c,u)))var u=c,l=a}return l}},3105:t=>{t.exports=function(t){return t.split("")}},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3303:(t,e,r)=>{Promise.resolve().then(r.bind(r,33806)),Promise.resolve().then(r.bind(r,28086))},4999:(t,e,r)=>{t.exports=r(85718).Uint8Array},5231:(t,e,r)=>{var n=r(29395),o=r(55048);t.exports=function(t){if(!o(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},5359:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},5566:(t,e,r)=>{var n=r(41011),o=r(34117),i=r(66713),a=r(42403);t.exports=function(t){return function(e){var r=o(e=a(e))?i(e):void 0,c=r?r[0]:e.charAt(0),u=r?n(r,1).join(""):e.slice(1);return c[t]()+u}}},6053:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},6330:t=>{t.exports=function(){return[]}},7383:(t,e,r)=>{var n=r(67009),o=r(32269),i=r(38428),a=r(55048);t.exports=function(t,e,r){if(!a(r))return!1;var c=typeof e;return("number"==c?!!(o(r)&&i(e,r.length)):"string"==c&&e in r)&&n(r[e],t)}},7651:(t,e,r)=>{var n=r(82038),o=r(52931),i=r(32269);t.exports=function(t){return i(t)?n(t):o(t)}},7935:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r=!0;return n(t,function(t,n,o){return r=!!e(t,n,o)}),r}},8336:(t,e,r)=>{var n=r(45803);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},8852:(t,e,r)=>{var n=r(1707);t.exports=function(t){return function(e){return n(e,t)}}},10090:(t,e,r)=>{var n=r(80458),o=r(89624),i=r(47282),a=i&&i.isTypedArray;t.exports=a?o(a):n},10653:(t,e,r)=>{var n=r(21456),o=r(63979),i=r(7651);t.exports=function(t){return n(t,i,o)}},10663:t=>{t.exports="object"==typeof global&&global&&global.Object===Object&&global},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11117:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new o(n,i||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,o,i,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,l,s=this._events[c],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,o),!0;case 5:return s.fn.call(s.context,e,n,o,i),!0;case 6:return s.fn.call(s.context,e,n,o,i,a),!0}for(l=1,u=Array(f-1);l<f;l++)u[l-1]=arguments[l];s.fn.apply(s.context,u)}else{var p,d=s.length;for(l=0;l<d;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,o);break;default:if(!u)for(p=1,u=Array(f-1);p<f;p++)u[p-1]=arguments[p];s[l].fn.apply(s[l].context,u)}}return!0},c.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var c=this._events[i];if(c.fn)c.fn!==e||o&&!c.once||n&&c.context!==n||a(this,i);else{for(var u=0,l=[],s=c.length;u<s;u++)(c[u].fn!==e||o&&!c[u].once||n&&c[u].context!==n)&&l.push(c[u]);l.length?this._events[i]=1===l.length?l[0]:l:a(this,i)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},11424:(t,e,r)=>{var n=r(47603);t.exports=r(66400)(n)},11539:(t,e,r)=>{var n=r(37643),o=r(55048),i=r(49227),a=0/0,c=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,l=/^0o[0-7]+$/i,s=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return a;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=u.test(t);return r||l.test(t)?s(t.slice(2),r?2:8):c.test(t)?a:+t}},12290:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},12344:(t,e,r)=>{t.exports=r(65984)()},14675:t=>{t.exports=function(t){return function(){return t}}},15079:(t,e,r)=>{"use strict";r.d(e,{bq:()=>f,eb:()=>d,gC:()=>p,l6:()=>l,yv:()=>s});var n=r(60687);r(43210);var o=r(22670),i=r(78272),a=r(13964),c=r(3589),u=r(4780);function l({...t}){return(0,n.jsx)(o.bL,{"data-slot":"select",...t})}function s({...t}){return(0,n.jsx)(o.WT,{"data-slot":"select-value",...t})}function f({className:t,children:e,...r}){return(0,n.jsxs)(o.l9,{"data-slot":"select-trigger",className:(0,u.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...r,children:[e,(0,n.jsx)(o.In,{asChild:!0,children:(0,n.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function p({className:t,children:e,position:r="popper",...i}){return(0,n.jsx)(o.ZL,{children:(0,n.jsxs)(o.UC,{"data-slot":"select-content",className:(0,u.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md","popper"===r&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:r,...i,children:[(0,n.jsx)(h,{}),(0,n.jsx)(o.LM,{className:(0,u.cn)("p-1","popper"===r&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:e}),(0,n.jsx)(y,{})]})})}function d({className:t,children:e,...r}){return(0,n.jsxs)(o.q7,{"data-slot":"select-item",className:(0,u.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...r,children:[(0,n.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,n.jsx)(o.VF,{children:(0,n.jsx)(a.A,{className:"size-4"})})}),(0,n.jsx)(o.p4,{children:e})]})}function h({className:t,...e}){return(0,n.jsx)(o.PP,{"data-slot":"select-scroll-up-button",className:(0,u.cn)("flex cursor-default items-center justify-center py-1",t),...e,children:(0,n.jsx)(c.A,{className:"size-4"})})}function y({className:t,...e}){return(0,n.jsx)(o.wn,{"data-slot":"select-scroll-down-button",className:(0,u.cn)("flex cursor-default items-center justify-center py-1",t),...e,children:(0,n.jsx)(i.A,{className:"size-4"})})}},15451:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return o(t)&&"[object Arguments]"==n(t)}},15871:(t,e,r)=>{var n=r(36341),o=r(27467);t.exports=function t(e,r,i,a,c){return e===r||(null!=e&&null!=r&&(o(e)||o(r))?n(e,r,i,a,t,c):e!=e&&r!=r)}},15880:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("PhoneOff",[["path",{d:"M10.68 13.31a16 16 0 0 0 3.41 2.6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7 2 2 0 0 1 1.72 2v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.42 19.42 0 0 1-3.33-2.67m-2.67-3.34a19.79 19.79 0 0 1-3.07-8.63A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91",key:"z86iuo"}],["line",{x1:"22",x2:"2",y1:"2",y2:"22",key:"11kh81"}]])},15883:(t,e,r)=>{var n=r(2984),o=r(46063),i=r(48169);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},15909:(t,e,r)=>{var n=r(87506),o=r(66930),i=r(658);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},16854:t=>{t.exports=function(t){return this.__data__.has(t)}},17518:(t,e,r)=>{var n=r(21367),o=r(1707),i=r(22),a=r(54765),c=r(43378),u=r(89624),l=r(65727),s=r(48169),f=r(40542);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t}):[s];var p=-1;return e=n(e,u(i)),c(a(t,function(t,r,o){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return l(t,e,r)})}},17830:(t,e,r)=>{t.exports=r(41547)(r(85718),"WeakMap")},18234:(t,e,r)=>{var n=r(91290),o=r(22),i=r(84482),a=Math.max;t.exports=function(t,e,r){var c=null==t?0:t.length;if(!c)return -1;var u=null==r?0:i(r);return u<0&&(u=a(c+u,0)),n(t,o(e,3),u)}},19121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19976:(t,e,r)=>{var n=r(8336);t.exports=function(t,e){var r=n(this,t),o=r.size;return r.set(t,e),this.size+=+(r.size!=o),this}},20540:(t,e,r)=>{var n=r(55048),o=r(70151),i=r(11539),a=Math.max,c=Math.min;t.exports=function(t,e,r){var u,l,s,f,p,d,h=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var r=u,n=l;return u=l=void 0,h=e,f=t.apply(n,r)}function g(t){var r=t-d,n=t-h;return void 0===d||r>=e||r<0||v&&n>=s}function x(){var t,r,n,i=o();if(g(i))return w(i);p=setTimeout(x,(t=i-d,r=i-h,n=e-t,v?c(n,s-r):n))}function w(t){return(p=void 0,m&&u)?b(t):(u=l=void 0,f)}function O(){var t,r=o(),n=g(r);if(u=arguments,l=this,d=r,n){if(void 0===p)return h=t=d,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(d)}return void 0===p&&(p=setTimeout(x,e)),f}return e=i(e)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(i(r.maxWait)||0,e):s,m="trailing"in r?!!r.trailing:m),O.cancel=function(){void 0!==p&&clearTimeout(p),h=0,u=d=l=p=void 0},O.flush=function(){return void 0===p?f:w(o())},O}},20623:(t,e,r)=>{var n=r(15871),o=r(40491),i=r(2896),a=r(67619),c=r(34883),u=r(41132),l=r(46436);t.exports=function(t,e){return a(t)&&c(e)?u(l(t),e):function(r){var a=o(r,t);return void 0===a&&a===e?i(r,t):n(e,a,3)}}},21235:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("PhoneOutgoing",[["polyline",{points:"22 8 22 2 16 2",key:"1g204g"}],["line",{x1:"16",x2:"22",y1:"8",y2:"2",key:"1ggias"}],["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},21367:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},21456:(t,e,r)=>{var n=r(41693),o=r(40542);t.exports=function(t,e,r){var i=e(t);return o(t)?i:n(i,r(t))}},21592:(t,e,r)=>{var n=r(42205),o=r(61837);t.exports=function(t,e){return n(o(t,e),1)}},21630:(t,e,r)=>{var n=r(10653),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,i,a,c){var u=1&r,l=n(t),s=l.length;if(s!=n(e).length&&!u)return!1;for(var f=s;f--;){var p=l[f];if(!(u?p in e:o.call(e,p)))return!1}var d=c.get(t),h=c.get(e);if(d&&h)return d==e&&h==t;var y=!0;c.set(t,e),c.set(e,t);for(var v=u;++f<s;){var m=t[p=l[f]],b=e[p];if(i)var g=u?i(b,m,p,e,t,c):i(m,b,p,t,e,c);if(!(void 0===g?m===b||a(m,b,r,i,c):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,w=e.constructor;x!=w&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w)&&(y=!1)}return c.delete(t),c.delete(e),y}},21820:t=>{"use strict";t.exports=require("os")},22964:(t,e,r)=>{t.exports=r(23729)(r(18234))},23729:(t,e,r)=>{var n=r(22),o=r(32269),i=r(7651);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!o(e)){var u=n(r,3);e=i(e),r=function(t){return u(c[t],t,c)}}var l=t(e,r,a);return l>-1?c[u?e[l]:l]:void 0}}},25118:t=>{t.exports=function(t){return this.__data__.has(t)}},27006:(t,e,r)=>{var n=r(46328),o=r(99525),i=r(58276);t.exports=function(t,e,r,a,c,u){var l=1&r,s=t.length,f=e.length;if(s!=f&&!(l&&f>s))return!1;var p=u.get(t),d=u.get(e);if(p&&d)return p==e&&d==t;var h=-1,y=!0,v=2&r?new n:void 0;for(u.set(t,e),u.set(e,t);++h<s;){var m=t[h],b=e[h];if(a)var g=l?a(b,m,h,e,t,u):a(m,b,h,t,e,u);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!o(e,function(t,e){if(!i(v,e)&&(m===t||c(m,t,r,a,u)))return v.push(e)})){y=!1;break}}else if(!(m===b||c(m,b,r,a,u))){y=!1;break}}return u.delete(t),u.delete(e),y}},27467:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},27669:t=>{t.exports=function(){this.__data__=[],this.size=0}},27910:t=>{"use strict";t.exports=require("stream")},28086:(t,e,r)=>{"use strict";r.d(e,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\app\\\\(workspace)\\\\dashboard\\\\DashboardContent.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\dashboard\\DashboardContent.tsx","default")},28354:t=>{"use strict";t.exports=require("util")},28837:(t,e,r)=>{var n=r(57797),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():o.call(e,r,1),--this.size,!0)}},28977:(t,e,r)=>{var n=r(11539),o=1/0;t.exports=function(t){return t?(t=n(t))===o||t===-o?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},29021:t=>{"use strict";t.exports=require("fs")},29205:(t,e,r)=>{var n=r(8336);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=+!!e,e}},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29395:(t,e,r)=>{var n=r(79474),o=r(70222),i=r(84713),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?o(t):i(t)}},29508:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).get(t)}},30401:(t,e,r)=>{t.exports=r(41547)(r(85718),"Promise")},30854:(t,e,r)=>{var n=r(66930),o=r(658),i=r(95746);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(t,e),this.size=r.size,this}},32269:(t,e,r)=>{var n=r(5231),o=r(69619);t.exports=function(t){return null!=t&&o(t.length)&&!n(t)}},33567:(t,e,r)=>{Promise.resolve().then(r.bind(r,23328)),Promise.resolve().then(r.bind(r,47804))},33806:(t,e,r)=>{"use strict";r.d(e,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\animations\\\\FadeIn.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\animations\\FadeIn.tsx","default")},33873:t=>{"use strict";t.exports=require("path")},34117:t=>{var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},34452:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},34631:t=>{"use strict";t.exports=require("tls")},34746:t=>{t.exports=function(t){return this.__data__.get(t)}},34772:(t,e,r)=>{t.exports=r(41547)(r(85718),"Set")},34883:(t,e,r)=>{var n=r(55048);t.exports=function(t){return t==t&&!n(t)}},34990:(t,e,r)=>{t.exports=r(87321)()},35142:(t,e,r)=>{var n=r(40542),o=r(67619),i=r(51449),a=r(42403);t.exports=function(t,e){return n(t)?t:o(t,e)?[t]:i(a(t))}},35163:(t,e,r)=>{var n=r(15451),o=r(27467),i=Object.prototype,a=i.hasOwnProperty,c=i.propertyIsEnumerable;t.exports=n(function(){return arguments}())?n:function(t){return o(t)&&a.call(t,"callee")&&!c.call(t,"callee")}},35697:(t,e,r)=>{var n=r(79474),o=r(4999),i=r(67009),a=r(27006),c=r(59774),u=r(2408),l=n?n.prototype:void 0,s=l?l.valueOf:void 0;t.exports=function(t,e,r,n,l,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!f(new o(t),new o(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var d=c;case"[object Set]":var h=1&n;if(d||(d=u),t.size!=e.size&&!h)break;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(d(t),d(e),n,l,f,p);return p.delete(t),v;case"[object Symbol]":if(s)return s.call(t)==s.call(e)}return!1}},35800:(t,e,r)=>{var n=r(57797);t.exports=function(t){return n(this.__data__,t)>-1}},36315:(t,e,r)=>{var n=r(22),o=r(92662);t.exports=function(t,e){return t&&t.length?o(t,n(e,2)):[]}},36341:(t,e,r)=>{var n=r(67200),o=r(27006),i=r(35697),a=r(21630),c=r(1566),u=r(40542),l=r(80329),s=r(10090),f="[object Arguments]",p="[object Array]",d="[object Object]",h=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var b=u(t),g=u(e),x=b?p:c(t),w=g?p:c(e);x=x==f?d:x,w=w==f?d:w;var O=x==d,j=w==d,A=x==w;if(A&&l(t)){if(!l(e))return!1;b=!0,O=!1}if(A&&!O)return m||(m=new n),b||s(t)?o(t,e,r,y,v,m):i(t,e,x,r,y,v,m);if(!(1&r)){var S=O&&h.call(t,"__wrapped__"),P=j&&h.call(e,"__wrapped__");if(S||P){var E=S?t.value():t,k=P?e.value():e;return m||(m=new n),v(E,k,r,y,m)}}return!!A&&(m||(m=new n),a(t,e,r,y,v,m))}},36959:t=>{t.exports=function(){}},37456:t=>{t.exports=function(t){return null==t}},37575:(t,e,r)=>{var n=r(66930);t.exports=function(){this.__data__=new n,this.size=0}},37643:(t,e,r)=>{var n=r(6053),o=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(o,""):t}},38404:(t,e,r)=>{var n=r(29395),o=r(65932),i=r(27467),a=Object.prototype,c=Function.prototype.toString,u=a.hasOwnProperty,l=c.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=n(t))return!1;var e=o(t);if(null===e)return!0;var r=u.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==l}},38428:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?0x1fffffffffffff:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},39672:(t,e,r)=>{var n=r(58141);t.exports=function(t,e){var r=this.__data__;return this.size+=+!this.has(t),r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},39774:t=>{t.exports=function(t){return t!=t}},40491:(t,e,r)=>{var n=r(1707);t.exports=function(t,e,r){var o=null==t?void 0:n(t,e);return void 0===o?r:o}},40542:t=>{t.exports=Array.isArray},41011:(t,e,r)=>{var n=r(41353);t.exports=function(t,e,r){var o=t.length;return r=void 0===r?o:r,!e&&r>=o?t:n(t,e,r)}},41132:t=>{t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},41157:(t,e,r)=>{var n=r(91928);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},41353:t=>{t.exports=function(t,e,r){var n=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(o);++n<o;)i[n]=t[n+e];return i}},41547:(t,e,r)=>{var n=r(61548),o=r(90851);t.exports=function(t,e){var r=o(t,e);return n(r)?r:void 0}},41693:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}},42082:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},42205:(t,e,r)=>{var n=r(41693),o=r(85450);t.exports=function t(e,r,i,a,c){var u=-1,l=e.length;for(i||(i=o),c||(c=[]);++u<l;){var s=e[u];r>0&&i(s)?r>1?t(s,r-1,i,a,c):n(c,s):a||(c[c.length]=s)}return c}},42403:(t,e,r)=>{var n=r(80195);t.exports=function(t){return null==t?"":n(t)}},43378:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},44493:(t,e,r)=>{"use strict";r.d(e,{BT:()=>u,Wu:()=>l,ZB:()=>c,Zp:()=>i,aR:()=>a,wL:()=>s});var n=r(60687);r(43210);var o=r(4780);function i({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card",className:(0,o.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",t),...e})}function a({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,o.cn)("flex flex-col gap-1.5 px-6",t),...e})}function c({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,o.cn)("leading-none font-semibold",t),...e})}function u({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,o.cn)("text-muted-foreground text-sm",t),...e})}function l({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,o.cn)("px-6",t),...e})}function s({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-footer",className:(0,o.cn)("flex items-center px-6",t),...e})}},45058:(t,e,r)=>{var n=r(42082),o=r(8852),i=r(67619),a=r(46436);t.exports=function(t){return i(t)?n(a(t)):o(t)}},45603:(t,e,r)=>{var n=r(20540),o=r(55048);t.exports=function(t,e,r){var i=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:i,maxWait:e,trailing:a})}},45803:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},46063:t=>{t.exports=function(t,e){return t<e}},46229:(t,e,r)=>{var n=r(48169),o=r(66354),i=r(11424);t.exports=function(t,e){return i(o(t,e,n),t+"")}},46328:(t,e,r)=>{var n=r(95746),o=r(89185),i=r(16854);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,t.exports=a},46436:(t,e,r)=>{var n=r(49227),o=1/0;t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-o?"-0":e}},47212:(t,e,r)=>{var n=r(87270),o=r(7935),i=r(22),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),u(t,i(e,3))}},47282:(t,e,r)=>{t=r.nmd(t);var n=r(10663),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&n.process,c=function(){try{var t=i&&i.require&&i.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=c},47603:(t,e,r)=>{var n=r(14675),o=r(91928),i=r(48169);t.exports=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:i},47804:(t,e,r)=>{"use strict";r.d(e,{default:()=>hz});var n={};r.r(n),r.d(n,{scaleBand:()=>nC,scaleDiverging:()=>function t(){var e=ix(cB()(it));return e.copy=function(){return cC(e,t())},nE.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=iM(cB()).domain([.1,1,10]);return e.copy=function(){return cC(e,t()).base(e.base())},nE.apply(e,arguments)},scaleDivergingPow:()=>cR,scaleDivergingSqrt:()=>cL,scaleDivergingSymlog:()=>function t(){var e=iN(cB());return e.copy=function(){return cC(e,t()).constant(e.constant())},nE.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t*=1)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,o7),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,o7):[0,1],ix(n)},scaleImplicit:()=>nT,scaleLinear:()=>iw,scaleLog:()=>function t(){let e=iM(ia()).domain([1,10]);return e.copy=()=>ii(e,t()).base(e.base()),nP.apply(e,arguments),e},scaleOrdinal:()=>nN,scalePoint:()=>nD,scalePow:()=>iR,scaleQuantile:()=>function t(){var e,r=[],n=[],o=[];function i(){var t=0,e=Math.max(1,n.length);for(o=Array(e-1);++t<e;)o[t-1]=function(t,e,r=om){if(!(!(n=t.length)||isNaN(e*=1))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(+r(t[i+1],i+1,t)-a)*(o-i)}}(r,t/e);return a}function a(t){return null==t||isNaN(t*=1)?e:n[og(o,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?o[e-1]:r[0],e<o.length?o[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e*=1)||r.push(e);return r.sort(od),i()},a.range=function(t){return arguments.length?(n=Array.from(t),i()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return o.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},nP.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,o=1,i=[.5],a=[0,1];function c(t){return null!=t&&t<=t?a[og(i,t,0,o)]:e}function u(){var t=-1;for(i=Array(o);++t<o;)i[t]=((t+1)*n-(t-o)*r)/(o+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r*=1,n*=1,u()):[r,n]},c.range=function(t){return arguments.length?(o=(a=Array.from(t)).length-1,u()):a.slice()},c.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,i[0]]:e>=o?[i[o-1],n]:[i[e-1],i[e]]},c.unknown=function(t){return arguments.length&&(e=t),c},c.thresholds=function(){return i.slice()},c.copy=function(){return t().domain([r,n]).range(a).unknown(e)},nP.apply(ix(c),arguments)},scaleRadial:()=>function t(){var e,r=ic(),n=[0,1],o=!1;function i(t){var n,i=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(i)?e:o?Math.round(i):i}return i.invert=function(t){return r.invert(iz(t))},i.domain=function(t){return arguments.length?(r.domain(t),i):r.domain()},i.range=function(t){return arguments.length?(r.range((n=Array.from(t,o7)).map(iz)),i):n.slice()},i.rangeRound=function(t){return i.range(t).round(!0)},i.round=function(t){return arguments.length?(o=!!t,i):o},i.clamp=function(t){return arguments.length?(r.clamp(t),i):r.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t(r.domain(),n).round(o).clamp(r.clamp()).unknown(e)},nP.apply(i,arguments),ix(i)},scaleSequential:()=>function t(){var e=ix(cN()(it));return e.copy=function(){return cC(e,t())},nE.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=iM(cN()).domain([1,10]);return e.copy=function(){return cC(e,t()).base(e.base())},nE.apply(e,arguments)},scaleSequentialPow:()=>cD,scaleSequentialQuantile:()=>function t(){var e=[],r=it;function n(t){if(null!=t&&!isNaN(t*=1))return r((og(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r*=1)||e.push(r);return e.sort(od),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n*=1)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e*=1))){if(e<=0||n<2)return i$(t);if(e>=1)return iU(t);var n,o=(n-1)*e,i=Math.floor(o),a=iU((function t(e,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(e.length-1,o)),!(n<=r&&r<=o))return e;for(i=void 0===i?iF:function(t=od){if(t===od)return iF;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(i);o>n;){if(o-n>600){let a=o-n+1,c=r-n+1,u=Math.log(a),l=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*l*(a-l)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*l/a+s)),p=Math.min(o,Math.floor(r+(a-c)*l/a+s));t(e,r,f,p,i)}let a=e[r],c=n,u=o;for(iq(e,n,r),i(e[o],a)>0&&iq(e,n,o);c<u;){for(iq(e,c,u),++c,--u;0>i(e[c],a);)++c;for(;i(e[u],a)>0;)--u}0===i(e[n],a)?iq(e,n,u):iq(e,++u,o),u<=r&&(n=u+1),r<=u&&(o=u-1)}return e})(t,i).subarray(0,i+1));return a+(i$(t.subarray(i+1))-a)*(o-i)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},nE.apply(n,arguments)},scaleSequentialSqrt:()=>cI,scaleSequentialSymlog:()=>function t(){var e=iN(cN());return e.copy=function(){return cC(e,t()).constant(e.constant())},nE.apply(e,arguments)},scaleSqrt:()=>iL,scaleSymlog:()=>function t(){var e=iN(ia());return e.copy=function(){return ii(e,t()).constant(e.constant())},nP.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],o=1;function i(t){return null!=t&&t<=t?n[og(r,t,0,o)]:e}return i.domain=function(t){return arguments.length?(o=Math.min((r=Array.from(t)).length,n.length-1),i):r.slice()},i.range=function(t){return arguments.length?(n=Array.from(t),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t().domain(r).range(n).unknown(e)},nP.apply(i,arguments)},scaleTime:()=>c_,scaleUtc:()=>cT,tickFormat:()=>ig});var o=r(60687),i=r(44493),a=r(29523),c=r(96834),u=r(15079),l=r(48730),s=r(15880),f=r(62688);let p=(0,f.A)("UserX",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]),d=(0,f.A)("Voicemail",[["circle",{cx:"6",cy:"12",r:"4",key:"1ehtga"}],["circle",{cx:"18",cy:"12",r:"4",key:"4vafl8"}],["line",{x1:"6",x2:"18",y1:"16",y2:"16",key:"pmt8us"}]]),h=(0,f.A)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]);var y=r(48340),v=r(21235);let m=(0,f.A)("ChartNoAxesColumnIncreasing",[["line",{x1:"12",x2:"12",y1:"20",y2:"10",key:"1vz5eb"}],["line",{x1:"18",x2:"18",y1:"20",y2:"4",key:"cun8e5"}],["line",{x1:"6",x2:"6",y1:"20",y2:"16",key:"hq0ia6"}]]);var b=r(40228);let g=(0,f.A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]]),x=(0,f.A)("PhoneIncoming",[["polyline",{points:"16 2 16 8 22 8",key:"1ygljm"}],["line",{x1:"22",x2:"16",y1:"2",y2:"8",key:"1xzwqn"}],["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);var w=r(52477),O=r(41312),j=r(49384),A=r(43210),S=r.n(A),P=r(45603),E=r.n(P),k=r(63866),M=r.n(k),_=r(77822),T=r.n(_),N=r(40491),C=r.n(N),D=r(93490),I=r.n(D),B=function(t){return 0===t?0:t>0?1:-1},R=function(t){return M()(t)&&t.indexOf("%")===t.length-1},L=function(t){return I()(t)&&!T()(t)},z=function(t){return L(t)||M()(t)},U=0,$=function(t){var e=++U;return"".concat(t||"").concat(e)},F=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!L(t)&&!M()(t))return n;if(R(t)){var i=t.indexOf("%");r=e*parseFloat(t.slice(0,i))/100}else r=+t;return T()(r)&&(r=n),o&&r>e&&(r=e),r},q=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},W=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++){if(r[t[n]])return!0;r[t[n]]=!0}return!1},X=function(t,e){return L(t)&&L(e)?function(r){return t+r*(e-t)}:function(){return e}};function V(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):C()(t,e))===r}):null}var H=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]},G=r(37456),Y=r.n(G),K=r(5231),Z=r.n(K),Q=r(55048),J=r.n(Q),tt=r(93780);function te(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}function tr(t){return(tr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var tn=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],to=["points","pathLength"],ti={svg:["viewBox","children"],polygon:to,polyline:to},ta=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],tc=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,A.isValidElement)(t)&&(r=t.props),!J()(r))return null;var n={};return Object.keys(r).forEach(function(t){ta.includes(t)&&(n[t]=e||function(e){return r[t](r,e)})}),n},tu=function(t,e,r){if(!J()(t)||"object"!==tr(t))return null;var n=null;return Object.keys(t).forEach(function(o){var i=t[o];ta.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(t){return i(e,r,t),null})}),n},tl=["children"],ts=["children"];function tf(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function tp(t){return(tp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var td={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},th=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},ty=null,tv=null,tm=function t(e){if(e===ty&&Array.isArray(tv))return tv;var r=[];return A.Children.forEach(e,function(e){Y()(e)||((0,tt.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),tv=r,ty=e,r};function tb(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return th(t)}):[th(e)],tm(t).forEach(function(t){var e=C()(t,"type.displayName")||C()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function tg(t,e){var r=tb(t,e);return r&&r[0]}var tx=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!L(r)&&!(r<=0)&&!!L(n)&&!(n<=0)},tw=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],tO=function(t,e,r,n){var o,i=null!==(o=null==ti?void 0:ti[n])&&void 0!==o?o:[];return!Z()(t)&&(n&&i.includes(e)||tn.includes(e))||r&&ta.includes(e)},tj=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,A.isValidElement)(t)&&(n=t.props),!J()(n))return null;var o={};return Object.keys(n).forEach(function(t){var i;tO(null===(i=n)||void 0===i?void 0:i[t],t,e,r)&&(o[t]=n[t])}),o},tA=function t(e,r){if(e===r)return!0;var n=A.Children.count(e);if(n!==A.Children.count(r))return!1;if(0===n)return!0;if(1===n)return tS(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var i=e[o],a=r[o];if(Array.isArray(i)||Array.isArray(a)){if(!t(i,a))return!1}else if(!tS(i,a))return!1}return!0},tS=function(t,e){if(Y()(t)&&Y()(e))return!0;if(!Y()(t)&&!Y()(e)){var r=t.props||{},n=r.children,o=tf(r,tl),i=e.props||{},a=i.children,c=tf(i,ts);if(n&&a)return te(o,c)&&tA(n,a);if(!n&&!a)return te(o,c)}return!1},tP=function(t,e){var r=[],n={};return tm(t).forEach(function(t,o){var i;if((i=t)&&i.type&&M()(i.type)&&tw.indexOf(i.type)>=0)r.push(t);else if(t){var a=th(t.type),c=e[a]||{},u=c.handler,l=c.once;if(u&&(!l||!n[a])){var s=u(t,a,o);r.push(s),n[a]=!0}}}),r},tE=function(t){var e=t&&t.type;return e&&td[e]?td[e]:null};function tk(t){return(tk="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tM(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t_(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tM(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=tk(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tk(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tk(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tM(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tT(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var tN=(0,A.forwardRef)(function(t,e){var r,n=t.aspect,o=t.initialDimension,i=void 0===o?{width:-1,height:-1}:o,a=t.width,c=void 0===a?"100%":a,u=t.height,l=void 0===u?"100%":u,s=t.minWidth,f=void 0===s?0:s,p=t.minHeight,d=t.maxHeight,h=t.children,y=t.debounce,v=void 0===y?0:y,m=t.id,b=t.className,g=t.onResize,x=t.style,w=(0,A.useRef)(null),O=(0,A.useRef)();O.current=g,(0,A.useImperativeHandle)(e,function(){return Object.defineProperty(w.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),w.current},configurable:!0})});var P=function(t){if(Array.isArray(t))return t}(r=(0,A.useState)({containerWidth:i.width,containerHeight:i.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(r,2)||function(t,e){if(t){if("string"==typeof t)return tT(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tT(t,e)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),k=P[0],M=P[1],_=(0,A.useCallback)(function(t,e){M(function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,A.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;_(n,o),null===(e=O.current)||void 0===e||e.call(O,n,o)};v>0&&(t=E()(t,v,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=w.current.getBoundingClientRect();return _(r.width,r.height),e.observe(w.current),function(){e.disconnect()}},[_,v]);var T=(0,A.useMemo)(function(){var t=k.containerWidth,e=k.containerHeight;if(t<0||e<0)return null;H(R(c)||R(l),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",c,l),H(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=R(c)?t:c,o=R(l)?e:l;n&&n>0&&(r?o=r/n:o&&(r=o*n),d&&o>d&&(o=d)),H(r>0||o>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,o,c,l,f,p,n);var i=!Array.isArray(h)&&th(h.type).endsWith("Chart");return S().Children.map(h,function(t){return S().isValidElement(t)?(0,A.cloneElement)(t,t_({width:r,height:o},i?{style:t_({height:"100%",width:"100%",maxHeight:o,maxWidth:r},t.props.style)}:{})):t})},[n,h,l,d,p,f,k,c]);return S().createElement("div",{id:m?"".concat(m):void 0,className:(0,j.A)("recharts-responsive-container",b),style:t_(t_({},void 0===x?{}:x),{},{width:c,height:l,minWidth:f,minHeight:p,maxHeight:d}),ref:w},T)}),tC=r(34990),tD=r.n(tC),tI=r(85938),tB=r.n(tI);function tR(t,e){if(!t)throw Error("Invariant failed")}var tL=["children","width","height","viewBox","className","style","title","desc"];function tz(){return(tz=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tU(t){var e=t.children,r=t.width,n=t.height,o=t.viewBox,i=t.className,a=t.style,c=t.title,u=t.desc,l=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,tL),s=o||{width:r,height:n,x:0,y:0},f=(0,j.A)("recharts-surface",i);return S().createElement("svg",tz({},tj(l,!0,"svg"),{className:f,width:r,height:n,style:a,viewBox:"".concat(s.x," ").concat(s.y," ").concat(s.width," ").concat(s.height)}),S().createElement("title",null,c),S().createElement("desc",null,u),e)}var t$=["children","className"];function tF(){return(tF=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tq=S().forwardRef(function(t,e){var r=t.children,n=t.className,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,t$),i=(0,j.A)("recharts-layer",n);return S().createElement("g",tF({className:i},tj(o,!0),{ref:e}),r)});function tW(t){return(tW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tX(){return(tX=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tV(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tH(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tG(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tH(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=tW(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tW(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tH(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tY(t){return Array.isArray(t)&&z(t[0])&&z(t[1])?t.join(" ~ "):t}var tK=function(t){var e=t.separator,r=void 0===e?" : ":e,n=t.contentStyle,o=t.itemStyle,i=void 0===o?{}:o,a=t.labelStyle,c=t.payload,u=t.formatter,l=t.itemSorter,s=t.wrapperClassName,f=t.labelClassName,p=t.label,d=t.labelFormatter,h=t.accessibilityLayer,y=tG({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),v=tG({margin:0},void 0===a?{}:a),m=!Y()(p),b=m?p:"",g=(0,j.A)("recharts-default-tooltip",s),x=(0,j.A)("recharts-tooltip-label",f);return m&&d&&null!=c&&(b=d(p,c)),S().createElement("div",tX({className:g,style:y},void 0!==h&&h?{role:"status","aria-live":"assertive"}:{}),S().createElement("p",{className:x,style:v},S().isValidElement(b)?b:"".concat(b)),function(){if(c&&c.length){var t=(l?tB()(c,l):c).map(function(t,e){if("none"===t.type)return null;var n=tG({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},i),o=t.formatter||u||tY,a=t.value,l=t.name,s=a,f=l;if(o&&null!=s&&null!=f){var p=o(a,l,t,e,c);if(Array.isArray(p)){var d=function(t){if(Array.isArray(t))return t}(p)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(p,2)||function(t,e){if(t){if("string"==typeof t)return tV(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tV(t,e)}}(p,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();s=d[0],f=d[1]}else s=p}return S().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:n},z(f)?S().createElement("span",{className:"recharts-tooltip-item-name"},f):null,z(f)?S().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,S().createElement("span",{className:"recharts-tooltip-item-value"},s),S().createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return S().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function tZ(t){return(tZ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tQ(t,e,r){var n;return(n=function(t,e){if("object"!=tZ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tZ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==tZ(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var tJ="recharts-tooltip-wrapper",t0={visibility:"hidden"};function t1(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,c=t.tooltipDimension,u=t.viewBox,l=t.viewBoxDimension;if(i&&L(i[n]))return i[n];var s=r[n]-c-o,f=r[n]+o;return e[n]?a[n]?s:f:a[n]?s<u[n]?Math.max(f,u[n]):Math.max(s,u[n]):f+c>u[n]+l?Math.max(s,u[n]):Math.max(f,u[n])}function t2(t){return(t2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t4(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t3(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t4(Object(r),!0).forEach(function(e){t7(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t4(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function t5(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(t5=function(){return!!t})()}function t6(t){return(t6=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function t8(t,e){return(t8=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function t7(t,e,r){return(e=t9(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function t9(t){var e=function(t,e){if("object"!=t2(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t2(e)?e:e+""}var et=function(t){var e;function r(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r);for(var t,e,n,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=r,n=[].concat(i),e=t6(e),t7(t=function(t,e){if(e&&("object"===t2(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,t5()?Reflect.construct(e,n||[],t6(this).constructor):e.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),t7(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,o,i;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=t.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=t.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&t8(t,e)}(r,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,n,o,i,a,c,u,l,s,f,p,d,h,y,v,m,b,g=this,x=this.props,w=x.active,O=x.allowEscapeViewBox,A=x.animationDuration,P=x.animationEasing,E=x.children,k=x.coordinate,M=x.hasPayload,_=x.isAnimationActive,T=x.offset,N=x.position,C=x.reverseDirection,D=x.useTranslate3d,I=x.viewBox,B=x.wrapperStyle,R=(f=(t={allowEscapeViewBox:O,coordinate:k,offsetTopLeft:T,position:N,reverseDirection:C,tooltipBox:this.state.lastBoundingBox,useTranslate3d:D,viewBox:I}).allowEscapeViewBox,p=t.coordinate,d=t.offsetTopLeft,h=t.position,y=t.reverseDirection,v=t.tooltipBox,m=t.useTranslate3d,b=t.viewBox,v.height>0&&v.width>0&&p?(r=(e={translateX:l=t1({allowEscapeViewBox:f,coordinate:p,key:"x",offsetTopLeft:d,position:h,reverseDirection:y,tooltipDimension:v.width,viewBox:b,viewBoxDimension:b.width}),translateY:s=t1({allowEscapeViewBox:f,coordinate:p,key:"y",offsetTopLeft:d,position:h,reverseDirection:y,tooltipDimension:v.height,viewBox:b,viewBoxDimension:b.height}),useTranslate3d:m}).translateX,n=e.translateY,u={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):u=t0,{cssProperties:u,cssClasses:(i=(o={translateX:l,translateY:s,coordinate:p}).coordinate,a=o.translateX,c=o.translateY,(0,j.A)(tJ,tQ(tQ(tQ(tQ({},"".concat(tJ,"-right"),L(a)&&i&&L(i.x)&&a>=i.x),"".concat(tJ,"-left"),L(a)&&i&&L(i.x)&&a<i.x),"".concat(tJ,"-bottom"),L(c)&&i&&L(i.y)&&c>=i.y),"".concat(tJ,"-top"),L(c)&&i&&L(i.y)&&c<i.y)))}),z=R.cssClasses,U=R.cssProperties,$=t3(t3({transition:_&&w?"transform ".concat(A,"ms ").concat(P):void 0},U),{},{pointerEvents:"none",visibility:!this.state.dismissed&&w&&M?"visible":"hidden",position:"absolute",top:0,left:0},B);return S().createElement("div",{tabIndex:-1,className:z,style:$,ref:function(t){g.wrapperNode=t}},E)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,t9(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(A.PureComponent),ee={isSsr:!0,get:function(t){return ee[t]},set:function(t,e){if("string"==typeof t)ee[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){ee[e]=t[e]})}}},er=r(36315),en=r.n(er);function eo(t,e,r){return!0===e?en()(t,r):Z()(e)?en()(t,e):t}function ei(t){return(ei="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ea(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ec(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ea(Object(r),!0).forEach(function(e){ef(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ea(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function eu(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eu=function(){return!!t})()}function el(t){return(el=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function es(t,e){return(es=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ef(t,e,r){return(e=ep(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ep(t){var e=function(t,e){if("object"!=ei(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ei(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ei(e)?e:e+""}function ed(t){return t.dataKey}var eh=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=el(t),function(t,e){if(e&&("object"===ei(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,eu()?Reflect.construct(t,e||[],el(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&es(t,e)}(r,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,n=r.active,o=r.allowEscapeViewBox,i=r.animationDuration,a=r.animationEasing,c=r.content,u=r.coordinate,l=r.filterNull,s=r.isAnimationActive,f=r.offset,p=r.payload,d=r.payloadUniqBy,h=r.position,y=r.reverseDirection,v=r.useTranslate3d,m=r.viewBox,b=r.wrapperStyle,g=null!=p?p:[];l&&g.length&&(g=eo(p.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),d,ed));var x=g.length>0;return S().createElement(et,{allowEscapeViewBox:o,animationDuration:i,animationEasing:a,isAnimationActive:s,active:n,coordinate:u,hasPayload:x,offset:f,position:h,reverseDirection:y,useTranslate3d:v,viewBox:m,wrapperStyle:b},(t=ec(ec({},this.props),{},{payload:g}),S().isValidElement(c)?S().cloneElement(c,t):"function"==typeof c?S().createElement(c,t):S().createElement(tK,t)))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ep(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(A.PureComponent);ef(eh,"displayName","Tooltip"),ef(eh,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!ee.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var ey=r(69433),ev=r.n(ey);let em=Math.cos,eb=Math.sin,eg=Math.sqrt,ex=Math.PI,ew=2*ex,eO={draw(t,e){let r=eg(e/ex);t.moveTo(r,0),t.arc(0,0,r,0,ew)}},ej=eg(1/3),eA=2*ej,eS=eb(ex/10)/eb(7*ex/10),eP=eb(ew/10)*eS,eE=-em(ew/10)*eS,ek=eg(3),eM=eg(3)/2,e_=1/eg(12),eT=(e_/2+1)*3;function eN(t){return function(){return t}}let eC=Math.PI,eD=2*eC,eI=eD-1e-6;function eB(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class eR{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?eB:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return eB;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,o,i){this._append`C${+t},${+e},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,r,n,o){if(t*=1,e*=1,r*=1,n*=1,(o*=1)<0)throw Error(`negative radius: ${o}`);let i=this._x1,a=this._y1,c=r-t,u=n-e,l=i-t,s=a-e,f=l*l+s*s;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>1e-6){if(Math.abs(s*c-u*l)>1e-6&&o){let p=r-i,d=n-a,h=c*c+u*u,y=Math.sqrt(h),v=Math.sqrt(f),m=o*Math.tan((eC-Math.acos((h+f-(p*p+d*d))/(2*y*v)))/2),b=m/v,g=m/y;Math.abs(b-1)>1e-6&&this._append`L${t+b*l},${e+b*s}`,this._append`A${o},${o},0,0,${+(s*p>l*d)},${this._x1=t+g*c},${this._y1=e+g*u}`}else this._append`L${this._x1=t},${this._y1=e}`}}arc(t,e,r,n,o,i){if(t*=1,e*=1,r*=1,i=!!i,r<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),u=t+a,l=e+c,s=1^i,f=i?n-o:o-n;null===this._x1?this._append`M${u},${l}`:(Math.abs(this._x1-u)>1e-6||Math.abs(this._y1-l)>1e-6)&&this._append`L${u},${l}`,r&&(f<0&&(f=f%eD+eD),f>eI?this._append`A${r},${r},0,1,${s},${t-a},${e-c}A${r},${r},0,1,${s},${this._x1=u},${this._y1=l}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=eC)},${s},${this._x1=t+r*Math.cos(o)},${this._y1=e+r*Math.sin(o)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function eL(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new eR(e)}function ez(t){return(ez="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}eR.prototype,eg(3),eg(3);var eU=["type","size","sizeType"];function e$(){return(e$=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eF(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eq(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eF(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=ez(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ez(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ez(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eF(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var eW={symbolCircle:eO,symbolCross:{draw(t,e){let r=eg(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=eg(e/eA),n=r*ej;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=eg(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=eg(.8908130915292852*e),n=eP*r,o=eE*r;t.moveTo(0,-r),t.lineTo(n,o);for(let e=1;e<5;++e){let i=ew*e/5,a=em(i),c=eb(i);t.lineTo(c*r,-a*r),t.lineTo(a*n-c*o,c*n+a*o)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-eg(e/(3*ek));t.moveTo(0,2*r),t.lineTo(-ek*r,-r),t.lineTo(ek*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=eg(e/eT),n=r/2,o=r*e_,i=r*e_+r,a=-n;t.moveTo(n,o),t.lineTo(n,i),t.lineTo(a,i),t.lineTo(-.5*n-eM*o,eM*n+-.5*o),t.lineTo(-.5*n-eM*i,eM*n+-.5*i),t.lineTo(-.5*a-eM*i,eM*a+-.5*i),t.lineTo(-.5*n+eM*o,-.5*o-eM*n),t.lineTo(-.5*n+eM*i,-.5*i-eM*n),t.lineTo(-.5*a+eM*i,-.5*i-eM*a),t.closePath()}}},eX=Math.PI/180,eV=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*eX;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},eH=function(t){var e,r=t.type,n=void 0===r?"circle":r,o=t.size,i=void 0===o?64:o,a=t.sizeType,c=void 0===a?"area":a,u=eq(eq({},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,eU)),{},{type:n,size:i,sizeType:c}),l=u.className,s=u.cx,f=u.cy,p=tj(u,!0);return s===+s&&f===+f&&i===+i?S().createElement("path",e$({},p,{className:(0,j.A)("recharts-symbols",l),transform:"translate(".concat(s,", ").concat(f,")"),d:(e=eW["symbol".concat(ev()(n))]||eO,(function(t,e){let r=null,n=eL(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"==typeof t?t:eN(t||eO),e="function"==typeof e?e:eN(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:eN(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:eN(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o})().type(e).size(eV(i,c,n))())})):null};function eG(t){return(eG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eY(){return(eY=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eK(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}eH.registerSymbol=function(t,e){eW["symbol".concat(ev()(t))]=e};function eZ(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eZ=function(){return!!t})()}function eQ(t){return(eQ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function eJ(t,e){return(eJ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function e0(t,e,r){return(e=e1(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function e1(t){var e=function(t,e){if("object"!=eG(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eG(e)?e:e+""}var e2=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=eQ(t),function(t,e){if(e&&("object"===eG(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,eZ()?Reflect.construct(t,e||[],eQ(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&eJ(t,e)}(r,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,n=32/3,o=t.inactive?e:t.color;if("plainline"===t.type)return S().createElement("line",{strokeWidth:4,fill:"none",stroke:o,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return S().createElement("path",{strokeWidth:4,fill:"none",stroke:o,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return S().createElement("path",{stroke:"none",fill:o,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(S().isValidElement(t.legendIcon)){var i=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eK(Object(r),!0).forEach(function(e){e0(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eK(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete i.legendIcon,S().cloneElement(t.legendIcon,i)}return S().createElement(eH,{fill:o,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,n=e.iconSize,o=e.layout,i=e.formatter,a=e.inactiveColor,c={x:0,y:0,width:32,height:32},u={display:"horizontal"===o?"inline-block":"block",marginRight:10},l={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var o=e.formatter||i,s=(0,j.A)(e0(e0({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var f=Z()(e.value)?null:e.value;H(!Z()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var p=e.inactive?a:e.color;return S().createElement("li",eY({className:s,style:u,key:"legend-item-".concat(r)},tu(t.props,e,r)),S().createElement(tU,{width:n,height:n,viewBox:c,style:l},t.renderIcon(e)),S().createElement("span",{className:"recharts-legend-item-text",style:{color:p}},o?o(f,e,r):f))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,n=t.align;return e&&e.length?S().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,e1(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(A.PureComponent);function e4(t){return(e4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}e0(e2,"displayName","Legend"),e0(e2,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var e3=["ref"];function e5(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function e6(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?e5(Object(r),!0).forEach(function(e){re(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):e5(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function e8(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,rr(n.key),n)}}function e7(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(e7=function(){return!!t})()}function e9(t){return(e9=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function rt(t,e){return(rt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function re(t,e,r){return(e=rr(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function rr(t){var e=function(t,e){if("object"!=e4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=e4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==e4(e)?e:e+""}function rn(t){return t.value}var ro=function(t){var e,r;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n);for(var t,e,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=e9(e),re(t=function(t,e){if(e&&("object"===e4(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,e7()?Reflect.construct(e,r||[],e9(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&rt(t,e)}(n,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?e6({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),e6(e6({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,n=e.width,o=e.height,i=e.wrapperStyle,a=e.payloadUniqBy,c=e.payload,u=e6(e6({position:"absolute",width:n||"auto",height:o||"auto"},this.getDefaultPosition(i)),i);return S().createElement("div",{className:"recharts-legend-wrapper",style:u,ref:function(e){t.wrapperNode=e}},function(t,e){if(S().isValidElement(t))return S().cloneElement(t,e);if("function"==typeof t)return S().createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,e3);return S().createElement(e2,r)}(r,e6(e6({},this.props),{},{payload:eo(c,a,rn)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=e6(e6({},this.defaultProps),t.props).layout;return"vertical"===r&&L(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&e8(n.prototype,e),r&&e8(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(A.PureComponent);function ri(){return(ri=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}re(ro,"displayName","Legend"),re(ro,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var ra=function(t){var e=t.cx,r=t.cy,n=t.r,o=t.className,i=(0,j.A)("recharts-dot",o);return e===+e&&r===+r&&n===+n?S().createElement("circle",ri({},tj(t,!1),tc(t),{className:i,cx:e,cy:r,r:n})):null},rc=r(87955),ru=r.n(rc),rl=Object.getOwnPropertyNames,rs=Object.getOwnPropertySymbols,rf=Object.prototype.hasOwnProperty;function rp(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function rd(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var c=t(e,r,n);return o.delete(e),o.delete(r),c}}function rh(t){return rl(t).concat(rs(t))}var ry=Object.hasOwn||function(t,e){return rf.call(t,e)};function rv(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var rm=Object.getOwnPropertyDescriptor,rb=Object.keys;function rg(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function rx(t,e){return rv(t.getTime(),e.getTime())}function rw(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function rO(t,e){return t===e}function rj(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.entries(),u=0;(n=c.next())&&!n.done;){for(var l=e.entries(),s=!1,f=0;(o=l.next())&&!o.done;){if(a[f]){f++;continue}var p=n.value,d=o.value;if(r.equals(p[0],d[0],u,f,t,e,r)&&r.equals(p[1],d[1],p[0],d[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;u++}return!0}function rA(t,e,r){var n=rb(t),o=n.length;if(rb(e).length!==o)return!1;for(;o-- >0;)if(!rT(t,e,r,n[o]))return!1;return!0}function rS(t,e,r){var n,o,i,a=rh(t),c=a.length;if(rh(e).length!==c)return!1;for(;c-- >0;)if(!rT(t,e,r,n=a[c])||(o=rm(t,n),i=rm(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function rP(t,e){return rv(t.valueOf(),e.valueOf())}function rE(t,e){return t.source===e.source&&t.flags===e.flags}function rk(t,e,r){var n,o,i=t.size;if(i!==e.size)return!1;if(!i)return!0;for(var a=Array(i),c=t.values();(n=c.next())&&!n.done;){for(var u=e.values(),l=!1,s=0;(o=u.next())&&!o.done;){if(!a[s]&&r.equals(n.value,o.value,n.value,o.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function rM(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function r_(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function rT(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||ry(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var rN=Array.isArray,rC="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,rD=Object.assign,rI=Object.prototype.toString.call.bind(Object.prototype.toString),rB=rR();function rR(t){void 0===t&&(t={});var e,r,n,o,i,a,c,u,l,s,f,p,d,h=t.circular,y=t.createInternalComparator,v=t.createState,m=t.strict,b=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?rS:rg,areDatesEqual:rx,areErrorsEqual:rw,areFunctionsEqual:rO,areMapsEqual:n?rp(rj,rS):rj,areNumbersEqual:rv,areObjectsEqual:n?rS:rA,arePrimitiveWrappersEqual:rP,areRegExpsEqual:rE,areSetsEqual:n?rp(rk,rS):rk,areTypedArraysEqual:n?rS:rM,areUrlsEqual:r_};if(r&&(o=rD({},o,r(o))),e){var i=rd(o.areArraysEqual),a=rd(o.areMapsEqual),c=rd(o.areObjectsEqual),u=rd(o.areSetsEqual);o=rD({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return o}(t)).areArraysEqual,n=e.areDatesEqual,o=e.areErrorsEqual,i=e.areFunctionsEqual,a=e.areMapsEqual,c=e.areNumbersEqual,u=e.areObjectsEqual,l=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,p=e.areTypedArraysEqual,d=e.areUrlsEqual,function(t,e,h){if(t===e)return!0;if(null==t||null==e)return!1;var y=typeof t;if(y!==typeof e)return!1;if("object"!==y)return"number"===y?c(t,e,h):"function"===y&&i(t,e,h);var v=t.constructor;if(v!==e.constructor)return!1;if(v===Object)return u(t,e,h);if(rN(t))return r(t,e,h);if(null!=rC&&rC(t))return p(t,e,h);if(v===Date)return n(t,e,h);if(v===RegExp)return s(t,e,h);if(v===Map)return a(t,e,h);if(v===Set)return f(t,e,h);var m=rI(t);return"[object Date]"===m?n(t,e,h):"[object RegExp]"===m?s(t,e,h):"[object Map]"===m?a(t,e,h):"[object Set]"===m?f(t,e,h):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof e.then&&u(t,e,h):"[object URL]"===m?d(t,e,h):"[object Error]"===m?o(t,e,h):"[object Arguments]"===m?u(t,e,h):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&l(t,e,h)}),g=y?y(b):function(t,e,r,n,o,i,a){return b(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var c=n(),u=c.cache;return r(t,a,{cache:void 0===u?e?new WeakMap:void 0:u,equals:o,meta:c.meta,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:void 0!==h&&h,comparator:b,createState:v,equals:g,strict:void 0!==m&&m})}function rL(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>e)t(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function rz(t){return(rz="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rU(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function r$(t){return(r$="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rF(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rq(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rF(Object(r),!0).forEach(function(e){rW(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rF(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rW(t,e,r){var n;return(n=function(t,e){if("object"!==r$(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==r$(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===r$(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}rR({strict:!0}),rR({circular:!0}),rR({circular:!0,strict:!0}),rR({createInternalComparator:function(){return rv}}),rR({strict:!0,createInternalComparator:function(){return rv}}),rR({circular:!0,createInternalComparator:function(){return rv}}),rR({circular:!0,createInternalComparator:function(){return rv},strict:!0});var rX=function(t){return t},rV=function(t,e){return Object.keys(e).reduce(function(r,n){return rq(rq({},r),{},rW({},n,t(n,e[n])))},{})},rH=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},rG=function(t,e,r,n,o,i,a,c){};function rY(t,e){if(t){if("string"==typeof t)return rK(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rK(t,e)}}function rK(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var rZ=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},rQ=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},rJ=function(t,e){return function(r){return rQ(rZ(t,e),r)}},r0=function(){for(var t,e,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],c=n[2],u=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,c=1,u=1;break;case"ease":i=.25,a=.1,c=.25,u=1;break;case"ease-in":i=.42,a=0,c=1,u=1;break;case"ease-out":i=.42,a=0,c=.58,u=1;break;case"ease-in-out":i=0,a=0,c=.58,u=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(t){if(Array.isArray(t))return t}(s=l[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(s,4)||rY(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],c=f[2],u=f[3]}else rG(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}rG([i,c,a,u].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=rJ(i,c),d=rJ(a,u),h=(t=i,e=c,function(r){var n;return rQ([].concat(function(t){if(Array.isArray(t))return rK(t)}(n=rZ(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||rY(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var o,i=p(r)-e,a=h(r);if(1e-4>Math.abs(i-e)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return d(r)};return y.isStepper=!1,y},r1=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,c=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,c=n*a/1e3+t;return 1e-4>Math.abs(c-e)&&1e-4>Math.abs(i)?[e,0]:[c,i]};return c.isStepper=!0,c.dt=a,c},r2=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return r0(n);case"spring":return r1();default:if("cubic-bezier"===n.split("(")[0])return r0(n);rG(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(rG(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function r4(t){return(r4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function r3(t){return function(t){if(Array.isArray(t))return r9(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||r7(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r5(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function r6(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?r5(Object(r),!0).forEach(function(e){r8(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):r5(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function r8(t,e,r){var n;return(n=function(t,e){if("object"!==r4(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==r4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===r4(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function r7(t,e){if(t){if("string"==typeof t)return r9(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return r9(t,e)}}function r9(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nt=function(t,e,r){return t+(e-t)*r},ne=function(t){return t.from!==t.to},nr=function t(e,r,n){var o=rV(function(t,r){if(ne(r)){var n,o=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(n,2)||r7(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return r6(r6({},r),{},{from:i,velocity:a})}return r},r);return n<1?rV(function(t,e){return ne(e)?r6(r6({},e),{},{velocity:nt(e.velocity,o[t].velocity,n),from:nt(e.from,o[t].from,n)}):e},r):t(e,o,n-1)};let nn=function(t,e,r,n,o){var i,a,c=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),u=c.reduce(function(r,n){return r6(r6({},r),{},r8({},n,[t[n],e[n]]))},{}),l=c.reduce(function(r,n){return r6(r6({},r),{},r8({},n,{from:t[n],velocity:0,to:e[n]}))},{}),s=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;l=nr(r,l,a),o(r6(r6(r6({},t),e),rV(function(t,e){return e.from},l))),i=n,Object.values(l).filter(ne).length&&(s=requestAnimationFrame(f))}:function(i){a||(a=i);var c=(i-a)/n,l=rV(function(t,e){return nt.apply(void 0,r3(e).concat([r(c)]))},u);if(o(r6(r6(r6({},t),e),l)),c<1)s=requestAnimationFrame(f);else{var p=rV(function(t,e){return nt.apply(void 0,r3(e).concat([r(1)]))},u);o(r6(r6(r6({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function no(t){return(no="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var ni=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function na(t){return function(t){if(Array.isArray(t))return nc(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return nc(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nc(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nc(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nu(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nl(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nu(Object(r),!0).forEach(function(e){ns(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nu(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ns(t,e,r){return(e=nf(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function nf(t){var e=function(t,e){if("object"!==no(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==no(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===no(e)?e:String(e)}function np(t,e){return(np=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function nd(t,e){if(e&&("object"===no(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return nh(t)}function nh(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function ny(t){return(ny=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var nv=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&np(t,e)}(o,t);var e,r,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=ny(o);return t=e?Reflect.construct(r,arguments,ny(this).constructor):r.apply(this,arguments),nd(this,t)});function o(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,o);var r=n.call(this,t,e),i=r.props,a=i.isActive,c=i.attributeName,u=i.from,l=i.to,s=i.steps,f=i.children,p=i.duration;if(r.handleStyleChange=r.handleStyleChange.bind(nh(r)),r.changeStyle=r.changeStyle.bind(nh(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),nd(r);if(s&&s.length)r.state={style:s[0].style};else if(u){if("function"==typeof f)return r.state={style:u},nd(r);r.state={style:c?ns({},c,u):u}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n){if(!r){var l={style:o?ns({},o,a):a};this.state&&u&&(o&&u[o]!==a||!o&&u!==a)&&this.setState(l);return}if(!rB(t.to,a)||!t.canBegin||!t.isActive){var s=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||i?c:t.to;if(this.state&&u){var p={style:o?ns({},o,f):f};(o&&u[o]!==f||!o&&u!==f)&&this.setState(p)}this.runAnimation(nl(nl({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,o=t.duration,i=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,l=nn(r,n,r2(i),o,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=l()},o,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,c=i.duration;return this.manager.start([o].concat(na(r.reduce(function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,l=n.properties,s=n.onAnimationEnd,f=o>0?r[o-1]:n,p=l||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(na(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:i,easing:c}),i]);var d=rH(p,i,c),h=nl(nl(nl({},f.style),u),{},{transition:d});return[].concat(na(t),[h,i,s]).filter(rX)},[a,Math.max(void 0===c?0:c,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){if(!this.manager){var e,r,n,o;this.manager=(r=function(){return null},n=!1,o=function t(e){if(!n){if(Array.isArray(e)){if(!e.length)return;var o=function(t){if(Array.isArray(t))return t}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(t,e){if(t){if("string"==typeof t)return rU(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rU(t,e)}}(e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);if("number"==typeof i){rL(t.bind(null,a),i);return}t(i),rL(t.bind(null,a));return}"object"===rz(e)&&r(e),"function"==typeof e&&e()}},{stop:function(){n=!0},start:function(t){n=!1,o(t)},subscribe:function(t){return r=t,function(){r=function(){return null}}}})}var i=t.begin,a=t.duration,c=t.attributeName,u=t.to,l=t.easing,s=t.onAnimationStart,f=t.onAnimationEnd,p=t.steps,d=t.children,h=this.manager;if(this.unSubscribe=h.subscribe(this.handleStyleChange),"function"==typeof l||"function"==typeof d||"spring"===l){this.runJSAnimation(t);return}if(p.length>1){this.runStepAnimation(t);return}var y=c?ns({},c,u):u,v=rH(Object.keys(y),a,l);h.start([s,i,nl(nl({},y),{},{transition:v}),a,f])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),n=(t.attributeName,t.easing,t.isActive),o=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,ni)),i=A.Children.count(e),a=this.state.style;if("function"==typeof e)return e(a);if(!n||0===i||r<=0)return e;var c=function(t){var e=t.props,r=e.style,n=e.className;return(0,A.cloneElement)(t,nl(nl({},o),{},{style:nl(nl({},void 0===r?{}:r),a),className:n}))};return 1===i?c(A.Children.only(e)):S().createElement("div",null,A.Children.map(e,function(t){return c(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,nf(n.key),n)}}(o.prototype,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(A.PureComponent);function nm(t){return(nm="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nb(){return(nb=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ng(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nx(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nw(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nx(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=nm(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nm(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nm(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nx(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}nv.displayName="Animate",nv.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},nv.propTypes={from:ru().oneOfType([ru().object,ru().string]),to:ru().oneOfType([ru().object,ru().string]),attributeName:ru().string,duration:ru().number,begin:ru().number,easing:ru().oneOfType([ru().string,ru().func]),steps:ru().arrayOf(ru().shape({duration:ru().number.isRequired,style:ru().object.isRequired,easing:ru().oneOfType([ru().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),ru().func]),properties:ru().arrayOf("string"),onAnimationEnd:ru().func})),children:ru().oneOfType([ru().node,ru().func]),isActive:ru().bool,canBegin:ru().bool,onAnimationEnd:ru().func,shouldReAnimate:ru().bool,onAnimationStart:ru().func,onAnimationReStart:ru().func};var nO=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&o instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(i+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),i+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(i+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),i+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(i+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),i+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(i+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},nj=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(o,o+a),l=Math.max(o,o+a),s=Math.min(i,i+c),f=Math.max(i,i+c);return r>=u&&r<=l&&n>=s&&n<=f}return!1},nA={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},nS=function(t){var e,r=nw(nw({},nA),t),n=(0,A.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,A.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return ng(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ng(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];(0,A.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&a(t)}catch(t){}},[]);var c=r.x,u=r.y,l=r.width,s=r.height,f=r.radius,p=r.className,d=r.animationEasing,h=r.animationDuration,y=r.animationBegin,v=r.isAnimationActive,m=r.isUpdateAnimationActive;if(c!==+c||u!==+u||l!==+l||s!==+s||0===l||0===s)return null;var b=(0,j.A)("recharts-rectangle",p);return m?S().createElement(nv,{canBegin:i>0,from:{width:l,height:s,x:c,y:u},to:{width:l,height:s,x:c,y:u},duration:h,animationEasing:d,isActive:m},function(t){var e=t.width,o=t.height,a=t.x,c=t.y;return S().createElement(nv,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:h,isActive:v,easing:d},S().createElement("path",nb({},tj(r,!0),{className:b,d:nO(a,c,e,o,f),ref:n})))}):S().createElement("path",nb({},tj(r,!0),{className:b,d:nO(c,u,l,s,f)}))};function nP(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function nE(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}class nk extends Map{constructor(t,e=n_){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(nM(this,t))}has(t){return super.has(nM(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function nM({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function n_(t){return null!==t&&"object"==typeof t?t.valueOf():t}let nT=Symbol("implicit");function nN(){var t=new nk,e=[],r=[],n=nT;function o(o){let i=t.get(o);if(void 0===i){if(n!==nT)return n;t.set(o,i=e.push(o)-1)}return r[i%r.length]}return o.domain=function(r){if(!arguments.length)return e.slice();for(let n of(e=[],t=new nk,r))t.has(n)||t.set(n,e.push(n)-1);return o},o.range=function(t){return arguments.length?(r=Array.from(t),o):r.slice()},o.unknown=function(t){return arguments.length?(n=t,o):n},o.copy=function(){return nN(e,r).unknown(n)},nP.apply(o,arguments),o}function nC(){var t,e,r=nN().unknown(void 0),n=r.domain,o=r.range,i=0,a=1,c=!1,u=0,l=0,s=.5;function f(){var r=n().length,f=a<i,p=f?a:i,d=f?i:a;t=(d-p)/Math.max(1,r-u+2*l),c&&(t=Math.floor(t)),p+=(d-p-t*(r-u))*s,e=t*(1-u),c&&(p=Math.round(p),e=Math.round(e));var h=(function(t,e,r){t*=1,e*=1,r=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((e-t)/r)),i=Array(o);++n<o;)i[n]=t+n*r;return i})(r).map(function(e){return p+t*e});return o(f?h.reverse():h)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([i,a]=t,i*=1,a*=1,f()):[i,a]},r.rangeRound=function(t){return[i,a]=t,i*=1,a*=1,c=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(c=!!t,f()):c},r.padding=function(t){return arguments.length?(u=Math.min(1,l=+t),f()):u},r.paddingInner=function(t){return arguments.length?(u=Math.min(1,t),f()):u},r.paddingOuter=function(t){return arguments.length?(l=+t,f()):l},r.align=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),f()):s},r.copy=function(){return nC(n(),[i,a]).round(c).paddingInner(u).paddingOuter(l).align(s)},nP.apply(f(),arguments)}function nD(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(nC.apply(null,arguments).paddingInner(1))}function nI(t){return(nI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nB(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nR(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nB(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=nI(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nI(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nI(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nB(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nL(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nz={widthCache:{},cacheCount:0},nU={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},n$="recharts_measurement_span",nF=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||ee.isSsr)return{width:0,height:0};var n=(Object.keys(e=nR({},r)).forEach(function(t){e[t]||delete e[t]}),e),o=JSON.stringify({text:t,copyStyle:n});if(nz.widthCache[o])return nz.widthCache[o];try{var i=document.getElementById(n$);i||((i=document.createElement("span")).setAttribute("id",n$),i.setAttribute("aria-hidden","true"),document.body.appendChild(i));var a=nR(nR({},nU),n);Object.assign(i.style,a),i.textContent="".concat(t);var c=i.getBoundingClientRect(),u={width:c.width,height:c.height};return nz.widthCache[o]=u,++nz.cacheCount>2e3&&(nz.cacheCount=0,nz.widthCache={}),u}catch(t){return{width:0,height:0}}};function nq(t){return(nq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nW(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nX(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nX(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nX(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nV(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=nq(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nq(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nq(e)?e:e+""}(n.key),n)}}var nH=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nG=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nY=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,nK=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,nZ={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},nQ=Object.keys(nZ),nJ=function(){var t,e;function r(t,e){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,r),this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||nY.test(e)||(this.num=NaN,this.unit=""),nQ.includes(e)&&(this.num=t*nZ[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=nW(null!==(e=nK.exec(t))&&void 0!==e?e:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],t&&nV(r.prototype,t),e&&nV(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function n0(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=nW(null!==(r=nH.exec(e))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],c=nJ.parse(null!=o?o:""),u=nJ.parse(null!=a?a:""),l="*"===i?c.multiply(u):c.divide(u);if(l.isNaN())return"NaN";e=e.replace(nH,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=nW(null!==(s=nG.exec(e))&&void 0!==s?s:[],4),p=f[1],d=f[2],h=f[3],y=nJ.parse(null!=p?p:""),v=nJ.parse(null!=h?h:""),m="+"===d?y.add(v):y.subtract(v);if(m.isNaN())return"NaN";e=e.replace(nG,m.toString())}return e}var n1=/\(([^()]*)\)/;function n2(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=nW(n1.exec(e),2)[1];e=e.replace(n1,n0(r))}return e}(e),e=n0(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var n4=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],n3=["dx","dy","angle","className","breakAll"];function n5(){return(n5=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function n6(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function n8(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return n7(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return n7(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function n7(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var n9=/[ \f\n\r\t\v\u2028\u2029]+/,ot=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];Y()(e)||(o=r?e.toString().split(""):e.toString().split(n9));var i=o.map(function(t){return{word:t,width:nF(t,n).width}}),a=r?0:nF("\xa0",n).width;return{wordsWithComputedWidth:i,spaceWidth:a}}catch(t){return null}},oe=function(t,e,r,n,o){var i,a=t.maxLines,c=t.children,u=t.style,l=t.breakAll,s=L(a),f=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var i=e.word,a=e.width,c=t[t.length-1];return c&&(null==n||o||c.width+a+r<Number(n))?(c.words.push(i),c.width+=a+r):t.push({words:[i],width:a}),t},[])},p=f(e);if(!s)return p;for(var d=function(t){var e=f(ot({breakAll:l,style:u,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},h=0,y=c.length-1,v=0;h<=y&&v<=c.length-1;){var m=Math.floor((h+y)/2),b=n8(d(m-1),2),g=b[0],x=b[1],w=n8(d(m),1)[0];if(g||w||(h=m+1),g&&w&&(y=m-1),!g&&w){i=x;break}v++}return i||p},or=function(t){return[{words:Y()(t)?[]:t.toString().split(n9)}]},on=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!ee.isSsr){var c=ot({breakAll:i,children:n,style:o});if(!c)return or(n);var u=c.wordsWithComputedWidth,l=c.spaceWidth;return oe({breakAll:i,children:n,maxLines:a,style:o},u,l,e,r)}return or(n)},oo="#808080",oi=function(t){var e,r=t.x,n=void 0===r?0:r,o=t.y,i=void 0===o?0:o,a=t.lineHeight,c=void 0===a?"1em":a,u=t.capHeight,l=void 0===u?"0.71em":u,s=t.scaleToFit,f=void 0!==s&&s,p=t.textAnchor,d=t.verticalAnchor,h=t.fill,y=void 0===h?oo:h,v=n6(t,n4),m=(0,A.useMemo)(function(){return on({breakAll:v.breakAll,children:v.children,maxLines:v.maxLines,scaleToFit:f,style:v.style,width:v.width})},[v.breakAll,v.children,v.maxLines,f,v.style,v.width]),b=v.dx,g=v.dy,x=v.angle,w=v.className,O=v.breakAll,P=n6(v,n3);if(!z(n)||!z(i))return null;var E=n+(L(b)?b:0),k=i+(L(g)?g:0);switch(void 0===d?"end":d){case"start":e=n2("calc(".concat(l,")"));break;case"middle":e=n2("calc(".concat((m.length-1)/2," * -").concat(c," + (").concat(l," / 2))"));break;default:e=n2("calc(".concat(m.length-1," * -").concat(c,")"))}var M=[];if(f){var _=m[0].width,T=v.width;M.push("scale(".concat((L(T)?T/_:1)/_,")"))}return x&&M.push("rotate(".concat(x,", ").concat(E,", ").concat(k,")")),M.length&&(P.transform=M.join(" ")),S().createElement("text",n5({},tj(P,!0),{x:E,y:k,className:(0,j.A)("recharts-text",w),textAnchor:void 0===p?"start":p,fill:y.includes("url")?oo:y}),m.map(function(t,r){var n=t.words.join(O?"":" ");return S().createElement("tspan",{x:E,dy:0===r?e:c,key:"".concat(n,"-").concat(r)},n)}))};let oa=Math.sqrt(50),oc=Math.sqrt(10),ou=Math.sqrt(2);function ol(t,e,r){let n,o,i;let a=(e-t)/Math.max(0,r),c=Math.floor(Math.log10(a)),u=a/Math.pow(10,c),l=u>=oa?10:u>=oc?5:u>=ou?2:1;return(c<0?(n=Math.round(t*(i=Math.pow(10,-c)/l)),o=Math.round(e*i),n/i<t&&++n,o/i>e&&--o,i=-i):(n=Math.round(t/(i=Math.pow(10,c)*l)),o=Math.round(e/i),n*i<t&&++n,o*i>e&&--o),o<n&&.5<=r&&r<2)?ol(t,e,2*r):[n,o,i]}function os(t,e,r){if(e*=1,t*=1,!((r*=1)>0))return[];if(t===e)return[t];let n=e<t,[o,i,a]=n?ol(e,t,r):ol(t,e,r);if(!(i>=o))return[];let c=i-o+1,u=Array(c);if(n){if(a<0)for(let t=0;t<c;++t)u[t]=-((i-t)/a);else for(let t=0;t<c;++t)u[t]=(i-t)*a}else if(a<0)for(let t=0;t<c;++t)u[t]=-((o+t)/a);else for(let t=0;t<c;++t)u[t]=(o+t)*a;return u}function of(t,e,r){return ol(t*=1,e*=1,r*=1)[2]}function op(t,e,r){e*=1,t*=1,r*=1;let n=e<t,o=n?of(e,t,r):of(t,e,r);return(n?-1:1)*(o<0?-(1/o):o)}function od(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function oh(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function oy(t){let e,r,n;function o(t,n,i=0,a=t.length){if(i<a){if(0!==e(n,n))return a;do{let e=i+a>>>1;0>r(t[e],n)?i=e+1:a=e}while(i<a)}return i}return 2!==t.length?(e=od,r=(e,r)=>od(t(e),r),n=(e,r)=>t(e)-r):(e=t===od||t===oh?t:ov,r=t,n=t),{left:o,center:function(t,e,r=0,i=t.length){let a=o(t,e,r,i-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{let e=o+i>>>1;0>=r(t[e],n)?o=e+1:i=e}while(o<i)}return o}}}function ov(){return 0}function om(t){return null===t?NaN:+t}let ob=oy(od),og=ob.right;function ox(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function ow(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function oO(){}ob.left,oy(om).center;var oj="\\s*([+-]?\\d+)\\s*",oA="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",oS="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",oP=/^#([0-9a-f]{3,8})$/,oE=RegExp(`^rgb\\(${oj},${oj},${oj}\\)$`),ok=RegExp(`^rgb\\(${oS},${oS},${oS}\\)$`),oM=RegExp(`^rgba\\(${oj},${oj},${oj},${oA}\\)$`),o_=RegExp(`^rgba\\(${oS},${oS},${oS},${oA}\\)$`),oT=RegExp(`^hsl\\(${oA},${oS},${oS}\\)$`),oN=RegExp(`^hsla\\(${oA},${oS},${oS},${oA}\\)$`),oC={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function oD(){return this.rgb().formatHex()}function oI(){return this.rgb().formatRgb()}function oB(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=oP.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?oR(e):3===r?new oU(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?oL(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?oL(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=oE.exec(t))?new oU(e[1],e[2],e[3],1):(e=ok.exec(t))?new oU(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=oM.exec(t))?oL(e[1],e[2],e[3],e[4]):(e=o_.exec(t))?oL(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=oT.exec(t))?oV(e[1],e[2]/100,e[3]/100,1):(e=oN.exec(t))?oV(e[1],e[2]/100,e[3]/100,e[4]):oC.hasOwnProperty(t)?oR(oC[t]):"transparent"===t?new oU(NaN,NaN,NaN,0):null}function oR(t){return new oU(t>>16&255,t>>8&255,255&t,1)}function oL(t,e,r,n){return n<=0&&(t=e=r=NaN),new oU(t,e,r,n)}function oz(t,e,r,n){var o;return 1==arguments.length?((o=t)instanceof oO||(o=oB(o)),o)?new oU((o=o.rgb()).r,o.g,o.b,o.opacity):new oU:new oU(t,e,r,null==n?1:n)}function oU(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function o$(){return`#${oX(this.r)}${oX(this.g)}${oX(this.b)}`}function oF(){let t=oq(this.opacity);return`${1===t?"rgb(":"rgba("}${oW(this.r)}, ${oW(this.g)}, ${oW(this.b)}${1===t?")":`, ${t})`}`}function oq(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function oW(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function oX(t){return((t=oW(t))<16?"0":"")+t.toString(16)}function oV(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new oG(t,e,r,n)}function oH(t){if(t instanceof oG)return new oG(t.h,t.s,t.l,t.opacity);if(t instanceof oO||(t=oB(t)),!t)return new oG;if(t instanceof oG)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,o=Math.min(e,r,n),i=Math.max(e,r,n),a=NaN,c=i-o,u=(i+o)/2;return c?(a=e===i?(r-n)/c+(r<n)*6:r===i?(n-e)/c+2:(e-r)/c+4,c/=u<.5?i+o:2-i-o,a*=60):c=u>0&&u<1?0:a,new oG(a,c,u,t.opacity)}function oG(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function oY(t){return(t=(t||0)%360)<0?t+360:t}function oK(t){return Math.max(0,Math.min(1,t||0))}function oZ(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function oQ(t,e,r,n,o){var i=t*t,a=i*t;return((1-3*t+3*i-a)*e+(4-6*i+3*a)*r+(1+3*t+3*i-3*a)*n+a*o)/6}ox(oO,oB,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:oD,formatHex:oD,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return oH(this).formatHsl()},formatRgb:oI,toString:oI}),ox(oU,oz,ow(oO,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new oU(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new oU(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new oU(oW(this.r),oW(this.g),oW(this.b),oq(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:o$,formatHex:o$,formatHex8:function(){return`#${oX(this.r)}${oX(this.g)}${oX(this.b)}${oX((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:oF,toString:oF})),ox(oG,function(t,e,r,n){return 1==arguments.length?oH(t):new oG(t,e,r,null==n?1:n)},ow(oO,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new oG(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new oG(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,o=2*r-n;return new oU(oZ(t>=240?t-240:t+120,o,n),oZ(t,o,n),oZ(t<120?t+240:t-120,o,n),this.opacity)},clamp(){return new oG(oY(this.h),oK(this.s),oK(this.l),oq(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=oq(this.opacity);return`${1===t?"hsl(":"hsla("}${oY(this.h)}, ${100*oK(this.s)}%, ${100*oK(this.l)}%${1===t?")":`, ${t})`}`}}));let oJ=t=>()=>t;function o0(t,e){var r,n,o=e-t;return o?(r=t,n=o,function(t){return r+t*n}):oJ(isNaN(t)?e:t)}let o1=function t(e){var r,n=1==(r=+e)?o0:function(t,e){var n,o,i;return e-t?(n=t,o=e,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(t){return Math.pow(n+t*o,i)}):oJ(isNaN(t)?e:t)};function o(t,e){var r=n((t=oz(t)).r,(e=oz(e)).r),o=n(t.g,e.g),i=n(t.b,e.b),a=o0(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return o.gamma=t,o}(1);function o2(t){return function(e){var r,n,o=e.length,i=Array(o),a=Array(o),c=Array(o);for(r=0;r<o;++r)n=oz(e[r]),i[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return i=t(i),a=t(a),c=t(c),n.opacity=1,function(t){return n.r=i(t),n.g=a(t),n.b=c(t),n+""}}}o2(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),o=t[n],i=t[n+1],a=n>0?t[n-1]:2*o-i,c=n<e-1?t[n+2]:2*i-o;return oQ((r-n/e)*e,a,o,i,c)}}),o2(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),o=t[(n+e-1)%e],i=t[n%e],a=t[(n+1)%e],c=t[(n+2)%e];return oQ((r-n/e)*e,o,i,a,c)}});function o4(t,e){return t*=1,e*=1,function(r){return t*(1-r)+e*r}}var o3=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,o5=RegExp(o3.source,"g");function o6(t,e){var r,n,o=typeof e;return null==e||"boolean"===o?oJ(e):("number"===o?o4:"string"===o?(n=oB(e))?(e=n,o1):function(t,e){var r,n,o,i,a,c=o3.lastIndex=o5.lastIndex=0,u=-1,l=[],s=[];for(t+="",e+="";(o=o3.exec(t))&&(i=o5.exec(e));)(a=i.index)>c&&(a=e.slice(c,a),l[u]?l[u]+=a:l[++u]=a),(o=o[0])===(i=i[0])?l[u]?l[u]+=i:l[++u]=i:(l[++u]=null,s.push({i:u,x:o4(o,i)})),c=o5.lastIndex;return c<e.length&&(a=e.slice(c),l[u]?l[u]+=a:l[++u]=a),l.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)l[(r=s[n]).i]=r.x(t);return l.join("")})}:e instanceof oB?o1:e instanceof Date?function(t,e){var r=new Date;return t*=1,e*=1,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,o=t?Math.min(n,t.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=o6(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=i[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},o={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=o6(t[r],e[r]):o[r]=e[r];return function(t){for(r in n)o[r]=n[r](t);return o}}:o4:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(r=0;r<n;++r)o[r]=t[r]*(1-i)+e[r]*i;return o}})(t,e)}function o8(t,e){return t*=1,e*=1,function(r){return Math.round(t*(1-r)+e*r)}}function o7(t){return+t}var o9=[0,1];function it(t){return t}function ie(t,e){var r;return(e-=t*=1)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function ir(t,e,r){var n=t[0],o=t[1],i=e[0],a=e[1];return o<n?(n=ie(o,n),i=r(a,i)):(n=ie(n,o),i=r(i,a)),function(t){return i(n(t))}}function io(t,e,r){var n=Math.min(t.length,e.length)-1,o=Array(n),i=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)o[a]=ie(t[a],t[a+1]),i[a]=r(e[a],e[a+1]);return function(e){var r=og(t,e,1,n)-1;return i[r](o[r](e))}}function ii(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function ia(){var t,e,r,n,o,i,a=o9,c=o9,u=o6,l=it;function s(){var t,e,r,u=Math.min(a.length,c.length);return l!==it&&(t=a[0],e=a[u-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=u>2?io:ir,o=i=null,f}function f(e){return null==e||isNaN(e*=1)?r:(o||(o=n(a.map(t),c,u)))(t(l(e)))}return f.invert=function(r){return l(e((i||(i=n(c,a.map(t),o4)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,o7),s()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),s()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=o8,s()},f.clamp=function(t){return arguments.length?(l=!!t||it,s()):l!==it},f.interpolate=function(t){return arguments.length?(u=t,s()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function ic(){return ia()(it,it)}var iu=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function il(t){var e;if(!(e=iu.exec(t)))throw Error("invalid format: "+t);return new is({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function is(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function ip(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function id(t){return(t=ip(Math.abs(t)))?t[1]:NaN}function ih(t,e){var r=ip(t,e);if(!r)return t+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}il.prototype=is.prototype,is.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let iy={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>ih(100*t,e),r:ih,s:function(t,e){var r=ip(t,e);if(!r)return t+"";var n=r[0],o=r[1],i=o-(cW=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=n.length;return i===a?n:i>a?n+Array(i-a+1).join("0"):i>0?n.slice(0,i)+"."+n.slice(i):"0."+Array(1-i).join("0")+ip(t,Math.max(0,e+i-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function iv(t){return t}var im=Array.prototype.map,ib=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function ig(t,e,r,n){var o,i,a,c=op(t,e,r);switch((n=il(null==n?",f":n)).type){case"s":var u=Math.max(Math.abs(t),Math.abs(e));return null==n.precision&&!isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(id(u)/3)))-id(Math.abs(c))))&&(n.precision=a),cH(n,u);case"":case"e":case"g":case"p":case"r":null==n.precision&&!isNaN(a=Math.max(0,id(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(o=Math.abs(o=c)))-id(o))+1)&&(n.precision=a-("e"===n.type));break;case"f":case"%":null==n.precision&&!isNaN(a=Math.max(0,-id(Math.abs(c))))&&(n.precision=a-("%"===n.type)*2)}return cV(n)}function ix(t){var e=t.domain;return t.ticks=function(t){var r=e();return os(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return ig(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,o,i=e(),a=0,c=i.length-1,u=i[a],l=i[c],s=10;for(l<u&&(o=u,u=l,l=o,o=a,a=c,c=o);s-- >0;){if((o=of(u,l,r))===n)return i[a]=u,i[c]=l,e(i);if(o>0)u=Math.floor(u/o)*o,l=Math.ceil(l/o)*o;else if(o<0)u=Math.ceil(u*o)/o,l=Math.floor(l*o)/o;else break;n=o}return t},t}function iw(){var t=ic();return t.copy=function(){return ii(t,iw())},nP.apply(t,arguments),ix(t)}function iO(t,e){t=t.slice();var r,n=0,o=t.length-1,i=t[n],a=t[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),t[n]=e.floor(i),t[o]=e.ceil(a),t}function ij(t){return Math.log(t)}function iA(t){return Math.exp(t)}function iS(t){return-Math.log(-t)}function iP(t){return-Math.exp(-t)}function iE(t){return isFinite(t)?+("1e"+t):t<0?0:t}function ik(t){return(e,r)=>-t(-e,r)}function iM(t){let e,r;let n=t(ij,iA),o=n.domain,i=10;function a(){var a,c;return e=(a=i)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),t=>Math.log(t)/a),r=10===(c=i)?iE:c===Math.E?Math.exp:t=>Math.pow(c,t),o()[0]<0?(e=ik(e),r=ik(r),t(iS,iP)):t(ij,iA),n}return n.base=function(t){return arguments.length?(i=+t,a()):i},n.domain=function(t){return arguments.length?(o(t),a()):o()},n.ticks=t=>{let n,a;let c=o(),u=c[0],l=c[c.length-1],s=l<u;s&&([u,l]=[l,u]);let f=e(u),p=e(l),d=null==t?10:+t,h=[];if(!(i%1)&&p-f<d){if(f=Math.floor(f),p=Math.ceil(p),u>0){for(;f<=p;++f)for(n=1;n<i;++n)if(!((a=f<0?n/r(-f):n*r(f))<u)){if(a>l)break;h.push(a)}}else for(;f<=p;++f)for(n=i-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<u)){if(a>l)break;h.push(a)}2*h.length<d&&(h=os(u,l,d))}else h=os(f,p,Math.min(p-f,d)).map(r);return s?h.reverse():h},n.tickFormat=(t,o)=>{if(null==t&&(t=10),null==o&&(o=10===i?"s":","),"function"!=typeof o&&(i%1||null!=(o=il(o)).precision||(o.trim=!0),o=cV(o)),t===1/0)return o;let a=Math.max(1,i*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*i<i-.5&&(n*=i),n<=a?o(t):""}},n.nice=()=>o(iO(o(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function i_(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function iT(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function iN(t){var e=1,r=t(i_(1),iT(e));return r.constant=function(r){return arguments.length?t(i_(e=+r),iT(e)):e},ix(r)}function iC(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function iD(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function iI(t){return t<0?-t*t:t*t}function iB(t){var e=t(it,it),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(it,it):.5===r?t(iD,iI):t(iC(r),iC(1/r)):r},ix(e)}function iR(){var t=iB(ia());return t.copy=function(){return ii(t,iR()).exponent(t.exponent())},nP.apply(t,arguments),t}function iL(){return iR.apply(null,arguments).exponent(.5)}function iz(t){return Math.sign(t)*t*t}function iU(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function i$(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}cV=(cX=function(t){var e,r,n,o=void 0===t.grouping||void 0===t.thousands?iv:(e=im.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var o=t.length,i=[],a=0,c=e[0],u=0;o>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),i.push(t.substring(o-=c,o+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return i.reverse().join(r)}),i=void 0===t.currency?"":t.currency[0]+"",a=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",u=void 0===t.numerals?iv:(n=im.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return n[+t]})}),l=void 0===t.percent?"%":t.percent+"",s=void 0===t.minus?"−":t.minus+"",f=void 0===t.nan?"NaN":t.nan+"";function p(t){var e=(t=il(t)).fill,r=t.align,n=t.sign,p=t.symbol,d=t.zero,h=t.width,y=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(y=!0,b="g"):iy[b]||(void 0===v&&(v=12),m=!0,b="g"),(d||"0"===e&&"="===r)&&(d=!0,e="0",r="=");var g="$"===p?i:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?a:/[%p]/.test(b)?l:"",w=iy[b],O=/[defgprs%]/.test(b);function j(t){var i,a,l,p=g,j=x;if("c"===b)j=w(t)+j,t="";else{var A=(t*=1)<0||1/t<0;if(t=isNaN(t)?f:w(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,o=-1;n<r;++n)switch(t[n]){case".":o=e=n;break;case"0":0===o&&(o=n),e=n;break;default:if(!+t[n])break t;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),A&&0==+t&&"+"!==n&&(A=!1),p=(A?"("===n?n:s:"-"===n||"("===n?"":n)+p,j=("s"===b?ib[8+cW/3]:"")+j+(A&&"("===n?")":""),O){for(i=-1,a=t.length;++i<a;)if(48>(l=t.charCodeAt(i))||l>57){j=(46===l?c+t.slice(i+1):t.slice(i))+j,t=t.slice(0,i);break}}}y&&!d&&(t=o(t,1/0));var S=p.length+t.length+j.length,P=S<h?Array(h-S+1).join(e):"";switch(y&&d&&(t=o(P+t,P.length?h-j.length:1/0),P=""),r){case"<":t=p+t+j+P;break;case"=":t=p+P+t+j;break;case"^":t=P.slice(0,S=P.length>>1)+p+t+j+P.slice(S);break;default:t=P+p+t+j}return u(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:p,formatPrefix:function(t,e){var r=p(((t=il(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(id(e)/3))),o=Math.pow(10,-n),i=ib[8+n/3];return function(t){return r(o*t)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,cH=cX.formatPrefix;function iF(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}function iq(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let iW=new Date,iX=new Date;function iV(t,e,r,n){function o(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),o.round=t=>{let e=o(t),r=o.ceil(t);return t-e<r-t?e:r},o.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),o.range=(r,n,i)=>{let a;let c=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return c;do c.push(a=new Date(+r)),e(r,i),t(r);while(a<r&&r<n);return c},o.filter=r=>iV(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t){if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}}),r&&(o.count=(e,n)=>(iW.setTime(+e),iX.setTime(+n),t(iW),t(iX),Math.floor(r(iW,iX))),o.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?o.filter(n?e=>n(e)%t==0:e=>o.count(0,e)%t==0):o:null),o}let iH=iV(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);iH.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?iV(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):iH:null,iH.range;let iG=iV(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());iG.range;let iY=iV(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());iY.range;let iK=iV(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());iK.range;let iZ=iV(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());iZ.range;let iQ=iV(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());iQ.range;let iJ=iV(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);iJ.range;let i0=iV(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);i0.range;let i1=iV(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function i2(t){return iV(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}i1.range;let i4=i2(0),i3=i2(1),i5=i2(2),i6=i2(3),i8=i2(4),i7=i2(5),i9=i2(6);function at(t){return iV(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}i4.range,i3.range,i5.range,i6.range,i8.range,i7.range,i9.range;let ae=at(0),ar=at(1),an=at(2),ao=at(3),ai=at(4),aa=at(5),ac=at(6);ae.range,ar.range,an.range,ao.range,ai.range,aa.range,ac.range;let au=iV(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());au.range;let al=iV(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());al.range;let as=iV(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());as.every=t=>isFinite(t=Math.floor(t))&&t>0?iV(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,as.range;let af=iV(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function ap(t,e,r,n,o,i){let a=[[iG,1,1e3],[iG,5,5e3],[iG,15,15e3],[iG,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function c(e,r,n){let o=Math.abs(r-e)/n,i=oy(([,,t])=>t).right(a,o);if(i===a.length)return t.every(op(e/31536e6,r/31536e6,n));if(0===i)return iH.every(Math.max(op(e,r,n),1));let[c,u]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return c.every(u)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let o=r&&"function"==typeof r.range?r:c(t,e,r),i=o?o.range(t,+e+1):[];return n?i.reverse():i},c]}af.every=t=>isFinite(t=Math.floor(t))&&t>0?iV(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,af.range;let[ad,ah]=ap(af,al,ae,i1,iQ,iK),[ay,av]=ap(as,au,i4,iJ,iZ,iY);function am(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function ab(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function ag(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var ax={"-":"",_:" ",0:"0"},aw=/^\s*\d+/,aO=/^%/,aj=/[\\^$*+?|[\]().{}]/g;function aA(t,e,r){var n=t<0?"-":"",o=(n?-t:t)+"",i=o.length;return n+(i<r?Array(r-i+1).join(e)+o:o)}function aS(t){return t.replace(aj,"\\$&")}function aP(t){return RegExp("^(?:"+t.map(aS).join("|")+")","i")}function aE(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function ak(t,e,r){var n=aw.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function aM(t,e,r){var n=aw.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function a_(t,e,r){var n=aw.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function aT(t,e,r){var n=aw.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function aN(t,e,r){var n=aw.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function aC(t,e,r){var n=aw.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function aD(t,e,r){var n=aw.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function aI(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function aB(t,e,r){var n=aw.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function aR(t,e,r){var n=aw.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function aL(t,e,r){var n=aw.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function az(t,e,r){var n=aw.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function aU(t,e,r){var n=aw.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function a$(t,e,r){var n=aw.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function aF(t,e,r){var n=aw.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function aq(t,e,r){var n=aw.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function aW(t,e,r){var n=aw.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function aX(t,e,r){var n=aO.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function aV(t,e,r){var n=aw.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function aH(t,e,r){var n=aw.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function aG(t,e){return aA(t.getDate(),e,2)}function aY(t,e){return aA(t.getHours(),e,2)}function aK(t,e){return aA(t.getHours()%12||12,e,2)}function aZ(t,e){return aA(1+iJ.count(as(t),t),e,3)}function aQ(t,e){return aA(t.getMilliseconds(),e,3)}function aJ(t,e){return aQ(t,e)+"000"}function a0(t,e){return aA(t.getMonth()+1,e,2)}function a1(t,e){return aA(t.getMinutes(),e,2)}function a2(t,e){return aA(t.getSeconds(),e,2)}function a4(t){var e=t.getDay();return 0===e?7:e}function a3(t,e){return aA(i4.count(as(t)-1,t),e,2)}function a5(t){var e=t.getDay();return e>=4||0===e?i8(t):i8.ceil(t)}function a6(t,e){return t=a5(t),aA(i8.count(as(t),t)+(4===as(t).getDay()),e,2)}function a8(t){return t.getDay()}function a7(t,e){return aA(i3.count(as(t)-1,t),e,2)}function a9(t,e){return aA(t.getFullYear()%100,e,2)}function ct(t,e){return aA((t=a5(t)).getFullYear()%100,e,2)}function ce(t,e){return aA(t.getFullYear()%1e4,e,4)}function cr(t,e){var r=t.getDay();return aA((t=r>=4||0===r?i8(t):i8.ceil(t)).getFullYear()%1e4,e,4)}function cn(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+aA(e/60|0,"0",2)+aA(e%60,"0",2)}function co(t,e){return aA(t.getUTCDate(),e,2)}function ci(t,e){return aA(t.getUTCHours(),e,2)}function ca(t,e){return aA(t.getUTCHours()%12||12,e,2)}function cc(t,e){return aA(1+i0.count(af(t),t),e,3)}function cu(t,e){return aA(t.getUTCMilliseconds(),e,3)}function cl(t,e){return cu(t,e)+"000"}function cs(t,e){return aA(t.getUTCMonth()+1,e,2)}function cf(t,e){return aA(t.getUTCMinutes(),e,2)}function cp(t,e){return aA(t.getUTCSeconds(),e,2)}function cd(t){var e=t.getUTCDay();return 0===e?7:e}function ch(t,e){return aA(ae.count(af(t)-1,t),e,2)}function cy(t){var e=t.getUTCDay();return e>=4||0===e?ai(t):ai.ceil(t)}function cv(t,e){return t=cy(t),aA(ai.count(af(t),t)+(4===af(t).getUTCDay()),e,2)}function cm(t){return t.getUTCDay()}function cb(t,e){return aA(ar.count(af(t)-1,t),e,2)}function cg(t,e){return aA(t.getUTCFullYear()%100,e,2)}function cx(t,e){return aA((t=cy(t)).getUTCFullYear()%100,e,2)}function cw(t,e){return aA(t.getUTCFullYear()%1e4,e,4)}function cO(t,e){var r=t.getUTCDay();return aA((t=r>=4||0===r?ai(t):ai.ceil(t)).getUTCFullYear()%1e4,e,4)}function cj(){return"+0000"}function cA(){return"%"}function cS(t){return+t}function cP(t){return Math.floor(+t/1e3)}function cE(t){return new Date(t)}function ck(t){return t instanceof Date?+t:+new Date(+t)}function cM(t,e,r,n,o,i,a,c,u,l){var s=ic(),f=s.invert,p=s.domain,d=l(".%L"),h=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function w(t){return(u(t)<t?d:c(t)<t?h:a(t)<t?y:i(t)<t?v:n(t)<t?o(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,ck)):p().map(cE)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?w:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(iO(r,t)):s},s.copy=function(){return ii(s,cM(t,e,r,n,o,i,a,c,u,l))},s}function c_(){return nP.apply(cM(ay,av,as,au,i4,iJ,iZ,iY,iG,cY).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function cT(){return nP.apply(cM(ad,ah,af,al,ae,i0,iQ,iK,iG,cK).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function cN(){var t,e,r,n,o,i=0,a=1,c=it,u=!1;function l(e){return null==e||isNaN(e*=1)?o:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),l):[c(0),c(1)]}}return l.domain=function(o){return arguments.length?([i,a]=o,t=n(i*=1),e=n(a*=1),r=t===e?0:1/(e-t),l):[i,a]},l.clamp=function(t){return arguments.length?(u=!!t,l):u},l.interpolator=function(t){return arguments.length?(c=t,l):c},l.range=s(o6),l.rangeRound=s(o8),l.unknown=function(t){return arguments.length?(o=t,l):o},function(o){return n=o,t=o(i),e=o(a),r=t===e?0:1/(e-t),l}}function cC(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function cD(){var t=iB(cN());return t.copy=function(){return cC(t,cD()).exponent(t.exponent())},nE.apply(t,arguments)}function cI(){return cD.apply(null,arguments).exponent(.5)}function cB(){var t,e,r,n,o,i,a,c=0,u=.5,l=1,s=1,f=it,p=!1;function d(t){return isNaN(t*=1)?a:(t=.5+((t=+i(t))-e)*(s*t<s*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function h(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=o6);for(var r=0,n=e.length-1,o=e[0],i=Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),d):[f(0),f(.5),f(1)]}}return d.domain=function(a){return arguments.length?([c,u,l]=a,t=i(c*=1),e=i(u*=1),r=i(l*=1),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,d):[c,u,l]},d.clamp=function(t){return arguments.length?(p=!!t,d):p},d.interpolator=function(t){return arguments.length?(f=t,d):f},d.range=h(o6),d.rangeRound=h(o8),d.unknown=function(t){return arguments.length?(a=t,d):a},function(a){return i=a,t=a(c),e=a(u),r=a(l),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),s=e<t?-1:1,d}}function cR(){var t=iB(cB());return t.copy=function(){return cC(t,cR()).exponent(t.exponent())},nE.apply(t,arguments)}function cL(){return cR.apply(null,arguments).exponent(.5)}function cz(t,e){if((o=t.length)>1)for(var r,n,o,i=1,a=t[e[0]],c=a.length;i<o;++i)for(n=a,a=t[e[i]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function cU(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function c$(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function cF(t,e){return t[e]}function cq(t){let e=[];return e.key=t,e}cY=(cG=function(t){var e=t.dateTime,r=t.date,n=t.time,o=t.periods,i=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,l=aP(o),s=aE(o),f=aP(i),p=aE(i),d=aP(a),h=aE(a),y=aP(c),v=aE(c),m=aP(u),b=aE(u),g={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:aG,e:aG,f:aJ,g:ct,G:cr,H:aY,I:aK,j:aZ,L:aQ,m:a0,M:a1,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:cS,s:cP,S:a2,u:a4,U:a3,V:a6,w:a8,W:a7,x:null,X:null,y:a9,Y:ce,Z:cn,"%":cA},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:co,e:co,f:cl,g:cx,G:cO,H:ci,I:ca,j:cc,L:cu,m:cs,M:cf,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:cS,s:cP,S:cp,u:cd,U:ch,V:cv,w:cm,W:cb,x:null,X:null,y:cg,Y:cw,Z:cj,"%":cA},w={a:function(t,e,r){var n=d.exec(e.slice(r));return n?(t.w=h.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return A(t,e,r,n)},d:aL,e:aL,f:aW,g:aD,G:aC,H:aU,I:aU,j:az,L:aq,m:aR,M:a$,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:aB,Q:aV,s:aH,S:aF,u:aM,U:a_,V:aT,w:ak,W:aN,x:function(t,e,n){return A(t,r,e,n)},X:function(t,e,r){return A(t,n,e,r)},y:aD,Y:aC,Z:aI,"%":aX};function O(t,e){return function(r){var n,o,i,a=[],c=-1,u=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++c<l;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(o=ax[n=t.charAt(++c)])?n=t.charAt(++c):o="e"===n?" ":"0",(i=e[n])&&(n=i(r,o)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function j(t,e){return function(r){var n,o,i=ag(1900,void 0,1);if(A(i,t,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!e||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=ab(ag(i.y,0,1))).getUTCDay())>4||0===o?ar.ceil(n):ar(n),n=i0.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=am(ag(i.y,0,1))).getDay())>4||0===o?i3.ceil(n):i3(n),n=iJ.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:+("W"in i)),o="Z"in i?ab(ag(i.y,0,1)).getUTCDay():am(ag(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,ab(i)):am(i)}}function A(t,e,r,n){for(var o,i,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return -1;if(37===(o=e.charCodeAt(a++))){if(!(i=w[(o=e.charAt(a++))in ax?e.charAt(a++):o])||(n=i(t,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return g.x=O(r,g),g.X=O(n,g),g.c=O(e,g),x.x=O(r,x),x.X=O(n,x),x.c=O(e,x),{format:function(t){var e=O(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=O(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,cG.parse,cK=cG.utcFormat,cG.utcParse,Array.prototype.slice;var cW,cX,cV,cH,cG,cY,cK,cZ,cQ,cJ=r(90453),c0=r.n(cJ),c1=r(15883),c2=r.n(c1),c4=r(21592),c3=r.n(c4),c5=r(71967),c6=r.n(c5),c8=!0,c7="[DecimalError] ",c9=c7+"Invalid argument: ",ut=c7+"Exponent out of range: ",ue=Math.floor,ur=Math.pow,un=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,uo=ue(1286742750677284.5),ui={};function ua(t,e){var r,n,o,i,a,c,u,l,s=t.constructor,f=s.precision;if(!t.s||!e.s)return e.s||(e=new s(t)),c8?uv(e,f):e;if(u=t.d,l=e.d,a=t.e,o=e.e,u=u.slice(),i=a-o){for(i<0?(n=u,i=-i,c=l.length):(n=l,o=a,c=u.length),i>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=u.length)-(i=l.length)<0&&(i=c,n=l,l=u,u=n),r=0;i;)r=(u[--i]=u[i]+l[i]+r)/1e7|0,u[i]%=1e7;for(r&&(u.unshift(r),++o),c=u.length;0==u[--c];)u.pop();return e.d=u,e.e=o,c8?uv(e,f):e}function uc(t,e,r){if(t!==~~t||t<e||t>r)throw Error(c9+t)}function uu(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(r=7-(n=t[e]+"").length)&&(i+=ud(r)),i+=n;(r=7-(n=(a=t[e])+"").length)&&(i+=ud(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}ui.absoluteValue=ui.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},ui.comparedTo=ui.cmp=function(t){var e,r,n,o;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(o=t.d.length)?n:o;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},ui.decimalPlaces=ui.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},ui.dividedBy=ui.div=function(t){return ul(this,new this.constructor(t))},ui.dividedToIntegerBy=ui.idiv=function(t){var e=this.constructor;return uv(ul(this,new e(t),0,1),e.precision)},ui.equals=ui.eq=function(t){return!this.cmp(t)},ui.exponent=function(){return uf(this)},ui.greaterThan=ui.gt=function(t){return this.cmp(t)>0},ui.greaterThanOrEqualTo=ui.gte=function(t){return this.cmp(t)>=0},ui.isInteger=ui.isint=function(){return this.e>this.d.length-2},ui.isNegative=ui.isneg=function(){return this.s<0},ui.isPositive=ui.ispos=function(){return this.s>0},ui.isZero=function(){return 0===this.s},ui.lessThan=ui.lt=function(t){return 0>this.cmp(t)},ui.lessThanOrEqualTo=ui.lte=function(t){return 1>this.cmp(t)},ui.logarithm=ui.log=function(t){var e,r=this.constructor,n=r.precision,o=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(cQ))throw Error(c7+"NaN");if(this.s<1)throw Error(c7+(this.s?"NaN":"-Infinity"));return this.eq(cQ)?new r(0):(c8=!1,e=ul(uh(this,o),uh(t,o),o),c8=!0,uv(e,n))},ui.minus=ui.sub=function(t){return t=new this.constructor(t),this.s==t.s?um(this,t):ua(this,(t.s=-t.s,t))},ui.modulo=ui.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(c7+"NaN");return this.s?(c8=!1,e=ul(this,t,0,1).times(t),c8=!0,this.minus(e)):uv(new r(this),n)},ui.naturalExponential=ui.exp=function(){return us(this)},ui.naturalLogarithm=ui.ln=function(){return uh(this)},ui.negated=ui.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},ui.plus=ui.add=function(t){return t=new this.constructor(t),this.s==t.s?ua(this,t):um(this,(t.s=-t.s,t))},ui.precision=ui.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(c9+t);if(e=uf(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},ui.squareRoot=ui.sqrt=function(){var t,e,r,n,o,i,a,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(c7+"NaN")}for(t=uf(this),c8=!1,0==(o=Math.sqrt(+this))||o==1/0?(((e=uu(this.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=ue((t+1)/2)-(t<0||t%2),n=new c(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new c(o.toString()),o=a=(r=c.precision)+3;;)if(n=(i=n).plus(ul(this,i,a+2)).times(.5),uu(i.d).slice(0,a)===(e=uu(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(uv(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=e)break;a+=4}return c8=!0,uv(n,r)},ui.times=ui.mul=function(t){var e,r,n,o,i,a,c,u,l,s=this.constructor,f=this.d,p=(t=new s(t)).d;if(!this.s||!t.s)return new s(0);for(t.s*=this.s,r=this.e+t.e,(u=f.length)<(l=p.length)&&(i=f,f=p,p=i,a=u,u=l,l=a),i=[],n=a=u+l;n--;)i.push(0);for(n=l;--n>=0;){for(e=0,o=u+n;o>n;)c=i[o]+p[n]*f[o-n-1]+e,i[o--]=c%1e7|0,e=c/1e7|0;i[o]=(i[o]+e)%1e7|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,c8?uv(t,s.precision):t},ui.toDecimalPlaces=ui.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(uc(t,0,1e9),void 0===e?e=n.rounding:uc(e,0,8),uv(r,t+uf(r)+1,e))},ui.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=ub(n,!0):(uc(t,0,1e9),void 0===e?e=o.rounding:uc(e,0,8),r=ub(n=uv(new o(n),t+1,e),!0,t+1)),r},ui.toFixed=function(t,e){var r,n,o=this.constructor;return void 0===t?ub(this):(uc(t,0,1e9),void 0===e?e=o.rounding:uc(e,0,8),r=ub((n=uv(new o(this),t+uf(this)+1,e)).abs(),!1,t+uf(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},ui.toInteger=ui.toint=function(){var t=this.constructor;return uv(new t(this),uf(this)+1,t.rounding)},ui.toNumber=function(){return+this},ui.toPower=ui.pow=function(t){var e,r,n,o,i,a,c=this,u=c.constructor,l=+(t=new u(t));if(!t.s)return new u(cQ);if(!(c=new u(c)).s){if(t.s<1)throw Error(c7+"Infinity");return c}if(c.eq(cQ))return c;if(n=u.precision,t.eq(cQ))return uv(c,n);if(a=(e=t.e)>=(r=t.d.length-1),i=c.s,a){if((r=l<0?-l:l)<=0x1fffffffffffff){for(o=new u(cQ),e=Math.ceil(n/7+4),c8=!1;r%2&&ug((o=o.times(c)).d,e),0!==(r=ue(r/2));)ug((c=c.times(c)).d,e);return c8=!0,t.s<0?new u(cQ).div(o):uv(o,n)}}else if(i<0)throw Error(c7+"NaN");return i=i<0&&1&t.d[Math.max(e,r)]?-1:1,c.s=1,c8=!1,o=t.times(uh(c,n+12)),c8=!0,(o=us(o)).s=i,o},ui.toPrecision=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?(r=uf(o),n=ub(o,r<=i.toExpNeg||r>=i.toExpPos)):(uc(t,1,1e9),void 0===e?e=i.rounding:uc(e,0,8),r=uf(o=uv(new i(o),t,e)),n=ub(o,t<=r||r<=i.toExpNeg,t)),n},ui.toSignificantDigits=ui.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(uc(t,1,1e9),void 0===e?e=r.rounding:uc(e,0,8)),uv(new r(this),t,e)},ui.toString=ui.valueOf=ui.val=ui.toJSON=ui[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=uf(this),e=this.constructor;return ub(this,t<=e.toExpNeg||t>=e.toExpPos)};var ul=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=+(t[r]<e[r]),t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var c,u,l,s,f,p,d,h,y,v,m,b,g,x,w,O,j,A,S=n.constructor,P=n.s==o.s?1:-1,E=n.d,k=o.d;if(!n.s)return new S(n);if(!o.s)throw Error(c7+"Division by zero");for(l=0,u=n.e-o.e,j=k.length,w=E.length,h=(d=new S(P)).d=[];k[l]==(E[l]||0);)++l;if(k[l]>(E[l]||0)&&--u,(b=null==i?i=S.precision:a?i+(uf(n)-uf(o))+1:i)<0)return new S(0);if(b=b/7+2|0,l=0,1==j)for(s=0,k=k[0],b++;(l<w||s)&&b--;l++)g=1e7*s+(E[l]||0),h[l]=g/k|0,s=g%k|0;else{for((s=1e7/(k[0]+1)|0)>1&&(k=t(k,s),E=t(E,s),j=k.length,w=E.length),x=j,v=(y=E.slice(0,j)).length;v<j;)y[v++]=0;(A=k.slice()).unshift(0),O=k[0],k[1]>=1e7/2&&++O;do s=0,(c=e(k,y,j,v))<0?(m=y[0],j!=v&&(m=1e7*m+(y[1]||0)),(s=m/O|0)>1?(s>=1e7&&(s=1e7-1),p=(f=t(k,s)).length,v=y.length,1==(c=e(f,y,p,v))&&(s--,r(f,j<p?A:k,p))):(0==s&&(c=s=1),f=k.slice()),(p=f.length)<v&&f.unshift(0),r(y,f,v),-1==c&&(v=y.length,(c=e(k,y,j,v))<1&&(s++,r(y,j<v?A:k,v))),v=y.length):0===c&&(s++,y=[0]),h[l++]=s,c&&y[0]?y[v++]=E[x]||0:(y=[E[x]],v=1);while((x++<w||void 0!==y[0])&&b--)}return h[0]||h.shift(),d.e=u,uv(d,a?i+uf(d)+1:i)}}();function us(t,e){var r,n,o,i,a,c=0,u=0,l=t.constructor,s=l.precision;if(uf(t)>16)throw Error(ut+uf(t));if(!t.s)return new l(cQ);for(null==e?(c8=!1,a=s):a=e,i=new l(.03125);t.abs().gte(.1);)t=t.times(i),u+=5;for(a+=Math.log(ur(2,u))/Math.LN10*2+5|0,r=n=o=new l(cQ),l.precision=a;;){if(n=uv(n.times(t),a),r=r.times(++c),uu((i=o.plus(ul(n,r,a))).d).slice(0,a)===uu(o.d).slice(0,a)){for(;u--;)o=uv(o.times(o),a);return l.precision=s,null==e?(c8=!0,uv(o,s)):o}o=i}}function uf(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function up(t,e,r){if(e>t.LN10.sd())throw c8=!0,r&&(t.precision=r),Error(c7+"LN10 precision limit exceeded");return uv(new t(t.LN10),e)}function ud(t){for(var e="";t--;)e+="0";return e}function uh(t,e){var r,n,o,i,a,c,u,l,s,f=1,p=t,d=p.d,h=p.constructor,y=h.precision;if(p.s<1)throw Error(c7+(p.s?"NaN":"-Infinity"));if(p.eq(cQ))return new h(0);if(null==e?(c8=!1,l=y):l=e,p.eq(10))return null==e&&(c8=!0),up(h,l);if(h.precision=l+=10,n=(r=uu(d)).charAt(0),!(15e14>Math.abs(i=uf(p))))return u=up(h,l+2,y).times(i+""),p=uh(new h(n+"."+r.slice(1)),l-10).plus(u),h.precision=y,null==e?(c8=!0,uv(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=uu((p=p.times(t)).d)).charAt(0),f++;for(i=uf(p),n>1?(p=new h("0."+r),i++):p=new h(n+"."+r.slice(1)),c=a=p=ul(p.minus(cQ),p.plus(cQ),l),s=uv(p.times(p),l),o=3;;){if(a=uv(a.times(s),l),uu((u=c.plus(ul(a,new h(o),l))).d).slice(0,l)===uu(c.d).slice(0,l))return c=c.times(2),0!==i&&(c=c.plus(up(h,l+2,y).times(i+""))),c=ul(c,new h(f),l),h.precision=y,null==e?(c8=!0,uv(c,y)):c;c=u,o+=2}}function uy(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,t.e=ue((r=r-n-1)/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=7;n<o;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),c8&&(t.e>uo||t.e<-uo))throw Error(ut+r)}else t.s=0,t.e=0,t.d=[0];return t}function uv(t,e,r){var n,o,i,a,c,u,l,s,f=t.d;for(a=1,i=f[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=7,o=e,l=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(i=f.length))return t;for(a=1,l=i=f[s];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(c=l/(i=ur(10,a-o-1))%10|0,u=e<0||void 0!==f[s+1]||l%i,u=r<4?(c||u)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||u||6==r&&(n>0?o>0?l/ur(10,a-o):0:f[s-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return u?(i=uf(t),f.length=1,e=e-i-1,f[0]=ur(10,(7-e%7)%7),t.e=ue(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=s,i=1,s--):(f.length=s+1,i=ur(10,7-n),f[s]=o>0?(l/ur(10,a-o)%ur(10,o)|0)*i:0),u)for(;;){if(0==s){1e7==(f[0]+=i)&&(f[0]=1,++t.e);break}if(f[s]+=i,1e7!=f[s])break;f[s--]=0,i=1}for(n=f.length;0===f[--n];)f.pop();if(c8&&(t.e>uo||t.e<-uo))throw Error(ut+uf(t));return t}function um(t,e){var r,n,o,i,a,c,u,l,s,f,p=t.constructor,d=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),c8?uv(e,d):e;if(u=t.d,f=e.d,n=e.e,l=t.e,u=u.slice(),a=l-n){for((s=a<0)?(r=u,a=-a,c=f.length):(r=f,n=l,c=u.length),a>(o=Math.max(Math.ceil(d/7),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((s=(o=u.length)<(c=f.length))&&(c=o),o=0;o<c;o++)if(u[o]!=f[o]){s=u[o]<f[o];break}a=0}for(s&&(r=u,u=f,f=r,e.s=-e.s),c=u.length,o=f.length-c;o>0;--o)u[c++]=0;for(o=f.length;o>a;){if(u[--o]<f[o]){for(i=o;i&&0===u[--i];)u[i]=1e7-1;--u[i],u[o]+=1e7}u[o]-=f[o]}for(;0===u[--c];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(e.d=u,e.e=n,c8?uv(e,d):e):new p(0)}function ub(t,e,r){var n,o=uf(t),i=uu(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+ud(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+ud(-o-1)+i,r&&(n=r-a)>0&&(i+=ud(n))):o>=a?(i+=ud(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+ud(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=ud(n))),t.s<0?"-"+i:i}function ug(t,e){if(t.length>e)return t.length=e,!0}function ux(t){if(!t||"object"!=typeof t)throw Error(c7+"Object expected");var e,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]])){if(ue(n)===n&&n>=o[e+1]&&n<=o[e+2])this[r]=n;else throw Error(c9+r+": "+n)}if(void 0!==(n=t[r="LN10"])){if(n==Math.LN10)this[r]=new this(n);else throw Error(c9+r+": "+n)}return this}var cZ=function t(e){var r,n,o;function i(t){if(!(this instanceof i))return new i(t);if(this.constructor=i,t instanceof i){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(c9+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return uy(this,t.toString())}if("string"!=typeof t)throw Error(c9+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,un.test(t))uy(this,t);else throw Error(c9+t)}if(i.prototype=ui,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=ux,void 0===e&&(e={}),e)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});cQ=new cZ(1);let uw=cZ;function uO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var uj=function(t){return t},uA={},uS=function(t){return t===uA},uP=function(t){return function e(){return 0==arguments.length||1==arguments.length&&uS(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},uE=function(t){return function t(e,r){return 1===e?r:uP(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==uA}).length;return a>=e?r.apply(void 0,o):t(e-a,uP(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map(function(t){return uS(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return uO(t)})(i)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return uO(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uO(t,e)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},uk=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},uM=uE(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),u_=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return uj;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}},uT=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},uN=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every(function(t,r){return t===e[r]})?r:(e=o,r=t.apply(void 0,o))}};uE(function(t,e,r){var n=+t;return n+r*(+e-n)}),uE(function(t,e,r){var n=e-+t;return(r-t)/(n=n||1/0)}),uE(function(t,e,r){var n=e-+t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});let uC={rangeStep:function(t,e,r){for(var n=new uw(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){var e;return 0===t?1:Math.floor(new uw(t).abs().log(10).toNumber())+1}};function uD(t){return function(t){if(Array.isArray(t))return uR(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||uB(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uI(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{n||null==c.return||c.return()}finally{if(o)throw i}}return r}}(t,e)||uB(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uB(t,e){if(t){if("string"==typeof t)return uR(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uR(t,e)}}function uR(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uL(t){var e=uI(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function uz(t,e,r){if(t.lte(0))return new uw(0);var n=uC.getDigitCount(t.toNumber()),o=new uw(10).pow(n),i=t.div(o),a=1!==n?.05:.1,c=new uw(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?c:new uw(Math.ceil(c))}function uU(t,e,r){var n=1,o=new uw(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new uw(10).pow(uC.getDigitCount(t)-1),o=new uw(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new uw(Math.floor(t)))}else 0===t?o=new uw(Math.floor((e-1)/2)):r||(o=new uw(Math.floor(t)));var a=Math.floor((e-1)/2);return u_(uM(function(t){return o.add(new uw(t-a).mul(n)).toNumber()}),uk)(0,e)}var u$=uN(function(t){var e=uI(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=uI(uL([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0){var s=l===1/0?[u].concat(uD(uk(0,o-1).map(function(){return 1/0}))):[].concat(uD(uk(0,o-1).map(function(){return-1/0})),[l]);return r>n?uT(s):s}if(u===l)return uU(u,o,i);var f=function t(e,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new uw(0),tickMin:new uw(0),tickMax:new uw(0)};var c=uz(new uw(r).sub(e).div(n-1),o,a),u=Math.ceil((i=e<=0&&r>=0?new uw(0):(i=new uw(e).add(r).div(2)).sub(new uw(i).mod(c))).sub(e).div(c).toNumber()),l=Math.ceil(new uw(r).sub(i).div(c).toNumber()),s=u+l+1;return s>n?t(e,r,n,o,a+1):(s<n&&(l=r>0?l+(n-s):l,u=r>0?u:u+(n-s)),{step:c,tickMin:i.sub(new uw(u).mul(c)),tickMax:i.add(new uw(l).mul(c))})}(u,l,a,i),p=f.step,d=f.tickMin,h=f.tickMax,y=uC.rangeStep(d,h.add(new uw(.1).mul(p)),p);return r>n?uT(y):y});uN(function(t){var e=uI(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),c=uI(uL([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0)return[r,n];if(u===l)return uU(u,o,i);var s=uz(new uw(l).sub(u).div(a-1),i,0),f=u_(uM(function(t){return new uw(u).add(new uw(t).mul(s)).toNumber()}),uk)(0,a).filter(function(t){return t>=u&&t<=l});return r>n?uT(f):f});var uF=uN(function(t,e){var r=uI(t,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=uI(uL([n,o]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,o];if(c===u)return[c];var l=Math.max(e,2),s=uz(new uw(u).sub(c).div(l-1),i,0),f=[].concat(uD(uC.rangeStep(new uw(c),new uw(u).sub(new uw(.99).mul(s)),s)),[u]);return n>o?uT(f):f}),uq=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function uW(t){return(uW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uX(){return(uX=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function uV(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uH(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(uH=function(){return!!t})()}function uG(t){return(uG=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function uY(t,e){return(uY=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function uK(t,e,r){return(e=uZ(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function uZ(t){var e=function(t,e){if("object"!=uW(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uW(e)?e:e+""}var uQ=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=uG(t),function(t,e){if(e&&("object"===uW(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,uH()?Reflect.construct(t,e||[],uG(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&uY(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,n=t.width,o=t.dataKey,i=t.data,a=t.dataPointFormatter,c=t.xAxis,u=t.yAxis,l=tj(function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,uq),!1);"x"===this.props.direction&&"number"!==c.type&&tR(!1);var s=i.map(function(t){var i,s,f=a(t,o),p=f.x,d=f.y,h=f.value,y=f.errorVal;if(!y)return null;var v=[];if(Array.isArray(y)){var m=function(t){if(Array.isArray(t))return t}(y)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(y,2)||function(t,e){if(t){if("string"==typeof t)return uV(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uV(t,e)}}(y,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=m[0],s=m[1]}else i=s=y;if("vertical"===r){var b=c.scale,g=d+e,x=g+n,w=g-n,O=b(h-i),j=b(h+s);v.push({x1:j,y1:x,x2:j,y2:w}),v.push({x1:O,y1:g,x2:j,y2:g}),v.push({x1:O,y1:x,x2:O,y2:w})}else if("horizontal"===r){var A=u.scale,P=p+e,E=P-n,k=P+n,M=A(h-i),_=A(h+s);v.push({x1:E,y1:_,x2:k,y2:_}),v.push({x1:P,y1:M,x2:P,y2:_}),v.push({x1:E,y1:M,x2:k,y2:M})}return S().createElement(tq,uX({className:"recharts-errorBar",key:"bar-".concat(v.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},l),v.map(function(t){return S().createElement("line",uX({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return S().createElement(tq,{className:"recharts-errorBars"},s)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,uZ(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(S().Component);function uJ(t){return(uJ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u0(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function u1(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u0(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=uJ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uJ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uJ(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u0(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}uK(uQ,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),uK(uQ,"displayName","ErrorBar");var u2=function(t){var e,r=t.children,n=t.formattedGraphicalItems,o=t.legendWidth,i=t.legendContent,a=tg(r,ro);if(!a)return null;var c=ro.defaultProps,u=void 0!==c?u1(u1({},c),a.props):{};return e=a.props&&a.props.payload?a.props&&a.props.payload:"children"===i?(n||[]).reduce(function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map(function(t){return{type:a.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(n||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?u1(u1({},r),e.props):{},o=n.dataKey,i=n.name,a=n.legendType;return{inactive:n.hide,dataKey:o,type:u.iconType||a||"square",color:lr(e),value:i||o,payload:n}}),u1(u1(u1({},u),ro.getWithHeight(a,o)),{},{payload:e,item:a})};function u4(t){return(u4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u3(t){return function(t){if(Array.isArray(t))return u5(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return u5(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return u5(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u5(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function u6(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function u8(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u6(Object(r),!0).forEach(function(e){u7(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u6(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function u7(t,e,r){var n;return(n=function(t,e){if("object"!=u4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==u4(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function u9(t,e,r){return Y()(t)||Y()(e)?r:z(e)?C()(t,e,r):Z()(e)?e(t):r}function lt(t,e,r,n){var o=c3()(t,function(t){return u9(t,e)});if("number"===r){var i=o.filter(function(t){return L(t)||parseFloat(t)});return i.length?[c2()(i),c0()(i)]:[1/0,-1/0]}return(n?o.filter(function(t){return!Y()(t)}):o).map(function(t){return z(t)||t instanceof Date?t:""})}var le=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(e=null==r?void 0:r.length)&&void 0!==e?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var c=o.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if(B(s-l)!==B(f-s)){var d=[];if(B(f-s)===B(c[1]-c[0])){p=f;var h=s+c[1]-c[0];d[0]=Math.min(h,(h+l)/2),d[1]=Math.max(h,(h+l)/2)}else{p=l;var y=f+c[1]-c[0];d[0]=Math.min(s,(y+s)/2),d[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=d[0]&&t<=d[1]){i=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){i=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},lr=function(t){var e,r,n=t.type.displayName,o=null!==(e=t.type)&&void 0!==e&&e.defaultProps?u8(u8({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},ln=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),c=0,u=a.length;c<u;c++)for(var l=o[a[c]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var d=l[s[f]],h=d.items,y=d.cateAxisId,v=h.filter(function(t){return th(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?u8(u8({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var w=Y()(g)?e:g;i[x].push({item:v[0],stackList:v.slice(1),barSize:Y()(w)?void 0:F(w,r,0)})}}return i},lo=function(t){var e,r=t.barGap,n=t.barCategoryGap,o=t.bandSize,i=t.sizeList,a=void 0===i?[]:i,c=t.maxBarSize,u=a.length;if(u<1)return null;var l=F(r,o,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/u,d=a.reduce(function(t,e){return t+e.barSize||0},0);(d+=(u-1)*l)>=o&&(d-=(u-1)*l,l=0),d>=o&&p>0&&(f=!0,p*=.9,d=u*p);var h={offset:((o-d)/2>>0)-l,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:h.offset+h.size+l,size:f?p:e.barSize}},n=[].concat(u3(t),[r]);return h=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:h})}),n},s)}else{var y=F(n,o,0,!0);o-2*y-(u-1)*l<=0&&(l=0);var v=(o-2*y-(u-1)*l)/u;v>1&&(v>>=0);var m=c===+c?Math.min(v,c):v;e=a.reduce(function(t,e,r){var n=[].concat(u3(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return e},li=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,c=u2({children:o,legendWidth:i-(a.left||0)-(a.right||0)});if(c){var u=n||{},l=u.width,s=u.height,f=c.align,p=c.verticalAlign,d=c.layout;if(("vertical"===d||"horizontal"===d&&"middle"===p)&&"center"!==f&&L(t[f]))return u8(u8({},t),{},u7({},f,t[f]+(l||0)));if(("horizontal"===d||"vertical"===d&&"center"===f)&&"middle"!==p&&L(t[p]))return u8(u8({},t),{},u7({},p,t[p]+(s||0)))}return t},la=function(t,e,r,n,o){var i=tb(e.props.children,uQ).filter(function(t){var e;return e=t.props.direction,!!Y()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===e?"xAxis"===o:"y"!==e||"yAxis"===o)});if(i&&i.length){var a=i.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=u9(e,r);if(Y()(n))return t;var o=Array.isArray(n)?[c2()(n),c0()(n)]:[n,n],i=a.reduce(function(t,r){var n=u9(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},lc=function(t,e,r,n,o){var i=e.map(function(e){return la(t,e,r,o,n)}).filter(function(t){return!Y()(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},lu=function(t,e,r,n,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===r&&i&&la(t,e,i,n)||lt(t,i,r,o)});if("number"===r)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},ll=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},ls=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/c:0;return(u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*B(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(o?o.indexOf(t):t)+u,value:t,offset:u}}).filter(function(t){return!T()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+u,value:t,offset:u}}):n.domain().map(function(t,e){return{coordinate:n(t)+u,value:o?o[t]:t,index:e,offset:u}})},lf=new WeakMap,lp=function(t,e){if("function"!=typeof e)return t;lf.has(t)||lf.set(t,new WeakMap);var r=lf.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},ld=function(t,e,r){var o=t.scale,i=t.type,a=t.layout,c=t.axisType;if("auto"===o)return"radial"===a&&"radiusAxis"===c?{scale:nC(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:iw(),realScaleType:"linear"}:"category"===i&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:nD(),realScaleType:"point"}:"category"===i?{scale:nC(),realScaleType:"band"}:{scale:iw(),realScaleType:"linear"};if(M()(o)){var u="scale".concat(ev()(o));return{scale:(n[u]||nD)(),realScaleType:n[u]?u:"point"}}return Z()(o)?{scale:o}:{scale:nD(),realScaleType:"point"}},lh=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=t(e[0]),c=t(e[r-1]);(a<o||a>i||c<o||c>i)&&t.domain([e[0],e[r-1]])}},ly=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},lv=function(t,e){if(!e||2!==e.length||!L(e[0])||!L(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!L(t[0])||t[0]<r)&&(o[0]=r),(!L(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},lm={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var c=T()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,o,i=0,a=t[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}cz(t,e)}},none:cz,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,o=t[e[0]],i=o.length;n<i;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;o[n][1]+=o[n][0]=-c/2}cz(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var c=0,u=0,l=0;c<o;++c){for(var s=t[e[c]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,d=0;d<c;++d){var h=t[e[d]];p+=(h[a][1]||0)-(h[a-1][1]||0)}u+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=i,u&&(i-=l/u)}r[a-1][1]+=r[a-1][0]=i,cz(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=T()(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},lb=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),o=lm[r];return(function(){var t=eN([]),e=c$,r=cz,n=cF;function o(o){var i,a,c=Array.from(t.apply(this,arguments),cq),u=c.length,l=-1;for(let t of o)for(i=0,++l;i<u;++i)(c[i][l]=[0,+n(t,c[i].key,l,o)]).data=t;for(i=0,a=cU(e(c));i<u;++i)c[a[i]].index=i;return r(c,a),c}return o.keys=function(e){return arguments.length?(t="function"==typeof e?e:eN(Array.from(e)),o):t},o.value=function(t){return arguments.length?(n="function"==typeof t?t:eN(+t),o):n},o.order=function(t){return arguments.length?(e=null==t?c$:"function"==typeof t?t:eN(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(r=null==t?cz:t,o):r},o})().keys(n).value(function(t,e){return+u9(t,e,0)}).order(c$).offset(o)(t)},lg=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o,i=null!==(o=e.type)&&void 0!==o&&o.defaultProps?u8(u8({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var c=i[r],u=t[c]||{hasStack:!1,stackGroups:{}};if(z(a)){var l=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),u.hasStack=!0,u.stackGroups[a]=l}else u.stackGroups[$("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return u8(u8({},t),{},u7({},c,u))},{});return Object.keys(a).reduce(function(e,i){var c=a[i];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,i){var a=c.stackGroups[i];return u8(u8({},e),{},u7({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:lb(t,a.items,o)}))},{})),u8(u8({},e),{},u7({},i,c))},{})},lx=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var u=t.domain();if(!u.length)return null;var l=u$(u,o,a);return t.domain([c2()(l),c0()(l)]),{niceTicks:l}}return o&&"number"===n?{niceTicks:uF(t.domain(),o,a)}:null};function lw(t){var e=t.axis,r=t.ticks,n=t.bandSize,o=t.entry,i=t.index,a=t.dataKey;if("category"===e.type){if(!e.allowDuplicatedCategory&&e.dataKey&&!Y()(o[e.dataKey])){var c=V(r,"value",o[e.dataKey]);if(c)return c.coordinate+n/2}return r[i]?r[i].coordinate+n/2:null}var u=u9(o,Y()(a)?e.dataKey:a);return Y()(u)?null:e.scale(u)}var lO=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=u9(i,e.dataKey,e.domain[a]);return Y()(c)?null:e.scale(c)-o/2+n},lj=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},lA=function(t,e){var r,n=(null!==(r=t.type)&&void 0!==r&&r.defaultProps?u8(u8({},t.type.defaultProps),t.props):t.props).stackId;if(z(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null},lS=function(t,e,r){return Object.keys(t).reduce(function(n,o){var i=t[o].stackedData.reduce(function(t,n){var o=n.slice(e,r+1).reduce(function(t,e){return[c2()(e.concat([t[0]]).filter(L)),c0()(e.concat([t[1]]).filter(L))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},lP=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lE=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lk=function(t,e,r){if(Z()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if(L(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(lP.test(t[0])){var o=+lP.exec(t[0])[1];n[0]=e[0]-o}else Z()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if(L(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(lE.test(t[1])){var i=+lE.exec(t[1])[1];n[1]=e[1]+i}else Z()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},lM=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=tB()(e,function(t){return t.coordinate}),i=1/0,a=1,c=o.length;a<c;a++){var u=o[a],l=o[a-1];i=Math.min((u.coordinate||0)-(l.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},l_=function(t,e,r){return!t||!t.length||c6()(t,C()(r,"type.defaultProps.domain"))?e:t},lT=function(t,e){var r=t.type.defaultProps?u8(u8({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return u8(u8({},tj(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:lr(t),value:u9(e,n),type:c,payload:e,chartType:u,hide:l})};function lN(t){return(lN="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lC(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lD(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lC(Object(r),!0).forEach(function(e){lI(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lC(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lI(t,e,r){var n;return(n=function(t,e){if("object"!=lN(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lN(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lN(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var lB=["Webkit","Moz","O","ms"],lR=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=lB.reduce(function(t,n){return lD(lD({},t),{},lI({},n+r,e))},{});return n[t]=e,n};function lL(t){return(lL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lz(){return(lz=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function lU(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function l$(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lU(Object(r),!0).forEach(function(e){lV(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lU(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lF(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,lH(n.key),n)}}function lq(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(lq=function(){return!!t})()}function lW(t){return(lW=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function lX(t,e){return(lX=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function lV(t,e,r){return(e=lH(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lH(t){var e=function(t,e){if("object"!=lL(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lL(e)?e:e+""}var lG=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=nD().domain(tD()(0,c)).range([o,o+i-a]),l=u.domain().map(function(t){return u(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:l}},lY=function(t){return t.changedTouches&&!!t.changedTouches.length},lK=function(t){var e,r;function n(t){var e,r,o;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[t],r=lW(r),lV(e=function(t,e){if(e&&("object"===lL(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,lq()?Reflect.construct(r,o||[],lW(this).constructor):r.apply(this,o)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),lV(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),lV(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,o=t.startIndex;null==n||n({endIndex:r,startIndex:o})}),e.detachDragEndListener()}),lV(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),lV(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),lV(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),lV(e,"handleSlideDragStart",function(t){var r=lY(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&lX(t,e)}(n,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,c=i.data.length-1,u=Math.min(e,r),l=Math.max(e,r),s=n.getIndexInRange(o,u),f=n.getIndexInRange(o,l);return{startIndex:s-s%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=u9(r[t],o,t);return Z()(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,c=i.width,u=i.travellerWidth,l=i.startIndex,s=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-o,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-o));var d=this.getIndex({startX:n+p,endX:o+p});(d.startIndex!==l||d.endIndex!==s)&&f&&f(d),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=lY(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,d=c.data,h={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,u+l-s-a):y<0&&(y=Math.max(y,u-a)),h[n]=a+y;var v=this.getIndex(h),m=v.startIndex,b=v.endIndex,g=function(){var t=d.length-1;return"startX"===n&&(o>i?m%p==0:b%p==0)||!!(o<i)&&b===t||"endX"===n&&(o>i?b%p==0:m%p==0)||!!(o>i)&&b===t};this.setState(lV(lV({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,c=this.state[e],u=o.indexOf(c);if(-1!==u){var l=u+t;if(-1!==l&&!(l>=o.length)){var s=o[l];("startX"!==e||!(s>=a))&&("endX"!==e||!(s<=i))&&this.setState(lV({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.fill,a=t.stroke;return S().createElement("rect",{stroke:a,fill:i,x:e,y:r,width:n,height:o})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.data,a=t.children,c=t.padding,u=A.Children.only(a);return u?S().cloneElement(u,{x:e,y:r,width:n,height:o,margin:c,compact:!0,data:i}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,o,i=this,a=this.props,c=a.y,u=a.travellerWidth,l=a.height,s=a.traveller,f=a.ariaLabel,p=a.data,d=a.startIndex,h=a.endIndex,y=Math.max(t,this.props.x),v=l$(l$({},tj(this.props,!1)),{},{x:y,y:c,width:u,height:l}),m=f||"Min value: ".concat(null===(r=p[d])||void 0===r?void 0:r.name,", Max value: ").concat(null===(o=p[h])||void 0===o?void 0:o.name);return S().createElement(tq,{tabIndex:0,role:"slider","aria-label":m,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),i.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){i.setState({isTravellerFocused:!0})},onBlur:function(){i.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(s,v))}},{key:"renderSlide",value:function(t,e){var r=this.props,n=r.y,o=r.height,i=r.stroke,a=r.travellerWidth,c=Math.min(t,e)+a,u=Math.max(Math.abs(e-t)-a,0);return S().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:c,y:n,width:u,height:o})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,n=t.y,o=t.height,i=t.travellerWidth,a=t.stroke,c=this.state,u=c.startX,l=c.endX,s={pointerEvents:"none",fill:a};return S().createElement(tq,{className:"recharts-brush-texts"},S().createElement(oi,lz({textAnchor:"end",verticalAnchor:"middle",x:Math.min(u,l)-5,y:n+o/2},s),this.getTextOfTick(e)),S().createElement(oi,lz({textAnchor:"start",verticalAnchor:"middle",x:Math.max(u,l)+i+5,y:n+o/2},s),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,n=t.children,o=t.x,i=t.y,a=t.width,c=t.height,u=t.alwaysShowText,l=this.state,s=l.startX,f=l.endX,p=l.isTextActive,d=l.isSlideMoving,h=l.isTravellerMoving,y=l.isTravellerFocused;if(!e||!e.length||!L(o)||!L(i)||!L(a)||!L(c)||a<=0||c<=0)return null;var v=(0,j.A)("recharts-brush",r),m=1===S().Children.count(n),b=lR("userSelect","none");return S().createElement(tq,{className:v,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:b},this.renderBackground(),m&&this.renderPanorama(),this.renderSlide(s,f),this.renderTravellerLayer(s,"startX"),this.renderTravellerLayer(f,"endX"),(p||d||h||y||u)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,n=t.width,o=t.height,i=t.stroke,a=Math.floor(r+o/2)-1;return S().createElement(S().Fragment,null,S().createElement("rect",{x:e,y:r,width:n,height:o,fill:i,stroke:"none"}),S().createElement("line",{x1:e+1,y1:a,x2:e+n-1,y2:a,fill:"none",stroke:"#fff"}),S().createElement("line",{x1:e+1,y1:a+2,x2:e+n-1,y2:a+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){var r;return S().isValidElement(t)?S().cloneElement(t,e):Z()(t)?t(e):n.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return l$({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?lG({data:r,width:n,x:o,travellerWidth:i,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);t[i]>e?o=i:n=i}return e>=t[o]?o:n}}],e&&lF(n.prototype,e),r&&lF(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(A.PureComponent);function lZ(t){return(lZ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lQ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lJ(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lQ(Object(r),!0).forEach(function(e){(function(t,e,r){var n;(n=function(t,e){if("object"!=lZ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lZ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lZ(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r})(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lQ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}lV(lK,"displayName","Brush"),lV(lK,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var l0=Math.PI/180,l1=function(t,e,r,n){return{x:t+Math.cos(-l0*n)*r,y:e+Math.sin(-l0*n)*r}},l2=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},l4=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=l2({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var c=Math.acos((r-o)/a);return n>i&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},l3=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},l5=function(t,e){var r,n=l4({x:t.x,y:t.y},e),o=n.radius,i=n.angle,a=e.innerRadius,c=e.outerRadius;if(o<a||o>c)return!1;if(0===o)return!0;var u=l3(e),l=u.startAngle,s=u.endAngle,f=i;if(l<=s){for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}else{for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}return r?lJ(lJ({},e),{},{radius:o,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null};function l6(t){return(l6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var l8=["offset"];function l7(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function l9(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function st(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l9(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=l6(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l6(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l6(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l9(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function se(){return(se=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var sr=function(t){var e=t.value,r=t.formatter,n=Y()(t.children)?e:t.children;return Z()(r)?r(n):n},sn=function(t,e,r){var n,o,i=t.position,a=t.viewBox,c=t.offset,u=t.className,l=a.cx,s=a.cy,f=a.innerRadius,p=a.outerRadius,d=a.startAngle,h=a.endAngle,y=a.clockWise,v=(f+p)/2,m=B(h-d)*Math.min(Math.abs(h-d),360),b=m>=0?1:-1;"insideStart"===i?(n=d+b*c,o=y):"insideEnd"===i?(n=h-b*c,o=!y):"end"===i&&(n=h+b*c,o=y),o=m<=0?o:!o;var g=l1(l,s,v,n),x=l1(l,s,v,n+(o?1:-1)*359),w="M".concat(g.x,",").concat(g.y,"\n    A").concat(v,",").concat(v,",0,1,").concat(+!o,",\n    ").concat(x.x,",").concat(x.y),O=Y()(t.id)?$("recharts-radial-line-"):t.id;return S().createElement("text",se({},r,{dominantBaseline:"central",className:(0,j.A)("recharts-radial-bar-label",u)}),S().createElement("defs",null,S().createElement("path",{id:O,d:w})),S().createElement("textPath",{xlinkHref:"#".concat(O)},e))},so=function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e.cx,i=e.cy,a=e.innerRadius,c=e.outerRadius,u=(e.startAngle+e.endAngle)/2;if("outside"===n){var l=l1(o,i,c+r,u),s=l.x;return{x:s,y:l.y,textAnchor:s>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=l1(o,i,(a+c)/2,u);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},si=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e.x,a=e.y,c=e.width,u=e.height,l=u>=0?1:-1,s=l*n,f=l>0?"end":"start",p=l>0?"start":"end",d=c>=0?1:-1,h=d*n,y=d>0?"end":"start",v=d>0?"start":"end";if("top"===o)return st(st({},{x:i+c/2,y:a-l*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===o)return st(st({},{x:i+c/2,y:a+u+s,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+u),0),width:c}:{});if("left"===o){var m={x:i-h,y:a+u/2,textAnchor:y,verticalAnchor:"middle"};return st(st({},m),r?{width:Math.max(m.x-r.x,0),height:u}:{})}if("right"===o){var b={x:i+c+h,y:a+u/2,textAnchor:v,verticalAnchor:"middle"};return st(st({},b),r?{width:Math.max(r.x+r.width-b.x,0),height:u}:{})}var g=r?{width:c,height:u}:{};return"insideLeft"===o?st({x:i+h,y:a+u/2,textAnchor:v,verticalAnchor:"middle"},g):"insideRight"===o?st({x:i+c-h,y:a+u/2,textAnchor:y,verticalAnchor:"middle"},g):"insideTop"===o?st({x:i+c/2,y:a+s,textAnchor:"middle",verticalAnchor:p},g):"insideBottom"===o?st({x:i+c/2,y:a+u-s,textAnchor:"middle",verticalAnchor:f},g):"insideTopLeft"===o?st({x:i+h,y:a+s,textAnchor:v,verticalAnchor:p},g):"insideTopRight"===o?st({x:i+c-h,y:a+s,textAnchor:y,verticalAnchor:p},g):"insideBottomLeft"===o?st({x:i+h,y:a+u-s,textAnchor:v,verticalAnchor:f},g):"insideBottomRight"===o?st({x:i+c-h,y:a+u-s,textAnchor:y,verticalAnchor:f},g):J()(o)&&(L(o.x)||R(o.x))&&(L(o.y)||R(o.y))?st({x:i+F(o.x,c),y:a+F(o.y,u),textAnchor:"end",verticalAnchor:"end"},g):st({x:i+c/2,y:a+u/2,textAnchor:"middle",verticalAnchor:"middle"},g)};function sa(t){var e,r=t.offset,n=st({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,l8)),o=n.viewBox,i=n.position,a=n.value,c=n.children,u=n.content,l=n.className,s=n.textBreakAll;if(!o||Y()(a)&&Y()(c)&&!(0,A.isValidElement)(u)&&!Z()(u))return null;if((0,A.isValidElement)(u))return(0,A.cloneElement)(u,n);if(Z()(u)){if(e=(0,A.createElement)(u,n),(0,A.isValidElement)(e))return e}else e=sr(n);var f="cx"in o&&L(o.cx),p=tj(n,!0);if(f&&("insideStart"===i||"insideEnd"===i||"end"===i))return sn(n,e,p);var d=f?so(n):si(n);return S().createElement(oi,se({className:(0,j.A)("recharts-label",void 0===l?"":l)},p,d,{breakAll:s}),e)}sa.displayName="Label";var sc=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,d=t.left,h=t.width,y=t.height,v=t.clockWise,m=t.labelViewBox;if(m)return m;if(L(h)&&L(y)){if(L(s)&&L(f))return{x:s,y:f,width:h,height:y};if(L(p)&&L(d))return{x:p,y:d,width:h,height:y}}return L(s)&&L(f)?{x:s,y:f,width:0,height:0}:L(e)&&L(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:v}:t.viewBox?t.viewBox:{}};sa.parseViewBox=sc,sa.renderCallByParent=function(t,e){var r,n,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var i=t.children,a=sc(t),c=tb(i,sa).map(function(t,r){return(0,A.cloneElement)(t,{viewBox:e||a,key:"label-".concat(r)})});if(!o)return c;return[(r=t.label,n=e||a,r?!0===r?S().createElement(sa,{key:"label-implicit",viewBox:n}):z(r)?S().createElement(sa,{key:"label-implicit",viewBox:n,value:r}):(0,A.isValidElement)(r)?r.type===sa?(0,A.cloneElement)(r,{key:"label-implicit",viewBox:n}):S().createElement(sa,{key:"label-implicit",content:r,viewBox:n}):Z()(r)?S().createElement(sa,{key:"label-implicit",content:r,viewBox:n}):J()(r)?S().createElement(sa,se({viewBox:n},r,{key:"label-implicit"})):null:null)].concat(function(t){if(Array.isArray(t))return l7(t)}(c)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(c)||function(t,e){if(t){if("string"==typeof t)return l7(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return l7(t,e)}}(c)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())};var su=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},sl=r(69691),ss=r.n(sl),sf=r(47212),sp=r.n(sf),sd=function(t){return null};sd.displayName="Cell";var sh=r(5359),sy=r.n(sh);function sv(t){return(sv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var sm=["valueAccessor"],sb=["data","dataKey","clockWise","id","textBreakAll"];function sg(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sx(){return(sx=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sw(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sO(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sw(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sv(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sv(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sv(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sw(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sj(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var sA=function(t){return Array.isArray(t.value)?sy()(t.value):t.value};function sS(t){var e=t.valueAccessor,r=void 0===e?sA:e,n=sj(t,sm),o=n.data,i=n.dataKey,a=n.clockWise,c=n.id,u=n.textBreakAll,l=sj(n,sb);return o&&o.length?S().createElement(tq,{className:"recharts-label-list"},o.map(function(t,e){var n=Y()(i)?r(t,e):u9(t&&t.payload,i),o=Y()(c)?{}:{id:"".concat(c,"-").concat(e)};return S().createElement(sa,sx({},tj(t,!0),l,o,{parentViewBox:t.parentViewBox,value:n,textBreakAll:u,viewBox:sa.parseViewBox(Y()(a)?t:sO(sO({},t),{},{clockWise:a})),key:"label-".concat(e),index:e}))})):null}sS.displayName="LabelList",sS.renderCallByParent=function(t,e){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&n&&!t.label)return null;var o=tb(t.children,sS).map(function(t,r){return(0,A.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return n?[(r=t.label)?!0===r?S().createElement(sS,{key:"labelList-implicit",data:e}):S().isValidElement(r)||Z()(r)?S().createElement(sS,{key:"labelList-implicit",data:e,content:r}):J()(r)?S().createElement(sS,sx({data:e},r,{key:"labelList-implicit"})):null:null].concat(function(t){if(Array.isArray(t))return sg(t)}(o)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(o)||function(t,e){if(t){if("string"==typeof t)return sg(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sg(t,e)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):o};var sP=r(38404),sE=r.n(sP),sk=r(98451),sM=r.n(sk);function s_(t){return(s_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sT(){return(sT=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sN(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sC(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sD(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sC(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=s_(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s_(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s_(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sC(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sI=function(t,e,r,n,o){var i,a=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-a/2,",").concat(e+o)+"L ".concat(t+r-a/2-n,",").concat(e+o)+"L ".concat(t,",").concat(e," Z")},sB={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},sR=function(t){var e,r=sD(sD({},sB),t),n=(0,A.useRef)(),o=function(t){if(Array.isArray(t))return t}(e=(0,A.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return sN(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sN(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];(0,A.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&a(t)}catch(t){}},[]);var c=r.x,u=r.y,l=r.upperWidth,s=r.lowerWidth,f=r.height,p=r.className,d=r.animationEasing,h=r.animationDuration,y=r.animationBegin,v=r.isUpdateAnimationActive;if(c!==+c||u!==+u||l!==+l||s!==+s||f!==+f||0===l&&0===s||0===f)return null;var m=(0,j.A)("recharts-trapezoid",p);return v?S().createElement(nv,{canBegin:i>0,from:{upperWidth:0,lowerWidth:0,height:f,x:c,y:u},to:{upperWidth:l,lowerWidth:s,height:f,x:c,y:u},duration:h,animationEasing:d,isActive:v},function(t){var e=t.upperWidth,o=t.lowerWidth,a=t.height,c=t.x,u=t.y;return S().createElement(nv,{canBegin:i>0,from:"0px ".concat(-1===i?1:i,"px"),to:"".concat(i,"px 0px"),attributeName:"strokeDasharray",begin:y,duration:h,easing:d},S().createElement("path",sT({},tj(r,!0),{className:m,d:sI(c,u,e,o,a),ref:n})))}):S().createElement("g",null,S().createElement("path",sT({},tj(r,!0),{className:m,d:sI(c,u,l,s,f)})))};function sL(t){return(sL="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sz(){return(sz=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sU(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s$(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sU(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sL(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sL(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sL(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sU(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sF=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,a=t.isExternal,c=t.cornerRadius,u=t.cornerIsExternal,l=c*(a?1:-1)+n,s=Math.asin(c/l)/l0,f=u?o:o+i*s;return{center:l1(e,r,l,f),circleTangency:l1(e,r,n,f),lineTangency:l1(e,r,l*Math.cos(s*l0),u?o-i*s:o),theta:s}},sq=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.startAngle,a=t.endAngle,c=B(a-i)*Math.min(Math.abs(a-i),359.999),u=i+c,l=l1(e,r,o,i),s=l1(e,r,o,u),f="M ".concat(l.x,",").concat(l.y,"\n    A ").concat(o,",").concat(o,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(i>u),",\n    ").concat(s.x,",").concat(s.y,"\n  ");if(n>0){var p=l1(e,r,n,i),d=l1(e,r,n,u);f+="L ".concat(d.x,",").concat(d.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(i<=u),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(e,",").concat(r," Z");return f},sW=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,u=t.startAngle,l=t.endAngle,s=B(l-u),f=sF({cx:e,cy:r,radius:o,angle:u,sign:s,cornerRadius:i,cornerIsExternal:c}),p=f.circleTangency,d=f.lineTangency,h=f.theta,y=sF({cx:e,cy:r,radius:o,angle:l,sign:-s,cornerRadius:i,cornerIsExternal:c}),v=y.circleTangency,m=y.lineTangency,b=y.theta,g=c?Math.abs(u-l):Math.abs(u-l)-h-b;if(g<0)return a?"M ".concat(d.x,",").concat(d.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):sq({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:u,endAngle:l});var x="M ".concat(d.x,",").concat(d.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(g>180),",").concat(+(s<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var w=sF({cx:e,cy:r,radius:n,angle:u,sign:s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),O=w.circleTangency,j=w.lineTangency,A=w.theta,S=sF({cx:e,cy:r,radius:n,angle:l,sign:-s,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),P=S.circleTangency,E=S.lineTangency,k=S.theta,M=c?Math.abs(u-l):Math.abs(u-l)-A-k;if(M<0&&0===i)return"".concat(x,"L").concat(e,",").concat(r,"Z");x+="L".concat(E.x,",").concat(E.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(P.x,",").concat(P.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(M>180),",").concat(+(s>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(s<0),",").concat(j.x,",").concat(j.y,"Z")}else x+="L".concat(e,",").concat(r,"Z");return x},sX={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},sV=function(t){var e,r=s$(s$({},sX),t),n=r.cx,o=r.cy,i=r.innerRadius,a=r.outerRadius,c=r.cornerRadius,u=r.forceCornerRadius,l=r.cornerIsExternal,s=r.startAngle,f=r.endAngle,p=r.className;if(a<i||s===f)return null;var d=(0,j.A)("recharts-sector",p),h=a-i,y=F(c,h,0,!0);return e=y>0&&360>Math.abs(s-f)?sW({cx:n,cy:o,innerRadius:i,outerRadius:a,cornerRadius:Math.min(y,h/2),forceCornerRadius:u,cornerIsExternal:l,startAngle:s,endAngle:f}):sq({cx:n,cy:o,innerRadius:i,outerRadius:a,startAngle:s,endAngle:f}),S().createElement("path",sz({},tj(r,!0),{className:d,d:e,role:"img"}))},sH=["option","shapeType","propTransformer","activeClassName","isActive"];function sG(t){return(sG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sY(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sK(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sY(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=sG(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sG(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sY(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sZ(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return S().createElement(nS,r);case"trapezoid":return S().createElement(sR,r);case"sector":return S().createElement(sV,r);case"symbols":if("symbols"===e)return S().createElement(eH,r);break;default:return null}}function sQ(t){var e,r=t.option,n=t.shapeType,o=t.propTransformer,i=t.activeClassName,a=t.isActive,c=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,sH);if((0,A.isValidElement)(r))e=(0,A.cloneElement)(r,sK(sK({},c),(0,A.isValidElement)(r)?r.props:r));else if(Z()(r))e=r(c);else if(sE()(r)&&!sM()(r)){var u=(void 0===o?function(t,e){return sK(sK({},e),t)}:o)(r,c);e=S().createElement(sZ,{shapeType:n,elementProps:u})}else e=S().createElement(sZ,{shapeType:n,elementProps:c});return a?S().createElement(tq,{className:void 0===i?"recharts-active-shape":i},e):e}function sJ(t,e){return null!=e&&"trapezoids"in t.props}function s0(t,e){return null!=e&&"sectors"in t.props}function s1(t,e){return null!=e&&"points"in t.props}function s2(t,e){var r,n,o=t.x===(null==e||null===(r=e.labelViewBox)||void 0===r?void 0:r.x)||t.x===e.x,i=t.y===(null==e||null===(n=e.labelViewBox)||void 0===n?void 0:n.y)||t.y===e.y;return o&&i}function s4(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function s3(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}var s5=["x","y"];function s6(t){return(s6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s8(){return(s8=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s7(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s9(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s7(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=s6(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=s6(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==s6(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s7(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ft(t,e){var r=t.x,n=t.y,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,s5),i=parseInt("".concat(r),10),a=parseInt("".concat(n),10),c=parseInt("".concat(e.height||o.height),10),u=parseInt("".concat(e.width||o.width),10);return s9(s9(s9(s9(s9({},e),o),i?{x:i}:{}),a?{y:a}:{}),{},{height:c,width:u,name:e.name,radius:e.radius})}function fe(t){return S().createElement(sQ,s8({shapeType:"rectangle",propTransformer:ft,activeClassName:"recharts-active-bar"},t))}var fr=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var o="number"==typeof r;return o?t(r,n):(o||tR(!1),e)}},fn=["value","background"];function fo(t){return(fo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fi(){return(fi=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fa(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fc(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fa(Object(r),!0).forEach(function(e){fp(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fa(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fu(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fd(n.key),n)}}function fl(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fl=function(){return!!t})()}function fs(t){return(fs=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ff(t,e){return(ff=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fp(t,e,r){return(e=fd(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fd(t){var e=function(t,e){if("object"!=fo(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fo(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fo(e)?e:e+""}var fh=function(t){var e,r;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n);for(var t,e,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=fs(e),fp(t=function(t,e){if(e&&("object"===fo(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,fl()?Reflect.construct(e,r||[],fs(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),fp(t,"id",$("recharts-bar-")),fp(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),fp(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ff(t,e)}(n,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,n=r.shape,o=r.dataKey,i=r.activeIndex,a=r.activeBar,c=tj(this.props,!1);return t&&t.map(function(t,r){var u=r===i,l=fc(fc(fc({},c),t),{},{isActive:u,option:u?a:n,index:r,dataKey:o,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return S().createElement(tq,fi({className:"recharts-bar-rectangle"},tu(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value)}),S().createElement(fe,l))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.layout,o=e.isAnimationActive,i=e.animationBegin,a=e.animationDuration,c=e.animationEasing,u=e.animationId,l=this.state.prevData;return S().createElement(nv,{begin:i,duration:a,isActive:o,easing:c,from:{t:0},to:{t:1},key:"bar-".concat(u),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var o=e.t,i=r.map(function(t,e){var r=l&&l[e];if(r){var i=X(r.x,t.x),a=X(r.y,t.y),c=X(r.width,t.width),u=X(r.height,t.height);return fc(fc({},t),{},{x:i(o),y:a(o),width:c(o),height:u(o)})}if("horizontal"===n){var s=X(0,t.height)(o);return fc(fc({},t),{},{y:t.y+t.height-s,height:s})}var f=X(0,t.width)(o);return fc(fc({},t),{},{width:f})});return S().createElement(tq,null,t.renderRectanglesStatically(i))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!c6()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,n=e.dataKey,o=e.activeIndex,i=tj(this.props.background,!1);return r.map(function(e,r){e.value;var a=e.background,c=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,fn);if(!a)return null;var u=fc(fc(fc(fc(fc({},c),{},{fill:"#eee"},a),i),tu(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return S().createElement(fe,fi({key:"background-bar-".concat(r),option:t.props.background,isActive:r===o},u))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,o=r.xAxis,i=r.yAxis,a=r.layout,c=tb(r.children,uQ);if(!c)return null;var u="vertical"===a?n[0].height/2:n[0].width/2,l=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:u9(t,e)}};return S().createElement(tq,{clipPath:t?"url(#clipPath-".concat(e,")"):null},c.map(function(t){return S().cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:n,xAxis:o,yAxis:i,layout:a,offset:u,dataPointFormatter:l})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,o=t.xAxis,i=t.yAxis,a=t.left,c=t.top,u=t.width,l=t.height,s=t.isAnimationActive,f=t.background,p=t.id;if(e||!r||!r.length)return null;var d=this.state.isAnimationFinished,h=(0,j.A)("recharts-bar",n),y=o&&o.allowDataOverflow,v=i&&i.allowDataOverflow,m=y||v,b=Y()(p)?this.id:p;return S().createElement(tq,{className:h},y||v?S().createElement("defs",null,S().createElement("clipPath",{id:"clipPath-".concat(b)},S().createElement("rect",{x:y?a:a-u/2,y:v?c:c-l/2,width:y?u:2*u,height:v?l:2*l}))):null,S().createElement(tq,{className:"recharts-bar-rectangles",clipPath:m?"url(#clipPath-".concat(b,")"):null},f?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(m,b),(!s||d)&&sS.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&fu(n.prototype,e),r&&fu(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(A.PureComponent);function fy(t){return(fy="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fv(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fx(n.key),n)}}function fm(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fb(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fm(Object(r),!0).forEach(function(e){fg(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fm(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fg(t,e,r){return(e=fx(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fx(t){var e=function(t,e){if("object"!=fy(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fy(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fy(e)?e:e+""}fp(fh,"displayName","Bar"),fp(fh,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!ee.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),fp(fh,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,d=ly(n,r);if(!d)return null;var h=e.layout,y=r.type.defaultProps,v=void 0!==y?fc(fc({},y),r.props):r.props,m=v.dataKey,b=v.children,g=v.minPointSize,x="horizontal"===h?a:i,w=l?x.scale.domain():null,O=lj({numericAxis:x}),j=tb(b,sd),A=f.map(function(t,e){l?f=lv(l[s+e],w):Array.isArray(f=u9(t,m))||(f=[O,f]);var n=fr(g,fh.defaultProps.minPointSize)(f[1],e);if("horizontal"===h){var f,p,y,v,b,x,A,S=[a.scale(f[0]),a.scale(f[1])],P=S[0],E=S[1];p=lO({axis:i,ticks:c,bandSize:o,offset:d.offset,entry:t,index:e}),y=null!==(A=null!=E?E:P)&&void 0!==A?A:void 0,v=d.size;var k=P-E;if(b=Number.isNaN(k)?0:k,x={x:p,y:a.y,width:v,height:a.height},Math.abs(n)>0&&Math.abs(b)<Math.abs(n)){var M=B(b||n)*(Math.abs(n)-Math.abs(b));y-=M,b+=M}}else{var _=[i.scale(f[0]),i.scale(f[1])],T=_[0],N=_[1];if(p=T,y=lO({axis:a,ticks:u,bandSize:o,offset:d.offset,entry:t,index:e}),v=N-T,b=d.size,x={x:i.x,y:y,width:i.width,height:b},Math.abs(n)>0&&Math.abs(v)<Math.abs(n)){var C=B(v||n)*(Math.abs(n)-Math.abs(v));v+=C}}return fc(fc(fc({},t),{},{x:p,y:y,width:v,height:b,value:l?f:f[1],payload:t,background:x},j&&j[e]&&j[e].props),{},{tooltipPayload:[lT(r,t)],tooltipPosition:{x:p+v/2,y:y+b/2}})});return fc({data:A,layout:h},p)});var fw=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},fO=function(){var t,e;function r(t){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,r),this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&fv(r.prototype,t),e&&fv(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();fg(fO,"EPS",1e-4);var fj=function(t){var e=Object.keys(t).reduce(function(e,r){return fb(fb({},e),{},fg({},r,fO.create(t[r])))},{});return fb(fb({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return ss()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:o})})},isInRange:function(t){return sp()(t,function(t,r){return e[r].isInRange(t)})}})},fA=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(n%180+180)%180*Math.PI/180,i=Math.atan(r/e);return Math.abs(o>i&&o<Math.PI-i?r/Math.sin(o):e/Math.cos(o))};function fS(){return(fS=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fP(t){return(fP="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fE(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fk(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fE(Object(r),!0).forEach(function(e){fN(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fE(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fM(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fM=function(){return!!t})()}function f_(t){return(f_=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fT(t,e){return(fT=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fN(t,e,r){return(e=fC(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fC(t){var e=function(t,e){if("object"!=fP(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fP(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fP(e)?e:e+""}var fD=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=fj({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return su(t,"discard")&&!i.isInRange(a)?null:a},fI=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=f_(t),function(t,e){if(e&&("object"===fP(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,fM()?Reflect.construct(t,e||[],f_(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&fT(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,n=t.y,o=t.r,i=t.alwaysShow,a=t.clipPathId,c=z(e),u=z(n);if(H(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!c||!u)return null;var l=fD(this.props);if(!l)return null;var s=l.x,f=l.y,p=this.props,d=p.shape,h=p.className,y=fk(fk({clipPath:su(this.props,"hidden")?"url(#".concat(a,")"):void 0},tj(this.props,!0)),{},{cx:s,cy:f});return S().createElement(tq,{className:(0,j.A)("recharts-reference-dot",h)},r.renderDot(d,y),sa.renderCallByParent(this.props,{x:s-o,y:f-o,width:2*o,height:2*o}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fC(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(S().Component);fN(fI,"displayName","ReferenceDot"),fN(fI,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),fN(fI,"renderDot",function(t,e){var r;return S().isValidElement(t)?S().cloneElement(t,e):Z()(t)?t(e):S().createElement(ra,fS({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var fB=r(67367),fR=r.n(fB);r(22964);var fL=r(86451),fz=r.n(fL)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),fU=(0,A.createContext)(void 0),f$=(0,A.createContext)(void 0),fF=(0,A.createContext)(void 0),fq=(0,A.createContext)({}),fW=(0,A.createContext)(void 0),fX=(0,A.createContext)(0),fV=(0,A.createContext)(0),fH=function(t){var e=t.state,r=e.xAxisMap,n=e.yAxisMap,o=e.offset,i=t.clipPathId,a=t.children,c=t.width,u=t.height,l=fz(o);return S().createElement(fU.Provider,{value:r},S().createElement(f$.Provider,{value:n},S().createElement(fq.Provider,{value:o},S().createElement(fF.Provider,{value:l},S().createElement(fW.Provider,{value:i},S().createElement(fX.Provider,{value:u},S().createElement(fV.Provider,{value:c},a)))))))},fG=function(t){var e=(0,A.useContext)(fU);null==e&&tR(!1);var r=e[t];return null==r&&tR(!1),r},fY=function(t){var e=(0,A.useContext)(f$);null==e&&tR(!1);var r=e[t];return null==r&&tR(!1),r},fK=function(){return(0,A.useContext)(fV)},fZ=function(){return(0,A.useContext)(fX)};function fQ(t){return(fQ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fJ(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fJ=function(){return!!t})()}function f0(t){return(f0=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f1(t,e){return(f1=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function f2(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f4(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f2(Object(r),!0).forEach(function(e){f3(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f2(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function f3(t,e,r){return(e=f5(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f5(t){var e=function(t,e){if("object"!=fQ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fQ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fQ(e)?e:e+""}function f6(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f8(){return(f8=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var f7=function(t,e){var r;return S().isValidElement(t)?S().cloneElement(t,e):Z()(t)?t(e):S().createElement("line",f8({},e,{className:"recharts-reference-line-line"}))},f9=function(t,e,r,n,o,i,a,c,u){var l=o.x,s=o.y,f=o.width,p=o.height;if(r){var d=u.y,h=t.y.apply(d,{position:i});if(su(u,"discard")&&!t.y.isInRange(h))return null;var y=[{x:l+f,y:h},{x:l,y:h}];return"left"===c?y.reverse():y}if(e){var v=u.x,m=t.x.apply(v,{position:i});if(su(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map(function(e){return t.apply(e,{position:i})});return su(u,"discard")&&fR()(g,function(e){return!t.isInRange(e)})?null:g}return null};function pt(t){var e,r=t.x,n=t.y,o=t.segment,i=t.xAxisId,a=t.yAxisId,c=t.shape,u=t.className,l=t.alwaysShow,s=(0,A.useContext)(fW),f=fG(i),p=fY(a),d=(0,A.useContext)(fF);if(!s||!d)return null;H(void 0===l,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var h=f9(fj({x:f.scale,y:p.scale}),z(r),z(n),o&&2===o.length,d,t.position,f.orientation,p.orientation,t);if(!h)return null;var y=function(t){if(Array.isArray(t))return t}(h)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{i=(r=r.call(t)).next;for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(h,2)||function(t,e){if(t){if("string"==typeof t)return f6(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return f6(t,e)}}(h,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),v=y[0],m=v.x,b=v.y,g=y[1],x=g.x,w=g.y,O=f4(f4({clipPath:su(t,"hidden")?"url(#".concat(s,")"):void 0},tj(t,!0)),{},{x1:m,y1:b,x2:x,y2:w});return S().createElement(tq,{className:(0,j.A)("recharts-reference-line",u)},f7(c,O),sa.renderCallByParent(t,fw({x:(e={x1:m,y1:b,x2:x,y2:w}).x1,y:e.y1},{x:e.x2,y:e.y2})))}var pe=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=f0(t),function(t,e){if(e&&("object"===fQ(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,fJ()?Reflect.construct(t,e||[],f0(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&f1(t,e)}(r,t),e=[{key:"render",value:function(){return S().createElement(pt,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,f5(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(S().Component);function pr(){return(pr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pn(t){return(pn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function po(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pi(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?po(Object(r),!0).forEach(function(e){pl(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):po(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}f3(pe,"displayName","ReferenceLine"),f3(pe,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function pa(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(pa=function(){return!!t})()}function pc(t){return(pc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function pu(t,e){return(pu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function pl(t,e,r){return(e=ps(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ps(t){var e=function(t,e){if("object"!=pn(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pn(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pn(e)?e:e+""}var pf=function(t,e,r,n,o){var i=o.x1,a=o.x2,c=o.y1,u=o.y2,l=o.xAxis,s=o.yAxis;if(!l||!s)return null;var f=fj({x:l.scale,y:s.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},d={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!su(o,"discard")||f.isInRange(p)&&f.isInRange(d)?fw(p,d):null},pp=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=pc(t),function(t,e){if(e&&("object"===pn(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,pa()?Reflect.construct(t,e||[],pc(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&pu(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,n=t.x2,o=t.y1,i=t.y2,a=t.className,c=t.alwaysShow,u=t.clipPathId;H(void 0===c,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var l=z(e),s=z(n),f=z(o),p=z(i),d=this.props.shape;if(!l&&!s&&!f&&!p&&!d)return null;var h=pf(l,s,f,p,this.props);if(!h&&!d)return null;var y=su(this.props,"hidden")?"url(#".concat(u,")"):void 0;return S().createElement(tq,{className:(0,j.A)("recharts-reference-area",a)},r.renderRect(d,pi(pi({clipPath:y},tj(this.props,!0)),h)),sa.renderCallByParent(this.props,h))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ps(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(S().Component);function pd(t){return function(t){if(Array.isArray(t))return ph(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return ph(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ph(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ph(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}pl(pp,"displayName","ReferenceArea"),pl(pp,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),pl(pp,"renderRect",function(t,e){var r;return S().isValidElement(t)?S().cloneElement(t,e):Z()(t)?t(e):S().createElement(nS,pr({},e,{className:"recharts-reference-area-rect"}))});var py=function(t,e,r,n,o){var i=tb(t,pe),a=tb(t,fI),c=[].concat(pd(i),pd(a)),u=tb(t,pp),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[l]===r&&su(e.props,"extendDomain")&&L(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),u.length){var p="".concat(s,"1"),d="".concat(s,"2");f=u.reduce(function(t,e){if(e.props[l]===r&&su(e.props,"extendDomain")&&L(e.props[p])&&L(e.props[d])){var n=e.props[p],o=e.props[d];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return L(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},pv=r(11117),pm=new(r.n(pv)()),pb="recharts.syncMouseEvents";function pg(t){return(pg="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function px(t,e,r){return(e=pw(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pw(t){var e=function(t,e){if("object"!=pg(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pg(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pg(e)?e:e+""}var pO=function(){var t,e;return t=function t(){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,t),px(this,"activeIndex",0),px(this,"coordinateList",[]),px(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!==(e=null!=n?n:this.coordinateList)&&void 0!==e?e:[],this.container=null!=i?i:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,u=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,l=o+this.offset.top+i/2+u;this.mouseHandlerCallback({pageX:n+a+c,pageY:l})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pw(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}();function pj(){}function pA(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function pS(t){this._context=t}function pP(t){this._context=t}function pE(t){this._context=t}pS.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:pA(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:pA(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pP.prototype={areaStart:pj,areaEnd:pj,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:pA(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pE.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:pA(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class pk{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function pM(t){this._context=t}function p_(t){this._context=t}function pT(t){return new p_(t)}pM.prototype={areaStart:pj,areaEnd:pj,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t*=1,e*=1,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function pN(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function pC(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function pD(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,c=(i-n)/3;t._context.bezierCurveTo(n+c,o+c*e,i-c,a-c*r,i,a)}function pI(t){this._context=t}function pB(t){this._context=new pR(t)}function pR(t){this._context=t}function pL(t){this._context=t}function pz(t){var e,r,n=t.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(e=0,i[n-1]=(t[n]+o[n-1])/2;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function pU(t,e){this._context=t,this._t=e}function p$(t){return t[0]}function pF(t){return t[1]}function pq(t,e){var r=eN(!0),n=null,o=pT,i=null,a=eL(c);function c(c){var u,l,s,f=(c=cU(c)).length,p=!1;for(null==n&&(i=o(s=a())),u=0;u<=f;++u)!(u<f&&r(l=c[u],u,c))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(l,u,c),+e(l,u,c));if(s)return i=null,s+""||null}return t="function"==typeof t?t:void 0===t?p$:eN(t),e="function"==typeof e?e:void 0===e?pF:eN(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:eN(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:eN(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:eN(!!t),c):r},c.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),c):o},c.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),c):n},c}function pW(t,e,r){var n=null,o=eN(!0),i=null,a=pT,c=null,u=eL(l);function l(l){var s,f,p,d,h,y=(l=cU(l)).length,v=!1,m=Array(y),b=Array(y);for(null==i&&(c=a(h=u())),s=0;s<=y;++s){if(!(s<y&&o(d=l[s],s,l))===v){if(v=!v)f=s,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=s-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}}v&&(m[s]=+t(d,s,l),b[s]=+e(d,s,l),c.point(n?+n(d,s,l):m[s],r?+r(d,s,l):b[s]))}if(h)return c=null,h+""||null}function s(){return pq().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?p$:eN(+t),e="function"==typeof e?e:void 0===e?eN(0):eN(+e),r="function"==typeof r?r:void 0===r?pF:eN(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:eN(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:eN(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:eN(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:eN(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:eN(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:eN(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(o="function"==typeof t?t:eN(!!t),l):o},l.curve=function(t){return arguments.length?(a=t,null!=i&&(c=a(i)),l):a},l.context=function(t){return arguments.length?(null==t?i=c=null:c=a(i=t),l):i},l}function pX(t){return(pX="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pV(){return(pV=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pH(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pG(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pH(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=pX(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pX(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pX(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pH(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}p_.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},pI.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:pD(this,this._t0,pC(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e*=1,(t*=1)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,pD(this,pC(this,r=pN(this,t,e)),r);break;default:pD(this,this._t0,r=pN(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(pB.prototype=Object.create(pI.prototype)).point=function(t,e){pI.prototype.point.call(this,e,t)},pR.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},pL.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r){if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=pz(t),o=pz(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a])}(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},pU.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var pY={curveBasisClosed:function(t){return new pP(t)},curveBasisOpen:function(t){return new pE(t)},curveBasis:function(t){return new pS(t)},curveBumpX:function(t){return new pk(t,!0)},curveBumpY:function(t){return new pk(t,!1)},curveLinearClosed:function(t){return new pM(t)},curveLinear:pT,curveMonotoneX:function(t){return new pI(t)},curveMonotoneY:function(t){return new pB(t)},curveNatural:function(t){return new pL(t)},curveStep:function(t){return new pU(t,.5)},curveStepAfter:function(t){return new pU(t,1)},curveStepBefore:function(t){return new pU(t,0)}},pK=function(t){return t.x===+t.x&&t.y===+t.y},pZ=function(t){return t.x},pQ=function(t){return t.y},pJ=function(t,e){if(Z()(t))return t;var r="curve".concat(ev()(t));return("curveMonotone"===r||"curveBump"===r)&&e?pY["".concat(r).concat("vertical"===e?"Y":"X")]:pY[r]||pT},p0=function(t){var e,r=t.type,n=t.points,o=void 0===n?[]:n,i=t.baseLine,a=t.layout,c=t.connectNulls,u=void 0!==c&&c,l=pJ(void 0===r?"linear":r,a),s=u?o.filter(function(t){return pK(t)}):o;if(Array.isArray(i)){var f=u?i.filter(function(t){return pK(t)}):i,p=s.map(function(t,e){return pG(pG({},t),{},{base:f[e]})});return(e="vertical"===a?pW().y(pQ).x1(pZ).x0(function(t){return t.base.x}):pW().x(pZ).y1(pQ).y0(function(t){return t.base.y})).defined(pK).curve(l),e(p)}return(e="vertical"===a&&L(i)?pW().y(pQ).x1(pZ).x0(i):L(i)?pW().x(pZ).y1(pQ).y0(i):pq().x(pZ).y(pQ)).defined(pK).curve(l),e(s)},p1=function(t){var e=t.className,r=t.points,n=t.path,o=t.pathRef;if((!r||!r.length)&&!n)return null;var i=r&&r.length?p0(t):n;return S().createElement("path",pV({},tj(t,!1),tc(t),{className:(0,j.A)("recharts-curve",e),d:i,ref:o}))};function p2(t){return(p2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var p4=["x","y","top","left","width","height","className"];function p3(){return(p3=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function p5(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var p6=function(t){var e=t.x,r=void 0===e?0:e,n=t.y,o=void 0===n?0:n,i=t.top,a=void 0===i?0:i,c=t.left,u=void 0===c?0:c,l=t.width,s=void 0===l?0:l,f=t.height,p=void 0===f?0:f,d=t.className,h=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p5(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=p2(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p2(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p5(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:o,top:a,left:u,width:s,height:p},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,p4));return L(r)&&L(o)&&L(s)&&L(p)&&L(a)&&L(u)?S().createElement("path",p3({},tj(h,!0),{className:(0,j.A)("recharts-cross",d),d:"M".concat(r,",").concat(a,"v").concat(p,"M").concat(u,",").concat(o,"h").concat(s)})):null};function p8(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[l1(e,r,n,o),l1(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}function p7(t){return(p7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p9(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p9(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=p7(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=p7(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==p7(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p9(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function de(t){var e,r,n,o,i=t.element,a=t.tooltipEventType,c=t.isActive,u=t.activeCoordinate,l=t.activePayload,s=t.offset,f=t.activeTooltipIndex,p=t.tooltipAxisBandSize,d=t.layout,h=t.chartName,y=null!==(r=i.props.cursor)&&void 0!==r?r:null===(n=i.type.defaultProps)||void 0===n?void 0:n.cursor;if(!i||!y||!c||!u||"ScatterChart"!==h&&"axis"!==a)return null;var v=p1;if("ScatterChart"===h)o=u,v=p6;else if("BarChart"===h)e=p/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===d?u.x-e:s.left+.5,y:"horizontal"===d?s.top+.5:u.y-e,width:"horizontal"===d?p:s.width-1,height:"horizontal"===d?s.height-1:p},v=nS;else if("radial"===d){var m=p8(u),b=m.cx,g=m.cy,x=m.radius;o={cx:b,cy:g,startAngle:m.startAngle,endAngle:m.endAngle,innerRadius:x,outerRadius:x},v=sV}else o={points:function(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return p8(e);var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=l1(c,u,l,f),d=l1(c,u,s,f);n=p.x,o=p.y,i=d.x,a=d.y}return[{x:n,y:o},{x:i,y:a}]}(d,u,s)},v=p1;var w=dt(dt(dt(dt({stroke:"#ccc",pointerEvents:"none"},s),o),tj(y,!1)),{},{payload:l,payloadIndex:f,className:(0,j.A)("recharts-tooltip-cursor",y.className)});return(0,A.isValidElement)(y)?(0,A.cloneElement)(y,w):(0,A.createElement)(v,w)}var dr=["item"],dn=["children","className","width","height","style","compact","title","desc"];function di(t){return(di="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function da(){return(da=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dc(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,c=[],u=!0,l=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=i.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,o=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw o}}return c}}(t,e)||dd(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function du(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function dl(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(dl=function(){return!!t})()}function ds(t){return(ds=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function df(t,e){return(df=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dp(t){return function(t){if(Array.isArray(t))return dh(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||dd(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function dd(t,e){if(t){if("string"==typeof t)return dh(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return dh(t,e)}}function dh(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function dy(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dy(Object(r),!0).forEach(function(e){dm(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dy(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dm(t,e,r){return(e=db(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function db(t){var e=function(t,e){if("object"!=di(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=di(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==di(e)?e:e+""}var dg={xAxis:["bottom","top"],yAxis:["left","right"]},dx={width:"100%",height:"100%"},dw={x:0,y:0};function dO(t){return t}var dj=function(t,e,r,n){var o=e.find(function(t){return t&&t.index===r});if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return dv(dv(dv({},n),l1(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var c=o.coordinate,u=n.angle;return dv(dv(dv({},n),l1(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return dw},dA=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(dp(t),dp(r)):t},[]);return i.length>0?i:t&&t.length&&L(n)&&L(o)?t.slice(n,o+1):[]};function dS(t){return"number"===t?[0,"auto"]:void 0}var dP=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=dA(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,c){var u,l,s=null!==(u=c.props.data)&&void 0!==u?u:e;return(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),l=i.dataKey&&!i.allowDuplicatedCategory?V(void 0===s?a:s,i.dataKey,n):s&&s[r]||a[r])?[].concat(dp(o),[lT(c,l)]):o},[])},dE=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=le(i,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=dP(t,e,l,s),p=dj(r,a,l,o);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},dk=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=t.stackOffset,p=ll(l,o);return r.reduce(function(e,r){var d=void 0!==r.type.defaultProps?dv(dv({},r.type.defaultProps),r.props):r.props,h=d.type,y=d.dataKey,v=d.allowDataOverflow,m=d.allowDuplicatedCategory,b=d.scale,g=d.ticks,x=d.includeHidden,w=d[i];if(e[w])return e;var O=dA(t.data,{graphicalItems:n.filter(function(t){var e;return(i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i])===w}),dataStartIndex:c,dataEndIndex:u}),j=O.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],o=null==t?void 0:t[1];if(n&&o&&L(n)&&L(o))return!0}return!1})(d.domain,v,h)&&(P=lk(d.domain,null,v),p&&("number"===h||"auto"!==b)&&(k=lt(O,y,"category")));var A=dS(h);if(!P||0===P.length){var S,P,E,k,M,_=null!==(M=d.domain)&&void 0!==M?M:A;if(y){if(P=lt(O,y,h),"category"===h&&p){var T=W(P);m&&T?(E=P,P=tD()(0,j)):m||(P=l_(_,P,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(dp(t),[e])},[]))}else if("category"===h)P=m?P.filter(function(t){return""!==t&&!Y()(t)}):l_(_,P,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||Y()(e)?t:[].concat(dp(t),[e])},[]);else if("number"===h){var N=lc(O,n.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===w&&(x||!o)}),y,o,l);N&&(P=N)}p&&("number"===h||"auto"!==b)&&(k=lt(O,y,"category"))}else P=p?tD()(0,j):a&&a[w]&&a[w].hasStack&&"number"===h?"expand"===f?[0,1]:lS(a[w].stackGroups,c,u):lu(O,n.filter(function(t){var e=i in t.props?t.props[i]:t.type.defaultProps[i],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===w&&(x||!r)}),h,l,!0);"number"===h?(P=py(s,P,w,o,g),_&&(P=lk(_,P,v))):"category"===h&&_&&P.every(function(t){return _.indexOf(t)>=0})&&(P=_)}return dv(dv({},e),{},dm({},w,dv(dv({},d),{},{axisType:o,domain:P,categoricalDomain:k,duplicateDomain:E,originalDomain:null!==(S=d.domain)&&void 0!==S?S:A,isCategorical:p,layout:l})))},{})},dM=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=dA(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),p=f.length,d=ll(l,o),h=-1;return r.reduce(function(t,e){var y,v=(void 0!==e.type.defaultProps?dv(dv({},e.type.defaultProps),e.props):e.props)[i],m=dS("number");return t[v]?t:(h++,y=d?tD()(0,p):a&&a[v]&&a[v].hasStack?py(s,y=lS(a[v].stackGroups,c,u),v,o):py(s,y=lk(m,lu(f,r.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===v&&!o}),"number",l),n.defaultProps.allowDataOverflow),v,o),dv(dv({},t),{},dm({},v,dv(dv({axisType:o},n.defaultProps),{},{hide:!0,orientation:C()(dg,"".concat(o,".").concat(h%2),null),domain:y,originalDomain:m,isCategorical:d,layout:l}))))},{})},d_=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=tb(l,o),p={};return f&&f.length?p=dk(t,{axes:f,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):i&&i.length&&(p=dM(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),p},dT=function(t){var e=q(t),r=ls(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:tB()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:lM(e,r)}},dN=function(t){var e=t.children,r=t.defaultShowTooltip,n=tg(e,lK),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},dC=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},dD=function(t,e){var r=t.props,n=t.graphicalItems,o=t.xAxisMap,i=void 0===o?{}:o,a=t.yAxisMap,c=void 0===a?{}:a,u=r.width,l=r.height,s=r.children,f=r.margin||{},p=tg(s,lK),d=tg(s,ro),h=Object.keys(c).reduce(function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:dv(dv({},t),{},dm({},n,t[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(i).reduce(function(t,e){var r=i[e],n=r.orientation;return r.mirror||r.hide?t:dv(dv({},t),{},dm({},n,C()(t,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),v=dv(dv({},y),h),m=v.bottom;p&&(v.bottom+=p.props.height||lK.defaultProps.height),d&&e&&(v=li(v,n,r,e));var b=u-v.left-v.right,g=l-v.top-v.bottom;return dv(dv({brushBottom:m},v),{},{width:Math.max(b,0),height:Math.max(g,0)})},dI=["layout","type","stroke","connectNulls","isRange","ref"],dB=["key"];function dR(t){return(dR="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dL(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function dz(){return(dz=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dU(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d$(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dU(Object(r),!0).forEach(function(e){dV(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dU(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dF(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dH(n.key),n)}}function dq(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(dq=function(){return!!t})()}function dW(t){return(dW=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dX(t,e){return(dX=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dV(t,e,r){return(e=dH(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dH(t){var e=function(t,e){if("object"!=dR(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dR(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dR(e)?e:e+""}var dG=function(t){var e,r;function n(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n);for(var t,e,r,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=n,r=[].concat(i),e=dW(e),dV(t=function(t,e){if(e&&("object"===dR(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,dq()?Reflect.construct(e,r||[],dW(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!0}),dV(t,"id",$("recharts-area-")),dV(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),Z()(e)&&e()}),dV(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),Z()(e)&&e()}),t}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&dX(t,e)}(n,t),e=[{key:"renderDots",value:function(t,e,r){var o=this.props.isAnimationActive,i=this.state.isAnimationFinished;if(o&&!i)return null;var a=this.props,c=a.dot,u=a.points,l=a.dataKey,s=tj(this.props,!1),f=tj(c,!0),p=u.map(function(t,e){var r=d$(d$(d$({key:"dot-".concat(e),r:3},s),f),{},{index:e,cx:t.x,cy:t.y,dataKey:l,value:t.value,payload:t.payload,points:u});return n.renderDotItem(c,r)}),d={clipPath:t?"url(#clipPath-".concat(e?"":"dots-").concat(r,")"):null};return S().createElement(tq,dz({className:"recharts-area-dots"},d),p)}},{key:"renderHorizontalRect",value:function(t){var e=this.props,r=e.baseLine,n=e.points,o=e.strokeWidth,i=n[0].x,a=n[n.length-1].x,c=t*Math.abs(i-a),u=c0()(n.map(function(t){return t.y||0}));return(L(r)&&"number"==typeof r?u=Math.max(r,u):r&&Array.isArray(r)&&r.length&&(u=Math.max(c0()(r.map(function(t){return t.y||0})),u)),L(u))?S().createElement("rect",{x:i<a?i:i-c,y:0,width:c,height:Math.floor(u+(o?parseInt("".concat(o),10):1))}):null}},{key:"renderVerticalRect",value:function(t){var e=this.props,r=e.baseLine,n=e.points,o=e.strokeWidth,i=n[0].y,a=n[n.length-1].y,c=t*Math.abs(i-a),u=c0()(n.map(function(t){return t.x||0}));return(L(r)&&"number"==typeof r?u=Math.max(r,u):r&&Array.isArray(r)&&r.length&&(u=Math.max(c0()(r.map(function(t){return t.x||0})),u)),L(u))?S().createElement("rect",{x:0,y:i<a?i:i-c,width:u+(o?parseInt("".concat(o),10):1),height:Math.floor(c)}):null}},{key:"renderClipRect",value:function(t){return"vertical"===this.props.layout?this.renderVerticalRect(t):this.renderHorizontalRect(t)}},{key:"renderAreaStatically",value:function(t,e,r,n){var o=this.props,i=o.layout,a=o.type,c=o.stroke,u=o.connectNulls,l=o.isRange,s=(o.ref,dL(o,dI));return S().createElement(tq,{clipPath:r?"url(#clipPath-".concat(n,")"):null},S().createElement(p1,dz({},tj(s,!0),{points:t,connectNulls:u,type:a,baseLine:e,layout:i,stroke:"none",className:"recharts-area-area"})),"none"!==c&&S().createElement(p1,dz({},tj(this.props,!1),{className:"recharts-area-curve",layout:i,type:a,connectNulls:u,fill:"none",points:t})),"none"!==c&&l&&S().createElement(p1,dz({},tj(this.props,!1),{className:"recharts-area-curve",layout:i,type:a,connectNulls:u,fill:"none",points:e})))}},{key:"renderAreaWithAnimation",value:function(t,e){var r=this,n=this.props,o=n.points,i=n.baseLine,a=n.isAnimationActive,c=n.animationBegin,u=n.animationDuration,l=n.animationEasing,s=n.animationId,f=this.state,p=f.prevPoints,d=f.prevBaseLine;return S().createElement(nv,{begin:c,duration:u,isActive:a,easing:l,from:{t:0},to:{t:1},key:"area-".concat(s),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(n){var a=n.t;if(p){var c,u=p.length/o.length,l=o.map(function(t,e){var r=Math.floor(e*u);if(p[r]){var n=p[r],o=X(n.x,t.x),i=X(n.y,t.y);return d$(d$({},t),{},{x:o(a),y:i(a)})}return t});return c=L(i)&&"number"==typeof i?X(d,i)(a):Y()(i)||T()(i)?X(d,0)(a):i.map(function(t,e){var r=Math.floor(e*u);if(d[r]){var n=d[r],o=X(n.x,t.x),i=X(n.y,t.y);return d$(d$({},t),{},{x:o(a),y:i(a)})}return t}),r.renderAreaStatically(l,c,t,e)}return S().createElement(tq,null,S().createElement("defs",null,S().createElement("clipPath",{id:"animationClipPath-".concat(e)},r.renderClipRect(a))),S().createElement(tq,{clipPath:"url(#animationClipPath-".concat(e,")")},r.renderAreaStatically(o,i,t,e)))})}},{key:"renderArea",value:function(t,e){var r=this.props,n=r.points,o=r.baseLine,i=r.isAnimationActive,a=this.state,c=a.prevPoints,u=a.prevBaseLine,l=a.totalLength;return i&&n&&n.length&&(!c&&l>0||!c6()(c,n)||!c6()(u,o))?this.renderAreaWithAnimation(t,e):this.renderAreaStatically(n,o,t,e)}},{key:"render",value:function(){var t,e=this.props,r=e.hide,n=e.dot,o=e.points,i=e.className,a=e.top,c=e.left,u=e.xAxis,l=e.yAxis,s=e.width,f=e.height,p=e.isAnimationActive,d=e.id;if(r||!o||!o.length)return null;var h=this.state.isAnimationFinished,y=1===o.length,v=(0,j.A)("recharts-area",i),m=u&&u.allowDataOverflow,b=l&&l.allowDataOverflow,g=m||b,x=Y()(d)?this.id:d,w=null!==(t=tj(n,!1))&&void 0!==t?t:{r:3,strokeWidth:2},O=w.r,A=w.strokeWidth,P=(n&&"object"===tp(n)&&"clipDot"in n?n:{}).clipDot,E=void 0===P||P,k=2*(void 0===O?3:O)+(void 0===A?2:A);return S().createElement(tq,{className:v},m||b?S().createElement("defs",null,S().createElement("clipPath",{id:"clipPath-".concat(x)},S().createElement("rect",{x:m?c:c-s/2,y:b?a:a-f/2,width:m?s:2*s,height:b?f:2*f})),!E&&S().createElement("clipPath",{id:"clipPath-dots-".concat(x)},S().createElement("rect",{x:c-k/2,y:a-k/2,width:s+k,height:f+k}))):null,y?null:this.renderArea(g,x),(n||y)&&this.renderDots(g,E,x),(!p||h)&&sS.renderCallByParent(this.props,o))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curPoints:t.points,curBaseLine:t.baseLine,prevPoints:e.curPoints,prevBaseLine:e.curBaseLine}:t.points!==e.curPoints||t.baseLine!==e.curBaseLine?{curPoints:t.points,curBaseLine:t.baseLine}:null}}],e&&dF(n.prototype,e),r&&dF(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(A.PureComponent);function dY(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],o=0;o<t.length;o+=e){if(void 0!==r&&!0!==r(t[o]))return;n.push(t[o])}return n}function dK(t,e,r,n,o){if(t*e<t*n||t*e>t*o)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-o)<=0}function dZ(t){return(dZ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dQ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dJ(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dQ(Object(r),!0).forEach(function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=dZ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dZ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dZ(e)?e:e+""}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dQ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}dV(dG,"displayName","Area"),dV(dG,"defaultProps",{stroke:"#3182bd",fill:"#3182bd",fillOpacity:.6,xAxisId:0,yAxisId:0,legendType:"line",connectNulls:!1,points:[],dot:!1,activeDot:!0,hide:!1,isAnimationActive:!ee.isSsr,animationBegin:0,animationDuration:1500,animationEasing:"ease"}),dV(dG,"getBaseValue",function(t,e,r,n){var o=t.layout,i=t.baseValue,a=e.props.baseValue,c=null!=a?a:i;if(L(c)&&"number"==typeof c)return c;var u="horizontal"===o?n:r,l=u.scale.domain();if("number"===u.type){var s=Math.max(l[0],l[1]),f=Math.min(l[0],l[1]);return"dataMin"===c?f:"dataMax"===c?s:s<0?s:Math.max(Math.min(l[0],l[1]),0)}return"dataMin"===c?l[0]:"dataMax"===c?l[1]:l[0]}),dV(dG,"getComposedData",function(t){var e,r=t.props,n=t.item,o=t.xAxis,i=t.yAxis,a=t.xAxisTicks,c=t.yAxisTicks,u=t.bandSize,l=t.dataKey,s=t.stackedData,f=t.dataStartIndex,p=t.displayedData,d=t.offset,h=r.layout,y=s&&s.length,v=dG.getBaseValue(r,n,o,i),m="horizontal"===h,b=!1,g=p.map(function(t,e){y?r=s[f+e]:Array.isArray(r=u9(t,l))?b=!0:r=[v,r];var r,n=null==r[1]||y&&null==u9(t,l);return m?{x:lw({axis:o,ticks:a,bandSize:u,entry:t,index:e}),y:n?null:i.scale(r[1]),value:r,payload:t}:{x:n?null:o.scale(r[1]),y:lw({axis:i,ticks:c,bandSize:u,entry:t,index:e}),value:r,payload:t}});return e=y||b?g.map(function(t){var e=Array.isArray(t.value)?t.value[0]:null;return m?{x:t.x,y:null!=e&&null!=t.y?i.scale(e):null}:{x:null!=e?o.scale(e):null,y:t.y}}):m?i.scale(v):o.scale(v),d$({points:g,baseLine:e,layout:h,isRange:b},d)}),dV(dG,"renderDotItem",function(t,e){var r;if(S().isValidElement(t))r=S().cloneElement(t,e);else if(Z()(t))r=t(e);else{var n=(0,j.A)("recharts-area-dot","boolean"!=typeof t?t.className:""),o=e.key,i=dL(e,dB);r=S().createElement(ra,dz({},i,{key:o,className:n}))}return r});var d0=["viewBox"],d1=["viewBox"],d2=["ticks"];function d4(t){return(d4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function d3(){return(d3=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function d5(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d6(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?d5(Object(r),!0).forEach(function(e){hr(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):d5(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function d8(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function d7(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hn(n.key),n)}}function d9(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(d9=function(){return!!t})()}function ht(t){return(ht=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function he(t,e){return(he=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hr(t,e,r){return(e=hn(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hn(t){var e=function(t,e){if("object"!=d4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=d4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==d4(e)?e:e+""}var ho=function(t){var e,r;function n(t){var e,r,o;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[t],r=ht(r),(e=function(t,e){if(e&&("object"===d4(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,d9()?Reflect.construct(r,o||[],ht(this).constructor):r.apply(this,o))).state={fontSize:"",letterSpacing:""},e}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&he(t,e)}(n,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=d8(t,d0),o=this.props,i=o.viewBox,a=d8(o,d1);return!te(r,i)||!te(n,a)||!te(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,o,i,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,d=c.tickSize,h=c.mirror,y=c.tickMargin,v=h?-1:1,m=t.tickSize||d,b=L(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(o=l+ +!h*f)-v*m)-v*y,i=b;break;case"left":n=o=t.coordinate,i=(e=(r=u+ +!h*s)-v*m)-v*y,a=b;break;case"right":n=o=t.coordinate,i=(e=(r=u+ +h*s)+v*m)+v*y,a=b;break;default:e=r=t.coordinate,a=(n=(o=l+ +h*f)+v*m)+v*y,i=b}return{line:{x1:e,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.orientation,a=t.mirror,c=t.axisLine,u=d6(d6(d6({},tj(this.props,!1)),tj(c,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var l=+("top"===i&&!a||"bottom"===i&&a);u=d6(d6({},u),{},{x1:e,y1:r+l*o,x2:e+n,y2:r+l*o})}else{var s=+("left"===i&&!a||"right"===i&&a);u=d6(d6({},u),{},{x1:e+s*n,y1:r,x2:e+s*n,y2:r+o})}return S().createElement("line",d3({},u,{className:(0,j.A)("recharts-cartesian-axis-line",C()(c,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var o=this,i=this.props,a=i.tickLine,c=i.stroke,u=i.tick,l=i.tickFormatter,s=i.unit,f=function(t,e,r){var n,o,i,a,c,u=t.tick,l=t.ticks,s=t.viewBox,f=t.minTickGap,p=t.orientation,d=t.interval,h=t.tickFormatter,y=t.unit,v=t.angle;if(!l||!l.length||!u)return[];if(L(d)||ee.isSsr)return dY(l,("number"==typeof d&&L(d)?d:0)+1);var m=[],b="top"===p||"bottom"===p?"width":"height",g=y&&"width"===b?nF(y,{fontSize:e,letterSpacing:r}):{width:0,height:0},x=function(t,n){var o,i=Z()(h)?h(t.value,n):t.value;return"width"===b?fA({width:(o=nF(i,{fontSize:e,letterSpacing:r})).width+g.width,height:o.height+g.height},v):nF(i,{fontSize:e,letterSpacing:r})[b]},w=l.length>=2?B(l[1].coordinate-l[0].coordinate):1,O=(n="width"===b,o=s.x,i=s.y,a=s.width,c=s.height,1===w?{start:n?o:i,end:n?o+a:i+c}:{start:n?o+a:i+c,end:n?o:i});return"equidistantPreserveStart"===d?function(t,e,r,n,o){for(var i,a=(n||[]).slice(),c=e.start,u=e.end,l=0,s=1,f=c;s<=a.length;)if(i=function(){var e,i=null==n?void 0:n[l];if(void 0===i)return{v:dY(n,s)};var a=l,p=function(){return void 0===e&&(e=r(i,a)),e},d=i.coordinate,h=0===l||dK(t,d,p,f,u);h||(l=0,f=c,s+=1),h&&(f=d+t*(p()/2+o),l+=s)}())return i.v;return[]}(w,O,x,l,f):("preserveStart"===d||"preserveStartEnd"===d?function(t,e,r,n,o,i){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(i){var s=n[c-1],f=r(s,c-1),p=t*(s.coordinate+t*f/2-l);a[c-1]=s=dJ(dJ({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate}),dK(t,s.tickCoord,function(){return f},u,l)&&(l=s.tickCoord-t*(f/2+o),a[c-1]=dJ(dJ({},s),{},{isShow:!0}))}for(var d=i?c-1:c,h=function(e){var n,i=a[e],c=function(){return void 0===n&&(n=r(i,e)),n};if(0===e){var s=t*(i.coordinate-t*c()/2-u);a[e]=i=dJ(dJ({},i),{},{tickCoord:s<0?i.coordinate-s*t:i.coordinate})}else a[e]=i=dJ(dJ({},i),{},{tickCoord:i.coordinate});dK(t,i.tickCoord,c,u,l)&&(u=i.tickCoord+t*(c()/2+o),a[e]=dJ(dJ({},i),{},{isShow:!0}))},y=0;y<d;y++)h(y);return a}(w,O,x,l,f,"preserveStartEnd"===d):function(t,e,r,n,o){for(var i=(n||[]).slice(),a=i.length,c=e.start,u=e.end,l=function(e){var n,l=i[e],s=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var f=t*(l.coordinate+t*s()/2-u);i[e]=l=dJ(dJ({},l),{},{tickCoord:f>0?l.coordinate-f*t:l.coordinate})}else i[e]=l=dJ(dJ({},l),{},{tickCoord:l.coordinate});dK(t,l.tickCoord,s,c,u)&&(u=l.tickCoord-t*(s()/2+o),i[e]=dJ(dJ({},l),{},{isShow:!0}))},s=a-1;s>=0;s--)l(s);return i}(w,O,x,l,f)).filter(function(t){return t.isShow})}(d6(d6({},this.props),{},{ticks:t}),e,r),p=this.getTickTextAnchor(),d=this.getTickVerticalAnchor(),h=tj(this.props,!1),y=tj(u,!1),v=d6(d6({},h),{},{fill:"none"},tj(a,!1)),m=f.map(function(t,e){var r=o.getTickLineCoord(t),i=r.line,m=r.tick,b=d6(d6(d6(d6({textAnchor:p,verticalAnchor:d},h),{},{stroke:"none",fill:c},y),m),{},{index:e,payload:t,visibleTicksCount:f.length,tickFormatter:l});return S().createElement(tq,d3({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},tu(o.props,t,e)),a&&S().createElement("line",d3({},v,i,{className:(0,j.A)("recharts-cartesian-axis-tick-line",C()(a,"className"))})),u&&n.renderTickItem(u,b,"".concat(Z()(l)?l(t.value,e):t.value).concat(s||"")))});return S().createElement("g",{className:"recharts-cartesian-axis-ticks"},m)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,n=e.width,o=e.height,i=e.ticksGenerator,a=e.className;if(e.hide)return null;var c=this.props,u=c.ticks,l=d8(c,d2),s=u;return(Z()(i)&&(s=i(u&&u.length>0?this.props:l)),n<=0||o<=0||!s||!s.length)?null:S().createElement(tq,{className:(0,j.A)("recharts-cartesian-axis",a),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(s,this.state.fontSize,this.state.letterSpacing),sa.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){var n;return S().isValidElement(t)?S().cloneElement(t,e):Z()(t)?t(e):S().createElement(oi,d3({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&d7(n.prototype,e),r&&d7(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(A.Component);function hi(t){return(hi="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}hr(ho,"displayName","CartesianAxis"),hr(ho,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});function ha(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ha=function(){return!!t})()}function hc(t){return(hc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hu(t,e){return(hu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hl(t,e,r){return(e=hs(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hs(t){var e=function(t,e){if("object"!=hi(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hi(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hi(e)?e:e+""}function hf(){return(hf=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hp(t){var e=t.xAxisId,r=fK(),n=fZ(),o=fG(e);return null==o?null:S().createElement(ho,hf({},o,{className:(0,j.A)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return ls(t,!0)}}))}var hd=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=hc(t),function(t,e){if(e&&("object"===hi(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,ha()?Reflect.construct(t,e||[],hc(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&hu(t,e)}(r,t),e=[{key:"render",value:function(){return S().createElement(hp,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hs(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(S().Component);function hh(t){return(hh="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}hl(hd,"displayName","XAxis"),hl(hd,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function hy(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hy=function(){return!!t})()}function hv(t){return(hv=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hm(t,e){return(hm=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hb(t,e,r){return(e=hg(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hg(t){var e=function(t,e){if("object"!=hh(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hh(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hh(e)?e:e+""}function hx(){return(hx=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var hw=function(t){var e=t.yAxisId,r=fK(),n=fZ(),o=fY(e);return null==o?null:S().createElement(ho,hx({},o,{className:(0,j.A)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return ls(t,!0)}}))},hO=function(t){var e;function r(){var t,e;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=hv(t),function(t,e){if(e&&("object"===hh(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,hy()?Reflect.construct(t,e||[],hv(this).constructor):t.apply(this,e))}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&hm(t,e)}(r,t),e=[{key:"render",value:function(){return S().createElement(hw,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hg(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(S().Component);hb(hO,"displayName","YAxis"),hb(hO,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var hj=function(t){var e=t.chartName,r=t.GraphicalChild,n=t.defaultTooltipEventType,o=void 0===n?"axis":n,i=t.validateTooltipEventTypes,a=void 0===i?["axis"]:i,c=t.axisComponents,u=t.legendContent,l=t.formatAxisMap,s=t.defaultProps,f=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,i=e.updateId,a=e.dataStartIndex,u=e.dataEndIndex,l=t.barSize,s=t.layout,f=t.barGap,p=t.barCategoryGap,d=t.maxBarSize,h=dC(s),y=h.numericAxisName,v=h.cateAxisName,m=!!r&&!!r.length&&r.some(function(t){var e=th(t&&t.type);return e&&e.indexOf("Bar")>=0}),b=[];return r.forEach(function(r,h){var g=dA(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:u}),x=void 0!==r.type.defaultProps?dv(dv({},r.type.defaultProps),r.props):r.props,w=x.dataKey,O=x.maxBarSize,j=x["".concat(y,"Id")],A=x["".concat(v,"Id")],S=c.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],o=x["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||tR(!1);var i=n[o];return dv(dv({},t),{},dm(dm({},r.axisType,i),"".concat(r.axisType,"Ticks"),ls(i)))},{}),P=S[v],E=S["".concat(v,"Ticks")],k=n&&n[j]&&n[j].hasStack&&lA(r,n[j].stackGroups),M=th(r.type).indexOf("Bar")>=0,_=lM(P,E),T=[],N=m&&ln({barSize:l,stackGroups:n,totalSize:"xAxis"===v?S[v].width:"yAxis"===v?S[v].height:void 0});if(M){var C,D,I=Y()(O)?d:O,B=null!==(C=null!==(D=lM(P,E,!0))&&void 0!==D?D:I)&&void 0!==C?C:0;T=lo({barGap:f,barCategoryGap:p,bandSize:B!==_?B:_,sizeList:N[A],maxBarSize:I}),B!==_&&(T=T.map(function(t){return dv(dv({},t),{},{position:dv(dv({},t.position),{},{offset:t.position.offset-B/2})})}))}var R=r&&r.type&&r.type.getComposedData;R&&b.push({props:dv(dv({},R(dv(dv({},S),{},{displayedData:g,props:t,dataKey:w,item:r,bandSize:_,barPosition:T,offset:o,stackedData:k,layout:s,dataStartIndex:a,dataEndIndex:u}))),{},dm(dm(dm({key:r.key||"item-".concat(h)},y,S[y]),v,S[v]),"animationId",i)),childIndex:tm(t.children).indexOf(r),item:r})}),b},p=function(t,n){var o=t.props,i=t.dataStartIndex,a=t.dataEndIndex,u=t.updateId;if(!tx({props:o}))return null;var s=o.children,p=o.layout,d=o.stackOffset,h=o.data,y=o.reverseStackOrder,v=dC(p),m=v.numericAxisName,b=v.cateAxisName,g=tb(s,r),x=lg(h,g,"".concat(m,"Id"),"".concat(b,"Id"),d,y),w=c.reduce(function(t,e){var r="".concat(e.axisType,"Map");return dv(dv({},t),{},dm({},r,d_(o,dv(dv({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&x,dataStartIndex:i,dataEndIndex:a}))))},{}),O=dD(dv(dv({},w),{},{props:o,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(w).forEach(function(t){w[t]=l(o,w[t],O,t.replace("Map",""),e)});var j=dT(w["".concat(b,"Map")]),A=f(o,dv(dv({},w),{},{dataStartIndex:i,dataEndIndex:a,updateId:u,graphicalItems:g,stackGroups:x,offset:O}));return dv(dv({formattedGraphicalItems:A,graphicalItems:g,offset:O,stackGroups:x},j),w)},d=function(t){var r;function n(t){var r,o,i,a,c;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),a=n,c=[t],a=ds(a),dm(i=function(t,e){if(e&&("object"===di(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,dl()?Reflect.construct(a,c||[],ds(this).constructor):a.apply(this,c)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),dm(i,"accessibilityManager",new pO),dm(i,"handleLegendBBoxUpdate",function(t){if(t){var e=i.state,r=e.dataStartIndex,n=e.dataEndIndex,o=e.updateId;i.setState(dv({legendBBox:t},p({props:i.props,dataStartIndex:r,dataEndIndex:n,updateId:o},dv(dv({},i.state),{},{legendBBox:t}))))}}),dm(i,"handleReceiveSyncEvent",function(t,e,r){i.props.syncId===t&&(r!==i.eventEmitterSymbol||"function"==typeof i.props.syncMethod)&&i.applySyncEvent(e)}),dm(i,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==i.state.dataStartIndex||r!==i.state.dataEndIndex){var n=i.state.updateId;i.setState(function(){return dv({dataStartIndex:e,dataEndIndex:r},p({props:i.props,dataStartIndex:e,dataEndIndex:r,updateId:n},i.state))}),i.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),dm(i,"handleMouseEnter",function(t){var e=i.getMouseInfo(t);if(e){var r=dv(dv({},e),{},{isTooltipActive:!0});i.setState(r),i.triggerSyncEvent(r);var n=i.props.onMouseEnter;Z()(n)&&n(r,t)}}),dm(i,"triggeredAfterMouseMove",function(t){var e=i.getMouseInfo(t),r=e?dv(dv({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};i.setState(r),i.triggerSyncEvent(r);var n=i.props.onMouseMove;Z()(n)&&n(r,t)}),dm(i,"handleItemMouseEnter",function(t){i.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),dm(i,"handleItemMouseLeave",function(){i.setState(function(){return{isTooltipActive:!1}})}),dm(i,"handleMouseMove",function(t){t.persist(),i.throttleTriggeredAfterMouseMove(t)}),dm(i,"handleMouseLeave",function(t){i.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};i.setState(e),i.triggerSyncEvent(e);var r=i.props.onMouseLeave;Z()(r)&&r(e,t)}),dm(i,"handleOuterEvent",function(t){var e,r,n=tE(t),o=C()(i.props,"".concat(n));n&&Z()(o)&&o(null!==(e=/.*touch.*/i.test(n)?i.getMouseInfo(t.changedTouches[0]):i.getMouseInfo(t))&&void 0!==e?e:{},t)}),dm(i,"handleClick",function(t){var e=i.getMouseInfo(t);if(e){var r=dv(dv({},e),{},{isTooltipActive:!0});i.setState(r),i.triggerSyncEvent(r);var n=i.props.onClick;Z()(n)&&n(r,t)}}),dm(i,"handleMouseDown",function(t){var e=i.props.onMouseDown;Z()(e)&&e(i.getMouseInfo(t),t)}),dm(i,"handleMouseUp",function(t){var e=i.props.onMouseUp;Z()(e)&&e(i.getMouseInfo(t),t)}),dm(i,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&i.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),dm(i,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&i.handleMouseDown(t.changedTouches[0])}),dm(i,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&i.handleMouseUp(t.changedTouches[0])}),dm(i,"handleDoubleClick",function(t){var e=i.props.onDoubleClick;Z()(e)&&e(i.getMouseInfo(t),t)}),dm(i,"handleContextMenu",function(t){var e=i.props.onContextMenu;Z()(e)&&e(i.getMouseInfo(t),t)}),dm(i,"triggerSyncEvent",function(t){void 0!==i.props.syncId&&pm.emit(pb,i.props.syncId,t,i.eventEmitterSymbol)}),dm(i,"applySyncEvent",function(t){var e=i.props,r=e.layout,n=e.syncMethod,o=i.state.updateId,a=t.dataStartIndex,c=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)i.setState(dv({dataStartIndex:a,dataEndIndex:c},p({props:i.props,dataStartIndex:a,dataEndIndex:c,updateId:o},i.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=i.state,d=f.offset,h=f.tooltipTicks;if(!d)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var y=0;y<h.length;y++)if(h[y].value===t.activeLabel){s=y;break}}var v=dv(dv({},d),{},{x:d.left,y:d.top}),m=Math.min(u,v.x+v.width),b=Math.min(l,v.y+v.height),g=h[s]&&h[s].value,x=dP(i.state,i.props.data,s),w=h[s]?{x:"horizontal"===r?h[s].coordinate:m,y:"horizontal"===r?b:h[s].coordinate}:dw;i.setState(dv(dv({},t),{},{activeLabel:g,activeCoordinate:w,activePayload:x,activeTooltipIndex:s}))}else i.setState(t)}),dm(i,"renderCursor",function(t){var r,n=i.state,o=n.isTooltipActive,a=n.activeCoordinate,c=n.activePayload,u=n.offset,l=n.activeTooltipIndex,s=n.tooltipAxisBandSize,f=i.getTooltipEventType(),p=null!==(r=t.props.active)&&void 0!==r?r:o,d=i.props.layout,h=t.key||"_recharts-cursor";return S().createElement(de,{key:h,activeCoordinate:a,activePayload:c,activeTooltipIndex:l,chartName:e,element:t,isActive:p,layout:d,offset:u,tooltipAxisBandSize:s,tooltipEventType:f})}),dm(i,"renderPolarAxis",function(t,e,r){var n=C()(t,"type.axisType"),o=C()(i.state,"".concat(n,"Map")),a=t.type.defaultProps,c=void 0!==a?dv(dv({},a),t.props):t.props,u=o&&o[c["".concat(n,"Id")]];return(0,A.cloneElement)(t,dv(dv({},u),{},{className:(0,j.A)(n,u.className),key:t.key||"".concat(e,"-").concat(r),ticks:ls(u,!0)}))}),dm(i,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,n=e.polarAngles,o=e.polarRadius,a=i.state,c=a.radiusAxisMap,u=a.angleAxisMap,l=q(c),s=q(u),f=s.cx,p=s.cy,d=s.innerRadius,h=s.outerRadius;return(0,A.cloneElement)(t,{polarAngles:Array.isArray(n)?n:ls(s,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(o)?o:ls(l,!0).map(function(t){return t.coordinate}),cx:f,cy:p,innerRadius:d,outerRadius:h,key:t.key||"polar-grid",radialLines:r})}),dm(i,"renderLegend",function(){var t=i.state.formattedGraphicalItems,e=i.props,r=e.children,n=e.width,o=e.height,a=i.props.margin||{},c=u2({children:r,formattedGraphicalItems:t,legendWidth:n-(a.left||0)-(a.right||0),legendContent:u});if(!c)return null;var l=c.item,s=du(c,dr);return(0,A.cloneElement)(l,dv(dv({},s),{},{chartWidth:n,chartHeight:o,margin:a,onBBoxUpdate:i.handleLegendBBoxUpdate}))}),dm(i,"renderTooltip",function(){var t,e=i.props,r=e.children,n=e.accessibilityLayer,o=tg(r,eh);if(!o)return null;var a=i.state,c=a.isTooltipActive,u=a.activeCoordinate,l=a.activePayload,s=a.activeLabel,f=a.offset,p=null!==(t=o.props.active)&&void 0!==t?t:c;return(0,A.cloneElement)(o,{viewBox:dv(dv({},f),{},{x:f.left,y:f.top}),active:p,label:s,payload:p?l:[],coordinate:u,accessibilityLayer:n})}),dm(i,"renderBrush",function(t){var e=i.props,r=e.margin,n=e.data,o=i.state,a=o.offset,c=o.dataStartIndex,u=o.dataEndIndex,l=o.updateId;return(0,A.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:lp(i.handleBrushChange,t.props.onChange),data:n,x:L(t.props.x)?t.props.x:a.left,y:L(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:L(t.props.width)?t.props.width:a.width,startIndex:c,endIndex:u,updateId:"brush-".concat(l)})}),dm(i,"renderReferenceElement",function(t,e,r){if(!t)return null;var n=i.clipPathId,o=i.state,a=o.xAxisMap,c=o.yAxisMap,u=o.offset,l=t.type.defaultProps||{},s=t.props,f=s.xAxisId,p=void 0===f?l.xAxisId:f,d=s.yAxisId,h=void 0===d?l.yAxisId:d;return(0,A.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[p],yAxis:c[h],viewBox:{x:u.left,y:u.top,width:u.width,height:u.height},clipPathId:n})}),dm(i,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,o=t.basePoint,i=t.childIndex,a=t.isRange,c=[],u=e.props.key,l=void 0!==e.item.type.defaultProps?dv(dv({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=dv(dv({index:i,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:lr(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},tj(s,!1)),tc(s));return c.push(n.renderActiveDot(s,f,"".concat(u,"-activePoint-").concat(i))),o?c.push(n.renderActiveDot(s,dv(dv({},f),{},{cx:o.x,cy:o.y}),"".concat(u,"-basePoint-").concat(i))):a&&c.push(null),c}),dm(i,"renderGraphicChild",function(t,e,r){var n=i.filterFormatItem(t,e,r);if(!n)return null;var o=i.getTooltipEventType(),a=i.state,c=a.isTooltipActive,u=a.tooltipAxis,l=a.activeTooltipIndex,s=a.activeLabel,f=tg(i.props.children,eh),p=n.props,d=p.points,h=p.isRange,y=p.baseLine,v=void 0!==n.item.type.defaultProps?dv(dv({},n.item.type.defaultProps),n.item.props):n.item.props,m=v.activeDot,b=v.hide,g=v.activeBar,x=v.activeShape,w=!!(!b&&c&&f&&(m||g||x)),O={};"axis"!==o&&f&&"click"===f.props.trigger?O={onClick:lp(i.handleItemMouseEnter,t.props.onClick)}:"axis"!==o&&(O={onMouseLeave:lp(i.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:lp(i.handleItemMouseEnter,t.props.onMouseEnter)});var j=(0,A.cloneElement)(t,dv(dv({},n.props),O));if(w){if(l>=0){if(u.dataKey&&!u.allowDuplicatedCategory){var S="function"==typeof u.dataKey?function(t){return"function"==typeof u.dataKey?u.dataKey(t.payload):null}:"payload.".concat(u.dataKey.toString());E=V(d,S,s),k=h&&y&&V(y,S,s)}else E=null==d?void 0:d[l],k=h&&y&&y[l];if(x||g){var P=void 0!==t.props.activeIndex?t.props.activeIndex:l;return[(0,A.cloneElement)(t,dv(dv(dv({},n.props),O),{},{activeIndex:P})),null,null]}if(!Y()(E))return[j].concat(dp(i.renderActivePoints({item:n,activePoint:E,basePoint:k,childIndex:l,isRange:h})))}else{var E,k,M,_=(null!==(M=i.getItemByXY(i.state.activeCoordinate))&&void 0!==M?M:{graphicalItem:j}).graphicalItem,T=_.item,N=void 0===T?t:T,C=_.childIndex,D=dv(dv(dv({},n.props),O),{},{activeIndex:C});return[(0,A.cloneElement)(N,D),null,null]}}return h?[j,null,null]:[j,null]}),dm(i,"renderCustomized",function(t,e,r){return(0,A.cloneElement)(t,dv(dv({key:"recharts-customized-".concat(r)},i.props),i.state))}),dm(i,"renderMap",{CartesianGrid:{handler:dO,once:!0},ReferenceArea:{handler:i.renderReferenceElement},ReferenceLine:{handler:dO},ReferenceDot:{handler:i.renderReferenceElement},XAxis:{handler:dO},YAxis:{handler:dO},Brush:{handler:i.renderBrush,once:!0},Bar:{handler:i.renderGraphicChild},Line:{handler:i.renderGraphicChild},Area:{handler:i.renderGraphicChild},Radar:{handler:i.renderGraphicChild},RadialBar:{handler:i.renderGraphicChild},Scatter:{handler:i.renderGraphicChild},Pie:{handler:i.renderGraphicChild},Funnel:{handler:i.renderGraphicChild},Tooltip:{handler:i.renderCursor,once:!0},PolarGrid:{handler:i.renderPolarGrid,once:!0},PolarAngleAxis:{handler:i.renderPolarAxis},PolarRadiusAxis:{handler:i.renderPolarAxis},Customized:{handler:i.renderCustomized}}),i.clipPathId="".concat(null!==(r=t.id)&&void 0!==r?r:$("recharts"),"-clip"),i.throttleTriggeredAfterMouseMove=E()(i.triggeredAfterMouseMove,null!==(o=t.throttleDelay)&&void 0!==o?o:1e3/60),i.state={},i}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&df(t,e)}(n,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=tg(e,eh);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=dP(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===o?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=dv(dv({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var d={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(d),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){tA([tg(t.children,eh)],[tg(this.props.children,eh)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=tg(this.props.children,eh);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return a.indexOf(e)>=0?e:o}return o}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},o={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},i=r.width/e.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var c=this.state,u=c.xAxisMap,l=c.yAxisMap;if("axis"!==this.getTooltipEventType()&&u&&l){var s=q(u).scale,f=q(l).scale,p=s&&s.invert?s.invert(o.chartX):null,d=f&&f.invert?f.invert(o.chartY):null;return dv(dv({},o),{},{xValue:p,yValue:d})}var h=dE(this.state,this.props.data,this.props.layout,a);return h?dv(dv({},o),h):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var c=this.state,u=c.angleAxisMap,l=c.radiusAxisMap;return u&&l?l5({x:o,y:i},q(u)):null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=tg(t,eh),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),dv(dv({},tc(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){pm.on(pb,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){pm.removeListener(pb,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===th(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,n=e.top,o=e.height,i=e.width;return S().createElement("defs",null,S().createElement("clipPath",{id:t},S().createElement("rect",{x:r,y:n,height:o,width:i})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=dc(e,2),n=r[0],o=r[1];return dv(dv({},t),{},dm({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=dc(e,2),n=r[0],o=r[1];return dv(dv({},t),{},dm({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],c=a.props,u=a.item,l=void 0!==u.type.defaultProps?dv(dv({},u.type.defaultProps),u.props):u.props,s=th(u.type);if("Bar"===s){var f=(c.data||[]).find(function(e){return nj(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(c.data||[]).find(function(e){return l5(t,e)});if(p)return{graphicalItem:a,payload:p}}else if(sJ(a,n)||s0(a,n)||s1(a,n)){var d=function(t){var e,r,n,o=t.activeTooltipItem,i=t.graphicalItem,a=t.itemData,c=(sJ(i,o)?e="trapezoids":s0(i,o)?e="sectors":s1(i,o)&&(e="points"),e),u=sJ(i,o)?null===(r=o.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:s0(i,o)?null===(n=o.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:s1(i,o)?o.payload:{},l=a.filter(function(t,e){var r=c6()(u,t),n=i.props[c].filter(function(t){var e;return(sJ(i,o)?e=s2:s0(i,o)?e=s4:s1(i,o)&&(e=s3),e)(t,o)}),a=i.props[c].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(l[l.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),h=void 0===l.activeIndex?d:l.activeIndex;return{graphicalItem:dv(dv({},a),{},{childIndex:h}),payload:s1(a,n)?l.data[d]:a.props.data[d]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!tx(this))return null;var n=this.props,o=n.children,i=n.className,a=n.width,c=n.height,u=n.style,l=n.compact,s=n.title,f=n.desc,p=tj(du(n,dn),!1);if(l)return S().createElement(fH,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},S().createElement(tU,da({},p,{width:a,height:c,title:s,desc:f}),this.renderClipPath(),tP(o,this.renderMap)));this.props.accessibilityLayer&&(p.tabIndex=null!==(t=this.props.tabIndex)&&void 0!==t?t:0,p.role=null!==(e=this.props.role)&&void 0!==e?e:"application",p.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},p.onFocus=function(){r.accessibilityManager.focus()});var d=this.parseEventsOfWrapper();return S().createElement(fH,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},S().createElement("div",da({className:(0,j.A)("recharts-wrapper",i),style:dv({position:"relative",cursor:"default",width:a,height:c},u)},d,{ref:function(t){r.container=t}}),S().createElement(tU,da({},p,{width:a,height:c,title:s,desc:f,style:dx}),this.renderClipPath(),tP(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,db(n.key),n)}}(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(A.Component);dm(d,"displayName",e),dm(d,"defaultProps",dv({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},s)),dm(d,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,o=t.children,i=t.width,a=t.height,c=t.layout,u=t.stackOffset,l=t.margin,s=e.dataStartIndex,f=e.dataEndIndex;if(void 0===e.updateId){var d=dN(t);return dv(dv(dv({},d),{},{updateId:0},p(dv(dv({props:t},d),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||i!==e.prevWidth||a!==e.prevHeight||c!==e.prevLayout||u!==e.prevStackOffset||!te(l,e.prevMargin)){var h=dN(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=dv(dv({},dE(e,n,c)),{},{updateId:e.updateId+1}),m=dv(dv(dv({},h),y),v);return dv(dv(dv({},m),p(dv({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:o})}if(!tA(o,e.prevChildren)){var b,g,x,w,O=tg(o,lK),j=O&&null!==(b=null===(g=O.props)||void 0===g?void 0:g.startIndex)&&void 0!==b?b:s,A=O&&null!==(x=null===(w=O.props)||void 0===w?void 0:w.endIndex)&&void 0!==x?x:f,S=Y()(n)||j!==s||A!==f?e.updateId+1:e.updateId;return dv(dv({updateId:S},p(dv(dv({props:t},e),{},{updateId:S,dataStartIndex:j,dataEndIndex:A}),e)),{},{prevChildren:o,dataStartIndex:j,dataEndIndex:A})}return null}),dm(d,"renderActiveDot",function(t,e,r){var n;return n=(0,A.isValidElement)(t)?(0,A.cloneElement)(t,e):Z()(t)?t(e):S().createElement(ra,e),S().createElement(tq,{className:"recharts-active-dot",key:r},n)});var h=(0,A.forwardRef)(function(t,e){return S().createElement(d,da({},t,{ref:e}))});return h.displayName=d.displayName,h}({chartName:"AreaChart",GraphicalChild:dG,axisComponents:[{axisType:"xAxis",AxisComp:hd},{axisType:"yAxis",AxisComp:hO}],formatAxisMap:function(t,e,r,n,o){var i=t.width,a=t.height,c=t.layout,u=t.children,l=Object.keys(e),s={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!tg(u,fh);return l.reduce(function(i,a){var u,l,p,d,h,y=e[a],v=y.orientation,m=y.domain,b=y.padding,g=void 0===b?{}:b,x=y.mirror,w=y.reversed,O="".concat(v).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var j=m[1]-m[0],A=1/0,S=y.categoricalDomain.sort();if(S.forEach(function(t,e){e>0&&(A=Math.min((t||0)-(S[e-1]||0),A))}),Number.isFinite(A)){var P=A/j,E="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(u=P*E/2),"no-gap"===y.padding){var k=F(t.barCategoryGap,P*E),M=P*E/2;u=M-k-(M-k)/E*k}}}l="xAxis"===n?[r.left+(g.left||0)+(u||0),r.left+r.width-(g.right||0)-(u||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(u||0),r.top+r.height-(g.bottom||0)-(u||0)]:y.range,w&&(l=[l[1],l[0]]);var _=ld(y,o,f),T=_.scale,N=_.realScaleType;T.domain(m).range(l),lh(T);var C=lx(T,fb(fb({},y),{},{realScaleType:N}));"xAxis"===n?(h="top"===v&&!x||"bottom"===v&&x,p=r.left,d=s[O]-h*y.height):"yAxis"===n&&(h="left"===v&&!x||"right"===v&&x,p=s[O]-h*y.width,d=r.top);var D=fb(fb(fb({},y),C),{},{realScaleType:N,x:p,y:d,scale:T,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return D.bandSize=lM(D,C),y.hide||"xAxis"!==n?y.hide||(s[O]+=(h?-1:1)*D.width):s[O]+=(h?-1:1)*D.height,fb(fb({},i),{},fg({},a,D))},{})}}),hA=r(76104),hS=r(23328),hP=r(35780),hE=r(47138);function hk(t,e){let r=(0,hE.a)(t),n=(0,hE.a)(e),o=r.getTime()-n.getTime();return o<0?-1:o>0?1:o}var hM=r(11392),h_=r(79186),hT=r(46127),hN=r(3211),hC=r(9903),hD=r(79943),hI=r(73437),hB=r(85814),hR=r.n(hB),hL=r(30474);let hz=function(){let[t,e]=(0,A.useState)(!0),[r,n]=(0,A.useState)(null),[f,j]=(0,A.useState)(null),[S,P]=(0,A.useState)("all"),[E,k]=(0,A.useState)("all"),[M,_]=(0,A.useState)(null),T=t=>{let e="string"==typeof t?parseInt(t):t,r=Math.floor(e/6e4),n=Math.floor(e%6e4/1e3);return`${r}:${n.toString().padStart(2,"0")}`},N=Array.from({length:10},(t,e)=>{let r=M?.callMetrics.connectionRate||0,n=r;return e<4?n=r-(13-e):e>8?n=r+(e-2):6===e&&(n=r+2),{day:e+1,value:Math.max(0,Math.min(100,n))}}),C=t=>{switch(t){case"customer-busy":return{icon:(0,o.jsx)(l.A,{className:"h-4 w-4"}),label:"Customer Busy"};case"customer-ended-call":return{icon:(0,o.jsx)(s.A,{className:"h-4 w-4"}),label:"Customer Ended The call"};case"assistant-ended-call":return{icon:(0,o.jsx)(p,{className:"h-4 w-4"}),label:"Agent Ended The call"};case"customer-did-not-answer":return{icon:(0,o.jsx)(s.A,{className:"h-4 w-4"}),label:"Customer Did not Answer"};case"customer-out-of-reach":return{icon:(0,o.jsx)(s.A,{className:"h-4 w-4"}),label:"Customer Out Of Reach"};case"voicemail":return{icon:(0,o.jsx)(d,{className:"h-4 w-4"}),label:"Voicemail"};case"silence-timed-out":return{icon:(0,o.jsx)(l.A,{className:"h-4 w-4"}),label:"Silence Timed Out"};default:return{icon:(0,o.jsx)(h,{className:"h-4 w-4"}),label:"Others (Timed out or failed to reach)"}}},D=Array.from({length:10},(t,e)=>{let r=M?.callMetrics.answerRate||0,n=r;return e<3?n=r-(10-e):e>7?n=r+(e-5):5===e&&(n=r+3),{day:e+1,value:Math.max(0,Math.min(100,n))}});return t?(0,o.jsxs)("div",{className:"flex flex-col items-center justify-center min-h-[60vh] space-y-4",children:[(0,o.jsx)("div",{className:"w-10 h-10 border-t-2 border-b-2 border-blue-500 rounded-full animate-spin"}),(0,o.jsx)("p",{className:"text-lg font-medium",children:"Loading..."})]}):r?(0,o.jsx)("div",{className:"flex flex-col items-center justify-center min-h-[60vh] space-y-4",children:(0,o.jsxs)("div",{className:"p-4 bg-red-50 text-red-500 rounded-lg",children:[(0,o.jsxs)("p",{children:["Error loading dashboard: ",r]}),(0,o.jsx)("button",{className:"mt-2 px-4 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200",onClick:()=>window.location.reload(),children:"Retry"})]})}):(0,o.jsx)(o.Fragment,{children:(0,o.jsxs)(hS.default,{direction:"up",children:[(0,o.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6",children:[(0,o.jsxs)("h1",{className:"text-2xl font-bold",children:["Dashboard ","all"!==E&&`(${E} Agents)`,"all"!==S&&` | Last ${S} days`]}),(0,o.jsx)("div",{className:"flex flex-col sm:flex-row gap-4 w-full md:w-auto",children:(0,o.jsxs)("div",{className:"flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 w-full",children:[(0,o.jsxs)(u.l6,{defaultValue:"30",value:S,onValueChange:P,children:[(0,o.jsx)(u.bq,{className:"h-9 w-full sm:w-[180px]",children:(0,o.jsx)(u.yv,{placeholder:"Last 30 days"})}),(0,o.jsxs)(u.gC,{children:[(0,o.jsx)(u.eb,{value:"all",children:"All"}),(0,o.jsx)(u.eb,{value:"7",children:"Last 7 days"}),(0,o.jsx)(u.eb,{value:"14",children:"Last 14 days"}),(0,o.jsx)(u.eb,{value:"30",children:"Last 30 days"}),(0,o.jsx)(u.eb,{value:"90",children:"Last 90 days"})]})]}),(0,o.jsxs)(u.l6,{defaultValue:"all",value:E,onValueChange:k,children:[(0,o.jsx)(u.bq,{className:"h-9 w-full sm:w-[180px]",children:(0,o.jsx)(u.yv,{placeholder:"All Agents",children:"all"===E?"All Agents":`${E} Agents`})}),(0,o.jsxs)(u.gC,{children:[(0,o.jsx)(u.eb,{value:"all",children:"All Agents"}),M?.agentRoles&&M.agentRoles.map(t=>(0,o.jsxs)(u.eb,{value:t,children:[t," Agents"]},t))]})]})]})})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 md:gap-6 mb-6",children:[(0,o.jsx)(i.Zp,{className:"border rounded-lg ",children:(0,o.jsx)(i.Wu,{className:"pt-6",children:(0,o.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total Calls"}),(0,o.jsx)("div",{className:"flex items-baseline",children:(0,o.jsx)("span",{className:"text-2xl font-bold mr-2",children:M?.callMetrics.totalCalls||0})})]}),(0,o.jsx)(y.A,{className:"h-5 w-5 text-gray-400"})]})})}),(0,o.jsx)(i.Zp,{className:"border rounded-lg ",children:(0,o.jsx)(i.Wu,{className:"pt-6",children:(0,o.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total Call in Minutes"}),(0,o.jsxs)("div",{className:"flex items-baseline",children:[(0,o.jsx)("span",{className:"text-2xl font-bold mr-1",children:M?.callMetrics.totalMinutes.toFixed(1)||0}),(0,o.jsx)("span",{className:"text-sm text-gray-500",children:"min"})]})]}),(0,o.jsx)(l.A,{className:"h-5 w-5 text-gray-400"})]})})}),(0,o.jsx)(i.Zp,{className:"border rounded-lg ",children:(0,o.jsx)(i.Wu,{className:"pt-6",children:(0,o.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Av. Call Length"}),(0,o.jsxs)("div",{className:"flex items-baseline",children:[(0,o.jsx)("span",{className:"text-2xl font-bold mr-1",children:(t=>{let e=Math.round(t/1e3),r=Math.floor(e/60);return`${r}m ${(e%60).toString().padStart(2,"0")}s`})(M?.callMetrics.averageLength||0)}),(0,o.jsx)("span",{className:"text-sm text-gray-500",children:"sec"})]})]}),(0,o.jsx)(l.A,{className:"h-5 w-5 text-gray-400"})]})})}),(0,o.jsx)(i.Zp,{className:"border rounded-lg ",children:(0,o.jsxs)(i.Wu,{className:"pt-6",children:[(0,o.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Call Engagement Rate"}),(0,o.jsx)("div",{className:"flex items-baseline",children:(0,o.jsxs)("span",{className:"text-2xl font-bold mr-1",children:[M?.callMetrics.connectionRate||0,"%"]})})]}),(0,o.jsx)(v.A,{className:"h-5 w-5 text-gray-400"})]}),(0,o.jsx)(hS.default,{delay:.3,direction:"right",children:(0,o.jsx)("div",{className:"h-12 mt-2",children:(0,o.jsx)(tN,{width:"100%",height:"100%",children:(0,o.jsx)(hj,{data:N,margin:{top:0,right:0,left:0,bottom:0},children:(0,o.jsx)(dG,{type:"monotone",dataKey:"value",stroke:"#4157ea",strokeWidth:2,fill:"#4157ea20"})})})})})]})})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6",children:[(0,o.jsx)(i.Zp,{className:"border rounded-lg",children:(0,o.jsxs)(i.Wu,{className:"pt-6",children:[(0,o.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total Campaigns"}),(0,o.jsx)(m,{className:"h-5 w-5 text-gray-400"})]}),(0,o.jsxs)("div",{className:"flex items-baseline",children:[(0,o.jsx)("span",{className:"text-2xl font-bold mr-2",children:M?.totalCounts.totalCampaigns||0}),(0,o.jsx)("span",{className:"text-lg text-gray-500",children:"campaigns"})]})]})}),(0,o.jsx)(i.Zp,{className:"border rounded-lg",children:(0,o.jsxs)(i.Wu,{className:"pt-6",children:[(0,o.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total Scheduled Calls"}),(0,o.jsx)(b.A,{className:"h-5 w-5 text-gray-400"})]}),(0,o.jsxs)("div",{className:"flex items-baseline",children:[(0,o.jsx)("span",{className:"text-2xl font-bold mr-2",children:M?.totalCounts.totalScheduledCalls||0}),(0,o.jsx)("span",{className:"text-lg text-gray-500",children:"scheduled"})]})]})}),(0,o.jsx)(i.Zp,{className:"border rounded-lg",children:(0,o.jsxs)(i.Wu,{className:"pt-6",children:[(0,o.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total Contacts"}),(0,o.jsx)(g,{className:"h-5 w-5 text-gray-400"})]}),(0,o.jsxs)("div",{className:"flex items-baseline",children:[(0,o.jsx)("div",{className:"text-2xl font-bold mr-2",children:M?.totalCounts.totalContacts||0}),(0,o.jsx)("span",{className:"text-lg text-gray-500",children:"contacts"})]})]})}),(0,o.jsx)(i.Zp,{className:"border rounded-lg",children:(0,o.jsxs)(i.Wu,{className:"pt-6",children:[(0,o.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("h3",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Answer Call Rate"}),(0,o.jsx)("div",{className:"flex items-baseline",children:(0,o.jsxs)("span",{className:"text-2xl font-bold mr-1",children:[M?.callMetrics.answerRate||0,"%"]})})]}),(0,o.jsx)(x,{className:"h-5 w-5 text-gray-400"})]}),(0,o.jsx)(hS.default,{delay:.3,direction:"right",children:(0,o.jsx)("div",{className:"h-10",children:(0,o.jsx)(tN,{width:"100%",height:"100%",children:(0,o.jsx)(hj,{data:D,margin:{top:0,right:0,left:0,bottom:0},children:(0,o.jsx)(dG,{type:"monotone",dataKey:"value",stroke:"#4157ea",strokeWidth:2,fill:"#4157ea20"})})})})})]})})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6 mb-6",children:[(0,o.jsx)(i.Zp,{className:"border rounded-lg",children:(0,o.jsxs)(i.Wu,{className:"pt-6",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,o.jsxs)("h3",{className:"text-sm font-medium",children:["Recent Calls ","all"!==E&&`(${E})`]}),(0,o.jsxs)(c.E,{variant:"outline",className:"bg-blue-50 text-blue-800 hover:bg-blue-50 border-none",children:[M?.callMetrics.totalCalls||0," calls"]})]}),(0,o.jsxs)("div",{className:"space-y-4",children:[M?.recentCalls.map(t=>{var e;return o.jsxs("div",{className:"flex items-center justify-between border-b pb-3",children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3",children:o.jsx(w.A,{className:"h-4 w-4 text-blue-600"})}),o.jsxs("div",{children:[o.jsxs("p",{className:"text-sm font-medium",children:[" ",t.fullName," | ",t.mobileNumber]}),o.jsx("p",{className:"text-xs text-gray-400",children:function(t,e,r){let n,o,i;let a=(0,hC.q)(),c=r?.locale??a.locale??hN.c,u=hk(t,e);if(isNaN(u))throw RangeError("Invalid time value");let l=Object.assign({},r,{addSuffix:r?.addSuffix,comparison:u});u>0?(n=(0,hE.a)(e),o=(0,hE.a)(t)):(n=(0,hE.a)(t),o=(0,hE.a)(e));let s=function(t,e,r){var n;return(n=void 0,t=>{let e=(n?Math[n]:Math.trunc)(t);return 0===e?0:e})((+(0,hE.a)(t)-+(0,hE.a)(e))/1e3)}(o,n),f=Math.round((s-((0,hD.G)(o)-(0,hD.G)(n))/1e3)/60);if(f<2){if(r?.includeSeconds){if(s<5)return c.formatDistance("lessThanXSeconds",5,l);if(s<10)return c.formatDistance("lessThanXSeconds",10,l);if(s<20)return c.formatDistance("lessThanXSeconds",20,l);else if(s<40)return c.formatDistance("halfAMinute",0,l);else if(s<60)return c.formatDistance("lessThanXMinutes",1,l);else return c.formatDistance("xMinutes",1,l)}return 0===f?c.formatDistance("lessThanXMinutes",1,l):c.formatDistance("xMinutes",f,l)}if(f<45)return c.formatDistance("xMinutes",f,l);if(f<90)return c.formatDistance("aboutXHours",1,l);if(f<hM.F6){let t=Math.round(f/60);return c.formatDistance("aboutXHours",t,l)}if(f<2520)return c.formatDistance("xDays",1,l);else if(f<hM.Nw){let t=Math.round(f/hM.F6);return c.formatDistance("xDays",t,l)}else if(f<2*hM.Nw)return i=Math.round(f/hM.Nw),c.formatDistance("aboutXMonths",i,l);if((i=function(t,e){let r;let n=(0,hE.a)(t),o=(0,hE.a)(e),i=hk(n,o),a=Math.abs((0,h_.U)(n,o));if(a<1)r=0;else{1===n.getMonth()&&n.getDate()>27&&n.setDate(30),n.setMonth(n.getMonth()-i*a);let e=hk(n,o)===-i;(function(t){let e=(0,hE.a)(t);return+function(t){let e=(0,hE.a)(t);return e.setHours(23,59,59,999),e}(e)==+(0,hT.p)(e)})((0,hE.a)(t))&&1===a&&1===hk(t,o)&&(e=!1),r=i*(a-Number(e))}return 0===r?0:r}(o,n))<12){let t=Math.round(f/hM.Nw);return c.formatDistance("xMonths",t,l)}{let t=i%12,e=Math.trunc(i/12);return t<3?c.formatDistance("aboutXYears",e,l):t<9?c.formatDistance("overXYears",e,l):c.formatDistance("almostXYears",e+1,l)}}(e=new Date(t.callStartTime),(0,hP.w)(e,Date.now()),{addSuffix:!0})})]})]}),o.jsxs("div",{className:"flex items-center",children:[o.jsx(v.A,{className:"h-3.5 w-3.5 text-blue-600 mr-2"}),o.jsx(c.E,{variant:"outline",className:"bg-blue-100 text-blue-800 hover:bg-blue-100 border-none mr-1",children:T(t.callDuration)})]})]},t._id)}),(0,o.jsx)(hR(),{href:"/history",children:(0,o.jsx)(a.$,{variant:"ghost",size:"sm",className:"w-full text-blue-600 hover:text-blue-700 hover:bg-blue-50",children:"View all calls"})})]})]})}),(0,o.jsx)(i.Zp,{className:"border rounded-lg",children:(0,o.jsxs)(i.Wu,{className:"pt-6",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,o.jsxs)("h3",{className:"text-sm font-medium",children:["Last Scheduled Cost ","all"!==E&&`(${E})`]}),(0,o.jsxs)(c.E,{variant:"outline",className:"bg-amber-50 text-amber-800 hover:bg-amber-50 border-none",children:[M?.recentSchedules.length||0," scheduled"]})]}),(0,o.jsxs)("div",{className:"space-y-4",children:[M?.recentSchedules.slice(0,3).slice(0,3).map(t=>o.jsxs("div",{className:"flex items-center justify-between border-b pb-3",children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"w-8 h-8 rounded-full bg-amber-100 flex items-center justify-center mr-3",children:o.jsx(b.A,{className:"h-4 w-4 text-amber-600"})}),o.jsxs("div",{children:[o.jsxs("p",{className:"text-sm font-medium",children:[t.contacts[0]?.Name||"N/A"," | ",t.contacts[0]?.MobileNumber||"N/A"]}),o.jsxs("p",{className:"text-xs text-gray-400",children:[hI.GP(new Date(t.scheduledTime),"MMM d, yyyy h:mm a")," • ",t.region||"N/A"]})]})]}),o.jsx(c.E,{variant:"outline",className:`
              ${"executed"===t.status?"bg-green-100 text-green-800 hover:bg-green-100":"pending"===t.status?"bg-amber-100 text-yellow-800 hover:bg-yellow-100":"bg-red-100 text-red-800 hover:bg-red-100"} border-none
            `,children:t.status||"pending"})]},t._id)),(0,o.jsx)(hR(),{href:"/schedule",children:(0,o.jsx)(a.$,{variant:"ghost",size:"sm",className:"w-full text-amber-600 hover:text-amber-700 hover:bg-amber-50",children:"View all scheduled calls"})})]})]})})]}),(0,o.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6",children:[(0,o.jsx)(i.Zp,{className:"border rounded-lg ",children:(0,o.jsxs)(i.Wu,{className:"pt-3",children:[(0,o.jsxs)("h3",{className:"text-sm font-medium mb-6",children:[" Sentiment Overview ","all"!==E&&`(${E})`," "]}),(0,o.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-center mt-9",children:[(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mx-auto mb-2",children:(0,o.jsx)("span",{className:"text-xl",children:"\uD83D\uDE0A"})}),(0,o.jsx)("h4",{className:"text-sm",children:"Positive"}),(0,o.jsxs)("p",{className:"text-xl font-bold",children:[M?.sentiments.positive||0,"%"]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mx-auto mb-2",children:(0,o.jsx)("span",{className:"text-xl",children:"\uD83D\uDE10"})}),(0,o.jsx)("h4",{className:"text-sm",children:"Neutral"}),(0,o.jsxs)("p",{className:"text-xl font-bold",children:[M?.sentiments.neutral||0,"%"]})]}),(0,o.jsxs)("div",{children:[(0,o.jsx)("div",{className:"w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mx-auto mb-2",children:(0,o.jsx)("span",{className:"text-xl",children:"\uD83D\uDE1E"})}),(0,o.jsx)("h4",{className:"text-sm",children:"Negative"}),(0,o.jsxs)("p",{className:"text-xl font-bold",children:[M?.sentiments.negative||0,"%"]})]})]})]})}),(0,o.jsx)(i.Zp,{className:"border rounded-lg",children:(0,o.jsxs)(i.Wu,{className:"pt-6",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,o.jsxs)("h3",{className:"text-sm font-medium",children:["  Recent Campaigns ","all"!==E&&`(${E})`," "]}),(0,o.jsxs)(c.E,{variant:"outline",className:"bg-green-50 text-green-800 hover:bg-green-50 border-none",children:[M?.recentCampaigns.length||0," campaigns"]})]}),(0,o.jsxs)("div",{className:"space-y-4",children:[M?.recentCampaigns.slice(0,3).map((t,e)=>o.jsxs("div",{className:"flex items-center justify-between border-b pb-3",children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3",children:o.jsx(O.A,{className:"h-4 w-4 text-green-600"})}),o.jsxs("div",{children:[o.jsx("p",{className:"text-sm font-medium",children:t.name}),o.jsxs("p",{className:"text-xs text-gray-400",children:[hI.GP(new Date(t.startDate),"MMM d, yyyy")," - ",t.endDate?hI.GP(new Date(t.endDate),"MMM d, yyyy"):"No End Date"]})]})]}),o.jsx(c.E,{variant:"outline",className:`
                  ${"active"===t.status?"bg-green-100 text-green-800 hover:bg-green-100":"paused"===t.status?"bg-amber-100 text-amber-800 hover:bg-amber-100":"bg-gray-100 text-gray-800 hover:bg-gray-100"} border-none
                `,children:"active"===t.status?"Active":"paused"===t.status?"Paused":"completed"===t.status?"Completed":"Inactive"})]},t._id||e)),(0,o.jsx)(hR(),{href:"/campaign",children:(0,o.jsx)(a.$,{variant:"ghost",size:"sm",className:"w-full text-green-600 hover:text-green-700 hover:bg-green-50",children:"View all campaigns"})})]})]})}),(0,o.jsx)(i.Zp,{className:"border rounded-lg",children:(0,o.jsxs)(i.Wu,{className:"pt-6",children:[(0,o.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,o.jsxs)("h3",{className:"text-sm font-medium",children:["Most Used Agents ","all"!==E&&`(${E})`]}),(0,o.jsx)(c.E,{variant:"outline",className:"bg-purple-50 text-purple-800 hover:bg-purple-50 border-none",children:"superadmin"===f?"all"===E?`${M?.topAgents.length} agents`:`${M?.topAgents.filter(t=>t.role===E).length} agents`:"all"===E?`${M?.topAgents.filter(t=>"active"===t.status).length} agents`:`${M?.topAgents.filter(t=>"active"===t.status&&t.role===E).length} agents`})]}),(0,o.jsxs)("div",{className:"space-y-4",children:[M?.topAgents.slice(0,5).map((t,e)=>o.jsxs("div",{className:"flex items-center justify-between border-b pb-3",children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3",children:t.avatar?o.jsx("img",{src:t.avatar,alt:`${t.name} avatar`,className:"h-full w-full rounded-full object-cover",onError:t=>{t.currentTarget.onerror=null,t.currentTarget.src=hA.A.src}}):o.jsx(hL.default,{src:hA.A,alt:`${t?.name} avatar`,width:64,height:64,className:"object-cover h-full w-full"})}),o.jsxs("div",{children:[o.jsx("p",{className:"text-sm font-medium",children:t?.name||"Agent"}),o.jsx("p",{className:"text-xs text-gray-400",children:t?.role||"Assistant"})]})]}),o.jsxs(c.E,{variant:"outline",className:"bg-purple-100 text-purple-800 hover:bg-purple-100 border-none",children:[t?.callCount||0," calls"]})]},t.id||e)),(0,o.jsx)(hR(),{href:"/agents",children:(0,o.jsx)(a.$,{variant:"ghost",size:"sm",className:"w-full text-purple-600 hover:text-purple-700 hover:bg-purple-50",children:"View all agents"})})]})]})}),(0,o.jsx)(i.Zp,{className:"border rounded-lg",children:(0,o.jsxs)(i.Wu,{className:"pt-6",children:[(0,o.jsxs)("h3",{className:"text-sm font-medium mb-4",children:["Call End Reasons ","all"!==E&&`(${E})`]}),(0,o.jsx)("div",{className:"space-y-4",children:M?.callEndReasons.slice(0,6).map((t,e)=>o.jsxs("div",{className:"flex items-center justify-between border-b pb-3",children:[o.jsxs("div",{className:"flex items-center",children:[o.jsx("div",{className:"w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3",children:C(t.reason).icon}),o.jsxs("div",{children:[o.jsx("p",{className:"text-sm font-medium",children:C(t.reason).label}),o.jsxs("p",{className:"text-xs text-gray-400",children:[String(t.count)," calls"]})]})]}),o.jsxs(c.E,{variant:"outline",className:"bg-blue-100 text-blue-800 hover:bg-blue-100 border-none",children:[t.percentage,"%"]})]},e))})]})})]})]})})}},48169:t=>{t.exports=function(t){return t}},48340:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},48385:t=>{var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+e+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",c="(?:"+r+"|"+n+")?",u="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[o,i,a].join("|")+")"+u+c+")*",s=RegExp(n+"(?="+n+")|"+("(?:"+[o+r+"?",r,i,a,"["+e+"]"].join("|"))+")"+(u+c+l),"g");t.exports=function(t){return t.match(s)||[]}},48730:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},49227:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==n(t)}},51449:(t,e,r)=>{var n=r(85745),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g;t.exports=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,function(t,r,n,o){e.push(n?o.replace(i,"$1"):r||t)}),e})},52477:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("PhoneCall",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}],["path",{d:"M14.05 2a9 9 0 0 1 8 7.94",key:"vmijpz"}],["path",{d:"M14.05 6A5 5 0 0 1 18 10",key:"13nbpp"}]])},52599:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}},52823:(t,e,r)=>{var n=r(85406),o=function(){var t=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();t.exports=function(t){return!!o&&o in t}},52931:(t,e,r)=>{var n=r(77834),o=r(89605),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return o(t);var e=[];for(var r in Object(t))i.call(t,r)&&"constructor"!=r&&e.push(r);return e}},54765:(t,e,r)=>{var n=r(67554),o=r(32269);t.exports=function(t,e){var r=-1,i=o(t)?Array(t.length):[];return n(t,function(t,n,o){i[++r]=e(t,n,o)}),i}},55048:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},55511:t=>{"use strict";t.exports=require("crypto")},55591:t=>{"use strict";t.exports=require("https")},56506:(t,e,r)=>{var n=r(32269);t.exports=function(t,e){return function(r,o){if(null==r)return r;if(!n(r))return t(r,o);for(var i=r.length,a=e?i:-1,c=Object(r);(e?a--:++a<i)&&!1!==o(c[a],a,c););return r}}},57202:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},57797:(t,e,r)=>{var n=r(67009);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return -1}},58141:(t,e,r)=>{t.exports=r(41547)(Object,"create")},58276:t=>{t.exports=function(t,e){return t.has(e)}},58744:(t,e,r)=>{var n=r(57797);t.exports=function(t,e){var r=this.__data__,o=n(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}},59467:(t,e,r)=>{var n=r(35142),o=r(35163),i=r(40542),a=r(38428),c=r(69619),u=r(46436);t.exports=function(t,e,r){e=n(e,t);for(var l=-1,s=e.length,f=!1;++l<s;){var p=u(e[l]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&c(s)&&a(p,s)&&(i(t)||o(t))}},59774:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},61320:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).has(t)}},61548:(t,e,r)=>{var n=r(5231),o=r(52823),i=r(55048),a=r(12290),c=/^\[object .+?Constructor\]$/,u=Object.prototype,l=Function.prototype.toString,s=u.hasOwnProperty,f=RegExp("^"+l.call(s).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(n(t)?f:c).test(a(t))}},61837:(t,e,r)=>{var n=r(21367),o=r(22),i=r(54765),a=r(40542);t.exports=function(t,e){return(a(t)?n:i)(t,o(e,3))}},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63866:(t,e,r)=>{var n=r(29395),o=r(40542),i=r(27467);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==n(t)}},63979:(t,e,r)=>{var n=r(52599),o=r(6330),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;t.exports=a?function(t){return null==t?[]:n(a(t=Object(t)),function(e){return i.call(t,e)})}:o},65662:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},65727:(t,e,r)=>{var n=r(81957);t.exports=function(t,e,r){for(var o=-1,i=t.criteria,a=e.criteria,c=i.length,u=r.length;++o<c;){var l=n(i[o],a[o]);if(l){if(o>=u)return l;return l*("desc"==r[o]?-1:1)}}return t.index-e.index}},65932:(t,e,r)=>{t.exports=r(65662)(Object.getPrototypeOf,Object)},65984:t=>{t.exports=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),c=a.length;c--;){var u=a[t?c:++o];if(!1===r(i[u],u,i))break}return e}}},66354:(t,e,r)=>{var n=r(85244),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,c=o(i.length-e,0),u=Array(c);++a<c;)u[a]=i[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=i[a];return l[e]=r(u),n(t,this,l)}}},66400:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var o=e(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},66713:(t,e,r)=>{var n=r(3105),o=r(34117),i=r(48385);t.exports=function(t){return o(t)?i(t):n(t)}},66837:(t,e,r)=>{var n=r(58141);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},66930:(t,e,r)=>{var n=r(27669),o=r(28837),i=r(94388),a=r(35800),c=r(58744);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},66992:(t,e)=>{"use strict";var r,n=Symbol.for("react.element"),o=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),d=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case i:case c:case a:case p:case d:return t;default:switch(t=t&&t.$$typeof){case s:case l:case f:case y:case h:case u:return t;default:return e}}case o:return e}}}(t)===i}},67009:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},67200:(t,e,r)=>{var n=r(66930),o=r(37575),i=r(75411),a=r(34746),c=r(25118),u=r(30854);function l(t){var e=this.__data__=new n(t);this.size=e.size}l.prototype.clear=o,l.prototype.delete=i,l.prototype.get=a,l.prototype.has=c,l.prototype.set=u,t.exports=l},67367:(t,e,r)=>{var n=r(99525),o=r(22),i=r(75847),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),u(t,o(e,3))}},67554:(t,e,r)=>{var n=r(99114);t.exports=r(56506)(n)},67619:(t,e,r)=>{var n=r(40542),o=r(49227),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!!("number"==r||"symbol"==r||"boolean"==r||null==t||o(t))||a.test(t)||!i.test(t)||null!=e&&t in Object(e)}},69433:(t,e,r)=>{t.exports=r(5566)("toUpperCase")},69619:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=0x1fffffffffffff}},69691:(t,e,r)=>{var n=r(41157),o=r(99114),i=r(22);t.exports=function(t,e){var r={};return e=i(e,3),o(t,function(t,o,i){n(r,o,e(t,o,i))}),r}},70120:(t,e,r)=>{"use strict";r.r(e),r.d(e,{GlobalError:()=>a.a,__next_app__:()=>f,pages:()=>s,routeModule:()=>p,tree:()=>l});var n=r(65239),o=r(48088),i=r(88170),a=r.n(i),c=r(30893),u={};for(let t in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(u[t]=()=>c[t]);r.d(e,u);let l={children:["",{children:["(workspace)",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,90732)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,50184)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,s=["C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\dashboard\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(workspace)/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},70151:(t,e,r)=>{var n=r(85718);t.exports=function(){return n.Date.now()}},70222:(t,e,r)=>{var n=r(79474),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,c=n?n.toStringTag:void 0;t.exports=function(t){var e=i.call(t,c),r=t[c];try{t[c]=void 0;var n=!0}catch(t){}var o=a.call(t);return n&&(e?t[c]=r:delete t[c]),o}},71960:t=>{t.exports=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1}},71967:(t,e,r)=>{var n=r(15871);t.exports=function(t,e){return n(t,e)}},74075:t=>{"use strict";t.exports=require("zlib")},74610:t=>{t.exports=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return -1}},75254:(t,e,r)=>{var n=r(78418),o=r(93311),i=r(41132);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},75411:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},75847:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r;return n(t,function(t,n,o){return!(r=e(t,n,o))}),!!r}},76104:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n={src:"/_next/static/media/Binghatti-Lisa.85c81ecb.jpeg",height:1586,width:1586,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/2wBDAQoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/wgARCAAIAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAX/xAAUAQEAAAAAAAAAAAAAAAAAAAAC/9oADAMBAAIQAxAAAACeA//EABsQAAEFAQEAAAAAAAAAAAAAAAECAwQREwAi/9oACAEBAAE/AG6e3khuoyZAbQNDWYR6SO//xAAVEQEBAAAAAAAAAAAAAAAAAAABAP/aAAgBAgEBPwAL/8QAFhEAAwAAAAAAAAAAAAAAAAAAAAFB/9oACAEDAQE/AHD/2Q==",blurWidth:8,blurHeight:8}},77822:(t,e,r)=>{var n=r(93490);t.exports=function(t){return n(t)&&t!=+t}},77834:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},78418:(t,e,r)=>{var n=r(67200),o=r(15871);t.exports=function(t,e,r,i){var a=r.length,c=a,u=!i;if(null==t)return!c;for(t=Object(t);a--;){var l=r[a];if(u&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<c;){var s=(l=r[a])[0],f=t[s],p=l[1];if(u&&l[2]){if(void 0===f&&!(s in t))return!1}else{var d=new n;if(i)var h=i(f,p,s,t,e,d);if(!(void 0===h?o(p,f,3,i,d):h))return!1}}return!0}},79428:t=>{"use strict";t.exports=require("buffer")},79474:(t,e,r)=>{t.exports=r(85718).Symbol},79551:t=>{"use strict";t.exports=require("url")},79646:t=>{"use strict";t.exports=require("child_process")},80195:(t,e,r)=>{var n=r(79474),o=r(21367),i=r(40542),a=r(49227),c=1/0,u=n?n.prototype:void 0,l=u?u.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(a(e))return l?l.call(e):"";var r=e+"";return"0"==r&&1/e==-c?"-0":r}},80329:(t,e,r)=>{t=r.nmd(t);var n=r(85718),o=r(1944),i=e&&!e.nodeType&&e,a=i&&t&&!t.nodeType&&t,c=a&&a.exports===i?n.Buffer:void 0,u=c?c.isBuffer:void 0;t.exports=u||o},80458:(t,e,r)=>{var n=r(29395),o=r(69619),i=r(27467),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!a[n(t)]}},80704:(t,e,r)=>{var n=r(96678);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},81488:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},81630:t=>{"use strict";t.exports=require("http")},81957:(t,e,r)=>{var n=r(49227);t.exports=function(t,e){if(t!==e){var r=void 0!==t,o=null===t,i=t==t,a=n(t),c=void 0!==e,u=null===e,l=e==e,s=n(e);if(!u&&!s&&!a&&t>e||a&&c&&l&&!u&&!s||o&&c&&l||!r&&l||!i)return 1;if(!o&&!a&&!s&&t<e||s&&r&&i&&!o&&!a||u&&r&&i||!c&&i||!l)return -1}return 0}},82038:(t,e,r)=>{var n=r(57202),o=r(35163),i=r(40542),a=r(80329),c=r(38428),u=r(10090),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=i(t),s=!r&&o(t),f=!r&&!s&&a(t),p=!r&&!s&&!f&&u(t),d=r||s||f||p,h=d?n(t.length,String):[],y=h.length;for(var v in t)(e||l.call(t,v))&&!(d&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,y)))&&h.push(v);return h}},83997:t=>{"use strict";t.exports=require("tty")},84031:(t,e,r)=>{"use strict";var n=r(34452);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,r,o,i,a){if(a!==n){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},84261:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=+!!e,e}},84482:(t,e,r)=>{var n=r(28977);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},84713:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},85244:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},85406:(t,e,r)=>{t.exports=r(85718)["__core-js_shared__"]},85450:(t,e,r)=>{var n=r(79474),o=r(35163),i=r(40542),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},85718:(t,e,r)=>{var n=r(10663),o="object"==typeof self&&self&&self.Object===Object&&self;t.exports=n||o||Function("return this")()},85745:(t,e,r)=>{var n=r(86451);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},85938:(t,e,r)=>{var n=r(42205),o=r(17518),i=r(46229),a=r(7383);t.exports=i(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,n(e,1),[])})},86451:(t,e,r)=>{var n=r(95746);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,t.exports=o},87270:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},87321:(t,e,r)=>{var n=r(98798),o=r(7383),i=r(28977);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&o(e,r,a)&&(r=a=void 0),e=i(e),void 0===r?(r=e,e=0):r=i(r),a=void 0===a?e<r?1:-1:i(a),n(e,r,a,t)}}},87506:(t,e,r)=>{var n=r(66837),o=r(84261),i=r(89492),a=r(90200),c=r(39672);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},87955:(t,e,r)=>{t.exports=r(84031)()},89167:(t,e,r)=>{t.exports=r(41547)(r(85718),"DataView")},89185:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},89492:(t,e,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(e,t)?e[t]:void 0}},89605:(t,e,r)=>{t.exports=r(65662)(Object.keys,Object)},89624:t=>{t.exports=function(t){return function(e){return t(e)}}},90200:(t,e,r)=>{var n=r(58141),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:o.call(e,t)}},90453:(t,e,r)=>{var n=r(2984),o=r(99180),i=r(48169);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},90732:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>a});var n=r(37413),o=r(33806),i=r(28086);function a(){return(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(o.default,{direction:"up",delay:.1,children:(0,n.jsx)(i.default,{})})})}},90851:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},91290:t=>{t.exports=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return -1}},91645:t=>{"use strict";t.exports=require("net")},91928:(t,e,r)=>{var n=r(41547);t.exports=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}()},92662:(t,e,r)=>{var n=r(46328),o=r(80704),i=r(71960),a=r(58276),c=r(95308),u=r(2408);t.exports=function(t,e,r){var l=-1,s=o,f=t.length,p=!0,d=[],h=d;if(r)p=!1,s=i;else if(f>=200){var y=e?null:c(t);if(y)return u(y);p=!1,s=a,h=new n}else h=e?[]:d;e:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=h.length;b--;)if(h[b]===m)continue e;e&&h.push(m),d.push(v)}else s(h,m,r)||(h!==d&&h.push(m),d.push(v))}return d}},93311:(t,e,r)=>{var n=r(34883),o=r(7651);t.exports=function(t){for(var e=o(t),r=e.length;r--;){var i=e[r],a=t[i];e[r]=[i,a,n(a)]}return e}},93490:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return"number"==typeof t||o(t)&&"[object Number]"==n(t)}},93780:(t,e,r)=>{"use strict";t.exports=r(66992)},94388:(t,e,r)=>{var n=r(57797);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},94735:t=>{"use strict";t.exports=require("events")},95308:(t,e,r)=>{var n=r(34772),o=r(36959),i=r(2408);t.exports=n&&1/i(new n([,-0]))[1]==1/0?function(t){return new n(t)}:o},95746:(t,e,r)=>{var n=r(15909),o=r(29205),i=r(29508),a=r(61320),c=r(19976);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=o,u.prototype.get=i,u.prototype.has=a,u.prototype.set=c,t.exports=u},96678:(t,e,r)=>{var n=r(91290),o=r(39774),i=r(74610);t.exports=function(t,e,r){return e==e?i(t,e,r):n(t,o,r)}},96834:(t,e,r)=>{"use strict";r.d(e,{E:()=>u});var n=r(60687);r(43210);var o=r(8730),i=r(24224),a=r(4780);let c=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function u({className:t,variant:e,asChild:r=!1,...i}){let u=r?o.DX:"span";return(0,n.jsx)(u,{"data-slot":"badge",className:(0,a.cn)(c({variant:e}),t),...i})}},98451:(t,e,r)=>{var n=r(29395),o=r(27467);t.exports=function(t){return!0===t||!1===t||o(t)&&"[object Boolean]"==n(t)}},98798:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,o,i){for(var a=-1,c=r(e((n-t)/(o||1)),0),u=Array(c);c--;)u[i?c:++a]=t,t+=o;return u}},99114:(t,e,r)=>{var n=r(12344),o=r(7651);t.exports=function(t,e){return t&&n(t,e,o)}},99180:t=>{t.exports=function(t,e){return t>e}},99525:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}}};var e=require("../../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),n=e.X(0,[287,9176,7674,5814,598,5188,6034,2766,1476,4772],()=>r(70120));module.exports=n})();