(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6483],{30285:(e,t,n)=>{"use strict";n.d(t,{$:()=>d,r:()=>i});var r=n(95155);n(12115);var s=n(99708),a=n(74466),l=n(59434);let i=(0,a.F)("inline-flex items-center cursor-pointer justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:n,size:a,asChild:d=!1,...o}=e,u=d?s.DX:"button";return(0,r.jsx)(u,{"data-slot":"button",className:(0,l.cn)(i({variant:n,size:a,className:t})),...o})}},35695:(e,t,n)=>{"use strict";var r=n(18999);n.o(r,"useParams")&&n.d(t,{useParams:function(){return r.useParams}}),n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}}),n.o(r,"useRouter")&&n.d(t,{useRouter:function(){return r.useRouter}})},50675:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(19946).A)("PhoneCall",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}],["path",{d:"M14.05 2a9 9 0 0 1 8 7.94",key:"vmijpz"}],["path",{d:"M14.05 6A5 5 0 0 1 18 10",key:"13nbpp"}]])},55346:(e,t,n)=>{"use strict";n.d(t,{default:()=>o});var r=n(95155),s=n(12115),a=n(30285),l=n(85127),i=n(50675),d=n(35695);function o(){let e=(0,d.useRouter)(),[t,n]=(0,s.useState)([]),o=()=>{e.push("/phonenumber/buy")};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-4",children:[(0,r.jsx)("h1",{className:"text-2xl font-semibold",children:"Phone Numbers"}),(0,r.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,r.jsx)(a.$,{variant:"outline",size:"sm",children:"Manage"}),(0,r.jsx)(a.$,{size:"sm",className:" transition-all duration-150 hover:scale-105",onClick:o,children:"Buy a Number"}),(0,r.jsx)(a.$,{variant:"outline",size:"sm",children:"Regulatory Bundles"})]})]}),(0,r.jsx)("div",{className:"bg-card rounded-lg border shadow-sm overflow-hidden mb-8",children:(0,r.jsxs)(l.XI,{children:[(0,r.jsx)(l.A0,{children:(0,r.jsxs)(l.Hj,{children:[(0,r.jsx)(l.nd,{children:"Phone Number"}),(0,r.jsx)(l.nd,{children:"Outbound "}),(0,r.jsx)(l.nd,{children:"Inbound "}),(0,r.jsx)(l.nd,{children:"Inbound Automation"}),(0,r.jsx)(l.nd,{children:"SMS Enabled"}),(0,r.jsx)(l.nd,{children:"Date Purchased"})]})}),(0,r.jsx)(l.BF,{children:t.length>0?t.map(e=>(0,r.jsxs)(l.Hj,{children:[(0,r.jsx)(l.nA,{className:"font-medium",children:e.number}),(0,r.jsx)(l.nA,{children:e.outboundThoughtly||"-"}),(0,r.jsx)(l.nA,{children:e.inboundThoughtly||"-"}),(0,r.jsx)(l.nA,{children:e.inboundAutomation||"-"}),(0,r.jsx)(l.nA,{children:e.smsEnabled?"Yes":"No"}),(0,r.jsx)(l.nA,{children:e.datePurchased.toLocaleDateString()})]},e.id)):(0,r.jsx)(l.Hj,{children:(0,r.jsx)(l.nA,{colSpan:6,className:"h-80 text-center",children:(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center",children:[(0,r.jsx)("div",{className:"h-16 w-16 bg-muted rounded-full flex items-center justify-center mb-4",children:(0,r.jsx)(i.A,{className:"h-8 w-8 text-muted-foreground"})}),(0,r.jsx)("p",{className:"text-lg font-medium mb-4",children:"You don't have any phone numbers yet"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground max-w-md text-center mb-8",children:"Buy a phone number to get started connecting your Thoughtlys for inbound and outbound calling."}),(0,r.jsx)(a.$,{className:" transition-all duration-150 hover:scale-105",onClick:o,children:"Buy a Number"})]})})})})]})})]})}},59434:(e,t,n)=>{"use strict";n.d(t,{cn:()=>a,v:()=>l});var r=n(52596),s=n(39688);function a(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,s.QP)((0,r.$)(t))}function l(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}},74466:(e,t,n)=>{"use strict";n.d(t,{F:()=>l});var r=n(52596);let s=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=r.$,l=(e,t)=>n=>{var r;if((null==t?void 0:t.variants)==null)return a(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:l,defaultVariants:i}=t,d=Object.keys(l).map(e=>{let t=null==n?void 0:n[e],r=null==i?void 0:i[e];if(null===t)return null;let a=s(t)||s(r);return l[e][a]}),o=n&&Object.entries(n).reduce((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e},{});return a(e,d,null==t?void 0:null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce((e,t)=>{let{class:n,className:r,...s}=t;return Object.entries(s).every(e=>{let[t,n]=e;return Array.isArray(n)?n.includes({...i,...o}[t]):({...i,...o})[t]===n})?[...e,n,r]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},85127:(e,t,n)=>{"use strict";n.d(t,{A0:()=>l,BF:()=>i,Hj:()=>d,XI:()=>a,nA:()=>u,nd:()=>o});var r=n(95155);n(12115);var s=n(59434);function a(e){let{className:t,...n}=e;return(0,r.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,r.jsx)("table",{"data-slot":"table",className:(0,s.cn)("w-full caption-bottom text-sm",t),...n})})}function l(e){let{className:t,...n}=e;return(0,r.jsx)("thead",{"data-slot":"table-header",className:(0,s.cn)("[&_tr]:border-b",t),...n})}function i(e){let{className:t,...n}=e;return(0,r.jsx)("tbody",{"data-slot":"table-body",className:(0,s.cn)("[&_tr:last-child]:border-0",t),...n})}function d(e){let{className:t,...n}=e;return(0,r.jsx)("tr",{"data-slot":"table-row",className:(0,s.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",t),...n})}function o(e){let{className:t,...n}=e;return(0,r.jsx)("th",{"data-slot":"table-head",className:(0,s.cn)("text-muted-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...n})}function u(e){let{className:t,...n}=e;return(0,r.jsx)("td",{"data-slot":"table-cell",className:(0,s.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",t),...n})}},97011:(e,t,n)=>{Promise.resolve().then(n.bind(n,55346))}},e=>{var t=t=>e(e.s=t);e.O(0,[4201,8441,1684,7358],()=>t(97011)),_N_E=e.O()}]);