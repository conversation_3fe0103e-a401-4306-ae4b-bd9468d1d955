(()=>{var e={};e.id=4025,e.ids=[4025],e.modules={709:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,47429,23)),Promise.resolve().then(t.bind(t,6931)),Promise.resolve().then(t.bind(t,46678)),Promise.resolve().then(t.bind(t,96988))},3169:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>p});var s=t(60687),n=t(43210),o=t(85814),a=t.n(o),i=t(44493),d=t(27900),l=t(28559),c=t(29523),u=t(89667);function p(){let[e,r]=(0,n.useState)(!1),[t,o]=(0,n.useState)(""),[p,m]=(0,n.useState)(!1),v=async e=>{e.preventDefault(),r(!0),setTimeout(()=>{r(!1),m(!0)},1500)};return(0,s.jsx)(s.Fragment,{children:(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)(i.Zp,{className:"w-full max-w-md bg-white dark:bg-gray-800 shadow-xl rounded-xl",children:[(0,s.jsxs)(i.aR,{className:"space-y-2",children:[(0,s.jsx)(i.ZB,{className:"text-center text-3xl font-bold bg-gradient-to-r from-[#383D73] to-[#74546D] bg-clip-text text-transparent",children:"Reset Password"}),(0,s.jsx)("p",{className:"text-center text-gray-500 dark:text-gray-400",children:p?"Check your email for a reset link":"Enter your email and we'll send you a link to reset your password"})]}),(0,s.jsx)(i.Wu,{children:p?(0,s.jsxs)("div",{className:"text-center py-6 space-y-4",children:[(0,s.jsxs)("div",{className:"bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 p-4 rounded-md",children:[(0,s.jsx)("p",{children:"We have sent a password reset link to:"}),(0,s.jsx)("p",{className:"font-medium",children:t}),(0,s.jsx)("p",{className:"text-sm mt-2",children:"Please check your inbox and follow the instructions."})]}),(0,s.jsx)(c.$,{className:"mt-4 bg-transparent text-[#383D73] dark:text-[#BE8FB8] hover:bg-gray-100 dark:hover:bg-gray-700",variant:"ghost",onClick:()=>{o(""),m(!1)},children:"Try another email"})]}):(0,s.jsxs)("form",{onSubmit:v,className:"space-y-6",children:[(0,s.jsx)("div",{className:"space-y-2",children:(0,s.jsx)(u.p,{type:"email",placeholder:"Email address",value:t,onChange:e=>o(e.target.value),required:!0,className:"h-12"})}),(0,s.jsx)(c.$,{type:"submit",className:"w-full h-12 bg-gradient-to-r from-[#383D73] to-[#74546D] hover:from-[#312E56] hover:to-[#BE8FB8] text-white font-semibold transition-all duration-200 hover:scale-[1.02] hover:shadow-md",disabled:e,children:e?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2"}),"Sending..."]}):(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[(0,s.jsx)(d.A,{className:"mr-2 h-5 w-5"}),"Send Reset Link"]})})]})}),(0,s.jsx)(i.wL,{className:"flex justify-center border-t border-gray-100 dark:border-gray-700 pt-4",children:(0,s.jsx)(a(),{href:"/login",children:(0,s.jsxs)(c.$,{variant:"ghost",className:"text-[#383D73] dark:text-[#BE8FB8] hover:bg-transparent hover:text-[#74546D] transition-all duration-200",children:[(0,s.jsx)(l.A,{className:"mr-2 h-4 w-4"}),"Back to Sign In"]})})})]})})})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4780:(e,r,t)=>{"use strict";t.d(r,{cn:()=>o,v:()=>a});var s=t(49384),n=t(82348);function o(...e){return(0,n.QP)((0,s.$)(e))}function a(e){return new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2,maximumFractionDigits:2}).format(e)}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11557:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,16444,23)),Promise.resolve().then(t.t.bind(t,16042,23)),Promise.resolve().then(t.t.bind(t,88170,23)),Promise.resolve().then(t.t.bind(t,49477,23)),Promise.resolve().then(t.t.bind(t,29345,23)),Promise.resolve().then(t.t.bind(t,12089,23)),Promise.resolve().then(t.t.bind(t,46577,23)),Promise.resolve().then(t.t.bind(t,31307,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23776:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>o});var s=t(60687),n=t(10218);function o({children:e,...r}){return(0,s.jsx)(n.N,{...r,enableSystem:!0,attribute:"class",defaultTheme:"system",disableTransitionOnChange:!1,children:e})}},24709:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,86346,23)),Promise.resolve().then(t.t.bind(t,27924,23)),Promise.resolve().then(t.t.bind(t,35656,23)),Promise.resolve().then(t.t.bind(t,40099,23)),Promise.resolve().then(t.t.bind(t,38243,23)),Promise.resolve().then(t.t.bind(t,28827,23)),Promise.resolve().then(t.t.bind(t,62763,23)),Promise.resolve().then(t.t.bind(t,97173,23))},24993:(e,r,t)=>{Promise.resolve().then(t.bind(t,72647))},26674:(e,r,t)=>{"use strict";t.d(r,{default:()=>i});var s=t(60687),n=t(39091),o=t(8693),a=t(43210);function i({children:e}){let[r]=(0,a.useState)(()=>new n.E);return(0,s.jsx)(o.Ht,{client:r,children:e})}},27900:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},28559:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(62688).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29523:(e,r,t)=>{"use strict";t.d(r,{$:()=>d,r:()=>i});var s=t(60687);t(43210);var n=t(8730),o=t(24224),a=t(4780);let i=(0,o.F)("inline-flex items-center cursor-pointer justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:r,size:t,asChild:o=!1,...d}){let l=o?n.DX:"button";return(0,s.jsx)(l,{"data-slot":"button",className:(0,a.cn)(i({variant:r,size:t,className:e})),...d})}},33873:e=>{"use strict";e.exports=require("path")},34094:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=t(65239),n=t(48088),o=t(88170),a=t.n(o),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(r,d);let l={children:["",{children:["(auth)",{children:["forgotpassword",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,72647)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(auth)\\forgotpassword\\page.tsx"]}]},{}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(auth)\\forgotpassword\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(auth)/forgotpassword/page",pathname:"/forgotpassword",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},38145:(e,r,t)=>{Promise.resolve().then(t.bind(t,3169))},44493:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>l,ZB:()=>i,Zp:()=>o,aR:()=>a,wL:()=>c});var s=t(60687);t(43210);var n=t(4780);function o({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",e),...r})}function a({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("flex flex-col gap-1.5 px-6",e),...r})}function i({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",e),...r})}function d({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...r})}function l({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",e),...r})}function c({className:e,...r}){return(0,s.jsx)("div",{"data-slot":"card-footer",className:(0,n.cn)("flex items-center px-6",e),...r})}},46678:(e,r,t)=>{"use strict";t.d(r,{ThemeProvider:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\theme\\ThemeProvider.tsx","ThemeProvider")},61135:()=>{},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72647:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\app\\\\(auth)\\\\forgotpassword\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(auth)\\forgotpassword\\page.tsx","default")},89667:(e,r,t)=>{"use strict";t.d(r,{p:()=>o});var s=t(60687);t(43210);var n=t(4780);function o({className:e,type:r,...t}){return(0,s.jsx)("input",{type:r,"data-slot":"input",className:(0,n.cn)("border-input file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),...t})}},94431:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m,metadata:()=>p});var s=t(37413),n=t(22376),o=t.n(n),a=t(68726),i=t.n(a);t(61135);var d=t(46678),l=t(6931),c=t(36162),u=t(96988);let p={title:"Orova AI",description:"Create, customize, and deploy AI agents effortlessly.",icons:{icon:"./favicon.png"}};function m({children:e}){return(0,s.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,s.jsxs)("head",{children:[(0,s.jsx)(c.default,{id:"microsoft-clarity",strategy:"afterInteractive",children:`
            (function(c,l,a,r,i,t,y){
              c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
              t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
              y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "${process.env.NEXT_PUBLIC_CLARITY_ID}");
          `}),(0,s.jsx)(c.default,{src:`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`,strategy:"afterInteractive"}),(0,s.jsx)(c.default,{id:"google-analytics",strategy:"afterInteractive",children:`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}');
          `})]}),(0,s.jsx)("body",{className:`${o().variable} ${i().variable} antialiased`,suppressHydrationWarning:!0,children:(0,s.jsx)(u.default,{children:(0,s.jsxs)(d.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:[(0,s.jsx)(l.Toaster,{position:"top-right",closeButton:!0,richColors:!0}),e]})})})]})}},94629:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,79167,23)),Promise.resolve().then(t.bind(t,52581)),Promise.resolve().then(t.bind(t,23776)),Promise.resolve().then(t.bind(t,26674))},96988:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\lib\\\\providers\\\\ReactQueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\lib\\providers\\ReactQueryProvider.tsx","default")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[287,9176,5814],()=>t(34094));module.exports=s})();