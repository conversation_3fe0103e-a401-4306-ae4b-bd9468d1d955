exports.id=4772,exports.ids=[4772],exports.modules={6607:(e,t,r)=>{"use strict";r.d(t,{t:()=>s});var a=r(28863);async function s(e,t={}){let r=localStorage.getItem("access_token");if(!r){let e=await (0,a.J1)();if(!e.success)throw Error("No authentication token available");r=e.newAccessToken}let n=new Headers(t.headers||{});n.has("Authorization")||n.set("Authorization",`Bearer ${r}`);let i=await fetch(e,{...t,headers:n});if(401===i.status||403===i.status){console.log("Token expired, attempting refresh...");let r=await (0,a.J1)();if(!r.success)throw console.error("Token refresh failed"),Error("Authentication failed");console.log("Token refreshed, retrying request...");let s=new Headers(t.headers||{});return s.set("Authorization",`Bearer ${r.newAccessToken}`),fetch(e,{...t,headers:s})}return i}},21342:(e,t,r)=>{"use strict";r.d(t,{I:()=>c,SQ:()=>l,_2:()=>d,lp:()=>u,mB:()=>m,rI:()=>i,ty:()=>o});var a=r(60687);r(43210);var s=r(26312),n=r(4780);function i({...e}){return(0,a.jsx)(s.bL,{"data-slot":"dropdown-menu",...e})}function o({...e}){return(0,a.jsx)(s.l9,{"data-slot":"dropdown-menu-trigger",...e})}function l({className:e,sideOffset:t=4,...r}){return(0,a.jsx)(s.ZL,{children:(0,a.jsx)(s.UC,{"data-slot":"dropdown-menu-content",sideOffset:t,className:(0,n.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-md",e),...r})})}function c({...e}){return(0,a.jsx)(s.YJ,{"data-slot":"dropdown-menu-group",...e})}function d({className:e,inset:t,variant:r="default",...i}){return(0,a.jsx)(s.q7,{"data-slot":"dropdown-menu-item","data-inset":t,"data-variant":r,className:(0,n.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...i})}function u({className:e,inset:t,...r}){return(0,a.jsx)(s.JU,{"data-slot":"dropdown-menu-label","data-inset":t,className:(0,n.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",e),...r})}function m({className:e,...t}){return(0,a.jsx)(s.wv,{"data-slot":"dropdown-menu-separator",className:(0,n.cn)("bg-border -mx-1 my-1 h-px",e),...t})}},23328:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var a=r(60687),s=r(82854);function n({children:e,duration:t=.5,delay:r=0,direction:n="up",distance:i=30,className:o="",once:l=!0,viewOffset:c=.1}){let d=0,u=0;return"up"===n&&(d=i),"down"===n&&(d=-i),"left"===n&&(u=i),"right"===n&&(u=-i),(0,a.jsx)(s.P.div,{initial:{y:d,x:u,opacity:0},whileInView:{y:0,x:0,opacity:1},transition:{duration:t,delay:r,ease:"easeOut"},viewport:{once:l,amount:c},className:o,children:e})}},24258:(e,t,r)=>{"use strict";r.d(t,{LB:()=>c,TK:()=>o,hG:()=>l,hU:()=>s,lo:()=>n,mP:()=>u,nu:()=>i,s2:()=>d});let a="http://localhost:4000";async function s(){try{let e=localStorage.getItem("access_token");if(!e)throw console.error("No access token available"),Error("No access token available");let t=await fetch(`${a}/api/users`,{headers:{Authorization:`Bearer ${e}`}});if(!t.ok)throw Error("Failed to fetch users");return await t.json()}catch(e){throw console.error("Error fetching users:",e),e}}let n=async()=>{let e=localStorage.getItem("access_token");if(!e)throw Error("No access token available");let t=await fetch(`${a}/api/users`,{headers:{Authorization:`Bearer ${e}`}});if(!t.ok)throw Error((await t.json()).message||"Failed to fetch users");return t.json()};async function i(e){try{if(!localStorage.getItem("access_token"))throw console.error("No access token available"),Error("No access token available");let t=await fetch(`${a}/api/users/register`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.text();throw Error(e||"Failed to add user")}return}catch(e){throw console.error("Error adding user:",e),e}}async function o(e,t){try{let r=localStorage.getItem("access_token");if(!r)throw console.error("No access token available"),Error("No access token available");let s=await fetch(`${a}/api/users/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`},body:JSON.stringify(t)});if(!s.ok)throw Error("Failed to update user");return await s.json()}catch(e){throw console.error("Error updating user:",e),e}}async function l(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");if(!(await fetch(`${a}/api/users/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${t}`}})).ok)throw Error("Failed to delete user")}catch(e){throw console.error("Error deleting user:",e),e}}async function c(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");let r=await fetch(`${a}/api/users/approve/${e}`,{method:"PATCH",headers:{Authorization:`Bearer ${t}`}});if(!r.ok)throw Error("Failed to approve user");return await r.json()}catch(e){throw console.error("Error approving user:",e),e}}async function d(e){try{let t=localStorage.getItem("access_token");if(!t)throw console.error("No access token available"),Error("No access token available");let r=await fetch(`${a}/api/users/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json",Authorization:`Bearer ${t}`},body:JSON.stringify({isApproved:!1})});if(!r.ok)throw Error("Failed to revoke user access");return await r.json()}catch(e){throw console.error("Error revoking user access:",e),e}}async function u(){try{let e=localStorage.getItem("access_token");if(!e)throw console.error("No access token available"),Error("No access token available");let t=await fetch(`${a}/api/users/me/credits`,{headers:{Authorization:`Bearer ${e}`}});if(!t.ok)throw Error("Failed to fetch user credits");let r=await t.json();return{credits:r.credits||0,minutes:r.minutes||0,freeCreditsRemaining:r.freeCreditsRemaining||0,paidCredits:r.paidCredits||0,totalAvailable:r.totalAvailable||0,usingFreeCredits:r.usingFreeCredits||!1,freeMinutesRemaining:r.freeMinutesRemaining||0,paidMinutes:r.paidMinutes||0,totalMinutesAvailable:r.totalMinutesAvailable||0,callPricePerMinute:r.callPricePerMinute||.1,monthlyResetDate:r.monthlyResetDate||1,monthlyAllowance:r.monthlyAllowance||0,minimumCreditsThreshold:r.minimumCreditsThreshold||1}}catch(e){return console.error("Error fetching user credits:",e),{credits:0,minutes:0,freeCreditsRemaining:0,paidCredits:0,totalAvailable:0,usingFreeCredits:!1,freeMinutesRemaining:0,paidMinutes:0,totalMinutesAvailable:0,callPricePerMinute:.1,monthlyResetDate:1,monthlyAllowance:0,minimumCreditsThreshold:1}}}},26882:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});let a={src:"/_next/static/media/OROVA-WHITE.76096952.png",height:124,width:732,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAABCAMAAADU3h9xAAAAD1BMVEX////Aucj7+/vp5e38/PzbXIb5AAAABXRSTlNs0Yl2dMCoCVsAAAAJcEhZcwAACxMAAAsTAQCanBgAAAARSURBVHicY2BkZmBiYWBgAAAAQAALSpjpiwAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:1}},28863:(e,t,r)=>{"use strict";r.d(t,{HW:()=>n,J1:()=>o,_f:()=>i});var a=r(6607);let s="http://localhost:4000";async function n(){try{let e=await (0,a.t)(`${s}/api/auth/me`,{method:"GET"});if(!e.ok)return{success:!1,error:`Error: ${e.status}`};let t=await e.json(),r=t.userId||t._id||t.id,n=t.email;if(r&&n)return{success:!0,user:{fullName:t.fullName||n.split("@")[0],userId:r,email:n,role:t.role||"user"}};return{success:!1,error:"Invalid user data received"}}catch(e){return console.error("Error fetching user data:",e),{success:!1,error:"An error occurred while fetching user data"}}}function i(e=10){return()=>{}}async function o(){let e=localStorage.getItem("refresh_token");if(!e)return{success:!1};try{let t=await fetch(`${s}/api/auth/refresh`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refreshToken:e})});if(!t.ok)return{success:!1};let r=await t.json();if(r.access_token)return localStorage.setItem("access_token",r.access_token),{success:!0,newAccessToken:r.access_token};return{success:!1}}catch(e){return console.error("Token refresh error:",e),{success:!1}}}},32584:(e,t,r)=>{"use strict";r.d(t,{BK:()=>o,eu:()=>i,q5:()=>l});var a=r(60687);r(43210);var s=r(92951),n=r(4780);function i({className:e,...t}){return(0,a.jsx)(s.bL,{"data-slot":"avatar",className:(0,n.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",e),...t})}function o({className:e,...t}){return(0,a.jsx)(s._V,{"data-slot":"avatar-image",className:(0,n.cn)("aspect-square size-full",e),...t})}function l({className:e,...t}){return(0,a.jsx)(s.H4,{"data-slot":"avatar-fallback",className:(0,n.cn)("bg-muted flex size-full items-center justify-center rounded-full",e),...t})}},39727:()=>{},47990:()=>{},50184:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\app\\\\(workspace)\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\(workspace)\\layout.tsx","default")},61055:(e,t,r)=>{Promise.resolve().then(r.bind(r,50184))},72796:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>L});var a=r(60687),s=r(43210),n=r(85814),i=r.n(n),o=r(30474),l=r(16189),c=r(10218),d=r(49625),u=r(83753),m=r(20835),h=r(98015),g=r(40228),f=r(78200),x=r(58887),p=r(84027),v=r(41312),b=r(35583),y=r(17313),w=r(41862),A=r(21134),k=r(363);let j={src:"/_next/static/media/OROVA-PURPLE.2cb479d5.png",height:118,width:724,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAABCAMAAADU3h9xAAAACVBMVEUyL1drYoAyLlUe8VZxAAAAA3RSTlNyzJlDn8tCAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAEUlEQVR4nGNgZGBgYmBgYAAAABsABEKIhOgAAAAASUVORK5CYII=",blurWidth:8,blurHeight:1};var N=r(26882),C=r(32584),S=r(29523),$=r(21342),E=r(58869),M=r(40083),B=r(6475);let R=(0,B.createServerReference)("00bd6ed5ddcea8d6a33b0b0c1e7ed159ce451d0d84",B.callServer,void 0,B.findSourceMapURL,"logoutUser");function I({user:e}){let[t,r]=(0,s.useState)(!1),n=(0,l.useRouter)(),o=async()=>{try{r(!0);let e=await R();localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user_data"),e.success||console.error("Logout failed:",e.message),n.push("/login")}catch(e){console.error("Error during logout:",e),localStorage.removeItem("access_token"),localStorage.removeItem("refresh_token"),localStorage.removeItem("user_data"),n.push("/login")}finally{r(!1)}};return(0,a.jsxs)($.rI,{children:[(0,a.jsx)($.ty,{asChild:!0,children:(0,a.jsx)(S.$,{variant:"ghost",className:"relative h-9 w-9 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700",children:(0,a.jsxs)(C.eu,{className:"h-9 w-9",children:[(0,a.jsx)(C.BK,{src:e.avatar,alt:e.name}),(0,a.jsxs)(C.q5,{children:[e.name[0].toUpperCase()," "]})]})})}),(0,a.jsxs)($.SQ,{className:"w-56 z-600",align:"end",forceMount:!0,children:[(0,a.jsx)($.lp,{className:"font-normal",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium leading-none dark:text-gray-100",children:e.fullName}),(0,a.jsx)("p",{className:"text-xs leading-none text-gray-500 dark:text-gray-400",children:e.email})]})}),(0,a.jsx)($.mB,{}),(0,a.jsxs)($.I,{children:[(0,a.jsx)(i(),{href:"/profile",children:(0,a.jsxs)($._2,{className:"cursor-pointer",children:[(0,a.jsx)(E.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Profile"})]})}),(0,a.jsx)(i(),{href:"/settings",children:(0,a.jsxs)($._2,{className:"cursor-pointer",children:[(0,a.jsx)(p.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Settings"})]})})]}),(0,a.jsx)($.mB,{}),(0,a.jsxs)($._2,{className:"cursor-pointer text-red-600 focus:text-red-600",onClick:o,disabled:t,children:[(0,a.jsx)(M.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:t?"Logging out...":"Logout"})]})]})]})}var P=r(23328);r(6607),r(28863);var _=r(88740),T=r(75256);function U(){let{totalMinutesAvailable:e,monthlyAllowance:t,isLoading:r,totalAvailable:s,organizationCreditThreshold:n,isConnected:o,hasValidData:l,lastSuccessfulFetch:c}=(0,T.I)(),d=l&&s<n,u=l&&s<2*n&&s>=n,m=c&&Date.now()-c>12e4;return(0,a.jsx)(_.default,{delay:.2,children:(0,a.jsx)("div",{className:"mx-1 my-3 transition-all duration-800 ease-in-out delay-1000",children:(0,a.jsxs)("div",{className:`bg-white dark:bg-gray-800 border rounded-lg p-4 transform transition-all duration-500 ease-in-out ${d?"border-red-300 dark:border-red-700 bg-red-50 dark:bg-red-900/10":u?"border-yellow-300 dark:border-yellow-700 bg-white dark:bg-yellow-900/10":"border-gray-200 dark:border-gray-700"}`,children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-2",children:[(0,a.jsx)("h3",{className:"text-xs font-medium",children:"Credits"}),(!o||m)&&(0,a.jsx)("div",{className:"w-2 h-2 rounded-full bg-orange-400 animate-pulse",title:"Connection issue - data may be outdated"})]}),(d||u)&&(0,a.jsx)("p",{className:` text-xs font-medium ${d?"text-red-500":"text-yellow-500"}`,children:d?"Low Credits":"Warning"})]}),(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,a.jsx)("span",{className:`text-xs ${d?"text-red-600 dark:text-red-400":u?"text-yellow-600 dark:text-yellow-400":"text-green-600 dark:text-green-400"}`,children:"Minutes Remaining"}),(0,a.jsx)("span",{className:`text-sm font-semibold ${d?"text-red-600 dark:text-red-400":u?"text-yellow-600 dark:text-yellow-400":"text-green-600 dark:text-green-400"}`,children:r?"...":`${e.toFixed(0)} min`})]}),(0,a.jsx)("div",{className:"h-1.5 bg-gray-100 dark:bg-gray-700 rounded-full overflow-hidden",children:(0,a.jsx)("div",{className:`h-full rounded-full ${d?"bg-red-500":u?"bg-yellow-500":"bg-green-500"}`,style:{width:`${Math.min(100,e/Math.max(t,1)*100)}%`}})})]}),(0,a.jsx)(i(),{href:"/billing",className:"block",children:(0,a.jsx)(S.$,{variant:"default",className:"w-full bg-black hover:bg-gray-800 dark:bg-gray-900 dark:hover:bg-gray-800 text-white text-xs py-1 h-8",children:"Recharge"})})]})})})}function L({children:e}){let{theme:t,setTheme:r}=(0,c.D)(),[n,C]=(0,s.useState)(!1),[S,$]=(0,s.useState)(!1),E=(0,l.usePathname)();(0,l.useRouter)();let[M,B]=(0,s.useState)(!0),[R,L]=(0,s.useState)(null),[z,O]=(0,s.useState)(!0),F=()=>{window.innerWidth<768&&B(!1)},D=R?{fullName:R.fullName||R.email.split("@")[0],name:R.email.split("@")[0],email:R.email,avatar:"",role:R.role}:{fullName:"Loading...",name:"Loading...",email:"",avatar:"",role:""},V=[{title:"Overview",links:[{name:"Dashboard",href:"/dashboard",icon:d.A},{name:"Agents",href:"/agents",icon:u.A},{name:"Campaigns",href:"/campaign",icon:m.A},{name:"History",href:"/history",icon:h.A},{name:"Schedule",href:"/schedule",icon:g.A}]},{title:"Resources",links:[{name:"Brain",href:"/brain",icon:f.A},{name:"Contacts",href:"/contacts",icon:x.A}]}],J={title:"Configuration",links:[{name:"Settings",href:"/settings",icon:p.A,isParent:!0,subLinks:[{name:"General",href:"/settings",icon:p.A},{name:"Users",href:"/users",icon:v.A},{name:"Billing",href:"/billing",icon:b.A},...R?.role==="superadmin"?[{name:"Workspaces",href:"/workspaces",icon:y.A}]:[]]}]};return!n||z?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsx)(w.A,{className:"h-12 w-12 animate-spin text-primary"}),(0,a.jsx)("p",{className:"text-lg font-medium",children:"Loading..."})]})}):(0,a.jsx)(T.n,{creditThreshold:1,children:(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsxs)("div",{className:`fixed inset-y-0 left-0 z-550 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 flex flex-col transition-all duration-500 ease-in-out ${M?"w-64 translate-x-0":"w-16 md:translate-x-0 -translate-x-full"}`,children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"flex items-center justify-start h-16 px-4 border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("div",{className:`flex items-center  ${M?"gap-6 ml-3":"justify-center w-full"}`,children:(0,a.jsxs)(i(),{href:"/",children:[M&&(0,a.jsx)(_.default,{delay:.2,children:(0,a.jsx)(o.default,{src:"dark"===t?N.A:j,alt:"Orova AI",className:"h-5 w-auto"})}),!M&&(0,a.jsx)(_.default,{delay:.1,children:(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-purple-600 flex items-center justify-center text-white font-bold",children:"O"})})]})})})}),(0,a.jsx)("div",{className:"flex-grow overflow-hidden",children:(0,a.jsx)("nav",{className:`px-4 py-4 space-y-6 ${!M&&"px-2"}`,children:V.map((e,t)=>(0,a.jsxs)("div",{children:[M&&(0,a.jsx)("h3",{className:"text-xs uppercase tracking-wider text-gray-500 dark:text-gray-400 font-semibold mb-2 px-3",children:e.title}),(0,a.jsx)("div",{className:"space-y-1",children:e.links.map(e=>{let t=e.icon,r=E.includes(e.href);return(0,a.jsxs)(i(),{href:e.href,onClick:F,className:`flex items-center ${M?"px-3":"px-0 justify-center"} py-2 text-sm font-medium rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 gap-3 mb-1 text-[#192c54] dark:text-white transition-all duration-200  ${r?"bg-gray-100 dark:bg-gray-700":""}`,title:M?"":e.name,children:[(0,a.jsx)(t,{className:"h-5 w-5"}),M&&e.name]},e.name)})})]},t))})}),(0,a.jsxs)("div",{className:`flex-shrink-0 border-t border-gray-200 dark:border-gray-700 px-4 py-4 ${!M&&"px-2"}`,children:[M&&(0,a.jsx)(U,{}),(0,a.jsx)("div",{className:"space-y-3",children:J.links.map(e=>{let t=e.icon,r=E.includes(e.href),s=e.isParent&&(r||e.subLinks?.some(e=>E.includes(e.href)));return(0,a.jsx)("div",{children:e.isParent?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("button",{onClick:()=>$(!S),className:`w-full cursor-pointer flex items-center ${M?"justify-between px-3":"justify-center px-0"} py-2 text-sm font-medium rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 gap-3 mb-1 text-[#192c54] dark:text-white transition-all duration-200  ${s?"bg-gray-100 dark:bg-gray-700":""}`,title:M?"":e.name,children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)(t,{className:"h-5 w-5"}),M&&e.name]}),M&&(0,a.jsx)("svg",{className:`w-4 h-4 transition-transform duration-300 ease-in-out ${S?"rotate-180":"rotate-0"}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),M&&(0,a.jsx)("div",{className:`ml-8 space-y-1 mt-1 mb-2 overflow-hidden transition-all duration-700 ease-in-out ${S?"max-h-[200px] opacity-100 transform-none":"max-h-0 opacity-0 transform translate-y-2"}`,children:e.subLinks?.map(e=>{let t=e.icon,r=E===e.href;return a.jsxs(i(),{href:e.href,className:`flex items-center px-3 py-2 text-sm font-medium rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 gap-3 mb-1 text-[#192c54] dark:text-white transition-all duration-300  hover:translate-x-1 hover:scale-[1.02] ${r?"bg-gray-100 dark:bg-gray-700":""}`,children:[a.jsx(t,{className:"h-4 w-4"}),e.name]},e.name)})})]}):(0,a.jsxs)(i(),{href:e.href,className:`flex items-center ${M?"px-3":"px-0 justify-center"} py-2 text-sm font-medium rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 gap-3 mb-1 text-[#192c54] dark:text-white transition-all duration-300 hover:translate-x-1 hover:scale-[1.02] ${r?"bg-gray-100 dark:bg-gray-700":""}`,title:M?"":e.name,children:[(0,a.jsx)(t,{className:"h-5 w-5"}),M&&e.name]})},e.name)})})]})]}),M&&(0,a.jsx)("div",{className:"fixed inset-0 bg-gray-800 bg-opacity-50 z-10 md:hidden",onClick:()=>B(!1)}),(0,a.jsxs)("div",{className:`transition-all duration-300 ${M?"md:pl-60 pl-0":"md:pl-16 pl-0"}`,children:[(0,a.jsx)("header",{className:"bg-white sticky top-0 z-500 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 w-full",children:(0,a.jsxs)("div",{className:"h-16 px-4 md:px-8 flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("button",{onClick:()=>{B(!M)},className:"p-2 rounded-md cursor-pointer text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white",children:(0,a.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 6h16M4 12h16M4 18h16"})})}),(0,a.jsxs)("h2",{className:"text-xl md:text-2xl font-bold text-gray-800 dark:text-gray-100 hidden sm:block",children:["Welcome Back, ",R?.fullName||"Guest"]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(I,{user:D}),(0,a.jsx)("div",{className:"h-6 w-px bg-gray-200 dark:bg-gray-700"}),(0,a.jsx)("button",{onClick:()=>{r("dark"===t?"light":"dark")},className:"p-2 rounded-lg text-gray-800 dark:text-white",children:"dark"===t?(0,a.jsx)(A.A,{size:18}):(0,a.jsx)(k.A,{size:18})})]})]})}),(0,a.jsx)(P.default,{children:(0,a.jsx)("main",{className:"p-4 md:p-10",children:e})})]})]})})}},75256:(e,t,r)=>{"use strict";r.d(t,{n:()=>o,I:()=>l});var a=r(60687),s=r(43210),n=r(24258);r(57405),r(52581);let i=(0,s.createContext)(void 0);function o({children:e,creditThreshold:t=1}){let[r,o]=(0,s.useState)(0),[l,c]=(0,s.useState)(0),[d,u]=(0,s.useState)(0),[m,h]=(0,s.useState)(0),[g,f]=(0,s.useState)(0),[x,p]=(0,s.useState)(!1),[v,b]=(0,s.useState)(0),[y,w]=(0,s.useState)(0),[A,k]=(0,s.useState)(0),[j,N]=(0,s.useState)(.1),[C,S]=(0,s.useState)(0),[$,E]=(0,s.useState)(1),[M,B]=(0,s.useState)(!0),[R,I]=(0,s.useState)(null),[P,_]=(0,s.useState)(null),[T,U]=(0,s.useState)(!1),[L,z]=(0,s.useState)(null),[O,F]=(0,s.useState)(0),{isConnected:D,on:V,registerUser:J}=function(e=""){let[t,r]=(0,s.useState)(!1),a=(0,s.useRef)(null),[n,i]=(0,s.useState)(null),o=(0,s.useCallback)((e,r,s)=>!!a.current&&!!t&&(s?a.current.emit(e,r,s):a.current.emit(e,r),!0),[t]),l=(0,s.useCallback)((e,t)=>a.current?(a.current.on(e,t),()=>{a.current?.off(e,t)}):()=>{},[]),c=(0,s.useCallback)(e=>e?a.current&&t?(console.log(`Registering user ${e} with socket ${a.current.id}`),a.current.emit("registerUser",{userId:e},t=>{t&&t.success?console.log(`User ${e} successfully registered with socket: ${t.message}`):console.error(`Failed to register user ${e} with socket:`,t)}),!0):(console.warn(`Cannot register user ${e}: socket is ${a.current?"created":"not created"} and connection is ${t?"active":"inactive"}`),a.current&&!t&&(console.log("Attempting to connect socket before registering user"),a.current.connect(),setTimeout(()=>{a.current&&a.current.connected&&(console.log(`Retrying registration for user ${e} after connection`),a.current.emit("registerUser",{userId:e}))},1e3)),!1):(console.error("Cannot register user: userId is empty"),!1),[t]);return{isConnected:t,error:n,emit:o,on:l,registerUser:c,socket:a.current}}("credits"),W=e=>{try{let t={...e,timestamp:Date.now()};localStorage.setItem("lastKnownCredits",JSON.stringify(t))}catch(e){console.warn("Failed to save credit data to localStorage:",e)}},H=()=>{try{let e=localStorage.getItem("lastKnownCredits");if(e){let t=JSON.parse(e);if(Date.now()-t.timestamp<3e5)return t}}catch(e){console.warn("Failed to load credit data from localStorage:",e)}return null},K=async()=>{try{B(!0);let e=await (0,n.mP)();return u(e.freeCreditsRemaining||0),h(e.paidCredits||0),f(e.totalAvailable||0),p(e.usingFreeCredits||!1),b(e.freeMinutesRemaining||0),w(e.paidMinutes||0),k(e.totalMinutesAvailable||0),o(e.credits||e.totalAvailable||0),c(e.minutes||e.totalMinutesAvailable||0),N(e.callPricePerMinute||.1),S(e.monthlyAllowance||0),E(e.minimumCreditsThreshold||1),U(!0),z(Date.now()),F(0),W(e),e}catch(t){console.error("Error fetching user credits:",t),F(e=>e+1);let e=H();if(e&&O<3)return console.log("Using cached credit data due to fetch failure"),u(e.freeCreditsRemaining||0),h(e.paidCredits||0),f(e.totalAvailable||0),p(e.usingFreeCredits||!1),b(e.freeMinutesRemaining||0),w(e.paidMinutes||0),k(e.totalMinutesAvailable||0),o(e.credits||e.totalAvailable||0),c(e.minutes||e.totalMinutesAvailable||0),N(e.callPricePerMinute||.1),S(e.monthlyAllowance||0),E(e.minimumCreditsThreshold||1),e;return{credits:0,minutes:0,callPricePerMinute:.1,freeCreditsRemaining:0,paidCredits:0,totalAvailable:0,usingFreeCredits:!1,freeMinutesRemaining:0,paidMinutes:0,totalMinutesAvailable:0,monthlyAllowance:0}}finally{B(!1)}},Y=async()=>{await K()},G=Math.max(t,$);return(0,a.jsx)(i.Provider,{value:{credits:r,minutes:l,freeCreditsRemaining:d,paidCredits:m,totalAvailable:g,usingFreeCredits:x,freeMinutesRemaining:v,paidMinutes:y,totalMinutesAvailable:A,callPricePerMinute:j,monthlyAllowance:C,hasSufficientCredits:g>=G,isLoading:M,refreshCredits:Y,creditThreshold:t,organizationCreditThreshold:$,effectiveThreshold:G,isConnected:D,hasValidData:T,lastSuccessfulFetch:L},children:e})}function l(){let e=(0,s.useContext)(i);if(void 0===e)throw Error("useCredits must be used within a CreditProvider");return e}},88740:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var a=r(60687),s=r(82854);function n({children:e,duration:t=.5,delay:r=0,initialScale:n=.9,className:i="",once:o=!0,viewOffset:l=.1}){return(0,a.jsx)(s.P.div,{initial:{scale:n,opacity:0},whileInView:{scale:1,opacity:1},transition:{duration:t,delay:r,ease:"easeOut"},viewport:{once:o,amount:l},className:i,children:e})}},98007:(e,t,r)=>{Promise.resolve().then(r.bind(r,72796))}};