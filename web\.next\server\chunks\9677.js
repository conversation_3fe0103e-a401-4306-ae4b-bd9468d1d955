"use strict";exports.id=9677,exports.ids=[9677],exports.modules={11860:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(62688).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},26134:(e,t,r)=>{r.d(t,{G$:()=>W,Hs:()=>b,UC:()=>et,VY:()=>eo,ZL:()=>Q,bL:()=>K,bm:()=>en,hE:()=>er,hJ:()=>ee,l9:()=>z,lG:()=>R});var o=r(43210),n=r(70569),a=r(98599),i=r(11273),s=r(96963),l=r(65551),d=r(31355),u=r(32547),c=r(25028),p=r(46059),f=r(14163),g=r(1359),m=r(42247),y=r(63376),h=r(8730),v=r(60687),x="Dialog",[D,b]=(0,i.A)(x),[j,w]=D(x),R=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:a,onOpenChange:i,modal:d=!0}=e,u=o.useRef(null),c=o.useRef(null),[p=!1,f]=(0,l.i)({prop:n,defaultProp:a,onChange:i});return(0,v.jsx)(j,{scope:t,triggerRef:u,contentRef:c,contentId:(0,s.B)(),titleId:(0,s.B)(),descriptionId:(0,s.B)(),open:p,onOpenChange:f,onOpenToggle:o.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};R.displayName=x;var A="DialogTrigger",C=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,i=w(A,r),s=(0,a.s)(t,i.triggerRef);return(0,v.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":S(i.open),...o,ref:s,onClick:(0,n.m)(e.onClick,i.onOpenToggle)})});C.displayName=A;var N="DialogPortal",[I,O]=D(N,{forceMount:void 0}),E=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:a}=e,i=w(N,t);return(0,v.jsx)(I,{scope:t,forceMount:r,children:o.Children.map(n,e=>(0,v.jsx)(p.C,{present:r||i.open,children:(0,v.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};E.displayName=N;var k="DialogOverlay",F=o.forwardRef((e,t)=>{let r=O(k,e.__scopeDialog),{forceMount:o=r.forceMount,...n}=e,a=w(k,e.__scopeDialog);return a.modal?(0,v.jsx)(p.C,{present:o||a.open,children:(0,v.jsx)(_,{...n,ref:t})}):null});F.displayName=k;var _=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=w(k,r);return(0,v.jsx)(m.A,{as:h.DX,allowPinchZoom:!0,shards:[n.contentRef],children:(0,v.jsx)(f.sG.div,{"data-state":S(n.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),P="DialogContent",$=o.forwardRef((e,t)=>{let r=O(P,e.__scopeDialog),{forceMount:o=r.forceMount,...n}=e,a=w(P,e.__scopeDialog);return(0,v.jsx)(p.C,{present:o||a.open,children:a.modal?(0,v.jsx)(M,{...n,ref:t}):(0,v.jsx)(G,{...n,ref:t})})});$.displayName=P;var M=o.forwardRef((e,t)=>{let r=w(P,e.__scopeDialog),i=o.useRef(null),s=(0,a.s)(t,r.contentRef,i);return o.useEffect(()=>{let e=i.current;if(e)return(0,y.Eq)(e)},[]),(0,v.jsx)(T,{...e,ref:s,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,n.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,n.m)(e.onFocusOutside,e=>e.preventDefault())})}),G=o.forwardRef((e,t)=>{let r=w(P,e.__scopeDialog),n=o.useRef(!1),a=o.useRef(!1);return(0,v.jsx)(T,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(n.current||r.triggerRef.current?.focus(),t.preventDefault()),n.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let o=t.target;r.triggerRef.current?.contains(o)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),T=o.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:i,onCloseAutoFocus:s,...l}=e,c=w(P,r),p=o.useRef(null),f=(0,a.s)(t,p);return(0,g.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(u.n,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:i,onUnmountAutoFocus:s,children:(0,v.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":S(c.open),...l,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(J,{titleId:c.titleId}),(0,v.jsx)(X,{contentRef:p,descriptionId:c.descriptionId})]})]})}),q="DialogTitle",V=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=w(q,r);return(0,v.jsx)(f.sG.h2,{id:n.titleId,...o,ref:t})});V.displayName=q;var B="DialogDescription",L=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,n=w(B,r);return(0,v.jsx)(f.sG.p,{id:n.descriptionId,...o,ref:t})});L.displayName=B;var Z="DialogClose",H=o.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,a=w(Z,r);return(0,v.jsx)(f.sG.button,{type:"button",...o,ref:t,onClick:(0,n.m)(e.onClick,()=>a.onOpenChange(!1))})});function S(e){return e?"open":"closed"}H.displayName=Z;var U="DialogTitleWarning",[W,Y]=(0,i.q)(U,{contentName:P,titleName:q,docsSlug:"dialog"}),J=({titleId:e})=>{let t=Y(U),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return o.useEffect(()=>{e&&!document.getElementById(e)&&console.error(r)},[r,e]),null},X=({contentRef:e,descriptionId:t})=>{let r=Y("DialogDescriptionWarning"),n=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return o.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&!document.getElementById(t)&&console.warn(n)},[n,e,t]),null},K=R,z=C,Q=E,ee=F,et=$,er=V,eo=L,en=H},88233:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(62688).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},97895:(e,t,r)=>{r.d(t,{UC:()=>F,VY:()=>M,ZD:()=>P,ZL:()=>E,bL:()=>O,hE:()=>$,hJ:()=>k,rc:()=>_});var o=r(43210),n=r(11273),a=r(98599),i=r(26134),s=r(70569),l=r(8730),d=r(60687),u="AlertDialog",[c,p]=(0,n.A)(u,[i.Hs]),f=(0,i.Hs)(),g=e=>{let{__scopeAlertDialog:t,...r}=e,o=f(t);return(0,d.jsx)(i.bL,{...o,...r,modal:!0})};g.displayName=u,o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(i.l9,{...n,...o,ref:t})}).displayName="AlertDialogTrigger";var m=e=>{let{__scopeAlertDialog:t,...r}=e,o=f(t);return(0,d.jsx)(i.ZL,{...o,...r})};m.displayName="AlertDialogPortal";var y=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(i.hJ,{...n,...o,ref:t})});y.displayName="AlertDialogOverlay";var h="AlertDialogContent",[v,x]=c(h),D=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:n,...u}=e,c=f(r),p=o.useRef(null),g=(0,a.s)(t,p),m=o.useRef(null);return(0,d.jsx)(i.G$,{contentName:h,titleName:b,docsSlug:"alert-dialog",children:(0,d.jsx)(v,{scope:r,cancelRef:m,children:(0,d.jsxs)(i.UC,{role:"alertdialog",...c,...u,ref:g,onOpenAutoFocus:(0,s.m)(u.onOpenAutoFocus,e=>{e.preventDefault(),m.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,d.jsx)(l.xV,{children:n}),(0,d.jsx)(I,{contentRef:p})]})})})});D.displayName=h;var b="AlertDialogTitle",j=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(i.hE,{...n,...o,ref:t})});j.displayName=b;var w="AlertDialogDescription",R=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(i.VY,{...n,...o,ref:t})});R.displayName=w;var A=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,n=f(r);return(0,d.jsx)(i.bm,{...n,...o,ref:t})});A.displayName="AlertDialogAction";var C="AlertDialogCancel",N=o.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...o}=e,{cancelRef:n}=x(C,r),s=f(r),l=(0,a.s)(t,n);return(0,d.jsx)(i.bm,{...s,...o,ref:l})});N.displayName=C;var I=({contentRef:e})=>{let t=`\`${h}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${h}\` by passing a \`${w}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${h}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return o.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},O=g,E=m,k=y,F=D,_=A,P=N,$=j,M=R},99270:(e,t,r)=>{r.d(t,{A:()=>o});let o=(0,r(62688).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])}};