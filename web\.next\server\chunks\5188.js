exports.id=5188,exports.ids=[5188],exports.modules={43:(e,t,r)=>{"use strict";r.d(t,{jH:()=>s});var n=r(43210);r(60687);var i=n.createContext(void 0);function s(e){let t=n.useContext(i);return e||t||"ltr"}},363:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},1359:(e,t,r)=>{"use strict";r.d(t,{Oh:()=>s});var n=r(43210),i=0;function s(){n.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??o()),document.body.insertAdjacentElement("beforeend",e[1]??o()),i++,()=>{1===i&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),i--}},[])}function o(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2666:(e,t,r)=>{"use strict";let n;let i=r(74075),s=r(10257),o=r(44400),{kStatusCode:a}=r(91813),l=Buffer[Symbol.species],u=Buffer.from([0,0,255,255]),c=Symbol("permessage-deflate"),h=Symbol("total-length"),d=Symbol("callback"),p=Symbol("buffers"),f=Symbol("error");class m{constructor(e,t,r){this._maxPayload=0|r,this._options=e||{},this._threshold=void 0!==this._options.threshold?this._options.threshold:1024,this._isServer=!!t,this._deflate=null,this._inflate=null,this.params=null,n||(n=new o(void 0!==this._options.concurrencyLimit?this._options.concurrencyLimit:10))}static get extensionName(){return"permessage-deflate"}offer(){let e={};return this._options.serverNoContextTakeover&&(e.server_no_context_takeover=!0),this._options.clientNoContextTakeover&&(e.client_no_context_takeover=!0),this._options.serverMaxWindowBits&&(e.server_max_window_bits=this._options.serverMaxWindowBits),this._options.clientMaxWindowBits?e.client_max_window_bits=this._options.clientMaxWindowBits:null==this._options.clientMaxWindowBits&&(e.client_max_window_bits=!0),e}accept(e){return e=this.normalizeParams(e),this.params=this._isServer?this.acceptAsServer(e):this.acceptAsClient(e),this.params}cleanup(){if(this._inflate&&(this._inflate.close(),this._inflate=null),this._deflate){let e=this._deflate[d];this._deflate.close(),this._deflate=null,e&&e(Error("The deflate stream was closed while data was being processed"))}}acceptAsServer(e){let t=this._options,r=e.find(e=>(!1!==t.serverNoContextTakeover||!e.server_no_context_takeover)&&(!e.server_max_window_bits||!1!==t.serverMaxWindowBits&&("number"!=typeof t.serverMaxWindowBits||!(t.serverMaxWindowBits>e.server_max_window_bits)))&&("number"!=typeof t.clientMaxWindowBits||!!e.client_max_window_bits));if(!r)throw Error("None of the extension offers can be accepted");return t.serverNoContextTakeover&&(r.server_no_context_takeover=!0),t.clientNoContextTakeover&&(r.client_no_context_takeover=!0),"number"==typeof t.serverMaxWindowBits&&(r.server_max_window_bits=t.serverMaxWindowBits),"number"==typeof t.clientMaxWindowBits?r.client_max_window_bits=t.clientMaxWindowBits:(!0===r.client_max_window_bits||!1===t.clientMaxWindowBits)&&delete r.client_max_window_bits,r}acceptAsClient(e){let t=e[0];if(!1===this._options.clientNoContextTakeover&&t.client_no_context_takeover)throw Error('Unexpected parameter "client_no_context_takeover"');if(t.client_max_window_bits){if(!1===this._options.clientMaxWindowBits||"number"==typeof this._options.clientMaxWindowBits&&t.client_max_window_bits>this._options.clientMaxWindowBits)throw Error('Unexpected or invalid parameter "client_max_window_bits"')}else"number"==typeof this._options.clientMaxWindowBits&&(t.client_max_window_bits=this._options.clientMaxWindowBits);return t}normalizeParams(e){return e.forEach(e=>{Object.keys(e).forEach(t=>{let r=e[t];if(r.length>1)throw Error(`Parameter "${t}" must have only a single value`);if(r=r[0],"client_max_window_bits"===t){if(!0!==r){let e=+r;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${r}`);r=e}else if(!this._isServer)throw TypeError(`Invalid value for parameter "${t}": ${r}`)}else if("server_max_window_bits"===t){let e=+r;if(!Number.isInteger(e)||e<8||e>15)throw TypeError(`Invalid value for parameter "${t}": ${r}`);r=e}else if("client_no_context_takeover"===t||"server_no_context_takeover"===t){if(!0!==r)throw TypeError(`Invalid value for parameter "${t}": ${r}`)}else throw Error(`Unknown parameter "${t}"`);e[t]=r})}),e}decompress(e,t,r){n.add(n=>{this._decompress(e,t,(e,t)=>{n(),r(e,t)})})}compress(e,t,r){n.add(n=>{this._compress(e,t,(e,t)=>{n(),r(e,t)})})}_decompress(e,t,r){let n=this._isServer?"client":"server";if(!this._inflate){let e=`${n}_max_window_bits`,t="number"!=typeof this.params[e]?i.Z_DEFAULT_WINDOWBITS:this.params[e];this._inflate=i.createInflateRaw({...this._options.zlibInflateOptions,windowBits:t}),this._inflate[c]=this,this._inflate[h]=0,this._inflate[p]=[],this._inflate.on("error",v),this._inflate.on("data",y)}this._inflate[d]=r,this._inflate.write(e),t&&this._inflate.write(u),this._inflate.flush(()=>{let e=this._inflate[f];if(e){this._inflate.close(),this._inflate=null,r(e);return}let i=s.concat(this._inflate[p],this._inflate[h]);this._inflate._readableState.endEmitted?(this._inflate.close(),this._inflate=null):(this._inflate[h]=0,this._inflate[p]=[],t&&this.params[`${n}_no_context_takeover`]&&this._inflate.reset()),r(null,i)})}_compress(e,t,r){let n=this._isServer?"server":"client";if(!this._deflate){let e=`${n}_max_window_bits`,t="number"!=typeof this.params[e]?i.Z_DEFAULT_WINDOWBITS:this.params[e];this._deflate=i.createDeflateRaw({...this._options.zlibDeflateOptions,windowBits:t}),this._deflate[h]=0,this._deflate[p]=[],this._deflate.on("data",g)}this._deflate[d]=r,this._deflate.write(e),this._deflate.flush(i.Z_SYNC_FLUSH,()=>{if(!this._deflate)return;let e=s.concat(this._deflate[p],this._deflate[h]);t&&(e=new l(e.buffer,e.byteOffset,e.length-4)),this._deflate[d]=null,this._deflate[h]=0,this._deflate[p]=[],t&&this.params[`${n}_no_context_takeover`]&&this._deflate.reset(),r(null,e)})}}function g(e){this[p].push(e),this[h]+=e.length}function y(e){if(this[h]+=e.length,this[c]._maxPayload<1||this[h]<=this[c]._maxPayload){this[p].push(e);return}this[f]=RangeError("Max payload size exceeded"),this[f].code="WS_ERR_UNSUPPORTED_MESSAGE_LENGTH",this[f][a]=1009,this.removeListener("data",y),this.reset()}function v(e){this[c]._inflate=null,e[a]=1007,this[d](e)}e.exports=m},3890:(e,t,r)=>{let n=r(83997),i=r(28354);t.init=function(e){e.inspectOpts={};let r=Object.keys(t.inspectOpts);for(let n=0;n<r.length;n++)e.inspectOpts[r[n]]=t.inspectOpts[r[n]]},t.log=function(...e){return process.stderr.write(i.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(r){let{namespace:n,useColors:i}=this;if(i){let t=this.color,i="\x1b[3"+(t<8?t:"8;5;"+t),s=`  ${i};1m${n} \u001B[0m`;r[0]=s+r[0].split("\n").join("\n"+s),r.push(i+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else r[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+n+" "+r[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:n.isatty(process.stderr.fd)},t.destroy=i.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=r(39228);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let r=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),n=process.env[t];return n=!!/^(yes|on|true|enabled)$/i.test(n)||!/^(no|off|false|disabled)$/i.test(n)&&("null"===n?null:Number(n)),e[r]=n,e},{}),e.exports=r(43095)(t);let{formatters:s}=e.exports;s.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},s.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},9510:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var n=r(43210),i=r(11273),s=r(98599),o=r(8730),a=r(60687);function l(e){let t=e+"CollectionProvider",[r,l]=(0,i.A)(t),[u,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),h=e=>{let{scope:t,children:r}=e,i=n.useRef(null),s=n.useRef(new Map).current;return(0,a.jsx)(u,{scope:t,itemMap:s,collectionRef:i,children:r})};h.displayName=t;let d=e+"CollectionSlot",p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,i=c(d,r),l=(0,s.s)(t,i.collectionRef);return(0,a.jsx)(o.DX,{ref:l,children:n})});p.displayName=d;let f=e+"CollectionItemSlot",m="data-radix-collection-item",g=n.forwardRef((e,t)=>{let{scope:r,children:i,...l}=e,u=n.useRef(null),h=(0,s.s)(t,u),d=c(f,r);return n.useEffect(()=>(d.itemMap.set(u,{ref:u,...l}),()=>void d.itemMap.delete(u))),(0,a.jsx)(o.DX,{[m]:"",ref:h,children:i})});return g.displayName=f,[{Provider:h,Slot:p,ItemSlot:g},function(t){let r=c(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${m}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},l]}},9680:(e,t,r)=>{e.exports=function(e){function t(e){let r,i,s;let o=null;function a(...e){if(!a.enabled)return;let n=Number(new Date);a.diff=n-(r||n),a.prev=r,a.curr=n,r=n,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(r,n)=>{if("%%"===r)return"%";i++;let s=t.formatters[n];if("function"==typeof s){let t=e[i];r=s.call(a,t),e.splice(i,1),i--}return r}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=n,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(i!==t.namespaces&&(i=t.namespaces,s=t.enabled(e)),s),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function n(e,r){let n=t(this.namespace+(void 0===r?":":r)+e);return n.log=this.log,n}function i(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(i),...t.skips.map(i).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let r;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let n=("string"==typeof e?e:"").split(/[\s,]+/),i=n.length;for(r=0;r<i;r++)n[r]&&("-"===(e=n[r].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let r,n;if("*"===e[e.length-1])return!0;for(r=0,n=t.skips.length;r<n;r++)if(t.skips[r].test(e))return!1;for(r=0,n=t.names.length;r<n;r++)if(t.names[r].test(e))return!0;return!1},t.humanize=r(67802),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(r=>{t[r]=e[r]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t)|0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}},10257:(e,t,r)=>{"use strict";let{EMPTY_BUFFER:n}=r(91813),i=Buffer[Symbol.species];function s(e,t,r,n,i){for(let s=0;s<i;s++)r[n+s]=e[s]^t[3&s]}function o(e,t){for(let r=0;r<e.length;r++)e[r]^=t[3&r]}function a(e){let t;return(a.readOnly=!0,Buffer.isBuffer(e))?e:(e instanceof ArrayBuffer?t=new i(e):ArrayBuffer.isView(e)?t=new i(e.buffer,e.byteOffset,e.byteLength):(t=Buffer.from(e),a.readOnly=!1),t)}if(e.exports={concat:function(e,t){if(0===e.length)return n;if(1===e.length)return e[0];let r=Buffer.allocUnsafe(t),s=0;for(let t=0;t<e.length;t++){let n=e[t];r.set(n,s),s+=n.length}return s<t?new i(r.buffer,r.byteOffset,s):r},mask:s,toArrayBuffer:function(e){return e.length===e.buffer.byteLength?e.buffer:e.buffer.slice(e.byteOffset,e.byteOffset+e.length)},toBuffer:a,unmask:o},!process.env.WS_NO_BUFFER_UTIL)try{let t=r(39727);e.exports.mask=function(e,r,n,i,o){o<48?s(e,r,n,i,o):t.mask(e,r,n,i,o)},e.exports.unmask=function(e,r){e.length<32?o(e,r):t.unmask(e,r)}}catch(e){}},11273:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,q:()=>s});var n=r(43210),i=r(60687);function s(e,t){let r=n.createContext(t),s=e=>{let{children:t,...s}=e,o=n.useMemo(()=>s,Object.values(s));return(0,i.jsx)(r.Provider,{value:o,children:t})};return s.displayName=e+"Provider",[s,function(i){let s=n.useContext(r);if(s)return s;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function o(e,t=[]){let r=[],s=()=>{let t=r.map(e=>n.createContext(e));return function(r){let i=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:i}}),[r,i])}};return s.scopeName=e,[function(t,s){let o=n.createContext(s),a=r.length;r=[...r,s];let l=t=>{let{scope:r,children:s,...l}=t,u=r?.[e]?.[a]||o,c=n.useMemo(()=>l,Object.values(l));return(0,i.jsx)(u.Provider,{value:c,children:s})};return l.displayName=t+"Provider",[l,function(r,i){let l=i?.[e]?.[a]||o,u=n.useContext(l);if(u)return u;if(void 0!==s)return s;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=r.reduce((t,{useScope:r,scopeName:n})=>{let i=r(e)[`__scope${n}`];return{...t,...i}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return r.scopeName=t.scopeName,r}(s,...t)]}},13495:(e,t,r)=>{"use strict";r.d(t,{c:()=>i});var n=r(43210);function i(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},14163:(e,t,r)=>{"use strict";r.d(t,{hO:()=>l,sG:()=>a});var n=r(43210),i=r(51215),s=r(8730),o=r(60687),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...i}=e,a=n?s.DX:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(a,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function l(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},17313:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Building2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},18853:(e,t,r)=>{"use strict";r.d(t,{X:()=>s});var n=r(43210),i=r(66156);function s(e){let[t,r]=n.useState(void 0);return(0,i.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,i;if(!Array.isArray(t)||!t.length)return;let s=t[0];if("borderBoxSize"in s){let e=s.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,i=t.blockSize}else n=e.offsetWidth,i=e.offsetHeight;r({width:n,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},19207:e=>{"use strict";e.exports=(e,t=process.argv)=>{let r=e.startsWith("-")?"":1===e.length?"-":"--",n=t.indexOf(r+e),i=t.indexOf("--");return -1!==n&&(-1===i||n<i)}},20835:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("ChartLine",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"m19 9-5 5-4-4-3 3",key:"2osh9i"}]])},21134:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]])},25028:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var n=r(43210),i=r(51215),s=r(14163),o=r(66156),a=r(60687),l=n.forwardRef((e,t)=>{let{container:r,...l}=e,[u,c]=n.useState(!1);(0,o.N)(()=>c(!0),[]);let h=r||u&&globalThis?.document?.body;return h?i.createPortal((0,a.jsx)(s.sG.div,{...l,ref:t}),h):null});l.displayName="Portal"},25133:(e,t,r)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=r(88573):e.exports=r(67461)},26312:(e,t,r)=>{"use strict";r.d(t,{UC:()=>eK,YJ:()=>eX,q7:()=>eZ,JU:()=>eY,ZL:()=>eG,bL:()=>eH,wv:()=>eJ,l9:()=>ez});var n=r(43210),i=r(70569),s=r(98599),o=r(11273),a=r(65551),l=r(14163),u=r(9510),c=r(43),h=r(31355),d=r(1359),p=r(32547),f=r(96963),m=r(55509),g=r(25028),y=r(46059),v=r(72942),b=r(8730),w=r(13495),x=r(63376),_=r(42247),C=r(60687),E=["Enter"," "],S=["ArrowUp","PageDown","End"],k=["ArrowDown","PageUp","Home",...S],T={ltr:[...E,"ArrowRight"],rtl:[...E,"ArrowLeft"]},A={ltr:["ArrowLeft"],rtl:["ArrowRight"]},R="Menu",[P,O,M]=(0,u.N)(R),[L,F]=(0,o.A)(R,[M,m.Bk,v.RG]),D=(0,m.Bk)(),N=(0,v.RG)(),[j,B]=L(R),[I,V]=L(R),U=e=>{let{__scopeMenu:t,open:r=!1,children:i,dir:s,onOpenChange:o,modal:a=!0}=e,l=D(t),[u,h]=n.useState(null),d=n.useRef(!1),p=(0,w.c)(o),f=(0,c.jH)(s);return n.useEffect(()=>{let e=()=>{d.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>d.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,C.jsx)(m.bL,{...l,children:(0,C.jsx)(j,{scope:t,open:r,onOpenChange:p,content:u,onContentChange:h,children:(0,C.jsx)(I,{scope:t,onClose:n.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:d,dir:f,modal:a,children:i})})})};U.displayName=R;var $=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,i=D(r);return(0,C.jsx)(m.Mz,{...i,...n,ref:t})});$.displayName="MenuAnchor";var W="MenuPortal",[q,H]=L(W,{forceMount:void 0}),z=e=>{let{__scopeMenu:t,forceMount:r,children:n,container:i}=e,s=B(W,t);return(0,C.jsx)(q,{scope:t,forceMount:r,children:(0,C.jsx)(y.C,{present:r||s.open,children:(0,C.jsx)(g.Z,{asChild:!0,container:i,children:n})})})};z.displayName=W;var G="MenuContent",[K,X]=L(G),Y=n.forwardRef((e,t)=>{let r=H(G,e.__scopeMenu),{forceMount:n=r.forceMount,...i}=e,s=B(G,e.__scopeMenu),o=V(G,e.__scopeMenu);return(0,C.jsx)(P.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(y.C,{present:n||s.open,children:(0,C.jsx)(P.Slot,{scope:e.__scopeMenu,children:o.modal?(0,C.jsx)(Z,{...i,ref:t}):(0,C.jsx)(J,{...i,ref:t})})})})}),Z=n.forwardRef((e,t)=>{let r=B(G,e.__scopeMenu),o=n.useRef(null),a=(0,s.s)(t,o);return n.useEffect(()=>{let e=o.current;if(e)return(0,x.Eq)(e)},[]),(0,C.jsx)(Q,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),J=n.forwardRef((e,t)=>{let r=B(G,e.__scopeMenu);return(0,C.jsx)(Q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),Q=n.forwardRef((e,t)=>{let{__scopeMenu:r,loop:o=!1,trapFocus:a,onOpenAutoFocus:l,onCloseAutoFocus:u,disableOutsidePointerEvents:c,onEntryFocus:f,onEscapeKeyDown:g,onPointerDownOutside:y,onFocusOutside:w,onInteractOutside:x,onDismiss:E,disableOutsideScroll:T,...A}=e,R=B(G,r),P=V(G,r),M=D(r),L=N(r),F=O(r),[j,I]=n.useState(null),U=n.useRef(null),$=(0,s.s)(t,U,R.onContentChange),W=n.useRef(0),q=n.useRef(""),H=n.useRef(0),z=n.useRef(null),X=n.useRef("right"),Y=n.useRef(0),Z=T?_.A:n.Fragment,J=T?{as:b.DX,allowPinchZoom:!0}:void 0,Q=e=>{let t=q.current+e,r=F().filter(e=>!e.disabled),n=document.activeElement,i=r.find(e=>e.ref.current===n)?.textValue,s=function(e,t,r){var n;let i=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,s=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===i.length&&(s=s.filter(e=>e!==r));let o=s.find(e=>e.toLowerCase().startsWith(i.toLowerCase()));return o!==r?o:void 0}(r.map(e=>e.textValue),t,i),o=r.find(e=>e.textValue===s)?.ref.current;(function e(t){q.current=t,window.clearTimeout(W.current),""!==t&&(W.current=window.setTimeout(()=>e(""),1e3))})(t),o&&setTimeout(()=>o.focus())};n.useEffect(()=>()=>window.clearTimeout(W.current),[]),(0,d.Oh)();let ee=n.useCallback(e=>X.current===z.current?.side&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,i=!1;for(let e=0,s=t.length-1;e<t.length;s=e++){let o=t[e].x,a=t[e].y,l=t[s].x,u=t[s].y;a>n!=u>n&&r<(l-o)*(n-a)/(u-a)+o&&(i=!i)}return i}({x:e.clientX,y:e.clientY},t)}(e,z.current?.area),[]);return(0,C.jsx)(K,{scope:r,searchRef:q,onItemEnter:n.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),onItemLeave:n.useCallback(e=>{ee(e)||(U.current?.focus(),I(null))},[ee]),onTriggerLeave:n.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),pointerGraceTimerRef:H,onPointerGraceIntentChange:n.useCallback(e=>{z.current=e},[]),children:(0,C.jsx)(Z,{...J,children:(0,C.jsx)(p.n,{asChild:!0,trapped:a,onMountAutoFocus:(0,i.m)(l,e=>{e.preventDefault(),U.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:u,children:(0,C.jsx)(h.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:g,onPointerDownOutside:y,onFocusOutside:w,onInteractOutside:x,onDismiss:E,children:(0,C.jsx)(v.bL,{asChild:!0,...L,dir:P.dir,orientation:"vertical",loop:o,currentTabStopId:j,onCurrentTabStopIdChange:I,onEntryFocus:(0,i.m)(f,e=>{P.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,C.jsx)(m.UC,{role:"menu","aria-orientation":"vertical","data-state":eS(R.open),"data-radix-menu-content":"",dir:P.dir,...M,...A,ref:$,style:{outline:"none",...A.style},onKeyDown:(0,i.m)(A.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&Q(e.key));let i=U.current;if(e.target!==i||!k.includes(e.key))return;e.preventDefault();let s=F().filter(e=>!e.disabled).map(e=>e.ref.current);S.includes(e.key)&&s.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(s)}),onBlur:(0,i.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(W.current),q.current="")}),onPointerMove:(0,i.m)(e.onPointerMove,eA(e=>{let t=e.target,r=Y.current!==e.clientX;e.currentTarget.contains(t)&&r&&(X.current=e.clientX>Y.current?"right":"left",Y.current=e.clientX)}))})})})})})})});Y.displayName=G;var ee=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,C.jsx)(l.sG.div,{role:"group",...n,ref:t})});ee.displayName="MenuGroup";var et=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,C.jsx)(l.sG.div,{...n,ref:t})});et.displayName="MenuLabel";var er="MenuItem",en="menu.itemSelect",ei=n.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:o,...a}=e,u=n.useRef(null),c=V(er,e.__scopeMenu),h=X(er,e.__scopeMenu),d=(0,s.s)(t,u),p=n.useRef(!1);return(0,C.jsx)(es,{...a,ref:d,disabled:r,onClick:(0,i.m)(e.onClick,()=>{let e=u.current;if(!r&&e){let t=new CustomEvent(en,{bubbles:!0,cancelable:!0});e.addEventListener(en,e=>o?.(e),{once:!0}),(0,l.hO)(e,t),t.defaultPrevented?p.current=!1:c.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,i.m)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=""!==h.searchRef.current;!r&&(!t||" "!==e.key)&&E.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});ei.displayName=er;var es=n.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:o=!1,textValue:a,...u}=e,c=X(er,r),h=N(r),d=n.useRef(null),p=(0,s.s)(t,d),[f,m]=n.useState(!1),[g,y]=n.useState("");return n.useEffect(()=>{let e=d.current;e&&y((e.textContent??"").trim())},[u.children]),(0,C.jsx)(P.ItemSlot,{scope:r,disabled:o,textValue:a??g,children:(0,C.jsx)(v.q7,{asChild:!0,...h,focusable:!o,children:(0,C.jsx)(l.sG.div,{role:"menuitem","data-highlighted":f?"":void 0,"aria-disabled":o||void 0,"data-disabled":o?"":void 0,...u,ref:p,onPointerMove:(0,i.m)(e.onPointerMove,eA(e=>{o?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,i.m)(e.onPointerLeave,eA(e=>c.onItemLeave(e))),onFocus:(0,i.m)(e.onFocus,()=>m(!0)),onBlur:(0,i.m)(e.onBlur,()=>m(!1))})})})}),eo=n.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:n,...s}=e;return(0,C.jsx)(ef,{scope:e.__scopeMenu,checked:r,children:(0,C.jsx)(ei,{role:"menuitemcheckbox","aria-checked":ek(r)?"mixed":r,...s,ref:t,"data-state":eT(r),onSelect:(0,i.m)(s.onSelect,()=>n?.(!!ek(r)||!r),{checkForDefaultPrevented:!1})})})});eo.displayName="MenuCheckboxItem";var ea="MenuRadioGroup",[el,eu]=L(ea,{value:void 0,onValueChange:()=>{}}),ec=n.forwardRef((e,t)=>{let{value:r,onValueChange:n,...i}=e,s=(0,w.c)(n);return(0,C.jsx)(el,{scope:e.__scopeMenu,value:r,onValueChange:s,children:(0,C.jsx)(ee,{...i,ref:t})})});ec.displayName=ea;var eh="MenuRadioItem",ed=n.forwardRef((e,t)=>{let{value:r,...n}=e,s=eu(eh,e.__scopeMenu),o=r===s.value;return(0,C.jsx)(ef,{scope:e.__scopeMenu,checked:o,children:(0,C.jsx)(ei,{role:"menuitemradio","aria-checked":o,...n,ref:t,"data-state":eT(o),onSelect:(0,i.m)(n.onSelect,()=>s.onValueChange?.(r),{checkForDefaultPrevented:!1})})})});ed.displayName=eh;var ep="MenuItemIndicator",[ef,em]=L(ep,{checked:!1}),eg=n.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:n,...i}=e,s=em(ep,r);return(0,C.jsx)(y.C,{present:n||ek(s.checked)||!0===s.checked,children:(0,C.jsx)(l.sG.span,{...i,ref:t,"data-state":eT(s.checked)})})});eg.displayName=ep;var ey=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e;return(0,C.jsx)(l.sG.div,{role:"separator","aria-orientation":"horizontal",...n,ref:t})});ey.displayName="MenuSeparator";var ev=n.forwardRef((e,t)=>{let{__scopeMenu:r,...n}=e,i=D(r);return(0,C.jsx)(m.i3,{...i,...n,ref:t})});ev.displayName="MenuArrow";var[eb,ew]=L("MenuSub"),ex="MenuSubTrigger",e_=n.forwardRef((e,t)=>{let r=B(ex,e.__scopeMenu),o=V(ex,e.__scopeMenu),a=ew(ex,e.__scopeMenu),l=X(ex,e.__scopeMenu),u=n.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:h}=l,d={__scopeMenu:e.__scopeMenu},p=n.useCallback(()=>{u.current&&window.clearTimeout(u.current),u.current=null},[]);return n.useEffect(()=>p,[p]),n.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),h(null)}},[c,h]),(0,C.jsx)($,{asChild:!0,...d,children:(0,C.jsx)(es,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":a.contentId,"data-state":eS(r.open),...e,ref:(0,s.t)(t,a.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,i.m)(e.onPointerMove,eA(t=>{l.onItemEnter(t),t.defaultPrevented||e.disabled||r.open||u.current||(l.onPointerGraceIntentChange(null),u.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100))})),onPointerLeave:(0,i.m)(e.onPointerLeave,eA(e=>{p();let t=r.content?.getBoundingClientRect();if(t){let n=r.content?.dataset.side,i="right"===n,s=t[i?"left":"right"],o=t[i?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(i?-5:5),y:e.clientY},{x:s,y:t.top},{x:o,y:t.top},{x:o,y:t.bottom},{x:s,y:t.bottom}],side:n}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,i.m)(e.onKeyDown,t=>{let n=""!==l.searchRef.current;!e.disabled&&(!n||" "!==t.key)&&T[o.dir].includes(t.key)&&(r.onOpenChange(!0),r.content?.focus(),t.preventDefault())})})})});e_.displayName=ex;var eC="MenuSubContent",eE=n.forwardRef((e,t)=>{let r=H(G,e.__scopeMenu),{forceMount:o=r.forceMount,...a}=e,l=B(G,e.__scopeMenu),u=V(G,e.__scopeMenu),c=ew(eC,e.__scopeMenu),h=n.useRef(null),d=(0,s.s)(t,h);return(0,C.jsx)(P.Provider,{scope:e.__scopeMenu,children:(0,C.jsx)(y.C,{present:o||l.open,children:(0,C.jsx)(P.Slot,{scope:e.__scopeMenu,children:(0,C.jsx)(Q,{id:c.contentId,"aria-labelledby":c.triggerId,...a,ref:d,align:"start",side:"rtl"===u.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{u.isUsingKeyboardRef.current&&h.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,i.m)(e.onEscapeKeyDown,e=>{u.onClose(),e.preventDefault()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=A[u.dir].includes(e.key);t&&r&&(l.onOpenChange(!1),c.trigger?.focus(),e.preventDefault())})})})})})});function eS(e){return e?"open":"closed"}function ek(e){return"indeterminate"===e}function eT(e){return ek(e)?"indeterminate":e?"checked":"unchecked"}function eA(e){return t=>"mouse"===t.pointerType?e(t):void 0}eE.displayName=eC;var eR="DropdownMenu",[eP,eO]=(0,o.A)(eR,[F]),eM=F(),[eL,eF]=eP(eR),eD=e=>{let{__scopeDropdownMenu:t,children:r,dir:i,open:s,defaultOpen:o,onOpenChange:l,modal:u=!0}=e,c=eM(t),h=n.useRef(null),[d=!1,p]=(0,a.i)({prop:s,defaultProp:o,onChange:l});return(0,C.jsx)(eL,{scope:t,triggerId:(0,f.B)(),triggerRef:h,contentId:(0,f.B)(),open:d,onOpenChange:p,onOpenToggle:n.useCallback(()=>p(e=>!e),[p]),modal:u,children:(0,C.jsx)(U,{...c,open:d,onOpenChange:p,dir:i,modal:u,children:r})})};eD.displayName=eR;var eN="DropdownMenuTrigger",ej=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:n=!1,...o}=e,a=eF(eN,r),u=eM(r);return(0,C.jsx)($,{asChild:!0,...u,children:(0,C.jsx)(l.sG.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":n?"":void 0,disabled:n,...o,ref:(0,s.t)(t,a.triggerRef),onPointerDown:(0,i.m)(e.onPointerDown,e=>{n||0!==e.button||!1!==e.ctrlKey||(a.onOpenToggle(),a.open||e.preventDefault())}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{!n&&(["Enter"," "].includes(e.key)&&a.onOpenToggle(),"ArrowDown"===e.key&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});ej.displayName=eN;var eB=e=>{let{__scopeDropdownMenu:t,...r}=e,n=eM(t);return(0,C.jsx)(z,{...n,...r})};eB.displayName="DropdownMenuPortal";var eI="DropdownMenuContent",eV=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...s}=e,o=eF(eI,r),a=eM(r),l=n.useRef(!1);return(0,C.jsx)(Y,{id:o.contentId,"aria-labelledby":o.triggerId,...a,...s,ref:t,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{l.current||o.triggerRef.current?.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,i.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!o.modal||n)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eV.displayName=eI;var eU=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eM(r);return(0,C.jsx)(ee,{...i,...n,ref:t})});eU.displayName="DropdownMenuGroup";var e$=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eM(r);return(0,C.jsx)(et,{...i,...n,ref:t})});e$.displayName="DropdownMenuLabel";var eW=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eM(r);return(0,C.jsx)(ei,{...i,...n,ref:t})});eW.displayName="DropdownMenuItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eM(r);return(0,C.jsx)(eo,{...i,...n,ref:t})}).displayName="DropdownMenuCheckboxItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eM(r);return(0,C.jsx)(ec,{...i,...n,ref:t})}).displayName="DropdownMenuRadioGroup",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eM(r);return(0,C.jsx)(ed,{...i,...n,ref:t})}).displayName="DropdownMenuRadioItem",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eM(r);return(0,C.jsx)(eg,{...i,...n,ref:t})}).displayName="DropdownMenuItemIndicator";var eq=n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eM(r);return(0,C.jsx)(ey,{...i,...n,ref:t})});eq.displayName="DropdownMenuSeparator",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eM(r);return(0,C.jsx)(ev,{...i,...n,ref:t})}).displayName="DropdownMenuArrow",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eM(r);return(0,C.jsx)(e_,{...i,...n,ref:t})}).displayName="DropdownMenuSubTrigger",n.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...n}=e,i=eM(r);return(0,C.jsx)(eE,{...i,...n,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var eH=eD,ez=ej,eG=eB,eK=eV,eX=eU,eY=e$,eZ=eW,eJ=eq},29940:(e,t,r)=>{e.exports=function(e){function t(e){let r,i,s;let o=null;function a(...e){if(!a.enabled)return;let n=Number(new Date);a.diff=n-(r||n),a.prev=r,a.curr=n,r=n,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(r,n)=>{if("%%"===r)return"%";i++;let s=t.formatters[n];if("function"==typeof s){let t=e[i];r=s.call(a,t),e.splice(i,1),i--}return r}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=n,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(i!==t.namespaces&&(i=t.namespaces,s=t.enabled(e)),s),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function n(e,r){let n=t(this.namespace+(void 0===r?":":r)+e);return n.log=this.log,n}function i(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(i),...t.skips.map(i).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let r;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let n=("string"==typeof e?e:"").split(/[\s,]+/),i=n.length;for(r=0;r<i;r++)n[r]&&("-"===(e=n[r].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let r,n;if("*"===e[e.length-1])return!0;for(r=0,n=t.skips.length;r<n;r++)if(t.skips[r].test(e))return!1;for(r=0,n=t.names.length;r<n;r++)if(t.names[r].test(e))return!0;return!1},t.humanize=r(67802),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(r=>{t[r]=e[r]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t)|0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}},31355:(e,t,r)=>{"use strict";r.d(t,{qW:()=>d});var n,i=r(43210),s=r(70569),o=r(14163),a=r(98599),l=r(13495),u=r(60687),c="dismissableLayer.update",h=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=i.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:d,onPointerDownOutside:m,onFocusOutside:g,onInteractOutside:y,onDismiss:v,...b}=e,w=i.useContext(h),[x,_]=i.useState(null),C=x?.ownerDocument??globalThis?.document,[,E]=i.useState({}),S=(0,a.s)(t,e=>_(e)),k=Array.from(w.layers),[T]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),A=k.indexOf(T),R=x?k.indexOf(x):-1,P=w.layersWithOutsidePointerEventsDisabled.size>0,O=R>=A,M=function(e,t=globalThis?.document){let r=(0,l.c)(e),n=i.useRef(!1),s=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){f("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",s.current),s.current=n,t.addEventListener("click",s.current,{once:!0})):n()}else t.removeEventListener("click",s.current);n.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",s.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...w.branches].some(e=>e.contains(t));!O||r||(m?.(e),y?.(e),e.defaultPrevented||v?.())},C),L=function(e,t=globalThis?.document){let r=(0,l.c)(e),n=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!n.current&&f("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;[...w.branches].some(e=>e.contains(t))||(g?.(e),y?.(e),e.defaultPrevented||v?.())},C);return function(e,t=globalThis?.document){let r=(0,l.c)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{R===w.layers.size-1&&(d?.(e),!e.defaultPrevented&&v&&(e.preventDefault(),v()))},C),i.useEffect(()=>{if(x)return r&&(0===w.layersWithOutsidePointerEventsDisabled.size&&(n=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(x)),w.layers.add(x),p(),()=>{r&&1===w.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=n)}},[x,C,r,w]),i.useEffect(()=>()=>{x&&(w.layers.delete(x),w.layersWithOutsidePointerEventsDisabled.delete(x),p())},[x,w]),i.useEffect(()=>{let e=()=>E({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,u.jsx)(o.sG.div,{...b,ref:S,style:{pointerEvents:P?O?"auto":"none":void 0,...e.style},onFocusCapture:(0,s.m)(e.onFocusCapture,L.onFocusCapture),onBlurCapture:(0,s.m)(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:(0,s.m)(e.onPointerDownCapture,M.onPointerDownCapture)})});function p(){let e=new CustomEvent(c);document.dispatchEvent(e)}function f(e,t,r,{discrete:n}){let i=r.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),n?(0,o.hO)(i,s):i.dispatchEvent(s)}d.displayName="DismissableLayer",i.forwardRef((e,t)=>{let r=i.useContext(h),n=i.useRef(null),s=(0,a.s)(t,n);return i.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(o.sG.div,{...e,ref:s})}).displayName="DismissableLayerBranch"},31637:(e,t,r)=>{"use strict";let{tokenChars:n}=r(37293);function i(e,t,r){void 0===e[t]?e[t]=[r]:e[t].push(r)}e.exports={format:function(e){return Object.keys(e).map(t=>{let r=e[t];return Array.isArray(r)||(r=[r]),r.map(e=>[t].concat(Object.keys(e).map(t=>{let r=e[t];return Array.isArray(r)||(r=[r]),r.map(e=>!0===e?t:`${t}=${e}`).join("; ")})).join("; ")).join(", ")}).join(", ")},parse:function(e){let t,r;let s=Object.create(null),o=Object.create(null),a=!1,l=!1,u=!1,c=-1,h=-1,d=-1,p=0;for(;p<e.length;p++)if(h=e.charCodeAt(p),void 0===t){if(-1===d&&1===n[h])-1===c&&(c=p);else if(0!==p&&(32===h||9===h))-1===d&&-1!==c&&(d=p);else if(59===h||44===h){if(-1===c)throw SyntaxError(`Unexpected character at index ${p}`);-1===d&&(d=p);let r=e.slice(c,d);44===h?(i(s,r,o),o=Object.create(null)):t=r,c=d=-1}else throw SyntaxError(`Unexpected character at index ${p}`)}else if(void 0===r){if(-1===d&&1===n[h])-1===c&&(c=p);else if(32===h||9===h)-1===d&&-1!==c&&(d=p);else if(59===h||44===h){if(-1===c)throw SyntaxError(`Unexpected character at index ${p}`);-1===d&&(d=p),i(o,e.slice(c,d),!0),44===h&&(i(s,t,o),o=Object.create(null),t=void 0),c=d=-1}else if(61===h&&-1!==c&&-1===d)r=e.slice(c,p),c=d=-1;else throw SyntaxError(`Unexpected character at index ${p}`)}else if(l){if(1!==n[h])throw SyntaxError(`Unexpected character at index ${p}`);-1===c?c=p:a||(a=!0),l=!1}else if(u){if(1===n[h])-1===c&&(c=p);else if(34===h&&-1!==c)u=!1,d=p;else if(92===h)l=!0;else throw SyntaxError(`Unexpected character at index ${p}`)}else if(34===h&&61===e.charCodeAt(p-1))u=!0;else if(-1===d&&1===n[h])-1===c&&(c=p);else if(-1!==c&&(32===h||9===h))-1===d&&(d=p);else if(59===h||44===h){if(-1===c)throw SyntaxError(`Unexpected character at index ${p}`);-1===d&&(d=p);let n=e.slice(c,d);a&&(n=n.replace(/\\/g,""),a=!1),i(o,r,n),44===h&&(i(s,t,o),o=Object.create(null),t=void 0),r=void 0,c=d=-1}else throw SyntaxError(`Unexpected character at index ${p}`);if(-1===c||u||32===h||9===h)throw SyntaxError("Unexpected end of input");-1===d&&(d=p);let f=e.slice(c,d);return void 0===t?i(s,f,o):(void 0===r?i(o,f,!0):a?i(o,r,f.replace(/\\/g,"")):i(o,r,f),i(s,t,o)),s}}},32547:(e,t,r)=>{"use strict";r.d(t,{n:()=>h});var n=r(43210),i=r(98599),s=r(14163),o=r(13495),a=r(60687),l="focusScope.autoFocusOnMount",u="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},h=n.forwardRef((e,t)=>{let{loop:r=!1,trapped:h=!1,onMountAutoFocus:g,onUnmountAutoFocus:y,...v}=e,[b,w]=n.useState(null),x=(0,o.c)(g),_=(0,o.c)(y),C=n.useRef(null),E=(0,i.s)(t,e=>w(e)),S=n.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;n.useEffect(()=>{if(h){let e=function(e){if(S.paused||!b)return;let t=e.target;b.contains(t)?C.current=t:f(C.current,{select:!0})},t=function(e){if(S.paused||!b)return;let t=e.relatedTarget;null===t||b.contains(t)||f(C.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&f(b)});return b&&r.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[h,b,S.paused]),n.useEffect(()=>{if(b){m.add(S);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(l,c);b.addEventListener(l,x),b.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let r=document.activeElement;for(let n of e)if(f(n,{select:t}),document.activeElement!==r)return}(d(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&f(b))}return()=>{b.removeEventListener(l,x),setTimeout(()=>{let t=new CustomEvent(u,c);b.addEventListener(u,_),b.dispatchEvent(t),t.defaultPrevented||f(e??document.body,{select:!0}),b.removeEventListener(u,_),m.remove(S)},0)}}},[b,x,_,S]);let k=n.useCallback(e=>{if(!r&&!h||S.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[i,s]=function(e){let t=d(e);return[p(t,e),p(t.reverse(),e)]}(t);i&&s?e.shiftKey||n!==s?e.shiftKey&&n===i&&(e.preventDefault(),r&&f(s,{select:!0})):(e.preventDefault(),r&&f(i,{select:!0})):n===t&&e.preventDefault()}},[r,h,S.paused]);return(0,a.jsx)(s.sG.div,{tabIndex:-1,...v,ref:E,onKeyDown:k})});function d(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function p(e,t){for(let r of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function f(e,{select:t=!1}={}){if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}h.displayName="FocusScope";var m=function(){let e=[];return{add(t){let r=e[0];t!==r&&r?.pause(),(e=g(e,t)).unshift(t)},remove(t){e=g(e,t),e[0]?.resume()}}}();function g(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}},35583:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Wallet",[["path",{d:"M19 7V4a1 1 0 0 0-1-1H5a2 2 0 0 0 0 4h15a1 1 0 0 1 1 1v4h-3a2 2 0 0 0 0 4h3a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1",key:"18etb6"}],["path",{d:"M3 5v14a2 2 0 0 0 2 2h15a1 1 0 0 0 1-1v-4",key:"xoc0q4"}]])},36207:(e,t,r)=>{"use strict";let n;let{Duplex:i}=r(27910),{randomFillSync:s}=r(55511),o=r(2666),{EMPTY_BUFFER:a}=r(91813),{isValidStatusCode:l}=r(37293),{mask:u,toBuffer:c}=r(10257),h=Symbol("kByteLength"),d=Buffer.alloc(4),p=8192;class f{constructor(e,t,r){this._extensions=t||{},r&&(this._generateMask=r,this._maskBuffer=Buffer.alloc(4)),this._socket=e,this._firstFragment=!0,this._compress=!1,this._bufferedBytes=0,this._deflating=!1,this._queue=[]}static frame(e,t){let r,i;let o=!1,a=2,l=!1;t.mask&&(r=t.maskBuffer||d,t.generateMask?t.generateMask(r):(8192===p&&(void 0===n&&(n=Buffer.alloc(8192)),s(n,0,8192),p=0),r[0]=n[p++],r[1]=n[p++],r[2]=n[p++],r[3]=n[p++]),l=(r[0]|r[1]|r[2]|r[3])==0,a=6),"string"==typeof e?i=(!t.mask||l)&&void 0!==t[h]?t[h]:(e=Buffer.from(e)).length:(i=e.length,o=t.mask&&t.readOnly&&!l);let c=i;i>=65536?(a+=8,c=127):i>125&&(a+=2,c=126);let f=Buffer.allocUnsafe(o?i+a:a);return(f[0]=t.fin?128|t.opcode:t.opcode,t.rsv1&&(f[0]|=64),f[1]=c,126===c?f.writeUInt16BE(i,2):127===c&&(f[2]=f[3]=0,f.writeUIntBE(i,4,6)),t.mask)?(f[1]|=128,f[a-4]=r[0],f[a-3]=r[1],f[a-2]=r[2],f[a-1]=r[3],l)?[f,e]:o?(u(e,r,f,a,i),[f]):(u(e,r,e,0,i),[f,e]):[f,e]}close(e,t,r,n){let i;if(void 0===e)i=a;else if("number"==typeof e&&l(e)){if(void 0!==t&&t.length){let r=Buffer.byteLength(t);if(r>123)throw RangeError("The message must not be greater than 123 bytes");(i=Buffer.allocUnsafe(2+r)).writeUInt16BE(e,0),"string"==typeof t?i.write(t,2):i.set(t,2)}else(i=Buffer.allocUnsafe(2)).writeUInt16BE(e,0)}else throw TypeError("First argument must be a valid error code number");let s={[h]:i.length,fin:!0,generateMask:this._generateMask,mask:r,maskBuffer:this._maskBuffer,opcode:8,readOnly:!1,rsv1:!1};this._deflating?this.enqueue([this.dispatch,i,!1,s,n]):this.sendFrame(f.frame(i,s),n)}ping(e,t,r){let n,i;if("string"==typeof e?(n=Buffer.byteLength(e),i=!1):(n=(e=c(e)).length,i=c.readOnly),n>125)throw RangeError("The data size must not be greater than 125 bytes");let s={[h]:n,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:9,readOnly:i,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,s,r]):this.sendFrame(f.frame(e,s),r)}pong(e,t,r){let n,i;if("string"==typeof e?(n=Buffer.byteLength(e),i=!1):(n=(e=c(e)).length,i=c.readOnly),n>125)throw RangeError("The data size must not be greater than 125 bytes");let s={[h]:n,fin:!0,generateMask:this._generateMask,mask:t,maskBuffer:this._maskBuffer,opcode:10,readOnly:i,rsv1:!1};this._deflating?this.enqueue([this.dispatch,e,!1,s,r]):this.sendFrame(f.frame(e,s),r)}send(e,t,r){let n,i;let s=this._extensions[o.extensionName],a=t.binary?2:1,l=t.compress;if("string"==typeof e?(n=Buffer.byteLength(e),i=!1):(n=(e=c(e)).length,i=c.readOnly),this._firstFragment?(this._firstFragment=!1,l&&s&&s.params[s._isServer?"server_no_context_takeover":"client_no_context_takeover"]&&(l=n>=s._threshold),this._compress=l):(l=!1,a=0),t.fin&&(this._firstFragment=!0),s){let s={[h]:n,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:i,rsv1:l};this._deflating?this.enqueue([this.dispatch,e,this._compress,s,r]):this.dispatch(e,this._compress,s,r)}else this.sendFrame(f.frame(e,{[h]:n,fin:t.fin,generateMask:this._generateMask,mask:t.mask,maskBuffer:this._maskBuffer,opcode:a,readOnly:i,rsv1:!1}),r)}dispatch(e,t,r,n){if(!t){this.sendFrame(f.frame(e,r),n);return}let i=this._extensions[o.extensionName];this._bufferedBytes+=r[h],this._deflating=!0,i.compress(e,r.fin,(e,t)=>{if(this._socket.destroyed){let e=Error("The socket was closed while data was being compressed");"function"==typeof n&&n(e);for(let t=0;t<this._queue.length;t++){let r=this._queue[t],n=r[r.length-1];"function"==typeof n&&n(e)}return}this._bufferedBytes-=r[h],this._deflating=!1,r.readOnly=!1,this.sendFrame(f.frame(t,r),n),this.dequeue()})}dequeue(){for(;!this._deflating&&this._queue.length;){let e=this._queue.shift();this._bufferedBytes-=e[3][h],Reflect.apply(e[0],this,e.slice(1))}}enqueue(e){this._bufferedBytes+=e[3][h],this._queue.push(e)}sendFrame(e,t){2===e.length?(this._socket.cork(),this._socket.write(e[0]),this._socket.write(e[1],t),this._socket.uncork()):this._socket.write(e[0],t)}}e.exports=f},36495:(e,t,r)=>{"use strict";let n=r(94735),i=r(81630),{Duplex:s}=r(27910),{createHash:o}=r(55511),a=r(31637),l=r(2666),u=r(70946),c=r(72635),{GUID:h,kWebSocket:d}=r(91813),p=/^[+/0-9A-Za-z]{22}==$/;class f extends n{constructor(e,t){if(super(),null==(e={allowSynchronousEvents:!0,autoPong:!0,maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!1,handleProtocols:null,clientTracking:!0,verifyClient:null,noServer:!1,backlog:null,server:null,host:null,path:null,port:null,WebSocket:c,...e}).port&&!e.server&&!e.noServer||null!=e.port&&(e.server||e.noServer)||e.server&&e.noServer)throw TypeError('One and only one of the "port", "server", or "noServer" options must be specified');if(null!=e.port?(this._server=i.createServer((e,t)=>{let r=i.STATUS_CODES[426];t.writeHead(426,{"Content-Length":r.length,"Content-Type":"text/plain"}),t.end(r)}),this._server.listen(e.port,e.host,e.backlog,t)):e.server&&(this._server=e.server),this._server){let e=this.emit.bind(this,"connection");this._removeListeners=function(e,t){for(let r of Object.keys(t))e.on(r,t[r]);return function(){for(let r of Object.keys(t))e.removeListener(r,t[r])}}(this._server,{listening:this.emit.bind(this,"listening"),error:this.emit.bind(this,"error"),upgrade:(t,r,n)=>{this.handleUpgrade(t,r,n,e)}})}!0===e.perMessageDeflate&&(e.perMessageDeflate={}),e.clientTracking&&(this.clients=new Set,this._shouldEmitClose=!1),this.options=e,this._state=0}address(){if(this.options.noServer)throw Error('The server is operating in "noServer" mode');return this._server?this._server.address():null}close(e){if(2===this._state){e&&this.once("close",()=>{e(Error("The server is not running"))}),process.nextTick(m,this);return}if(e&&this.once("close",e),1!==this._state){if(this._state=1,this.options.noServer||this.options.server)this._server&&(this._removeListeners(),this._removeListeners=this._server=null),this.clients&&this.clients.size?this._shouldEmitClose=!0:process.nextTick(m,this);else{let e=this._server;this._removeListeners(),this._removeListeners=this._server=null,e.close(()=>{m(this)})}}}shouldHandle(e){if(this.options.path){let t=e.url.indexOf("?");if((-1!==t?e.url.slice(0,t):e.url)!==this.options.path)return!1}return!0}handleUpgrade(e,t,r,n){t.on("error",g);let i=e.headers["sec-websocket-key"],s=e.headers.upgrade,o=+e.headers["sec-websocket-version"];if("GET"!==e.method){v(this,e,t,405,"Invalid HTTP method");return}if(void 0===s||"websocket"!==s.toLowerCase()){v(this,e,t,400,"Invalid Upgrade header");return}if(void 0===i||!p.test(i)){v(this,e,t,400,"Missing or invalid Sec-WebSocket-Key header");return}if(8!==o&&13!==o){v(this,e,t,400,"Missing or invalid Sec-WebSocket-Version header");return}if(!this.shouldHandle(e)){y(t,400);return}let c=e.headers["sec-websocket-protocol"],h=new Set;if(void 0!==c)try{h=u.parse(c)}catch(r){v(this,e,t,400,"Invalid Sec-WebSocket-Protocol header");return}let d=e.headers["sec-websocket-extensions"],f={};if(this.options.perMessageDeflate&&void 0!==d){let r=new l(this.options.perMessageDeflate,!0,this.options.maxPayload);try{let e=a.parse(d);e[l.extensionName]&&(r.accept(e[l.extensionName]),f[l.extensionName]=r)}catch(r){v(this,e,t,400,"Invalid or unacceptable Sec-WebSocket-Extensions header");return}}if(this.options.verifyClient){let s={origin:e.headers[`${8===o?"sec-websocket-origin":"origin"}`],secure:!!(e.socket.authorized||e.socket.encrypted),req:e};if(2===this.options.verifyClient.length){this.options.verifyClient(s,(s,o,a,l)=>{if(!s)return y(t,o||401,a,l);this.completeUpgrade(f,i,h,e,t,r,n)});return}if(!this.options.verifyClient(s))return y(t,401)}this.completeUpgrade(f,i,h,e,t,r,n)}completeUpgrade(e,t,r,n,i,s,u){if(!i.readable||!i.writable)return i.destroy();if(i[d])throw Error("server.handleUpgrade() was called more than once with the same socket, possibly due to a misconfiguration");if(this._state>0)return y(i,503);let c=o("sha1").update(t+h).digest("base64"),p=["HTTP/1.1 101 Switching Protocols","Upgrade: websocket","Connection: Upgrade",`Sec-WebSocket-Accept: ${c}`],f=new this.options.WebSocket(null,void 0,this.options);if(r.size){let e=this.options.handleProtocols?this.options.handleProtocols(r,n):r.values().next().value;e&&(p.push(`Sec-WebSocket-Protocol: ${e}`),f._protocol=e)}if(e[l.extensionName]){let t=e[l.extensionName].params,r=a.format({[l.extensionName]:[t]});p.push(`Sec-WebSocket-Extensions: ${r}`),f._extensions=e}this.emit("headers",p,n),i.write(p.concat("\r\n").join("\r\n")),i.removeListener("error",g),f.setSocket(i,s,{allowSynchronousEvents:this.options.allowSynchronousEvents,maxPayload:this.options.maxPayload,skipUTF8Validation:this.options.skipUTF8Validation}),this.clients&&(this.clients.add(f),f.on("close",()=>{this.clients.delete(f),this._shouldEmitClose&&!this.clients.size&&process.nextTick(m,this)})),u(f,n)}}function m(e){e._state=2,e.emit("close")}function g(){this.destroy()}function y(e,t,r,n){r=r||i.STATUS_CODES[t],n={Connection:"close","Content-Type":"text/html","Content-Length":Buffer.byteLength(r),...n},e.once("finish",e.destroy),e.end(`HTTP/1.1 ${t} ${i.STATUS_CODES[t]}\r
`+Object.keys(n).map(e=>`${e}: ${n[e]}`).join("\r\n")+"\r\n\r\n"+r)}function v(e,t,r,n,i){if(e.listenerCount("wsClientError")){let n=Error(i);Error.captureStackTrace(n,v),e.emit("wsClientError",n,r,t)}else y(r,n,i)}e.exports=f},37101:(e,t,r)=>{var n=r(29021),i=r(79551),s=r(79646).spawn;function o(e){"use strict";e=e||{};var t,o,a=this,l=r(81630),u=r(55591),c={},h=!1,d={"User-Agent":"node-XMLHttpRequest",Accept:"*/*"},p=Object.assign({},d),f=["accept-charset","accept-encoding","access-control-request-headers","access-control-request-method","connection","content-length","content-transfer-encoding","cookie","cookie2","date","expect","host","keep-alive","origin","referer","te","trailer","transfer-encoding","upgrade","via"],m=["TRACE","TRACK","CONNECT"],g=!1,y=!1,v=!1,b={};this.UNSENT=0,this.OPENED=1,this.HEADERS_RECEIVED=2,this.LOADING=3,this.DONE=4,this.readyState=this.UNSENT,this.onreadystatechange=null,this.responseText="",this.responseXML="",this.response=Buffer.alloc(0),this.status=null,this.statusText=null,this.open=function(e,t,r,n,i){if(this.abort(),y=!1,v=!1,!(e&&-1===m.indexOf(e)))throw Error("SecurityError: Request method not allowed");c={method:e,url:t.toString(),async:"boolean"!=typeof r||r,user:n||null,password:i||null},w(this.OPENED)},this.setDisableHeaderCheck=function(e){h=e},this.setRequestHeader=function(e,t){if(this.readyState!=this.OPENED)throw Error("INVALID_STATE_ERR: setRequestHeader can only be called when state is OPEN");if(!h&&(!e||-1!==f.indexOf(e.toLowerCase())))return console.warn('Refused to set unsafe header "'+e+'"'),!1;if(g)throw Error("INVALID_STATE_ERR: send flag is true");return p[e]=t,!0},this.getResponseHeader=function(e){return"string"==typeof e&&this.readyState>this.OPENED&&o.headers[e.toLowerCase()]&&!y?o.headers[e.toLowerCase()]:null},this.getAllResponseHeaders=function(){if(this.readyState<this.HEADERS_RECEIVED||y)return"";var e="";for(var t in o.headers)"set-cookie"!==t&&"set-cookie2"!==t&&(e+=t+": "+o.headers[t]+"\r\n");return e.substr(0,e.length-2)},this.getRequestHeader=function(e){return"string"==typeof e&&p[e]?p[e]:""},this.send=function(r){if(this.readyState!=this.OPENED)throw Error("INVALID_STATE_ERR: connection must be opened before send() is called");if(g)throw Error("INVALID_STATE_ERR: send has already been called");var h,d=!1,f=!1,m=i.parse(c.url);switch(m.protocol){case"https:":d=!0;case"http:":h=m.hostname;break;case"file:":f=!0;break;case void 0:case"":h="localhost";break;default:throw Error("Protocol not supported.")}if(f){if("GET"!==c.method)throw Error("XMLHttpRequest: Only GET method is supported");if(c.async)n.readFile(unescape(m.pathname),function(e,t){e?a.handleError(e,e.errno||-1):(a.status=200,a.responseText=t.toString("utf8"),a.response=t,w(a.DONE))});else try{this.response=n.readFileSync(unescape(m.pathname)),this.responseText=this.response.toString("utf8"),this.status=200,w(a.DONE)}catch(e){this.handleError(e,e.errno||-1)}return}var v=m.port||(d?443:80),b=m.pathname+(m.search?m.search:"");if(p.Host=h,d&&443===v||80===v||(p.Host+=":"+m.port),c.user){void 0===c.password&&(c.password="");var x=new Buffer(c.user+":"+c.password);p.Authorization="Basic "+x.toString("base64")}"GET"===c.method||"HEAD"===c.method?r=null:r?(p["Content-Length"]=Buffer.isBuffer(r)?r.length:Buffer.byteLength(r),Object.keys(p).some(function(e){return"content-type"===e.toLowerCase()})||(p["Content-Type"]="text/plain;charset=UTF-8")):"POST"===c.method&&(p["Content-Length"]=0);var _=e.agent||!1,C={host:h,port:v,path:b,method:c.method,headers:p,agent:_};if(d&&(C.pfx=e.pfx,C.key=e.key,C.passphrase=e.passphrase,C.cert=e.cert,C.ca=e.ca,C.ciphers=e.ciphers,C.rejectUnauthorized=!1!==e.rejectUnauthorized),y=!1,c.async){var E=d?u.request:l.request;g=!0,a.dispatchEvent("readystatechange");var S=function(r){if(302===(o=r).statusCode||303===o.statusCode||307===o.statusCode){c.url=o.headers.location;var n=i.parse(c.url);h=n.hostname;var s={hostname:n.hostname,port:n.port,path:n.path,method:303===o.statusCode?"GET":c.method,headers:p};d&&(s.pfx=e.pfx,s.key=e.key,s.passphrase=e.passphrase,s.cert=e.cert,s.ca=e.ca,s.ciphers=e.ciphers,s.rejectUnauthorized=!1!==e.rejectUnauthorized),(t=E(s,S).on("error",k)).end();return}w(a.HEADERS_RECEIVED),a.status=o.statusCode,o.on("data",function(e){if(e){var t=Buffer.from(e);a.response=Buffer.concat([a.response,t])}g&&w(a.LOADING)}),o.on("end",function(){g&&(g=!1,w(a.DONE),a.responseText=a.response.toString("utf8"))}),o.on("error",function(e){a.handleError(e)})},k=function(e){if(t.reusedSocket&&"ECONNRESET"===e.code)return E(C,S).on("error",k);a.handleError(e)};t=E(C,S).on("error",k),e.autoUnref&&t.on("socket",e=>{e.unref()}),r&&t.write(r),t.end(),a.dispatchEvent("loadstart")}else{var T=".node-xmlhttprequest-content-"+process.pid,A=".node-xmlhttprequest-sync-"+process.pid;n.writeFileSync(A,"","utf8");for(var R="var http = require('http'), https = require('https'), fs = require('fs');var doRequest = http"+(d?"s":"")+".request;var options = "+JSON.stringify(C)+";var responseText = '';var responseData = Buffer.alloc(0);var req = doRequest(options, function(response) {response.on('data', function(chunk) {  var data = Buffer.from(chunk);  responseText += data.toString('utf8');  responseData = Buffer.concat([responseData, data]);});response.on('end', function() {fs.writeFileSync('"+T+"', JSON.stringify({err: null, data: {statusCode: response.statusCode, headers: response.headers, text: responseText, data: responseData.toString('base64')}}), 'utf8');fs.unlinkSync('"+A+"');});response.on('error', function(error) {fs.writeFileSync('"+T+"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');fs.unlinkSync('"+A+"');});}).on('error', function(error) {fs.writeFileSync('"+T+"', 'NODE-XMLHTTPREQUEST-ERROR:' + JSON.stringify(error), 'utf8');fs.unlinkSync('"+A+"');});"+(r?"req.write('"+JSON.stringify(r).slice(1,-1).replace(/'/g,"\\'")+"');":"")+"req.end();",P=s(process.argv[0],["-e",R]);n.existsSync(A););if(a.responseText=n.readFileSync(T,"utf8"),P.stdin.end(),n.unlinkSync(T),a.responseText.match(/^NODE-XMLHTTPREQUEST-ERROR:/)){var O=JSON.parse(a.responseText.replace(/^NODE-XMLHTTPREQUEST-ERROR:/,""));a.handleError(O,503)}else{a.status=a.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:([0-9]*),.*/,"$1");var M=JSON.parse(a.responseText.replace(/^NODE-XMLHTTPREQUEST-STATUS:[0-9]*,(.*)/,"$1"));o={statusCode:a.status,headers:M.data.headers},a.responseText=M.data.text,a.response=Buffer.from(M.data.data,"base64"),w(a.DONE,!0)}}},this.handleError=function(e,t){this.status=t||0,this.statusText=e,this.responseText=e.stack,y=!0,w(this.DONE)},this.abort=function(){t&&(t.abort(),t=null),p=Object.assign({},d),this.responseText="",this.responseXML="",this.response=Buffer.alloc(0),y=v=!0,this.readyState!==this.UNSENT&&(this.readyState!==this.OPENED||g)&&this.readyState!==this.DONE&&(g=!1,w(this.DONE)),this.readyState=this.UNSENT},this.addEventListener=function(e,t){e in b||(b[e]=[]),b[e].push(t)},this.removeEventListener=function(e,t){e in b&&(b[e]=b[e].filter(function(e){return e!==t}))},this.dispatchEvent=function(e){if("function"==typeof a["on"+e]&&(this.readyState===this.DONE&&c.async?setTimeout(function(){a["on"+e]()},0):a["on"+e]()),e in b)for(let t=0,r=b[e].length;t<r;t++)this.readyState===this.DONE?setTimeout(function(){b[e][t].call(a)},0):b[e][t].call(a)};var w=function(e){if(a.readyState!==e&&(a.readyState!==a.UNSENT||!v)&&(a.readyState=e,(c.async||a.readyState<a.OPENED||a.readyState===a.DONE)&&a.dispatchEvent("readystatechange"),a.readyState===a.DONE)){let e;e=v?"abort":y?"error":"load",a.dispatchEvent(e),a.dispatchEvent("loadend")}}}e.exports=o,o.XMLHttpRequest=o},37293:(e,t,r)=>{"use strict";let{isUtf8:n}=r(79428);function i(e){let t=e.length,r=0;for(;r<t;)if((128&e[r])==0)r++;else if((224&e[r])==192){if(r+1===t||(192&e[r+1])!=128||(254&e[r])==192)return!1;r+=2}else if((240&e[r])==224){if(r+2>=t||(192&e[r+1])!=128||(192&e[r+2])!=128||224===e[r]&&(224&e[r+1])==128||237===e[r]&&(224&e[r+1])==160)return!1;r+=3}else{if((248&e[r])!=240||r+3>=t||(192&e[r+1])!=128||(192&e[r+2])!=128||(192&e[r+3])!=128||240===e[r]&&(240&e[r+1])==128||244===e[r]&&e[r+1]>143||e[r]>244)return!1;r+=4}return!0}if(e.exports={isValidStatusCode:function(e){return e>=1e3&&e<=1014&&1004!==e&&1005!==e&&1006!==e||e>=3e3&&e<=4999},isValidUTF8:i,tokenChars:[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,1,1,1,1,1,0,0,1,1,0,1,1,0,1,1,1,1,1,1,1,1,1,1,0,0,0,0,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,0,0,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,0,1,0,1,0]},n)e.exports.isValidUTF8=function(e){return e.length<24?i(e):n(e)};else if(!process.env.WS_NO_UTF_8_VALIDATE)try{let t=r(47990);e.exports.isValidUTF8=function(e){return e.length<32?i(e):t(e)}}catch(e){}},39228:(e,t,r)=>{"use strict";let n;let i=r(21820),s=r(83997),o=r(19207),{env:a}=process;function l(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function u(e,t){if(0===n)return 0;if(o("color=16m")||o("color=full")||o("color=truecolor"))return 3;if(o("color=256"))return 2;if(e&&!t&&void 0===n)return 0;let r=n||0;if("dumb"===a.TERM)return r;if("win32"===process.platform){let e=i.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in a)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some(e=>e in a)||"codeship"===a.CI_NAME?1:r;if("TEAMCITY_VERSION"in a)return+!!/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(a.TEAMCITY_VERSION);if("truecolor"===a.COLORTERM)return 3;if("TERM_PROGRAM"in a){let e=parseInt((a.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(a.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(a.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(a.TERM)||"COLORTERM"in a?1:r}o("no-color")||o("no-colors")||o("color=false")||o("color=never")?n=0:(o("color")||o("colors")||o("color=true")||o("color=always"))&&(n=1),"FORCE_COLOR"in a&&(n="true"===a.FORCE_COLOR?1:"false"===a.FORCE_COLOR?0:0===a.FORCE_COLOR.length?1:Math.min(parseInt(a.FORCE_COLOR,10),3)),e.exports={supportsColor:function(e){return l(u(e,e&&e.isTTY))},stdout:l(u(!0,s.isatty(1))),stderr:l(u(!0,s.isatty(2)))}},40083:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},40228:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},41312:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},42009:(e,t,r)=>{let n=r(83997),i=r(28354);t.init=function(e){e.inspectOpts={};let r=Object.keys(t.inspectOpts);for(let n=0;n<r.length;n++)e.inspectOpts[r[n]]=t.inspectOpts[r[n]]},t.log=function(...e){return process.stderr.write(i.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(r){let{namespace:n,useColors:i}=this;if(i){let t=this.color,i="\x1b[3"+(t<8?t:"8;5;"+t),s=`  ${i};1m${n} \u001B[0m`;r[0]=s+r[0].split("\n").join("\n"+s),r.push(i+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else r[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+n+" "+r[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:n.isatty(process.stderr.fd)},t.destroy=i.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=r(39228);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let r=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),n=process.env[t];return n=!!/^(yes|on|true|enabled)$/i.test(n)||!/^(no|off|false|disabled)$/i.test(n)&&("null"===n?null:Number(n)),e[r]=n,e},{}),e.exports=r(9680)(t);let{formatters:s}=e.exports;s.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},s.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},42247:(e,t,r)=>{"use strict";r.d(t,{A:()=>H});var n,i=function(){return(i=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function s(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}Object.create;Object.create;var o=("function"==typeof SuppressedError&&SuppressedError,r(43210)),a="right-scroll-bar-position",l="width-before-scroll-bar";function u(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var c="undefined"!=typeof window?o.useLayoutEffect:o.useEffect,h=new WeakMap;function d(e){return e}var p=function(e){void 0===e&&(e={});var t,r,n,s,o=(t=null,void 0===r&&(r=d),n=[],s=!1,{read:function(){if(s)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var t=r(e,s);return n.push(t),function(){n=n.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(s=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){s=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var i=function(){var r=t;t=[],r.forEach(e)},o=function(){return Promise.resolve().then(i)};o(),n={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),n}}}});return o.options=i({async:!0,ssr:!1},e),o}(),f=function(){},m=o.forwardRef(function(e,t){var r,n,a,l,d=o.useRef(null),m=o.useState({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:f}),g=m[0],y=m[1],v=e.forwardProps,b=e.children,w=e.className,x=e.removeScrollBar,_=e.enabled,C=e.shards,E=e.sideCar,S=e.noIsolation,k=e.inert,T=e.allowPinchZoom,A=e.as,R=e.gapMode,P=s(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),O=(r=[d,t],n=function(e){return r.forEach(function(t){return u(t,e)})},(a=(0,o.useState)(function(){return{value:null,callback:n,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=n,l=a.facade,c(function(){var e=h.get(l);if(e){var t=new Set(e),n=new Set(r),i=l.current;t.forEach(function(e){n.has(e)||u(e,null)}),n.forEach(function(e){t.has(e)||u(e,i)})}h.set(l,r)},[r]),l),M=i(i({},P),g);return o.createElement(o.Fragment,null,_&&o.createElement(E,{sideCar:p,removeScrollBar:x,shards:C,noIsolation:S,inert:k,setCallbacks:y,allowPinchZoom:!!T,lockRef:d,gapMode:R}),v?o.cloneElement(o.Children.only(b),i(i({},M),{ref:O})):o.createElement(void 0===A?"div":A,i({},M,{className:w,ref:O}),b))});m.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},m.classNames={fullWidth:l,zeroRight:a};var g=function(e){var t=e.sideCar,r=s(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return o.createElement(n,i({},r))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(i){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=n||r.nc;return t&&e.setAttribute("nonce",t),e}())){var s,o;(s=t).styleSheet?s.styleSheet.cssText=i:s.appendChild(document.createTextNode(i)),o=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(o)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},v=function(){var e=y();return function(t,r){o.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},b=function(){var e=v();return function(t){return e(t.styles,t.dynamic),null}},w={left:0,top:0,right:0,gap:0},x=function(e){return parseInt(e||"",10)||0},_=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],i=t["padding"===e?"paddingRight":"marginRight"];return[x(r),x(n),x(i)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return w;var t=_(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},E=b(),S="data-scroll-locked",k=function(e,t,r,n){var i=e.left,s=e.top,o=e.right,u=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(u,"px ").concat(n,";\n  }\n  body[").concat(S,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(i,"px;\n    padding-top: ").concat(s,"px;\n    padding-right: ").concat(o,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(u,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(u,"px ").concat(n,";\n  }\n  \n  .").concat(l," {\n    margin-right: ").concat(u,"px ").concat(n,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(S,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},T=function(){var e=parseInt(document.body.getAttribute(S)||"0",10);return isFinite(e)?e:0},A=function(){o.useEffect(function(){return document.body.setAttribute(S,(T()+1).toString()),function(){var e=T()-1;e<=0?document.body.removeAttribute(S):document.body.setAttribute(S,e.toString())}},[])},R=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,i=void 0===n?"margin":n;A();var s=o.useMemo(function(){return C(i)},[i]);return o.createElement(E,{styles:k(s,!t,i,r?"":"!important")})},P=!1;if("undefined"!=typeof window)try{var O=Object.defineProperty({},"passive",{get:function(){return P=!0,!0}});window.addEventListener("test",O,O),window.removeEventListener("test",O,O)}catch(e){P=!1}var M=!!P&&{passive:!1},L=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},F=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),D(e,n)){var i=N(e,n);if(i[1]>i[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},D=function(e,t){return"v"===e?L(t,"overflowY"):L(t,"overflowX")},N=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},j=function(e,t,r,n,i){var s,o=(s=window.getComputedStyle(t).direction,"h"===e&&"rtl"===s?-1:1),a=o*n,l=r.target,u=t.contains(l),c=!1,h=a>0,d=0,p=0;do{var f=N(e,l),m=f[0],g=f[1]-f[2]-o*m;(m||g)&&D(e,l)&&(d+=g,p+=m),l=l instanceof ShadowRoot?l.host:l.parentNode}while(!u&&l!==document.body||u&&(t.contains(l)||t===l));return h&&(i&&1>Math.abs(d)||!i&&a>d)?c=!0:!h&&(i&&1>Math.abs(p)||!i&&-a>p)&&(c=!0),c},B=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},I=function(e){return[e.deltaX,e.deltaY]},V=function(e){return e&&"current"in e?e.current:e},U=0,$=[];let W=(p.useMedium(function(e){var t=o.useRef([]),r=o.useRef([0,0]),n=o.useRef(),i=o.useState(U++)[0],s=o.useState(b)[0],a=o.useRef(e);o.useEffect(function(){a.current=e},[e]),o.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(i));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,i=0,s=t.length;i<s;i++)!n&&i in t||(n||(n=Array.prototype.slice.call(t,0,i)),n[i]=t[i]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(V),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[e.inert,e.lockRef.current,e.shards]);var l=o.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var i,s=B(e),o=r.current,l="deltaX"in e?e.deltaX:o[0]-s[0],u="deltaY"in e?e.deltaY:o[1]-s[1],c=e.target,h=Math.abs(l)>Math.abs(u)?"h":"v";if("touches"in e&&"h"===h&&"range"===c.type)return!1;var d=F(h,c);if(!d)return!0;if(d?i=h:(i="v"===h?"h":"v",d=F(h,c)),!d)return!1;if(!n.current&&"changedTouches"in e&&(l||u)&&(n.current=i),!i)return!0;var p=n.current||i;return j(p,t,e,"h"===p?l:u,!0)},[]),u=o.useCallback(function(e){if($.length&&$[$.length-1]===s){var r="deltaY"in e?I(e):B(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta)[0]===r[0]&&n[1]===r[1]})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var i=(a.current.shards||[]).map(V).filter(Boolean).filter(function(t){return t.contains(e.target)});(i.length>0?l(e,i[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=o.useCallback(function(e,r,n,i){var s={name:e,delta:r,target:n,should:i,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(s),setTimeout(function(){t.current=t.current.filter(function(e){return e!==s})},1)},[]),h=o.useCallback(function(e){r.current=B(e),n.current=void 0},[]),d=o.useCallback(function(t){c(t.type,I(t),t.target,l(t,e.lockRef.current))},[]),p=o.useCallback(function(t){c(t.type,B(t),t.target,l(t,e.lockRef.current))},[]);o.useEffect(function(){return $.push(s),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",u,M),document.addEventListener("touchmove",u,M),document.addEventListener("touchstart",h,M),function(){$=$.filter(function(e){return e!==s}),document.removeEventListener("wheel",u,M),document.removeEventListener("touchmove",u,M),document.removeEventListener("touchstart",h,M)}},[]);var f=e.removeScrollBar,m=e.inert;return o.createElement(o.Fragment,null,m?o.createElement(s,{styles:"\n  .block-interactivity-".concat(i," {pointer-events: none;}\n  .allow-interactivity-").concat(i," {pointer-events: all;}\n")}):null,f?o.createElement(R,{gapMode:e.gapMode}):null)}),g);var q=o.forwardRef(function(e,t){return o.createElement(m,i({},e,{ref:t,sideCar:W}))});q.classNames=m.classNames;let H=q},43095:(e,t,r)=>{e.exports=function(e){function t(e){let r,i,s;let o=null;function a(...e){if(!a.enabled)return;let n=Number(new Date);a.diff=n-(r||n),a.prev=r,a.curr=n,r=n,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(r,n)=>{if("%%"===r)return"%";i++;let s=t.formatters[n];if("function"==typeof s){let t=e[i];r=s.call(a,t),e.splice(i,1),i--}return r}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=n,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==o?o:(i!==t.namespaces&&(i=t.namespaces,s=t.enabled(e)),s),set:e=>{o=e}}),"function"==typeof t.init&&t.init(a),a}function n(e,r){let n=t(this.namespace+(void 0===r?":":r)+e);return n.log=this.log,n}function i(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(i),...t.skips.map(i).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let r;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let n=("string"==typeof e?e:"").split(/[\s,]+/),i=n.length;for(r=0;r<i;r++)n[r]&&("-"===(e=n[r].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let r,n;if("*"===e[e.length-1])return!0;for(r=0,n=t.skips.length;r<n;r++)if(t.skips[r].test(e))return!1;for(r=0,n=t.names.length;r<n;r++)if(t.names[r].test(e))return!0;return!1},t.humanize=r(67802),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(r=>{t[r]=e[r]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t)|0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t}},43121:(e,t,r)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let r="color: "+this.color;t.splice(1,0,r,"color: inherit");let n=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(i=n))}),t.splice(i,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=r(9680)(t);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},44400:e=>{"use strict";let t=Symbol("kDone"),r=Symbol("kRun");class n{constructor(e){this[t]=()=>{this.pending--,this[r]()},this.concurrency=e||1/0,this.jobs=[],this.pending=0}add(e){this.jobs.push(e),this[r]()}[r](){if(this.pending!==this.concurrency&&this.jobs.length){let e=this.jobs.shift();this.pending++,e(this[t])}}}e.exports=n},46059:(e,t,r)=>{"use strict";r.d(t,{C:()=>o});var n=r(43210),i=r(98599),s=r(66156),o=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[i,o]=n.useState(),l=n.useRef({}),u=n.useRef(e),c=n.useRef("none"),[h,d]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=a(l.current);c.current="mounted"===h?e:"none"},[h]),(0,s.N)(()=>{let t=l.current,r=u.current;if(r!==e){let n=c.current,i=a(t);e?d("MOUNT"):"none"===i||t?.display==="none"?d("UNMOUNT"):r&&n!==i?d("ANIMATION_OUT"):d("UNMOUNT"),u.current=e}},[e,d]),(0,s.N)(()=>{if(i){let e;let t=i.ownerDocument.defaultView??window,r=r=>{let n=a(l.current).includes(r.animationName);if(r.target===i&&n&&(d("ANIMATION_END"),!u.current)){let r=i.style.animationFillMode;i.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=r)})}},n=e=>{e.target===i&&(c.current=a(l.current))};return i.addEventListener("animationstart",n),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{t.clearTimeout(e),i.removeEventListener("animationstart",n),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}d("ANIMATION_END")},[i,d]),{isPresent:["mounted","unmountSuspended"].includes(h),ref:n.useCallback(e=>{e&&(l.current=getComputedStyle(e)),o(e)},[])}}(t),l="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),u=(0,i.s)(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof r||o.isPresent?n.cloneElement(l,{ref:u}):null};function a(e){return e?.animationName||"none"}o.displayName="Presence"},47905:(e,t,r)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=r(43121):e.exports=r(42009)},49625:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("LayoutDashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},55509:(e,t,r)=>{"use strict";r.d(t,{Mz:()=>eY,i3:()=>eJ,UC:()=>eZ,bL:()=>eX,Bk:()=>eL});var n=r(43210);let i=["top","right","bottom","left"],s=Math.min,o=Math.max,a=Math.round,l=Math.floor,u=e=>({x:e,y:e}),c={left:"right",right:"left",bottom:"top",top:"bottom"},h={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function f(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function g(e){return"y"===e?"height":"width"}function y(e){return["top","bottom"].includes(p(e))?"y":"x"}function v(e){return e.replace(/start|end/g,e=>h[e])}function b(e){return e.replace(/left|right|bottom|top/g,e=>c[e])}function w(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:r,width:n,height:i}=e;return{width:n,height:i,top:r,left:t,right:t+n,bottom:r+i,x:t,y:r}}function _(e,t,r){let n,{reference:i,floating:s}=e,o=y(t),a=m(y(t)),l=g(a),u=p(t),c="y"===o,h=i.x+i.width/2-s.width/2,d=i.y+i.height/2-s.height/2,v=i[l]/2-s[l]/2;switch(u){case"top":n={x:h,y:i.y-s.height};break;case"bottom":n={x:h,y:i.y+i.height};break;case"right":n={x:i.x+i.width,y:d};break;case"left":n={x:i.x-s.width,y:d};break;default:n={x:i.x,y:i.y}}switch(f(t)){case"start":n[a]-=v*(r&&c?-1:1);break;case"end":n[a]+=v*(r&&c?-1:1)}return n}let C=async(e,t,r)=>{let{placement:n="bottom",strategy:i="absolute",middleware:s=[],platform:o}=r,a=s.filter(Boolean),l=await (null==o.isRTL?void 0:o.isRTL(t)),u=await o.getElementRects({reference:e,floating:t,strategy:i}),{x:c,y:h}=_(u,n,l),d=n,p={},f=0;for(let r=0;r<a.length;r++){let{name:s,fn:m}=a[r],{x:g,y:y,data:v,reset:b}=await m({x:c,y:h,initialPlacement:n,placement:d,strategy:i,middlewareData:p,rects:u,platform:o,elements:{reference:e,floating:t}});c=null!=g?g:c,h=null!=y?y:h,p={...p,[s]:{...p[s],...v}},b&&f<=50&&(f++,"object"==typeof b&&(b.placement&&(d=b.placement),b.rects&&(u=!0===b.rects?await o.getElementRects({reference:e,floating:t,strategy:i}):b.rects),{x:c,y:h}=_(u,d,l)),r=-1)}return{x:c,y:h,placement:d,strategy:i,middlewareData:p}};async function E(e,t){var r;void 0===t&&(t={});let{x:n,y:i,platform:s,rects:o,elements:a,strategy:l}=e,{boundary:u="clippingAncestors",rootBoundary:c="viewport",elementContext:h="floating",altBoundary:p=!1,padding:f=0}=d(t,e),m=w(f),g=a[p?"floating"===h?"reference":"floating":h],y=x(await s.getClippingRect({element:null==(r=await (null==s.isElement?void 0:s.isElement(g)))||r?g:g.contextElement||await (null==s.getDocumentElement?void 0:s.getDocumentElement(a.floating)),boundary:u,rootBoundary:c,strategy:l})),v="floating"===h?{x:n,y:i,width:o.floating.width,height:o.floating.height}:o.reference,b=await (null==s.getOffsetParent?void 0:s.getOffsetParent(a.floating)),_=await (null==s.isElement?void 0:s.isElement(b))&&await (null==s.getScale?void 0:s.getScale(b))||{x:1,y:1},C=x(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:v,offsetParent:b,strategy:l}):v);return{top:(y.top-C.top+m.top)/_.y,bottom:(C.bottom-y.bottom+m.bottom)/_.y,left:(y.left-C.left+m.left)/_.x,right:(C.right-y.right+m.right)/_.x}}function S(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function k(e){return i.some(t=>e[t]>=0)}async function T(e,t){let{placement:r,platform:n,elements:i}=e,s=await (null==n.isRTL?void 0:n.isRTL(i.floating)),o=p(r),a=f(r),l="y"===y(r),u=["left","top"].includes(o)?-1:1,c=s&&l?-1:1,h=d(t,e),{mainAxis:m,crossAxis:g,alignmentAxis:v}="number"==typeof h?{mainAxis:h,crossAxis:0,alignmentAxis:null}:{mainAxis:h.mainAxis||0,crossAxis:h.crossAxis||0,alignmentAxis:h.alignmentAxis};return a&&"number"==typeof v&&(g="end"===a?-1*v:v),l?{x:g*c,y:m*u}:{x:m*u,y:g*c}}function A(){return"undefined"!=typeof window}function R(e){return M(e)?(e.nodeName||"").toLowerCase():"#document"}function P(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function O(e){var t;return null==(t=(M(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function M(e){return!!A()&&(e instanceof Node||e instanceof P(e).Node)}function L(e){return!!A()&&(e instanceof Element||e instanceof P(e).Element)}function F(e){return!!A()&&(e instanceof HTMLElement||e instanceof P(e).HTMLElement)}function D(e){return!!A()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof P(e).ShadowRoot)}function N(e){let{overflow:t,overflowX:r,overflowY:n,display:i}=U(e);return/auto|scroll|overlay|hidden|clip/.test(t+n+r)&&!["inline","contents"].includes(i)}function j(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function B(e){let t=I(),r=L(e)?U(e):e;return["transform","translate","scale","rotate","perspective"].some(e=>!!r[e]&&"none"!==r[e])||!!r.containerType&&"normal"!==r.containerType||!t&&!!r.backdropFilter&&"none"!==r.backdropFilter||!t&&!!r.filter&&"none"!==r.filter||["transform","translate","scale","rotate","perspective","filter"].some(e=>(r.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(r.contain||"").includes(e))}function I(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function V(e){return["html","body","#document"].includes(R(e))}function U(e){return P(e).getComputedStyle(e)}function $(e){return L(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function W(e){if("html"===R(e))return e;let t=e.assignedSlot||e.parentNode||D(e)&&e.host||O(e);return D(t)?t.host:t}function q(e,t,r){var n;void 0===t&&(t=[]),void 0===r&&(r=!0);let i=function e(t){let r=W(t);return V(r)?t.ownerDocument?t.ownerDocument.body:t.body:F(r)&&N(r)?r:e(r)}(e),s=i===(null==(n=e.ownerDocument)?void 0:n.body),o=P(i);if(s){let e=H(o);return t.concat(o,o.visualViewport||[],N(i)?i:[],e&&r?q(e):[])}return t.concat(i,q(i,[],r))}function H(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function z(e){let t=U(e),r=parseFloat(t.width)||0,n=parseFloat(t.height)||0,i=F(e),s=i?e.offsetWidth:r,o=i?e.offsetHeight:n,l=a(r)!==s||a(n)!==o;return l&&(r=s,n=o),{width:r,height:n,$:l}}function G(e){return L(e)?e:e.contextElement}function K(e){let t=G(e);if(!F(t))return u(1);let r=t.getBoundingClientRect(),{width:n,height:i,$:s}=z(t),o=(s?a(r.width):r.width)/n,l=(s?a(r.height):r.height)/i;return o&&Number.isFinite(o)||(o=1),l&&Number.isFinite(l)||(l=1),{x:o,y:l}}let X=u(0);function Y(e){let t=P(e);return I()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:X}function Z(e,t,r,n){var i;void 0===t&&(t=!1),void 0===r&&(r=!1);let s=e.getBoundingClientRect(),o=G(e),a=u(1);t&&(n?L(n)&&(a=K(n)):a=K(e));let l=(void 0===(i=r)&&(i=!1),n&&(!i||n===P(o))&&i)?Y(o):u(0),c=(s.left+l.x)/a.x,h=(s.top+l.y)/a.y,d=s.width/a.x,p=s.height/a.y;if(o){let e=P(o),t=n&&L(n)?P(n):n,r=e,i=H(r);for(;i&&n&&t!==r;){let e=K(i),t=i.getBoundingClientRect(),n=U(i),s=t.left+(i.clientLeft+parseFloat(n.paddingLeft))*e.x,o=t.top+(i.clientTop+parseFloat(n.paddingTop))*e.y;c*=e.x,h*=e.y,d*=e.x,p*=e.y,c+=s,h+=o,i=H(r=P(i))}}return x({width:d,height:p,x:c,y:h})}function J(e,t){let r=$(e).scrollLeft;return t?t.left+r:Z(O(e)).left+r}function Q(e,t,r){void 0===r&&(r=!1);let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-(r?0:J(e,n)),y:n.top+t.scrollTop}}function ee(e,t,r){let n;if("viewport"===t)n=function(e,t){let r=P(e),n=O(e),i=r.visualViewport,s=n.clientWidth,o=n.clientHeight,a=0,l=0;if(i){s=i.width,o=i.height;let e=I();(!e||e&&"fixed"===t)&&(a=i.offsetLeft,l=i.offsetTop)}return{width:s,height:o,x:a,y:l}}(e,r);else if("document"===t)n=function(e){let t=O(e),r=$(e),n=e.ownerDocument.body,i=o(t.scrollWidth,t.clientWidth,n.scrollWidth,n.clientWidth),s=o(t.scrollHeight,t.clientHeight,n.scrollHeight,n.clientHeight),a=-r.scrollLeft+J(e),l=-r.scrollTop;return"rtl"===U(n).direction&&(a+=o(t.clientWidth,n.clientWidth)-i),{width:i,height:s,x:a,y:l}}(O(e));else if(L(t))n=function(e,t){let r=Z(e,!0,"fixed"===t),n=r.top+e.clientTop,i=r.left+e.clientLeft,s=F(e)?K(e):u(1),o=e.clientWidth*s.x,a=e.clientHeight*s.y;return{width:o,height:a,x:i*s.x,y:n*s.y}}(t,r);else{let r=Y(e);n={x:t.x-r.x,y:t.y-r.y,width:t.width,height:t.height}}return x(n)}function et(e){return"static"===U(e).position}function er(e,t){if(!F(e)||"fixed"===U(e).position)return null;if(t)return t(e);let r=e.offsetParent;return O(e)===r&&(r=r.ownerDocument.body),r}function en(e,t){let r=P(e);if(j(e))return r;if(!F(e)){let t=W(e);for(;t&&!V(t);){if(L(t)&&!et(t))return t;t=W(t)}return r}let n=er(e,t);for(;n&&["table","td","th"].includes(R(n))&&et(n);)n=er(n,t);return n&&V(n)&&et(n)&&!B(n)?r:n||function(e){let t=W(e);for(;F(t)&&!V(t);){if(B(t))return t;if(j(t))break;t=W(t)}return null}(e)||r}let ei=async function(e){let t=this.getOffsetParent||en,r=this.getDimensions,n=await r(e.floating);return{reference:function(e,t,r){let n=F(t),i=O(t),s="fixed"===r,o=Z(e,!0,s,t),a={scrollLeft:0,scrollTop:0},l=u(0);if(n||!n&&!s){if(("body"!==R(t)||N(i))&&(a=$(t)),n){let e=Z(t,!0,s,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else i&&(l.x=J(i))}let c=!i||n||s?u(0):Q(i,a);return{x:o.left+a.scrollLeft-l.x-c.x,y:o.top+a.scrollTop-l.y-c.y,width:o.width,height:o.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:n.width,height:n.height}}},es={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:r,offsetParent:n,strategy:i}=e,s="fixed"===i,o=O(n),a=!!t&&j(t.floating);if(n===o||a&&s)return r;let l={scrollLeft:0,scrollTop:0},c=u(1),h=u(0),d=F(n);if((d||!d&&!s)&&(("body"!==R(n)||N(o))&&(l=$(n)),F(n))){let e=Z(n);c=K(n),h.x=e.x+n.clientLeft,h.y=e.y+n.clientTop}let p=!o||d||s?u(0):Q(o,l,!0);return{width:r.width*c.x,height:r.height*c.y,x:r.x*c.x-l.scrollLeft*c.x+h.x+p.x,y:r.y*c.y-l.scrollTop*c.y+h.y+p.y}},getDocumentElement:O,getClippingRect:function(e){let{element:t,boundary:r,rootBoundary:n,strategy:i}=e,a=[..."clippingAncestors"===r?j(t)?[]:function(e,t){let r=t.get(e);if(r)return r;let n=q(e,[],!1).filter(e=>L(e)&&"body"!==R(e)),i=null,s="fixed"===U(e).position,o=s?W(e):e;for(;L(o)&&!V(o);){let t=U(o),r=B(o);r||"fixed"!==t.position||(i=null),(s?!r&&!i:!r&&"static"===t.position&&!!i&&["absolute","fixed"].includes(i.position)||N(o)&&!r&&function e(t,r){let n=W(t);return!(n===r||!L(n)||V(n))&&("fixed"===U(n).position||e(n,r))}(e,o))?n=n.filter(e=>e!==o):i=t,o=W(o)}return t.set(e,n),n}(t,this._c):[].concat(r),n],l=a[0],u=a.reduce((e,r)=>{let n=ee(t,r,i);return e.top=o(n.top,e.top),e.right=s(n.right,e.right),e.bottom=s(n.bottom,e.bottom),e.left=o(n.left,e.left),e},ee(t,l,i));return{width:u.right-u.left,height:u.bottom-u.top,x:u.left,y:u.top}},getOffsetParent:en,getElementRects:ei,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:r}=z(e);return{width:t,height:r}},getScale:K,isElement:L,isRTL:function(e){return"rtl"===U(e).direction}};function eo(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ea=e=>({name:"arrow",options:e,async fn(t){let{x:r,y:n,placement:i,rects:a,platform:l,elements:u,middlewareData:c}=t,{element:h,padding:p=0}=d(e,t)||{};if(null==h)return{};let v=w(p),b={x:r,y:n},x=m(y(i)),_=g(x),C=await l.getDimensions(h),E="y"===x,S=E?"clientHeight":"clientWidth",k=a.reference[_]+a.reference[x]-b[x]-a.floating[_],T=b[x]-a.reference[x],A=await (null==l.getOffsetParent?void 0:l.getOffsetParent(h)),R=A?A[S]:0;R&&await (null==l.isElement?void 0:l.isElement(A))||(R=u.floating[S]||a.floating[_]);let P=R/2-C[_]/2-1,O=s(v[E?"top":"left"],P),M=s(v[E?"bottom":"right"],P),L=R-C[_]-M,F=R/2-C[_]/2+(k/2-T/2),D=o(O,s(F,L)),N=!c.arrow&&null!=f(i)&&F!==D&&a.reference[_]/2-(F<O?O:M)-C[_]/2<0,j=N?F<O?F-O:F-L:0;return{[x]:b[x]+j,data:{[x]:D,centerOffset:F-D-j,...N&&{alignmentOffset:j}},reset:N}}}),el=(e,t,r)=>{let n=new Map,i={platform:es,...r},s={...i.platform,_c:n};return C(e,t,{...i,platform:s})};var eu=r(51215),ec="undefined"!=typeof document?n.useLayoutEffect:n.useEffect;function eh(e,t){let r,n,i;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((r=e.length)!==t.length)return!1;for(n=r;0!=n--;)if(!eh(e[n],t[n]))return!1;return!0}if((r=(i=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(n=r;0!=n--;)if(!({}).hasOwnProperty.call(t,i[n]))return!1;for(n=r;0!=n--;){let r=i[n];if(("_owner"!==r||!e.$$typeof)&&!eh(e[r],t[r]))return!1}return!0}return e!=e&&t!=t}function ed(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ep(e,t){let r=ed(e);return Math.round(t*r)/r}function ef(e){let t=n.useRef(e);return ec(()=>{t.current=e}),t}let em=e=>({name:"arrow",options:e,fn(t){let{element:r,padding:n}="function"==typeof e?e(t):e;return r&&({}).hasOwnProperty.call(r,"current")?null!=r.current?ea({element:r.current,padding:n}).fn(t):{}:r?ea({element:r,padding:n}).fn(t):{}}}),eg=(e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var r,n;let{x:i,y:s,placement:o,middlewareData:a}=t,l=await T(t,e);return o===(null==(r=a.offset)?void 0:r.placement)&&null!=(n=a.arrow)&&n.alignmentOffset?{}:{x:i+l.x,y:s+l.y,data:{...l,placement:o}}}}}(e),options:[e,t]}),ey=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:r,y:n,placement:i}=t,{mainAxis:a=!0,crossAxis:l=!1,limiter:u={fn:e=>{let{x:t,y:r}=e;return{x:t,y:r}}},...c}=d(e,t),h={x:r,y:n},f=await E(t,c),g=y(p(i)),v=m(g),b=h[v],w=h[g];if(a){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",r=b+f[e],n=b-f[t];b=o(r,s(b,n))}if(l){let e="y"===g?"top":"left",t="y"===g?"bottom":"right",r=w+f[e],n=w-f[t];w=o(r,s(w,n))}let x=u.fn({...t,[v]:b,[g]:w});return{...x,data:{x:x.x-r,y:x.y-n,enabled:{[v]:a,[g]:l}}}}}}(e),options:[e,t]}),ev=(e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:r,y:n,placement:i,rects:s,middlewareData:o}=t,{offset:a=0,mainAxis:l=!0,crossAxis:u=!0}=d(e,t),c={x:r,y:n},h=y(i),f=m(h),g=c[f],v=c[h],b=d(a,t),w="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(l){let e="y"===f?"height":"width",t=s.reference[f]-s.floating[e]+w.mainAxis,r=s.reference[f]+s.reference[e]-w.mainAxis;g<t?g=t:g>r&&(g=r)}if(u){var x,_;let e="y"===f?"width":"height",t=["top","left"].includes(p(i)),r=s.reference[h]-s.floating[e]+(t&&(null==(x=o.offset)?void 0:x[h])||0)+(t?0:w.crossAxis),n=s.reference[h]+s.reference[e]+(t?0:(null==(_=o.offset)?void 0:_[h])||0)-(t?w.crossAxis:0);v<r?v=r:v>n&&(v=n)}return{[f]:g,[h]:v}}}}(e),options:[e,t]}),eb=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var r,n,i,s,o;let{placement:a,middlewareData:l,rects:u,initialPlacement:c,platform:h,elements:w}=t,{mainAxis:x=!0,crossAxis:_=!0,fallbackPlacements:C,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:k="none",flipAlignment:T=!0,...A}=d(e,t);if(null!=(r=l.arrow)&&r.alignmentOffset)return{};let R=p(a),P=y(c),O=p(c)===c,M=await (null==h.isRTL?void 0:h.isRTL(w.floating)),L=C||(O||!T?[b(c)]:function(e){let t=b(e);return[v(e),t,v(t)]}(c)),F="none"!==k;!C&&F&&L.push(...function(e,t,r,n){let i=f(e),s=function(e,t,r){let n=["left","right"],i=["right","left"];switch(e){case"top":case"bottom":if(r)return t?i:n;return t?n:i;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===r,n);return i&&(s=s.map(e=>e+"-"+i),t&&(s=s.concat(s.map(v)))),s}(c,T,k,M));let D=[c,...L],N=await E(t,A),j=[],B=(null==(n=l.flip)?void 0:n.overflows)||[];if(x&&j.push(N[R]),_){let e=function(e,t,r){void 0===r&&(r=!1);let n=f(e),i=m(y(e)),s=g(i),o="x"===i?n===(r?"end":"start")?"right":"left":"start"===n?"bottom":"top";return t.reference[s]>t.floating[s]&&(o=b(o)),[o,b(o)]}(a,u,M);j.push(N[e[0]],N[e[1]])}if(B=[...B,{placement:a,overflows:j}],!j.every(e=>e<=0)){let e=((null==(i=l.flip)?void 0:i.index)||0)+1,t=D[e];if(t)return{data:{index:e,overflows:B},reset:{placement:t}};let r=null==(s=B.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:s.placement;if(!r)switch(S){case"bestFit":{let e=null==(o=B.filter(e=>{if(F){let t=y(e.placement);return t===P||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:o[0];e&&(r=e);break}case"initialPlacement":r=c}if(a!==r)return{reset:{placement:r}}}return{}}}}(e),options:[e,t]}),ew=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var r,n;let i,a;let{placement:l,rects:u,platform:c,elements:h}=t,{apply:m=()=>{},...g}=d(e,t),v=await E(t,g),b=p(l),w=f(l),x="y"===y(l),{width:_,height:C}=u.floating;"top"===b||"bottom"===b?(i=b,a=w===(await (null==c.isRTL?void 0:c.isRTL(h.floating))?"start":"end")?"left":"right"):(a=b,i="end"===w?"top":"bottom");let S=C-v.top-v.bottom,k=_-v.left-v.right,T=s(C-v[i],S),A=s(_-v[a],k),R=!t.middlewareData.shift,P=T,O=A;if(null!=(r=t.middlewareData.shift)&&r.enabled.x&&(O=k),null!=(n=t.middlewareData.shift)&&n.enabled.y&&(P=S),R&&!w){let e=o(v.left,0),t=o(v.right,0),r=o(v.top,0),n=o(v.bottom,0);x?O=_-2*(0!==e||0!==t?e+t:o(v.left,v.right)):P=C-2*(0!==r||0!==n?r+n:o(v.top,v.bottom))}await m({...t,availableWidth:O,availableHeight:P});let M=await c.getDimensions(h.floating);return _!==M.width||C!==M.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}),ex=(e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:r}=t,{strategy:n="referenceHidden",...i}=d(e,t);switch(n){case"referenceHidden":{let e=S(await E(t,{...i,elementContext:"reference"}),r.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:k(e)}}}case"escaped":{let e=S(await E(t,{...i,altBoundary:!0}),r.floating);return{data:{escapedOffsets:e,escaped:k(e)}}}default:return{}}}}}(e),options:[e,t]}),e_=(e,t)=>({...em(e),options:[e,t]});var eC=r(14163),eE=r(60687),eS=n.forwardRef((e,t)=>{let{children:r,width:n=10,height:i=5,...s}=e;return(0,eE.jsx)(eC.sG.svg,{...s,ref:t,width:n,height:i,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?r:(0,eE.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eS.displayName="Arrow";var ek=r(98599),eT=r(11273),eA=r(13495),eR=r(66156),eP=r(18853),eO="Popper",[eM,eL]=(0,eT.A)(eO),[eF,eD]=eM(eO),eN=e=>{let{__scopePopper:t,children:r}=e,[i,s]=n.useState(null);return(0,eE.jsx)(eF,{scope:t,anchor:i,onAnchorChange:s,children:r})};eN.displayName=eO;var ej="PopperAnchor",eB=n.forwardRef((e,t)=>{let{__scopePopper:r,virtualRef:i,...s}=e,o=eD(ej,r),a=n.useRef(null),l=(0,ek.s)(t,a);return n.useEffect(()=>{o.onAnchorChange(i?.current||a.current)}),i?null:(0,eE.jsx)(eC.sG.div,{...s,ref:l})});eB.displayName=ej;var eI="PopperContent",[eV,eU]=eM(eI),e$=n.forwardRef((e,t)=>{let{__scopePopper:r,side:i="bottom",sideOffset:a=0,align:u="center",alignOffset:c=0,arrowPadding:h=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:f=0,sticky:m="partial",hideWhenDetached:g=!1,updatePositionStrategy:y="optimized",onPlaced:v,...b}=e,w=eD(eI,r),[x,_]=n.useState(null),C=(0,ek.s)(t,e=>_(e)),[E,S]=n.useState(null),k=(0,eP.X)(E),T=k?.width??0,A=k?.height??0,R="number"==typeof f?f:{top:0,right:0,bottom:0,left:0,...f},P=Array.isArray(p)?p:[p],M=P.length>0,L={padding:R,boundary:P.filter(ez),altBoundary:M},{refs:F,floatingStyles:D,placement:N,isPositioned:j,middlewareData:B}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:r="absolute",middleware:i=[],platform:s,elements:{reference:o,floating:a}={},transform:l=!0,whileElementsMounted:u,open:c}=e,[h,d]=n.useState({x:0,y:0,strategy:r,placement:t,middlewareData:{},isPositioned:!1}),[p,f]=n.useState(i);eh(p,i)||f(i);let[m,g]=n.useState(null),[y,v]=n.useState(null),b=n.useCallback(e=>{e!==C.current&&(C.current=e,g(e))},[]),w=n.useCallback(e=>{e!==E.current&&(E.current=e,v(e))},[]),x=o||m,_=a||y,C=n.useRef(null),E=n.useRef(null),S=n.useRef(h),k=null!=u,T=ef(u),A=ef(s),R=ef(c),P=n.useCallback(()=>{if(!C.current||!E.current)return;let e={placement:t,strategy:r,middleware:p};A.current&&(e.platform=A.current),el(C.current,E.current,e).then(e=>{let t={...e,isPositioned:!1!==R.current};O.current&&!eh(S.current,t)&&(S.current=t,eu.flushSync(()=>{d(t)}))})},[p,t,r,A,R]);ec(()=>{!1===c&&S.current.isPositioned&&(S.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let O=n.useRef(!1);ec(()=>(O.current=!0,()=>{O.current=!1}),[]),ec(()=>{if(x&&(C.current=x),_&&(E.current=_),x&&_){if(T.current)return T.current(x,_,P);P()}},[x,_,P,T,k]);let M=n.useMemo(()=>({reference:C,floating:E,setReference:b,setFloating:w}),[b,w]),L=n.useMemo(()=>({reference:x,floating:_}),[x,_]),F=n.useMemo(()=>{let e={position:r,left:0,top:0};if(!L.floating)return e;let t=ep(L.floating,h.x),n=ep(L.floating,h.y);return l?{...e,transform:"translate("+t+"px, "+n+"px)",...ed(L.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,l,L.floating,h.x,h.y]);return n.useMemo(()=>({...h,update:P,refs:M,elements:L,floatingStyles:F}),[h,P,M,L,F])}({strategy:"fixed",placement:i+("center"!==u?"-"+u:""),whileElementsMounted:(...e)=>(function(e,t,r,n){let i;void 0===n&&(n={});let{ancestorScroll:a=!0,ancestorResize:u=!0,elementResize:c="function"==typeof ResizeObserver,layoutShift:h="function"==typeof IntersectionObserver,animationFrame:d=!1}=n,p=G(e),f=a||u?[...p?q(p):[],...q(t)]:[];f.forEach(e=>{a&&e.addEventListener("scroll",r,{passive:!0}),u&&e.addEventListener("resize",r)});let m=p&&h?function(e,t){let r,n=null,i=O(e);function a(){var e;clearTimeout(r),null==(e=n)||e.disconnect(),n=null}return function u(c,h){void 0===c&&(c=!1),void 0===h&&(h=1),a();let d=e.getBoundingClientRect(),{left:p,top:f,width:m,height:g}=d;if(c||t(),!m||!g)return;let y=l(f),v=l(i.clientWidth-(p+m)),b={rootMargin:-y+"px "+-v+"px "+-l(i.clientHeight-(f+g))+"px "+-l(p)+"px",threshold:o(0,s(1,h))||1},w=!0;function x(t){let n=t[0].intersectionRatio;if(n!==h){if(!w)return u();n?u(!1,n):r=setTimeout(()=>{u(!1,1e-7)},1e3)}1!==n||eo(d,e.getBoundingClientRect())||u(),w=!1}try{n=new IntersectionObserver(x,{...b,root:i.ownerDocument})}catch(e){n=new IntersectionObserver(x,b)}n.observe(e)}(!0),a}(p,r):null,g=-1,y=null;c&&(y=new ResizeObserver(e=>{let[n]=e;n&&n.target===p&&y&&(y.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=y)||e.observe(t)})),r()}),p&&!d&&y.observe(p),y.observe(t));let v=d?Z(e):null;return d&&function t(){let n=Z(e);v&&!eo(v,n)&&r(),v=n,i=requestAnimationFrame(t)}(),r(),()=>{var e;f.forEach(e=>{a&&e.removeEventListener("scroll",r),u&&e.removeEventListener("resize",r)}),null==m||m(),null==(e=y)||e.disconnect(),y=null,d&&cancelAnimationFrame(i)}})(...e,{animationFrame:"always"===y}),elements:{reference:w.anchor},middleware:[eg({mainAxis:a+A,alignmentAxis:c}),d&&ey({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?ev():void 0,...L}),d&&eb({...L}),ew({...L,apply:({elements:e,rects:t,availableWidth:r,availableHeight:n})=>{let{width:i,height:s}=t.reference,o=e.floating.style;o.setProperty("--radix-popper-available-width",`${r}px`),o.setProperty("--radix-popper-available-height",`${n}px`),o.setProperty("--radix-popper-anchor-width",`${i}px`),o.setProperty("--radix-popper-anchor-height",`${s}px`)}}),E&&e_({element:E,padding:h}),eG({arrowWidth:T,arrowHeight:A}),g&&ex({strategy:"referenceHidden",...L})]}),[I,V]=eK(N),U=(0,eA.c)(v);(0,eR.N)(()=>{j&&U?.()},[j,U]);let $=B.arrow?.x,W=B.arrow?.y,H=B.arrow?.centerOffset!==0,[z,K]=n.useState();return(0,eR.N)(()=>{x&&K(window.getComputedStyle(x).zIndex)},[x]),(0,eE.jsx)("div",{ref:F.setFloating,"data-radix-popper-content-wrapper":"",style:{...D,transform:j?D.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:z,"--radix-popper-transform-origin":[B.transformOrigin?.x,B.transformOrigin?.y].join(" "),...B.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eE.jsx)(eV,{scope:r,placedSide:I,onArrowChange:S,arrowX:$,arrowY:W,shouldHideArrow:H,children:(0,eE.jsx)(eC.sG.div,{"data-side":I,"data-align":V,...b,ref:C,style:{...b.style,animation:j?void 0:"none"}})})})});e$.displayName=eI;var eW="PopperArrow",eq={top:"bottom",right:"left",bottom:"top",left:"right"},eH=n.forwardRef(function(e,t){let{__scopePopper:r,...n}=e,i=eU(eW,r),s=eq[i.placedSide];return(0,eE.jsx)("span",{ref:i.onArrowChange,style:{position:"absolute",left:i.arrowX,top:i.arrowY,[s]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[i.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[i.placedSide],visibility:i.shouldHideArrow?"hidden":void 0},children:(0,eE.jsx)(eS,{...n,ref:t,style:{...n.style,display:"block"}})})});function ez(e){return null!==e}eH.displayName=eW;var eG=e=>({name:"transformOrigin",options:e,fn(t){let{placement:r,rects:n,middlewareData:i}=t,s=i.arrow?.centerOffset!==0,o=s?0:e.arrowWidth,a=s?0:e.arrowHeight,[l,u]=eK(r),c={start:"0%",center:"50%",end:"100%"}[u],h=(i.arrow?.x??0)+o/2,d=(i.arrow?.y??0)+a/2,p="",f="";return"bottom"===l?(p=s?c:`${h}px`,f=`${-a}px`):"top"===l?(p=s?c:`${h}px`,f=`${n.floating.height+a}px`):"right"===l?(p=`${-a}px`,f=s?c:`${d}px`):"left"===l&&(p=`${n.floating.width+a}px`,f=s?c:`${d}px`),{data:{x:p,y:f}}}});function eK(e){let[t,r="center"]=e.split("-");return[t,r]}var eX=eN,eY=eB,eZ=e$,eJ=eH},57405:(e,t,r)=>{"use strict";let n,i;r.d(t,{io:()=>eO});var s,o={};r.r(o),r.d(o,{Decoder:()=>ew,Encoder:()=>ev,PacketType:()=>s,protocol:()=>ey});var a=r(37101),l=r.t(a,2);let u=Object.create(null);u.open="0",u.close="1",u.ping="2",u.pong="3",u.message="4",u.upgrade="5",u.noop="6";let c=Object.create(null);Object.keys(u).forEach(e=>{c[u[e]]=e});let h={type:"error",data:"parser error"},d=({type:e,data:t},r,n)=>n(t instanceof ArrayBuffer||ArrayBuffer.isView(t)?r?t:"b"+p(t,!0).toString("base64"):u[e]+(t||"")),p=(e,t)=>Buffer.isBuffer(e)||e instanceof Uint8Array&&!t?e:e instanceof ArrayBuffer?Buffer.from(e):Buffer.from(e.buffer,e.byteOffset,e.byteLength),f=(e,t)=>{if("string"!=typeof e)return{type:"message",data:m(e,t)};let r=e.charAt(0);return"b"===r?{type:"message",data:m(Buffer.from(e.substring(1),"base64"),t)}:c[r]?e.length>1?{type:c[r],data:e.substring(1)}:{type:c[r]}:h},m=(e,t)=>"arraybuffer"===t?e instanceof ArrayBuffer?e:Buffer.isBuffer(e)?e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength):e.buffer:Buffer.isBuffer(e)?e:Buffer.from(e),g=(e,t)=>{let r=e.length,n=Array(r),i=0;e.forEach((e,s)=>{d(e,!1,e=>{n[s]=e,++i===r&&t(n.join("\x1e"))})})},y=(e,t)=>{let r=e.split("\x1e"),n=[];for(let e=0;e<r.length;e++){let i=f(r[e],t);if(n.push(i),"error"===i.type)break}return n};function v(e){return e.reduce((e,t)=>e+t.length,0)}function b(e,t){if(e[0].length===t)return e.shift();let r=new Uint8Array(t),n=0;for(let i=0;i<t;i++)r[i]=e[0][n++],n===e[0].length&&(e.shift(),n=0);return e.length&&n<e[0].length&&(e[0]=e[0].slice(n)),r}function w(e){if(e)return function(e){for(var t in w.prototype)e[t]=w.prototype[t];return e}(e)}w.prototype.on=w.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},w.prototype.once=function(e,t){function r(){this.off(e,r),t.apply(this,arguments)}return r.fn=t,this.on(e,r),this},w.prototype.off=w.prototype.removeListener=w.prototype.removeAllListeners=w.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var r,n=this._callbacks["$"+e];if(!n)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var i=0;i<n.length;i++)if((r=n[i])===t||r.fn===t){n.splice(i,1);break}return 0===n.length&&delete this._callbacks["$"+e],this},w.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=Array(arguments.length-1),r=this._callbacks["$"+e],n=1;n<arguments.length;n++)t[n-1]=arguments[n];if(r){r=r.slice(0);for(var n=0,i=r.length;n<i;++n)r[n].apply(this,t)}return this},w.prototype.emitReserved=w.prototype.emit,w.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},w.prototype.hasListeners=function(e){return!!this.listeners(e).length};let x=process.nextTick,_=global;class C{constructor(){this._cookies=new Map}parseCookies(e){e&&e.forEach(e=>{let t=function(e){let t=e.split("; "),r=t[0].indexOf("=");if(-1===r)return;let n=t[0].substring(0,r).trim();if(!n.length)return;let i=t[0].substring(r+1).trim();34===i.charCodeAt(0)&&(i=i.slice(1,-1));let s={name:n,value:i};for(let e=1;e<t.length;e++){let r=t[e].split("=");if(2!==r.length)continue;let n=r[0].trim(),i=r[1].trim();switch(n){case"Expires":s.expires=new Date(i);break;case"Max-Age":let o=new Date;o.setUTCSeconds(o.getUTCSeconds()+parseInt(i,10)),s.expires=o}}return s}(e);t&&this._cookies.set(t.name,t)})}get cookies(){let e=Date.now();return this._cookies.forEach((t,r)=>{var n;(null===(n=t.expires)||void 0===n?void 0:n.getTime())<e&&this._cookies.delete(r)}),this._cookies.entries()}addCookies(e){let t=[];for(let[e,r]of this.cookies)t.push(`${e}=${r.value}`);t.length&&(e.setDisableHeaderCheck(!0),e.setRequestHeader("cookie",t.join("; ")))}appendCookies(e){for(let[t,r]of this.cookies)e.append("cookie",`${t}=${r.value}`)}}function E(e,...t){return t.reduce((t,r)=>(e.hasOwnProperty(r)&&(t[r]=e[r]),t),{})}let S=_.setTimeout,k=_.clearTimeout;function T(e,t){t.useNativeTimers?(e.setTimeoutFn=S.bind(_),e.clearTimeoutFn=k.bind(_)):(e.setTimeoutFn=_.setTimeout.bind(_),e.clearTimeoutFn=_.clearTimeout.bind(_))}function A(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}var R=r(99616);let P=R("engine.io-client:transport");class O extends Error{constructor(e,t,r){super(e),this.description=t,this.context=r,this.type="TransportError"}}class M extends w{constructor(e){super(),this.writable=!1,T(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,t,r){return super.emitReserved("error",new O(e,t,r)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return("opening"===this.readyState||"open"===this.readyState)&&(this.doClose(),this.onClose()),this}send(e){"open"===this.readyState?this.write(e):P("transport is not open, discarding packets")}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){let t=f(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e,t={}){return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){let e=this.opts.hostname;return -1===e.indexOf(":")?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(e){let t=function(e){let t="";for(let r in e)e.hasOwnProperty(r)&&(t.length&&(t+="&"),t+=encodeURIComponent(r)+"="+encodeURIComponent(e[r]));return t}(e);return t.length?"?"+t:""}}let L=R("engine.io-client:polling");class F extends M{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";let t=()=>{L("paused"),this.readyState="paused",e()};if(this._polling||!this.writable){let e=0;this._polling&&(L("we are currently polling - waiting to pause"),e++,this.once("pollComplete",function(){L("pre-pause polling complete"),--e||t()})),this.writable||(L("we are currently writing - waiting to pause"),e++,this.once("drain",function(){L("pre-pause writing complete"),--e||t()}))}else t()}_poll(){L("polling"),this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){L("polling got data %s",e),y(e,this.socket.binaryType).forEach(e=>{if("opening"===this.readyState&&"open"===e.type&&this.onOpen(),"close"===e.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(e)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState?this._poll():L('ignoring poll - transport state "%s"',this.readyState))}doClose(){let e=()=>{L("writing close packet"),this.write([{type:"close"}])};"open"===this.readyState?(L("transport open - closing"),e()):(L("transport not open - deferring close"),this.once("open",e))}write(e){this.writable=!1,g(e,e=>{this.doWrite(e,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){let e=this.opts.secure?"https":"http",t=this.query||{};return!1!==this.opts.timestampRequests&&(t[this.opts.timestampParam]=A()),this.supportsBinary||t.sid||(t.b64=1),this.createUri(e,t)}}let D=!1;try{D="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(e){}let N=D,j=R("engine.io-client:polling");function B(){}class I extends F{constructor(e){if(super(e),"undefined"!=typeof location){let t="https:"===location.protocol,r=location.port;r||(r=t?"443":"80"),this.xd="undefined"!=typeof location&&e.hostname!==location.hostname||r!==e.port}}doWrite(e,t){let r=this.request({method:"POST",data:e});r.on("success",t),r.on("error",(e,t)=>{this.onError("xhr post error",e,t)})}doPoll(){j("xhr poll");let e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(e,t)=>{this.onError("xhr poll error",e,t)}),this.pollXhr=e}}class V extends w{constructor(e,t,r){super(),this.createRequest=e,T(this,r),this._opts=r,this._method=r.method||"GET",this._uri=t,this._data=void 0!==r.data?r.data:null,this._create()}_create(){var e;let t=E(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this._opts.xd;let r=this._xhr=this.createRequest(t);try{j("xhr open %s: %s",this._method,this._uri),r.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders)for(let e in r.setDisableHeaderCheck&&r.setDisableHeaderCheck(!0),this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(e)&&r.setRequestHeader(e,this._opts.extraHeaders[e])}catch(e){}if("POST"===this._method)try{r.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(e){}try{r.setRequestHeader("Accept","*/*")}catch(e){}null===(e=this._opts.cookieJar)||void 0===e||e.addCookies(r),"withCredentials"in r&&(r.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(r.timeout=this._opts.requestTimeout),r.onreadystatechange=()=>{var e;3===r.readyState&&(null===(e=this._opts.cookieJar)||void 0===e||e.parseCookies(r.getResponseHeader("set-cookie"))),4===r.readyState&&(200===r.status||1223===r.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"==typeof r.status?r.status:0)},0))},j("xhr data %s",this._data),r.send(this._data)}catch(e){this.setTimeoutFn(()=>{this._onError(e)},0);return}"undefined"!=typeof document&&(this._index=V.requestsCount++,V.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if(void 0!==this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=B,e)try{this._xhr.abort()}catch(e){}"undefined"!=typeof document&&delete V.requests[this._index],this._xhr=null}}_onLoad(){let e=this._xhr.responseText;null!==e&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}function U(){for(let e in V.requests)V.requests.hasOwnProperty(e)&&V.requests[e].abort()}V.requestsCount=0,V.requests={},"undefined"!=typeof document&&("function"==typeof attachEvent?attachEvent("onunload",U):"function"==typeof addEventListener&&addEventListener("onpagehide"in _?"pagehide":"unload",U,!1)),function(){let e=function(e){let t=e.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!t||N))return new XMLHttpRequest}catch(e){}if(!t)try{return new _[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch(e){}}({xdomain:!1});e&&e.responseType}();let $=a||l;class W extends I{request(e={}){var t;return Object.assign(e,{xd:this.xd,cookieJar:null===(t=this.socket)||void 0===t?void 0:t._cookieJar},this.opts),new V(e=>new $(e),this.uri(),e)}}r(83090),r(64203),r(36207);var q=r(72635);r(36495);let H=R("engine.io-client:websocket"),z="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class G extends M{get name(){return"websocket"}doOpen(){let e=this.uri(),t=this.opts.protocols,r=z?{}:E(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(r.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,t,r)}catch(e){return this.emitReserved("error",e)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let r=e[t],n=t===e.length-1;d(r,this.supportsBinary,e=>{try{this.doWrite(r,e)}catch(e){H("websocket closed before onclose event")}n&&x(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){let e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=A()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}}_.WebSocket||_.MozWebSocket;class K extends G{createSocket(e,t,r){var n;if(null===(n=this.socket)||void 0===n?void 0:n._cookieJar)for(let[e,t]of(r.headers=r.headers||{},r.headers.cookie="string"==typeof r.headers.cookie?[r.headers.cookie]:r.headers.cookie||[],this.socket._cookieJar.cookies))r.headers.cookie.push(`${e}=${t.value}`);return new q(e,t,r)}doWrite(e,t){let r={};e.options&&(r.compress=e.options.compress),this.opts.perMessageDeflate&&("string"==typeof t?Buffer.byteLength(t):t.length)<this.opts.perMessageDeflate.threshold&&(r.compress=!1),this.ws.send(t,r)}}let X=R("engine.io-client:webtransport");class Y extends M{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(e){return this.emitReserved("error",e)}this._transport.closed.then(()=>{X("transport closed gracefully"),this.onClose()}).catch(e=>{X("transport closed due to %s",e),this.onError("webtransport error",e)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(e=>{let t=function(e,t){i||(i=new TextDecoder);let r=[],n=0,s=-1,o=!1;return new TransformStream({transform(a,l){for(r.push(a);;){if(0===n){if(1>v(r))break;let e=b(r,1);o=(128&e[0])==128,n=(s=127&e[0])<126?3:126===s?1:2}else if(1===n){if(2>v(r))break;let e=b(r,2);s=new DataView(e.buffer,e.byteOffset,e.length).getUint16(0),n=3}else if(2===n){if(8>v(r))break;let e=b(r,8),t=new DataView(e.buffer,e.byteOffset,e.length),i=t.getUint32(0);if(i>2097151){l.enqueue(h);break}s=0x100000000*i+t.getUint32(4),n=3}else{if(v(r)<s)break;let e=b(r,s);l.enqueue(f(o?e:i.decode(e),t)),n=0}if(0===s||s>e){l.enqueue(h);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),r=e.readable.pipeThrough(t).getReader(),s=new TransformStream({transform(e,t){!function(e,t){if(e.data instanceof ArrayBuffer||ArrayBuffer.isView(e.data))return t(p(e.data,!1));d(e,!0,e=>{n||(n=new TextEncoder),t(n.encode(e))})}(e,r=>{let n;let i=r.length;if(i<126)new DataView((n=new Uint8Array(1)).buffer).setUint8(0,i);else if(i<65536){let e=new DataView((n=new Uint8Array(3)).buffer);e.setUint8(0,126),e.setUint16(1,i)}else{let e=new DataView((n=new Uint8Array(9)).buffer);e.setUint8(0,127),e.setBigUint64(1,BigInt(i))}e.data&&"string"!=typeof e.data&&(n[0]|=128),t.enqueue(n),t.enqueue(r)})}});s.readable.pipeTo(e.writable),this._writer=s.writable.getWriter();let o=()=>{r.read().then(({done:e,value:t})=>{if(e){X("session is closed");return}X("received chunk: %o",t),this.onPacket(t),o()}).catch(e=>{X("an error occurred while reading: %s",e)})};o();let a={type:"open"};this.query.sid&&(a.data=`{"sid":"${this.query.sid}"}`),this._writer.write(a).then(()=>this.onOpen())})})}write(e){this.writable=!1;for(let t=0;t<e.length;t++){let r=e[t],n=t===e.length-1;this._writer.write(r).then(()=>{n&&x(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var e;null===(e=this._transport)||void 0===e||e.close()}}let Z={websocket:K,webtransport:Y,polling:W},J=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,Q=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function ee(e){if(e.length>8e3)throw"URI too long";let t=e,r=e.indexOf("["),n=e.indexOf("]");-1!=r&&-1!=n&&(e=e.substring(0,r)+e.substring(r,n).replace(/:/g,";")+e.substring(n,e.length));let i=J.exec(e||""),s={},o=14;for(;o--;)s[Q[o]]=i[o]||"";return -1!=r&&-1!=n&&(s.source=t,s.host=s.host.substring(1,s.host.length-1).replace(/;/g,":"),s.authority=s.authority.replace("[","").replace("]","").replace(/;/g,":"),s.ipv6uri=!0),s.pathNames=function(e,t){let r=t.replace(/\/{2,9}/g,"/").split("/");return("/"==t.slice(0,1)||0===t.length)&&r.splice(0,1),"/"==t.slice(-1)&&r.splice(r.length-1,1),r}(0,s.path),s.queryKey=function(e,t){let r={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(e,t,n){t&&(r[t]=n)}),r}(0,s.query),s}let et=R("engine.io-client:socket"),er="function"==typeof addEventListener&&"function"==typeof removeEventListener,en=[];er&&addEventListener("offline",()=>{et("closing %d connection(s) because the network was lost",en.length),en.forEach(e=>e())},!1);class ei extends w{constructor(e,t){if(super(),this.binaryType="nodebuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&"object"==typeof e&&(t=e,e=null),e){let r=ee(e);t.hostname=r.host,t.secure="https"===r.protocol||"wss"===r.protocol,t.port=r.port,r.query&&(t.query=r.query)}else t.host&&(t.hostname=ee(t.host).host);T(this,t),this.secure=null!=t.secure?t.secure:"undefined"!=typeof location&&"https:"===location.protocol,t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=t.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},t.transports.forEach(e=>{let t=e.prototype.name;this.transports.push(t),this._transportsByName[t]=e}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(e){let t={},r=e.split("&");for(let e=0,n=r.length;e<n;e++){let n=r[e].split("=");t[decodeURIComponent(n[0])]=decodeURIComponent(n[1])}return t}(this.opts.query)),er&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(et("adding listener for the 'offline' event"),this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},en.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=new C),this._open()}createTransport(e){et('creating transport "%s"',e);let t=Object.assign({},this.opts.query);t.EIO=4,t.transport=e,this.id&&(t.sid=this.id);let r=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return et("options: %j",r),new this._transportsByName[e](r)}_open(){if(0===this.transports.length){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}let e=this.opts.rememberUpgrade&&ei.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";let t=this.createTransport(e);t.open(),this.setTransport(t)}setTransport(e){et("setting transport %s",e.name),this.transport&&(et("clearing existing transport %s",this.transport.name),this.transport.removeAllListeners()),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",e=>this._onClose("transport close",e))}onOpen(){et("socket open"),this.readyState="open",ei.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(et('socket receive: type "%s", data "%s"',e.type,e.data),this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":let t=Error("server error");t.code=e.data,this._onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data)}else et('packet received with socket readyState "%s"',this.readyState)}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);let e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){let e=this._getWritablePackets();et("flushing %d packets in socket",e.length),this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let e=1;for(let t=0;t<this.writeBuffer.length;t++){let r=this.writeBuffer[t].data;if(r)e+="string"==typeof r?function(e){let t=0,r=0;for(let n=0,i=e.length;n<i;n++)(t=e.charCodeAt(n))<128?r+=1:t<2048?r+=2:t<55296||t>=57344?r+=3:(n++,r+=4);return r}(r):Math.ceil(1.33*(r.byteLength||r.size));if(t>0&&e>this._maxPayload)return et("only send %d out of %d packets",t,this.writeBuffer.length),this.writeBuffer.slice(0,t);e+=2}return et("payload size is %d (max: %d)",e,this._maxPayload),this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;let e=Date.now()>this._pingTimeoutTime;return e&&(et("throttled timer detected, scheduling connection close"),this._pingTimeoutTime=0,x(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),e}write(e,t,r){return this._sendPacket("message",e,t,r),this}send(e,t,r){return this._sendPacket("message",e,t,r),this}_sendPacket(e,t,r,n){if("function"==typeof t&&(n=t,t=void 0),"function"==typeof r&&(n=r,r=null),"closing"===this.readyState||"closed"===this.readyState)return;(r=r||{}).compress=!1!==r.compress;let i={type:e,data:t,options:r};this.emitReserved("packetCreate",i),this.writeBuffer.push(i),n&&this.once("flush",n),this.flush()}close(){let e=()=>{this._onClose("forced close"),et("socket closing - telling transport to close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},r=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return("opening"===this.readyState||"open"===this.readyState)&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?r():e()}):this.upgrading?r():e()),this}_onError(e){if(et("socket error %j",e),ei.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return et("trying next transport"),this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(et('socket close with reason: "%s"',e),this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),er&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){let e=en.indexOf(this._offlineEventListener);-1!==e&&(et("removing listener for the 'offline' event"),en.splice(e,1))}this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this._prevBufferLen=0}}}ei.protocol=4;class es extends ei{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade){et("starting upgrade probes");for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}}_probe(e){et('probing transport "%s"',e);let t=this.createTransport(e),r=!1;ei.priorWebsocketSuccess=!1;let n=()=>{r||(et('probe transport "%s" opened',e),t.send([{type:"ping",data:"probe"}]),t.once("packet",n=>{if(!r){if("pong"===n.type&&"probe"===n.data)et('probe transport "%s" pong',e),this.upgrading=!0,this.emitReserved("upgrading",t),t&&(ei.priorWebsocketSuccess="websocket"===t.name,et('pausing current transport "%s"',this.transport.name),this.transport.pause(()=>{!r&&"closed"!==this.readyState&&(et("changing transport and sending upgrade packet"),u(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())}));else{et('probe transport "%s" failed',e);let r=Error("probe error");r.transport=t.name,this.emitReserved("upgradeError",r)}}}))};function i(){r||(r=!0,u(),t.close(),t=null)}let s=r=>{let n=Error("probe error: "+r);n.transport=t.name,i(),et('probe transport "%s" failed because of error: %s',e,r),this.emitReserved("upgradeError",n)};function o(){s("transport closed")}function a(){s("socket closed")}function l(e){t&&e.name!==t.name&&(et('"%s" works - aborting "%s"',e.name,t.name),i())}let u=()=>{t.removeListener("open",n),t.removeListener("error",s),t.removeListener("close",o),this.off("close",a),this.off("upgrading",l)};t.once("open",n),t.once("error",s),t.once("close",o),this.once("close",a),this.once("upgrading",l),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==e?this.setTimeoutFn(()=>{r||t.open()},200):t.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){let t=[];for(let r=0;r<e.length;r++)~this.transports.indexOf(e[r])&&t.push(e[r]);return t}}class eo extends es{constructor(e,t={}){let r="object"==typeof e?e:t;(!r.transports||r.transports&&"string"==typeof r.transports[0])&&(r.transports=(r.transports||["polling","websocket","webtransport"]).map(e=>Z[e]).filter(e=>!!e)),super(e,r)}}eo.protocol;var ea=r(47905);let el=ea("socket.io-client:url"),eu="function"==typeof ArrayBuffer,ec=e=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer,eh=Object.prototype.toString,ed="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===eh.call(Blob),ep="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===eh.call(File);function ef(e){return eu&&(e instanceof ArrayBuffer||ec(e))||ed&&e instanceof Blob||ep&&e instanceof File}let em=r(25133)("socket.io-parser"),eg=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],ey=5;!function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"}(s||(s={}));class ev{constructor(e){this.replacer=e}encode(e){return(em("encoding packet %j",e),(e.type===s.EVENT||e.type===s.ACK)&&function e(t,r){if(!t||"object"!=typeof t)return!1;if(Array.isArray(t)){for(let r=0,n=t.length;r<n;r++)if(e(t[r]))return!0;return!1}if(ef(t))return!0;if(t.toJSON&&"function"==typeof t.toJSON&&1==arguments.length)return e(t.toJSON(),!0);for(let r in t)if(Object.prototype.hasOwnProperty.call(t,r)&&e(t[r]))return!0;return!1}(e))?this.encodeAsBinary({type:e.type===s.EVENT?s.BINARY_EVENT:s.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id}):[this.encodeAsString(e)]}encodeAsString(e){let t=""+e.type;return(e.type===s.BINARY_EVENT||e.type===s.BINARY_ACK)&&(t+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(t+=e.nsp+","),null!=e.id&&(t+=e.id),null!=e.data&&(t+=JSON.stringify(e.data,this.replacer)),em("encoded %j as %s",e,t),t}encodeAsBinary(e){let t=function(e){let t=[],r=e.data;return e.data=function e(t,r){if(!t)return t;if(ef(t)){let e={_placeholder:!0,num:r.length};return r.push(t),e}if(Array.isArray(t)){let n=Array(t.length);for(let i=0;i<t.length;i++)n[i]=e(t[i],r);return n}if("object"==typeof t&&!(t instanceof Date)){let n={};for(let i in t)Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=e(t[i],r));return n}return t}(r,t),e.attachments=t.length,{packet:e,buffers:t}}(e),r=this.encodeAsString(t.packet),n=t.buffers;return n.unshift(r),n}}function eb(e){return"[object Object]"===Object.prototype.toString.call(e)}class ew extends w{constructor(e){super(),this.reviver=e}add(e){let t;if("string"==typeof e){if(this.reconstructor)throw Error("got plaintext data when reconstructing a packet");let r=(t=this.decodeString(e)).type===s.BINARY_EVENT;r||t.type===s.BINARY_ACK?(t.type=r?s.EVENT:s.ACK,this.reconstructor=new ex(t),0===t.attachments&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else if(ef(e)||e.base64){if(this.reconstructor)(t=this.reconstructor.takeBinaryData(e))&&(this.reconstructor=null,super.emitReserved("decoded",t));else throw Error("got binary data when not reconstructing a packet")}else throw Error("Unknown type: "+e)}decodeString(e){let t=0,r={type:Number(e.charAt(0))};if(void 0===s[r.type])throw Error("unknown packet type "+r.type);if(r.type===s.BINARY_EVENT||r.type===s.BINARY_ACK){let n=t+1;for(;"-"!==e.charAt(++t)&&t!=e.length;);let i=e.substring(n,t);if(i!=Number(i)||"-"!==e.charAt(t))throw Error("Illegal attachments");r.attachments=Number(i)}if("/"===e.charAt(t+1)){let n=t+1;for(;++t&&","!==e.charAt(t)&&t!==e.length;);r.nsp=e.substring(n,t)}else r.nsp="/";let n=e.charAt(t+1);if(""!==n&&Number(n)==n){let n=t+1;for(;++t;){let r=e.charAt(t);if(null==r||Number(r)!=r){--t;break}if(t===e.length)break}r.id=Number(e.substring(n,t+1))}if(e.charAt(++t)){let n=this.tryParse(e.substr(t));if(ew.isPayloadValid(r.type,n))r.data=n;else throw Error("invalid payload")}return em("decoded %s as %j",e,r),r}tryParse(e){try{return JSON.parse(e,this.reviver)}catch(e){return!1}}static isPayloadValid(e,t){switch(e){case s.CONNECT:return eb(t);case s.DISCONNECT:return void 0===t;case s.CONNECT_ERROR:return"string"==typeof t||eb(t);case s.EVENT:case s.BINARY_EVENT:return Array.isArray(t)&&("number"==typeof t[0]||"string"==typeof t[0]&&-1===eg.indexOf(t[0]));case s.ACK:case s.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class ex{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){var t,r;let e=(t=this.reconPack,r=this.buffers,t.data=function e(t,r){if(!t)return t;if(t&&!0===t._placeholder){if("number"==typeof t.num&&t.num>=0&&t.num<r.length)return r[t.num];throw Error("illegal attachments")}if(Array.isArray(t))for(let n=0;n<t.length;n++)t[n]=e(t[n],r);else if("object"==typeof t)for(let n in t)Object.prototype.hasOwnProperty.call(t,n)&&(t[n]=e(t[n],r));return t}(t.data,r),delete t.attachments,t);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function e_(e,t,r){return e.on(t,r),function(){e.off(t,r)}}let eC=ea("socket.io-client:socket"),eE=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class eS extends w{constructor(e,t,r){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,r&&r.auth&&(this.auth=r.auth),this._opts=Object.assign({},r),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;let e=this.io;this.subs=[e_(e,"open",this.onopen.bind(this)),e_(e,"packet",this.onpacket.bind(this)),e_(e,"error",this.onerror.bind(this)),e_(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...e){return e.unshift("message"),this.emit.apply(this,e),this}emit(e,...t){var r,n,i;if(eE.hasOwnProperty(e))throw Error('"'+e.toString()+'" is a reserved event name');if(t.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(t),this;let o={type:s.EVENT,data:t};if(o.options={},o.options.compress=!1!==this.flags.compress,"function"==typeof t[t.length-1]){let e=this.ids++;eC("emitting packet with ack id %d",e);let r=t.pop();this._registerAckCallback(e,r),o.id=e}let a=null===(n=null===(r=this.io.engine)||void 0===r?void 0:r.transport)||void 0===n?void 0:n.writable,l=this.connected&&!(null===(i=this.io.engine)||void 0===i?void 0:i._hasPingExpired());return this.flags.volatile&&!a?eC("discard packet as the transport is not currently writable"):l?(this.notifyOutgoingListeners(o),this.packet(o)):this.sendBuffer.push(o),this.flags={},this}_registerAckCallback(e,t){var r;let n=null!==(r=this.flags.timeout)&&void 0!==r?r:this._opts.ackTimeout;if(void 0===n){this.acks[e]=t;return}let i=this.io.setTimeoutFn(()=>{delete this.acks[e];for(let t=0;t<this.sendBuffer.length;t++)this.sendBuffer[t].id===e&&(eC("removing packet with ack id %d from the buffer",e),this.sendBuffer.splice(t,1));eC("event with ack id %d has timed out after %d ms",e,n),t.call(this,Error("operation has timed out"))},n),s=(...e)=>{this.io.clearTimeoutFn(i),t.apply(this,e)};s.withError=!0,this.acks[e]=s}emitWithAck(e,...t){return new Promise((r,n)=>{let i=(e,t)=>e?n(e):r(t);i.withError=!0,t.push(i),this.emit(e,...t)})}_addToQueue(e){let t;"function"==typeof e[e.length-1]&&(t=e.pop());let r={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((e,...n)=>{if(r===this._queue[0])return null!==e?r.tryCount>this._opts.retries&&(eC("packet [%d] is discarded after %d tries",r.id,r.tryCount),this._queue.shift(),t&&t(e)):(eC("packet [%d] was successfully sent",r.id),this._queue.shift(),t&&t(null,...n)),r.pending=!1,this._drainQueue()}),this._queue.push(r),this._drainQueue()}_drainQueue(e=!1){if(eC("draining queue"),!this.connected||0===this._queue.length)return;let t=this._queue[0];if(t.pending&&!e){eC("packet [%d] has already been sent and is waiting for an ack",t.id);return}t.pending=!0,t.tryCount++,eC("sending packet [%d] (try n\xb0%d)",t.id,t.tryCount),this.flags=t.flags,this.emit.apply(this,t.args)}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){eC("transport is open - connecting"),"function"==typeof this.auth?this.auth(e=>{this._sendConnectPacket(e)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:s.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){eC("close (%s)",e),this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(e=>{if(!this.sendBuffer.some(t=>String(t.id)===e)){let t=this.acks[e];delete this.acks[e],t.withError&&t.call(this,Error("socket has been disconnected"))}})}onpacket(e){if(e.nsp===this.nsp)switch(e.type){case s.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case s.EVENT:case s.BINARY_EVENT:this.onevent(e);break;case s.ACK:case s.BINARY_ACK:this.onack(e);break;case s.DISCONNECT:this.ondisconnect();break;case s.CONNECT_ERROR:this.destroy();let t=Error(e.data.message);t.data=e.data.data,this.emitReserved("connect_error",t)}}onevent(e){let t=e.data||[];eC("emitting event %j",t),null!=e.id&&(eC("attaching ack callback to event"),t.push(this.ack(e.id))),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length)for(let t of this._anyListeners.slice())t.apply(this,e);super.emit.apply(this,e),this._pid&&e.length&&"string"==typeof e[e.length-1]&&(this._lastOffset=e[e.length-1])}ack(e){let t=this,r=!1;return function(...n){r||(r=!0,eC("sending ack %j",n),t.packet({type:s.ACK,id:e,data:n}))}}onack(e){let t=this.acks[e.id];if("function"!=typeof t){eC("bad ack %s",e.id);return}delete this.acks[e.id],eC("calling ack %s with %j",e.id,e.data),t.withError&&e.data.unshift(null),t.apply(this,e.data)}onconnect(e,t){eC("socket connected with id %s",e),this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(e=>this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e=>{this.notifyOutgoingListeners(e),this.packet(e)}),this.sendBuffer=[]}ondisconnect(){eC("server disconnect (%s)",this.nsp),this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(e=>e()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&(eC("performing disconnect (%s)",this.nsp),this.packet({type:s.DISCONNECT})),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){let t=this._anyListeners;for(let r=0;r<t.length;r++)if(e===t[r]){t.splice(r,1);break}}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){let t=this._anyOutgoingListeners;for(let r=0;r<t.length;r++)if(e===t[r]){t.splice(r,1);break}}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length)for(let t of this._anyOutgoingListeners.slice())t.apply(this,e.data)}}function ek(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}ek.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),r=Math.floor(t*this.jitter*e);e=(1&Math.floor(10*t))==0?e-r:e+r}return 0|Math.min(e,this.max)},ek.prototype.reset=function(){this.attempts=0},ek.prototype.setMin=function(e){this.ms=e},ek.prototype.setMax=function(e){this.max=e},ek.prototype.setJitter=function(e){this.jitter=e};let eT=ea("socket.io-client:manager");class eA extends w{constructor(e,t){var r;super(),this.nsps={},this.subs=[],e&&"object"==typeof e&&(t=e,e=void 0),(t=t||{}).path=t.path||"/socket.io",this.opts=t,T(this,t),this.reconnection(!1!==t.reconnection),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor(null!==(r=t.randomizationFactor)&&void 0!==r?r:.5),this.backoff=new ek({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==t.timeout?2e4:t.timeout),this._readyState="closed",this.uri=e;let n=t.parser||o;this.encoder=new n.Encoder,this.decoder=new n.Decoder,this._autoConnect=!1!==t.autoConnect,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return void 0===e?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return void 0===e?this._reconnectionDelay:(this._reconnectionDelay=e,null===(t=this.backoff)||void 0===t||t.setMin(e),this)}randomizationFactor(e){var t;return void 0===e?this._randomizationFactor:(this._randomizationFactor=e,null===(t=this.backoff)||void 0===t||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return void 0===e?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,null===(t=this.backoff)||void 0===t||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(e){if(eT("readyState %s",this._readyState),~this._readyState.indexOf("open"))return this;eT("opening %s",this.uri),this.engine=new eo(this.uri,this.opts);let t=this.engine,r=this;this._readyState="opening",this.skipReconnect=!1;let n=e_(t,"open",function(){r.onopen(),e&&e()}),i=t=>{eT("error"),this.cleanup(),this._readyState="closed",this.emitReserved("error",t),e?e(t):this.maybeReconnectOnOpen()},s=e_(t,"error",i);if(!1!==this._timeout){let e=this._timeout;eT("connect attempt will timeout after %d",e);let r=this.setTimeoutFn(()=>{eT("connect attempt timed out after %d",e),n(),i(Error("timeout")),t.close()},e);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}return this.subs.push(n),this.subs.push(s),this}connect(e){return this.open(e)}onopen(){eT("open"),this.cleanup(),this._readyState="open",this.emitReserved("open");let e=this.engine;this.subs.push(e_(e,"ping",this.onping.bind(this)),e_(e,"data",this.ondata.bind(this)),e_(e,"error",this.onerror.bind(this)),e_(e,"close",this.onclose.bind(this)),e_(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(e){this.onclose("parse error",e)}}ondecoded(e){x(()=>{this.emitReserved("packet",e)},this.setTimeoutFn)}onerror(e){eT("error",e),this.emitReserved("error",e)}socket(e,t){let r=this.nsps[e];return r?this._autoConnect&&!r.active&&r.connect():(r=new eS(this,e,t),this.nsps[e]=r),r}_destroy(e){for(let e of Object.keys(this.nsps))if(this.nsps[e].active){eT("socket %s is still active, skipping close",e);return}this._close()}_packet(e){eT("writing packet %j",e);let t=this.encoder.encode(e);for(let r=0;r<t.length;r++)this.engine.write(t[r],e.options)}cleanup(){eT("cleanup"),this.subs.forEach(e=>e()),this.subs.length=0,this.decoder.destroy()}_close(){eT("disconnect"),this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,t){var r;eT("closed due to %s",e),this.cleanup(),null===(r=this.engine)||void 0===r||r.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;let e=this;if(this.backoff.attempts>=this._reconnectionAttempts)eT("reconnect failed"),this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{let t=this.backoff.duration();eT("will wait %dms before reconnect attempt",t),this._reconnecting=!0;let r=this.setTimeoutFn(()=>{!e.skipReconnect&&(eT("attempting reconnect"),this.emitReserved("reconnect_attempt",e.backoff.attempts),e.skipReconnect||e.open(t=>{t?(eT("reconnect attempt error"),e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",t)):(eT("reconnect success"),e.onreconnect())}))},t);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}}onreconnect(){let e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}let eR=ea("socket.io-client"),eP={};function eO(e,t){let r;"object"==typeof e&&(t=e,e=void 0);let n=function(e,t="",r){let n=e;r=r||"undefined"!=typeof location&&location,null==e&&(e=r.protocol+"//"+r.host),"string"==typeof e&&("/"===e.charAt(0)&&(e="/"===e.charAt(1)?r.protocol+e:r.host+e),/^(https?|wss?):\/\//.test(e)||(el("protocol-less url %s",e),e=void 0!==r?r.protocol+"//"+e:"https://"+e),el("parse %s",e),n=ee(e)),!n.port&&(/^(http|ws)$/.test(n.protocol)?n.port="80":/^(http|ws)s$/.test(n.protocol)&&(n.port="443")),n.path=n.path||"/";let i=-1!==n.host.indexOf(":")?"["+n.host+"]":n.host;return n.id=n.protocol+"://"+i+":"+n.port+t,n.href=n.protocol+"://"+i+(r&&r.port===n.port?"":":"+n.port),n}(e,(t=t||{}).path||"/socket.io"),i=n.source,s=n.id,o=n.path,a=eP[s]&&o in eP[s].nsps;return t.forceNew||t["force new connection"]||!1===t.multiplex||a?(eR("ignoring socket cache for %s",i),r=new eA(i,t)):(eP[s]||(eR("new io instance for %s",i),eP[s]=new eA(i,t)),r=eP[s]),n.query&&!t.query&&(t.query=n.queryKey),r.socket(n.path,t)}Object.assign(eO,{Manager:eA,Socket:eS,io:eO,connect:eO})},58869:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},58887:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("MessageSquare",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},63376:(e,t,r)=>{"use strict";r.d(t,{Eq:()=>c});var n=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},i=new WeakMap,s=new WeakMap,o={},a=0,l=function(e){return e&&(e.host||l(e.parentNode))},u=function(e,t,r,n){var u=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=l(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});o[r]||(o[r]=new WeakMap);var c=o[r],h=[],d=new Set,p=new Set(u),f=function(e){!(!e||d.has(e))&&(d.add(e),f(e.parentNode))};u.forEach(f);var m=function(e){!(!e||p.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(d.has(e))m(e);else try{var t=e.getAttribute(n),o=null!==t&&"false"!==t,a=(i.get(e)||0)+1,l=(c.get(e)||0)+1;i.set(e,a),c.set(e,l),h.push(e),1===a&&o&&s.set(e,!0),1===l&&e.setAttribute(r,"true"),o||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),d.clear(),a++,function(){h.forEach(function(e){var t=i.get(e)-1,o=c.get(e)-1;i.set(e,t),c.set(e,o),t||(s.has(e)||e.removeAttribute(n),s.delete(e)),o||e.removeAttribute(r)}),--a||(i=new WeakMap,i=new WeakMap,s=new WeakMap,o={})}},c=function(e,t,r){void 0===r&&(r="data-aria-hidden");var i=Array.from(Array.isArray(e)?e:[e]),s=t||n(e);return s?(i.push.apply(i,Array.from(s.querySelectorAll("[aria-live]"))),u(i,s,r,"aria-hidden")):function(){return null}}},64203:(e,t,r)=>{"use strict";let{Writable:n}=r(27910),i=r(2666),{BINARY_TYPES:s,EMPTY_BUFFER:o,kStatusCode:a,kWebSocket:l}=r(91813),{concat:u,toArrayBuffer:c,unmask:h}=r(10257),{isValidStatusCode:d,isValidUTF8:p}=r(37293),f=Buffer[Symbol.species];class m extends n{constructor(e={}){super(),this._allowSynchronousEvents=void 0===e.allowSynchronousEvents||e.allowSynchronousEvents,this._binaryType=e.binaryType||s[0],this._extensions=e.extensions||{},this._isServer=!!e.isServer,this._maxPayload=0|e.maxPayload,this._skipUTF8Validation=!!e.skipUTF8Validation,this[l]=void 0,this._bufferedBytes=0,this._buffers=[],this._compressed=!1,this._payloadLength=0,this._mask=void 0,this._fragmented=0,this._masked=!1,this._fin=!1,this._opcode=0,this._totalPayloadLength=0,this._messageLength=0,this._fragments=[],this._errored=!1,this._loop=!1,this._state=0}_write(e,t,r){if(8===this._opcode&&0==this._state)return r();this._bufferedBytes+=e.length,this._buffers.push(e),this.startLoop(r)}consume(e){if(this._bufferedBytes-=e,e===this._buffers[0].length)return this._buffers.shift();if(e<this._buffers[0].length){let t=this._buffers[0];return this._buffers[0]=new f(t.buffer,t.byteOffset+e,t.length-e),new f(t.buffer,t.byteOffset,e)}let t=Buffer.allocUnsafe(e);do{let r=this._buffers[0],n=t.length-e;e>=r.length?t.set(this._buffers.shift(),n):(t.set(new Uint8Array(r.buffer,r.byteOffset,e),n),this._buffers[0]=new f(r.buffer,r.byteOffset+e,r.length-e)),e-=r.length}while(e>0);return t}startLoop(e){this._loop=!0;do switch(this._state){case 0:this.getInfo(e);break;case 1:this.getPayloadLength16(e);break;case 2:this.getPayloadLength64(e);break;case 3:this.getMask();break;case 4:this.getData(e);break;case 5:case 6:this._loop=!1;return}while(this._loop);this._errored||e()}getInfo(e){if(this._bufferedBytes<2){this._loop=!1;return}let t=this.consume(2);if((48&t[0])!=0){e(this.createError(RangeError,"RSV2 and RSV3 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_2_3"));return}let r=(64&t[0])==64;if(r&&!this._extensions[i.extensionName]){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._fin=(128&t[0])==128,this._opcode=15&t[0],this._payloadLength=127&t[1],0===this._opcode){if(r){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(!this._fragmented){e(this.createError(RangeError,"invalid opcode 0",!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._opcode=this._fragmented}else if(1===this._opcode||2===this._opcode){if(this._fragmented){e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}this._compressed=r}else if(this._opcode>7&&this._opcode<11){if(!this._fin){e(this.createError(RangeError,"FIN must be set",!0,1002,"WS_ERR_EXPECTED_FIN"));return}if(r){e(this.createError(RangeError,"RSV1 must be clear",!0,1002,"WS_ERR_UNEXPECTED_RSV_1"));return}if(this._payloadLength>125||8===this._opcode&&1===this._payloadLength){e(this.createError(RangeError,`invalid payload length ${this._payloadLength}`,!0,1002,"WS_ERR_INVALID_CONTROL_PAYLOAD_LENGTH"));return}}else{e(this.createError(RangeError,`invalid opcode ${this._opcode}`,!0,1002,"WS_ERR_INVALID_OPCODE"));return}if(this._fin||this._fragmented||(this._fragmented=this._opcode),this._masked=(128&t[1])==128,this._isServer){if(!this._masked){e(this.createError(RangeError,"MASK must be set",!0,1002,"WS_ERR_EXPECTED_MASK"));return}}else if(this._masked){e(this.createError(RangeError,"MASK must be clear",!0,1002,"WS_ERR_UNEXPECTED_MASK"));return}126===this._payloadLength?this._state=1:127===this._payloadLength?this._state=2:this.haveLength(e)}getPayloadLength16(e){if(this._bufferedBytes<2){this._loop=!1;return}this._payloadLength=this.consume(2).readUInt16BE(0),this.haveLength(e)}getPayloadLength64(e){if(this._bufferedBytes<8){this._loop=!1;return}let t=this.consume(8),r=t.readUInt32BE(0);if(r>2097151){e(this.createError(RangeError,"Unsupported WebSocket frame: payload length > 2^53 - 1",!1,1009,"WS_ERR_UNSUPPORTED_DATA_PAYLOAD_LENGTH"));return}this._payloadLength=0x100000000*r+t.readUInt32BE(4),this.haveLength(e)}haveLength(e){if(this._payloadLength&&this._opcode<8&&(this._totalPayloadLength+=this._payloadLength,this._totalPayloadLength>this._maxPayload&&this._maxPayload>0)){e(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._masked?this._state=3:this._state=4}getMask(){if(this._bufferedBytes<4){this._loop=!1;return}this._mask=this.consume(4),this._state=4}getData(e){let t=o;if(this._payloadLength){if(this._bufferedBytes<this._payloadLength){this._loop=!1;return}t=this.consume(this._payloadLength),this._masked&&(this._mask[0]|this._mask[1]|this._mask[2]|this._mask[3])!=0&&h(t,this._mask)}if(this._opcode>7){this.controlMessage(t,e);return}if(this._compressed){this._state=5,this.decompress(t,e);return}t.length&&(this._messageLength=this._totalPayloadLength,this._fragments.push(t)),this.dataMessage(e)}decompress(e,t){this._extensions[i.extensionName].decompress(e,this._fin,(e,r)=>{if(e)return t(e);if(r.length){if(this._messageLength+=r.length,this._messageLength>this._maxPayload&&this._maxPayload>0){t(this.createError(RangeError,"Max payload size exceeded",!1,1009,"WS_ERR_UNSUPPORTED_MESSAGE_LENGTH"));return}this._fragments.push(r)}this.dataMessage(t),0===this._state&&this.startLoop(t)})}dataMessage(e){if(!this._fin){this._state=0;return}let t=this._messageLength,r=this._fragments;if(this._totalPayloadLength=0,this._messageLength=0,this._fragmented=0,this._fragments=[],2===this._opcode){let n;n="nodebuffer"===this._binaryType?u(r,t):"arraybuffer"===this._binaryType?c(u(r,t)):r,this._allowSynchronousEvents?(this.emit("message",n,!0),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",n,!0),this._state=0,this.startLoop(e)}))}else{let n=u(r,t);if(!this._skipUTF8Validation&&!p(n)){e(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}5===this._state||this._allowSynchronousEvents?(this.emit("message",n,!1),this._state=0):(this._state=6,setImmediate(()=>{this.emit("message",n,!1),this._state=0,this.startLoop(e)}))}}controlMessage(e,t){if(8===this._opcode){if(0===e.length)this._loop=!1,this.emit("conclude",1005,o),this.end();else{let r=e.readUInt16BE(0);if(!d(r)){t(this.createError(RangeError,`invalid status code ${r}`,!0,1002,"WS_ERR_INVALID_CLOSE_CODE"));return}let n=new f(e.buffer,e.byteOffset+2,e.length-2);if(!this._skipUTF8Validation&&!p(n)){t(this.createError(Error,"invalid UTF-8 sequence",!0,1007,"WS_ERR_INVALID_UTF8"));return}this._loop=!1,this.emit("conclude",r,n),this.end()}this._state=0;return}this._allowSynchronousEvents?(this.emit(9===this._opcode?"ping":"pong",e),this._state=0):(this._state=6,setImmediate(()=>{this.emit(9===this._opcode?"ping":"pong",e),this._state=0,this.startLoop(t)}))}createError(e,t,r,n,i){this._loop=!1,this._errored=!0;let s=new e(r?`Invalid WebSocket frame: ${t}`:t);return Error.captureStackTrace(s,this.createError),s.code=i,s[a]=n,s}}e.exports=m},65551:(e,t,r)=>{"use strict";r.d(t,{i:()=>s});var n=r(43210),i=r(13495);function s({prop:e,defaultProp:t,onChange:r=()=>{}}){let[s,o]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[s]=r,o=n.useRef(s),a=(0,i.c)(t);return n.useEffect(()=>{o.current!==s&&(a(s),o.current=s)},[s,o,a]),r}({defaultProp:t,onChange:r}),a=void 0!==e,l=a?e:s,u=(0,i.c)(r);return[l,n.useCallback(t=>{if(a){let r="function"==typeof t?t(e):t;r!==e&&u(r)}else o(t)},[a,e,o,u])]}},66156:(e,t,r)=>{"use strict";r.d(t,{N:()=>i});var n=r(43210),i=globalThis?.document?n.useLayoutEffect:()=>{}},67461:(e,t,r)=>{let n=r(83997),i=r(28354);t.init=function(e){e.inspectOpts={};let r=Object.keys(t.inspectOpts);for(let n=0;n<r.length;n++)e.inspectOpts[r[n]]=t.inspectOpts[r[n]]},t.log=function(...e){return process.stderr.write(i.formatWithOptions(t.inspectOpts,...e)+"\n")},t.formatArgs=function(r){let{namespace:n,useColors:i}=this;if(i){let t=this.color,i="\x1b[3"+(t<8?t:"8;5;"+t),s=`  ${i};1m${n} \u001B[0m`;r[0]=s+r[0].split("\n").join("\n"+s),r.push(i+"m+"+e.exports.humanize(this.diff)+"\x1b[0m")}else r[0]=(t.inspectOpts.hideDate?"":new Date().toISOString()+" ")+n+" "+r[0]},t.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},t.load=function(){return process.env.DEBUG},t.useColors=function(){return"colors"in t.inspectOpts?!!t.inspectOpts.colors:n.isatty(process.stderr.fd)},t.destroy=i.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),t.colors=[6,2,3,4,5,1];try{let e=r(39228);e&&(e.stderr||e).level>=2&&(t.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}t.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,t)=>{let r=t.substring(6).toLowerCase().replace(/_([a-z])/g,(e,t)=>t.toUpperCase()),n=process.env[t];return n=!!/^(yes|on|true|enabled)$/i.test(n)||!/^(no|off|false|disabled)$/i.test(n)&&("null"===n?null:Number(n)),e[r]=n,e},{}),e.exports=r(29940)(t);let{formatters:s}=e.exports;s.o=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts).split("\n").map(e=>e.trim()).join(" ")},s.O=function(e){return this.inspectOpts.colors=this.useColors,i.inspect(e,this.inspectOpts)}},67802:e=>{function t(e,t,r,n){return Math.round(e/r)+" "+n+(t>=1.5*r?"s":"")}e.exports=function(e,r){r=r||{};var n,i,s,o,a=typeof e;if("string"===a&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var r=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*r;case"weeks":case"week":case"w":return 6048e5*r;case"days":case"day":case"d":return 864e5*r;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*r;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*r;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*r;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}(e);if("number"===a&&isFinite(e)){return r.long?(i=Math.abs(n=e))>=864e5?t(n,i,864e5,"day"):i>=36e5?t(n,i,36e5,"hour"):i>=6e4?t(n,i,6e4,"minute"):i>=1e3?t(n,i,1e3,"second"):n+" ms":(o=Math.abs(s=e))>=864e5?Math.round(s/864e5)+"d":o>=36e5?Math.round(s/36e5)+"h":o>=6e4?Math.round(s/6e4)+"m":o>=1e3?Math.round(s/1e3)+"s":s+"ms"}throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},70569:(e,t,r)=>{"use strict";function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}r.d(t,{m:()=>n})},70946:(e,t,r)=>{"use strict";let{tokenChars:n}=r(37293);e.exports={parse:function(e){let t=new Set,r=-1,i=-1,s=0;for(;s<e.length;s++){let o=e.charCodeAt(s);if(-1===i&&1===n[o])-1===r&&(r=s);else if(0!==s&&(32===o||9===o))-1===i&&-1!==r&&(i=s);else if(44===o){if(-1===r)throw SyntaxError(`Unexpected character at index ${s}`);-1===i&&(i=s);let n=e.slice(r,i);if(t.has(n))throw SyntaxError(`The "${n}" subprotocol is duplicated`);t.add(n),r=i=-1}else throw SyntaxError(`Unexpected character at index ${s}`)}if(-1===r||-1!==i)throw SyntaxError("Unexpected end of input");let o=e.slice(r,s);if(t.has(o))throw SyntaxError(`The "${o}" subprotocol is duplicated`);return t.add(o),t}}},72635:(e,t,r)=>{"use strict";let n=r(94735),i=r(55591),s=r(81630),o=r(91645),a=r(34631),{randomBytes:l,createHash:u}=r(55511),{Duplex:c,Readable:h}=r(27910),{URL:d}=r(79551),p=r(2666),f=r(64203),m=r(36207),{BINARY_TYPES:g,EMPTY_BUFFER:y,GUID:v,kForOnEventAttribute:b,kListener:w,kStatusCode:x,kWebSocket:_,NOOP:C}=r(91813),{EventTarget:{addEventListener:E,removeEventListener:S}}=r(81548),{format:k,parse:T}=r(31637),{toBuffer:A}=r(10257),R=Symbol("kAborted"),P=[8,13],O=["CONNECTING","OPEN","CLOSING","CLOSED"],M=/^[!#$%&'*+\-.0-9A-Z^_`|a-z~]+$/;class L extends n{constructor(e,t,r){super(),this._binaryType=g[0],this._closeCode=1006,this._closeFrameReceived=!1,this._closeFrameSent=!1,this._closeMessage=y,this._closeTimer=null,this._extensions={},this._paused=!1,this._protocol="",this._readyState=L.CONNECTING,this._receiver=null,this._sender=null,this._socket=null,null!==e?(this._bufferedAmount=0,this._isServer=!1,this._redirects=0,void 0===t?t=[]:Array.isArray(t)||("object"==typeof t&&null!==t?(r=t,t=[]):t=[t]),function e(t,r,n,o){let a,c,h,f;let m={allowSynchronousEvents:!0,autoPong:!0,protocolVersion:P[1],maxPayload:0x6400000,skipUTF8Validation:!1,perMessageDeflate:!0,followRedirects:!1,maxRedirects:10,...o,socketPath:void 0,hostname:void 0,protocol:void 0,timeout:void 0,method:"GET",host:void 0,path:void 0,port:void 0};if(t._autoPong=m.autoPong,!P.includes(m.protocolVersion))throw RangeError(`Unsupported protocol version: ${m.protocolVersion} (supported versions: ${P.join(", ")})`);if(r instanceof d)a=r;else try{a=new d(r)}catch(e){throw SyntaxError(`Invalid URL: ${r}`)}"http:"===a.protocol?a.protocol="ws:":"https:"===a.protocol&&(a.protocol="wss:"),t._url=a.href;let g="wss:"===a.protocol,y="ws+unix:"===a.protocol;if("ws:"===a.protocol||g||y?y&&!a.pathname?c="The URL's pathname is empty":a.hash&&(c="The URL contains a fragment identifier"):c='The URL\'s protocol must be one of "ws:", "wss:", "http:", "https", or "ws+unix:"',c){let e=SyntaxError(c);if(0===t._redirects)throw e;F(t,e);return}let b=g?443:80,w=l(16).toString("base64"),x=g?i.request:s.request,_=new Set;if(m.createConnection=m.createConnection||(g?N:D),m.defaultPort=m.defaultPort||b,m.port=a.port||b,m.host=a.hostname.startsWith("[")?a.hostname.slice(1,-1):a.hostname,m.headers={...m.headers,"Sec-WebSocket-Version":m.protocolVersion,"Sec-WebSocket-Key":w,Connection:"Upgrade",Upgrade:"websocket"},m.path=a.pathname+a.search,m.timeout=m.handshakeTimeout,m.perMessageDeflate&&(h=new p(!0!==m.perMessageDeflate?m.perMessageDeflate:{},!1,m.maxPayload),m.headers["Sec-WebSocket-Extensions"]=k({[p.extensionName]:h.offer()})),n.length){for(let e of n){if("string"!=typeof e||!M.test(e)||_.has(e))throw SyntaxError("An invalid or duplicated subprotocol was specified");_.add(e)}m.headers["Sec-WebSocket-Protocol"]=n.join(",")}if(m.origin&&(m.protocolVersion<13?m.headers["Sec-WebSocket-Origin"]=m.origin:m.headers.Origin=m.origin),(a.username||a.password)&&(m.auth=`${a.username}:${a.password}`),y){let e=m.path.split(":");m.socketPath=e[0],m.path=e[1]}if(m.followRedirects){if(0===t._redirects){t._originalIpc=y,t._originalSecure=g,t._originalHostOrSocketPath=y?m.socketPath:a.host;let e=o&&o.headers;if(o={...o,headers:{}},e)for(let[t,r]of Object.entries(e))o.headers[t.toLowerCase()]=r}else if(0===t.listenerCount("redirect")){let e=y?!!t._originalIpc&&m.socketPath===t._originalHostOrSocketPath:!t._originalIpc&&a.host===t._originalHostOrSocketPath;e&&(!t._originalSecure||g)||(delete m.headers.authorization,delete m.headers.cookie,e||delete m.headers.host,m.auth=void 0)}m.auth&&!o.headers.authorization&&(o.headers.authorization="Basic "+Buffer.from(m.auth).toString("base64")),f=t._req=x(m),t._redirects&&t.emit("redirect",t.url,f)}else f=t._req=x(m);m.timeout&&f.on("timeout",()=>{j(t,f,"Opening handshake has timed out")}),f.on("error",e=>{null===f||f[R]||(f=t._req=null,F(t,e))}),f.on("response",i=>{let s=i.headers.location,a=i.statusCode;if(s&&m.followRedirects&&a>=300&&a<400){let i;if(++t._redirects>m.maxRedirects){j(t,f,"Maximum redirects exceeded");return}f.abort();try{i=new d(s,r)}catch(e){F(t,SyntaxError(`Invalid URL: ${s}`));return}e(t,i,n,o)}else t.emit("unexpected-response",f,i)||j(t,f,`Unexpected server response: ${i.statusCode}`)}),f.on("upgrade",(e,r,n)=>{let i;if(t.emit("upgrade",e),t.readyState!==L.CONNECTING)return;f=t._req=null;let s=e.headers.upgrade;if(void 0===s||"websocket"!==s.toLowerCase()){j(t,r,"Invalid Upgrade header");return}let o=u("sha1").update(w+v).digest("base64");if(e.headers["sec-websocket-accept"]!==o){j(t,r,"Invalid Sec-WebSocket-Accept header");return}let a=e.headers["sec-websocket-protocol"];if(void 0!==a?_.size?_.has(a)||(i="Server sent an invalid subprotocol"):i="Server sent a subprotocol but none was requested":_.size&&(i="Server sent no subprotocol"),i){j(t,r,i);return}a&&(t._protocol=a);let l=e.headers["sec-websocket-extensions"];if(void 0!==l){let e;if(!h){j(t,r,"Server sent a Sec-WebSocket-Extensions header but no extension was requested");return}try{e=T(l)}catch(e){j(t,r,"Invalid Sec-WebSocket-Extensions header");return}let n=Object.keys(e);if(1!==n.length||n[0]!==p.extensionName){j(t,r,"Server indicated an extension that was not requested");return}try{h.accept(e[p.extensionName])}catch(e){j(t,r,"Invalid Sec-WebSocket-Extensions header");return}t._extensions[p.extensionName]=h}t.setSocket(r,n,{allowSynchronousEvents:m.allowSynchronousEvents,generateMask:m.generateMask,maxPayload:m.maxPayload,skipUTF8Validation:m.skipUTF8Validation})}),m.finishRequest?m.finishRequest(f,t):f.end()}(this,e,t,r)):(this._autoPong=r.autoPong,this._isServer=!0)}get binaryType(){return this._binaryType}set binaryType(e){g.includes(e)&&(this._binaryType=e,this._receiver&&(this._receiver._binaryType=e))}get bufferedAmount(){return this._socket?this._socket._writableState.length+this._sender._bufferedBytes:this._bufferedAmount}get extensions(){return Object.keys(this._extensions).join()}get isPaused(){return this._paused}get onclose(){return null}get onerror(){return null}get onopen(){return null}get onmessage(){return null}get protocol(){return this._protocol}get readyState(){return this._readyState}get url(){return this._url}setSocket(e,t,r){let n=new f({allowSynchronousEvents:r.allowSynchronousEvents,binaryType:this.binaryType,extensions:this._extensions,isServer:this._isServer,maxPayload:r.maxPayload,skipUTF8Validation:r.skipUTF8Validation});this._sender=new m(e,this._extensions,r.generateMask),this._receiver=n,this._socket=e,n[_]=this,e[_]=this,n.on("conclude",I),n.on("drain",V),n.on("error",U),n.on("message",W),n.on("ping",q),n.on("pong",H),e.setTimeout&&e.setTimeout(0),e.setNoDelay&&e.setNoDelay(),t.length>0&&e.unshift(t),e.on("close",G),e.on("data",K),e.on("end",X),e.on("error",Y),this._readyState=L.OPEN,this.emit("open")}emitClose(){if(!this._socket){this._readyState=L.CLOSED,this.emit("close",this._closeCode,this._closeMessage);return}this._extensions[p.extensionName]&&this._extensions[p.extensionName].cleanup(),this._receiver.removeAllListeners(),this._readyState=L.CLOSED,this.emit("close",this._closeCode,this._closeMessage)}close(e,t){if(this.readyState!==L.CLOSED){if(this.readyState===L.CONNECTING){j(this,this._req,"WebSocket was closed before the connection was established");return}if(this.readyState===L.CLOSING){this._closeFrameSent&&(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end();return}this._readyState=L.CLOSING,this._sender.close(e,t,!this._isServer,e=>{!e&&(this._closeFrameSent=!0,(this._closeFrameReceived||this._receiver._writableState.errorEmitted)&&this._socket.end())}),this._closeTimer=setTimeout(this._socket.destroy.bind(this._socket),3e4)}}pause(){this.readyState!==L.CONNECTING&&this.readyState!==L.CLOSED&&(this._paused=!0,this._socket.pause())}ping(e,t,r){if(this.readyState===L.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(r=e,e=t=void 0):"function"==typeof t&&(r=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==L.OPEN){B(this,e,r);return}void 0===t&&(t=!this._isServer),this._sender.ping(e||y,t,r)}pong(e,t,r){if(this.readyState===L.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof e?(r=e,e=t=void 0):"function"==typeof t&&(r=t,t=void 0),"number"==typeof e&&(e=e.toString()),this.readyState!==L.OPEN){B(this,e,r);return}void 0===t&&(t=!this._isServer),this._sender.pong(e||y,t,r)}resume(){this.readyState!==L.CONNECTING&&this.readyState!==L.CLOSED&&(this._paused=!1,this._receiver._writableState.needDrain||this._socket.resume())}send(e,t,r){if(this.readyState===L.CONNECTING)throw Error("WebSocket is not open: readyState 0 (CONNECTING)");if("function"==typeof t&&(r=t,t={}),"number"==typeof e&&(e=e.toString()),this.readyState!==L.OPEN){B(this,e,r);return}let n={binary:"string"!=typeof e,mask:!this._isServer,compress:!0,fin:!0,...t};this._extensions[p.extensionName]||(n.compress=!1),this._sender.send(e||y,n,r)}terminate(){if(this.readyState!==L.CLOSED){if(this.readyState===L.CONNECTING){j(this,this._req,"WebSocket was closed before the connection was established");return}this._socket&&(this._readyState=L.CLOSING,this._socket.destroy())}}}function F(e,t){e._readyState=L.CLOSING,e.emit("error",t),e.emitClose()}function D(e){return e.path=e.socketPath,o.connect(e)}function N(e){return e.path=void 0,e.servername||""===e.servername||(e.servername=o.isIP(e.host)?"":e.host),a.connect(e)}function j(e,t,r){e._readyState=L.CLOSING;let n=Error(r);Error.captureStackTrace(n,j),t.setHeader?(t[R]=!0,t.abort(),t.socket&&!t.socket.destroyed&&t.socket.destroy(),process.nextTick(F,e,n)):(t.destroy(n),t.once("error",e.emit.bind(e,"error")),t.once("close",e.emitClose.bind(e)))}function B(e,t,r){if(t){let r=A(t).length;e._socket?e._sender._bufferedBytes+=r:e._bufferedAmount+=r}if(r){let t=Error(`WebSocket is not open: readyState ${e.readyState} (${O[e.readyState]})`);process.nextTick(r,t)}}function I(e,t){let r=this[_];r._closeFrameReceived=!0,r._closeMessage=t,r._closeCode=e,void 0!==r._socket[_]&&(r._socket.removeListener("data",K),process.nextTick(z,r._socket),1005===e?r.close():r.close(e,t))}function V(){let e=this[_];e.isPaused||e._socket.resume()}function U(e){let t=this[_];void 0!==t._socket[_]&&(t._socket.removeListener("data",K),process.nextTick(z,t._socket),t.close(e[x])),t.emit("error",e)}function $(){this[_].emitClose()}function W(e,t){this[_].emit("message",e,t)}function q(e){let t=this[_];t._autoPong&&t.pong(e,!this._isServer,C),t.emit("ping",e)}function H(e){this[_].emit("pong",e)}function z(e){e.resume()}function G(){let e;let t=this[_];this.removeListener("close",G),this.removeListener("data",K),this.removeListener("end",X),t._readyState=L.CLOSING,this._readableState.endEmitted||t._closeFrameReceived||t._receiver._writableState.errorEmitted||null===(e=t._socket.read())||t._receiver.write(e),t._receiver.end(),this[_]=void 0,clearTimeout(t._closeTimer),t._receiver._writableState.finished||t._receiver._writableState.errorEmitted?t.emitClose():(t._receiver.on("error",$),t._receiver.on("finish",$))}function K(e){this[_]._receiver.write(e)||this.pause()}function X(){let e=this[_];e._readyState=L.CLOSING,e._receiver.end(),this.end()}function Y(){let e=this[_];this.removeListener("error",Y),this.on("error",C),e&&(e._readyState=L.CLOSING,this.destroy())}Object.defineProperty(L,"CONNECTING",{enumerable:!0,value:O.indexOf("CONNECTING")}),Object.defineProperty(L.prototype,"CONNECTING",{enumerable:!0,value:O.indexOf("CONNECTING")}),Object.defineProperty(L,"OPEN",{enumerable:!0,value:O.indexOf("OPEN")}),Object.defineProperty(L.prototype,"OPEN",{enumerable:!0,value:O.indexOf("OPEN")}),Object.defineProperty(L,"CLOSING",{enumerable:!0,value:O.indexOf("CLOSING")}),Object.defineProperty(L.prototype,"CLOSING",{enumerable:!0,value:O.indexOf("CLOSING")}),Object.defineProperty(L,"CLOSED",{enumerable:!0,value:O.indexOf("CLOSED")}),Object.defineProperty(L.prototype,"CLOSED",{enumerable:!0,value:O.indexOf("CLOSED")}),["binaryType","bufferedAmount","extensions","isPaused","protocol","readyState","url"].forEach(e=>{Object.defineProperty(L.prototype,e,{enumerable:!0})}),["open","error","close","message"].forEach(e=>{Object.defineProperty(L.prototype,`on${e}`,{enumerable:!0,get(){for(let t of this.listeners(e))if(t[b])return t[w];return null},set(t){for(let t of this.listeners(e))if(t[b]){this.removeListener(e,t);break}"function"==typeof t&&this.addEventListener(e,t,{[b]:!0})}})}),L.prototype.addEventListener=E,L.prototype.removeEventListener=S,e.exports=L},72942:(e,t,r)=>{"use strict";r.d(t,{RG:()=>x,bL:()=>P,q7:()=>O});var n=r(43210),i=r(70569),s=r(9510),o=r(98599),a=r(11273),l=r(96963),u=r(14163),c=r(13495),h=r(65551),d=r(43),p=r(60687),f="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[y,v,b]=(0,s.N)(g),[w,x]=(0,a.A)(g,[b]),[_,C]=w(g),E=n.forwardRef((e,t)=>(0,p.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(S,{...e,ref:t})})}));E.displayName=g;var S=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:s,loop:a=!1,dir:l,currentTabStopId:g,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:b,onEntryFocus:w,preventScrollOnEntryFocus:x=!1,...C}=e,E=n.useRef(null),S=(0,o.s)(t,E),k=(0,d.jH)(l),[T=null,A]=(0,h.i)({prop:g,defaultProp:y,onChange:b}),[P,O]=n.useState(!1),M=(0,c.c)(w),L=v(r),F=n.useRef(!1),[D,N]=n.useState(0);return n.useEffect(()=>{let e=E.current;if(e)return e.addEventListener(f,M),()=>e.removeEventListener(f,M)},[M]),(0,p.jsx)(_,{scope:r,orientation:s,dir:k,loop:a,currentTabStopId:T,onItemFocus:n.useCallback(e=>A(e),[A]),onItemShiftTab:n.useCallback(()=>O(!0),[]),onFocusableItemAdd:n.useCallback(()=>N(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>N(e=>e-1),[]),children:(0,p.jsx)(u.sG.div,{tabIndex:P||0===D?-1:0,"data-orientation":s,...C,ref:S,style:{outline:"none",...e.style},onMouseDown:(0,i.m)(e.onMouseDown,()=>{F.current=!0}),onFocus:(0,i.m)(e.onFocus,e=>{let t=!F.current;if(e.target===e.currentTarget&&t&&!P){let t=new CustomEvent(f,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=L().filter(e=>e.focusable);R([e.find(e=>e.active),e.find(e=>e.id===T),...e].filter(Boolean).map(e=>e.ref.current),x)}}F.current=!1}),onBlur:(0,i.m)(e.onBlur,()=>O(!1))})})}),k="RovingFocusGroupItem",T=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:s=!0,active:o=!1,tabStopId:a,...c}=e,h=(0,l.B)(),d=a||h,f=C(k,r),m=f.currentTabStopId===d,g=v(r),{onFocusableItemAdd:b,onFocusableItemRemove:w}=f;return n.useEffect(()=>{if(s)return b(),()=>w()},[s,b,w]),(0,p.jsx)(y.ItemSlot,{scope:r,id:d,focusable:s,active:o,children:(0,p.jsx)(u.sG.span,{tabIndex:m?0:-1,"data-orientation":f.orientation,...c,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{s?f.onItemFocus(d):e.preventDefault()}),onFocus:(0,i.m)(e.onFocus,()=>f.onItemFocus(d)),onKeyDown:(0,i.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){f.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let i=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(i)))return A[i]}(e,f.orientation,f.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=f.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>R(r))}})})})});T.displayName=k;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function R(e,t=!1){let r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var P=E,O=T},76420:(e,t,r)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let r="color: "+this.color;t.splice(1,0,r,"color: inherit");let n=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(i=n))}),t.splice(i,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=r(43095)(t);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},78200:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},81548:(e,t,r)=>{"use strict";let{kForOnEventAttribute:n,kListener:i}=r(91813),s=Symbol("kCode"),o=Symbol("kData"),a=Symbol("kError"),l=Symbol("kMessage"),u=Symbol("kReason"),c=Symbol("kTarget"),h=Symbol("kType"),d=Symbol("kWasClean");class p{constructor(e){this[c]=null,this[h]=e}get target(){return this[c]}get type(){return this[h]}}Object.defineProperty(p.prototype,"target",{enumerable:!0}),Object.defineProperty(p.prototype,"type",{enumerable:!0});class f extends p{constructor(e,t={}){super(e),this[s]=void 0===t.code?0:t.code,this[u]=void 0===t.reason?"":t.reason,this[d]=void 0!==t.wasClean&&t.wasClean}get code(){return this[s]}get reason(){return this[u]}get wasClean(){return this[d]}}Object.defineProperty(f.prototype,"code",{enumerable:!0}),Object.defineProperty(f.prototype,"reason",{enumerable:!0}),Object.defineProperty(f.prototype,"wasClean",{enumerable:!0});class m extends p{constructor(e,t={}){super(e),this[a]=void 0===t.error?null:t.error,this[l]=void 0===t.message?"":t.message}get error(){return this[a]}get message(){return this[l]}}Object.defineProperty(m.prototype,"error",{enumerable:!0}),Object.defineProperty(m.prototype,"message",{enumerable:!0});class g extends p{constructor(e,t={}){super(e),this[o]=void 0===t.data?null:t.data}get data(){return this[o]}}function y(e,t,r){"object"==typeof e&&e.handleEvent?e.handleEvent.call(e,r):e.call(t,r)}Object.defineProperty(g.prototype,"data",{enumerable:!0}),e.exports={CloseEvent:f,ErrorEvent:m,Event:p,EventTarget:{addEventListener(e,t,r={}){let s;for(let s of this.listeners(e))if(!r[n]&&s[i]===t&&!s[n])return;if("message"===e)s=function(e,r){let n=new g("message",{data:r?e:e.toString()});n[c]=this,y(t,this,n)};else if("close"===e)s=function(e,r){let n=new f("close",{code:e,reason:r.toString(),wasClean:this._closeFrameReceived&&this._closeFrameSent});n[c]=this,y(t,this,n)};else if("error"===e)s=function(e){let r=new m("error",{error:e,message:e.message});r[c]=this,y(t,this,r)};else{if("open"!==e)return;s=function(){let e=new p("open");e[c]=this,y(t,this,e)}}s[n]=!!r[n],s[i]=t,r.once?this.once(e,s):this.on(e,s)},removeEventListener(e,t){for(let r of this.listeners(e))if(r[i]===t&&!r[n]){this.removeListener(e,r);break}}},MessageEvent:g}},82854:(e,t,r)=>{"use strict";let n;function i(e){return null!==e&&"object"==typeof e&&"function"==typeof e.start}function s(e){let t=[{},{}];return null==e||e.values.forEach((e,r)=>{t[0][r]=e.get(),t[1][r]=e.getVelocity()}),t}function o(e,t,r,n){if("function"==typeof t){let[i,o]=s(n);t=t(void 0!==r?r:e.custom,i,o)}if("string"==typeof t&&(t=e.variants&&e.variants[t]),"function"==typeof t){let[i,o]=s(n);t=t(void 0!==r?r:e.custom,i,o)}return t}function a(e,t,r){let n=e.getProps();return o(n,t,void 0!==r?r:n.custom,e)}function l(e){let t;return()=>(void 0===t&&(t=e()),t)}r.d(t,{P:()=>sg});let u=l(()=>void 0!==window.ScrollTimeline);class c{constructor(e){this.stop=()=>this.runAll("stop"),this.animations=e.filter(Boolean)}get finished(){return Promise.all(this.animations.map(e=>"finished"in e?e.finished:e))}getAll(e){return this.animations[0][e]}setAll(e,t){for(let r=0;r<this.animations.length;r++)this.animations[r][e]=t}attachTimeline(e,t){let r=this.animations.map(r=>u()&&r.attachTimeline?r.attachTimeline(e):"function"==typeof t?t(r):void 0);return()=>{r.forEach((e,t)=>{e&&e(),this.animations[t].stop()})}}get time(){return this.getAll("time")}set time(e){this.setAll("time",e)}get speed(){return this.getAll("speed")}set speed(e){this.setAll("speed",e)}get startTime(){return this.getAll("startTime")}get duration(){let e=0;for(let t=0;t<this.animations.length;t++)e=Math.max(e,this.animations[t].duration);return e}runAll(e){this.animations.forEach(t=>t[e]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class h extends c{then(e,t){return Promise.all(this.animations).then(e).catch(t)}}function d(e,t){return e?e[t]||e.default||e:void 0}function p(e){let t=0,r=e.next(t);for(;!r.done&&t<2e4;)t+=50,r=e.next(t);return t>=2e4?1/0:t}function f(e){return"function"==typeof e}function m(e,t){e.timeline=t,e.onfinish=null}let g=e=>Array.isArray(e)&&"number"==typeof e[0],y={linearEasing:void 0},v=function(e,t){let r=l(e);return()=>{var e;return null!==(e=y[t])&&void 0!==e?e:r()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(e){return!1}return!0},"linearEasing"),b=(e,t,r)=>{let n=t-e;return 0===n?1:(r-e)/n},w=(e,t,r=10)=>{let n="",i=Math.max(Math.round(t/r),2);for(let t=0;t<i;t++)n+=e(b(0,i-1,t))+", ";return`linear(${n.substring(0,n.length-2)})`},x=([e,t,r,n])=>`cubic-bezier(${e}, ${t}, ${r}, ${n})`,_={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:x([0,.65,.55,1]),circOut:x([.55,0,1,.45]),backIn:x([.31,.01,.66,-.59]),backOut:x([.33,1.53,.69,.99])},C={x:!1,y:!1};function E(e,t){let r=function(e,t,r){var n;if(e instanceof EventTarget)return[e];if("string"==typeof e){let t=document,r=(n=void 0,t.querySelectorAll(e));return r?Array.from(r):[]}return Array.from(e)}(e),n=new AbortController;return[r,{passive:!0,...t,signal:n.signal},()=>n.abort()]}function S(e){return!("touch"===e.pointerType||C.x||C.y)}function k(e,t){let r=`${t}PointerCapture`;if(e.target instanceof Element&&r in e.target&&void 0!==e.pointerId)try{e.target[r](e.pointerId)}catch(e){}}let T=(e,t)=>!!t&&(e===t||T(e,t.parentElement)),A=e=>"mouse"===e.pointerType?"number"!=typeof e.button||e.button<=0:!1!==e.isPrimary,R=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),P=new WeakSet;function O(e){return t=>{"Enter"===t.key&&e(t)}}function M(e,t){e.dispatchEvent(new PointerEvent("pointer"+t,{isPrimary:!0,bubbles:!0}))}let L=(e,t)=>{let r=e.currentTarget;if(!r)return;let n=O(()=>{if(P.has(r))return;M(r,"down");let e=O(()=>{M(r,"up")});r.addEventListener("keyup",e,t),r.addEventListener("blur",()=>M(r,"cancel"),t)});r.addEventListener("keydown",n,t),r.addEventListener("blur",()=>r.removeEventListener("keydown",n),t)};function F(e){return A(e)&&!(C.x||C.y)}let D=e=>1e3*e,N=e=>e/1e3,j=e=>e,B=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],I=new Set(B),V=new Set(["width","height","top","left","right","bottom",...B]),U=e=>Array.isArray(e),$=e=>!!(e&&"object"==typeof e&&e.mix&&e.toValue),W=e=>U(e)?e[e.length-1]||0:e,q={skipAnimations:!1,useManualTiming:!1},H=["read","resolveKeyframes","update","preRender","render","postRender"],z={value:null,addProjectionMetrics:null};function G(e,t){let r=!1,n=!0,i={delta:0,timestamp:0,isProcessing:!1},s=()=>r=!0,o=H.reduce((e,r)=>(e[r]=function(e,t){let r=new Set,n=new Set,i=!1,s=!1,o=new WeakSet,a={delta:0,timestamp:0,isProcessing:!1},l=0;function u(t){o.has(t)&&(c.schedule(t),e()),l++,t(a)}let c={schedule:(e,t=!1,s=!1)=>{let a=s&&i?r:n;return t&&o.add(e),a.has(e)||a.add(e),e},cancel:e=>{n.delete(e),o.delete(e)},process:e=>{if(a=e,i){s=!0;return}i=!0,[r,n]=[n,r],r.forEach(u),t&&z.value&&z.value.frameloop[t].push(l),l=0,r.clear(),i=!1,s&&(s=!1,c.process(e))}};return c}(s,t?r:void 0),e),{}),{read:a,resolveKeyframes:l,update:u,preRender:c,render:h,postRender:d}=o,p=()=>{let s=q.useManualTiming?i.timestamp:performance.now();r=!1,q.useManualTiming||(i.delta=n?1e3/60:Math.max(Math.min(s-i.timestamp,40),1)),i.timestamp=s,i.isProcessing=!0,a.process(i),l.process(i),u.process(i),c.process(i),h.process(i),d.process(i),i.isProcessing=!1,r&&t&&(n=!1,e(p))},f=()=>{r=!0,n=!0,i.isProcessing||e(p)};return{schedule:H.reduce((e,t)=>{let n=o[t];return e[t]=(e,t=!1,i=!1)=>(r||f(),n.schedule(e,t,i)),e},{}),cancel:e=>{for(let t=0;t<H.length;t++)o[H[t]].cancel(e)},state:i,steps:o}}let{schedule:K,cancel:X,state:Y,steps:Z}=G("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:j,!0);function J(){n=void 0}let Q={now:()=>(void 0===n&&Q.set(Y.isProcessing||q.useManualTiming?Y.timestamp:performance.now()),n),set:e=>{n=e,queueMicrotask(J)}};function ee(e,t){-1===e.indexOf(t)&&e.push(t)}function et(e,t){let r=e.indexOf(t);r>-1&&e.splice(r,1)}class er{constructor(){this.subscriptions=[]}add(e){return ee(this.subscriptions,e),()=>et(this.subscriptions,e)}notify(e,t,r){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](e,t,r);else for(let i=0;i<n;i++){let n=this.subscriptions[i];n&&n(e,t,r)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let en=e=>!isNaN(parseFloat(e)),ei={current:void 0};class es{constructor(e,t={}){this.version="12.4.10",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(e,t=!0)=>{let r=Q.now();this.updatedAt!==r&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(e),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),t&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(e),this.owner=t.owner}setCurrent(e){this.current=e,this.updatedAt=Q.now(),null===this.canTrackVelocity&&void 0!==e&&(this.canTrackVelocity=en(this.current))}setPrevFrameValue(e=this.current){this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt}onChange(e){return this.on("change",e)}on(e,t){this.events[e]||(this.events[e]=new er);let r=this.events[e].add(t);return"change"===e?()=>{r(),K.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,t){this.passiveEffect=e,this.stopPassiveEffect=t}set(e,t=!0){t&&this.passiveEffect?this.passiveEffect(e,this.updateAndNotify):this.updateAndNotify(e,t)}setWithVelocity(e,t,r){this.set(t),this.prev=void 0,this.prevFrameValue=e,this.prevUpdatedAt=this.updatedAt-r}jump(e,t=!0){this.updateAndNotify(e),this.prev=e,this.prevUpdatedAt=this.prevFrameValue=void 0,t&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return ei.current&&ei.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var e;let t=Q.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let r=Math.min(this.updatedAt-this.prevUpdatedAt,30);return e=parseFloat(this.current)-parseFloat(this.prevFrameValue),r?1e3/r*e:0}start(e){return this.stop(),new Promise(t=>{this.hasAnimated=!0,this.animation=e(t),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function eo(e,t){return new es(e,t)}let ea=e=>!!(e&&e.getVelocity);function el(e,t){let r=e.getValue("willChange");if(ea(r)&&r.add)return r.add(t)}let eu=e=>e.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),ec="data-"+eu("framerAppearId"),eh={current:!1},ed=(e,t,r)=>(((1-3*r+3*t)*e+(3*r-6*t))*e+3*t)*e;function ep(e,t,r,n){if(e===t&&r===n)return j;let i=t=>(function(e,t,r,n,i){let s,o;let a=0;do(s=ed(o=t+(r-t)/2,n,i)-e)>0?r=o:t=o;while(Math.abs(s)>1e-7&&++a<12);return o})(t,0,1,e,r);return e=>0===e||1===e?e:ed(i(e),t,n)}let ef=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,em=e=>t=>1-e(1-t),eg=ep(.33,1.53,.69,.99),ey=em(eg),ev=ef(ey),eb=e=>(e*=2)<1?.5*ey(e):.5*(2-Math.pow(2,-10*(e-1))),ew=e=>1-Math.sin(Math.acos(e)),ex=em(ew),e_=ef(ew),eC=e=>/^0[^.\s]+$/u.test(e),eE=(e,t,r)=>r>t?t:r<e?e:r,eS={test:e=>"number"==typeof e,parse:parseFloat,transform:e=>e},ek={...eS,transform:e=>eE(0,1,e)},eT={...eS,default:1},eA=e=>Math.round(1e5*e)/1e5,eR=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,eP=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,eO=(e,t)=>r=>!!("string"==typeof r&&eP.test(r)&&r.startsWith(e)||t&&null!=r&&Object.prototype.hasOwnProperty.call(r,t)),eM=(e,t,r)=>n=>{if("string"!=typeof n)return n;let[i,s,o,a]=n.match(eR);return{[e]:parseFloat(i),[t]:parseFloat(s),[r]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},eL=e=>eE(0,255,e),eF={...eS,transform:e=>Math.round(eL(e))},eD={test:eO("rgb","red"),parse:eM("red","green","blue"),transform:({red:e,green:t,blue:r,alpha:n=1})=>"rgba("+eF.transform(e)+", "+eF.transform(t)+", "+eF.transform(r)+", "+eA(ek.transform(n))+")"},eN={test:eO("#"),parse:function(e){let t="",r="",n="",i="";return e.length>5?(t=e.substring(1,3),r=e.substring(3,5),n=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),r=e.substring(2,3),n=e.substring(3,4),i=e.substring(4,5),t+=t,r+=r,n+=n,i+=i),{red:parseInt(t,16),green:parseInt(r,16),blue:parseInt(n,16),alpha:i?parseInt(i,16)/255:1}},transform:eD.transform},ej=e=>({test:t=>"string"==typeof t&&t.endsWith(e)&&1===t.split(" ").length,parse:parseFloat,transform:t=>`${t}${e}`}),eB=ej("deg"),eI=ej("%"),eV=ej("px"),eU=ej("vh"),e$=ej("vw"),eW={...eI,parse:e=>eI.parse(e)/100,transform:e=>eI.transform(100*e)},eq={test:eO("hsl","hue"),parse:eM("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:r,alpha:n=1})=>"hsla("+Math.round(e)+", "+eI.transform(eA(t))+", "+eI.transform(eA(r))+", "+eA(ek.transform(n))+")"},eH={test:e=>eD.test(e)||eN.test(e)||eq.test(e),parse:e=>eD.test(e)?eD.parse(e):eq.test(e)?eq.parse(e):eN.parse(e),transform:e=>"string"==typeof e?e:e.hasOwnProperty("red")?eD.transform(e):eq.transform(e)},ez=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,eG="number",eK="color",eX=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function eY(e){let t=e.toString(),r=[],n={color:[],number:[],var:[]},i=[],s=0,o=t.replace(eX,e=>(eH.test(e)?(n.color.push(s),i.push(eK),r.push(eH.parse(e))):e.startsWith("var(")?(n.var.push(s),i.push("var"),r.push(e)):(n.number.push(s),i.push(eG),r.push(parseFloat(e))),++s,"${}")).split("${}");return{values:r,split:o,indexes:n,types:i}}function eZ(e){return eY(e).values}function eJ(e){let{split:t,types:r}=eY(e),n=t.length;return e=>{let i="";for(let s=0;s<n;s++)if(i+=t[s],void 0!==e[s]){let t=r[s];t===eG?i+=eA(e[s]):t===eK?i+=eH.transform(e[s]):i+=e[s]}return i}}let eQ=e=>"number"==typeof e?0:e,e0={test:function(e){var t,r;return isNaN(e)&&"string"==typeof e&&((null===(t=e.match(eR))||void 0===t?void 0:t.length)||0)+((null===(r=e.match(ez))||void 0===r?void 0:r.length)||0)>0},parse:eZ,createTransformer:eJ,getAnimatableNone:function(e){let t=eZ(e);return eJ(e)(t.map(eQ))}},e1=new Set(["brightness","contrast","saturate","opacity"]);function e2(e){let[t,r]=e.slice(0,-1).split("(");if("drop-shadow"===t)return e;let[n]=r.match(eR)||[];if(!n)return e;let i=r.replace(n,""),s=+!!e1.has(t);return n!==r&&(s*=100),t+"("+s+i+")"}let e3=/\b([a-z-]*)\(.*?\)/gu,e6={...e0,getAnimatableNone:e=>{let t=e.match(e3);return t?t.map(e2).join(" "):e}},e9={...eS,transform:Math.round},e4={borderWidth:eV,borderTopWidth:eV,borderRightWidth:eV,borderBottomWidth:eV,borderLeftWidth:eV,borderRadius:eV,radius:eV,borderTopLeftRadius:eV,borderTopRightRadius:eV,borderBottomRightRadius:eV,borderBottomLeftRadius:eV,width:eV,maxWidth:eV,height:eV,maxHeight:eV,top:eV,right:eV,bottom:eV,left:eV,padding:eV,paddingTop:eV,paddingRight:eV,paddingBottom:eV,paddingLeft:eV,margin:eV,marginTop:eV,marginRight:eV,marginBottom:eV,marginLeft:eV,backgroundPositionX:eV,backgroundPositionY:eV,rotate:eB,rotateX:eB,rotateY:eB,rotateZ:eB,scale:eT,scaleX:eT,scaleY:eT,scaleZ:eT,skew:eB,skewX:eB,skewY:eB,distance:eV,translateX:eV,translateY:eV,translateZ:eV,x:eV,y:eV,z:eV,perspective:eV,transformPerspective:eV,opacity:ek,originX:eW,originY:eW,originZ:eV,zIndex:e9,size:eV,fillOpacity:ek,strokeOpacity:ek,numOctaves:e9},e5={...e4,color:eH,backgroundColor:eH,outlineColor:eH,fill:eH,stroke:eH,borderColor:eH,borderTopColor:eH,borderRightColor:eH,borderBottomColor:eH,borderLeftColor:eH,filter:e6,WebkitFilter:e6},e8=e=>e5[e];function e7(e,t){let r=e8(e);return r!==e6&&(r=e0),r.getAnimatableNone?r.getAnimatableNone(t):void 0}let te=new Set(["auto","none","0"]),tt=e=>e===eS||e===eV,tr=(e,t)=>parseFloat(e.split(", ")[t]),tn=(e,t)=>(r,{transform:n})=>{if("none"===n||!n)return 0;let i=n.match(/^matrix3d\((.+)\)$/u);if(i)return tr(i[1],t);{let t=n.match(/^matrix\((.+)\)$/u);return t?tr(t[1],e):0}},ti=new Set(["x","y","z"]),ts=B.filter(e=>!ti.has(e)),to={width:({x:e},{paddingLeft:t="0",paddingRight:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),height:({y:e},{paddingTop:t="0",paddingBottom:r="0"})=>e.max-e.min-parseFloat(t)-parseFloat(r),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:tn(4,13),y:tn(5,14)};to.translateX=to.x,to.translateY=to.y;let ta=new Set,tl=!1,tu=!1;function tc(){if(tu){let e=Array.from(ta).filter(e=>e.needsMeasurement),t=new Set(e.map(e=>e.element)),r=new Map;t.forEach(e=>{let t=function(e){let t=[];return ts.forEach(r=>{let n=e.getValue(r);void 0!==n&&(t.push([r,n.get()]),n.set(+!!r.startsWith("scale")))}),t}(e);t.length&&(r.set(e,t),e.render())}),e.forEach(e=>e.measureInitialState()),t.forEach(e=>{e.render();let t=r.get(e);t&&t.forEach(([t,r])=>{var n;null===(n=e.getValue(t))||void 0===n||n.set(r)})}),e.forEach(e=>e.measureEndState()),e.forEach(e=>{void 0!==e.suspendedScrollY&&window.scrollTo(0,e.suspendedScrollY)})}tu=!1,tl=!1,ta.forEach(e=>e.complete()),ta.clear()}function th(){ta.forEach(e=>{e.readKeyframes(),e.needsMeasurement&&(tu=!0)})}class td{constructor(e,t,r,n,i,s=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...e],this.onComplete=t,this.name=r,this.motionValue=n,this.element=i,this.isAsync=s}scheduleResolve(){this.isScheduled=!0,this.isAsync?(ta.add(this),tl||(tl=!0,K.read(th),K.resolveKeyframes(tc))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:e,name:t,element:r,motionValue:n}=this;for(let i=0;i<e.length;i++)if(null===e[i]){if(0===i){let i=null==n?void 0:n.get(),s=e[e.length-1];if(void 0!==i)e[0]=i;else if(r&&t){let n=r.readValue(t,s);null!=n&&(e[0]=n)}void 0===e[0]&&(e[0]=s),n&&void 0===i&&n.set(e[0])}else e[i]=e[i-1]}}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),ta.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,ta.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let tp=e=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(e),tf=e=>t=>"string"==typeof t&&t.startsWith(e),tm=tf("--"),tg=tf("var(--"),ty=e=>!!tg(e)&&tv.test(e.split("/*")[0].trim()),tv=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,tb=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,tw=e=>t=>t.test(e),tx=[eS,eV,eI,eB,e$,eU,{test:e=>"auto"===e,parse:e=>e}],t_=e=>tx.find(tw(e));class tC extends td{constructor(e,t,r,n,i){super(e,t,r,n,i,!0)}readKeyframes(){let{unresolvedKeyframes:e,element:t,name:r}=this;if(!t||!t.current)return;super.readKeyframes();for(let r=0;r<e.length;r++){let n=e[r];if("string"==typeof n&&ty(n=n.trim())){let i=function e(t,r,n=1){j(n<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,s]=function(e){let t=tb.exec(e);if(!t)return[,];let[,r,n,i]=t;return[`--${null!=r?r:n}`,i]}(t);if(!i)return;let o=window.getComputedStyle(r).getPropertyValue(i);if(o){let e=o.trim();return tp(e)?parseFloat(e):e}return ty(s)?e(s,r,n+1):s}(n,t.current);void 0!==i&&(e[r]=i),r===e.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!V.has(r)||2!==e.length)return;let[n,i]=e,s=t_(n),o=t_(i);if(s!==o){if(tt(s)&&tt(o))for(let t=0;t<e.length;t++){let r=e[t];"string"==typeof r&&(e[t]=parseFloat(r))}else this.needsMeasurement=!0}}resolveNoneKeyframes(){let{unresolvedKeyframes:e,name:t}=this,r=[];for(let t=0;t<e.length;t++){var n;("number"==typeof(n=e[t])?0===n:null===n||"none"===n||"0"===n||eC(n))&&r.push(t)}r.length&&function(e,t,r){let n,i=0;for(;i<e.length&&!n;){let t=e[i];"string"==typeof t&&!te.has(t)&&eY(t).values.length&&(n=e[i]),i++}if(n&&r)for(let i of t)e[i]=e7(r,n)}(e,r,t)}measureInitialState(){let{element:e,unresolvedKeyframes:t,name:r}=this;if(!e||!e.current)return;"height"===r&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=to[r](e.measureViewportBox(),window.getComputedStyle(e.current)),t[0]=this.measuredOrigin;let n=t[t.length-1];void 0!==n&&e.getValue(r,n).jump(n,!1)}measureEndState(){var e;let{element:t,name:r,unresolvedKeyframes:n}=this;if(!t||!t.current)return;let i=t.getValue(r);i&&i.jump(this.measuredOrigin,!1);let s=n.length-1,o=n[s];n[s]=to[r](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),(null===(e=this.removedTransforms)||void 0===e?void 0:e.length)&&this.removedTransforms.forEach(([e,r])=>{t.getValue(e).set(r)}),this.resolveNoneKeyframes()}}let tE=(e,t)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(e0.test(e)||"0"===e)&&!e.startsWith("url(")),tS=e=>null!==e;function tk(e,{repeat:t,repeatType:r="loop"},n){let i=e.filter(tS),s=t&&"loop"!==r&&t%2==1?0:i.length-1;return s&&void 0!==n?n:i[s]}class tT{constructor({autoplay:e=!0,delay:t=0,type:r="keyframes",repeat:n=0,repeatDelay:i=0,repeatType:s="loop",...o}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=Q.now(),this.options={autoplay:e,delay:t,type:r,repeat:n,repeatDelay:i,repeatType:s,...o},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(th(),tc()),this._resolved}onKeyframesResolved(e,t){this.resolvedAt=Q.now(),this.hasAttemptedResolve=!0;let{name:r,type:n,velocity:i,delay:s,onComplete:o,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(e,t,r,n){let i=e[0];if(null===i)return!1;if("display"===t||"visibility"===t)return!0;let s=e[e.length-1],o=tE(i,t),a=tE(s,t);return j(o===a,`You are trying to animate ${t} from "${i}" to "${s}". ${i} is not an animatable value - to enable this animation set ${i} to a value animatable to ${s} via the \`style\` property.`),!!o&&!!a&&(function(e){let t=e[0];if(1===e.length)return!0;for(let r=0;r<e.length;r++)if(e[r]!==t)return!0}(e)||("spring"===r||f(r))&&n)}(e,r,n,i)){if(eh.current||!s){a&&a(tk(e,this.options,t)),o&&o(),this.resolveFinishedPromise();return}this.options.duration=0}let u=this.initPlayback(e,t);!1!==u&&(this._resolved={keyframes:e,finalKeyframe:t,...u},this.onPostResolved())}onPostResolved(){}then(e,t){return this.currentFinishedPromise.then(e,t)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(e=>{this.resolveFinishedPromise=e})}}let tA={layout:0,mainThread:0,waapi:0},tR=(e,t,r)=>e+(t-e)*r;function tP(e,t,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?e+(t-e)*6*r:r<.5?t:r<2/3?e+(t-e)*(2/3-r)*6:e}function tO(e,t){return r=>r>0?t:e}let tM=(e,t,r)=>{let n=e*e,i=r*(t*t-n)+n;return i<0?0:Math.sqrt(i)},tL=[eN,eD,eq],tF=e=>tL.find(t=>t.test(e));function tD(e){let t=tF(e);if(j(!!t,`'${e}' is not an animatable color. Use the equivalent color code instead.`),!t)return!1;let r=t.parse(e);return t===eq&&(r=function({hue:e,saturation:t,lightness:r,alpha:n}){e/=360,r/=100;let i=0,s=0,o=0;if(t/=100){let n=r<.5?r*(1+t):r+t-r*t,a=2*r-n;i=tP(a,n,e+1/3),s=tP(a,n,e),o=tP(a,n,e-1/3)}else i=s=o=r;return{red:Math.round(255*i),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(r)),r}let tN=(e,t)=>{let r=tD(e),n=tD(t);if(!r||!n)return tO(e,t);let i={...r};return e=>(i.red=tM(r.red,n.red,e),i.green=tM(r.green,n.green,e),i.blue=tM(r.blue,n.blue,e),i.alpha=tR(r.alpha,n.alpha,e),eD.transform(i))},tj=(e,t)=>r=>t(e(r)),tB=(...e)=>e.reduce(tj),tI=new Set(["none","hidden"]);function tV(e,t){return r=>tR(e,t,r)}function tU(e){return"number"==typeof e?tV:"string"==typeof e?ty(e)?tO:eH.test(e)?tN:tq:Array.isArray(e)?t$:"object"==typeof e?eH.test(e)?tN:tW:tO}function t$(e,t){let r=[...e],n=r.length,i=e.map((e,r)=>tU(e)(e,t[r]));return e=>{for(let t=0;t<n;t++)r[t]=i[t](e);return r}}function tW(e,t){let r={...e,...t},n={};for(let i in r)void 0!==e[i]&&void 0!==t[i]&&(n[i]=tU(e[i])(e[i],t[i]));return e=>{for(let t in n)r[t]=n[t](e);return r}}let tq=(e,t)=>{let r=e0.createTransformer(t),n=eY(e),i=eY(t);return n.indexes.var.length===i.indexes.var.length&&n.indexes.color.length===i.indexes.color.length&&n.indexes.number.length>=i.indexes.number.length?tI.has(e)&&!i.values.length||tI.has(t)&&!n.values.length?function(e,t){return tI.has(e)?r=>r<=0?e:t:r=>r>=1?t:e}(e,t):tB(t$(function(e,t){var r;let n=[],i={color:0,var:0,number:0};for(let s=0;s<t.values.length;s++){let o=t.types[s],a=e.indexes[o][i[o]],l=null!==(r=e.values[a])&&void 0!==r?r:0;n[s]=l,i[o]++}return n}(n,i),i.values),r):(j(!0,`Complex values '${e}' and '${t}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tO(e,t))};function tH(e,t,r){return"number"==typeof e&&"number"==typeof t&&"number"==typeof r?tR(e,t,r):tU(e)(e,t)}function tz(e,t,r){var n,i;let s=Math.max(t-5,0);return n=r-e(s),(i=t-s)?1e3/i*n:0}let tG={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tK(e,t){return e*Math.sqrt(1-t*t)}let tX=["duration","bounce"],tY=["stiffness","damping","mass"];function tZ(e,t){return t.some(t=>void 0!==e[t])}function tJ(e=tG.visualDuration,t=tG.bounce){let r;let n="object"!=typeof e?{visualDuration:e,keyframes:[0,1],bounce:t}:e,{restSpeed:i,restDelta:s}=n,o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:c,mass:h,duration:d,velocity:f,isResolvedFromDuration:m}=function(e){let t={velocity:tG.velocity,stiffness:tG.stiffness,damping:tG.damping,mass:tG.mass,isResolvedFromDuration:!1,...e};if(!tZ(e,tY)&&tZ(e,tX)){if(e.visualDuration){let r=2*Math.PI/(1.2*e.visualDuration),n=r*r,i=2*eE(.05,1,1-(e.bounce||0))*Math.sqrt(n);t={...t,mass:tG.mass,stiffness:n,damping:i}}else{let r=function({duration:e=tG.duration,bounce:t=tG.bounce,velocity:r=tG.velocity,mass:n=tG.mass}){let i,s;j(e<=D(tG.maxDuration),"Spring duration must be 10 seconds or less");let o=1-t;o=eE(tG.minDamping,tG.maxDamping,o),e=eE(tG.minDuration,tG.maxDuration,N(e)),o<1?(i=t=>{let n=t*o,i=n*e;return .001-(n-r)/tK(t,o)*Math.exp(-i)},s=t=>{let n=t*o*e,s=Math.pow(o,2)*Math.pow(t,2)*e,a=Math.exp(-n),l=tK(Math.pow(t,2),o);return(n*r+r-s)*a*(-i(t)+.001>0?-1:1)/l}):(i=t=>-.001+Math.exp(-t*e)*((t-r)*e+1),s=t=>e*e*(r-t)*Math.exp(-t*e));let a=function(e,t,r){let n=r;for(let r=1;r<12;r++)n-=e(n)/t(n);return n}(i,s,5/e);if(e=D(e),isNaN(a))return{stiffness:tG.stiffness,damping:tG.damping,duration:e};{let t=Math.pow(a,2)*n;return{stiffness:t,damping:2*o*Math.sqrt(n*t),duration:e}}}(e);(t={...t,...r,mass:tG.mass}).isResolvedFromDuration=!0}}return t}({...n,velocity:-N(n.velocity||0)}),g=f||0,y=c/(2*Math.sqrt(u*h)),v=a-o,b=N(Math.sqrt(u/h)),x=5>Math.abs(v);if(i||(i=x?tG.restSpeed.granular:tG.restSpeed.default),s||(s=x?tG.restDelta.granular:tG.restDelta.default),y<1){let e=tK(b,y);r=t=>a-Math.exp(-y*b*t)*((g+y*b*v)/e*Math.sin(e*t)+v*Math.cos(e*t))}else if(1===y)r=e=>a-Math.exp(-b*e)*(v+(g+b*v)*e);else{let e=b*Math.sqrt(y*y-1);r=t=>{let r=Math.exp(-y*b*t),n=Math.min(e*t,300);return a-r*((g+y*b*v)*Math.sinh(n)+e*v*Math.cosh(n))/e}}let _={calculatedDuration:m&&d||null,next:e=>{let t=r(e);if(m)l.done=e>=d;else{let n=0;y<1&&(n=0===e?D(g):tz(r,e,t));let o=Math.abs(a-t)<=s;l.done=Math.abs(n)<=i&&o}return l.value=l.done?a:t,l},toString:()=>{let e=Math.min(p(_),2e4),t=w(t=>_.next(e*t).value,e,30);return e+"ms "+t}};return _}function tQ({keyframes:e,velocity:t=0,power:r=.8,timeConstant:n=325,bounceDamping:i=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:c}){let h,d;let p=e[0],f={done:!1,value:p},m=e=>void 0!==a&&e<a||void 0!==l&&e>l,g=e=>void 0===a?l:void 0===l?a:Math.abs(a-e)<Math.abs(l-e)?a:l,y=r*t,v=p+y,b=void 0===o?v:o(v);b!==v&&(y=b-p);let w=e=>-y*Math.exp(-e/n),x=e=>b+w(e),_=e=>{let t=w(e),r=x(e);f.done=Math.abs(t)<=u,f.value=f.done?b:r},C=e=>{m(f.value)&&(h=e,d=tJ({keyframes:[f.value,g(f.value)],velocity:tz(x,e,f.value),damping:i,stiffness:s,restDelta:u,restSpeed:c}))};return C(0),{calculatedDuration:null,next:e=>{let t=!1;return(d||void 0!==h||(t=!0,_(e),C(e)),void 0!==h&&e>=h)?d.next(e-h):(t||_(e),f)}}}let t0=ep(.42,0,1,1),t1=ep(0,0,.58,1),t2=ep(.42,0,.58,1),t3=e=>Array.isArray(e)&&"number"!=typeof e[0],t6={linear:j,easeIn:t0,easeInOut:t2,easeOut:t1,circIn:ew,circInOut:e_,circOut:ex,backIn:ey,backInOut:ev,backOut:eg,anticipate:eb},t9=e=>{if(g(e)){j(4===e.length,"Cubic bezier arrays must contain four numerical values.");let[t,r,n,i]=e;return ep(t,r,n,i)}return"string"==typeof e?(j(void 0!==t6[e],`Invalid easing type '${e}'`),t6[e]):e};function t4({duration:e=300,keyframes:t,times:r,ease:n="easeInOut"}){let i=t3(n)?n.map(t9):t9(n),s={done:!1,value:t[0]},o=function(e,t,{clamp:r=!0,ease:n,mixer:i}={}){let s=e.length;if(j(s===t.length,"Both input and output ranges must be the same length"),1===s)return()=>t[0];if(2===s&&t[0]===t[1])return()=>t[1];let o=e[0]===e[1];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());let a=function(e,t,r){let n=[],i=r||tH,s=e.length-1;for(let r=0;r<s;r++){let s=i(e[r],e[r+1]);t&&(s=tB(Array.isArray(t)?t[r]||j:t,s)),n.push(s)}return n}(t,n,i),l=a.length,u=r=>{if(o&&r<e[0])return t[0];let n=0;if(l>1)for(;n<e.length-2&&!(r<e[n+1]);n++);let i=b(e[n],e[n+1],r);return a[n](i)};return r?t=>u(eE(e[0],e[s-1],t)):u}((r&&r.length===t.length?r:function(e){let t=[0];return function(e,t){let r=e[e.length-1];for(let n=1;n<=t;n++){let i=b(0,t,n);e.push(tR(r,1,i))}}(t,e.length-1),t}(t)).map(t=>t*e),t,{ease:Array.isArray(i)?i:t.map(()=>i||t2).splice(0,t.length-1)});return{calculatedDuration:e,next:t=>(s.value=o(t),s.done=t>=e,s)}}let t5=e=>{let t=({timestamp:t})=>e(t);return{start:()=>K.update(t,!0),stop:()=>X(t),now:()=>Y.isProcessing?Y.timestamp:Q.now()}},t8={decay:tQ,inertia:tQ,tween:t4,keyframes:t4,spring:tJ},t7=e=>e/100;class re extends tT{constructor(e){super(e),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:e}=this.options;e&&e()};let{name:t,motionValue:r,element:n,keyframes:i}=this.options,s=(null==n?void 0:n.KeyframeResolver)||td;this.resolver=new s(i,(e,t)=>this.onKeyframesResolved(e,t),t,r,n),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(e){let t,r;let{type:n="keyframes",repeat:i=0,repeatDelay:s=0,repeatType:o,velocity:a=0}=this.options,l=f(n)?n:t8[n]||t4;l!==t4&&"number"!=typeof e[0]&&(t=tB(t7,tH(e[0],e[1])),e=[0,100]);let u=l({...this.options,keyframes:e});"mirror"===o&&(r=l({...this.options,keyframes:[...e].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=p(u));let{calculatedDuration:c}=u,h=c+s;return{generator:u,mirroredGenerator:r,mapPercentToKeyframes:t,calculatedDuration:c,resolvedDuration:h,totalDuration:h*(i+1)-s}}onPostResolved(){let{autoplay:e=!0}=this.options;tA.mainThread++,this.play(),"paused"!==this.pendingPlayState&&e?this.state=this.pendingPlayState:this.pause()}tick(e,t=!1){let{resolved:r}=this;if(!r){let{keyframes:e}=this.options;return{done:!0,value:e[e.length-1]}}let{finalKeyframe:n,generator:i,mirroredGenerator:s,mapPercentToKeyframes:o,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:c}=r;if(null===this.startTime)return i.next(0);let{delay:h,repeat:d,repeatType:p,repeatDelay:f,onUpdate:m}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,e):this.speed<0&&(this.startTime=Math.min(e-u/this.speed,this.startTime)),t?this.currentTime=e:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(e-this.startTime)*this.speed;let g=this.currentTime-h*(this.speed>=0?1:-1),y=this.speed>=0?g<0:g>u;this.currentTime=Math.max(g,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let v=this.currentTime,b=i;if(d){let e=Math.min(this.currentTime,u)/c,t=Math.floor(e),r=e%1;!r&&e>=1&&(r=1),1===r&&t--,(t=Math.min(t,d+1))%2&&("reverse"===p?(r=1-r,f&&(r-=f/c)):"mirror"===p&&(b=s)),v=eE(0,1,r)*c}let w=y?{done:!1,value:a[0]}:b.next(v);o&&(w.value=o(w.value));let{done:x}=w;y||null===l||(x=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let _=null===this.holdTime&&("finished"===this.state||"running"===this.state&&x);return _&&void 0!==n&&(w.value=tk(a,this.options,n)),m&&m(w.value),_&&this.finish(),w}get duration(){let{resolved:e}=this;return e?N(e.calculatedDuration):0}get time(){return N(this.currentTime)}set time(e){e=D(e),this.currentTime=e,null!==this.holdTime||0===this.speed?this.holdTime=e:this.driver&&(this.startTime=this.driver.now()-e/this.speed)}get speed(){return this.playbackSpeed}set speed(e){let t=this.playbackSpeed!==e;this.playbackSpeed=e,t&&(this.time=N(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:e=t5,onPlay:t,startTime:r}=this.options;this.driver||(this.driver=e(e=>this.tick(e))),t&&t();let n=this.driver.now();null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=n):this.startTime=null!=r?r:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var e;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!==(e=this.currentTime)&&void 0!==e?e:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:e}=this.options;e&&e()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel(),tA.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(e){return this.startTime=0,this.tick(e,!0)}}let rt=new Set(["opacity","clipPath","filter","transform"]),rr=l(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),rn={anticipate:eb,backInOut:ev,circInOut:e_};class ri extends tT{constructor(e){super(e);let{name:t,motionValue:r,element:n,keyframes:i}=this.options;this.resolver=new tC(i,(e,t)=>this.onKeyframesResolved(e,t),t,r,n),this.resolver.scheduleResolve()}initPlayback(e,t){var r;let{duration:n=300,times:i,ease:s,type:o,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if("string"==typeof s&&v()&&s in rn&&(s=rn[s]),f((r=this.options).type)||"spring"===r.type||!function e(t){return!!("function"==typeof t&&v()||!t||"string"==typeof t&&(t in _||v())||g(t)||Array.isArray(t)&&t.every(e))}(r.ease)){let{onComplete:t,onUpdate:r,motionValue:a,element:l,...u}=this.options,c=function(e,t){let r=new re({...t,keyframes:e,repeat:0,delay:0,isGenerator:!0}),n={done:!1,value:e[0]},i=[],s=0;for(;!n.done&&s<2e4;)i.push((n=r.sample(s)).value),s+=10;return{times:void 0,keyframes:i,duration:s-10,ease:"linear"}}(e,u);1===(e=c.keyframes).length&&(e[1]=e[0]),n=c.duration,i=c.times,s=c.ease,o="keyframes"}let c=function(e,t,r,{delay:n=0,duration:i=300,repeat:s=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){let u={[t]:r};l&&(u.offset=l);let c=function e(t,r){if(t)return"function"==typeof t&&v()?w(t,r):g(t)?x(t):Array.isArray(t)?t.map(t=>e(t,r)||_.easeOut):_[t]}(a,i);Array.isArray(c)&&(u.easing=c),z.value&&tA.waapi++;let h=e.animate(u,{delay:n,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"});return z.value&&h.finished.finally(()=>{tA.waapi--}),h}(a.owner.current,l,e,{...this.options,duration:n,times:i,ease:s});return c.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(m(c,this.pendingTimeline),this.pendingTimeline=void 0):c.onfinish=()=>{let{onComplete:r}=this.options;a.set(tk(e,this.options,t)),r&&r(),this.cancel(),this.resolveFinishedPromise()},{animation:c,duration:n,times:i,type:o,ease:s,keyframes:e}}get duration(){let{resolved:e}=this;if(!e)return 0;let{duration:t}=e;return N(t)}get time(){let{resolved:e}=this;if(!e)return 0;let{animation:t}=e;return N(t.currentTime||0)}set time(e){let{resolved:t}=this;if(!t)return;let{animation:r}=t;r.currentTime=D(e)}get speed(){let{resolved:e}=this;if(!e)return 1;let{animation:t}=e;return t.playbackRate}set speed(e){let{resolved:t}=this;if(!t)return;let{animation:r}=t;r.playbackRate=e}get state(){let{resolved:e}=this;if(!e)return"idle";let{animation:t}=e;return t.playState}get startTime(){let{resolved:e}=this;if(!e)return null;let{animation:t}=e;return t.startTime}attachTimeline(e){if(this._resolved){let{resolved:t}=this;if(!t)return j;let{animation:r}=t;m(r,e)}else this.pendingTimeline=e;return j}play(){if(this.isStopped)return;let{resolved:e}=this;if(!e)return;let{animation:t}=e;"finished"===t.playState&&this.updateFinishedPromise(),t.play()}pause(){let{resolved:e}=this;if(!e)return;let{animation:t}=e;t.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:e}=this;if(!e)return;let{animation:t,keyframes:r,duration:n,type:i,ease:s,times:o}=e;if("idle"===t.playState||"finished"===t.playState)return;if(this.time){let{motionValue:e,onUpdate:t,onComplete:a,element:l,...u}=this.options,c=new re({...u,keyframes:r,duration:n,type:i,ease:s,times:o,isGenerator:!0}),h=D(this.time);e.setWithVelocity(c.sample(h-10).value,c.sample(h).value,10)}let{onStop:a}=this.options;a&&a(),this.cancel()}complete(){let{resolved:e}=this;e&&e.animation.finish()}cancel(){let{resolved:e}=this;e&&e.animation.cancel()}static supports(e){let{motionValue:t,name:r,repeatDelay:n,repeatType:i,damping:s,type:o}=e;if(!t||!t.owner||!(t.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=t.owner.getProps();return rr()&&r&&rt.has(r)&&!a&&!l&&!n&&"mirror"!==i&&0!==s&&"inertia"!==o}}let rs={type:"spring",stiffness:500,damping:25,restSpeed:10},ro=e=>({type:"spring",stiffness:550,damping:0===e?2*Math.sqrt(550):30,restSpeed:10}),ra={type:"keyframes",duration:.8},rl={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ru=(e,{keyframes:t})=>t.length>2?ra:I.has(e)?e.startsWith("scale")?ro(t[1]):rs:rl,rc=(e,t,r,n={},i,s)=>o=>{let a=d(n,e)||{},l=a.delay||n.delay||0,{elapsed:u=0}=n;u-=D(l);let c={keyframes:Array.isArray(r)?r:[null,r],ease:"easeOut",velocity:t.getVelocity(),...a,delay:-u,onUpdate:e=>{t.set(e),a.onUpdate&&a.onUpdate(e)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:e,motionValue:t,element:s?void 0:i};!function({when:e,delay:t,delayChildren:r,staggerChildren:n,staggerDirection:i,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}(a)&&(c={...c,...ru(e,c)}),c.duration&&(c.duration=D(c.duration)),c.repeatDelay&&(c.repeatDelay=D(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let p=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0!==c.delay||(p=!0)),(eh.current||q.skipAnimations)&&(p=!0,c.duration=0,c.delay=0),p&&!s&&void 0!==t.get()){let e=tk(c.keyframes,a);if(void 0!==e)return K.update(()=>{c.onUpdate(e),c.onComplete()}),new h([])}return!s&&ri.supports(c)?new ri(c):new re(c)};function rh(e,t,{delay:r=0,transitionOverride:n,type:i}={}){var s;let{transition:o=e.getDefaultTransition(),transitionEnd:l,...u}=t;n&&(o=n);let c=[],h=i&&e.animationState&&e.animationState.getState()[i];for(let t in u){let n=e.getValue(t,null!==(s=e.latestValues[t])&&void 0!==s?s:null),i=u[t];if(void 0===i||h&&function({protectedKeys:e,needsAnimating:t},r){let n=e.hasOwnProperty(r)&&!0!==t[r];return t[r]=!1,n}(h,t))continue;let a={delay:r,...d(o||{},t)},l=!1;if(window.MotionHandoffAnimation){let r=e.props[ec];if(r){let e=window.MotionHandoffAnimation(r,t,K);null!==e&&(a.startTime=e,l=!0)}}el(e,t),n.start(rc(t,n,i,e.shouldReduceMotion&&V.has(t)?{type:!1}:a,e,l));let p=n.animation;p&&c.push(p)}return l&&Promise.all(c).then(()=>{K.update(()=>{l&&function(e,t){let{transitionEnd:r={},transition:n={},...i}=a(e,t)||{};for(let t in i={...i,...r}){let r=W(i[t]);e.hasValue(t)?e.getValue(t).set(r):e.addValue(t,eo(r))}}(e,l)})}),c}function rd(e,t,r={}){var n;let i=a(e,t,"exit"===r.type?null===(n=e.presenceContext)||void 0===n?void 0:n.custom:void 0),{transition:s=e.getDefaultTransition()||{}}=i||{};r.transitionOverride&&(s=r.transitionOverride);let o=i?()=>Promise.all(rh(e,i,r)):()=>Promise.resolve(),l=e.variantChildren&&e.variantChildren.size?(n=0)=>{let{delayChildren:i=0,staggerChildren:o,staggerDirection:a}=s;return function(e,t,r=0,n=0,i=1,s){let o=[],a=(e.variantChildren.size-1)*n,l=1===i?(e=0)=>e*n:(e=0)=>a-e*n;return Array.from(e.variantChildren).sort(rp).forEach((e,n)=>{e.notify("AnimationStart",t),o.push(rd(e,t,{...s,delay:r+l(n)}).then(()=>e.notify("AnimationComplete",t)))}),Promise.all(o)}(e,t,i+n,o,a,r)}:()=>Promise.resolve(),{when:u}=s;if(!u)return Promise.all([o(),l(r.delay)]);{let[e,t]="beforeChildren"===u?[o,l]:[l,o];return e().then(()=>t())}}function rp(e,t){return e.sortNodePosition(t)}function rf(e,t){if(!Array.isArray(t))return!1;let r=t.length;if(r!==e.length)return!1;for(let n=0;n<r;n++)if(t[n]!==e[n])return!1;return!0}function rm(e){return"string"==typeof e||Array.isArray(e)}let rg=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ry=["initial",...rg],rv=ry.length,rb=[...rg].reverse(),rw=rg.length;function rx(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function r_(){return{animate:rx(!0),whileInView:rx(),whileHover:rx(),whileTap:rx(),whileDrag:rx(),whileFocus:rx(),exit:rx()}}class rC{constructor(e){this.isMounted=!1,this.node=e}update(){}}class rE extends rC{constructor(e){super(e),e.animationState||(e.animationState=function(e){let t=t=>Promise.all(t.map(({animation:t,options:r})=>(function(e,t,r={}){let n;if(e.notify("AnimationStart",t),Array.isArray(t))n=Promise.all(t.map(t=>rd(e,t,r)));else if("string"==typeof t)n=rd(e,t,r);else{let i="function"==typeof t?a(e,t,r.custom):t;n=Promise.all(rh(e,i,r))}return n.then(()=>{e.notify("AnimationComplete",t)})})(e,t,r))),r=r_(),n=!0,s=t=>(r,n)=>{var i;let s=a(e,n,"exit"===t?null===(i=e.presenceContext)||void 0===i?void 0:i.custom:void 0);if(s){let{transition:e,transitionEnd:t,...n}=s;r={...r,...n,...t}}return r};function o(o){let{props:l}=e,u=function e(t){if(!t)return;if(!t.isControllingVariants){let r=t.parent&&e(t.parent)||{};return void 0!==t.props.initial&&(r.initial=t.props.initial),r}let r={};for(let e=0;e<rv;e++){let n=ry[e],i=t.props[n];(rm(i)||!1===i)&&(r[n]=i)}return r}(e.parent)||{},c=[],h=new Set,d={},p=1/0;for(let t=0;t<rw;t++){var f,m;let a=rb[t],g=r[a],y=void 0!==l[a]?l[a]:u[a],v=rm(y),b=a===o?g.isActive:null;!1===b&&(p=t);let w=y===u[a]&&y!==l[a]&&v;if(w&&n&&e.manuallyAnimateOnMount&&(w=!1),g.protectedKeys={...d},!g.isActive&&null===b||!y&&!g.prevProp||i(y)||"boolean"==typeof y)continue;let x=(f=g.prevProp,"string"==typeof(m=y)?m!==f:!!Array.isArray(m)&&!rf(m,f)),_=x||a===o&&g.isActive&&!w&&v||t>p&&v,C=!1,E=Array.isArray(y)?y:[y],S=E.reduce(s(a),{});!1===b&&(S={});let{prevResolvedValues:k={}}=g,T={...k,...S},A=t=>{_=!0,h.has(t)&&(C=!0,h.delete(t)),g.needsAnimating[t]=!0;let r=e.getValue(t);r&&(r.liveStyle=!1)};for(let e in T){let t=S[e],r=k[e];if(d.hasOwnProperty(e))continue;let n=!1;(U(t)&&U(r)?rf(t,r):t===r)?void 0!==t&&h.has(e)?A(e):g.protectedKeys[e]=!0:null!=t?A(e):h.add(e)}g.prevProp=y,g.prevResolvedValues=S,g.isActive&&(d={...d,...S}),n&&e.blockInitialAnimation&&(_=!1);let R=!(w&&x)||C;_&&R&&c.push(...E.map(e=>({animation:e,options:{type:a}})))}if(h.size){let t={};if("boolean"!=typeof l.initial){let r=a(e,Array.isArray(l.initial)?l.initial[0]:l.initial);r&&r.transition&&(t.transition=r.transition)}h.forEach(r=>{let n=e.getBaseTarget(r),i=e.getValue(r);i&&(i.liveStyle=!0),t[r]=null!=n?n:null}),c.push({animation:t})}let g=!!c.length;return n&&(!1===l.initial||l.initial===l.animate)&&!e.manuallyAnimateOnMount&&(g=!1),n=!1,g?t(c):Promise.resolve()}return{animateChanges:o,setActive:function(t,n){var i;if(r[t].isActive===n)return Promise.resolve();null===(i=e.variantChildren)||void 0===i||i.forEach(e=>{var r;return null===(r=e.animationState)||void 0===r?void 0:r.setActive(t,n)}),r[t].isActive=n;let s=o(t);for(let e in r)r[e].protectedKeys={};return s},setAnimateFunction:function(r){t=r(e)},getState:()=>r,reset:()=>{r=r_(),n=!0}}}(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();i(e)&&(this.unmountControls=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:t}=this.node.prevProps||{};e!==t&&this.updateAnimationControlsSubscription()}unmount(){var e;this.node.animationState.reset(),null===(e=this.unmountControls)||void 0===e||e.call(this)}}let rS=0;class rk extends rC{constructor(){super(...arguments),this.id=rS++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:t}=this.node.presenceContext,{isPresent:r}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===r)return;let n=this.node.animationState.setActive("exit",!e);t&&!e&&n.then(()=>{t(this.id)})}mount(){let{register:e,onExitComplete:t}=this.node.presenceContext||{};t&&t(this.id),e&&(this.unmount=e(this.id))}unmount(){}}function rT(e,t,r,n={passive:!0}){return e.addEventListener(t,r,n),()=>e.removeEventListener(t,r)}function rA(e){return{point:{x:e.pageX,y:e.pageY}}}let rR=e=>t=>A(t)&&e(t,rA(t));function rP(e,t,r,n){return rT(e,t,rR(r),n)}function rO({top:e,left:t,right:r,bottom:n}){return{x:{min:t,max:r},y:{min:e,max:n}}}function rM(e){return e.max-e.min}function rL(e,t,r,n=.5){e.origin=n,e.originPoint=tR(t.min,t.max,e.origin),e.scale=rM(r)/rM(t),e.translate=tR(r.min,r.max,e.origin)-e.originPoint,(e.scale>=.9999&&e.scale<=1.0001||isNaN(e.scale))&&(e.scale=1),(e.translate>=-.01&&e.translate<=.01||isNaN(e.translate))&&(e.translate=0)}function rF(e,t,r,n){rL(e.x,t.x,r.x,n?n.originX:void 0),rL(e.y,t.y,r.y,n?n.originY:void 0)}function rD(e,t,r){e.min=r.min+t.min,e.max=e.min+rM(t)}function rN(e,t,r){e.min=t.min-r.min,e.max=e.min+rM(t)}function rj(e,t,r){rN(e.x,t.x,r.x),rN(e.y,t.y,r.y)}let rB=()=>({translate:0,scale:1,origin:0,originPoint:0}),rI=()=>({x:rB(),y:rB()}),rV=()=>({min:0,max:0}),rU=()=>({x:rV(),y:rV()});function r$(e){return[e("x"),e("y")]}function rW(e){return void 0===e||1===e}function rq({scale:e,scaleX:t,scaleY:r}){return!rW(e)||!rW(t)||!rW(r)}function rH(e){return rq(e)||rz(e)||e.z||e.rotate||e.rotateX||e.rotateY||e.skewX||e.skewY}function rz(e){var t,r;return(t=e.x)&&"0%"!==t||(r=e.y)&&"0%"!==r}function rG(e,t,r,n,i){return void 0!==i&&(e=n+i*(e-n)),n+r*(e-n)+t}function rK(e,t=0,r=1,n,i){e.min=rG(e.min,t,r,n,i),e.max=rG(e.max,t,r,n,i)}function rX(e,{x:t,y:r}){rK(e.x,t.translate,t.scale,t.originPoint),rK(e.y,r.translate,r.scale,r.originPoint)}function rY(e,t){e.min=e.min+t,e.max=e.max+t}function rZ(e,t,r,n,i=.5){let s=tR(e.min,e.max,i);rK(e,t,r,s,n)}function rJ(e,t){rZ(e.x,t.x,t.scaleX,t.scale,t.originX),rZ(e.y,t.y,t.scaleY,t.scale,t.originY)}function rQ(e,t){return rO(function(e,t){if(!t)return e;let r=t({x:e.left,y:e.top}),n=t({x:e.right,y:e.bottom});return{top:r.y,left:r.x,bottom:n.y,right:n.x}}(e.getBoundingClientRect(),t))}function r0(e){return e&&"object"==typeof e&&Object.prototype.hasOwnProperty.call(e,"current")}let r1=(e,t)=>Math.abs(e-t);class r2{constructor(e,t,{transformPagePoint:r,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let e=r9(this.lastMoveEventInfo,this.history),t=null!==this.startEvent,r=function(e,t){return Math.sqrt(r1(e.x,t.x)**2+r1(e.y,t.y)**2)}(e.offset,{x:0,y:0})>=3;if(!t&&!r)return;let{point:n}=e,{timestamp:i}=Y;this.history.push({...n,timestamp:i});let{onStart:s,onMove:o}=this.handlers;t||(s&&s(this.lastMoveEvent,e),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,e)},this.handlePointerMove=(e,t)=>{if(this.index=r5(e.currentTarget),e.target instanceof Element&&e.target.hasPointerCapture&&void 0!==e.pointerId)try{if(!e.target.hasPointerCapture(e.pointerId))return}catch(e){}this.lastMoveEvent=e,this.lastMoveEventInfo=r3(t,this.transformPagePoint),K.update(this.updatePoint,!0)},this.handlePointerUp=(e,t)=>{k(e,"release"),this.end();let{onEnd:r,onSessionEnd:n,resumeAnimation:i}=this.handlers;if(this.dragSnapToOrigin&&i&&i(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=r9("pointercancel"===e.type||"lostpointercapture"===e.type?this.lastMoveEventInfo:r3(t,this.transformPagePoint),this.history);this.startEvent&&r&&r(e,s),n&&n(e,s)},!A(e))return;this.dragSnapToOrigin=n,this.handlers=t,this.transformPagePoint=r;let i=r3(rA(e),this.transformPagePoint),{point:s}=i,{timestamp:o}=Y;this.history=[{...s,timestamp:o}];let{onSessionStart:a}=t;a&&a(e,r9(i,this.history)),k(e,"set"),this.removeListeners=tB(rP(e.currentTarget,"pointermove",this.handlePointerMove),rP(e.currentTarget,"pointerup",this.handlePointerUp),rP(e.currentTarget,"pointercancel",this.handlePointerUp),rP(e.currentTarget,"lostpointercapture",(e,t)=>{r5(e.currentTarget)!==this.index?k(e,"set"):this.handlePointerUp(e,t)}))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),X(this.updatePoint)}}function r3(e,t){return t?{point:t(e.point)}:e}function r6(e,t){return{x:e.x-t.x,y:e.y-t.y}}function r9({point:e},t){return{point:e,delta:r6(e,r4(t)),offset:r6(e,t[0]),velocity:function(e,t){if(e.length<2)return{x:0,y:0};let r=e.length-1,n=null,i=r4(e);for(;r>=0&&(n=e[r],!(i.timestamp-n.timestamp>D(.1)));)r--;if(!n)return{x:0,y:0};let s=N(i.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(i.x-n.x)/s,y:(i.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(t,.1)}}function r4(e){return e[e.length-1]}function r5(e){return e.parentNode?Array.from(e.parentNode.children).indexOf(e):-1}function r8(e,t,r){return{min:void 0!==t?e.min+t:void 0,max:void 0!==r?e.max+r-(e.max-e.min):void 0}}function r7(e,t){let r=t.min-e.min,n=t.max-e.max;return t.max-t.min<e.max-e.min&&([r,n]=[n,r]),{min:r,max:n}}function ne(e,t,r){return{min:nt(e,t),max:nt(e,r)}}function nt(e,t){return"number"==typeof e?e:e[t]||0}let nr=new WeakMap;class nn{constructor(e){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=rU(),this.visualElement=e}start(e,{snapToCursor:t=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new r2(e,{onSessionStart:e=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),t&&this.snapToCursor(rA(e).point)},onStart:(e,t)=>{var r;let{drag:n,dragPropagation:i,onDragStart:s}=this.getProps();if(n&&!i&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===(r=n)||"y"===r?C[r]?null:(C[r]=!0,()=>{C[r]=!1}):C.x||C.y?null:(C.x=C.y=!0,()=>{C.x=C.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),r$(e=>{let t=this.getAxisMotionValue(e).get()||0;if(eI.test(t)){let{projection:r}=this.visualElement;if(r&&r.layout){let n=r.layout.layoutBox[e];n&&(t=rM(n)*(parseFloat(t)/100))}}this.originPoint[e]=t}),s&&K.postRender(()=>s(e,t)),el(this.visualElement,"transform");let{animationState:o}=this.visualElement;o&&o.setActive("whileDrag",!0)},onMove:(e,t)=>{let{dragPropagation:r,dragDirectionLock:n,onDirectionLock:i,onDrag:s}=this.getProps();if(!r&&!this.openDragLock)return;let{offset:o}=t;if(n&&null===this.currentDirection){this.currentDirection=function(e,t=10){let r=null;return Math.abs(e.y)>t?r="y":Math.abs(e.x)>t&&(r="x"),r}(o),null!==this.currentDirection&&i&&i(this.currentDirection);return}this.updateAxis("x",t.point,o),this.updateAxis("y",t.point,o),this.visualElement.render(),s&&s(e,t)},onSessionEnd:(e,t)=>this.stop(e,t),resumeAnimation:()=>r$(e=>{var t;return"paused"===this.getAnimationState(e)&&(null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n})}stop(e,t){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:n}=t;this.startAnimation(n);let{onDragEnd:i}=this.getProps();i&&K.postRender(()=>i(e,t))}cancel(){this.isDragging=!1;let{projection:e,animationState:t}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),t&&t.setActive("whileDrag",!1)}updateAxis(e,t,r){let{drag:n}=this.getProps();if(!r||!ni(e,n,this.currentDirection))return;let i=this.getAxisMotionValue(e),s=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(s=function(e,{min:t,max:r},n){return void 0!==t&&e<t?e=n?tR(t,e,n.min):Math.max(e,t):void 0!==r&&e>r&&(e=n?tR(r,e,n.max):Math.min(e,r)),e}(s,this.constraints[e],this.elastic[e])),i.set(s)}resolveConstraints(){var e;let{dragConstraints:t,dragElastic:r}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(e=this.visualElement.projection)||void 0===e?void 0:e.layout,i=this.constraints;t&&r0(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&n?this.constraints=function(e,{top:t,left:r,bottom:n,right:i}){return{x:r8(e.x,r,i),y:r8(e.y,t,n)}}(n.layoutBox,t):this.constraints=!1,this.elastic=function(e=.35){return!1===e?e=0:!0===e&&(e=.35),{x:ne(e,"left","right"),y:ne(e,"top","bottom")}}(r),i!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&r$(e=>{!1!==this.constraints&&this.getAxisMotionValue(e)&&(this.constraints[e]=function(e,t){let r={};return void 0!==t.min&&(r.min=t.min-e.min),void 0!==t.max&&(r.max=t.max-e.min),r}(n.layoutBox[e],this.constraints[e]))})}resolveRefConstraints(){var e;let{dragConstraints:t,onMeasureDragConstraints:r}=this.getProps();if(!t||!r0(t))return!1;let n=t.current;j(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:i}=this.visualElement;if(!i||!i.layout)return!1;let s=function(e,t,r){let n=rQ(e,r),{scroll:i}=t;return i&&(rY(n.x,i.offset.x),rY(n.y,i.offset.y)),n}(n,i.root,this.visualElement.getTransformPagePoint()),o={x:r7((e=i.layout.layoutBox).x,s.x),y:r7(e.y,s.y)};if(r){let e=r(function({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}(o));this.hasMutatedConstraints=!!e,e&&(o=rO(e))}return o}startAnimation(e){let{drag:t,dragMomentum:r,dragElastic:n,dragTransition:i,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(r$(o=>{if(!ni(o,t,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:r?e[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...i,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(e,t){let r=this.getAxisMotionValue(e);return el(this.visualElement,e),r.start(rc(e,r,0,t,this.visualElement,!1))}stopAnimation(){r$(e=>this.getAxisMotionValue(e).stop())}pauseAnimation(){r$(e=>{var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.pause()})}getAnimationState(e){var t;return null===(t=this.getAxisMotionValue(e).animation)||void 0===t?void 0:t.state}getAxisMotionValue(e){let t=`_drag${e.toUpperCase()}`,r=this.visualElement.getProps();return r[t]||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){r$(t=>{let{drag:r}=this.getProps();if(!ni(t,r,this.currentDirection))return;let{projection:n}=this.visualElement,i=this.getAxisMotionValue(t);if(n&&n.layout){let{min:r,max:s}=n.layout.layoutBox[t];i.set(e[t]-tR(r,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:t}=this.getProps(),{projection:r}=this.visualElement;if(!r0(t)||!r||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};r$(e=>{let t=this.getAxisMotionValue(e);if(t&&!1!==this.constraints){let r=t.get();n[e]=function(e,t){let r=.5,n=rM(e),i=rM(t);return i>n?r=b(t.min,t.max-n,e.min):n>i&&(r=b(e.min,e.max-i,t.min)),eE(0,1,r)}({min:r,max:r},this.constraints[e])}});let{transformTemplate:i}=this.visualElement.getProps();this.visualElement.current.style.transform=i?i({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),r$(t=>{if(!ni(t,e,null))return;let r=this.getAxisMotionValue(t),{min:i,max:s}=this.constraints[t];r.set(tR(i,s,n[t]))})}addListeners(){if(!this.visualElement.current)return;nr.set(this.visualElement,this);let e=rP(this.visualElement.current,"pointerdown",e=>{let{drag:t,dragListener:r=!0}=this.getProps();t&&r&&this.start(e)}),t=()=>{let{dragConstraints:e}=this.getProps();r0(e)&&e.current&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,n=r.addEventListener("measure",t);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),K.read(t);let i=rT(window,"resize",()=>this.scalePositionWithinConstraints()),s=r.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t})=>{this.isDragging&&t&&(r$(t=>{let r=this.getAxisMotionValue(t);r&&(this.originPoint[t]+=e[t].translate,r.set(r.get()+e[t].translate))}),this.visualElement.render())});return()=>{i(),e(),n(),s&&s()}}getProps(){let e=this.visualElement.getProps(),{drag:t=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:i=!1,dragElastic:s=.35,dragMomentum:o=!0}=e;return{...e,drag:t,dragDirectionLock:r,dragPropagation:n,dragConstraints:i,dragElastic:s,dragMomentum:o}}}function ni(e,t,r){return(!0===t||t===e)&&(null===r||r===e)}class ns extends rC{constructor(e){super(e),this.removeGroupControls=j,this.removeListeners=j,this.controls=new nn(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||j}unmount(){this.removeGroupControls(),this.removeListeners()}}let no=e=>(t,r)=>{e&&K.postRender(()=>e(t,r))};class na extends rC{constructor(){super(...arguments),this.removePointerDownListener=j}onPointerDown(e){this.session=new r2(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint()})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:t,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:no(e),onStart:no(t),onMove:r,onEnd:(e,t)=>{delete this.session,n&&K.postRender(()=>n(e,t))}}}mount(){this.removePointerDownListener=rP(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var nl,nu,nc=r(60687),nh=r(43210);let nd=(0,nh.createContext)(null),np=(0,nh.createContext)({}),nf=(0,nh.createContext)({}),{schedule:nm,cancel:ng}=G(queueMicrotask,!1),ny={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nv(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}let nb={correct:(e,t)=>{if(!t.target)return e;if("string"==typeof e){if(!eV.test(e))return e;e=parseFloat(e)}let r=nv(e,t.target.x),n=nv(e,t.target.y);return`${r}% ${n}%`}},nw={};class nx extends nh.Component{componentDidMount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r,layoutId:n}=this.props,{projection:i}=e;(function(e){for(let t in e)nw[t]=e[t],tm(t)&&(nw[t].isCSSVariable=!0)})(nC),i&&(t.group&&t.group.add(i),r&&r.register&&n&&r.register(i),i.root.didUpdate(),i.addEventListener("animationComplete",()=>{this.safeToRemove()}),i.setOptions({...i.options,onExitComplete:()=>this.safeToRemove()})),ny.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:t,visualElement:r,drag:n,isPresent:i}=this.props,s=r.projection;return s&&(s.isPresent=i,n||e.layoutDependency!==t||void 0===t||e.isPresent!==i?s.willUpdate():this.safeToRemove(),e.isPresent===i||(i?s.promote():s.relegate()||K.postRender(()=>{let e=s.getStack();e&&e.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),nm.postRender(()=>{!e.currentAnimation&&e.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:e,layoutGroup:t,switchLayoutGroup:r}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),t&&t.group&&t.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}}function n_(e){let[t,r]=function(e=!0){let t=(0,nh.useContext)(nd);if(null===t)return[!0,null];let{isPresent:r,onExitComplete:n,register:i}=t,s=(0,nh.useId)();(0,nh.useEffect)(()=>{if(e)return i(s)},[e]);let o=(0,nh.useCallback)(()=>e&&n&&n(s),[s,n,e]);return!r&&n?[!1,o]:[!0]}(),n=(0,nh.useContext)(np);return(0,nc.jsx)(nx,{...e,layoutGroup:n,switchLayoutGroup:(0,nh.useContext)(nf),isPresent:t,safeToRemove:r})}let nC={borderRadius:{...nb,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:nb,borderTopRightRadius:nb,borderBottomLeftRadius:nb,borderBottomRightRadius:nb,boxShadow:{correct:(e,{treeScale:t,projectionDelta:r})=>{let n=e0.parse(e);if(n.length>5)return e;let i=e0.createTransformer(e),s=+("number"!=typeof n[0]),o=r.x.scale*t.x,a=r.y.scale*t.y;n[0+s]/=o,n[1+s]/=a;let l=tR(o,a,.5);return"number"==typeof n[2+s]&&(n[2+s]/=l),"number"==typeof n[3+s]&&(n[3+s]/=l),i(n)}}},nE=(e,t)=>e.depth-t.depth;class nS{constructor(){this.children=[],this.isDirty=!1}add(e){ee(this.children,e),this.isDirty=!0}remove(e){et(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(nE),this.isDirty=!1,this.children.forEach(e)}}function nk(e){let t=ea(e)?e.get():e;return $(t)?t.toValue():t}let nT=["TopLeft","TopRight","BottomLeft","BottomRight"],nA=nT.length,nR=e=>"string"==typeof e?parseFloat(e):e,nP=e=>"number"==typeof e||eV.test(e);function nO(e,t){return void 0!==e[t]?e[t]:e.borderRadius}let nM=nF(0,.5,ex),nL=nF(.5,.95,j);function nF(e,t,r){return n=>n<e?0:n>t?1:r(b(e,t,n))}function nD(e,t){e.min=t.min,e.max=t.max}function nN(e,t){nD(e.x,t.x),nD(e.y,t.y)}function nj(e,t){e.translate=t.translate,e.scale=t.scale,e.originPoint=t.originPoint,e.origin=t.origin}function nB(e,t,r,n,i){return e-=t,e=n+1/r*(e-n),void 0!==i&&(e=n+1/i*(e-n)),e}function nI(e,t,[r,n,i],s,o){!function(e,t=0,r=1,n=.5,i,s=e,o=e){if(eI.test(t)&&(t=parseFloat(t),t=tR(o.min,o.max,t/100)-o.min),"number"!=typeof t)return;let a=tR(s.min,s.max,n);e===s&&(a-=t),e.min=nB(e.min,t,r,a,i),e.max=nB(e.max,t,r,a,i)}(e,t[r],t[n],t[i],t.scale,s,o)}let nV=["x","scaleX","originX"],nU=["y","scaleY","originY"];function n$(e,t,r,n){nI(e.x,t,nV,r?r.x:void 0,n?n.x:void 0),nI(e.y,t,nU,r?r.y:void 0,n?n.y:void 0)}function nW(e){return 0===e.translate&&1===e.scale}function nq(e){return nW(e.x)&&nW(e.y)}function nH(e,t){return e.min===t.min&&e.max===t.max}function nz(e,t){return Math.round(e.min)===Math.round(t.min)&&Math.round(e.max)===Math.round(t.max)}function nG(e,t){return nz(e.x,t.x)&&nz(e.y,t.y)}function nK(e){return rM(e.x)/rM(e.y)}function nX(e,t){return e.translate===t.translate&&e.scale===t.scale&&e.originPoint===t.originPoint}class nY{constructor(){this.members=[]}add(e){ee(this.members,e),e.scheduleRender()}remove(e){if(et(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let e=this.members[this.members.length-1];e&&this.promote(e)}}relegate(e){let t;let r=this.members.findIndex(t=>e===t);if(0===r)return!1;for(let e=r;e>=0;e--){let r=this.members[e];if(!1!==r.isPresent){t=r;break}}return!!t&&(this.promote(t),!0)}promote(e,t){let r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,t&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:n}=e.options;!1===n&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:t,resumingFrom:r}=e;t.onExitComplete&&t.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nZ={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},nJ=["","X","Y","Z"],nQ={visibility:"hidden"},n0=0;function n1(e,t,r,n){let{latestValues:i}=t;i[e]&&(r[e]=i[e],t.setStaticValue(e,0),n&&(n[e]=0))}function n2({attachResizeListener:e,defaultParent:t,measureScroll:r,checkIsScrollRoot:n,resetTransform:i}){return class{constructor(e={},r=null==t?void 0:t()){this.id=n0++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,z.value&&(nZ.nodes=nZ.calculatedTargetDeltas=nZ.calculatedProjections=0),this.nodes.forEach(n9),this.nodes.forEach(ir),this.nodes.forEach(ii),this.nodes.forEach(n4),z.addProjectionMetrics&&z.addProjectionMetrics(nZ)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=e,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let e=0;e<this.path.length;e++)this.path[e].shouldResetTransform=!0;this.root===this&&(this.nodes=new nS)}addEventListener(e,t){return this.eventHandlers.has(e)||this.eventHandlers.set(e,new er),this.eventHandlers.get(e).add(t)}notifyListeners(e,...t){let r=this.eventHandlers.get(e);r&&r.notify(...t)}hasListeners(e){return this.eventHandlers.has(e)}mount(t,r=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=t instanceof SVGElement&&"svg"!==t.tagName,this.instance=t;let{layoutId:n,layout:i,visualElement:s}=this.options;if(s&&!s.current&&s.mount(t),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),r&&(i||n)&&(this.isLayoutDirty=!0),e){let r;let n=()=>this.root.updateBlockedByResize=!1;e(t,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(e,t){let r=Q.now(),n=({timestamp:i})=>{let s=i-r;s>=250&&(X(n),e(s-t))};return K.read(n,!0),()=>X(n)}(n,250),ny.hasAnimatedSinceResize&&(ny.hasAnimatedSinceResize=!1,this.nodes.forEach(it))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&s&&(n||i)&&this.addEventListener("didUpdate",({delta:e,hasLayoutChanged:t,hasRelativeLayoutChanged:r,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let i=this.options.transition||s.getDefaultTransition()||ic,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=s.getProps(),l=!this.targetLayout||!nG(this.targetLayout,n),u=!t&&r;if(this.options.layoutRoot||this.resumeFrom||u||t&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(e,u);let t={...d(i,"layout"),onPlay:o,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(t.delay=0,t.type=!1),this.startAnimation(t)}else t||it(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let e=this.getStack();e&&e.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,X(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(is),this.animationId++)}getTransformTemplate(){let{visualElement:e}=this.options;return e&&e.getProps().transformTemplate}willUpdate(e=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function e(t){if(t.hasCheckedOptimisedAppear=!0,t.root===t)return;let{visualElement:r}=t.options;if(!r)return;let n=r.props[ec];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:e,layoutId:r}=t.options;window.MotionCancelOptimisedAnimation(n,"transform",K,!(e||r))}let{parent:i}=t;i&&!i.hasCheckedOptimisedAppear&&e(i)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let e=0;e<this.path.length;e++){let t=this.path[e];t.shouldResetTransform=!0,t.updateScroll("snapshot"),t.options.layoutRoot&&t.willUpdate(!1)}let{layoutId:t,layout:r}=this.options;if(void 0===t&&!r)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),e&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(n8);return}this.isUpdating||this.nodes.forEach(n7),this.isUpdating=!1,this.nodes.forEach(ie),this.nodes.forEach(n3),this.nodes.forEach(n6),this.clearAllSnapshots();let e=Q.now();Y.delta=eE(0,1e3/60,e-Y.timestamp),Y.timestamp=e,Y.isProcessing=!0,Z.update.process(Y),Z.preRender.process(Y),Z.render.process(Y),Y.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,nm.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(n5),this.sharedNodes.forEach(io)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,K.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){K.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure(),!this.snapshot||rM(this.snapshot.measuredBox.x)||rM(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let e=0;e<this.path.length;e++)this.path[e].updateScroll();let e=this.layout;this.layout=this.measure(!1),this.layoutCorrected=rU(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:t}=this.options;t&&t.notify("LayoutMeasure",this.layout.layoutBox,e?e.layoutBox:void 0)}updateScroll(e="measure"){let t=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===e&&(t=!1),t){let t=n(this.instance);this.scroll={animationId:this.root.animationId,phase:e,isRoot:t,offset:r(this.instance),wasRoot:this.scroll?this.scroll.isRoot:t}}}resetTransform(){if(!i)return;let e=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,t=this.projectionDelta&&!nq(this.projectionDelta),r=this.getTransformTemplate(),n=r?r(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;e&&(t||rH(this.latestValues)||s)&&(i(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(e=!0){var t;let r=this.measurePageBox(),n=this.removeElementScroll(r);return e&&(n=this.removeTransform(n)),ip((t=n).x),ip(t.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var e;let{visualElement:t}=this.options;if(!t)return rU();let r=t.measureViewportBox();if(!((null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)||this.path.some(ig))){let{scroll:e}=this.root;e&&(rY(r.x,e.offset.x),rY(r.y,e.offset.y))}return r}removeElementScroll(e){var t;let r=rU();if(nN(r,e),null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)return r;for(let t=0;t<this.path.length;t++){let n=this.path[t],{scroll:i,options:s}=n;n!==this.root&&i&&s.layoutScroll&&(i.wasRoot&&nN(r,e),rY(r.x,i.offset.x),rY(r.y,i.offset.y))}return r}applyTransform(e,t=!1){let r=rU();nN(r,e);for(let e=0;e<this.path.length;e++){let n=this.path[e];!t&&n.options.layoutScroll&&n.scroll&&n!==n.root&&rJ(r,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),rH(n.latestValues)&&rJ(r,n.latestValues)}return rH(this.latestValues)&&rJ(r,this.latestValues),r}removeTransform(e){let t=rU();nN(t,e);for(let e=0;e<this.path.length;e++){let r=this.path[e];if(!r.instance||!rH(r.latestValues))continue;rq(r.latestValues)&&r.updateSnapshot();let n=rU();nN(n,r.measurePageBox()),n$(t,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,n)}return rH(this.latestValues)&&n$(t,this.latestValues),t}setTargetDelta(e){this.targetDelta=e,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(e){this.options={...this.options,...e,crossfade:void 0===e.crossfade||e.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==Y.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(e=!1){var t,r,n,i;let s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==s;if(!(e||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=Y.timestamp,!this.targetDelta&&!this.relativeTarget){let e=this.getClosestProjectingParent();e&&e.layout&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rU(),this.relativeTargetOrigin=rU(),rj(this.relativeTargetOrigin,this.layout.layoutBox,e.layout.layoutBox),nN(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=rU(),this.targetWithTransforms=rU()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),r=this.target,n=this.relativeTarget,i=this.relativeParent.target,rD(r.x,n.x,i.x),rD(r.y,n.y,i.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nN(this.target,this.layout.layoutBox),rX(this.target,this.targetDelta)):nN(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let e=this.getClosestProjectingParent();e&&!!e.resumingFrom==!!this.resumingFrom&&!e.options.layoutScroll&&e.target&&1!==this.animationProgress?(this.relativeParent=e,this.forceRelativeParentToResolveTarget(),this.relativeTarget=rU(),this.relativeTargetOrigin=rU(),rj(this.relativeTargetOrigin,this.target,e.target),nN(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}z.value&&nZ.calculatedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||rq(this.parent.latestValues)||rz(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var e;let t=this.getLead(),r=!!this.resumingFrom||this!==t,n=!0;if((this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty))&&(n=!1),r&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===Y.timestamp&&(n=!1),n)return;let{layout:i,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(i||s))return;nN(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;(function(e,t,r,n=!1){let i,s;let o=r.length;if(o){t.x=t.y=1;for(let a=0;a<o;a++){s=(i=r[a]).projectionDelta;let{visualElement:o}=i.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&i.options.layoutScroll&&i.scroll&&i!==i.root&&rJ(e,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),s&&(t.x*=s.x.scale,t.y*=s.y.scale,rX(e,s)),n&&rH(i.latestValues)&&rJ(e,i.latestValues))}t.x<1.0000000000001&&t.x>.999999999999&&(t.x=1),t.y<1.0000000000001&&t.y>.999999999999&&(t.y=1)}})(this.layoutCorrected,this.treeScale,this.path,r),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=rU());let{target:l}=t;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nj(this.prevProjectionDelta.x,this.projectionDelta.x),nj(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),rF(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&nX(this.projectionDelta.x,this.prevProjectionDelta.x)&&nX(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),z.value&&nZ.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(e=!0){var t;if(null===(t=this.options.visualElement)||void 0===t||t.scheduleRender(),e){let e=this.getStack();e&&e.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=rI(),this.projectionDelta=rI(),this.projectionDeltaWithTransform=rI()}setAnimationOrigin(e,t=!1){let r;let n=this.snapshot,i=n?n.latestValues:{},s={...this.latestValues},o=rI();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!t;let a=rU(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),c=!u||u.members.length<=1,h=!!(l&&!c&&!0===this.options.crossfade&&!this.path.some(iu));this.animationProgress=0,this.mixTargetDelta=t=>{let n=t/1e3;if(ia(o.x,e.x,n),ia(o.y,e.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,f,m,g;if(rj(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,f=this.relativeTargetOrigin,m=a,g=n,il(p.x,f.x,m.x,g),il(p.y,f.y,m.y,g),r&&(u=this.relativeTarget,d=r,nH(u.x,d.x)&&nH(u.y,d.y)))this.isProjectionDirty=!1;r||(r=rU()),nN(r,this.relativeTarget)}l&&(this.animationValues=s,function(e,t,r,n,i,s){i?(e.opacity=tR(0,void 0!==r.opacity?r.opacity:1,nM(n)),e.opacityExit=tR(void 0!==t.opacity?t.opacity:1,0,nL(n))):s&&(e.opacity=tR(void 0!==t.opacity?t.opacity:1,void 0!==r.opacity?r.opacity:1,n));for(let i=0;i<nA;i++){let s=`border${nT[i]}Radius`,o=nO(t,s),a=nO(r,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||nP(o)===nP(a)?(e[s]=Math.max(tR(nR(o),nR(a),n),0),(eI.test(a)||eI.test(o))&&(e[s]+="%")):e[s]=a)}(t.rotate||r.rotate)&&(e.rotate=tR(t.rotate||0,r.rotate||0,n))}(s,i,this.latestValues,n,h,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(e){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(X(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=K.update(()=>{ny.hasAnimatedSinceResize=!0,tA.layout++,this.currentAnimation=function(e,t,r){let n=ea(0)?0:eo(e);return n.start(rc("",n,1e3,r)),n.animation}(0,0,{...e,onUpdate:t=>{this.mixTargetDelta(t),e.onUpdate&&e.onUpdate(t)},onStop:()=>{tA.layout--},onComplete:()=>{tA.layout--,e.onComplete&&e.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let e=this.getStack();e&&e.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let e=this.getLead(),{targetWithTransforms:t,target:r,layout:n,latestValues:i}=e;if(t&&r&&n){if(this!==e&&this.layout&&n&&im(this.options.animationType,this.layout.layoutBox,n.layoutBox)){r=this.target||rU();let t=rM(this.layout.layoutBox.x);r.x.min=e.target.x.min,r.x.max=r.x.min+t;let n=rM(this.layout.layoutBox.y);r.y.min=e.target.y.min,r.y.max=r.y.min+n}nN(t,r),rJ(t,i),rF(this.projectionDeltaWithTransform,this.layoutCorrected,t,i)}}registerSharedNode(e,t){this.sharedNodes.has(e)||this.sharedNodes.set(e,new nY),this.sharedNodes.get(e).add(t);let r=t.options.initialPromotionConfig;t.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(t):void 0})}isLead(){let e=this.getStack();return!e||e.lead===this}getLead(){var e;let{layoutId:t}=this.options;return t&&(null===(e=this.getStack())||void 0===e?void 0:e.lead)||this}getPrevLead(){var e;let{layoutId:t}=this.options;return t?null===(e=this.getStack())||void 0===e?void 0:e.prevLead:void 0}getStack(){let{layoutId:e}=this.options;if(e)return this.root.sharedNodes.get(e)}promote({needsReset:e,transition:t,preserveFollowOpacity:r}={}){let n=this.getStack();n&&n.promote(this,r),e&&(this.projectionDelta=void 0,this.needsReset=!0),t&&this.setOptions({transition:t})}relegate(){let e=this.getStack();return!!e&&e.relegate(this)}resetSkewAndRotation(){let{visualElement:e}=this.options;if(!e)return;let t=!1,{latestValues:r}=e;if((r.z||r.rotate||r.rotateX||r.rotateY||r.rotateZ||r.skewX||r.skewY)&&(t=!0),!t)return;let n={};r.z&&n1("z",e,n,this.animationValues);for(let t=0;t<nJ.length;t++)n1(`rotate${nJ[t]}`,e,n,this.animationValues),n1(`skew${nJ[t]}`,e,n,this.animationValues);for(let t in e.render(),n)e.setStaticValue(t,n[t]),this.animationValues&&(this.animationValues[t]=n[t]);e.scheduleRender()}getProjectionStyles(e){var t,r;if(!this.instance||this.isSVG)return;if(!this.isVisible)return nQ;let n={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,n.opacity="",n.pointerEvents=nk(null==e?void 0:e.pointerEvents)||"",n.transform=i?i(this.latestValues,""):"none",n;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let t={};return this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=nk(null==e?void 0:e.pointerEvents)||""),this.hasProjected&&!rH(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1),t}let o=s.animationValues||s.latestValues;this.applyTransformsToTarget(),n.transform=function(e,t,r){let n="",i=e.x.translate/t.x,s=e.y.translate/t.y,o=(null==r?void 0:r.z)||0;if((i||s||o)&&(n=`translate3d(${i}px, ${s}px, ${o}px) `),(1!==t.x||1!==t.y)&&(n+=`scale(${1/t.x}, ${1/t.y}) `),r){let{transformPerspective:e,rotate:t,rotateX:i,rotateY:s,skewX:o,skewY:a}=r;e&&(n=`perspective(${e}px) ${n}`),t&&(n+=`rotate(${t}deg) `),i&&(n+=`rotateX(${i}deg) `),s&&(n+=`rotateY(${s}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=e.x.scale*t.x,l=e.y.scale*t.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,o),i&&(n.transform=i(o,n.transform));let{x:a,y:l}=this.projectionDelta;for(let e in n.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,s.animationValues?n.opacity=s===this?null!==(r=null!==(t=o.opacity)&&void 0!==t?t:this.latestValues.opacity)&&void 0!==r?r:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:n.opacity=s===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,nw){if(void 0===o[e])continue;let{correct:t,applyTo:r,isCSSVariable:i}=nw[e],a="none"===n.transform?o[e]:t(o[e],s);if(r){let e=r.length;for(let t=0;t<e;t++)n[r[t]]=a}else i?this.options.visualElement.renderState.vars[e]=a:n[e]=a}return this.options.layoutId&&(n.pointerEvents=s===this?nk(null==e?void 0:e.pointerEvents)||"":"none"),n}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(e=>{var t;return null===(t=e.currentAnimation)||void 0===t?void 0:t.stop()}),this.root.nodes.forEach(n8),this.root.sharedNodes.clear()}}}function n3(e){e.updateLayout()}function n6(e){var t;let r=(null===(t=e.resumeFrom)||void 0===t?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&r&&e.hasListeners("didUpdate")){let{layoutBox:t,measuredBox:n}=e.layout,{animationType:i}=e.options,s=r.source!==e.layout.source;"size"===i?r$(e=>{let n=s?r.measuredBox[e]:r.layoutBox[e],i=rM(n);n.min=t[e].min,n.max=n.min+i}):im(i,r.layoutBox,t)&&r$(n=>{let i=s?r.measuredBox[n]:r.layoutBox[n],o=rM(t[n]);i.max=i.min+o,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[n].max=e.relativeTarget[n].min+o)});let o=rI();rF(o,t,r.layoutBox);let a=rI();s?rF(a,e.applyTransform(n,!0),r.measuredBox):rF(a,t,r.layoutBox);let l=!nq(o),u=!1;if(!e.resumeFrom){let n=e.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:i,layout:s}=n;if(i&&s){let o=rU();rj(o,r.layoutBox,i.layoutBox);let a=rU();rj(a,t,s.layoutBox),nG(o,a)||(u=!0),n.options.layoutRoot&&(e.relativeTarget=a,e.relativeTargetOrigin=o,e.relativeParent=n)}}}e.notifyListeners("didUpdate",{layout:t,snapshot:r,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(e.isLead()){let{onExitComplete:t}=e.options;t&&t()}e.options.transition=void 0}function n9(e){z.value&&nZ.nodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function n4(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function n5(e){e.clearSnapshot()}function n8(e){e.clearMeasurements()}function n7(e){e.isLayoutDirty=!1}function ie(e){let{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function it(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function ir(e){e.resolveTargetDelta()}function ii(e){e.calcProjection()}function is(e){e.resetSkewAndRotation()}function io(e){e.removeLeadSnapshot()}function ia(e,t,r){e.translate=tR(t.translate,0,r),e.scale=tR(t.scale,1,r),e.origin=t.origin,e.originPoint=t.originPoint}function il(e,t,r,n){e.min=tR(t.min,r.min,n),e.max=tR(t.max,r.max,n)}function iu(e){return e.animationValues&&void 0!==e.animationValues.opacityExit}let ic={duration:.45,ease:[.4,0,.1,1]},ih=e=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(e),id=ih("applewebkit/")&&!ih("chrome/")?Math.round:j;function ip(e){e.min=id(e.min),e.max=id(e.max)}function im(e,t,r){return"position"===e||"preserve-aspect"===e&&!(.2>=Math.abs(nK(t)-nK(r)))}function ig(e){var t;return e!==e.root&&(null===(t=e.scroll)||void 0===t?void 0:t.wasRoot)}let iy=n2({attachResizeListener:(e,t)=>rT(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),iv={current:void 0},ib=n2({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!iv.current){let e=new iy({});e.mount(window),e.setOptions({layoutScroll:!0}),iv.current=e}return iv.current},resetTransform:(e,t)=>{e.style.transform=void 0!==t?t:"none"},checkIsScrollRoot:e=>"fixed"===window.getComputedStyle(e).position});function iw(e,t,r){let{props:n}=e;e.animationState&&n.whileHover&&e.animationState.setActive("whileHover","Start"===r);let i=n["onHover"+r];i&&K.postRender(()=>i(t,rA(t)))}class ix extends rC{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[n,i,s]=E(e,r),o=e=>{if(!S(e))return;let{target:r}=e,n=t(r,e);if("function"!=typeof n||!r)return;let s=e=>{S(e)&&(n(e),r.removeEventListener("pointerleave",s))};r.addEventListener("pointerleave",s,i)};return n.forEach(e=>{e.addEventListener("pointerenter",o,i)}),s}(e,(e,t)=>(iw(this.node,t,"Start"),e=>iw(this.node,e,"End"))))}unmount(){}}class i_ extends rC{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch(t){e=!0}e&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tB(rT(this.node.current,"focus",()=>this.onFocus()),rT(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function iC(e,t,r){let{props:n}=e;if(e.current instanceof HTMLButtonElement&&e.current.disabled)return;e.animationState&&n.whileTap&&e.animationState.setActive("whileTap","Start"===r);let i=n["onTap"+("End"===r?"":r)];i&&K.postRender(()=>i(t,rA(t)))}class iE extends rC{mount(){let{current:e}=this.node;e&&(this.unmount=function(e,t,r={}){let[n,i,s]=E(e,r),o=e=>{let r=e.currentTarget;if(!r||!F(e)||P.has(r))return;P.add(r),k(e,"set");let n=t(r,e),s=(e,t)=>{r.removeEventListener("pointerup",o),r.removeEventListener("pointercancel",a),k(e,"release"),F(e)&&P.has(r)&&(P.delete(r),"function"==typeof n&&n(e,{success:t}))},o=e=>{var t,n;e.isTrusted&&(t=e,n=r instanceof Element?r.getBoundingClientRect():{left:0,top:0,right:window.innerWidth,bottom:window.innerHeight},t.clientX<n.left||t.clientX>n.right||t.clientY<n.top||t.clientY>n.bottom)?s(e,!1):s(e,!(r instanceof Element)||T(r,e.target))},a=e=>{s(e,!1)};r.addEventListener("pointerup",o,i),r.addEventListener("pointercancel",a,i),r.addEventListener("lostpointercapture",a,i)};return n.forEach(e=>{e=r.useGlobalTarget?window:e;let t=!1;if(e instanceof HTMLElement){var n;t=!0,n=e,!R.has(n.tagName)&&-1===n.tabIndex&&null===e.getAttribute("tabindex")&&(e.tabIndex=0)}e.addEventListener("pointerdown",o,i),t&&e.addEventListener("focus",e=>L(e,i),i)}),s}(e,(e,t)=>(iC(this.node,t,"Start"),(e,{success:t})=>iC(this.node,e,t?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let iS=new WeakMap,ik=new WeakMap,iT=e=>{let t=iS.get(e.target);t&&t(e)},iA=e=>{e.forEach(iT)},iR={some:0,all:1};class iP extends rC{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:t,margin:r,amount:n="some",once:i}=e,s={root:t?t.current:void 0,rootMargin:r,threshold:"number"==typeof n?n:iR[n]};return function(e,t,r){let n=function({root:e,...t}){let r=e||document;ik.has(r)||ik.set(r,{});let n=ik.get(r),i=JSON.stringify(t);return n[i]||(n[i]=new IntersectionObserver(iA,{root:e,...t})),n[i]}(t);return iS.set(e,r),n.observe(e),()=>{iS.delete(e),n.unobserve(e)}}(this.node.current,s,e=>{let{isIntersecting:t}=e;if(this.isInView===t||(this.isInView=t,i&&!t&&this.hasEnteredView))return;t&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",t);let{onViewportEnter:r,onViewportLeave:n}=this.node.getProps(),s=t?r:n;s&&s(e)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:e,prevProps:t}=this.node;["amount","margin","root"].some(function({viewport:e={}},{viewport:t={}}={}){return r=>e[r]!==t[r]}(e,t))&&this.startObserver()}unmount(){}}let iO=(0,nh.createContext)({strict:!1}),iM=(0,nh.createContext)({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),iL=(0,nh.createContext)({});function iF(e){return i(e.animate)||ry.some(t=>rm(e[t]))}function iD(e){return!!(iF(e)||e.variants)}function iN(e){return Array.isArray(e)?e.join(" "):e}let ij="undefined"!=typeof window,iB={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},iI={};for(let e in iB)iI[e]={isEnabled:t=>iB[e].some(e=>!!t[e])};let iV=Symbol.for("motionComponentSymbol"),iU=ij?nh.useLayoutEffect:nh.useEffect;function i$(e,{layout:t,layoutId:r}){return I.has(e)||e.startsWith("origin")||(t||void 0!==r)&&(!!nw[e]||"opacity"===e)}let iW=(e,t)=>t&&"number"==typeof e?t.transform(e):e,iq={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},iH=B.length;function iz(e,t,r){let{style:n,vars:i,transformOrigin:s}=e,o=!1,a=!1;for(let e in t){let r=t[e];if(I.has(e)){o=!0;continue}if(tm(e)){i[e]=r;continue}{let t=iW(r,e4[e]);e.startsWith("origin")?(a=!0,s[e]=t):n[e]=t}}if(!t.transform&&(o||r?n.transform=function(e,t,r){let n="",i=!0;for(let s=0;s<iH;s++){let o=B[s],a=e[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===+!!o.startsWith("scale"):0===parseFloat(a))||r){let e=iW(a,e4[o]);if(!l){i=!1;let t=iq[o]||o;n+=`${t}(${e}) `}r&&(t[o]=e)}}return n=n.trim(),r?n=r(t,i?"":n):i&&(n="none"),n}(t,e.transform,r):n.transform&&(n.transform="none")),a){let{originX:e="50%",originY:t="50%",originZ:r=0}=s;n.transformOrigin=`${e} ${t} ${r}`}}let iG=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function iK(e,t,r){for(let n in t)ea(t[n])||i$(n,r)||(e[n]=t[n])}let iX=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function iY(e){return e.startsWith("while")||e.startsWith("drag")&&"draggable"!==e||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||iX.has(e)}let iZ=e=>!iY(e);try{!function(e){e&&(iZ=t=>t.startsWith("on")?!iY(t):e(t))}(require("@emotion/is-prop-valid").default)}catch(e){}let iJ=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function iQ(e){if("string"!=typeof e||e.includes("-"));else if(iJ.indexOf(e)>-1||/[A-Z]/u.test(e))return!0;return!1}let i0={offset:"stroke-dashoffset",array:"stroke-dasharray"},i1={offset:"strokeDashoffset",array:"strokeDasharray"};function i2(e,t,r){return"string"==typeof e?e:eV.transform(t+r*e)}function i3(e,{attrX:t,attrY:r,attrScale:n,originX:i,originY:s,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},c,h){if(iz(e,u,h),c){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};let{attrs:d,style:p,dimensions:f}=e;d.transform&&(f&&(p.transform=d.transform),delete d.transform),f&&(void 0!==i||void 0!==s||p.transform)&&(p.transformOrigin=function(e,t,r){let n=i2(t,e.x,e.width),i=i2(r,e.y,e.height);return`${n} ${i}`}(f,void 0!==i?i:.5,void 0!==s?s:.5)),void 0!==t&&(d.x=t),void 0!==r&&(d.y=r),void 0!==n&&(d.scale=n),void 0!==o&&function(e,t,r=1,n=0,i=!0){e.pathLength=1;let s=i?i0:i1;e[s.offset]=eV.transform(-n);let o=eV.transform(t),a=eV.transform(r);e[s.array]=`${o} ${a}`}(d,o,a,l,!1)}let i6=()=>({...iG(),attrs:{}}),i9=e=>"string"==typeof e&&"svg"===e.toLowerCase(),i4=e=>(t,r)=>{let n=(0,nh.useContext)(iL),s=(0,nh.useContext)(nd),a=()=>(function({scrapeMotionValuesFromProps:e,createRenderState:t,onUpdate:r},n,s,a){let l={latestValues:function(e,t,r,n){let s={},a=n(e,{});for(let e in a)s[e]=nk(a[e]);let{initial:l,animate:u}=e,c=iF(e),h=iD(e);t&&h&&!c&&!1!==e.inherit&&(void 0===l&&(l=t.initial),void 0===u&&(u=t.animate));let d=!!r&&!1===r.initial,p=(d=d||!1===l)?u:l;if(p&&"boolean"!=typeof p&&!i(p)){let t=Array.isArray(p)?p:[p];for(let r=0;r<t.length;r++){let n=o(e,t[r]);if(n){let{transitionEnd:e,transition:t,...r}=n;for(let e in r){let t=r[e];if(Array.isArray(t)){let e=d?t.length-1:0;t=t[e]}null!==t&&(s[e]=t)}for(let t in e)s[t]=e[t]}}}return s}(n,s,a,e),renderState:t()};return r&&(l.onMount=e=>r({props:n,current:e,...l}),l.onUpdate=e=>r(e)),l})(e,t,n,s);return r?a():function(e){let t=(0,nh.useRef)(null);return null===t.current&&(t.current=e()),t.current}(a)};function i5(e,t,r){var n;let{style:i}=e,s={};for(let o in i)(ea(i[o])||t.style&&ea(t.style[o])||i$(o,e)||(null===(n=null==r?void 0:r.getValue(o))||void 0===n?void 0:n.liveStyle)!==void 0)&&(s[o]=i[o]);return s}let i8={useVisualState:i4({scrapeMotionValuesFromProps:i5,createRenderState:iG})};function i7(e,t){try{t.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(e){t.dimensions={x:0,y:0,width:0,height:0}}}function se(e,{style:t,vars:r},n,i){for(let s in Object.assign(e.style,t,i&&i.getProjectionStyles(n)),r)e.style.setProperty(s,r[s])}let st=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function sr(e,t,r,n){for(let r in se(e,t,void 0,n),t.attrs)e.setAttribute(st.has(r)?r:eu(r),t.attrs[r])}function sn(e,t,r){let n=i5(e,t,r);for(let r in e)(ea(e[r])||ea(t[r]))&&(n[-1!==B.indexOf(r)?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r]=e[r]);return n}let si=["x","y","width","height","cx","cy","r"],ss={useVisualState:i4({scrapeMotionValuesFromProps:sn,createRenderState:i6,onUpdate:({props:e,prevProps:t,current:r,renderState:n,latestValues:i})=>{if(!r)return;let s=!!e.drag;if(!s){for(let e in i)if(I.has(e)){s=!0;break}}if(!s)return;let o=!t;if(t)for(let r=0;r<si.length;r++){let n=si[r];e[n]!==t[n]&&(o=!0)}o&&K.read(()=>{i7(r,n),K.render(()=>{i3(n,i,i9(r.tagName),e.transformTemplate),sr(r,n)})})}})},so={current:null},sa={current:!1},sl=[...tx,eH,e0],su=e=>sl.find(tw(e)),sc=new WeakMap,sh=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class sd{scrapeMotionValuesFromProps(e,t,r){return{}}constructor({parent:e,props:t,presenceContext:r,reducedMotionConfig:n,blockInitialAnimation:i,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=td,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let e=Q.now();this.renderScheduledAt<e&&(this.renderScheduledAt=e,K.render(this.render,!1,!0))};let{latestValues:a,renderState:l,onUpdate:u}=s;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=t.initial?{...a}:{},this.renderState=l,this.parent=e,this.props=t,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!i,this.isControllingVariants=iF(t),this.isVariantNode=iD(t),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:c,...h}=this.scrapeMotionValuesFromProps(t,{},this);for(let e in h){let t=h[e];void 0!==a[e]&&ea(t)&&t.set(a[e],!1)}}mount(e){this.current=e,sc.set(e,this),this.projection&&!this.projection.instance&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((e,t)=>this.bindToMotionValue(t,e)),sa.current||function(){if(sa.current=!0,ij){if(window.matchMedia){let e=window.matchMedia("(prefers-reduced-motion)"),t=()=>so.current=e.matches;e.addListener(t),t()}else so.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||so.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let e in this.projection&&this.projection.unmount(),X(this.notifyUpdate),X(this.render),this.valueSubscriptions.forEach(e=>e()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[e].clear();for(let e in this.features){let t=this.features[e];t&&(t.unmount(),t.isMounted=!1)}this.current=null}bindToMotionValue(e,t){let r;this.valueSubscriptions.has(e)&&this.valueSubscriptions.get(e)();let n=I.has(e);n&&this.onBindTransform&&this.onBindTransform();let i=t.on("change",t=>{this.latestValues[e]=t,this.props.onUpdate&&K.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),s=t.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(r=window.MotionCheckAppearSync(this,e,t)),this.valueSubscriptions.set(e,()=>{i(),s(),r&&r(),t.owner&&t.stop()})}sortNodePosition(e){return this.current&&this.sortInstanceNodePosition&&this.type===e.type?this.sortInstanceNodePosition(this.current,e.current):0}updateFeatures(){let e="animation";for(e in iI){let t=iI[e];if(!t)continue;let{isEnabled:r,Feature:n}=t;if(!this.features[e]&&n&&r(this.props)&&(this.features[e]=new n(this)),this.features[e]){let t=this.features[e];t.isMounted?t.update():(t.mount(),t.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):rU()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,t){this.latestValues[e]=t}update(e,t){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=t;for(let t=0;t<sh.length;t++){let r=sh[t];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let n=e["on"+r];n&&(this.propEventSubscriptions[r]=this.on(r,n))}this.prevMotionValues=function(e,t,r){for(let n in t){let i=t[n],s=r[n];if(ea(i))e.addValue(n,i);else if(ea(s))e.addValue(n,eo(i,{owner:e}));else if(s!==i){if(e.hasValue(n)){let t=e.getValue(n);!0===t.liveStyle?t.jump(i):t.hasAnimated||t.set(i)}else{let t=e.getStaticValue(n);e.addValue(n,eo(void 0!==t?t:i,{owner:e}))}}}for(let n in r)void 0===t[n]&&e.removeValue(n);return t}(this,this.scrapeMotionValuesFromProps(e,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(e){let t=this.getClosestVariantNode();if(t)return t.variantChildren&&t.variantChildren.add(e),()=>t.variantChildren.delete(e)}addValue(e,t){let r=this.values.get(e);t!==r&&(r&&this.removeValue(e),this.bindToMotionValue(e,t),this.values.set(e,t),this.latestValues[e]=t.get())}removeValue(e){this.values.delete(e);let t=this.valueSubscriptions.get(e);t&&(t(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,t){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return void 0===r&&void 0!==t&&(r=eo(null===t?void 0:t,{owner:this}),this.addValue(e,r)),r}readValue(e,t){var r;let n=void 0===this.latestValues[e]&&this.current?null!==(r=this.getBaseTargetFromProps(this.props,e))&&void 0!==r?r:this.readValueFromInstance(this.current,e,this.options):this.latestValues[e];return null!=n&&("string"==typeof n&&(tp(n)||eC(n))?n=parseFloat(n):!su(n)&&e0.test(t)&&(n=e7(e,t)),this.setBaseTarget(e,ea(n)?n.get():n)),ea(n)?n.get():n}setBaseTarget(e,t){this.baseTarget[e]=t}getBaseTarget(e){var t;let r;let{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let i=o(this.props,n,null===(t=this.presenceContext)||void 0===t?void 0:t.custom);i&&(r=i[e])}if(n&&void 0!==r)return r;let i=this.getBaseTargetFromProps(this.props,e);return void 0===i||ea(i)?void 0!==this.initialValues[e]&&void 0===r?void 0:this.baseTarget[e]:i}on(e,t){return this.events[e]||(this.events[e]=new er),this.events[e].add(t)}notify(e,...t){this.events[e]&&this.events[e].notify(...t)}}class sp extends sd{constructor(){super(...arguments),this.KeyframeResolver=tC}sortInstanceNodePosition(e,t){return 2&e.compareDocumentPosition(t)?1:-1}getBaseTargetFromProps(e,t){return e.style?e.style[t]:void 0}removeValueFromRenderState(e,{vars:t,style:r}){delete t[e],delete r[e]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;ea(e)&&(this.childSubscription=e.on("change",e=>{this.current&&(this.current.textContent=`${e}`)}))}}class sf extends sp{constructor(){super(...arguments),this.type="html",this.renderInstance=se}readValueFromInstance(e,t){if(I.has(t)){let e=e8(t);return e&&e.default||0}{let r=window.getComputedStyle(e),n=(tm(t)?r.getPropertyValue(t):r[t])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(e,{transformPagePoint:t}){return rQ(e,t)}build(e,t,r){iz(e,t,r.transformTemplate)}scrapeMotionValuesFromProps(e,t,r){return i5(e,t,r)}}class sm extends sp{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=rU,this.updateDimensions=()=>{this.current&&!this.renderState.dimensions&&i7(this.current,this.renderState)}}getBaseTargetFromProps(e,t){return e[t]}readValueFromInstance(e,t){if(I.has(t)){let e=e8(t);return e&&e.default||0}return t=st.has(t)?t:eu(t),e.getAttribute(t)}scrapeMotionValuesFromProps(e,t,r){return sn(e,t,r)}onBindTransform(){this.current&&!this.renderState.dimensions&&K.postRender(this.updateDimensions)}build(e,t,r){i3(e,t,this.isSVGTag,r.transformTemplate)}renderInstance(e,t,r,n){sr(e,t,r,n)}mount(e){this.isSVGTag=i9(e.tagName),super.mount(e)}}let sg=function(e){if("undefined"==typeof Proxy)return e;let t=new Map;return new Proxy((...t)=>e(...t),{get:(r,n)=>"create"===n?e:(t.has(n)||t.set(n,e(n)),t.get(n))})}((nl={animation:{Feature:rE},exit:{Feature:rk},inView:{Feature:iP},tap:{Feature:iE},focus:{Feature:i_},hover:{Feature:ix},pan:{Feature:na},drag:{Feature:ns,ProjectionNode:ib,MeasureLayout:n_},layout:{ProjectionNode:ib,MeasureLayout:n_}},nu=(e,t)=>iQ(e)?new sm(t):new sf(t,{allowProjection:e!==nh.Fragment}),function(e,{forwardMotionProps:t}={forwardMotionProps:!1}){return function({preloadedFeatures:e,createVisualElement:t,useRender:r,useVisualState:n,Component:i}){var s,o;function a(e,s){var o,a,l;let u;let c={...(0,nh.useContext)(iM),...e,layoutId:function({layoutId:e}){let t=(0,nh.useContext)(np).id;return t&&void 0!==e?t+"-"+e:e}(e)},{isStatic:h}=c,d=function(e){let{initial:t,animate:r}=function(e,t){if(iF(e)){let{initial:t,animate:r}=e;return{initial:!1===t||rm(t)?t:void 0,animate:rm(r)?r:void 0}}return!1!==e.inherit?t:{}}(e,(0,nh.useContext)(iL));return(0,nh.useMemo)(()=>({initial:t,animate:r}),[iN(t),iN(r)])}(e),p=n(e,h);if(!h&&ij){a=0,l=0,(0,nh.useContext)(iO).strict;let e=function(e){let{drag:t,layout:r}=iI;if(!t&&!r)return{};let n={...t,...r};return{MeasureLayout:(null==t?void 0:t.isEnabled(e))||(null==r?void 0:r.isEnabled(e))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(c);u=e.MeasureLayout,d.visualElement=function(e,t,r,n,i){var s,o;let{visualElement:a}=(0,nh.useContext)(iL),l=(0,nh.useContext)(iO),u=(0,nh.useContext)(nd),c=(0,nh.useContext)(iM).reducedMotion,h=(0,nh.useRef)(null);n=n||l.renderer,!h.current&&n&&(h.current=n(e,{visualState:t,parent:a,props:r,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:c}));let d=h.current,p=(0,nh.useContext)(nf);d&&!d.projection&&i&&("html"===d.type||"svg"===d.type)&&function(e,t,r,n){let{layoutId:i,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u}=t;e.projection=new r(e.latestValues,t["data-framer-portal-id"]?void 0:function e(t){if(t)return!1!==t.options.allowProjection?t.projection:e(t.parent)}(e.parent)),e.projection.setOptions({layoutId:i,layout:s,alwaysMeasureLayout:!!o||a&&r0(a),visualElement:e,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,layoutScroll:l,layoutRoot:u})}(h.current,r,i,p);let f=(0,nh.useRef)(!1);(0,nh.useInsertionEffect)(()=>{d&&f.current&&d.update(r,u)});let m=r[ec],g=(0,nh.useRef)(!!m&&!(null===(s=window.MotionHandoffIsComplete)||void 0===s?void 0:s.call(window,m))&&(null===(o=window.MotionHasOptimisedAnimation)||void 0===o?void 0:o.call(window,m)));return iU(()=>{d&&(f.current=!0,window.MotionIsMounted=!0,d.updateFeatures(),nm.render(d.render),g.current&&d.animationState&&d.animationState.animateChanges())}),(0,nh.useEffect)(()=>{d&&(!g.current&&d.animationState&&d.animationState.animateChanges(),g.current&&(queueMicrotask(()=>{var e;null===(e=window.MotionHandoffMarkAsComplete)||void 0===e||e.call(window,m)}),g.current=!1))}),d}(i,p,c,t,e.ProjectionNode)}return(0,nc.jsxs)(iL.Provider,{value:d,children:[u&&d.visualElement?(0,nc.jsx)(u,{visualElement:d.visualElement,...c}):null,r(i,e,(o=d.visualElement,(0,nh.useCallback)(e=>{e&&p.onMount&&p.onMount(e),o&&(e?o.mount(e):o.unmount()),s&&("function"==typeof s?s(e):r0(s)&&(s.current=e))},[o])),p,h,d.visualElement)]})}e&&function(e){for(let t in e)iI[t]={...iI[t],...e[t]}}(e),a.displayName=`motion.${"string"==typeof i?i:`create(${null!==(o=null!==(s=i.displayName)&&void 0!==s?s:i.name)&&void 0!==o?o:""})`}`;let l=(0,nh.forwardRef)(a);return l[iV]=i,l}({...iQ(e)?ss:i8,preloadedFeatures:nl,useRender:function(e=!1){return(t,r,n,{latestValues:i},s)=>{let o=(iQ(t)?function(e,t,r,n){let i=(0,nh.useMemo)(()=>{let r=i6();return i3(r,t,i9(n),e.transformTemplate),{...r.attrs,style:{...r.style}}},[t]);if(e.style){let t={};iK(t,e.style,e),i.style={...t,...i.style}}return i}:function(e,t){let r={},n=function(e,t){let r=e.style||{},n={};return iK(n,r,e),Object.assign(n,function({transformTemplate:e},t){return(0,nh.useMemo)(()=>{let r=iG();return iz(r,t,e),Object.assign({},r.vars,r.style)},[t])}(e,t)),n}(e,t);return e.drag&&!1!==e.dragListener&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===e.drag?"none":`pan-${"x"===e.drag?"y":"x"}`),void 0===e.tabIndex&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=n,r})(r,i,s,t),a=function(e,t,r){let n={};for(let i in e)("values"!==i||"object"!=typeof e.values)&&(iZ(i)||!0===r&&iY(i)||!t&&!iY(i)||e.draggable&&i.startsWith("onDrag"))&&(n[i]=e[i]);return n}(r,"string"==typeof t,e),l=t!==nh.Fragment?{...a,...o,ref:n}:{},{children:u}=r,c=(0,nh.useMemo)(()=>ea(u)?u.get():u,[u]);return(0,nh.createElement)(t,{...l,children:c})}}(t),createVisualElement:nu,Component:e})}))},83090:(e,t,r)=>{"use strict";let{Duplex:n}=r(27910);function i(e){e.emit("close")}function s(){!this.destroyed&&this._writableState.finished&&this.destroy()}function o(e){this.removeListener("error",o),this.destroy(),0===this.listenerCount("error")&&this.emit("error",e)}e.exports=function(e,t){let r=!0,a=new n({...t,autoDestroy:!1,emitClose:!1,objectMode:!1,writableObjectMode:!1});return e.on("message",function(t,r){let n=!r&&a._readableState.objectMode?t.toString():t;a.push(n)||e.pause()}),e.once("error",function(e){a.destroyed||(r=!1,a.destroy(e))}),e.once("close",function(){a.destroyed||a.push(null)}),a._destroy=function(t,n){if(e.readyState===e.CLOSED){n(t),process.nextTick(i,a);return}let s=!1;e.once("error",function(e){s=!0,n(e)}),e.once("close",function(){s||n(t),process.nextTick(i,a)}),r&&e.terminate()},a._final=function(t){if(e.readyState===e.CONNECTING){e.once("open",function(){a._final(t)});return}null!==e._socket&&(e._socket._writableState.finished?(t(),a._readableState.endEmitted&&a.destroy()):(e._socket.once("finish",function(){t()}),e.close()))},a._read=function(){e.isPaused&&e.resume()},a._write=function(t,r,n){if(e.readyState===e.CONNECTING){e.once("open",function(){a._write(t,r,n)});return}e.send(t,n)},a.on("end",s),a.on("error",o),a}},83753:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Bot",[["path",{d:"M12 8V4H8",key:"hb8ula"}],["rect",{width:"16",height:"12",x:"4",y:"8",rx:"2",key:"enze0r"}],["path",{d:"M2 14h2",key:"vft8re"}],["path",{d:"M20 14h2",key:"4cs60a"}],["path",{d:"M15 13v2",key:"1xurst"}],["path",{d:"M9 13v2",key:"rq6x2g"}]])},84027:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},88573:(e,t,r)=>{t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let r="color: "+this.color;t.splice(1,0,r,"color: inherit");let n=0,i=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(i=n))}),t.splice(i,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch(e){}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},t.useColors=function(){let e;return"undefined"!=typeof window&&!!window.process&&("renderer"===window.process.type||!!window.process.__nwjs)||!("undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&(e=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(e[1],10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch(e){}}(),t.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=r(29940)(t);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},91813:e=>{"use strict";e.exports={BINARY_TYPES:["nodebuffer","arraybuffer","fragments"],EMPTY_BUFFER:Buffer.alloc(0),GUID:"258EAFA5-E914-47DA-95CA-C5AB0DC85B11",kForOnEventAttribute:Symbol("kIsForOnEventAttribute"),kListener:Symbol("kListener"),kStatusCode:Symbol("status-code"),kWebSocket:Symbol("websocket"),NOOP:()=>{}}},92951:(e,t,r)=>{"use strict";r.d(t,{H4:()=>x,_V:()=>w,bL:()=>b});var n=r(43210),i=r(11273),s=r(13495),o=r(66156),a=r(14163),l=r(60687),u="Avatar",[c,h]=(0,i.A)(u),[d,p]=c(u),f=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...i}=e,[s,o]=n.useState("idle");return(0,l.jsx)(d,{scope:r,imageLoadingStatus:s,onImageLoadingStatusChange:o,children:(0,l.jsx)(a.sG.span,{...i,ref:t})})});f.displayName=u;var m="AvatarImage",g=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:i,onLoadingStatusChange:u=()=>{},...c}=e,h=p(m,r),d=function(e,t){let[r,i]=n.useState("idle");return(0,o.N)(()=>{if(!e){i("error");return}let r=!0,n=new window.Image,s=e=>()=>{r&&i(e)};return i("loading"),n.onload=s("loaded"),n.onerror=s("error"),n.src=e,t&&(n.referrerPolicy=t),()=>{r=!1}},[e,t]),r}(i,c.referrerPolicy),f=(0,s.c)(e=>{u(e),h.onImageLoadingStatusChange(e)});return(0,o.N)(()=>{"idle"!==d&&f(d)},[d,f]),"loaded"===d?(0,l.jsx)(a.sG.img,{...c,ref:t,src:i}):null});g.displayName=m;var y="AvatarFallback",v=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:i,...s}=e,o=p(y,r),[u,c]=n.useState(void 0===i);return n.useEffect(()=>{if(void 0!==i){let e=window.setTimeout(()=>c(!0),i);return()=>window.clearTimeout(e)}},[i]),u&&"loaded"!==o.imageLoadingStatus?(0,l.jsx)(a.sG.span,{...s,ref:t}):null});v.displayName=y;var b=f,w=g,x=v},96963:(e,t,r)=>{"use strict";r.d(t,{B:()=>l});var n,i=r(43210),s=r(66156),o=(n||(n=r.t(i,2)))["useId".toString()]||(()=>void 0),a=0;function l(e){let[t,r]=i.useState(o());return(0,s.N)(()=>{e||r(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},98015:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(62688).A)("ScrollText",[["path",{d:"M15 12h-5",key:"r7krc0"}],["path",{d:"M15 8h-5",key:"1khuty"}],["path",{d:"M19 17V5a2 2 0 0 0-2-2H4",key:"zz82l3"}],["path",{d:"M8 21h12a2 2 0 0 0 2-2v-1a1 1 0 0 0-1-1H11a1 1 0 0 0-1 1v1a2 2 0 1 1-4 0V5a2 2 0 1 0-4 0v2a1 1 0 0 0 1 1h3",key:"1ph1d7"}]])},99616:(e,t,r)=>{"undefined"==typeof process||"renderer"===process.type||process.__nwjs?e.exports=r(76420):e.exports=r(3890)}};