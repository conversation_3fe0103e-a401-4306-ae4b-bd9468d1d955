"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1906],{9428:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(19946).A)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},14885:(e,t,n)=>{n.d(t,{C1:()=>O,bL:()=>j});var r=n(12115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function i(...e){return t=>{let n=!1,r=e.map(e=>{let r=o(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():o(e[t],null)}}}}function u(...e){return r.useCallback(i(...e),e)}var a=n(95155);function l(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function s(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}var d=globalThis?.document?r.useLayoutEffect:()=>{},c=e=>{let{present:t,children:n}=e,o=function(e){var t,n;let[o,i]=r.useState(),u=r.useRef({}),a=r.useRef(e),l=r.useRef("none"),[s,c]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=f(u.current);l.current="mounted"===s?e:"none"},[s]),d(()=>{let t=u.current,n=a.current;if(n!==e){let r=l.current,o=f(t);e?c("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):n&&r!==o?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),d(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=f(u.current).includes(e.animationName);if(e.target===o&&r&&(c("ANIMATION_END"),!a.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(l.current=f(u.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}c("ANIMATION_END")},[o,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:r.useCallback(e=>{e&&(u.current=getComputedStyle(e)),i(e)},[])}}(t),i="function"==typeof n?n({present:o.isPresent}):r.Children.only(n),a=u(o.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(i));return"function"==typeof n||o.isPresent?r.cloneElement(i,{ref:a}):null};function f(e){return(null==e?void 0:e.animationName)||"none"}c.displayName="Presence",n(47650);var p=Symbol("radix.slottable");function m(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===p}var v=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=function(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){var u;let e,a;let l=(u=n,(a=(e=Object.getOwnPropertyDescriptor(u.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?u.ref:(a=(e=Object.getOwnPropertyDescriptor(u,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?u.props.ref:u.props.ref||u.ref),s=function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props);return n.type!==r.Fragment&&(s.ref=t?i(t,l):l),r.cloneElement(n,s)}return r.Children.count(n)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),n=r.forwardRef((e,n)=>{let{children:o,...i}=e,u=r.Children.toArray(o),l=u.find(m);if(l){let e=l.props.children,o=u.map(t=>t!==l?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...i,ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null})}return(0,a.jsx)(t,{...i,ref:n,children:o})});return n.displayName=`${e}.Slot`,n}(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),y="Checkbox",[h,b]=function(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return o.scopeName=e,[function(t,o){let i=r.createContext(o),u=n.length;n=[...n,o];let l=t=>{let{scope:n,children:o,...l}=t,s=n?.[e]?.[u]||i,d=r.useMemo(()=>l,Object.values(l));return(0,a.jsx)(s.Provider,{value:d,children:o})};return l.displayName=t+"Provider",[l,function(n,a){let l=a?.[e]?.[u]||i,s=r.useContext(l);if(s)return s;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(o,...t)]}(y),[w,g]=h(y),N=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,name:o,checked:i,defaultChecked:d,required:c,disabled:f,value:p="on",onCheckedChange:m,form:y,...h}=e,[b,g]=r.useState(null),N=u(t,e=>g(e)),E=r.useRef(!1),k=!b||y||!!b.closest("form"),[j=!1,O]=function({prop:e,defaultProp:t,onChange:n=()=>{}}){let[o,i]=function({defaultProp:e,onChange:t}){let n=r.useState(e),[o]=n,i=r.useRef(o),u=s(t);return r.useEffect(()=>{i.current!==o&&(u(o),i.current=o)},[o,i,u]),n}({defaultProp:t,onChange:n}),u=void 0!==e,a=u?e:o,l=s(n);return[a,r.useCallback(t=>{if(u){let n="function"==typeof t?t(e):t;n!==e&&l(n)}else i(t)},[u,e,i,l])]}({prop:i,defaultProp:d,onChange:m}),A=r.useRef(j);return r.useEffect(()=>{let e=null==b?void 0:b.form;if(e){let t=()=>O(A.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[b,O]),(0,a.jsxs)(w,{scope:n,state:j,disabled:f,children:[(0,a.jsx)(v.button,{type:"button",role:"checkbox","aria-checked":x(j)?"mixed":j,"aria-required":c,"data-state":C(j),"data-disabled":f?"":void 0,disabled:f,value:p,...h,ref:N,onKeyDown:l(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:l(e.onClick,e=>{O(e=>!!x(e)||!e),k&&(E.current=e.isPropagationStopped(),E.current||e.stopPropagation())})}),k&&(0,a.jsx)(R,{control:b,bubbles:!E.current,name:o,value:p,checked:j,required:c,disabled:f,form:y,style:{transform:"translateX(-100%)"},defaultChecked:!x(d)&&d})]})});N.displayName=y;var E="CheckboxIndicator",k=r.forwardRef((e,t)=>{let{__scopeCheckbox:n,forceMount:r,...o}=e,i=g(E,n);return(0,a.jsx)(c,{present:r||x(i.state)||!0===i.state,children:(0,a.jsx)(v.span,{"data-state":C(i.state),"data-disabled":i.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});k.displayName=E;var R=e=>{let{control:t,checked:n,bubbles:o=!0,defaultChecked:i,...u}=e,l=r.useRef(null),s=function(e){let t=r.useRef({value:e,previous:e});return r.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(n),c=function(e){let[t,n]=r.useState(void 0);return d(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(t);r.useEffect(()=>{let e=l.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(s!==n&&t){let r=new Event("click",{bubbles:o});e.indeterminate=x(n),t.call(e,!x(n)&&n),e.dispatchEvent(r)}},[s,n,o]);let f=r.useRef(!x(n)&&n);return(0,a.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:null!=i?i:f.current,...u,tabIndex:-1,ref:l,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function x(e){return"indeterminate"===e}function C(e){return x(e)?"indeterminate":e?"checked":"unchecked"}var j=N,O=k},54059:(e,t,n)=>{n.d(t,{C1:()=>W,bL:()=>D,q7:()=>U});var r=n(12115),o=n(85185),i=n(6101),u=n(46081),a=n(63655),l=n(89196),s=n(5845),d=n(94315),c=n(11275),f=n(45503),p=n(28905),m=n(95155),v="Radio",[y,h]=(0,u.A)(v),[b,w]=y(v),g=r.forwardRef((e,t)=>{let{__scopeRadio:n,name:u,checked:l=!1,required:s,disabled:d,value:c="on",onCheck:f,form:p,...v}=e,[y,h]=r.useState(null),w=(0,i.s)(t,e=>h(e)),g=r.useRef(!1),N=!y||p||!!y.closest("form");return(0,m.jsxs)(b,{scope:n,checked:l,disabled:d,children:[(0,m.jsx)(a.sG.button,{type:"button",role:"radio","aria-checked":l,"data-state":R(l),"data-disabled":d?"":void 0,disabled:d,value:c,...v,ref:w,onClick:(0,o.m)(e.onClick,e=>{l||null==f||f(),N&&(g.current=e.isPropagationStopped(),g.current||e.stopPropagation())})}),N&&(0,m.jsx)(k,{control:y,bubbles:!g.current,name:u,value:c,checked:l,required:s,disabled:d,form:p,style:{transform:"translateX(-100%)"}})]})});g.displayName=v;var N="RadioIndicator",E=r.forwardRef((e,t)=>{let{__scopeRadio:n,forceMount:r,...o}=e,i=w(N,n);return(0,m.jsx)(p.C,{present:r||i.checked,children:(0,m.jsx)(a.sG.span,{"data-state":R(i.checked),"data-disabled":i.disabled?"":void 0,...o,ref:t})})});E.displayName=N;var k=e=>{let{control:t,checked:n,bubbles:o=!0,...i}=e,u=r.useRef(null),a=(0,f.Z)(n),l=(0,c.X)(t);return r.useEffect(()=>{let e=u.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(a!==n&&t){let r=new Event("click",{bubbles:o});t.call(e,n),e.dispatchEvent(r)}},[a,n,o]),(0,m.jsx)("input",{type:"radio","aria-hidden":!0,defaultChecked:n,...i,tabIndex:-1,ref:u,style:{...e.style,...l,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function R(e){return e?"checked":"unchecked"}var x=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],C="RadioGroup",[j,O]=(0,u.A)(C,[l.RG,h]),A=(0,l.RG)(),M=h(),[P,I]=j(C),S=r.forwardRef((e,t)=>{let{__scopeRadioGroup:n,name:r,defaultValue:o,value:i,required:u=!1,disabled:c=!1,orientation:f,dir:p,loop:v=!0,onValueChange:y,...h}=e,b=A(n),w=(0,d.jH)(p),[g,N]=(0,s.i)({prop:i,defaultProp:o,onChange:y});return(0,m.jsx)(P,{scope:n,name:r,required:u,disabled:c,value:g,onValueChange:N,children:(0,m.jsx)(l.bL,{asChild:!0,...b,orientation:f,dir:w,loop:v,children:(0,m.jsx)(a.sG.div,{role:"radiogroup","aria-required":u,"aria-orientation":f,"data-disabled":c?"":void 0,dir:w,...h,ref:t})})})});S.displayName=C;var L="RadioGroupItem",_=r.forwardRef((e,t)=>{let{__scopeRadioGroup:n,disabled:u,...a}=e,s=I(L,n),d=s.disabled||u,c=A(n),f=M(n),p=r.useRef(null),v=(0,i.s)(t,p),y=s.value===a.value,h=r.useRef(!1);return r.useEffect(()=>{let e=e=>{x.includes(e.key)&&(h.current=!0)},t=()=>h.current=!1;return document.addEventListener("keydown",e),document.addEventListener("keyup",t),()=>{document.removeEventListener("keydown",e),document.removeEventListener("keyup",t)}},[]),(0,m.jsx)(l.q7,{asChild:!0,...c,focusable:!d,active:y,children:(0,m.jsx)(g,{disabled:d,required:s.required,checked:y,...f,...a,name:s.name,ref:v,onCheck:()=>s.onValueChange(a.value),onKeyDown:(0,o.m)(e=>{"Enter"===e.key&&e.preventDefault()}),onFocus:(0,o.m)(a.onFocus,()=>{var e;h.current&&(null===(e=p.current)||void 0===e||e.click())})})})});_.displayName=L;var T=r.forwardRef((e,t)=>{let{__scopeRadioGroup:n,...r}=e,o=M(n);return(0,m.jsx)(E,{...o,...r,ref:t})});T.displayName="RadioGroupIndicator";var D=S,U=_,W=T}}]);