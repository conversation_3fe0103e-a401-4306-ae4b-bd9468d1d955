(()=>{var e={};e.id=9492,e.ids=[9492],e.modules={709:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,47429,23)),Promise.resolve().then(r.bind(r,6931)),Promise.resolve().then(r.bind(r,46678)),Promise.resolve().then(r.bind(r,96988))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11557:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23776:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>o});var n=r(60687),s=r(10218);function o({children:e,...t}){return(0,n.jsx)(s.N,{...t,enableSystem:!0,attribute:"class",defaultTheme:"system",disableTransitionOnChange:!1,children:e})}},24709:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},26674:(e,t,r)=>{"use strict";r.d(t,{default:()=>a});var n=r(60687),s=r(39091),o=r(8693),i=r(43210);function a({children:e}){let[t]=(0,i.useState)(()=>new s.E);return(0,n.jsx)(o.Ht,{client:t,children:e})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},46678:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\components\\theme\\ThemeProvider.tsx","ThemeProvider")},61135:()=>{},61904:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var n=r(65239),s=r(48088),o=r(88170),i=r.n(o),a=r(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=[],u={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},94431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v,metadata:()=>p});var n=r(37413),s=r(22376),o=r.n(s),i=r(68726),a=r.n(i);r(61135);var d=r(46678),l=r(6931),c=r(36162),u=r(96988);let p={title:"Orova AI",description:"Create, customize, and deploy AI agents effortlessly.",icons:{icon:"./favicon.png"}};function v({children:e}){return(0,n.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,n.jsxs)("head",{children:[(0,n.jsx)(c.default,{id:"microsoft-clarity",strategy:"afterInteractive",children:`
            (function(c,l,a,r,i,t,y){
              c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
              t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
              y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
            })(window, document, "clarity", "script", "${process.env.NEXT_PUBLIC_CLARITY_ID}");
          `}),(0,n.jsx)(c.default,{src:`https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GA_ID}`,strategy:"afterInteractive"}),(0,n.jsx)(c.default,{id:"google-analytics",strategy:"afterInteractive",children:`
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}
            gtag('js', new Date());
            gtag('config', '${process.env.NEXT_PUBLIC_GA_ID}');
          `})]}),(0,n.jsx)("body",{className:`${o().variable} ${a().variable} antialiased`,suppressHydrationWarning:!0,children:(0,n.jsx)(u.default,{children:(0,n.jsxs)(d.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:[(0,n.jsx)(l.Toaster,{position:"top-right",closeButton:!0,richColors:!0}),e]})})})]})}},94629:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,79167,23)),Promise.resolve().then(r.bind(r,52581)),Promise.resolve().then(r.bind(r,23776)),Promise.resolve().then(r.bind(r,26674))},96988:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\OneDrive - OS Websolutions B.V\\\\projects\\\\agent\\\\dev\\\\orova-vapi\\\\web\\\\src\\\\lib\\\\providers\\\\ReactQueryProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\OneDrive - OS Websolutions B.V\\projects\\agent\\dev\\orova-vapi\\web\\src\\lib\\providers\\ReactQueryProvider.tsx","default")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[287],()=>r(61904));module.exports=n})();