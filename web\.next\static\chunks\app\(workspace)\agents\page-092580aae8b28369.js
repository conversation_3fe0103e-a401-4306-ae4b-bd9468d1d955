(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4716],{2488:(e,t,a)=>{"use strict";a.d(t,{default:()=>l});var s=a(95155),r=a(226);function l(e){let{children:t,duration:a=.5,delay:l=0,direction:n="up",distance:i=30,className:o="",once:d=!0,viewOffset:c=.1}=e,u=0,m=0;return"up"===n&&(u=i),"down"===n&&(u=-i),"left"===n&&(m=i),"right"===n&&(m=-i),(0,s.jsx)(r.P.div,{initial:{y:u,x:m,opacity:0},whileInView:{y:0,x:0,opacity:1},transition:{duration:a,delay:l,ease:"easeOut"},viewport:{once:d,amount:c},className:o,children:t})}},6108:(e,t,a)=>{"use strict";a.d(t,{a:()=>i});var s=a(26715),r=a(32960),l=a(5041);async function n(){let e=localStorage.getItem("access_token"),t=await fetch("".concat("http://localhost:4000","/api/agents"),{headers:{Authorization:"Bearer ".concat(e)}});if(!t.ok)throw Error("Failed to fetch agents");return t.json()}function i(){let e=(0,s.jE)(),{data:t=[],isLoading:a,error:i}=(0,r.I)({queryKey:["agents"],queryFn:n,staleTime:1/0,gcTime:1/0}),{mutate:o}=(0,l.n)({mutationFn:e=>Promise.resolve(e),onSuccess:t=>{e.setQueryData(["agents"],t)}});return{agents:t,setAgents:o,agentsisLoading:a,deleteAgentMutation:(0,l.n)({mutationFn:async e=>{let t=localStorage.getItem("access_token");if(!t)throw Error("Authentication required");let a=await fetch("".concat("http://localhost:4000","/api/agents/").concat(e),{method:"DELETE",headers:{Authorization:"Bearer ".concat(t)}});if(!a.ok)throw Error("Failed to delete agent: ".concat(a.status))},onMutate:async t=>{await e.cancelQueries({queryKey:["agents"]});let a=e.getQueryData(["agents"]);return e.setQueryData(["agents"],e=>{var a;return null!==(a=null==e?void 0:e.filter(e=>e.id!==t))&&void 0!==a?a:[]}),{previousAgents:a}},onSuccess:(t,a)=>{e.invalidateQueries({queryKey:["agents"]}),e.removeQueries({queryKey:["agent",a]})}}),AgentsError:i instanceof Error?i.message:null}}},26126:(e,t,a)=>{"use strict";a.d(t,{E:()=>o});var s=a(95155);a(12115);var r=a(99708),l=a(74466),n=a(59434);let i=(0,l.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:a,asChild:l=!1,...o}=e,d=l?r.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,n.cn)(i({variant:a}),t),...o})}},35600:(e,t,a)=>{"use strict";a.d(t,{G:()=>g});var s=a(95155),r=a(12115),l=a(30285),n=a(54165),i=a(62523),o=a(51154),d=a(19420),c=a(25561),u=a(39365),m=a.n(u),h=a(56671);a(30133);var x=a(59071);function g(e){let{isOpen:t,onClose:a,agent:u}=e,[g,p]=(0,r.useState)({name:"",phoneNumber:""}),[f,b]=(0,r.useState)(!1),[v,j]=(0,r.useState)(null),A=async()=>{if(u){b(!0),j(null);try{let e=g.phoneNumber.replace(/\s+/g,""),t=e.startsWith("+")?e:"+".concat(e),s=!1;try{await (0,c.SQ)({contactName:g.name,phoneNumber:t,campaigns:[]})}catch(e){if(e.message.includes("Conflict")||e.message.includes("already exists"))s=!0;else throw e}let r=(0,x.s)(t)||"",l=[{Name:g.name,MobileNumber:t}];await (0,c.G6)(u.id,l,r),h.o.success(s?"Call initiated with existing contact":"Contact created and call initiated"),a()}catch(e){console.error("Call error:",e),e.message.includes("already exists")?j("Contact already exists with this name and phone number"):j(e instanceof Error?e.message:"Failed to initiate call"),h.o.error("Failed to process request")}finally{b(!1)}}};return(0,s.jsx)(n.lG,{open:t,onOpenChange:e=>{e||(p({name:"",phoneNumber:""}),j(null)),a()},children:(0,s.jsxs)(n.Cf,{className:"sm:max-w-[425px]",children:[(0,s.jsxs)(n.c7,{children:[(0,s.jsx)(n.L3,{children:"Start Phone Call"}),(0,s.jsxs)(n.rr,{children:["Enter your details to start a call with",(0,s.jsx)("span",{className:"font-bold ml-1",children:null==u?void 0:u.name})]})]}),(0,s.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"name",className:"text-sm font-medium",children:"Your Name"}),(0,s.jsx)(i.p,{id:"name",placeholder:"Enter your name",className:"mt-2",value:g.name,onChange:e=>p(t=>({...t,name:e.target.value}))})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"phone",className:"text-sm font-medium",children:"Phone Number"}),(0,s.jsx)(m(),{country:(0,x.u)(),value:g.phoneNumber,onChange:e=>p(t=>({...t,phoneNumber:"+".concat(e)})),containerClass:"mt-2",inputClass:"!w-full !h-10 !pl-[48px] !rounded-md !border !border-input !bg-background !px-3 !py-2 !text-sm !ring-offset-background file:!border-0 file:!bg-transparent file:!text-sm file:!font-medium placeholder:!text-muted-foreground focus-visible:!outline-none focus-visible:!ring-2 focus-visible:!ring-ring focus-visible:!ring-offset-2 disabled:!cursor-not-allowed disabled:!opacity-50",buttonClass:"!border-r-0 !bg-transparent !border !border-input",dropdownClass:"!bg-background !border !border-input",specialLabel:""})]}),v&&(0,s.jsx)("div",{className:"text-sm text-red-500 bg-red-50 dark:bg-red-900/20 p-3 rounded-md",children:v})]}),(0,s.jsxs)(n.Es,{children:[(0,s.jsx)(l.$,{variant:"outline",onClick:a,children:"Cancel"}),(0,s.jsx)(l.$,{onClick:A,disabled:f||!g.name||!g.phoneNumber,className:"bg-green-600 hover:bg-green-700 text-white",children:f?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Initiating..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Start Call"]})})]})]})})}},44838:(e,t,a)=>{"use strict";a.d(t,{I:()=>d,SQ:()=>o,_2:()=>c,lp:()=>u,mB:()=>m,rI:()=>n,ty:()=>i});var s=a(95155);a(12115);var r=a(48698),l=a(59434);function n(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"dropdown-menu",...t})}function i(e){let{...t}=e;return(0,s.jsx)(r.l9,{"data-slot":"dropdown-menu-trigger",...t})}function o(e){let{className:t,sideOffset:a=4,...n}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsx)(r.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] overflow-hidden rounded-md border p-1 shadow-md",t),...n})})}function d(e){let{...t}=e;return(0,s.jsx)(r.YJ,{"data-slot":"dropdown-menu-group",...t})}function c(e){let{className:t,inset:a,variant:n="default",...i}=e;return(0,s.jsx)(r.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":n,className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive-foreground data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/40 data-[variant=destructive]:focus:text-destructive-foreground data-[variant=destructive]:*:[svg]:!text-destructive-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i})}function u(e){let{className:t,inset:a,...n}=e;return(0,s.jsx)(r.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,l.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...n})}function m(e){let{className:t,...a}=e;return(0,s.jsx)(r.wv,{"data-slot":"dropdown-menu-separator",className:(0,l.cn)("bg-border -mx-1 my-1 h-px",t),...a})}},46102:(e,t,a)=>{"use strict";a.d(t,{Bc:()=>n,ZI:()=>d,k$:()=>o,m_:()=>i});var s=a(95155);a(12115);var r=a(89613),l=a(59434);function n(e){let{delayDuration:t=0,...a}=e;return(0,s.jsx)(r.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...a})}function i(e){let{...t}=e;return(0,s.jsx)(n,{children:(0,s.jsx)(r.bL,{"data-slot":"tooltip",...t})})}function o(e){let{...t}=e;return(0,s.jsx)(r.l9,{"data-slot":"tooltip-trigger",...t})}function d(e){let{className:t,sideOffset:a=0,children:n,...i}=e;return(0,s.jsx)(r.ZL,{children:(0,s.jsxs)(r.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,l.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit rounded-md px-3 py-1.5 text-xs text-balance",t),...i,children:[n,(0,s.jsx)(r.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},51905:(e,t,a)=>{Promise.resolve().then(a.bind(a,94495))},62829:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s={src:"/_next/static/media/Binghatti-Lisa.85c81ecb.jpeg",height:1586,width:1586,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/2wBDAQoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/wgARCAAIAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAX/xAAUAQEAAAAAAAAAAAAAAAAAAAAC/9oADAMBAAIQAxAAAACeA//EABsQAAEFAQEAAAAAAAAAAAAAAAECAwQREwAi/9oACAEBAAE/AG6e3khuoyZAbQNDWYR6SO//xAAVEQEBAAAAAAAAAAAAAAAAAAABAP/aAAgBAgEBPwAL/8QAFhEAAwAAAAAAAAAAAAAAAAAAAAFB/9oACAEDAQE/AHD/2Q==",blurWidth:8,blurHeight:8}},66681:(e,t,a)=>{"use strict";a.d(t,{A:()=>r});var s=a(12115);function r(){let[e,t]=(0,s.useState)(null),[a,r]=(0,s.useState)(!0),[l,n]=(0,s.useState)(null);(0,s.useEffect)(()=>{!async function(){try{let e=localStorage.getItem("access_token");if(!e)throw Error("No access token available");let a=await fetch("".concat("http://localhost:4000","/api/auth/me"),{headers:{Authorization:"Bearer ".concat(e),"Content-Type":"application/json"}});if(!a.ok)throw Error("Failed to fetch user profile: ".concat(a.status));let s=await a.json();t({email:s.email,fullName:s.fullName,role:s.role})}catch(e){n(e instanceof Error?e.message:"Failed to load user profile")}finally{r(!1)}}()},[]);let i=(null==e?void 0:e.role)||null;return{user:e,userRole:i,authIsLoading:a,authError:l}}},86902:(e,t,a)=>{"use strict";a.d(t,{M:()=>m});var s=a(95155),r=a(12115),l=a(54416),n=a(51154),i=a(90105),o=a(62829),d=a(66766),c=a(30285),u=a(62523);function m(e){let{agent:t,isOpen:a,onClose:m}=e,[h,x]=(0,r.useState)([]),[g,p]=(0,r.useState)(""),[f,b]=(0,r.useState)(!1),v=["I understand what you're saying. Let me help you with that.","That's interesting! Could you tell me more?","I'm processing your request. Here's what I think...","Based on what you've told me, I would suggest...","Let me check that for you quickly."],j=async e=>{if(!e.trim())return;let t={id:Date.now().toString(),content:e,sender:"user",timestamp:new Date};x(e=>[...e,t]),p(""),b(!0),setTimeout(()=>{let e=v[Math.floor(Math.random()*v.length)],t={id:(Date.now()+1).toString(),content:e,sender:"agent",timestamp:new Date};x(e=>[...e,t]),b(!1)},1e3)};return(0,r.useState)(()=>{a&&0===h.length&&x([{id:"initial",content:"Hello! My name is ".concat(null==t?void 0:t.name,". How can I assist you today?"),sender:"agent",timestamp:new Date}])}),(0,s.jsxs)("div",{className:"fixed right-0 top-19 h-[90vh] border-2 w-96 bg-white dark:bg-gray-800 shadow-xl transition-all duration-200 ".concat(a?"opacity-100 visible":"opacity-0 invisible"," z-50 rounded-lg"),children:[(0,s.jsxs)("div",{className:"border-b p-4 flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[(0,s.jsx)("div",{className:"relative h-10 w-10 rounded-full overflow-hidden",children:(null==t?void 0:t.avatar)?(0,s.jsx)("img",{src:t.avatar,alt:"".concat(t.name," avatar"),className:"h-full w-full object-cover"}):(0,s.jsx)(d.default,{src:o.A,alt:"".concat(null==t?void 0:t.name," avatar"),className:"object-cover",fill:!0})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"font-semibold",children:null==t?void 0:t.name}),(0,s.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:null==t?void 0:t.role})]})]}),(0,s.jsx)(c.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:m,children:(0,s.jsx)(l.A,{className:"h-4 w-4"})})]}),(0,s.jsxs)("div",{className:"h-[calc(100%-8rem)] overflow-y-auto p-4 space-y-4",children:[h.map(e=>(0,s.jsx)("div",{className:"flex ".concat("user"===e.sender?"justify-end":"justify-start"),children:(0,s.jsx)("div",{className:"max-w-[80%] rounded-lg p-3 ".concat("user"===e.sender?"bg-primary text-primary-foreground":"bg-gray-100 dark:bg-gray-700"),children:e.content})},e.id)),f&&(0,s.jsx)("div",{className:"flex justify-start",children:(0,s.jsx)("div",{className:"bg-gray-100 dark:bg-gray-700 rounded-lg p-3",children:(0,s.jsx)(n.A,{className:"h-4 w-4 animate-spin"})})})]}),(0,s.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-4 bg-white dark:bg-gray-800 border-t",children:(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),j(g)},className:"flex gap-2",children:[(0,s.jsx)(u.p,{value:g,onChange:e=>p(e.target.value),placeholder:"Type a message...",className:"flex-1"}),(0,s.jsx)(c.$,{type:"submit",size:"icon",children:(0,s.jsx)(i.A,{className:"h-4 w-4"})})]})})]})}},90010:(e,t,a)=>{"use strict";a.d(t,{$v:()=>x,EO:()=>c,Lt:()=>i,Rx:()=>g,Zr:()=>p,ck:()=>m,r7:()=>h,wd:()=>u});var s=a(95155);a(12115);var r=a(17649),l=a(59434),n=a(30285);function i(e){let{...t}=e;return(0,s.jsx)(r.bL,{"data-slot":"alert-dialog",...t})}function o(e){let{...t}=e;return(0,s.jsx)(r.ZL,{"data-slot":"alert-dialog-portal",...t})}function d(e){let{className:t,...a}=e;return(0,s.jsx)(r.hJ,{"data-slot":"alert-dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-650 bg-black/80",t),...a})}function c(e){let{className:t,...a}=e;return(0,s.jsxs)(o,{children:[(0,s.jsx)(d,{}),(0,s.jsx)(r.UC,{"data-slot":"alert-dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-650 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...a})]})}function u(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",t),...a})}function m(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"alert-dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function h(e){let{className:t,...a}=e;return(0,s.jsx)(r.hE,{"data-slot":"alert-dialog-title",className:(0,l.cn)("text-lg font-semibold",t),...a})}function x(e){let{className:t,...a}=e;return(0,s.jsx)(r.VY,{"data-slot":"alert-dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",t),...a})}function g(e){let{className:t,...a}=e;return(0,s.jsx)(r.rc,{className:(0,l.cn)((0,n.r)(),t),...a})}function p(e){let{className:t,...a}=e;return(0,s.jsx)(r.ZD,{className:(0,l.cn)((0,n.r)({variant:"outline"}),t),...a})}},94495:(e,t,a)=>{"use strict";a.d(t,{default:()=>M});var s=a(95155),r=a(12115),l=a(30285),n=a(62523),i=a(84616),o=a(47924),d=a(51154),c=a(89917),u=a(15273),m=a(81497),h=a(21714),x=a(62209),g=a(26126),p=a(2488),f=a(44838),b=a(44020),v=a(62525),j=a(62829),A=a(66766),w=a(35695),y=a(6874),N=a.n(y),k=a(90010),C=a(46102),E=a(66681),S=a(6108),I=a(35600),F=a(86902),D=a(54165),z=a(56671),Q=a(97207),L=a(91951),_=a(41185),B=a.n(_);function P(e){let{isOpen:t,onClose:a,agent:n}=e,[i,o]=(0,r.useState)(!1),[c,u]=(0,r.useState)(!1),[m,h]=(0,r.useState)(null),[x,g]=(0,r.useState)(!1),[p,f]=(0,r.useState)(""),[b,v]=(0,r.useState)(null);(0,r.useEffect)(()=>{if(t&&!b){let e=new(B())({apiKey:"9964f6f2-48ed-49b4-b3cd-24ad553e26ee",debug:!0,audioConfig:{sampleRate:16e3,channelCount:1},cors:{allowedOrigins:["http://localhost:3000"]}});e.on("call-start",()=>{console.log("Call started successfully"),o(!0),u(!1),z.o.success("Call started")}),e.on("call-end",()=>{o(!1),z.o.info("Call ended")}),e.on("message",e=>{f(e.content)}),e.on("speech-start",()=>{g(!0)}),e.on("speech-end",()=>{g(!1)}),e.on("error",e=>{console.error("VAPI error details:",e),h(e.message||"An unexpected error occurred"),u(!1),z.o.error("Call error: ".concat(e.message))}),v(e)}return()=>{if(b){try{b.stop()}catch(e){console.error("Error stopping call:",e)}v(null)}}},[t,b]);let j=async()=>{if(n&&b){if(i)b.stop(),o(!1);else{u(!0),h(null);try{var e;(await navigator.mediaDevices.getUserMedia({audio:!0})).getTracks().forEach(e=>e.stop()),await b.start({assistantId:n.id,audioConfig:{sampleRate:16e3,channelCount:1},transcriber:{language:(null===(e=n.transcriber)||void 0===e?void 0:e.language)||"en-US"}})}catch(e){console.error("Web call error details:",{message:e.message,stack:e.stack,name:e.name}),"NotAllowedError"===e.name?h("Microphone permission denied. Please enable microphone access."):navigator.onLine?h(e.message||"Failed to start web call. Please try again."):h("No internet connection. Please check your network."),z.o.error("Failed to start web call"),u(!1)}}}};return(0,s.jsx)(D.lG,{open:t,onOpenChange:e=>{e||(i&&(null==b||b.stop()),h(null),f("")),a()},children:(0,s.jsxs)(D.Cf,{className:"sm:max-w-[425px]",children:[(0,s.jsxs)(D.c7,{children:[(0,s.jsxs)(D.L3,{children:["Web Call with ",null==n?void 0:n.name]}),(0,s.jsx)(D.rr,{children:i?"Call in progress":"Start a web call with your assistant"})]}),(0,s.jsxs)("div",{className:"py-4",children:[!i&&(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-4",children:"Make sure your microphone is connected and you have granted browser permissions."}),i&&p&&(0,s.jsxs)("div",{className:"mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-md",children:[(0,s.jsx)("p",{className:"text-sm font-medium mb-1",children:"Last Response:"}),(0,s.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:p})]}),m&&(0,s.jsx)("div",{className:"mb-4 text-sm text-red-500 bg-red-50 dark:bg-red-900/20 p-3 rounded-md",children:m}),(0,s.jsxs)("div",{className:"mt-6 flex justify-end gap-3",children:[(0,s.jsx)(l.$,{variant:"outline",onClick:a,disabled:c,children:"Cancel"}),(0,s.jsx)(l.$,{onClick:j,disabled:c,className:"".concat(i?"bg-red-600 hover:bg-red-700":"bg-black hover:bg-gray-800 dark:bg-white dark:text-black"," text-white"),children:c?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Starting..."]}):i?(0,s.jsxs)(s.Fragment,{children:[x?(0,s.jsx)(Q.A,{className:"mr-2 h-4 w-4"}):(0,s.jsx)(L.A,{className:"mr-2 h-4 w-4"}),"End Call"]}):"Start Call"})]})]})]})})}function M(){let[e,t]=(0,r.useState)(null),[a,y]=(0,r.useState)(!1),[D,z]=(0,r.useState)(!1),[Q,L]=(0,r.useState)(!1),[_,B]=(0,r.useState)(!1),[M,U]=(0,r.useState)(null),[$,O]=(0,r.useState)(!1),[G,T]=(0,r.useState)(""),Z=(0,w.useRouter)(),{userRole:R,authIsLoading:q,authError:K}=(0,E.A)(),{agents:Y,agentsisLoading:X,AgentsError:H,deleteAgentMutation:V}=(0,S.a)(),W=async()=>{if(M)try{await V.mutateAsync(M.id),B(!1)}catch(e){console.error("Failed to delete agent:",e)}finally{U(null)}},J=e=>({"en-US":"English (US)","en-GB":"English (UK)",en:"English","fr-FR":"French",fr:"French","es-ES":"Spanish","de-DE":"German","it-IT":"Italian","ar-SA":"Arabic"})[e]||e,ee=(0,r.useMemo)(()=>{let e=Y.filter(e=>e.name.toLowerCase().includes(G.toLowerCase()));return"superadmin"===R?e:e.filter(e=>"active"===e.status)},[Y,G,R]);return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"AI Agents"}),(0,s.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Manage and interact with your assistants"})]}),"superadmin"===R&&(0,s.jsx)(N(),{href:"/agents/create",children:(0,s.jsxs)(l.$,{size:"lg",className:" text-white transition-all duration-200 hover:scale-107 dark:text-black",children:[(0,s.jsx)(i.A,{className:"h-5 w-5"}),"Create New Agent"]})})]}),(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(o.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5"}),(0,s.jsx)(n.p,{placeholder:"Search assistants...",className:"pl-10 bg-white dark:bg-gray-800",value:G,onChange:e=>T(e.target.value)})]})}),K&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md text-yellow-800",children:K}),H&&(0,s.jsx)("div",{className:"mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-md text-yellow-800",children:H}),q||X?(0,s.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)(d.A,{className:"h-12 w-12 animate-spin text-primary mb-4"}),(0,s.jsx)("p",{className:"text-lg font-medium",children:"Loading your assistants..."})]})}):(0,s.jsx)("div",{className:"grid gap-6 grid-cols-1 sm:grod-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 auto-rows-fr",children:ee.map((a,r)=>{var n;return(0,s.jsx)(p.default,{direction:"up",delay:.1,children:(0,s.jsxs)("div",{className:"text-card-foreground flex flex-col rounded-xl border-1  dark:border-gray-500 relative bg-white dark:bg-gray-800 hover:shadow-xl transition-shadow duration-200 overflow-hidden cursor-pointer h-full",onClick:e=>{e.target.closest(".clickPrevention")||Z.push("/agents/edit/".concat(a.id))},children:[(0,s.jsx)("div",{className:"absolute top-2 right-2 z-20 clickPrevention",onClick:e=>e.stopPropagation(),children:"superadmin"===R&&(0,s.jsxs)(f.rI,{children:[(0,s.jsx)(f.ty,{asChild:!0,children:(0,s.jsx)(l.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 rounded-full",children:(0,s.jsx)(b.A,{className:"h-4 w-4"})})}),(0,s.jsxs)(f.SQ,{align:"end",children:[(0,s.jsxs)(f._2,{className:"text-blue-500 focus:text-blue-500 cursor-pointer",onSelect:e=>{e.preventDefault(),Z.push("/agents/edit/".concat(a.id))},children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Edit"]}),(0,s.jsxs)(f._2,{className:"text-red-600 focus:text-red-600 cursor-pointer",onSelect:e=>{e.preventDefault(),U(a),B(!0)},children:[(0,s.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})}),(0,s.jsxs)("div",{className:"flex h-full",children:[(0,s.jsxs)("div",{className:"relative w-[120px] md:w-[120px] lg:w-[130px] xl:w-[140px]",children:[(0,s.jsx)("div",{className:"h-full w-full",onClick:()=>Z.push("/agents/edit/".concat(a.id)),children:a.avatar?(0,s.jsx)("img",{src:a.avatar,alt:"".concat(a.name," avatar"),className:"h-full w-full object-cover"}):(0,s.jsx)("div",{className:"h-full w-full bg-black",children:(0,s.jsx)(A.default,{src:j.A,alt:"".concat(a.name," avatar"),fill:!0,className:"object-cover"})})}),(0,s.jsx)("div",{className:"absolute bottom-2 right-2 h-2 sm:h-3 w-2 sm:w-3 rounded-full border-2 border-white dark:border-gray-800 \n             ".concat("active"===a.status?"bg-green-500":"bg-gray-400")})]}),(0,s.jsxs)("div",{className:"flex flex-col flex-1 p-3 md:p-2 lg:p-3 xl:p-4",children:[(0,s.jsxs)("div",{className:"mb-2",onClick:()=>Z.push("/agents/edit/".concat(a.id)),children:[(0,s.jsx)("h3",{className:"font-semibold text-sm md:text-base lg:text-sm xl:text-base mb-1 truncate",children:a.name}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("p",{className:"text-xs md:text-sm lg:text-xs xl:text-sm text-[#702760] dark:text-white font-medium truncate",children:(null==a?void 0:a.role)||"Assistant"}),(0,s.jsx)("div",{className:"flex items-center gap-1.5",children:(null===(n=a.transcriber)||void 0===n?void 0:n.language)&&(0,s.jsx)(g.E,{variant:"outline",className:"text-[10px] md:text-xs lg:text-[10px] xl:text-xs bg-purple-50 dark:bg-purple-900/20 text-gray-700 dark:text-white border-purple-200 dark:border-purple-800",children:J(a.transcriber.language)})})]})]}),(0,s.jsx)("div",{className:"flex items-center gap-1.5 md:gap-1 lg:gap-1.5 xl:gap-2 mt-auto",onClick:e=>e.stopPropagation(),children:(0,s.jsxs)(C.Bc,{children:[(0,s.jsxs)(C.m_,{children:[(0,s.jsx)(C.k$,{asChild:!0,children:(0,s.jsx)(l.$,{variant:"ghost",size:"sm",className:"h-7 w-7 md:h-6 md:w-6 lg:h-7 lg:w-7 xl:h-8 xl:w-8 p-0 rounded-full hover:bg-emerald-50 dark:hover:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-700 transition-all duration-200 hover:scale-115",children:(0,s.jsx)(u.A,{className:"h-3.5 w-3.5 md:h-3 md:w-3 lg:h-3.5 lg:w-3.5 xl:h-4 xl:w-4 text-emerald-500 dark:text-emerald-400"})})}),(0,s.jsx)(C.ZI,{className:"text-xs sm:text-sm border-gray-200 dark:border-gray-700",children:(0,s.jsx)("p",{children:"Test voice"})})]}),(0,s.jsxs)(C.m_,{children:[(0,s.jsx)(C.k$,{asChild:!0,children:(0,s.jsx)(l.$,{variant:"ghost",size:"sm",className:"h-7 w-7 md:h-6 md:w-6 lg:h-7 lg:w-7 xl:h-8 xl:w-8 p-0 rounded-full hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-blue-200 dark:border-gray-700 transition-all duration-200 hover:scale-115",onClick:s=>{D&&(null==e?void 0:e.id)===a.id?(z(!1),t(null)):(t(a),z(!0))},children:(0,s.jsx)(m.A,{className:"h-3.5 w-3.5 md:h-3 md:w-3 lg:h-3.5 lg:w-3.5 xl:h-4 xl:w-4 text-blue-500 dark:text-blue-400"})})}),(0,s.jsx)(C.ZI,{className:"text-xs sm:text-sm border-gray-200 dark:border-gray-700",children:(0,s.jsx)("p",{children:"Chat with agent"})})]}),(0,s.jsxs)(C.m_,{children:[(0,s.jsx)(C.k$,{asChild:!0,children:(0,s.jsx)(l.$,{variant:"ghost",size:"sm",className:"h-7 w-7 md:h-6 md:w-6 lg:h-7 lg:w-7 xl:h-8 xl:w-8 p-0 rounded-full hover:bg-indigo-50 dark:hover:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-700 transition-all duration-200 hover:scale-115",onClick:e=>{t(a),L(!0)},children:(0,s.jsx)(h.A,{className:"h-3.5 w-3.5 md:h-3 md:w-3 lg:h-3.5 lg:w-3.5 xl:h-4 xl:w-4 text-indigo-500 dark:text-indigo-400"})})}),(0,s.jsx)(C.ZI,{className:"border-2 border-gray-200 dark:border-gray-700",children:(0,s.jsx)("p",{children:"Start web call"})})]}),(0,s.jsxs)(C.m_,{children:[(0,s.jsx)(C.k$,{asChild:!0,children:(0,s.jsx)(l.$,{variant:"ghost",size:"sm",className:"h-7 w-7 md:h-6 md:w-6 lg:h-7 lg:w-7 xl:h-8 xl:w-8 p-0 rounded-full hover:bg-purple-50 dark:hover:bg-purple-900/20 border border-purple-200 dark:border-gray-700 transition-all duration-200 hover:scale-115",onClick:e=>{t(a),y(!0)},children:(0,s.jsx)(x.A,{className:"h-3.5 w-3.5 md:h-3 md:w-3 lg:h-3.5 lg:w-3.5 xl:h-4 xl:w-4 text-purple-500 dark:text-purple-400"})})}),(0,s.jsx)(C.ZI,{className:"text-xs sm:text-sm border-2 border-gray-200 dark:border-gray-700",children:(0,s.jsx)("p",{children:"Start phone call"})})]})]})})]})]})]})},a.id)})}),(0,s.jsx)(k.Lt,{open:_,onOpenChange:B,children:(0,s.jsxs)(k.EO,{children:[(0,s.jsxs)(k.wd,{children:[(0,s.jsx)(k.r7,{children:"Are you sure?"}),(0,s.jsxs)(k.$v,{children:['This will permanently delete the agent "',null==M?void 0:M.name,'". This action cannot be undone.']})]}),(0,s.jsxs)(k.ck,{children:[(0,s.jsx)(k.Zr,{disabled:$,children:"Cancel"}),(0,s.jsx)(k.Rx,{onClick:W,disabled:$,className:"bg-red-600 hover:bg-red-700 text-white",children:$?"Deleting...":"Delete"})]})]})}),(0,s.jsx)(I.G,{agent:e,isOpen:a,onClose:()=>y(!1)}),(0,s.jsx)(F.M,{agent:e,isOpen:D,onClose:()=>z(!1)}),(0,s.jsx)(P,{agent:e,isOpen:Q,onClose:()=>L(!1)})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[7540,9424,4201,4341,6403,1071,6671,226,6766,6874,424,3860,9735,1504,9621,8441,1684,7358],()=>t(51905)),_N_E=e.O()}]);