"use strict";exports.id=1816,exports.ids=[1816],exports.modules={283:(e,a,s)=>{s.d(a,{A:()=>d});var l=s(60687),r=s(80013),i=s(15079),t=s(18116),n=s(44493),o=s(43210);function d({agent:e,setAgent:a}){let[s,d]=(0,o.useState)({provider:e?.transcriber?.provider||"",language:e?.transcriber?.language||"",model:e?.transcriber?.model||"",confidenceThreshold:e?.transcriber?.confidenceThreshold||.4}),[c,h]=(0,o.useState)({provider:e?.voice?.provider||"11labs",model:e?.voice?.model||"eleven_turbo_v2_5",voiceId:e?.voice?.voiceId||"",stability:e?.voice?.stability||.5}),m=(l,r)=>{let i={...s,[l]:r};if("provider"===l)switch(r){case"deepgram":i.model="nova-3";break;case"elevenlabs":i.model="scribe";break;case"gladia":i.model="fast";break;case"google":i.model="gemini-2.0-flash";break;case"openai":i.model="gpt-4o-transcribe";break;case"speechmatics":i.model="default";break;case"talkscriber":i.model="whisper";break;case"assemblyai":i.model="none";break;default:i.model=""}d(i),a({...e,transcriber:{...e.transcriber,provider:i.provider,model:i.model,language:i.language,confidenceThreshold:i.confidenceThreshold}})},u=(s,l)=>{let r={...c,[s]:l};if("provider"===s)switch(l){case"11labs":r.model="eleven_turbo_v2_5",r.voiceId="N2lVS1w4EtoT3dr4eOWO";break;case"cartesia":r.model="sonic_2",r.voiceId="3b554273-4299-48b9-9aaf-eefd438e3941";break;case"rime":r.model="mist",r.voiceId="gerald";break;case"playht":r.model="playht2_turbo",r.voiceId="melissa";break;case"openai":r.model="tts-1",r.voiceId="alloy";break;case"deepgram":r.model="aura",r.voiceId="asteria";break;default:r.model=""}h(r),a({...e,voice:{...e.voice,[s]:l}})};return(0,l.jsxs)("div",{className:"space-y-6 p-6",children:[(0,l.jsx)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4",children:(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"Voice Configuration"})}),(0,l.jsxs)("div",{className:"space-y-4 mt-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"Transcriber Settings"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(r.J,{htmlFor:"transcriber-provider",children:"Provider"}),(0,l.jsxs)(i.l6,{value:s?.provider||"",onValueChange:e=>m("provider",e),children:[(0,l.jsx)(i.bq,{id:"transcriber-provider",className:"w-full",children:(0,l.jsx)(i.yv,{placeholder:"Select provider"})}),(0,l.jsxs)(i.gC,{children:[(0,l.jsx)(i.eb,{value:"assemblyai",children:"Assembly AI"}),(0,l.jsx)(i.eb,{value:"deepgram",children:"Deepgram"}),(0,l.jsx)(i.eb,{value:"elevenlabs",children:"Elevenlabs"}),(0,l.jsx)(i.eb,{value:"gladia",children:"Gladia"}),(0,l.jsx)(i.eb,{value:"google",children:"Google"}),(0,l.jsx)(i.eb,{value:"openai",children:"OpenAI"}),(0,l.jsx)(i.eb,{value:"speechmatics",children:"Speechmatics"}),(0,l.jsx)(i.eb,{value:"talkscriber",children:"Talkscriber"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(r.J,{htmlFor:"transcriber-language",children:"Language"}),(0,l.jsxs)(i.l6,{value:s?.language||"en",onValueChange:e=>m("language",e),children:[(0,l.jsx)(i.bq,{id:"transcriber-language",className:"w-full",children:(0,l.jsx)(i.yv,{placeholder:"Select language"})}),(0,l.jsxs)(i.gC,{children:[(0,l.jsx)(i.eb,{value:"en",children:"English"}),(0,l.jsx)(i.eb,{value:"en-US",children:"English (US)"}),(0,l.jsx)(i.eb,{value:"multi",children:"Multi-language"})]})]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(r.J,{htmlFor:"transcriber-model",children:"Model"}),(0,l.jsxs)(i.l6,{value:s?.model||"none",onValueChange:e=>m("model",e),disabled:"assemblyai"===s.provider,children:[(0,l.jsx)(i.bq,{id:"transcriber-model",className:"w-full",children:(0,l.jsx)(i.yv,{placeholder:"Select model"})}),(0,l.jsxs)(i.gC,{children:["deepgram"===s.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.eb,{value:"nova-3",children:"Nova 3"}),(0,l.jsx)(i.eb,{value:"nova-3-general",children:"Nova 3 General"}),(0,l.jsx)(i.eb,{value:"nova-3-medical",children:"Nova 3 Medical"}),(0,l.jsx)(i.eb,{value:"nova-2",children:"Nova 2"}),(0,l.jsx)(i.eb,{value:"nova-2-general",children:"Nova 2 General"}),(0,l.jsx)(i.eb,{value:"nova-2-meeting",children:"Nova 2 Meeting"}),(0,l.jsx)(i.eb,{value:"nova-2-phonecall",children:"Nova 2 Phonecall"}),(0,l.jsx)(i.eb,{value:"nova-2-finance",children:"Nova 2 Finance"}),(0,l.jsx)(i.eb,{value:"nova-2-conversational-ai",children:"Nova 2 Conversational AI"}),(0,l.jsx)(i.eb,{value:"nova-2-voicemail",children:"Nova 2 Voicemail"}),(0,l.jsx)(i.eb,{value:"nova-2-video",children:"Nova 2 Video"}),(0,l.jsx)(i.eb,{value:"nova-2-medical",children:"Nova 2 Medical"}),(0,l.jsx)(i.eb,{value:"nova-2-drive-thru",children:"Nova 2 Drive Thru"}),(0,l.jsx)(i.eb,{value:"nova-2-automotive",children:"Nova 2 Automotive"})]}),"elevenlabs"===s.provider&&(0,l.jsx)(i.eb,{value:"scribe",children:"Scribe"}),"gladia"===s.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.eb,{value:"fast",children:"Fast"}),(0,l.jsx)(i.eb,{value:"accurate",children:"Accurate"})]}),"google"===s.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.eb,{value:"gemini-2.0-flash",children:"Gemini 2.0 Flash"}),(0,l.jsx)(i.eb,{value:"gemini-2.0-flash-lite",children:"Gemini 2.0 Flash Lite"}),(0,l.jsx)(i.eb,{value:"gemini-1.5-flash-lite",children:"Gemini 1.5 Flash Lite"}),(0,l.jsx)(i.eb,{value:"gemini-1.5-pro",children:"Gemini 1.5 Pro"})]}),"openai"===s.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.eb,{value:"gpt-4o-transcribe",children:"GPT-4o Transcribe"}),(0,l.jsx)(i.eb,{value:"gpt-4o-mini-transcribe",children:"GPT-4o Mini Transcribe"})]}),"speechmatics"===s.provider&&(0,l.jsx)(i.eb,{value:"default",children:"Default"}),"talkscriber"===s.provider&&(0,l.jsx)(i.eb,{value:"whisper",children:"Whisper"}),"assemblyai"===s.provider&&(0,l.jsx)(i.eb,{value:"none",children:"No models available"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)(r.J,{htmlFor:"confidence-threshold",children:"Confidence Threshold"}),(0,l.jsx)("span",{className:"text-sm text-muted-foreground mt-3",children:s.confidenceThreshold.toFixed(1)})]}),(0,l.jsx)(t.A,{id:"confidence-threshold",value:[s.confidenceThreshold],min:0,max:1,step:.1,className:"w-full",onValueChange:e=>m("confidenceThreshold",e[0])})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"Voice Configuration"}),(0,l.jsx)(n.Zp,{className:"p-4",children:(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(r.J,{htmlFor:"voice-provider",children:"Voice Provider"}),(0,l.jsxs)(i.l6,{value:c?.provider||"",onValueChange:e=>u("provider",e),children:[(0,l.jsx)(i.bq,{id:"voice-provider",className:"w-full",children:(0,l.jsx)(i.yv,{placeholder:"Select provider"})}),(0,l.jsxs)(i.gC,{children:[(0,l.jsx)(i.eb,{value:"11labs",children:"ElevenLabs"}),(0,l.jsx)(i.eb,{value:"cartesia",children:"Cartesia"}),(0,l.jsx)(i.eb,{value:"rime",children:"Rime AI"}),(0,l.jsx)(i.eb,{value:"playht",children:"PlayHT"}),(0,l.jsx)(i.eb,{value:"openai",children:"OpenAI"}),(0,l.jsx)(i.eb,{value:"deepgram",children:"Deepgram"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(r.J,{htmlFor:"voice-model",children:"Voice Model"}),(0,l.jsxs)(i.l6,{value:c?.model||"",onValueChange:e=>u("model",e),children:[(0,l.jsx)(i.bq,{id:"voice-model",className:"w-full",children:(0,l.jsx)(i.yv,{placeholder:"Select model"})}),(0,l.jsxs)(i.gC,{children:["11labs"===c.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.eb,{value:"eleven_turbo_v2_5",children:"Eleven Turbo v2.5"}),(0,l.jsx)(i.eb,{value:"eleven_flash_v2_5",children:"Eleven Flash 2.5"}),(0,l.jsx)(i.eb,{value:"eleven_flash_v2",children:"Eleven Flash V2"}),(0,l.jsx)(i.eb,{value:"eleven_multilingual_v2",children:"Eleven Multilingual v2"}),(0,l.jsx)(i.eb,{value:"eleven_english_v1",children:"Eleven English v1"})]}),"cartesia"===c.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.eb,{value:"sonic_2",children:"Sonic 2"}),(0,l.jsx)(i.eb,{value:"sonic_english",children:"Sonic English"}),(0,l.jsx)(i.eb,{value:"sonic_multilingual",children:"Sonic Multilingual"}),(0,l.jsx)(i.eb,{value:"sonic_preview",children:"Sonic Preview"}),(0,l.jsx)(i.eb,{value:"sonic",children:"Sonic"})]}),"rime"===c.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.eb,{value:"mist",children:"Mist"}),(0,l.jsx)(i.eb,{value:"mistv2",children:"Mist v2"})]}),"playht"===c.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.eb,{value:"playht2_turbo",children:"PlayHT 2.0 Turbo"}),(0,l.jsx)(i.eb,{value:"playht2",children:"PlayHT 2.0"}),(0,l.jsx)(i.eb,{value:"play3_mini",children:"Play 3.0 Mini"}),(0,l.jsx)(i.eb,{value:"playdialog",children:"PlayDialog"})]}),"openai"===c.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.eb,{value:"tts-1",children:"TTS-1"}),(0,l.jsx)(i.eb,{value:"tts-1-hd",children:"TTS-1-HD"}),(0,l.jsx)(i.eb,{value:"gpt-4o-mini-tts",children:"GPT-4o Mini TTS"})]}),"deepgram"===c.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.eb,{value:"aura",children:"Aura"}),(0,l.jsx)(i.eb,{value:"aura_2",children:"Aura 2"})]})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(r.J,{htmlFor:"voice-id",children:"Voice ID"}),(0,l.jsxs)(i.l6,{value:c?.voiceId||"",onValueChange:e=>u("voiceId",e),children:[(0,l.jsx)(i.bq,{id:"voice-id",className:"w-full",children:(0,l.jsx)(i.yv,{placeholder:"Select voice"})}),(0,l.jsxs)(i.gC,{children:["11labs"===c.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.eb,{value:"N2lVS1w4EtoT3dr4eOWO",children:"Callum"}),(0,l.jsx)(i.eb,{value:"piTKgcLEGmPE4e6mEKli",children:"Nicole"})]}),"cartesia"===c.provider&&(0,l.jsx)(l.Fragment,{children:(0,l.jsx)(i.eb,{value:"3b554273-4299-48b9-9aaf-eefd438e3941",children:"Indian Lady"})}),"rime"===c.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.eb,{value:"gerald",children:"Gerald"}),(0,l.jsx)(i.eb,{value:"gultch",children:"Gultch"}),(0,l.jsx)(i.eb,{value:"rob",children:"Rob"})]}),"playht"===c.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.eb,{value:"melissa",children:"Melissa"}),(0,l.jsx)(i.eb,{value:"davis",children:"Davis"})]}),"openai"===c.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.eb,{value:"alloy",children:"Alloy"}),(0,l.jsx)(i.eb,{value:"shimmer",children:"Shimmer"})]}),"deepgram"===c.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(i.eb,{value:"asteria",children:"Asteria"}),(0,l.jsx)(i.eb,{value:"perseus",children:"Perseus"})]})]})]})]}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)(r.J,{htmlFor:"stability",children:["Stability: ",c.stability]}),(0,l.jsx)(t.A,{id:"stability",min:0,max:1,step:.1,value:[c.stability],onValueChange:e=>u("stability",e[0])})]})]})})]})]})]})}s(44140)},3710:(e,a,s)=>{s.d(a,{M:()=>m});var l=s(60687),r=s(43210),i=s(11860),t=s(41862),n=s(27900),o=s(76104),d=s(30474),c=s(29523),h=s(89667);function m({agent:e,isOpen:a,onClose:s}){let[m,u]=(0,r.useState)([]),[x,g]=(0,r.useState)(""),[v,p]=(0,r.useState)(!1),b=["I understand what you're saying. Let me help you with that.","That's interesting! Could you tell me more?","I'm processing your request. Here's what I think...","Based on what you've told me, I would suggest...","Let me check that for you quickly."],j=async e=>{if(!e.trim())return;let a={id:Date.now().toString(),content:e,sender:"user",timestamp:new Date};u(e=>[...e,a]),g(""),p(!0),setTimeout(()=>{let e=b[Math.floor(Math.random()*b.length)],a={id:(Date.now()+1).toString(),content:e,sender:"agent",timestamp:new Date};u(e=>[...e,a]),p(!1)},1e3)};return(0,r.useState)(()=>{a&&0===m.length&&u([{id:"initial",content:`Hello! My name is ${e?.name}. How can I assist you today?`,sender:"agent",timestamp:new Date}])}),(0,l.jsxs)("div",{className:`fixed right-0 top-19 h-[90vh] border-2 w-96 bg-white dark:bg-gray-800 shadow-xl transition-all duration-200 ${a?"opacity-100 visible":"opacity-0 invisible"} z-50 rounded-lg`,children:[(0,l.jsxs)("div",{className:"border-b p-4 flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[(0,l.jsx)("div",{className:"relative h-10 w-10 rounded-full overflow-hidden",children:e?.avatar?(0,l.jsx)("img",{src:e.avatar,alt:`${e.name} avatar`,className:"h-full w-full object-cover"}):(0,l.jsx)(d.default,{src:o.A,alt:`${e?.name} avatar`,className:"object-cover",fill:!0})}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-semibold",children:e?.name}),(0,l.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:e?.role})]})]}),(0,l.jsx)(c.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0",onClick:s,children:(0,l.jsx)(i.A,{className:"h-4 w-4"})})]}),(0,l.jsxs)("div",{className:"h-[calc(100%-8rem)] overflow-y-auto p-4 space-y-4",children:[m.map(e=>(0,l.jsx)("div",{className:`flex ${"user"===e.sender?"justify-end":"justify-start"}`,children:(0,l.jsx)("div",{className:`max-w-[80%] rounded-lg p-3 ${"user"===e.sender?"bg-primary text-primary-foreground":"bg-gray-100 dark:bg-gray-700"}`,children:e.content})},e.id)),v&&(0,l.jsx)("div",{className:"flex justify-start",children:(0,l.jsx)("div",{className:"bg-gray-100 dark:bg-gray-700 rounded-lg p-3",children:(0,l.jsx)(t.A,{className:"h-4 w-4 animate-spin"})})})]}),(0,l.jsx)("div",{className:"absolute bottom-0 left-0 right-0 p-4 bg-white dark:bg-gray-800 border-t",children:(0,l.jsxs)("form",{onSubmit:e=>{e.preventDefault(),j(x)},className:"flex gap-2",children:[(0,l.jsx)(h.p,{value:x,onChange:e=>g(e.target.value),placeholder:"Type a message...",className:"flex-1"}),(0,l.jsx)(c.$,{type:"submit",size:"icon",children:(0,l.jsx)(n.A,{className:"h-4 w-4"})})]})})]})}},15079:(e,a,s)=>{s.d(a,{bq:()=>h,eb:()=>u,gC:()=>m,l6:()=>d,yv:()=>c});var l=s(60687);s(43210);var r=s(22670),i=s(78272),t=s(13964),n=s(3589),o=s(4780);function d({...e}){return(0,l.jsx)(r.bL,{"data-slot":"select",...e})}function c({...e}){return(0,l.jsx)(r.WT,{"data-slot":"select-value",...e})}function h({className:e,children:a,...s}){return(0,l.jsxs)(r.l9,{"data-slot":"select-trigger",className:(0,o.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex h-9 w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...s,children:[a,(0,l.jsx)(r.In,{asChild:!0,children:(0,l.jsx)(i.A,{className:"size-4 opacity-50"})})]})}function m({className:e,children:a,position:s="popper",...i}){return(0,l.jsx)(r.ZL,{children:(0,l.jsxs)(r.UC,{"data-slot":"select-content",className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border shadow-md","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...i,children:[(0,l.jsx)(x,{}),(0,l.jsx)(r.LM,{className:(0,o.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,l.jsx)(g,{})]})})}function u({className:e,children:a,...s}){return(0,l.jsxs)(r.q7,{"data-slot":"select-item",className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",e),...s,children:[(0,l.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,l.jsx)(r.VF,{children:(0,l.jsx)(t.A,{className:"size-4"})})}),(0,l.jsx)(r.p4,{children:a})]})}function x({className:e,...a}){return(0,l.jsx)(r.PP,{"data-slot":"select-scroll-up-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,l.jsx)(n.A,{className:"size-4"})})}function g({className:e,...a}){return(0,l.jsx)(r.wn,{"data-slot":"select-scroll-down-button",className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,l.jsx)(i.A,{className:"size-4"})})}},18116:(e,a,s)=>{s.d(a,{A:()=>n});var l=s(60687),r=s(43210),i=s(24851),t=s(4780);function n({className:e,defaultValue:a,value:s,min:n=0,max:o=100,...d}){let c=r.useMemo(()=>Array.isArray(s)?s:Array.isArray(a)?a:[n,o],[s,a,n,o]);return(0,l.jsxs)(i.bL,{"data-slot":"slider",defaultValue:a,value:s,min:n,max:o,className:(0,t.cn)("relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",e),...d,children:[(0,l.jsx)(i.CC,{"data-slot":"slider-track",className:(0,t.cn)("bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),children:(0,l.jsx)(i.Q6,{"data-slot":"slider-range",className:(0,t.cn)("bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full")})}),Array.from({length:c.length},(e,a)=>(0,l.jsx)(i.zi,{"data-slot":"slider-thumb",className:"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"},a))]})}},21696:(e,a,s)=>{s.d(a,{f:()=>n});var l=s(8693),r=s(51423),i=s(54050);async function t(e){let a=localStorage.getItem("access_token"),[s,l]=await Promise.all([fetch(`http://localhost:4000/api/agents/${e}`,{headers:{Authorization:`Bearer ${a}`}}),fetch("http://localhost:4000/api/phone-numbers",{headers:{Authorization:`Bearer ${a}`}})]);if(!s.ok||!l.ok)throw Error("Failed to fetch agent data");let[r,i]=await Promise.all([s.json(),l.json()]);return{agent:r,phoneNumbers:i}}function n(e){let a=(0,l.jE)(),{data:s}=(0,r.I)({queryKey:["phone-numbers"],queryFn:async()=>{let e=localStorage.getItem("access_token"),a=await fetch("http://localhost:4000/api/phone-numbers",{headers:{Authorization:`Bearer ${e}`}});if(!a.ok)throw Error("Failed to fetch phone numbers");return a.json()},staleTime:6e5,gcTime:9e5}),{data:n,isLoading:o,error:d}=(0,r.I)({queryKey:["agent",e],queryFn:()=>t(e),enabled:!!e,staleTime:6e5,gcTime:9e5}),c=(0,i.n)({mutationFn:async e=>{let a=localStorage.getItem("access_token");if(!a)throw Error("Authentication required");let s=await fetch(`http://localhost:4000/api/agents/${e.id}`,{method:"PATCH",headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error(`Failed to update agent: ${s.status}`);return s.json()},onMutate:async s=>{await a.cancelQueries({queryKey:["agent",e]}),await a.cancelQueries({queryKey:["agents"]});let l=a.getQueryData(["agent",e]),r=a.getQueryData(["agents"]);return a.setQueryData(["agent",e],e=>({...e??{phoneNumbers:[]},agent:s})),a.setQueryData(["agents"],(e=[])=>e.map(e=>e.id===s.id?s:e)),{previousAgent:l,previousAgents:r}},onSettled:()=>{a.invalidateQueries({queryKey:["agent",e]}),a.invalidateQueries({queryKey:["agents"]})}}),h=(0,i.n)({mutationFn:async e=>{let a=localStorage.getItem("access_token");if(!a)throw Error("Authentication required");let s=await fetch("http://localhost:4000/api/agents",{method:"POST",headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok)throw Error(`Failed to create agent: ${s.status}`);return s.json()},onSuccess:()=>{a.invalidateQueries({queryKey:["agents"]})}}),m=(0,i.n)({mutationFn:async e=>{let a=localStorage.getItem("access_token");if(!a)throw Error("Authentication required");let s=await fetch(`http://localhost:4000/api/agents/${e}`,{method:"DELETE",headers:{Authorization:`Bearer ${a}`}});if(!s.ok)throw Error(`Failed to delete agent: ${s.status}`)},onSuccess:(e,s)=>{a.removeQueries({queryKey:["agent",s]}),a.setQueryData(["agents"],(e=[])=>e.filter(e=>e.id!==s))}});return{agent:n?.agent,setAgent:e=>{e&&c.mutate(e)},phoneNumbers:e?n?.phoneNumbers:s??[],agentIsLoading:o,agentError:d instanceof Error?d.message:null,createAgentMutation:h,updateAgentMutation:c,deleteAgentMutation:m}}},22758:(e,a,s)=>{s.d(a,{A:()=>h});var l=s(60687),r=s(27900),i=s(41550),t=s(53061),n=s(15880),o=s(10022),d=s(40228),c=s(43210);function h({agent:e,setAgent:a,phoneNumbers:s}){let[h,m]=(0,c.useState)(null),[u,x]=(0,c.useState)("config"),[g,v]=(0,c.useState)({sms:{type:"sms",icon:(0,l.jsx)(r.A,{className:"h-4 w-4"}),label:"Send SMS",description:"Send SMS messages to customers",color:"bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300",enabled:!0},email:{type:"email",icon:(0,l.jsx)(i.A,{className:"h-4 w-4"}),label:"Send Email",description:"Send email communications",color:"bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300",enabled:!0},transfer:{type:"transfer",icon:(0,l.jsx)(t.A,{className:"h-4 w-4"}),label:"Transfer Call",description:"Transfer calls to other agents",color:"bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300",enabled:!0},end:{type:"end",icon:(0,l.jsx)(n.A,{className:"h-4 w-4"}),label:"End Call",description:"End the current call",color:"bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300",enabled:!0},extract:{type:"extract",icon:(0,l.jsx)(o.A,{className:"h-4 w-4"}),label:"Extract Info",description:"Extract information from conversations",color:"bg-amber-100 text-amber-700 dark:bg-amber-900 dark:text-amber-300",enabled:!0},calendar:{type:"calendar",icon:(0,l.jsx)(d.A,{className:"h-4 w-4"}),label:"Calendar",description:"Schedule appointments and meetings",color:"bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300",enabled:!0}}),[p,b]=(0,c.useState)(""),[j,f]=(0,c.useState)("both"),[y,N]=(0,c.useState)("end"),[A,w]=(0,c.useState)(""),[k,C]=(0,c.useState)(""),[S,F]=(0,c.useState)("followup");return(0,l.jsx)("div",{className:"space-y-6 p-6",children:(0,l.jsx)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4",children:(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"Actions Configuration"})})})}},34688:(e,a,s)=>{s.d(a,{A:()=>f});var l=s(60687),r=s(89667),i=s(80013),t=s(15079),n=s(29523),o=s(43210),d=s(16023),c=s(91391),h=s(58887),m=s(38894),u=s(21235),x=s(35169);s(44140);var g=s(63503),v=s(76242),p=s(95534),b=s(3710);let j="http://localhost:4000";function f({agent:e,setAgent:a,phoneNumbers:s,isCreateMode:f}){let[y,N]=(0,o.useState)(!1),[A,w]=(0,o.useState)(!1),[k,C]=(0,o.useState)(null),[S,F]=(0,o.useState)({unit:"%",width:100,height:100,x:0,y:0}),[E,T]=(0,o.useState)(!1),[I,P]=(0,o.useState)(!1),[M,G]=(0,o.useState)(null),[$,_]=(0,o.useState)(null),z=(0,o.useRef)(null),D=(0,o.useRef)(null),L=(e,a)=>{let s=document.createElement("canvas"),l=e.naturalWidth/e.width,r=e.naturalHeight/e.height;s.width=a.width,s.height=a.height;let i=s.getContext("2d");if(!i)throw Error("No 2d context");return i.fillStyle="#FFFFFF",i.fillRect(0,0,s.width,s.height),i.drawImage(e,a.x*l,a.y*r,a.width*l,a.height*r,0,0,a.width,a.height),new Promise(e=>{s.toBlob(a=>{if(!a)throw Error("Canvas is empty");e(a)},"image/jpeg",.95)})},B=e=>{let a=e.name.split("."),s=a.length>1?a.pop():"",l=a.join("."),r=Math.floor(9e3*Math.random()+1e3);return new File([e],`${l}_${r}.${s}`,{type:e.type})},q=async()=>{if(z.current&&$)try{w(!1),N(!0);let s=M||{x:S.x,y:S.y,width:"number"==typeof S.width?S.width:0,height:"number"==typeof S.height?S.height:0,unit:"px"},l=await L(z.current,s),r=B(new File([l],$.name,{type:"image/jpeg"})),i=localStorage.getItem("access_token");if(!i){console.error("No access token available");return}let t=new FormData;t.append("avatar",r);let n=await fetch(`${j}/api/agents/${e.id}/avatar`,{method:"POST",headers:{Authorization:`Bearer ${i}`},body:t});if(!n.ok)throw Error(`Failed to upload avatar: ${n.status}`);let o=await n.json();a({...e,avatar:`${j}${o.avatarPath}`})}catch(e){console.error("Error uploading avatar:",e)}finally{N(!1),_(null),C(null),D.current&&(D.current.value="")}},Q=e=>{if(e.width!==e.height){let a=Math.min(e.width,e.height);return{...e,width:a,height:a}}return{...e}};return(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)("div",{className:"space-y-8 p-4 sm:p-6",children:[(0,l.jsxs)("div",{className:"flex flex-col lg:flex-row gap-6 lg:gap-8",children:[(0,l.jsxs)("div",{className:"w-full lg:w-1/3 xl:w-1/4 flex flex-col items-center",children:[(0,l.jsxs)("div",{className:"relative cursor-pointer group w-full max-w-[200px]",onClick:()=>{D.current&&(D.current.value=""),D.current?.click()},children:[(0,l.jsxs)("div",{className:"aspect-square rounded-lg bg-black p-1 overflow-hidden",children:[e?.avatar?(0,l.jsx)("img",{src:e.avatar,alt:`${e.name} avatar`,className:"h-full w-full object-cover"}):(0,l.jsx)("div",{className:"h-full w-full bg-gray-200 flex items-center justify-center text-5xl",children:e?.name?e.name.charAt(0).toUpperCase():"A"}),y&&(0,l.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-70 rounded-lg flex items-center justify-center",children:(0,l.jsx)("div",{className:"animate-spin h-8 w-8 border-4 border-t-transparent border-white rounded-full"})})]}),(0,l.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity",children:(0,l.jsx)(d.A,{className:"h-8 w-8 text-white"})}),(0,l.jsx)("input",{type:"file",ref:D,className:"hidden",accept:"image/jpeg,image/png,image/gif,image/webp",onChange:e=>{let a=e.target.files?.[0];if(!a)return;if(a.size>2097152){alert("File is too large. Maximum size is 2MB."),D.current&&(D.current.value="");return}_(a),G(null),F({unit:"%",width:100,height:100,x:0,y:0});let s=new FileReader;s.onload=()=>{C(s.result),w(!0)},s.readAsDataURL(a)}})]}),!f&&(0,l.jsx)("div",{className:"flex items-center justify-center gap-2 mt-4",children:(0,l.jsxs)(v.Bc,{children:[(0,l.jsxs)(v.m_,{children:[(0,l.jsx)(v.k$,{asChild:!0,children:(0,l.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 rounded-full hover:bg-emerald-50 dark:hover:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-700 transition-all duration-200 hover:scale-115",children:(0,l.jsx)(c.A,{className:"h-4 w-4 text-emerald-500 dark:text-emerald-400"})})}),(0,l.jsx)(v.ZI,{className:"border-2 border-gray-200 dark:border-gray-700",children:(0,l.jsx)("p",{children:"Test voice"})})]}),(0,l.jsxs)(v.m_,{children:[(0,l.jsx)(v.k$,{asChild:!0,children:(0,l.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 rounded-full hover:bg-blue-50 dark:hover:bg-blue-900/20 border border-blue-200 dark:border-gray-700 transition-all duration-200 hover:scale-115",onClick:()=>P(!0),children:(0,l.jsx)(h.A,{className:"h-4 w-4 text-blue-500 dark:text-blue-400"})})}),(0,l.jsx)(v.ZI,{className:"border-2 border-gray-200 dark:border-gray-700",children:(0,l.jsx)("p",{children:"Chat with agent"})})]}),(0,l.jsxs)(v.m_,{children:[(0,l.jsx)(v.k$,{asChild:!0,children:(0,l.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 rounded-full hover:bg-indigo-50 dark:hover:bg-indigo-900/20 border border-indigo-200 dark:border-indigo-700 transition-all duration-200 hover:scale-115",children:(0,l.jsx)(m.A,{className:"h-4 w-4 text-indigo-500 dark:text-indigo-400"})})}),(0,l.jsx)(v.ZI,{className:"border-2 border-gray-200 dark:border-gray-700",children:(0,l.jsx)("p",{children:"Start web call"})})]}),(0,l.jsxs)(v.m_,{children:[(0,l.jsx)(v.k$,{asChild:!0,children:(0,l.jsx)(n.$,{variant:"ghost",size:"sm",className:"h-8 w-8 p-0 rounded-full hover:bg-amber-50 dark:hover:bg-amber-900/20 border border-purple-200 dark:border-gray-700 transition-all duration-200 hover:scale-115",onClick:()=>T(!0),children:(0,l.jsx)(u.A,{className:"h-4 w-4 text-purple-500 dark:text-purple-400"})})}),(0,l.jsx)(v.ZI,{className:"border-2 border-gray-200 dark:border-gray-700",children:(0,l.jsx)("p",{children:"Start phone call"})})]})]})})]}),(0,l.jsxs)("div",{className:"w-full lg:w-2/3 xl:w-3/4 space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(i.J,{htmlFor:"name",children:"Name"}),(0,l.jsx)(r.p,{id:"name",value:e?.name||"",onChange:s=>{a({...e,name:s.target.value})},placeholder:"Enter agent name",className:"w-full"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(i.J,{htmlFor:"role",children:"Role"}),(0,l.jsx)(r.p,{id:"role",value:e?.role||"",onChange:s=>{a({...e,role:s.target.value})},placeholder:"Enter agent role",className:"w-full"})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"Phone Numbers"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(i.J,{htmlFor:"localPhoneNumber",children:"Local Number"}),(0,l.jsxs)(t.l6,{value:e?.localPhoneNumberId||"",onValueChange:s=>{a({...e,localPhoneNumberId:s})},children:[(0,l.jsx)(t.bq,{id:"localPhoneNumber",className:"w-full",children:(0,l.jsx)(t.yv,{placeholder:"Select a phone number"})}),(0,l.jsxs)(t.gC,{children:[(0,l.jsx)(t.eb,{value:"none",children:"None"}),s?.map(e=>l.jsxs(t.eb,{value:e.id,children:[e.number," ",e.name?`(${e.name})`:""]},e.id))]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(i.J,{htmlFor:"internationalPhoneNumber",children:"International Number"}),(0,l.jsxs)(t.l6,{value:e?.internationalPhoneNumberId||"",onValueChange:s=>{a({...e,internationalPhoneNumberId:s})},children:[(0,l.jsx)(t.bq,{id:"internationalPhoneNumber",className:"w-full",children:(0,l.jsx)(t.yv,{placeholder:"Select a phone number"})}),(0,l.jsxs)(t.gC,{children:[(0,l.jsx)(t.eb,{value:"none",children:"None"}),s?.map(e=>l.jsxs(t.eb,{value:e.id,children:[e.number," ",e.name?`(${e.name})`:""]},e.id))]})]})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(i.J,{htmlFor:"language",children:"Language"}),(0,l.jsxs)(t.l6,{value:e?.transcriber?.language||"",children:[(0,l.jsx)(t.bq,{id:"language",className:"w-full sm:w-auto",children:(0,l.jsx)(t.yv,{placeholder:"Select a language"})}),(0,l.jsx)(t.gC,{children:[{code:"en-US",name:"English (US)"},{code:"en-GB",name:"English (UK)"},{code:"en",name:"English"},{code:"fr-FR",name:"French"},{code:"fr",name:"French"},{code:"es-ES",name:"Spanish"},{code:"de-DE",name:"German"},{code:"it-IT",name:"Italian"},{code:"ar-SA",name:"Arabic"}].map(e=>(0,l.jsx)(t.eb,{value:e.code,children:e.name},e.code))})]})]})]})]}),!f&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(p.G,{isOpen:E,onClose:()=>T(!1),agent:e}),(0,l.jsx)(b.M,{agent:e,isOpen:I,onClose:()=>P(!1)})]}),(0,l.jsx)(g.lG,{open:A,onOpenChange:w,children:(0,l.jsxs)(g.Cf,{className:"sm:max-w-md",children:[(0,l.jsx)(g.c7,{children:(0,l.jsx)(g.L3,{children:"Crop Avatar Image"})}),k&&(0,l.jsxs)("div",{className:"mt-4 flex flex-col items-center",children:[(0,l.jsx)(x.Ay,{crop:S,onChange:e=>F(Q(e)),onComplete:e=>{G(e)},circularCrop:!0,minWidth:50,minHeight:50,keepSelection:!0,locked:!0,children:(0,l.jsx)("img",{ref:z,src:k,alt:"Crop preview",style:{maxHeight:"400px"},onLoad:e=>{let{width:a,height:s}=e.currentTarget,l=Math.min(a,s),r=(a-l)/2,i=(s-l)/2;F({unit:"px",width:l,height:l,x:r,y:i})}})}),(0,l.jsx)("p",{className:"text-sm text-muted-foreground mt-2",children:"Drag to adjust the crop area."})]}),(0,l.jsxs)(g.Es,{className:"flex justify-between mt-4",children:[(0,l.jsx)(n.$,{variant:"outline",onClick:()=>{w(!1),_(null),C(null),D.current&&(D.current.value="")},children:"Cancel"}),(0,l.jsx)(n.$,{onClick:q,children:"Apply & Upload"})]})]})})]})})}},34729:(e,a,s)=>{s.d(a,{T:()=>i});var l=s(60687);s(43210);var r=s(4780);function i({className:e,...a}){return(0,l.jsx)("textarea",{"data-slot":"textarea",className:(0,r.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),...a})}},44493:(e,a,s)=>{s.d(a,{BT:()=>o,Wu:()=>d,ZB:()=>n,Zp:()=>i,aR:()=>t,wL:()=>c});var l=s(60687);s(43210);var r=s(4780);function i({className:e,...a}){return(0,l.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl py-6 border ",e),...a})}function t({className:e,...a}){return(0,l.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("flex flex-col gap-1.5 px-6",e),...a})}function n({className:e,...a}){return(0,l.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",e),...a})}function o({className:e,...a}){return(0,l.jsx)("div",{"data-slot":"card-description",className:(0,r.cn)("text-muted-foreground text-sm",e),...a})}function d({className:e,...a}){return(0,l.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",e),...a})}function c({className:e,...a}){return(0,l.jsx)("div",{"data-slot":"card-footer",className:(0,r.cn)("flex items-center px-6",e),...a})}},45685:(e,a,s)=>{s.d(a,{A:()=>r});var l=s(60687);function r({agent:e,setAgent:a,phoneNumbers:s}){return(0,l.jsx)("div",{className:"space-y-6 p-6",children:(0,l.jsx)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4",children:(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"Brain Configuration"})})})}},54987:(e,a,s)=>{s.d(a,{d:()=>t});var l=s(60687);s(43210);var r=s(90270),i=s(4780);function t({className:e,...a}){return(0,l.jsx)(r.bL,{"data-slot":"switch",className:(0,i.cn)("peer data-[state=checked]:bg-primary data-[state=unchecked]:bg-input focus-visible:border-ring focus-visible:ring-ring/50 inline-flex h-5 w-9 shrink-0 items-center rounded-full border-2 border-transparent shadow-xs transition-all outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,l.jsx)(r.zi,{"data-slot":"switch-thumb",className:(0,i.cn)("bg-background pointer-events-none block size-4 rounded-full ring-0 shadow-lg transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")})})}},76104:(e,a,s)=>{s.d(a,{A:()=>l});let l={src:"/_next/static/media/Binghatti-Lisa.85c81ecb.jpeg",height:1586,width:1586,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/2wBDAQoKCgoKCgsMDAsPEA4QDxYUExMUFiIYGhgaGCIzICUgICUgMy03LCksNy1RQDg4QFFeT0pPXnFlZXGPiI+7u/v/wgARCAAIAAgDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAX/xAAUAQEAAAAAAAAAAAAAAAAAAAAC/9oADAMBAAIQAxAAAACeA//EABsQAAEFAQEAAAAAAAAAAAAAAAECAwQREwAi/9oACAEBAAE/AG6e3khuoyZAbQNDWYR6SO//xAAVEQEBAAAAAAAAAAAAAAAAAAABAP/aAAgBAgEBPwAL/8QAFhEAAwAAAAAAAAAAAAAAAAAAAAFB/9oACAEDAQE/AHD/2Q==",blurWidth:8,blurHeight:8}},76242:(e,a,s)=>{s.d(a,{Bc:()=>t,ZI:()=>d,k$:()=>o,m_:()=>n});var l=s(60687);s(43210);var r=s(9989),i=s(4780);function t({delayDuration:e=0,...a}){return(0,l.jsx)(r.Kq,{"data-slot":"tooltip-provider",delayDuration:e,...a})}function n({...e}){return(0,l.jsx)(t,{children:(0,l.jsx)(r.bL,{"data-slot":"tooltip",...e})})}function o({...e}){return(0,l.jsx)(r.l9,{"data-slot":"tooltip-trigger",...e})}function d({className:e,sideOffset:a=0,children:s,...t}){return(0,l.jsx)(r.ZL,{children:(0,l.jsxs)(r.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,i.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit rounded-md px-3 py-1.5 text-xs text-balance",e),...t,children:[s,(0,l.jsx)(r.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}},78885:(e,a,s)=>{s.d(a,{A:()=>h});var l=s(60687),r=s(89667),i=s(80013),t=s(15079),n=s(18116),o=s(54987),d=s(43210),c=s(44493);function h({agent:e,setAgent:a,phoneNumbers:s}){let[h,m]=(0,d.useState)({model:e?.model?.model||"",provider:e?.model?.provider||"",maxTokens:e?.model?.maxTokens||100,temperature:e?.model?.temperature||.2}),u=(s,l)=>{let r={...h,[s]:l};if("provider"===s)switch(l){case"openai":r.model="gpt-4o";break;case"anthropic":r.model="claude-3-7-sonnet-20250219";break;case"google":r.model="gemini-2.0-flash";break;case"cerebras":r.model="llama-3.3-70b";break;case"deep-seek":r.model="deepseek-chat";break;case"xai":r.model="grok-2";break;case"mistral":r.model="mistral-large-latest";break;default:r.model=""}m(r),a({...e,model:{...e.model,provider:r.provider,model:r.model,maxTokens:r.maxTokens,temperature:r.temperature}})};return(0,l.jsxs)("div",{className:"space-y-6 p-6",children:[(0,l.jsx)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 mb-4",children:(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"Advanced Settings"})}),(0,l.jsxs)("div",{className:"space-y-4 mt-6",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"Model Settings"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(i.J,{htmlFor:"model-provider",children:"Provider"}),(0,l.jsxs)(t.l6,{value:h?.provider||"",onValueChange:e=>u("provider",e),children:[(0,l.jsx)(t.bq,{id:"model-provider",className:"w-full",children:(0,l.jsx)(t.yv,{placeholder:"Select provider"})}),(0,l.jsxs)(t.gC,{children:[(0,l.jsx)(t.eb,{value:"openai",children:"OpenAI"}),(0,l.jsx)(t.eb,{value:"anthropic",children:"Anthropic"}),(0,l.jsx)(t.eb,{value:"google",children:"Google"}),(0,l.jsx)(t.eb,{value:"cerebras",children:"Cerebras"}),(0,l.jsx)(t.eb,{value:"deep-seek",children:"Deepseek"}),(0,l.jsx)(t.eb,{value:"xai",children:"Xai"}),(0,l.jsx)(t.eb,{value:"mistral",children:"Mistral"}),(0,l.jsx)(t.eb,{value:"perplexity-ai",children:"Perplexity AI"}),(0,l.jsx)(t.eb,{value:"anyscale",children:"Anyscale"}),(0,l.jsx)(t.eb,{value:"inflection-ai",children:"Inflection AI"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(i.J,{htmlFor:"model-name",children:"Model"}),(0,l.jsxs)(t.l6,{value:h?.model||"",onValueChange:e=>u("model",e),disabled:["perplexity-ai","anyscale","inflection-ai"].includes(h.provider),children:[(0,l.jsx)(t.bq,{id:"model-name",className:"w-full",children:(0,l.jsx)(t.yv,{placeholder:"Select model"})}),(0,l.jsxs)(t.gC,{children:["openai"===h.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(t.eb,{value:"gpt-4o",children:"GPT 4o"}),(0,l.jsx)(t.eb,{value:"gpt-4.1",children:"GPT 4.1"}),(0,l.jsx)(t.eb,{value:"gpt-4.1-mini",children:"GPT 4.1 Mini"}),(0,l.jsx)(t.eb,{value:"gpt-4.1-nano",children:"GPT 4.1 Nano"}),(0,l.jsx)(t.eb,{value:"gpt-4.5-preview",children:"GPT 4.5 Preview"}),(0,l.jsx)(t.eb,{value:"gpt-4o-mini",children:"GPT 4o Mini"}),(0,l.jsx)(t.eb,{value:"chatgpt-4o-latest",children:"ChatGPT 4o Latest"}),(0,l.jsx)(t.eb,{value:"o3",children:"O3"}),(0,l.jsx)(t.eb,{value:"o3-mini",children:"O3 Mini"}),(0,l.jsx)(t.eb,{value:"o4-mini",children:"O4 Mini"}),(0,l.jsx)(t.eb,{value:"o1-preview",children:"O1 Preview"}),(0,l.jsx)(t.eb,{value:"o1-mini",children:"O1 Mini"}),(0,l.jsx)(t.eb,{value:"gpt-4o-realtime-preview-2024-10-01",children:"GPT 4o Realtime Preview"}),(0,l.jsx)(t.eb,{value:"gpt-4-turbo",children:"GPT 4 Turbo"}),(0,l.jsx)(t.eb,{value:"gpt-4",children:"GPT 4"}),(0,l.jsx)(t.eb,{value:"gpt-3.5-turbo",children:"GPT 3.5 Turbo"})]}),"anthropic"===h.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(t.eb,{value:"claude-3-7-sonnet-20250219",children:"Claude 3.7 Sonnet"}),(0,l.jsx)(t.eb,{value:"claude-3-5-sonnet-20241022",children:"Claude 3.5 Sonnet"}),(0,l.jsx)(t.eb,{value:"claude-3-5-haiku-20241022",children:"Claude 3.5 Haiku (Old)"}),(0,l.jsx)(t.eb,{value:"claude-3-opus-20240229",children:"Claude 3 Opus"}),(0,l.jsx)(t.eb,{value:"claude-3-sonnet-20240229",children:"Claude 3 Sonnet"}),(0,l.jsx)(t.eb,{value:"claude-3-haiku-20240307",children:"Claude 3 Haiku (Latest)"})]}),"google"===h.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(t.eb,{value:"gemini-2.5-flash-preview-04-17",children:"Gemini 2.5 Flash Preview"}),(0,l.jsx)(t.eb,{value:"gemini-2.0-flash",children:"Gemini 2.0 Flash"}),(0,l.jsx)(t.eb,{value:"gemini-2.0-flash-thinking-exp",children:"Gemini 2.0 Flash Thinking (Experimental)"}),(0,l.jsx)(t.eb,{value:"gemini-2.0-pro-exp-02-05",children:"Gemini 2.0 Pro (Experimental)"}),(0,l.jsx)(t.eb,{value:"gemini-2.0-flash-lite",children:"Gemini 2.0 Flash Lite"}),(0,l.jsx)(t.eb,{value:"gemini-2.0-flash-lite-preview-02-05",children:"Gemini 2.0 Flash Lite Preview"}),(0,l.jsx)(t.eb,{value:"gemini-2.0-flash-exp",children:"Gemini 2.0 Flash (Experimental)"}),(0,l.jsx)(t.eb,{value:"gemini-2.0-flash-realtime-exp",children:"Gemini 2.0 Flash Realtime (Experimental)"}),(0,l.jsx)(t.eb,{value:"gemini-1.5-flash",children:"Gemini 1.5 Flash"}),(0,l.jsx)(t.eb,{value:"gemini-1.5-flash-002",children:"Gemini 1.5 Flash 002"}),(0,l.jsx)(t.eb,{value:"gemini-1.5-pro",children:"Gemini 1.5 Pro"}),(0,l.jsx)(t.eb,{value:"gemini-1.5-pro-002",children:"Gemini 1.5 Pro 002"}),(0,l.jsx)(t.eb,{value:"gemini-1.0-pro",children:"Gemini 1.0 Pro"})]}),"cerebras"===h.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(t.eb,{value:"llama-3.3-70b",children:"Llama 3.3 70B"}),(0,l.jsx)(t.eb,{value:"llama3.1-8b",children:"Llama 3.1 8B"})]}),"deep-seek"===h.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(t.eb,{value:"deepseek-chat",children:"DeepSeek Chat (V3)"}),(0,l.jsx)(t.eb,{value:"deepseek-reasoner",children:"Deepseek-R1"})]}),"xai"===h.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(t.eb,{value:"grok-2",children:"Grok 2"}),(0,l.jsx)(t.eb,{value:"grok-3",children:"Grok 3"}),(0,l.jsx)(t.eb,{value:"grok-beta",children:"Grok Beta"})]}),"mistral"===h.provider&&(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(t.eb,{value:"mistral-large-latest",children:"Mistral Large"}),(0,l.jsx)(t.eb,{value:"pixtral-large-latest",children:"Pixtral Large"}),(0,l.jsx)(t.eb,{value:"mistral-small",children:"Mistral Small"})]}),["perplexity-ai","anyscale","inflection-ai"].includes(h.provider)&&(0,l.jsx)(t.eb,{value:"none",disabled:!0,children:"No models available"})]})]})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(i.J,{htmlFor:"max-tokens",children:"Max Tokens"}),(0,l.jsx)(r.p,{id:"max-tokens",type:"number",min:1,max:500,value:h?.maxTokens||100,onChange:e=>u("maxTokens",Math.min(500,parseInt(e.target.value)||0)),className:"w-full"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)(i.J,{htmlFor:"temperature",children:"Temperature"}),(0,l.jsx)("span",{className:"text-sm text-muted-foreground mt-3",children:h?.temperature.toFixed(1)||.2})]}),(0,l.jsx)(n.A,{id:"temperature",value:[h?.temperature],min:0,max:2,step:.1,className:"w-full",onValueChange:e=>u("temperature",e[0])})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"Webhook (Server Url)"}),(0,l.jsx)(c.Zp,{className:"p-4",children:(0,l.jsx)(r.p,{placeholder:"https://your-service.com/webhook",value:e?.server?.url||"",onChange:s=>{a({...e,server:{url:s.target.value}})}})})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"Background Denoising"}),(0,l.jsx)(c.Zp,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)(i.J,{htmlFor:"background-denoising",children:"Enable background noise reduction"}),(0,l.jsx)(o.d,{id:"background-denoising",checked:e?.backgroundDenoisingEnabled||!1,onCheckedChange:s=>{a({...e,backgroundDenoisingEnabled:s})}})]})})]})]})}s(44140)},80013:(e,a,s)=>{s.d(a,{J:()=>t});var l=s(60687);s(43210);var r=s(78148),i=s(4780);function t({className:e,...a}){return(0,l.jsx)(r.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...a})}},85763:(e,a,s)=>{s.d(a,{Xi:()=>o,av:()=>d,j7:()=>n,tU:()=>t});var l=s(60687);s(43210);var r=s(55146),i=s(4780);function t({className:e,...a}){return(0,l.jsx)(r.bL,{"data-slot":"tabs",className:(0,i.cn)("flex flex-col gap-2",e),...a})}function n({className:e,...a}){return(0,l.jsx)(r.B8,{"data-slot":"tabs-list",className:(0,i.cn)("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-1",e),...a})}function o({className:e,...a}){return(0,l.jsx)(r.l9,{"data-slot":"tabs-trigger",className:(0,i.cn)("data-[state=active]:bg-background data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring inline-flex flex-1 items-center justify-center gap-1.5 rounded-md px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",e),...a})}function d({className:e,...a}){return(0,l.jsx)(r.UC,{"data-slot":"tabs-content",className:(0,i.cn)("flex-1 outline-none",e),...a})}},93187:(e,a,s)=>{s.d(a,{A:()=>h});var l=s(60687),r=s(29523),i=s(34729),t=s(44493),n=s(15079),o=s(47158),d=s(43210),c=s(89667);function h({agent:e,setAgent:a}){let[s,h]=(0,d.useState)("customer-service");return(0,l.jsx)(l.Fragment,{children:(0,l.jsxs)("div",{className:"space-y-6 p-6",children:[(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"Welcome Message"}),(0,l.jsxs)(r.$,{variant:"outline",size:"sm",onClick:()=>{let s=[`Hello there! I'm ${e.name||"your friend"}, your dedicated ${e.role||"assistant"}. I'm here to help with any questions or concerns you might have today.`,`Welcome! This is ${e.role||"your assistant"}. I'm ready to assist you with anything you need!`,`Hi, thanks for reaching out! I'm ${e.name||"your assitant"}, and I'll be your ${e.role||"guide and advisor"} today. How may I help you?`],l=s[Math.floor(Math.random()*s.length)];a({...e,firstMessage:l})},children:[(0,l.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Generate"]})]}),(0,l.jsx)(t.Zp,{className:"p-4",children:(0,l.jsx)(i.T,{className:"min-h-[100px] resize-y",placeholder:"Enter a welcome message that the agent will use to start conversations...",value:e?.firstMessage||"",onChange:s=>{a({...e,firstMessage:s.target.value})}})})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"Role Instructions"}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsxs)(n.l6,{value:s,onValueChange:h,children:[(0,l.jsx)(n.bq,{className:"w-[180px]",children:(0,l.jsx)(n.yv,{placeholder:"Select template"})}),(0,l.jsxs)(n.gC,{children:[(0,l.jsx)(n.eb,{value:"customer-service",children:"Customer Service"}),(0,l.jsx)(n.eb,{value:"sales",children:"Sales Agent"}),(0,l.jsx)(n.eb,{value:"support",children:"Technical Support"})]})]}),(0,l.jsxs)(r.$,{variant:"outline",size:"sm",children:[(0,l.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Generate"]})]})]}),(0,l.jsx)(t.Zp,{className:"p-4",children:(0,l.jsx)(i.T,{id:"systemContent",className:"max-h-[500px] resize-y",placeholder:"Describe the role and responsibilities of this agent...",value:e?.model?.messages?.find(e=>"system"===e.role)?.content||"",onChange:s=>{let l;let r=s.target.value,i=e.model||{messages:[]},t=i.messages||[];l=t.find(e=>"system"===e.role)?t.map(e=>"system"===e.role?{...e,content:r}:e):[...t,{role:"system",content:r}],a({...e,model:{...i,messages:l}})}})})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold",children:"Voicemail Message"}),(0,l.jsx)(t.Zp,{className:"p-4",children:(0,l.jsx)(c.p,{placeholder:"Enter a message to leave when reaching voicemail...",value:e?.voicemailMessage||"",onChange:s=>{a({...e,voicemailMessage:s.target.value})}})})]})]})})}},95534:(e,a,s)=>{s.d(a,{G:()=>g});var l=s(60687),r=s(43210),i=s(29523),t=s(63503),n=s(89667),o=s(41862),d=s(48340),c=s(89757),h=s(97461),m=s.n(h),u=s(52581);s(90895);var x=s(4845);function g({isOpen:e,onClose:a,agent:s}){let[h,g]=(0,r.useState)({name:"",phoneNumber:""}),[v,p]=(0,r.useState)(!1),[b,j]=(0,r.useState)(null),f=async()=>{if(s){p(!0),j(null);try{let e=h.phoneNumber.replace(/\s+/g,""),l=e.startsWith("+")?e:`+${e}`,r=!1;try{await (0,c.SQ)({contactName:h.name,phoneNumber:l,campaigns:[]})}catch(e){if(e.message.includes("Conflict")||e.message.includes("already exists"))r=!0;else throw e}let i=(0,x.s)(l)||"",t=[{Name:h.name,MobileNumber:l}];await (0,c.G6)(s.id,t,i),u.o.success(r?"Call initiated with existing contact":"Contact created and call initiated"),a()}catch(e){console.error("Call error:",e),e.message.includes("already exists")?j("Contact already exists with this name and phone number"):j(e instanceof Error?e.message:"Failed to initiate call"),u.o.error("Failed to process request")}finally{p(!1)}}};return(0,l.jsx)(t.lG,{open:e,onOpenChange:e=>{e||(g({name:"",phoneNumber:""}),j(null)),a()},children:(0,l.jsxs)(t.Cf,{className:"sm:max-w-[425px]",children:[(0,l.jsxs)(t.c7,{children:[(0,l.jsx)(t.L3,{children:"Start Phone Call"}),(0,l.jsxs)(t.rr,{children:["Enter your details to start a call with",(0,l.jsx)("span",{className:"font-bold ml-1",children:s?.name})]})]}),(0,l.jsxs)("div",{className:"grid gap-4 py-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"name",className:"text-sm font-medium",children:"Your Name"}),(0,l.jsx)(n.p,{id:"name",placeholder:"Enter your name",className:"mt-2",value:h.name,onChange:e=>g(a=>({...a,name:e.target.value}))})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("label",{htmlFor:"phone",className:"text-sm font-medium",children:"Phone Number"}),(0,l.jsx)(m(),{country:(0,x.u)(),value:h.phoneNumber,onChange:e=>g(a=>({...a,phoneNumber:`+${e}`})),containerClass:"mt-2",inputClass:"!w-full !h-10 !pl-[48px] !rounded-md !border !border-input !bg-background !px-3 !py-2 !text-sm !ring-offset-background file:!border-0 file:!bg-transparent file:!text-sm file:!font-medium placeholder:!text-muted-foreground focus-visible:!outline-none focus-visible:!ring-2 focus-visible:!ring-ring focus-visible:!ring-offset-2 disabled:!cursor-not-allowed disabled:!opacity-50",buttonClass:"!border-r-0 !bg-transparent !border !border-input",dropdownClass:"!bg-background !border !border-input",specialLabel:""})]}),b&&(0,l.jsx)("div",{className:"text-sm text-red-500 bg-red-50 dark:bg-red-900/20 p-3 rounded-md",children:b})]}),(0,l.jsxs)(t.Es,{children:[(0,l.jsx)(i.$,{variant:"outline",onClick:a,children:"Cancel"}),(0,l.jsx)(i.$,{onClick:f,disabled:v||!h.name||!h.phoneNumber,className:"bg-green-600 hover:bg-green-700 text-white",children:v?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(o.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Initiating..."]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(d.A,{className:"mr-2 h-4 w-4"}),"Start Call"]})})]})]})})}}};